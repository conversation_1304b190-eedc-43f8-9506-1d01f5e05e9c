/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { XRReferenceSpace } from '../spaces/XRReferenceSpace.js';
import { XRRigidTransform } from '../primitives/XRRigidTransform.js';
interface XRReferenceSpaceEventInit extends EventInit {
    referenceSpace: XRReferenceSpace;
    transform?: XRRigidTransform;
}
export declare class XRReferenceSpaceEvent extends Event {
    readonly referenceSpace: XRReferenceSpace;
    readonly transform?: XRRigidTransform;
    constructor(type: string, eventInitDict: XRReferenceSpaceEventInit);
}
export interface XRReferenceSpaceEventHandler {
    (evt: XRReferenceSpaceEvent): any;
}
export {};
//# sourceMappingURL=XRReferenceSpaceEvent.d.ts.map