# maath

## 0.10.8

### Patch Changes

- fix: lower three peer dep requirement

## 0.10.7

### Patch Changes

- yarn

## 0.10.6

### Patch Changes

- ???

## 0.10.5

### Patch Changes

- add parameters to roundedplanegeo

## 0.10.4

### Patch Changes

- up

## 0.10.3

### Patch Changes

- fix publish

## 0.10.2

### Patch Changes

- fix: autoburst"

## 0.10.1

### Patch Changes

- manual burst flash gen

## 0.10.0

### Minor Changes

- feat: flashgen

## 0.9.0

### Minor Changes

- add damptLookAt

## 0.8.2

### Patch Changes

- expose repeat

## 0.8.1

### Patch Changes

- add linear

## 0.8.0

### Minor Changes

- add easings functions

## 0.7.0

### Minor Changes

- feat: applyCylindricalUV

## 0.6.0

### Minor Changes

- uv gen

## 0.5.3

### Patch Changes

- fix damp return

## 0.5.2

### Patch Changes

- fix types

## 0.5.1

### Patch Changes

- fix uppercase for roundedplanegeom

## 0.5.0

### Minor Changes

- new category for geometry + roundedplane

## 0.4.2

### Patch Changes

- fix: dampc rgb array support

## 0.4.1

### Patch Changes

- fix: eps check for dampm"

## 0.4.0

### Minor Changes

- feat: dampm, support for matrix4"

## 0.3.0

### Minor Changes

- feat: dampq, support for quaternions

## 0.2.0

### Minor Changes

- feat: damp2,3,4,E,C

## 0.1.1

### Patch Changes

- fix: damp is missing eps

## 0.1.0

### Minor Changes

- 1a033d1: adds entry point that exports everything
- feat: exp, deltaAngle, damp, dampAngle

## 0.0.2

### Patch Changes

- f9084f6: change(buffer): Makes addAxis more generic, allowing adding axis to 2D and 3D buffers

## 0.0.1

### Patch Changes

- ecdd821: new(misc): Adds coords transformation helpers
- 8107891: new(misc): Adds 2D coords transformation helpers
