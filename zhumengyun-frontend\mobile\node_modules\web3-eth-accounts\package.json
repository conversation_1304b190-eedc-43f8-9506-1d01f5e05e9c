{"name": "web3-eth-accounts", "version": "4.3.1", "description": "Package for managing Ethereum accounts and signing", "main": "./lib/commonjs/index.js", "module": "./lib/esm/index.js", "exports": {".": {"types": "./lib/types/index.d.ts", "import": "./lib/esm/index.js", "require": "./lib/commonjs/index.js"}}, "repository": "https://github.com/ChainSafe/web3.js", "author": "ChainSafe Systems", "license": "LGPL-3.0", "engines": {"node": ">=14", "npm": ">=6.12.0"}, "files": ["lib/**/*", "src/**/*"], "scripts": {"clean": "rimraf dist && rimraf lib", "prebuild": "yarn clean", "build": "concurrently --kill-others-on-fail \"yarn:build:*(!check)\"", "build:cjs": "tsc --build tsconfig.cjs.json && echo '{\"type\": \"commonjs\"}' > ./lib/commonjs/package.json", "build:esm": "tsc --build tsconfig.esm.json && echo '{\"type\": \"module\"}' > ./lib/esm/package.json", "build:types": "tsc --build tsconfig.types.json", "build:check": "node -e \"require('./lib')\"", "lint": "eslint --cache --cache-strategy content --ext .ts .", "lint:fix": "eslint --fix --ext .js,.ts .", "format": "prettier --write '**/*'", "test": "jest --config=./test/unit/jest.config.js", "test:coverage:unit": "jest --config=./test/unit/jest.config.js --coverage=true --coverage-reporters=text", "test:coverage:integration": "jest --config=./test/integration/jest.config.js --coverage=true --coverage-reporters=text", "test:ci": "jest --coverage=true --coverage-reporters=json --verbose", "test:watch": "npm test -- --watch", "test:unit": "jest --config=./test/unit/jest.config.js", "test:integration": "jest --config=./test/integration/jest.config.js"}, "devDependencies": {"@types/jest": "^28.1.6", "@typescript-eslint/eslint-plugin": "^5.30.7", "@typescript-eslint/parser": "^5.30.7", "eslint": "^8.20.0", "eslint-config-base-web3": "0.1.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "jest": "^29.7.0", "jest-extended": "^3.0.1", "jest-when": "^3.5.1", "prettier": "^2.7.1", "ts-jest": "^29.1.1", "typescript": "^5.5.4", "web3-providers-ipc": "^4.0.7"}, "dependencies": {"@ethereumjs/rlp": "^4.0.1", "crc-32": "^1.2.2", "ethereum-cryptography": "^2.0.0", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "gitHead": "aa197b8f7914ff336423c4081459c9bf7867c4b3"}