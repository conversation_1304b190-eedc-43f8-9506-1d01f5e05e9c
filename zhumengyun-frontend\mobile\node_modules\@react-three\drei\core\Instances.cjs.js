"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("three"),r=require("react"),n=require("@react-three/fiber"),a=require("react-composer"),s=require("../helpers/deprecated.cjs.js");function c(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var u=c(e),o=i(t),l=i(r),f=c(a);const d=new o.Matrix4,m=new o.Matrix4,y=[],p=new o.Mesh;class g extends o.Group{constructor(){super(),this.color=new o.Color("white"),this.instance={current:void 0},this.instanceKey={current:void 0}}get geometry(){var e;return null==(e=this.instance.current)?void 0:e.geometry}raycast(e,t){const r=this.instance.current;if(!r)return;if(!r.geometry||!r.material)return;p.geometry=r.geometry;const n=r.matrixWorld,a=r.userData.instances.indexOf(this.instanceKey);if(!(-1===a||a>r.count)){r.getMatrixAt(a,d),m.multiplyMatrices(n,d),p.matrixWorld=m,r.material instanceof o.Material?p.material.side=r.material.side:p.material.side=r.material[0].side,p.raycast(e,y);for(let e=0,r=y.length;e<r;e++){const r=y[e];r.instanceId=a,r.object=this,t.push(r)}y.length=0}}}const x=l.createContext(null),h=new o.Matrix4,b=new o.Matrix4,M=new o.Matrix4,w=new o.Vector3,A=new o.Quaternion,v=new o.Vector3,E=l.forwardRef((({context:e,children:t,...r},a)=>{l.useMemo((()=>n.extend({PositionMesh:g})),[]);const s=l.useRef();l.useImperativeHandle(a,(()=>s.current),[]);const{subscribe:c,getParent:i}=l.useContext(e||x);return l.useLayoutEffect((()=>c(s)),[]),l.createElement("positionMesh",u.default({instance:i(),instanceKey:s,ref:s},r),t)})),j=l.forwardRef((({context:e,children:t,range:r,limit:a=1e3,frames:c=1/0,...i},f)=>{const[{localContext:d,instance:m}]=l.useState((()=>{const e=l.createContext(null);return{localContext:e,instance:l.forwardRef(((t,r)=>l.createElement(E,u.default({context:e},t,{ref:r}))))}})),y=l.useRef(null);l.useImperativeHandle(f,(()=>y.current),[]);const[p,g]=l.useState([]),[[j,D]]=l.useState((()=>{const e=new Float32Array(16*a);for(let t=0;t<a;t++)M.identity().toArray(e,16*t);return[e,new Float32Array([...new Array(3*a)].map((()=>1)))]}));l.useEffect((()=>{y.current.instanceMatrix.needsUpdate=!0}));let R=0,O=0;const C=l.useRef([]);l.useLayoutEffect((()=>{C.current=Object.entries(y.current.geometry.attributes).filter((([e,t])=>t.isInstancedBufferAttribute))})),n.useFrame((()=>{if(c===1/0||R<c){y.current.updateMatrix(),y.current.updateMatrixWorld(),h.copy(y.current.matrixWorld).invert(),O=Math.min(a,void 0!==r?r:a,p.length),y.current.count=O,s.setUpdateRange(y.current.instanceMatrix,{offset:0,count:16*O}),s.setUpdateRange(y.current.instanceColor,{offset:0,count:3*O});for(let e=0;e<p.length;e++){const t=p[e].current;t.matrixWorld.decompose(w,A,v),b.compose(w,A,v).premultiply(h),b.toArray(j,16*e),y.current.instanceMatrix.needsUpdate=!0,t.color.toArray(D,3*e),y.current.instanceColor.needsUpdate=!0}R++}}));const P=l.useMemo((()=>({getParent:()=>y,subscribe:e=>(g((t=>[...t,e])),()=>g((t=>t.filter((t=>t.current!==e.current)))))})),[]);return l.createElement("instancedMesh",u.default({userData:{instances:p,limit:a,frames:c},matrixAutoUpdate:!1,ref:y,args:[null,null,0],raycast:()=>null},i),l.createElement("instancedBufferAttribute",{attach:"instanceMatrix",count:j.length/16,array:j,itemSize:16,usage:o.DynamicDrawUsage}),l.createElement("instancedBufferAttribute",{attach:"instanceColor",count:D.length/3,array:D,itemSize:3,usage:o.DynamicDrawUsage}),"function"==typeof t?l.createElement(d.Provider,{value:P},t(m)):e?l.createElement(e.Provider,{value:P},t):l.createElement(x.Provider,{value:P},t))})),D=l.forwardRef((function({meshes:e,children:t,...r},n){const a=Array.isArray(e);if(!a)for(const t of Object.keys(e))e[t].isMesh||delete e[t];return l.createElement("group",{ref:n},l.createElement(f.default,{components:(a?e:Object.values(e)).map((({geometry:e,material:t})=>l.createElement(j,u.default({key:e.uuid,geometry:e,material:t},r))))},(r=>a?t(...r):t(Object.keys(e).filter((t=>e[t].isMesh)).reduce(((e,t,n)=>({...e,[t]:r[n]})),{})))))}));const R=l.forwardRef((({name:e,defaultValue:t,normalized:r,usage:a=o.DynamicDrawUsage},s)=>{const c=l.useRef(null);l.useImperativeHandle(s,(()=>c.current),[]),l.useLayoutEffect((()=>{const r=c.current.__r3f.parent;r.geometry.attributes[e]=c.current;const n=Array.isArray(t)?t:[t],a=Array.from({length:r.userData.limit},(()=>n)).flat();return c.current.array=new Float32Array(a),c.current.itemSize=n.length,c.current.count=a.length/c.current.itemSize,()=>{delete r.geometry.attributes[e]}}),[e]);let i=0;return n.useFrame((()=>{const t=c.current.__r3f.parent;if(t.userData.frames===1/0||i<t.userData.frames){for(let r=0;r<t.userData.instances.length;r++){const n=t.userData.instances[r].current[e];void 0!==n&&(c.current.set(Array.isArray(n)?n:"function"==typeof n.toArray?n.toArray():[n],r*c.current.itemSize),c.current.needsUpdate=!0)}i++}})),l.createElement("instancedBufferAttribute",{ref:c,usage:a,normalized:r})}));exports.Instance=E,exports.InstancedAttribute=R,exports.Instances=j,exports.Merged=D,exports.PositionMesh=g,exports.createInstances=function(){const e=l.createContext(null);return[l.forwardRef(((t,r)=>l.createElement(j,u.default({ref:r,context:e},t)))),l.forwardRef(((t,r)=>l.createElement(E,u.default({ref:r,context:e},t))))]};
