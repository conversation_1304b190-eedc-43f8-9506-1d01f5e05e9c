{"version": 3, "file": "3DMLoader.js", "sources": ["../../src/loaders/3DMLoader.js"], "sourcesContent": ["import {\n  BufferGeometry<PERSON>oader,\n  FileLoader,\n  Loader,\n  Object3D,\n  MeshStandardMaterial,\n  Mesh,\n  Color,\n  Points,\n  PointsMaterial,\n  Line,\n  LineBasicMaterial,\n  Matrix4,\n  DirectionalLight,\n  PointLight,\n  SpotLight,\n  RectAreaLight,\n  Vector3,\n  Sprite,\n  SpriteMaterial,\n  CanvasTexture,\n  LinearFilter,\n  ClampToEdgeWrapping,\n  TextureLoader,\n} from 'three'\n\nconst _taskCache = new WeakMap()\n\nclass Rhino3dmLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.libraryPath = ''\n    this.libraryPending = null\n    this.libraryBinary = null\n    this.libraryConfig = {}\n\n    this.url = ''\n\n    this.workerLimit = 4\n    this.workerPool = []\n    this.workerNextTaskID = 1\n    this.workerSourceURL = ''\n    this.workerConfig = {}\n\n    this.materials = []\n  }\n\n  setLibraryPath(path) {\n    this.libraryPath = path\n\n    return this\n  }\n\n  setWorkerLimit(workerLimit) {\n    this.workerLimit = workerLimit\n\n    return this\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const loader = new FileLoader(this.manager)\n\n    loader.setPath(this.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(this.requestHeader)\n\n    this.url = url\n\n    loader.load(\n      url,\n      (buffer) => {\n        // Check for an existing task using this buffer. A transferred buffer cannot be transferred\n        // again from this thread.\n        if (_taskCache.has(buffer)) {\n          const cachedTask = _taskCache.get(buffer)\n\n          return cachedTask.promise.then(onLoad).catch(onError)\n        }\n\n        this.decodeObjects(buffer, url).then(onLoad).catch(onError)\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  debug() {\n    console.log(\n      'Task load: ',\n      this.workerPool.map((worker) => worker._taskLoad),\n    )\n  }\n\n  decodeObjects(buffer, url) {\n    let worker\n    let taskID\n\n    const taskCost = buffer.byteLength\n\n    const objectPending = this._getWorker(taskCost)\n      .then((_worker) => {\n        worker = _worker\n        taskID = this.workerNextTaskID++ //hmmm\n\n        return new Promise((resolve, reject) => {\n          worker._callbacks[taskID] = { resolve, reject }\n\n          worker.postMessage({ type: 'decode', id: taskID, buffer }, [buffer])\n\n          //this.debug();\n        })\n      })\n      .then((message) => this._createGeometry(message.data))\n\n    // Remove task from the task list.\n    // Note: replaced '.finally()' with '.catch().then()' block - iOS 11 support (#19416)\n    objectPending\n      .catch(() => true)\n      .then(() => {\n        if (worker && taskID) {\n          this._releaseTask(worker, taskID)\n\n          //this.debug();\n        }\n      })\n\n    // Cache the task result.\n    _taskCache.set(buffer, {\n      url: url,\n      promise: objectPending,\n    })\n\n    return objectPending\n  }\n\n  parse(data, onLoad, onError) {\n    this.decodeObjects(data, '').then(onLoad).catch(onError)\n  }\n\n  _compareMaterials(material) {\n    const mat = {}\n    mat.name = material.name\n    mat.color = {}\n    mat.color.r = material.color.r\n    mat.color.g = material.color.g\n    mat.color.b = material.color.b\n    mat.type = material.type\n\n    for (let i = 0; i < this.materials.length; i++) {\n      const m = this.materials[i]\n      const _mat = {}\n      _mat.name = m.name\n      _mat.color = {}\n      _mat.color.r = m.color.r\n      _mat.color.g = m.color.g\n      _mat.color.b = m.color.b\n      _mat.type = m.type\n\n      if (JSON.stringify(mat) === JSON.stringify(_mat)) {\n        return m\n      }\n    }\n\n    this.materials.push(material)\n\n    return material\n  }\n\n  _createMaterial(material) {\n    if (material === undefined) {\n      return new MeshStandardMaterial({\n        color: new Color(1, 1, 1),\n        metalness: 0.8,\n        name: 'default',\n        side: 2,\n      })\n    }\n\n    const _diffuseColor = material.diffuseColor\n\n    const diffusecolor = new Color(_diffuseColor.r / 255.0, _diffuseColor.g / 255.0, _diffuseColor.b / 255.0)\n\n    if (_diffuseColor.r === 0 && _diffuseColor.g === 0 && _diffuseColor.b === 0) {\n      diffusecolor.r = 1\n      diffusecolor.g = 1\n      diffusecolor.b = 1\n    }\n\n    // console.log( material );\n\n    const mat = new MeshStandardMaterial({\n      color: diffusecolor,\n      name: material.name,\n      side: 2,\n      transparent: material.transparency > 0 ? true : false,\n      opacity: 1.0 - material.transparency,\n    })\n\n    const textureLoader = new TextureLoader()\n\n    for (let i = 0; i < material.textures.length; i++) {\n      const texture = material.textures[i]\n\n      if (texture.image !== null) {\n        const map = textureLoader.load(texture.image)\n\n        switch (texture.type) {\n          case 'Diffuse':\n            mat.map = map\n\n            break\n\n          case 'Bump':\n            mat.bumpMap = map\n\n            break\n\n          case 'Transparency':\n            mat.alphaMap = map\n            mat.transparent = true\n\n            break\n\n          case 'Emap':\n            mat.envMap = map\n\n            break\n        }\n      }\n    }\n\n    return mat\n  }\n\n  _createGeometry(data) {\n    // console.log(data);\n\n    const object = new Object3D()\n    const instanceDefinitionObjects = []\n    const instanceDefinitions = []\n    const instanceReferences = []\n\n    object.userData['layers'] = data.layers\n    object.userData['groups'] = data.groups\n    object.userData['settings'] = data.settings\n    object.userData['objectType'] = 'File3dm'\n    object.userData['materials'] = null\n    object.name = this.url\n\n    let objects = data.objects\n    const materials = data.materials\n\n    for (let i = 0; i < objects.length; i++) {\n      const obj = objects[i]\n      const attributes = obj.attributes\n\n      switch (obj.objectType) {\n        case 'InstanceDefinition':\n          instanceDefinitions.push(obj)\n\n          break\n\n        case 'InstanceReference':\n          instanceReferences.push(obj)\n\n          break\n\n        default:\n          let _object\n\n          if (attributes.materialIndex >= 0) {\n            const rMaterial = materials[attributes.materialIndex]\n            let material = this._createMaterial(rMaterial)\n            material = this._compareMaterials(material)\n            _object = this._createObject(obj, material)\n          } else {\n            const material = this._createMaterial()\n            _object = this._createObject(obj, material)\n          }\n\n          if (_object === undefined) {\n            continue\n          }\n\n          const layer = data.layers[attributes.layerIndex]\n\n          _object.visible = layer ? data.layers[attributes.layerIndex].visible : true\n\n          if (attributes.isInstanceDefinitionObject) {\n            instanceDefinitionObjects.push(_object)\n          } else {\n            object.add(_object)\n          }\n\n          break\n      }\n    }\n\n    for (let i = 0; i < instanceDefinitions.length; i++) {\n      const iDef = instanceDefinitions[i]\n\n      objects = []\n\n      for (let j = 0; j < iDef.attributes.objectIds.length; j++) {\n        const objId = iDef.attributes.objectIds[j]\n\n        for (let p = 0; p < instanceDefinitionObjects.length; p++) {\n          const idoId = instanceDefinitionObjects[p].userData.attributes.id\n\n          if (objId === idoId) {\n            objects.push(instanceDefinitionObjects[p])\n          }\n        }\n      }\n\n      // Currently clones geometry and does not take advantage of instancing\n\n      for (let j = 0; j < instanceReferences.length; j++) {\n        const iRef = instanceReferences[j]\n\n        if (iRef.geometry.parentIdefId === iDef.attributes.id) {\n          const iRefObject = new Object3D()\n          const xf = iRef.geometry.xform.array\n\n          const matrix = new Matrix4()\n          matrix.set(\n            xf[0],\n            xf[1],\n            xf[2],\n            xf[3],\n            xf[4],\n            xf[5],\n            xf[6],\n            xf[7],\n            xf[8],\n            xf[9],\n            xf[10],\n            xf[11],\n            xf[12],\n            xf[13],\n            xf[14],\n            xf[15],\n          )\n\n          iRefObject.applyMatrix4(matrix)\n\n          for (let p = 0; p < objects.length; p++) {\n            iRefObject.add(objects[p].clone(true))\n          }\n\n          object.add(iRefObject)\n        }\n      }\n    }\n\n    object.userData['materials'] = this.materials\n    return object\n  }\n\n  _createObject(obj, mat) {\n    const loader = new BufferGeometryLoader()\n\n    const attributes = obj.attributes\n\n    let geometry, material, _color, color\n\n    switch (obj.objectType) {\n      case 'Point':\n      case 'PointSet':\n        geometry = loader.parse(obj.geometry)\n\n        if (geometry.attributes.hasOwnProperty('color')) {\n          material = new PointsMaterial({ vertexColors: true, sizeAttenuation: false, size: 2 })\n        } else {\n          _color = attributes.drawColor\n          color = new Color(_color.r / 255.0, _color.g / 255.0, _color.b / 255.0)\n          material = new PointsMaterial({ color: color, sizeAttenuation: false, size: 2 })\n        }\n\n        material = this._compareMaterials(material)\n\n        const points = new Points(geometry, material)\n        points.userData['attributes'] = attributes\n        points.userData['objectType'] = obj.objectType\n\n        if (attributes.name) {\n          points.name = attributes.name\n        }\n\n        return points\n\n      case 'Mesh':\n      case 'Extrusion':\n      case 'SubD':\n      case 'Brep':\n        if (obj.geometry === null) return\n\n        geometry = loader.parse(obj.geometry)\n\n        if (geometry.attributes.hasOwnProperty('color')) {\n          mat.vertexColors = true\n        }\n\n        if (mat === null) {\n          mat = this._createMaterial()\n          mat = this._compareMaterials(mat)\n        }\n\n        const mesh = new Mesh(geometry, mat)\n        mesh.castShadow = attributes.castsShadows\n        mesh.receiveShadow = attributes.receivesShadows\n        mesh.userData['attributes'] = attributes\n        mesh.userData['objectType'] = obj.objectType\n\n        if (attributes.name) {\n          mesh.name = attributes.name\n        }\n\n        return mesh\n\n      case 'Curve':\n        geometry = loader.parse(obj.geometry)\n\n        _color = attributes.drawColor\n        color = new Color(_color.r / 255.0, _color.g / 255.0, _color.b / 255.0)\n\n        material = new LineBasicMaterial({ color: color })\n        material = this._compareMaterials(material)\n\n        const lines = new Line(geometry, material)\n        lines.userData['attributes'] = attributes\n        lines.userData['objectType'] = obj.objectType\n\n        if (attributes.name) {\n          lines.name = attributes.name\n        }\n\n        return lines\n\n      case 'TextDot':\n        geometry = obj.geometry\n\n        const ctx = document.createElement('canvas').getContext('2d')\n        const font = `${geometry.fontHeight}px ${geometry.fontFace}`\n        ctx.font = font\n        const width = ctx.measureText(geometry.text).width + 10\n        const height = geometry.fontHeight + 10\n\n        const r = window.devicePixelRatio\n\n        ctx.canvas.width = width * r\n        ctx.canvas.height = height * r\n        ctx.canvas.style.width = width + 'px'\n        ctx.canvas.style.height = height + 'px'\n        ctx.setTransform(r, 0, 0, r, 0, 0)\n\n        ctx.font = font\n        ctx.textBaseline = 'middle'\n        ctx.textAlign = 'center'\n        color = attributes.drawColor\n        ctx.fillStyle = `rgba(${color.r},${color.g},${color.b},${color.a})`\n        ctx.fillRect(0, 0, width, height)\n        ctx.fillStyle = 'white'\n        ctx.fillText(geometry.text, width / 2, height / 2)\n\n        const texture = new CanvasTexture(ctx.canvas)\n        texture.minFilter = LinearFilter\n        texture.wrapS = ClampToEdgeWrapping\n        texture.wrapT = ClampToEdgeWrapping\n\n        material = new SpriteMaterial({ map: texture, depthTest: false })\n        const sprite = new Sprite(material)\n        sprite.position.set(geometry.point[0], geometry.point[1], geometry.point[2])\n        sprite.scale.set(width / 10, height / 10, 1.0)\n\n        sprite.userData['attributes'] = attributes\n        sprite.userData['objectType'] = obj.objectType\n\n        if (attributes.name) {\n          sprite.name = attributes.name\n        }\n\n        return sprite\n\n      case 'Light':\n        geometry = obj.geometry\n\n        let light\n\n        if (geometry.isDirectionalLight) {\n          light = new DirectionalLight()\n          light.castShadow = attributes.castsShadows\n          light.position.set(geometry.location[0], geometry.location[1], geometry.location[2])\n          light.target.position.set(geometry.direction[0], geometry.direction[1], geometry.direction[2])\n          light.shadow.normalBias = 0.1\n        } else if (geometry.isPointLight) {\n          light = new PointLight()\n          light.castShadow = attributes.castsShadows\n          light.position.set(geometry.location[0], geometry.location[1], geometry.location[2])\n          light.shadow.normalBias = 0.1\n        } else if (geometry.isRectangularLight) {\n          light = new RectAreaLight()\n\n          const width = Math.abs(geometry.width[2])\n          const height = Math.abs(geometry.length[0])\n\n          light.position.set(geometry.location[0] - height / 2, geometry.location[1], geometry.location[2] - width / 2)\n\n          light.height = height\n          light.width = width\n\n          light.lookAt(new Vector3(geometry.direction[0], geometry.direction[1], geometry.direction[2]))\n        } else if (geometry.isSpotLight) {\n          light = new SpotLight()\n          light.castShadow = attributes.castsShadows\n          light.position.set(geometry.location[0], geometry.location[1], geometry.location[2])\n          light.target.position.set(geometry.direction[0], geometry.direction[1], geometry.direction[2])\n          light.angle = geometry.spotAngleRadians\n          light.shadow.normalBias = 0.1\n        } else if (geometry.isLinearLight) {\n          console.warn('THREE.3DMLoader:  No conversion exists for linear lights.')\n\n          return\n        }\n\n        if (light) {\n          light.intensity = geometry.intensity\n          _color = geometry.diffuse\n          color = new Color(_color.r / 255.0, _color.g / 255.0, _color.b / 255.0)\n          light.color = color\n          light.userData['attributes'] = attributes\n          light.userData['objectType'] = obj.objectType\n        }\n\n        return light\n    }\n  }\n\n  _initLibrary() {\n    if (!this.libraryPending) {\n      // Load rhino3dm wrapper.\n      const jsLoader = new FileLoader(this.manager)\n      jsLoader.setPath(this.libraryPath)\n      const jsContent = new Promise((resolve, reject) => {\n        jsLoader.load('rhino3dm.js', resolve, undefined, reject)\n      })\n\n      // Load rhino3dm WASM binary.\n      const binaryLoader = new FileLoader(this.manager)\n      binaryLoader.setPath(this.libraryPath)\n      binaryLoader.setResponseType('arraybuffer')\n      const binaryContent = new Promise((resolve, reject) => {\n        binaryLoader.load('rhino3dm.wasm', resolve, undefined, reject)\n      })\n\n      this.libraryPending = Promise.all([jsContent, binaryContent]).then(([jsContent, binaryContent]) => {\n        //this.libraryBinary = binaryContent;\n        this.libraryConfig.wasmBinary = binaryContent\n\n        const fn = Rhino3dmWorker.toString()\n\n        const body = [\n          '/* rhino3dm.js */',\n          jsContent,\n          '/* worker */',\n          fn.substring(fn.indexOf('{') + 1, fn.lastIndexOf('}')),\n        ].join('\\n')\n\n        this.workerSourceURL = URL.createObjectURL(new Blob([body]))\n      })\n    }\n\n    return this.libraryPending\n  }\n\n  _getWorker(taskCost) {\n    return this._initLibrary().then(() => {\n      if (this.workerPool.length < this.workerLimit) {\n        const worker = new Worker(this.workerSourceURL)\n\n        worker._callbacks = {}\n        worker._taskCosts = {}\n        worker._taskLoad = 0\n\n        worker.postMessage({\n          type: 'init',\n          libraryConfig: this.libraryConfig,\n        })\n\n        worker.onmessage = function (e) {\n          const message = e.data\n\n          switch (message.type) {\n            case 'decode':\n              worker._callbacks[message.id].resolve(message)\n              break\n\n            case 'error':\n              worker._callbacks[message.id].reject(message)\n              break\n\n            default:\n              console.error('THREE.Rhino3dmLoader: Unexpected message, \"' + message.type + '\"')\n          }\n        }\n\n        this.workerPool.push(worker)\n      } else {\n        this.workerPool.sort(function (a, b) {\n          return a._taskLoad > b._taskLoad ? -1 : 1\n        })\n      }\n\n      const worker = this.workerPool[this.workerPool.length - 1]\n\n      worker._taskLoad += taskCost\n\n      return worker\n    })\n  }\n\n  _releaseTask(worker, taskID) {\n    worker._taskLoad -= worker._taskCosts[taskID]\n    delete worker._callbacks[taskID]\n    delete worker._taskCosts[taskID]\n  }\n\n  dispose() {\n    for (let i = 0; i < this.workerPool.length; ++i) {\n      this.workerPool[i].terminate()\n    }\n\n    this.workerPool.length = 0\n\n    return this\n  }\n}\n\n/* WEB WORKER */\n\nfunction Rhino3dmWorker() {\n  let libraryPending\n  let libraryConfig\n  let rhino\n\n  onmessage = function (e) {\n    const message = e.data\n\n    switch (message.type) {\n      case 'init':\n        libraryConfig = message.libraryConfig\n        const wasmBinary = libraryConfig.wasmBinary\n        let RhinoModule\n        libraryPending = new Promise(function (resolve) {\n          /* Like Basis Loader */\n          RhinoModule = { wasmBinary, onRuntimeInitialized: resolve }\n\n          rhino3dm(RhinoModule)\n        }).then(() => {\n          rhino = RhinoModule\n        })\n\n        break\n\n      case 'decode':\n        const buffer = message.buffer\n        libraryPending.then(() => {\n          const data = decodeObjects(rhino, buffer)\n\n          self.postMessage({ type: 'decode', id: message.id, data })\n        })\n\n        break\n    }\n  }\n\n  function decodeObjects(rhino, buffer) {\n    const arr = new Uint8Array(buffer)\n    const doc = rhino.File3dm.fromByteArray(arr)\n\n    const objects = []\n    const materials = []\n    const layers = []\n    const views = []\n    const namedViews = []\n    const groups = []\n\n    //Handle objects\n\n    const objs = doc.objects()\n    const cnt = objs.count\n\n    for (let i = 0; i < cnt; i++) {\n      const _object = objs.get(i)\n\n      const object = extractObjectData(_object, doc)\n\n      _object.delete()\n\n      if (object) {\n        objects.push(object)\n      }\n    }\n\n    // Handle instance definitions\n    // console.log( `Instance Definitions Count: ${doc.instanceDefinitions().count()}` );\n\n    for (let i = 0; i < doc.instanceDefinitions().count(); i++) {\n      const idef = doc.instanceDefinitions().get(i)\n      const idefAttributes = extractProperties(idef)\n      idefAttributes.objectIds = idef.getObjectIds()\n\n      objects.push({ geometry: null, attributes: idefAttributes, objectType: 'InstanceDefinition' })\n    }\n\n    // Handle materials\n\n    const textureTypes = [\n      // rhino.TextureType.Bitmap,\n      rhino.TextureType.Diffuse,\n      rhino.TextureType.Bump,\n      rhino.TextureType.Transparency,\n      rhino.TextureType.Opacity,\n      rhino.TextureType.Emap,\n    ]\n\n    const pbrTextureTypes = [\n      rhino.TextureType.PBR_BaseColor,\n      rhino.TextureType.PBR_Subsurface,\n      rhino.TextureType.PBR_SubsurfaceScattering,\n      rhino.TextureType.PBR_SubsurfaceScatteringRadius,\n      rhino.TextureType.PBR_Metallic,\n      rhino.TextureType.PBR_Specular,\n      rhino.TextureType.PBR_SpecularTint,\n      rhino.TextureType.PBR_Roughness,\n      rhino.TextureType.PBR_Anisotropic,\n      rhino.TextureType.PBR_Anisotropic_Rotation,\n      rhino.TextureType.PBR_Sheen,\n      rhino.TextureType.PBR_SheenTint,\n      rhino.TextureType.PBR_Clearcoat,\n      rhino.TextureType.PBR_ClearcoatBump,\n      rhino.TextureType.PBR_ClearcoatRoughness,\n      rhino.TextureType.PBR_OpacityIor,\n      rhino.TextureType.PBR_OpacityRoughness,\n      rhino.TextureType.PBR_Emission,\n      rhino.TextureType.PBR_AmbientOcclusion,\n      rhino.TextureType.PBR_Displacement,\n    ]\n\n    for (let i = 0; i < doc.materials().count(); i++) {\n      const _material = doc.materials().get(i)\n      const _pbrMaterial = _material.physicallyBased()\n\n      let material = extractProperties(_material)\n\n      const textures = []\n\n      for (let j = 0; j < textureTypes.length; j++) {\n        const _texture = _material.getTexture(textureTypes[j])\n        if (_texture) {\n          let textureType = textureTypes[j].constructor.name\n          textureType = textureType.substring(12, textureType.length)\n          const texture = { type: textureType }\n\n          const image = doc.getEmbeddedFileAsBase64(_texture.fileName)\n\n          if (image) {\n            texture.image = 'data:image/png;base64,' + image\n          } else {\n            console.warn(`THREE.3DMLoader: Image for ${textureType} texture not embedded in file.`)\n            texture.image = null\n          }\n\n          textures.push(texture)\n\n          _texture.delete()\n        }\n      }\n\n      material.textures = textures\n\n      if (_pbrMaterial.supported) {\n        console.log('pbr true')\n\n        for (let j = 0; j < pbrTextureTypes.length; j++) {\n          const _texture = _material.getTexture(textureTypes[j])\n          if (_texture) {\n            const image = doc.getEmbeddedFileAsBase64(_texture.fileName)\n            let textureType = textureTypes[j].constructor.name\n            textureType = textureType.substring(12, textureType.length)\n            const texture = { type: textureType, image: 'data:image/png;base64,' + image }\n            textures.push(texture)\n\n            _texture.delete()\n          }\n        }\n\n        const pbMaterialProperties = extractProperties(_material.physicallyBased())\n\n        material = Object.assign(pbMaterialProperties, material)\n      }\n\n      materials.push(material)\n\n      _material.delete()\n      _pbrMaterial.delete()\n    }\n\n    // Handle layers\n\n    for (let i = 0; i < doc.layers().count(); i++) {\n      const _layer = doc.layers().get(i)\n      const layer = extractProperties(_layer)\n\n      layers.push(layer)\n\n      _layer.delete()\n    }\n\n    // Handle views\n\n    for (let i = 0; i < doc.views().count(); i++) {\n      const _view = doc.views().get(i)\n      const view = extractProperties(_view)\n\n      views.push(view)\n\n      _view.delete()\n    }\n\n    // Handle named views\n\n    for (let i = 0; i < doc.namedViews().count(); i++) {\n      const _namedView = doc.namedViews().get(i)\n      const namedView = extractProperties(_namedView)\n\n      namedViews.push(namedView)\n\n      _namedView.delete()\n    }\n\n    // Handle groups\n\n    for (let i = 0; i < doc.groups().count(); i++) {\n      const _group = doc.groups().get(i)\n      const group = extractProperties(_group)\n\n      groups.push(group)\n\n      _group.delete()\n    }\n\n    // Handle settings\n\n    const settings = extractProperties(doc.settings())\n\n    //TODO: Handle other document stuff like dimstyles, instance definitions, bitmaps etc.\n\n    // Handle dimstyles\n    // console.log( `Dimstyle Count: ${doc.dimstyles().count()}` );\n\n    // Handle bitmaps\n    // console.log( `Bitmap Count: ${doc.bitmaps().count()}` );\n\n    // Handle strings -- this seems to be broken at the moment in rhino3dm\n    // console.log( `Document Strings Count: ${doc.strings().count()}` );\n\n    /*\n\t\tfor( var i = 0; i < doc.strings().count(); i++ ){\n\n\t\t\tvar _string= doc.strings().get( i );\n\n\t\t\tconsole.log(_string);\n\t\t\tvar string = extractProperties( _group );\n\n\t\t\tstrings.push( string );\n\n\t\t\t_string.delete();\n\n\t\t}\n\t\t*/\n\n    doc.delete()\n\n    return { objects, materials, layers, views, namedViews, groups, settings }\n  }\n\n  function extractObjectData(object, doc) {\n    const _geometry = object.geometry()\n    const _attributes = object.attributes()\n    let objectType = _geometry.objectType\n    let geometry, attributes, position, data, mesh\n\n    // skip instance definition objects\n    //if( _attributes.isInstanceDefinitionObject ) { continue; }\n\n    // TODO: handle other geometry types\n    switch (objectType) {\n      case rhino.ObjectType.Curve:\n        const pts = curveToPoints(_geometry, 100)\n\n        position = {}\n        attributes = {}\n        data = {}\n\n        position.itemSize = 3\n        position.type = 'Float32Array'\n        position.array = []\n\n        for (let j = 0; j < pts.length; j++) {\n          position.array.push(pts[j][0])\n          position.array.push(pts[j][1])\n          position.array.push(pts[j][2])\n        }\n\n        attributes.position = position\n        data.attributes = attributes\n\n        geometry = { data }\n\n        break\n\n      case rhino.ObjectType.Point:\n        const pt = _geometry.location\n\n        position = {}\n        const color = {}\n        attributes = {}\n        data = {}\n\n        position.itemSize = 3\n        position.type = 'Float32Array'\n        position.array = [pt[0], pt[1], pt[2]]\n\n        const _color = _attributes.drawColor(doc)\n\n        color.itemSize = 3\n        color.type = 'Float32Array'\n        color.array = [_color.r / 255.0, _color.g / 255.0, _color.b / 255.0]\n\n        attributes.position = position\n        attributes.color = color\n        data.attributes = attributes\n\n        geometry = { data }\n\n        break\n\n      case rhino.ObjectType.PointSet:\n      case rhino.ObjectType.Mesh:\n        geometry = _geometry.toThreejsJSON()\n\n        break\n\n      case rhino.ObjectType.Brep:\n        const faces = _geometry.faces()\n        mesh = new rhino.Mesh()\n\n        for (let faceIndex = 0; faceIndex < faces.count; faceIndex++) {\n          const face = faces.get(faceIndex)\n          const _mesh = face.getMesh(rhino.MeshType.Any)\n\n          if (_mesh) {\n            mesh.append(_mesh)\n            _mesh.delete()\n          }\n\n          face.delete()\n        }\n\n        if (mesh.faces().count > 0) {\n          mesh.compact()\n          geometry = mesh.toThreejsJSON()\n          faces.delete()\n        }\n\n        mesh.delete()\n\n        break\n\n      case rhino.ObjectType.Extrusion:\n        mesh = _geometry.getMesh(rhino.MeshType.Any)\n\n        if (mesh) {\n          geometry = mesh.toThreejsJSON()\n          mesh.delete()\n        }\n\n        break\n\n      case rhino.ObjectType.TextDot:\n        geometry = extractProperties(_geometry)\n\n        break\n\n      case rhino.ObjectType.Light:\n        geometry = extractProperties(_geometry)\n\n        break\n\n      case rhino.ObjectType.InstanceReference:\n        geometry = extractProperties(_geometry)\n        geometry.xform = extractProperties(_geometry.xform)\n        geometry.xform.array = _geometry.xform.toFloatArray(true)\n\n        break\n\n      case rhino.ObjectType.SubD:\n        // TODO: precalculate resulting vertices and faces and warn on excessive results\n        _geometry.subdivide(3)\n        mesh = rhino.Mesh.createFromSubDControlNet(_geometry)\n        if (mesh) {\n          geometry = mesh.toThreejsJSON()\n          mesh.delete()\n        }\n\n        break\n\n      /*\n\t\t\t\tcase rhino.ObjectType.Annotation:\n\t\t\t\tcase rhino.ObjectType.Hatch:\n\t\t\t\tcase rhino.ObjectType.ClipPlane:\n\t\t\t\t*/\n\n      default:\n        console.warn(`THREE.3DMLoader: TODO: Implement ${objectType.constructor.name}`)\n        break\n    }\n\n    if (geometry) {\n      attributes = extractProperties(_attributes)\n      attributes.geometry = extractProperties(_geometry)\n\n      if (_attributes.groupCount > 0) {\n        attributes.groupIds = _attributes.getGroupList()\n      }\n\n      if (_attributes.userStringCount > 0) {\n        attributes.userStrings = _attributes.getUserStrings()\n      }\n\n      if (_geometry.userStringCount > 0) {\n        attributes.geometry.userStrings = _geometry.getUserStrings()\n      }\n\n      attributes.drawColor = _attributes.drawColor(doc)\n\n      objectType = objectType.constructor.name\n      objectType = objectType.substring(11, objectType.length)\n\n      return { geometry, attributes, objectType }\n    } else {\n      console.warn(`THREE.3DMLoader: ${objectType.constructor.name} has no associated mesh geometry.`)\n    }\n  }\n\n  function extractProperties(object) {\n    const result = {}\n\n    for (const property in object) {\n      const value = object[property]\n\n      if (typeof value !== 'function') {\n        if (typeof value === 'object' && value !== null && value.hasOwnProperty('constructor')) {\n          result[property] = { name: value.constructor.name, value: value.value }\n        } else {\n          result[property] = value\n        }\n      } else {\n        // these are functions that could be called to extract more data.\n        //console.log( `${property}: ${object[ property ].constructor.name}` );\n      }\n    }\n\n    return result\n  }\n\n  function curveToPoints(curve, pointLimit) {\n    let pointCount = pointLimit\n    let rc = []\n    const ts = []\n\n    if (curve instanceof rhino.LineCurve) {\n      return [curve.pointAtStart, curve.pointAtEnd]\n    }\n\n    if (curve instanceof rhino.PolylineCurve) {\n      pointCount = curve.pointCount\n      for (let i = 0; i < pointCount; i++) {\n        rc.push(curve.point(i))\n      }\n\n      return rc\n    }\n\n    if (curve instanceof rhino.PolyCurve) {\n      const segmentCount = curve.segmentCount\n\n      for (let i = 0; i < segmentCount; i++) {\n        const segment = curve.segmentCurve(i)\n        const segmentArray = curveToPoints(segment, pointCount)\n        rc = rc.concat(segmentArray)\n        segment.delete()\n      }\n\n      return rc\n    }\n\n    if (curve instanceof rhino.ArcCurve) {\n      pointCount = Math.floor(curve.angleDegrees / 5)\n      pointCount = pointCount < 2 ? 2 : pointCount\n      // alternative to this hardcoded version: https://stackoverflow.com/a/18499923/2179399\n    }\n\n    if (curve instanceof rhino.NurbsCurve && curve.degree === 1) {\n      const pLine = curve.tryGetPolyline()\n\n      for (let i = 0; i < pLine.count; i++) {\n        rc.push(pLine.get(i))\n      }\n\n      pLine.delete()\n\n      return rc\n    }\n\n    const domain = curve.domain\n    const divisions = pointCount - 1.0\n\n    for (let j = 0; j < pointCount; j++) {\n      const t = domain[0] + (j / divisions) * (domain[1] - domain[0])\n\n      if (t === domain[0] || t === domain[1]) {\n        ts.push(t)\n        continue\n      }\n\n      const tan = curve.tangentAt(t)\n      const prevTan = curve.tangentAt(ts.slice(-1)[0])\n\n      // Duplicated from THREE.Vector3\n      // How to pass imports to worker?\n\n      const tS = tan[0] * tan[0] + tan[1] * tan[1] + tan[2] * tan[2]\n      const ptS = prevTan[0] * prevTan[0] + prevTan[1] * prevTan[1] + prevTan[2] * prevTan[2]\n\n      const denominator = Math.sqrt(tS * ptS)\n\n      let angle\n\n      if (denominator === 0) {\n        angle = Math.PI / 2\n      } else {\n        const theta = (tan.x * prevTan.x + tan.y * prevTan.y + tan.z * prevTan.z) / denominator\n        angle = Math.acos(Math.max(-1, Math.min(1, theta)))\n      }\n\n      if (angle < 0.1) continue\n\n      ts.push(t)\n    }\n\n    rc = ts.map((t) => curve.pointAt(t))\n    return rc\n  }\n}\n\nexport { Rhino3dmLoader }\n"], "names": ["width", "height", "js<PERSON><PERSON><PERSON>", "binaryContent", "worker", "rhino"], "mappings": ";AA0BA,MAAM,aAAa,oBAAI,QAAS;AAEhC,MAAM,uBAAuB,OAAO;AAAA,EAClC,YAAY,SAAS;AACnB,UAAM,OAAO;AAEb,SAAK,cAAc;AACnB,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB,CAAE;AAEvB,SAAK,MAAM;AAEX,SAAK,cAAc;AACnB,SAAK,aAAa,CAAE;AACpB,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AACvB,SAAK,eAAe,CAAE;AAEtB,SAAK,YAAY,CAAE;AAAA,EACpB;AAAA,EAED,eAAe,MAAM;AACnB,SAAK,cAAc;AAEnB,WAAO;AAAA,EACR;AAAA,EAED,eAAe,aAAa;AAC1B,SAAK,cAAc;AAEnB,WAAO;AAAA,EACR;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,SAAS,IAAI,WAAW,KAAK,OAAO;AAE1C,WAAO,QAAQ,KAAK,IAAI;AACxB,WAAO,gBAAgB,aAAa;AACpC,WAAO,iBAAiB,KAAK,aAAa;AAE1C,SAAK,MAAM;AAEX,WAAO;AAAA,MACL;AAAA,MACA,CAAC,WAAW;AAGV,YAAI,WAAW,IAAI,MAAM,GAAG;AAC1B,gBAAM,aAAa,WAAW,IAAI,MAAM;AAExC,iBAAO,WAAW,QAAQ,KAAK,MAAM,EAAE,MAAM,OAAO;AAAA,QACrD;AAED,aAAK,cAAc,QAAQ,GAAG,EAAE,KAAK,MAAM,EAAE,MAAM,OAAO;AAAA,MAC3D;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,QAAQ;AACN,YAAQ;AAAA,MACN;AAAA,MACA,KAAK,WAAW,IAAI,CAAC,WAAW,OAAO,SAAS;AAAA,IACjD;AAAA,EACF;AAAA,EAED,cAAc,QAAQ,KAAK;AACzB,QAAI;AACJ,QAAI;AAEJ,UAAM,WAAW,OAAO;AAExB,UAAM,gBAAgB,KAAK,WAAW,QAAQ,EAC3C,KAAK,CAAC,YAAY;AACjB,eAAS;AACT,eAAS,KAAK;AAEd,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,eAAO,WAAW,MAAM,IAAI,EAAE,SAAS,OAAQ;AAE/C,eAAO,YAAY,EAAE,MAAM,UAAU,IAAI,QAAQ,OAAM,GAAI,CAAC,MAAM,CAAC;AAAA,MAG7E,CAAS;AAAA,IACT,CAAO,EACA,KAAK,CAAC,YAAY,KAAK,gBAAgB,QAAQ,IAAI,CAAC;AAIvD,kBACG,MAAM,MAAM,IAAI,EAChB,KAAK,MAAM;AACV,UAAI,UAAU,QAAQ;AACpB,aAAK,aAAa,QAAQ,MAAM;AAAA,MAGjC;AAAA,IACT,CAAO;AAGH,eAAW,IAAI,QAAQ;AAAA,MACrB;AAAA,MACA,SAAS;AAAA,IACf,CAAK;AAED,WAAO;AAAA,EACR;AAAA,EAED,MAAM,MAAM,QAAQ,SAAS;AAC3B,SAAK,cAAc,MAAM,EAAE,EAAE,KAAK,MAAM,EAAE,MAAM,OAAO;AAAA,EACxD;AAAA,EAED,kBAAkB,UAAU;AAC1B,UAAM,MAAM,CAAE;AACd,QAAI,OAAO,SAAS;AACpB,QAAI,QAAQ,CAAE;AACd,QAAI,MAAM,IAAI,SAAS,MAAM;AAC7B,QAAI,MAAM,IAAI,SAAS,MAAM;AAC7B,QAAI,MAAM,IAAI,SAAS,MAAM;AAC7B,QAAI,OAAO,SAAS;AAEpB,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,YAAM,IAAI,KAAK,UAAU,CAAC;AAC1B,YAAM,OAAO,CAAE;AACf,WAAK,OAAO,EAAE;AACd,WAAK,QAAQ,CAAE;AACf,WAAK,MAAM,IAAI,EAAE,MAAM;AACvB,WAAK,MAAM,IAAI,EAAE,MAAM;AACvB,WAAK,MAAM,IAAI,EAAE,MAAM;AACvB,WAAK,OAAO,EAAE;AAEd,UAAI,KAAK,UAAU,GAAG,MAAM,KAAK,UAAU,IAAI,GAAG;AAChD,eAAO;AAAA,MACR;AAAA,IACF;AAED,SAAK,UAAU,KAAK,QAAQ;AAE5B,WAAO;AAAA,EACR;AAAA,EAED,gBAAgB,UAAU;AACxB,QAAI,aAAa,QAAW;AAC1B,aAAO,IAAI,qBAAqB;AAAA,QAC9B,OAAO,IAAI,MAAM,GAAG,GAAG,CAAC;AAAA,QACxB,WAAW;AAAA,QACX,MAAM;AAAA,QACN,MAAM;AAAA,MACd,CAAO;AAAA,IACF;AAED,UAAM,gBAAgB,SAAS;AAE/B,UAAM,eAAe,IAAI,MAAM,cAAc,IAAI,KAAO,cAAc,IAAI,KAAO,cAAc,IAAI,GAAK;AAExG,QAAI,cAAc,MAAM,KAAK,cAAc,MAAM,KAAK,cAAc,MAAM,GAAG;AAC3E,mBAAa,IAAI;AACjB,mBAAa,IAAI;AACjB,mBAAa,IAAI;AAAA,IAClB;AAID,UAAM,MAAM,IAAI,qBAAqB;AAAA,MACnC,OAAO;AAAA,MACP,MAAM,SAAS;AAAA,MACf,MAAM;AAAA,MACN,aAAa,SAAS,eAAe,IAAI,OAAO;AAAA,MAChD,SAAS,IAAM,SAAS;AAAA,IAC9B,CAAK;AAED,UAAM,gBAAgB,IAAI,cAAe;AAEzC,aAAS,IAAI,GAAG,IAAI,SAAS,SAAS,QAAQ,KAAK;AACjD,YAAM,UAAU,SAAS,SAAS,CAAC;AAEnC,UAAI,QAAQ,UAAU,MAAM;AAC1B,cAAM,MAAM,cAAc,KAAK,QAAQ,KAAK;AAE5C,gBAAQ,QAAQ,MAAI;AAAA,UAClB,KAAK;AACH,gBAAI,MAAM;AAEV;AAAA,UAEF,KAAK;AACH,gBAAI,UAAU;AAEd;AAAA,UAEF,KAAK;AACH,gBAAI,WAAW;AACf,gBAAI,cAAc;AAElB;AAAA,UAEF,KAAK;AACH,gBAAI,SAAS;AAEb;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAED,WAAO;AAAA,EACR;AAAA,EAED,gBAAgB,MAAM;AAGpB,UAAM,SAAS,IAAI,SAAU;AAC7B,UAAM,4BAA4B,CAAE;AACpC,UAAM,sBAAsB,CAAE;AAC9B,UAAM,qBAAqB,CAAE;AAE7B,WAAO,SAAS,QAAQ,IAAI,KAAK;AACjC,WAAO,SAAS,QAAQ,IAAI,KAAK;AACjC,WAAO,SAAS,UAAU,IAAI,KAAK;AACnC,WAAO,SAAS,YAAY,IAAI;AAChC,WAAO,SAAS,WAAW,IAAI;AAC/B,WAAO,OAAO,KAAK;AAEnB,QAAI,UAAU,KAAK;AACnB,UAAM,YAAY,KAAK;AAEvB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAM,MAAM,QAAQ,CAAC;AACrB,YAAM,aAAa,IAAI;AAEvB,cAAQ,IAAI,YAAU;AAAA,QACpB,KAAK;AACH,8BAAoB,KAAK,GAAG;AAE5B;AAAA,QAEF,KAAK;AACH,6BAAmB,KAAK,GAAG;AAE3B;AAAA,QAEF;AACE,cAAI;AAEJ,cAAI,WAAW,iBAAiB,GAAG;AACjC,kBAAM,YAAY,UAAU,WAAW,aAAa;AACpD,gBAAI,WAAW,KAAK,gBAAgB,SAAS;AAC7C,uBAAW,KAAK,kBAAkB,QAAQ;AAC1C,sBAAU,KAAK,cAAc,KAAK,QAAQ;AAAA,UACtD,OAAiB;AACL,kBAAM,WAAW,KAAK,gBAAiB;AACvC,sBAAU,KAAK,cAAc,KAAK,QAAQ;AAAA,UAC3C;AAED,cAAI,YAAY,QAAW;AACzB;AAAA,UACD;AAED,gBAAM,QAAQ,KAAK,OAAO,WAAW,UAAU;AAE/C,kBAAQ,UAAU,QAAQ,KAAK,OAAO,WAAW,UAAU,EAAE,UAAU;AAEvE,cAAI,WAAW,4BAA4B;AACzC,sCAA0B,KAAK,OAAO;AAAA,UAClD,OAAiB;AACL,mBAAO,IAAI,OAAO;AAAA,UACnB;AAED;AAAA,MACH;AAAA,IACF;AAED,aAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACnD,YAAM,OAAO,oBAAoB,CAAC;AAElC,gBAAU,CAAE;AAEZ,eAAS,IAAI,GAAG,IAAI,KAAK,WAAW,UAAU,QAAQ,KAAK;AACzD,cAAM,QAAQ,KAAK,WAAW,UAAU,CAAC;AAEzC,iBAAS,IAAI,GAAG,IAAI,0BAA0B,QAAQ,KAAK;AACzD,gBAAM,QAAQ,0BAA0B,CAAC,EAAE,SAAS,WAAW;AAE/D,cAAI,UAAU,OAAO;AACnB,oBAAQ,KAAK,0BAA0B,CAAC,CAAC;AAAA,UAC1C;AAAA,QACF;AAAA,MACF;AAID,eAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,KAAK;AAClD,cAAM,OAAO,mBAAmB,CAAC;AAEjC,YAAI,KAAK,SAAS,iBAAiB,KAAK,WAAW,IAAI;AACrD,gBAAM,aAAa,IAAI,SAAU;AACjC,gBAAM,KAAK,KAAK,SAAS,MAAM;AAE/B,gBAAM,SAAS,IAAI,QAAS;AAC5B,iBAAO;AAAA,YACL,GAAG,CAAC;AAAA,YACJ,GAAG,CAAC;AAAA,YACJ,GAAG,CAAC;AAAA,YACJ,GAAG,CAAC;AAAA,YACJ,GAAG,CAAC;AAAA,YACJ,GAAG,CAAC;AAAA,YACJ,GAAG,CAAC;AAAA,YACJ,GAAG,CAAC;AAAA,YACJ,GAAG,CAAC;AAAA,YACJ,GAAG,CAAC;AAAA,YACJ,GAAG,EAAE;AAAA,YACL,GAAG,EAAE;AAAA,YACL,GAAG,EAAE;AAAA,YACL,GAAG,EAAE;AAAA,YACL,GAAG,EAAE;AAAA,YACL,GAAG,EAAE;AAAA,UACN;AAED,qBAAW,aAAa,MAAM;AAE9B,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,uBAAW,IAAI,QAAQ,CAAC,EAAE,MAAM,IAAI,CAAC;AAAA,UACtC;AAED,iBAAO,IAAI,UAAU;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AAED,WAAO,SAAS,WAAW,IAAI,KAAK;AACpC,WAAO;AAAA,EACR;AAAA,EAED,cAAc,KAAK,KAAK;AACtB,UAAM,SAAS,IAAI,qBAAsB;AAEzC,UAAM,aAAa,IAAI;AAEvB,QAAI,UAAU,UAAU,QAAQ;AAEhC,YAAQ,IAAI,YAAU;AAAA,MACpB,KAAK;AAAA,MACL,KAAK;AACH,mBAAW,OAAO,MAAM,IAAI,QAAQ;AAEpC,YAAI,SAAS,WAAW,eAAe,OAAO,GAAG;AAC/C,qBAAW,IAAI,eAAe,EAAE,cAAc,MAAM,iBAAiB,OAAO,MAAM,GAAG;AAAA,QAC/F,OAAe;AACL,mBAAS,WAAW;AACpB,kBAAQ,IAAI,MAAM,OAAO,IAAI,KAAO,OAAO,IAAI,KAAO,OAAO,IAAI,GAAK;AACtE,qBAAW,IAAI,eAAe,EAAE,OAAc,iBAAiB,OAAO,MAAM,GAAG;AAAA,QAChF;AAED,mBAAW,KAAK,kBAAkB,QAAQ;AAE1C,cAAM,SAAS,IAAI,OAAO,UAAU,QAAQ;AAC5C,eAAO,SAAS,YAAY,IAAI;AAChC,eAAO,SAAS,YAAY,IAAI,IAAI;AAEpC,YAAI,WAAW,MAAM;AACnB,iBAAO,OAAO,WAAW;AAAA,QAC1B;AAED,eAAO;AAAA,MAET,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,YAAI,IAAI,aAAa;AAAM;AAE3B,mBAAW,OAAO,MAAM,IAAI,QAAQ;AAEpC,YAAI,SAAS,WAAW,eAAe,OAAO,GAAG;AAC/C,cAAI,eAAe;AAAA,QACpB;AAED,YAAI,QAAQ,MAAM;AAChB,gBAAM,KAAK,gBAAiB;AAC5B,gBAAM,KAAK,kBAAkB,GAAG;AAAA,QACjC;AAED,cAAM,OAAO,IAAI,KAAK,UAAU,GAAG;AACnC,aAAK,aAAa,WAAW;AAC7B,aAAK,gBAAgB,WAAW;AAChC,aAAK,SAAS,YAAY,IAAI;AAC9B,aAAK,SAAS,YAAY,IAAI,IAAI;AAElC,YAAI,WAAW,MAAM;AACnB,eAAK,OAAO,WAAW;AAAA,QACxB;AAED,eAAO;AAAA,MAET,KAAK;AACH,mBAAW,OAAO,MAAM,IAAI,QAAQ;AAEpC,iBAAS,WAAW;AACpB,gBAAQ,IAAI,MAAM,OAAO,IAAI,KAAO,OAAO,IAAI,KAAO,OAAO,IAAI,GAAK;AAEtE,mBAAW,IAAI,kBAAkB,EAAE,MAAY,CAAE;AACjD,mBAAW,KAAK,kBAAkB,QAAQ;AAE1C,cAAM,QAAQ,IAAI,KAAK,UAAU,QAAQ;AACzC,cAAM,SAAS,YAAY,IAAI;AAC/B,cAAM,SAAS,YAAY,IAAI,IAAI;AAEnC,YAAI,WAAW,MAAM;AACnB,gBAAM,OAAO,WAAW;AAAA,QACzB;AAED,eAAO;AAAA,MAET,KAAK;AACH,mBAAW,IAAI;AAEf,cAAM,MAAM,SAAS,cAAc,QAAQ,EAAE,WAAW,IAAI;AAC5D,cAAM,OAAO,GAAG,SAAS,gBAAgB,SAAS;AAClD,YAAI,OAAO;AACX,cAAM,QAAQ,IAAI,YAAY,SAAS,IAAI,EAAE,QAAQ;AACrD,cAAM,SAAS,SAAS,aAAa;AAErC,cAAM,IAAI,OAAO;AAEjB,YAAI,OAAO,QAAQ,QAAQ;AAC3B,YAAI,OAAO,SAAS,SAAS;AAC7B,YAAI,OAAO,MAAM,QAAQ,QAAQ;AACjC,YAAI,OAAO,MAAM,SAAS,SAAS;AACnC,YAAI,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAEjC,YAAI,OAAO;AACX,YAAI,eAAe;AACnB,YAAI,YAAY;AAChB,gBAAQ,WAAW;AACnB,YAAI,YAAY,QAAQ,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAC/D,YAAI,SAAS,GAAG,GAAG,OAAO,MAAM;AAChC,YAAI,YAAY;AAChB,YAAI,SAAS,SAAS,MAAM,QAAQ,GAAG,SAAS,CAAC;AAEjD,cAAM,UAAU,IAAI,cAAc,IAAI,MAAM;AAC5C,gBAAQ,YAAY;AACpB,gBAAQ,QAAQ;AAChB,gBAAQ,QAAQ;AAEhB,mBAAW,IAAI,eAAe,EAAE,KAAK,SAAS,WAAW,OAAO;AAChE,cAAM,SAAS,IAAI,OAAO,QAAQ;AAClC,eAAO,SAAS,IAAI,SAAS,MAAM,CAAC,GAAG,SAAS,MAAM,CAAC,GAAG,SAAS,MAAM,CAAC,CAAC;AAC3E,eAAO,MAAM,IAAI,QAAQ,IAAI,SAAS,IAAI,CAAG;AAE7C,eAAO,SAAS,YAAY,IAAI;AAChC,eAAO,SAAS,YAAY,IAAI,IAAI;AAEpC,YAAI,WAAW,MAAM;AACnB,iBAAO,OAAO,WAAW;AAAA,QAC1B;AAED,eAAO;AAAA,MAET,KAAK;AACH,mBAAW,IAAI;AAEf,YAAI;AAEJ,YAAI,SAAS,oBAAoB;AAC/B,kBAAQ,IAAI,iBAAkB;AAC9B,gBAAM,aAAa,WAAW;AAC9B,gBAAM,SAAS,IAAI,SAAS,SAAS,CAAC,GAAG,SAAS,SAAS,CAAC,GAAG,SAAS,SAAS,CAAC,CAAC;AACnF,gBAAM,OAAO,SAAS,IAAI,SAAS,UAAU,CAAC,GAAG,SAAS,UAAU,CAAC,GAAG,SAAS,UAAU,CAAC,CAAC;AAC7F,gBAAM,OAAO,aAAa;AAAA,QACpC,WAAmB,SAAS,cAAc;AAChC,kBAAQ,IAAI,WAAY;AACxB,gBAAM,aAAa,WAAW;AAC9B,gBAAM,SAAS,IAAI,SAAS,SAAS,CAAC,GAAG,SAAS,SAAS,CAAC,GAAG,SAAS,SAAS,CAAC,CAAC;AACnF,gBAAM,OAAO,aAAa;AAAA,QACpC,WAAmB,SAAS,oBAAoB;AACtC,kBAAQ,IAAI,cAAe;AAE3B,gBAAMA,SAAQ,KAAK,IAAI,SAAS,MAAM,CAAC,CAAC;AACxC,gBAAMC,UAAS,KAAK,IAAI,SAAS,OAAO,CAAC,CAAC;AAE1C,gBAAM,SAAS,IAAI,SAAS,SAAS,CAAC,IAAIA,UAAS,GAAG,SAAS,SAAS,CAAC,GAAG,SAAS,SAAS,CAAC,IAAID,SAAQ,CAAC;AAE5G,gBAAM,SAASC;AACf,gBAAM,QAAQD;AAEd,gBAAM,OAAO,IAAI,QAAQ,SAAS,UAAU,CAAC,GAAG,SAAS,UAAU,CAAC,GAAG,SAAS,UAAU,CAAC,CAAC,CAAC;AAAA,QACvG,WAAmB,SAAS,aAAa;AAC/B,kBAAQ,IAAI,UAAW;AACvB,gBAAM,aAAa,WAAW;AAC9B,gBAAM,SAAS,IAAI,SAAS,SAAS,CAAC,GAAG,SAAS,SAAS,CAAC,GAAG,SAAS,SAAS,CAAC,CAAC;AACnF,gBAAM,OAAO,SAAS,IAAI,SAAS,UAAU,CAAC,GAAG,SAAS,UAAU,CAAC,GAAG,SAAS,UAAU,CAAC,CAAC;AAC7F,gBAAM,QAAQ,SAAS;AACvB,gBAAM,OAAO,aAAa;AAAA,QACpC,WAAmB,SAAS,eAAe;AACjC,kBAAQ,KAAK,2DAA2D;AAExE;AAAA,QACD;AAED,YAAI,OAAO;AACT,gBAAM,YAAY,SAAS;AAC3B,mBAAS,SAAS;AAClB,kBAAQ,IAAI,MAAM,OAAO,IAAI,KAAO,OAAO,IAAI,KAAO,OAAO,IAAI,GAAK;AACtE,gBAAM,QAAQ;AACd,gBAAM,SAAS,YAAY,IAAI;AAC/B,gBAAM,SAAS,YAAY,IAAI,IAAI;AAAA,QACpC;AAED,eAAO;AAAA,IACV;AAAA,EACF;AAAA,EAED,eAAe;AACb,QAAI,CAAC,KAAK,gBAAgB;AAExB,YAAM,WAAW,IAAI,WAAW,KAAK,OAAO;AAC5C,eAAS,QAAQ,KAAK,WAAW;AACjC,YAAM,YAAY,IAAI,QAAQ,CAAC,SAAS,WAAW;AACjD,iBAAS,KAAK,eAAe,SAAS,QAAW,MAAM;AAAA,MAC/D,CAAO;AAGD,YAAM,eAAe,IAAI,WAAW,KAAK,OAAO;AAChD,mBAAa,QAAQ,KAAK,WAAW;AACrC,mBAAa,gBAAgB,aAAa;AAC1C,YAAM,gBAAgB,IAAI,QAAQ,CAAC,SAAS,WAAW;AACrD,qBAAa,KAAK,iBAAiB,SAAS,QAAW,MAAM;AAAA,MACrE,CAAO;AAED,WAAK,iBAAiB,QAAQ,IAAI,CAAC,WAAW,aAAa,CAAC,EAAE,KAAK,CAAC,CAACE,YAAWC,cAAa,MAAM;AAEjG,aAAK,cAAc,aAAaA;AAEhC,cAAM,KAAK,eAAe,SAAU;AAEpC,cAAM,OAAO;AAAA,UACX;AAAA,UACAD;AAAA,UACA;AAAA,UACA,GAAG,UAAU,GAAG,QAAQ,GAAG,IAAI,GAAG,GAAG,YAAY,GAAG,CAAC;AAAA,QAC/D,EAAU,KAAK,IAAI;AAEX,aAAK,kBAAkB,IAAI,gBAAgB,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;AAAA,MACnE,CAAO;AAAA,IACF;AAED,WAAO,KAAK;AAAA,EACb;AAAA,EAED,WAAW,UAAU;AACnB,WAAO,KAAK,eAAe,KAAK,MAAM;AACpC,UAAI,KAAK,WAAW,SAAS,KAAK,aAAa;AAC7C,cAAME,UAAS,IAAI,OAAO,KAAK,eAAe;AAE9C,QAAAA,QAAO,aAAa,CAAE;AACtB,QAAAA,QAAO,aAAa,CAAE;AACtB,QAAAA,QAAO,YAAY;AAEnB,QAAAA,QAAO,YAAY;AAAA,UACjB,MAAM;AAAA,UACN,eAAe,KAAK;AAAA,QAC9B,CAAS;AAED,QAAAA,QAAO,YAAY,SAAU,GAAG;AAC9B,gBAAM,UAAU,EAAE;AAElB,kBAAQ,QAAQ,MAAI;AAAA,YAClB,KAAK;AACH,cAAAA,QAAO,WAAW,QAAQ,EAAE,EAAE,QAAQ,OAAO;AAC7C;AAAA,YAEF,KAAK;AACH,cAAAA,QAAO,WAAW,QAAQ,EAAE,EAAE,OAAO,OAAO;AAC5C;AAAA,YAEF;AACE,sBAAQ,MAAM,gDAAgD,QAAQ,OAAO,GAAG;AAAA,UACnF;AAAA,QACF;AAED,aAAK,WAAW,KAAKA,OAAM;AAAA,MACnC,OAAa;AACL,aAAK,WAAW,KAAK,SAAU,GAAG,GAAG;AACnC,iBAAO,EAAE,YAAY,EAAE,YAAY,KAAK;AAAA,QAClD,CAAS;AAAA,MACF;AAED,YAAM,SAAS,KAAK,WAAW,KAAK,WAAW,SAAS,CAAC;AAEzD,aAAO,aAAa;AAEpB,aAAO;AAAA,IACb,CAAK;AAAA,EACF;AAAA,EAED,aAAa,QAAQ,QAAQ;AAC3B,WAAO,aAAa,OAAO,WAAW,MAAM;AAC5C,WAAO,OAAO,WAAW,MAAM;AAC/B,WAAO,OAAO,WAAW,MAAM;AAAA,EAChC;AAAA,EAED,UAAU;AACR,aAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,EAAE,GAAG;AAC/C,WAAK,WAAW,CAAC,EAAE,UAAW;AAAA,IAC/B;AAED,SAAK,WAAW,SAAS;AAEzB,WAAO;AAAA,EACR;AACH;AAIA,SAAS,iBAAiB;AACxB,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,cAAY,SAAU,GAAG;AACvB,UAAM,UAAU,EAAE;AAElB,YAAQ,QAAQ,MAAI;AAAA,MAClB,KAAK;AACH,wBAAgB,QAAQ;AACxB,cAAM,aAAa,cAAc;AACjC,YAAI;AACJ,yBAAiB,IAAI,QAAQ,SAAU,SAAS;AAE9C,wBAAc,EAAE,YAAY,sBAAsB,QAAS;AAE3D,mBAAS,WAAW;AAAA,QAC9B,CAAS,EAAE,KAAK,MAAM;AACZ,kBAAQ;AAAA,QAClB,CAAS;AAED;AAAA,MAEF,KAAK;AACH,cAAM,SAAS,QAAQ;AACvB,uBAAe,KAAK,MAAM;AACxB,gBAAM,OAAO,cAAc,OAAO,MAAM;AAExC,eAAK,YAAY,EAAE,MAAM,UAAU,IAAI,QAAQ,IAAI,MAAM;AAAA,QACnE,CAAS;AAED;AAAA,IACH;AAAA,EACF;AAED,WAAS,cAAcC,QAAO,QAAQ;AACpC,UAAM,MAAM,IAAI,WAAW,MAAM;AACjC,UAAM,MAAMA,OAAM,QAAQ,cAAc,GAAG;AAE3C,UAAM,UAAU,CAAE;AAClB,UAAM,YAAY,CAAE;AACpB,UAAM,SAAS,CAAE;AACjB,UAAM,QAAQ,CAAE;AAChB,UAAM,aAAa,CAAE;AACrB,UAAM,SAAS,CAAE;AAIjB,UAAM,OAAO,IAAI,QAAS;AAC1B,UAAM,MAAM,KAAK;AAEjB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAM,UAAU,KAAK,IAAI,CAAC;AAE1B,YAAM,SAAS,kBAAkB,SAAS,GAAG;AAE7C,cAAQ,OAAQ;AAEhB,UAAI,QAAQ;AACV,gBAAQ,KAAK,MAAM;AAAA,MACpB;AAAA,IACF;AAKD,aAAS,IAAI,GAAG,IAAI,IAAI,sBAAsB,SAAS,KAAK;AAC1D,YAAM,OAAO,IAAI,oBAAmB,EAAG,IAAI,CAAC;AAC5C,YAAM,iBAAiB,kBAAkB,IAAI;AAC7C,qBAAe,YAAY,KAAK,aAAc;AAE9C,cAAQ,KAAK,EAAE,UAAU,MAAM,YAAY,gBAAgB,YAAY,sBAAsB;AAAA,IAC9F;AAID,UAAM,eAAe;AAAA;AAAA,MAEnBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,IACnB;AAED,UAAM,kBAAkB;AAAA,MACtBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,MAClBA,OAAM,YAAY;AAAA,IACnB;AAED,aAAS,IAAI,GAAG,IAAI,IAAI,YAAY,SAAS,KAAK;AAChD,YAAM,YAAY,IAAI,UAAS,EAAG,IAAI,CAAC;AACvC,YAAM,eAAe,UAAU,gBAAiB;AAEhD,UAAI,WAAW,kBAAkB,SAAS;AAE1C,YAAM,WAAW,CAAE;AAEnB,eAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,cAAM,WAAW,UAAU,WAAW,aAAa,CAAC,CAAC;AACrD,YAAI,UAAU;AACZ,cAAI,cAAc,aAAa,CAAC,EAAE,YAAY;AAC9C,wBAAc,YAAY,UAAU,IAAI,YAAY,MAAM;AAC1D,gBAAM,UAAU,EAAE,MAAM,YAAa;AAErC,gBAAM,QAAQ,IAAI,wBAAwB,SAAS,QAAQ;AAE3D,cAAI,OAAO;AACT,oBAAQ,QAAQ,2BAA2B;AAAA,UACvD,OAAiB;AACL,oBAAQ,KAAK,8BAA8B,2CAA2C;AACtF,oBAAQ,QAAQ;AAAA,UACjB;AAED,mBAAS,KAAK,OAAO;AAErB,mBAAS,OAAQ;AAAA,QAClB;AAAA,MACF;AAED,eAAS,WAAW;AAEpB,UAAI,aAAa,WAAW;AAC1B,gBAAQ,IAAI,UAAU;AAEtB,iBAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,gBAAM,WAAW,UAAU,WAAW,aAAa,CAAC,CAAC;AACrD,cAAI,UAAU;AACZ,kBAAM,QAAQ,IAAI,wBAAwB,SAAS,QAAQ;AAC3D,gBAAI,cAAc,aAAa,CAAC,EAAE,YAAY;AAC9C,0BAAc,YAAY,UAAU,IAAI,YAAY,MAAM;AAC1D,kBAAM,UAAU,EAAE,MAAM,aAAa,OAAO,2BAA2B,MAAO;AAC9E,qBAAS,KAAK,OAAO;AAErB,qBAAS,OAAQ;AAAA,UAClB;AAAA,QACF;AAED,cAAM,uBAAuB,kBAAkB,UAAU,gBAAe,CAAE;AAE1E,mBAAW,OAAO,OAAO,sBAAsB,QAAQ;AAAA,MACxD;AAED,gBAAU,KAAK,QAAQ;AAEvB,gBAAU,OAAQ;AAClB,mBAAa,OAAQ;AAAA,IACtB;AAID,aAAS,IAAI,GAAG,IAAI,IAAI,SAAS,SAAS,KAAK;AAC7C,YAAM,SAAS,IAAI,OAAM,EAAG,IAAI,CAAC;AACjC,YAAM,QAAQ,kBAAkB,MAAM;AAEtC,aAAO,KAAK,KAAK;AAEjB,aAAO,OAAQ;AAAA,IAChB;AAID,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,SAAS,KAAK;AAC5C,YAAM,QAAQ,IAAI,MAAK,EAAG,IAAI,CAAC;AAC/B,YAAM,OAAO,kBAAkB,KAAK;AAEpC,YAAM,KAAK,IAAI;AAEf,YAAM,OAAQ;AAAA,IACf;AAID,aAAS,IAAI,GAAG,IAAI,IAAI,aAAa,SAAS,KAAK;AACjD,YAAM,aAAa,IAAI,WAAU,EAAG,IAAI,CAAC;AACzC,YAAM,YAAY,kBAAkB,UAAU;AAE9C,iBAAW,KAAK,SAAS;AAEzB,iBAAW,OAAQ;AAAA,IACpB;AAID,aAAS,IAAI,GAAG,IAAI,IAAI,SAAS,SAAS,KAAK;AAC7C,YAAM,SAAS,IAAI,OAAM,EAAG,IAAI,CAAC;AACjC,YAAM,QAAQ,kBAAkB,MAAM;AAEtC,aAAO,KAAK,KAAK;AAEjB,aAAO,OAAQ;AAAA,IAChB;AAID,UAAM,WAAW,kBAAkB,IAAI,SAAQ,CAAE;AA4BjD,QAAI,OAAQ;AAEZ,WAAO,EAAE,SAAS,WAAW,QAAQ,OAAO,YAAY,QAAQ,SAAU;AAAA,EAC3E;AAED,WAAS,kBAAkB,QAAQ,KAAK;AACtC,UAAM,YAAY,OAAO,SAAU;AACnC,UAAM,cAAc,OAAO,WAAY;AACvC,QAAI,aAAa,UAAU;AAC3B,QAAI,UAAU,YAAY,UAAU,MAAM;AAM1C,YAAQ,YAAU;AAAA,MAChB,KAAK,MAAM,WAAW;AACpB,cAAM,MAAM,cAAc,WAAW,GAAG;AAExC,mBAAW,CAAE;AACb,qBAAa,CAAE;AACf,eAAO,CAAE;AAET,iBAAS,WAAW;AACpB,iBAAS,OAAO;AAChB,iBAAS,QAAQ,CAAE;AAEnB,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,mBAAS,MAAM,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;AAC7B,mBAAS,MAAM,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;AAC7B,mBAAS,MAAM,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;AAAA,QAC9B;AAED,mBAAW,WAAW;AACtB,aAAK,aAAa;AAElB,mBAAW,EAAE,KAAM;AAEnB;AAAA,MAEF,KAAK,MAAM,WAAW;AACpB,cAAM,KAAK,UAAU;AAErB,mBAAW,CAAE;AACb,cAAM,QAAQ,CAAE;AAChB,qBAAa,CAAE;AACf,eAAO,CAAE;AAET,iBAAS,WAAW;AACpB,iBAAS,OAAO;AAChB,iBAAS,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAErC,cAAM,SAAS,YAAY,UAAU,GAAG;AAExC,cAAM,WAAW;AACjB,cAAM,OAAO;AACb,cAAM,QAAQ,CAAC,OAAO,IAAI,KAAO,OAAO,IAAI,KAAO,OAAO,IAAI,GAAK;AAEnE,mBAAW,WAAW;AACtB,mBAAW,QAAQ;AACnB,aAAK,aAAa;AAElB,mBAAW,EAAE,KAAM;AAEnB;AAAA,MAEF,KAAK,MAAM,WAAW;AAAA,MACtB,KAAK,MAAM,WAAW;AACpB,mBAAW,UAAU,cAAe;AAEpC;AAAA,MAEF,KAAK,MAAM,WAAW;AACpB,cAAM,QAAQ,UAAU,MAAO;AAC/B,eAAO,IAAI,MAAM,KAAM;AAEvB,iBAAS,YAAY,GAAG,YAAY,MAAM,OAAO,aAAa;AAC5D,gBAAM,OAAO,MAAM,IAAI,SAAS;AAChC,gBAAM,QAAQ,KAAK,QAAQ,MAAM,SAAS,GAAG;AAE7C,cAAI,OAAO;AACT,iBAAK,OAAO,KAAK;AACjB,kBAAM,OAAQ;AAAA,UACf;AAED,eAAK,OAAQ;AAAA,QACd;AAED,YAAI,KAAK,QAAQ,QAAQ,GAAG;AAC1B,eAAK,QAAS;AACd,qBAAW,KAAK,cAAe;AAC/B,gBAAM,OAAQ;AAAA,QACf;AAED,aAAK,OAAQ;AAEb;AAAA,MAEF,KAAK,MAAM,WAAW;AACpB,eAAO,UAAU,QAAQ,MAAM,SAAS,GAAG;AAE3C,YAAI,MAAM;AACR,qBAAW,KAAK,cAAe;AAC/B,eAAK,OAAQ;AAAA,QACd;AAED;AAAA,MAEF,KAAK,MAAM,WAAW;AACpB,mBAAW,kBAAkB,SAAS;AAEtC;AAAA,MAEF,KAAK,MAAM,WAAW;AACpB,mBAAW,kBAAkB,SAAS;AAEtC;AAAA,MAEF,KAAK,MAAM,WAAW;AACpB,mBAAW,kBAAkB,SAAS;AACtC,iBAAS,QAAQ,kBAAkB,UAAU,KAAK;AAClD,iBAAS,MAAM,QAAQ,UAAU,MAAM,aAAa,IAAI;AAExD;AAAA,MAEF,KAAK,MAAM,WAAW;AAEpB,kBAAU,UAAU,CAAC;AACrB,eAAO,MAAM,KAAK,yBAAyB,SAAS;AACpD,YAAI,MAAM;AACR,qBAAW,KAAK,cAAe;AAC/B,eAAK,OAAQ;AAAA,QACd;AAED;AAAA,MAQF;AACE,gBAAQ,KAAK,oCAAoC,WAAW,YAAY,MAAM;AAC9E;AAAA,IACH;AAED,QAAI,UAAU;AACZ,mBAAa,kBAAkB,WAAW;AAC1C,iBAAW,WAAW,kBAAkB,SAAS;AAEjD,UAAI,YAAY,aAAa,GAAG;AAC9B,mBAAW,WAAW,YAAY,aAAc;AAAA,MACjD;AAED,UAAI,YAAY,kBAAkB,GAAG;AACnC,mBAAW,cAAc,YAAY,eAAgB;AAAA,MACtD;AAED,UAAI,UAAU,kBAAkB,GAAG;AACjC,mBAAW,SAAS,cAAc,UAAU,eAAgB;AAAA,MAC7D;AAED,iBAAW,YAAY,YAAY,UAAU,GAAG;AAEhD,mBAAa,WAAW,YAAY;AACpC,mBAAa,WAAW,UAAU,IAAI,WAAW,MAAM;AAEvD,aAAO,EAAE,UAAU,YAAY,WAAY;AAAA,IACjD,OAAW;AACL,cAAQ,KAAK,oBAAoB,WAAW,YAAY,uCAAuC;AAAA,IAChG;AAAA,EACF;AAED,WAAS,kBAAkB,QAAQ;AACjC,UAAM,SAAS,CAAE;AAEjB,eAAW,YAAY,QAAQ;AAC7B,YAAM,QAAQ,OAAO,QAAQ;AAE7B,UAAI,OAAO,UAAU,YAAY;AAC/B,YAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,MAAM,eAAe,aAAa,GAAG;AACtF,iBAAO,QAAQ,IAAI,EAAE,MAAM,MAAM,YAAY,MAAM,OAAO,MAAM,MAAO;AAAA,QACjF,OAAe;AACL,iBAAO,QAAQ,IAAI;AAAA,QACpB;AAAA,MAIF;AAAA,IACF;AAED,WAAO;AAAA,EACR;AAED,WAAS,cAAc,OAAO,YAAY;AACxC,QAAI,aAAa;AACjB,QAAI,KAAK,CAAE;AACX,UAAM,KAAK,CAAE;AAEb,QAAI,iBAAiB,MAAM,WAAW;AACpC,aAAO,CAAC,MAAM,cAAc,MAAM,UAAU;AAAA,IAC7C;AAED,QAAI,iBAAiB,MAAM,eAAe;AACxC,mBAAa,MAAM;AACnB,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,WAAG,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MACvB;AAED,aAAO;AAAA,IACR;AAED,QAAI,iBAAiB,MAAM,WAAW;AACpC,YAAM,eAAe,MAAM;AAE3B,eAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AACrC,cAAM,UAAU,MAAM,aAAa,CAAC;AACpC,cAAM,eAAe,cAAc,SAAS,UAAU;AACtD,aAAK,GAAG,OAAO,YAAY;AAC3B,gBAAQ,OAAQ;AAAA,MACjB;AAED,aAAO;AAAA,IACR;AAED,QAAI,iBAAiB,MAAM,UAAU;AACnC,mBAAa,KAAK,MAAM,MAAM,eAAe,CAAC;AAC9C,mBAAa,aAAa,IAAI,IAAI;AAAA,IAEnC;AAED,QAAI,iBAAiB,MAAM,cAAc,MAAM,WAAW,GAAG;AAC3D,YAAM,QAAQ,MAAM,eAAgB;AAEpC,eAAS,IAAI,GAAG,IAAI,MAAM,OAAO,KAAK;AACpC,WAAG,KAAK,MAAM,IAAI,CAAC,CAAC;AAAA,MACrB;AAED,YAAM,OAAQ;AAEd,aAAO;AAAA,IACR;AAED,UAAM,SAAS,MAAM;AACrB,UAAM,YAAY,aAAa;AAE/B,aAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,YAAM,IAAI,OAAO,CAAC,IAAK,IAAI,aAAc,OAAO,CAAC,IAAI,OAAO,CAAC;AAE7D,UAAI,MAAM,OAAO,CAAC,KAAK,MAAM,OAAO,CAAC,GAAG;AACtC,WAAG,KAAK,CAAC;AACT;AAAA,MACD;AAED,YAAM,MAAM,MAAM,UAAU,CAAC;AAC7B,YAAM,UAAU,MAAM,UAAU,GAAG,MAAM,EAAE,EAAE,CAAC,CAAC;AAK/C,YAAM,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;AAC7D,YAAM,MAAM,QAAQ,CAAC,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC;AAEtF,YAAM,cAAc,KAAK,KAAK,KAAK,GAAG;AAEtC,UAAI;AAEJ,UAAI,gBAAgB,GAAG;AACrB,gBAAQ,KAAK,KAAK;AAAA,MAC1B,OAAa;AACL,cAAM,SAAS,IAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,QAAQ,KAAK;AAC5E,gBAAQ,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,CAAC;AAAA,MACnD;AAED,UAAI,QAAQ;AAAK;AAEjB,SAAG,KAAK,CAAC;AAAA,IACV;AAED,SAAK,GAAG,IAAI,CAAC,MAAM,MAAM,QAAQ,CAAC,CAAC;AACnC,WAAO;AAAA,EACR;AACH;"}