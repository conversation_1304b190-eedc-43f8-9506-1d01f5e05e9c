import"../shell/shell.js";import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as o from"../../core/root/root.js";import*as n from"../../ui/legacy/legacy.js";import*as i from"../../core/sdk/sdk.js";import*as a from"../../models/issues_manager/issues_manager.js";import*as r from"../../models/extensions/extensions.js";import*as s from"../../models/workspace/workspace.js";import*as l from"../../panels/timeline/utils/utils.js";import*as c from"../../panels/network/forward/forward.js";import*as d from"../../core/host/host.js";import*as g from"../../core/rn_experiments/rn_experiments.js";import*as m from"../main/main.js";const u={toggleDeviceToolbar:"Toggle device toolbar",captureScreenshot:"Capture screenshot",captureFullSizeScreenshot:"Capture full size screenshot",captureNodeScreenshot:"Capture node screenshot",showMediaQueries:"Show media queries",device:"device",hideMediaQueries:"Hide media queries",showRulers:"Show rulers in the Device Mode toolbar",hideRulers:"Hide rulers in the Device Mode toolbar",showDeviceFrame:"Show device frame",hideDeviceFrame:"Hide device frame"},w=t.i18n.registerUIStrings("panels/emulation/emulation-meta.ts",u),p=t.i18n.getLazilyComputedLocalizedString.bind(void 0,w);let v;async function h(){return v||(v=await import("../../panels/emulation/emulation.js")),v}n.ActionRegistration.registerActionExtension({category:"MOBILE",actionId:"emulation.toggle-device-mode",toggleable:!0,loadActionDelegate:async()=>new((await h()).DeviceModeWrapper.ActionDelegate),condition:o.Runtime.conditions.canDock,title:p(u.toggleDeviceToolbar),iconClass:"devices",bindings:[{platform:"windows,linux",shortcut:"Shift+Ctrl+M"},{platform:"mac",shortcut:"Shift+Meta+M"}]}),n.ActionRegistration.registerActionExtension({actionId:"emulation.capture-screenshot",category:"SCREENSHOT",loadActionDelegate:async()=>new((await h()).DeviceModeWrapper.ActionDelegate),condition:o.Runtime.conditions.canDock,title:p(u.captureScreenshot)}),n.ActionRegistration.registerActionExtension({actionId:"emulation.capture-full-height-screenshot",category:"SCREENSHOT",loadActionDelegate:async()=>new((await h()).DeviceModeWrapper.ActionDelegate),condition:o.Runtime.conditions.canDock,title:p(u.captureFullSizeScreenshot)}),n.ActionRegistration.registerActionExtension({actionId:"emulation.capture-node-screenshot",category:"SCREENSHOT",loadActionDelegate:async()=>new((await h()).DeviceModeWrapper.ActionDelegate),condition:o.Runtime.conditions.canDock,title:p(u.captureNodeScreenshot)}),e.Settings.registerSettingExtension({category:"MOBILE",settingName:"show-media-query-inspector",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:p(u.showMediaQueries)},{value:!1,title:p(u.hideMediaQueries)}],tags:[p(u.device)]}),e.Settings.registerSettingExtension({category:"MOBILE",settingName:"emulation.show-rulers",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:p(u.showRulers)},{value:!1,title:p(u.hideRulers)}],tags:[p(u.device)]}),e.Settings.registerSettingExtension({category:"MOBILE",settingName:"emulation.show-device-outline",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:p(u.showDeviceFrame)},{value:!1,title:p(u.hideDeviceFrame)}],tags:[p(u.device)]}),n.Toolbar.registerToolbarItem({actionId:"emulation.toggle-device-mode",condition:o.Runtime.conditions.canDock,location:"main-toolbar-left",order:1,showLabel:void 0,loadItem:void 0,separator:void 0}),e.AppProvider.registerAppProvider({loadAppProvider:async()=>(await h()).AdvancedApp.AdvancedAppProvider.instance(),condition:o.Runtime.conditions.canDock,order:0}),n.ContextMenu.registerItem({location:"deviceModeMenu/save",order:12,actionId:"emulation.capture-screenshot"}),n.ContextMenu.registerItem({location:"deviceModeMenu/save",order:13,actionId:"emulation.capture-full-height-screenshot"});const R={sensors:"Sensors",geolocation:"geolocation",timezones:"timezones",locale:"locale",locales:"locales",accelerometer:"accelerometer",deviceOrientation:"device orientation",locations:"Locations",touch:"Touch",devicebased:"Device-based",forceEnabled:"Force enabled",emulateIdleDetectorState:"Emulate Idle Detector state",noIdleEmulation:"No idle emulation",userActiveScreenUnlocked:"User active, screen unlocked",userActiveScreenLocked:"User active, screen locked",userIdleScreenUnlocked:"User idle, screen unlocked",userIdleScreenLocked:"User idle, screen locked",showSensors:"Show Sensors",showLocations:"Show Locations"},y=t.i18n.registerUIStrings("panels/sensors/sensors-meta.ts",R),f=t.i18n.getLazilyComputedLocalizedString.bind(void 0,y);let k;async function b(){return k||(k=await import("../../panels/sensors/sensors.js")),k}n.ViewManager.registerViewExtension({location:"drawer-view",commandPrompt:f(R.showSensors),title:f(R.sensors),id:"sensors",persistence:"closeable",order:100,loadView:async()=>new((await b()).SensorsView.SensorsView),tags:[f(R.geolocation),f(R.timezones),f(R.locale),f(R.locales),f(R.accelerometer),f(R.deviceOrientation)]}),n.ViewManager.registerViewExtension({location:"settings-view",id:"emulation-locations",commandPrompt:f(R.showLocations),title:f(R.locations),order:40,loadView:async()=>new((await b()).LocationsSettingsTab.LocationsSettingsTab),settings:["emulation.locations"],iconName:"location-on"}),e.Settings.registerSettingExtension({storageType:"Synced",settingName:"emulation.locations",settingType:"array",defaultValue:[{title:"Berlin",lat:52.520007,long:13.404954,timezoneId:"Europe/Berlin",locale:"de-DE"},{title:"London",lat:51.507351,long:-.127758,timezoneId:"Europe/London",locale:"en-GB"},{title:"Moscow",lat:55.755826,long:37.6173,timezoneId:"Europe/Moscow",locale:"ru-RU"},{title:"Mountain View",lat:37.386052,long:-122.083851,timezoneId:"America/Los_Angeles",locale:"en-US"},{title:"Mumbai",lat:19.075984,long:72.877656,timezoneId:"Asia/Kolkata",locale:"mr-IN"},{title:"San Francisco",lat:37.774929,long:-122.419416,timezoneId:"America/Los_Angeles",locale:"en-US"},{title:"Shanghai",lat:31.230416,long:121.473701,timezoneId:"Asia/Shanghai",locale:"zh-Hans-CN"},{title:"São Paulo",lat:-23.55052,long:-46.633309,timezoneId:"America/Sao_Paulo",locale:"pt-BR"},{title:"Tokyo",lat:35.689487,long:139.691706,timezoneId:"Asia/Tokyo",locale:"ja-JP"}]}),e.Settings.registerSettingExtension({title:f(R.touch),reloadRequired:!0,settingName:"emulation.touch",settingType:"enum",defaultValue:"none",options:[{value:"none",title:f(R.devicebased),text:f(R.devicebased)},{value:"force",title:f(R.forceEnabled),text:f(R.forceEnabled)}]}),e.Settings.registerSettingExtension({title:f(R.emulateIdleDetectorState),settingName:"emulation.idle-detection",settingType:"enum",defaultValue:"none",options:[{value:"none",title:f(R.noIdleEmulation),text:f(R.noIdleEmulation)},{value:'{"isUserActive":true,"isScreenUnlocked":true}',title:f(R.userActiveScreenUnlocked),text:f(R.userActiveScreenUnlocked)},{value:'{"isUserActive":true,"isScreenUnlocked":false}',title:f(R.userActiveScreenLocked),text:f(R.userActiveScreenLocked)},{value:'{"isUserActive":false,"isScreenUnlocked":true}',title:f(R.userIdleScreenUnlocked),text:f(R.userIdleScreenUnlocked)},{value:'{"isUserActive":false,"isScreenUnlocked":false}',title:f(R.userIdleScreenLocked),text:f(R.userIdleScreenLocked)}]});const T={developerResources:"Developer resources",showDeveloperResources:"Show Developer resources"},S=t.i18n.registerUIStrings("panels/developer_resources/developer_resources-meta.ts",T),A=t.i18n.getLazilyComputedLocalizedString.bind(void 0,S);let N;async function E(){return N||(N=await import("../../panels/developer_resources/developer_resources.js")),N}n.ViewManager.registerViewExtension({location:"drawer-view",id:"developer-resources",title:A(T.developerResources),commandPrompt:A(T.showDeveloperResources),order:100,persistence:"closeable",loadView:async()=>new((await E()).DeveloperResourcesView.DeveloperResourcesView)}),e.Revealer.registerRevealer({contextTypes:()=>[i.PageResourceLoader.ResourceKey],destination:e.Revealer.RevealerDestination.DEVELOPER_RESOURCES_PANEL,loadRevealer:async()=>new((await E()).DeveloperResourcesView.DeveloperResourcesRevealer)});const P={rendering:"Rendering",showRendering:"Show Rendering",paint:"paint",layout:"layout",fps:"fps",cssMediaType:"CSS media type",cssMediaFeature:"CSS media feature",visionDeficiency:"vision deficiency",colorVisionDeficiency:"color vision deficiency",reloadPage:"Reload page",hardReloadPage:"Hard reload page",forceAdBlocking:"Force ad blocking on this site",blockAds:"Block ads on this site",showAds:"Show ads on this site, if allowed",autoOpenDevTools:"Auto-open DevTools for popups",doNotAutoOpen:"Do not auto-open DevTools for popups",disablePaused:"Disable paused state overlay",toggleCssPrefersColorSchemeMedia:"Toggle CSS media feature prefers-color-scheme"},x=t.i18n.registerUIStrings("entrypoints/inspector_main/inspector_main-meta.ts",P),I=t.i18n.getLazilyComputedLocalizedString.bind(void 0,x);let M;async function D(){return M||(M=await import("../inspector_main/inspector_main.js")),M}n.ViewManager.registerViewExtension({location:"drawer-view",id:"rendering",title:I(P.rendering),commandPrompt:I(P.showRendering),persistence:"closeable",order:50,loadView:async()=>new((await D()).RenderingOptions.RenderingOptionsView),tags:[I(P.paint),I(P.layout),I(P.fps),I(P.cssMediaType),I(P.cssMediaFeature),I(P.visionDeficiency),I(P.colorVisionDeficiency)]}),n.ActionRegistration.registerActionExtension({category:"NAVIGATION",actionId:"inspector-main.reload",loadActionDelegate:async()=>new((await D()).InspectorMain.ReloadActionDelegate),iconClass:"refresh",title:I(P.reloadPage),bindings:[{platform:"windows,linux",shortcut:"Ctrl+R"},{platform:"windows,linux",shortcut:"F5"},{platform:"mac",shortcut:"Meta+R"}]}),n.ActionRegistration.registerActionExtension({category:"NAVIGATION",actionId:"inspector-main.hard-reload",loadActionDelegate:async()=>new((await D()).InspectorMain.ReloadActionDelegate),title:I(P.hardReloadPage),bindings:[{platform:"windows,linux",shortcut:"Shift+Ctrl+R"},{platform:"windows,linux",shortcut:"Shift+F5"},{platform:"windows,linux",shortcut:"Ctrl+F5"},{platform:"windows,linux",shortcut:"Ctrl+Shift+F5"},{platform:"mac",shortcut:"Shift+Meta+R"}]}),n.ActionRegistration.registerActionExtension({actionId:"rendering.toggle-prefers-color-scheme",category:"RENDERING",title:I(P.toggleCssPrefersColorSchemeMedia),loadActionDelegate:async()=>new((await D()).RenderingOptions.ReloadActionDelegate)}),e.Settings.registerSettingExtension({category:"NETWORK",title:I(P.forceAdBlocking),settingName:"network.ad-blocking-enabled",settingType:"boolean",storageType:"Session",defaultValue:!1,options:[{value:!0,title:I(P.blockAds)},{value:!1,title:I(P.showAds)}]}),e.Settings.registerSettingExtension({category:"GLOBAL",storageType:"Synced",title:I(P.autoOpenDevTools),settingName:"auto-attach-to-created-pages",settingType:"boolean",order:2,defaultValue:!1,options:[{value:!0,title:I(P.autoOpenDevTools)},{value:!1,title:I(P.doNotAutoOpen)}]}),e.Settings.registerSettingExtension({category:"APPEARANCE",storageType:"Synced",title:I(P.disablePaused),settingName:"disable-paused-state-overlay",settingType:"boolean",defaultValue:!1}),n.Toolbar.registerToolbarItem({loadItem:async()=>(await D()).InspectorMain.NodeIndicator.instance(),order:2,location:"main-toolbar-left"}),n.Toolbar.registerToolbarItem({loadItem:async()=>(await D()).OutermostTargetSelector.OutermostTargetSelector.instance(),order:98,location:"main-toolbar-right",experiment:"outermost-target-selector"}),n.Toolbar.registerToolbarItem({loadItem:async()=>(await D()).OutermostTargetSelector.OutermostTargetSelector.instance(),order:98,location:"main-toolbar-right",showLabel:void 0,condition:void 0,separator:void 0,actionId:void 0,experiment:"outermost-target-selector"});const L={issues:"Issues",showIssues:"Show Issues"},V=t.i18n.registerUIStrings("panels/issues/issues-meta.ts",L),C=t.i18n.getLazilyComputedLocalizedString.bind(void 0,V);let O;async function F(){return O||(O=await import("../../panels/issues/issues.js")),O}n.ViewManager.registerViewExtension({location:"drawer-view",id:"issues-pane",title:C(L.issues),commandPrompt:C(L.showIssues),order:100,persistence:"closeable",loadView:async()=>new((await F()).IssuesPane.IssuesPane)}),e.Revealer.registerRevealer({contextTypes:()=>[a.Issue.Issue],destination:e.Revealer.RevealerDestination.ISSUES_VIEW,loadRevealer:async()=>new((await F()).IssueRevealer.IssueRevealer)});const U={throttling:"Throttling",showThrottling:"Show Throttling",goOffline:"Go offline",device:"device",throttlingTag:"throttling",enableSlowGThrottling:"Enable slow `3G` throttling",enableFastGThrottling:"Enable fast `3G` throttling",goOnline:"Go online"},_=t.i18n.registerUIStrings("panels/mobile_throttling/mobile_throttling-meta.ts",U),B=t.i18n.getLazilyComputedLocalizedString.bind(void 0,_);let z;async function q(){return z||(z=await import("../../panels/mobile_throttling/mobile_throttling.js")),z}n.ViewManager.registerViewExtension({location:"settings-view",id:"throttling-conditions",title:B(U.throttling),commandPrompt:B(U.showThrottling),order:35,loadView:async()=>new((await q()).ThrottlingSettingsTab.ThrottlingSettingsTab),settings:["custom-network-conditions"],iconName:"performance"}),n.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-offline",category:"NETWORK",title:B(U.goOffline),loadActionDelegate:async()=>new((await q()).ThrottlingManager.ActionDelegate),tags:[B(U.device),B(U.throttlingTag)]}),n.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-low-end-mobile",category:"NETWORK",title:B(U.enableSlowGThrottling),loadActionDelegate:async()=>new((await q()).ThrottlingManager.ActionDelegate),tags:[B(U.device),B(U.throttlingTag)]}),n.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-mid-tier-mobile",category:"NETWORK",title:B(U.enableFastGThrottling),loadActionDelegate:async()=>new((await q()).ThrottlingManager.ActionDelegate),tags:[B(U.device),B(U.throttlingTag)]}),n.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-online",category:"NETWORK",title:B(U.goOnline),loadActionDelegate:async()=>new((await q()).ThrottlingManager.ActionDelegate),tags:[B(U.device),B(U.throttlingTag)]}),e.Settings.registerSettingExtension({storageType:"Synced",settingName:"custom-network-conditions",settingType:"array",defaultValue:[]});const W={showNetwork:"Show Network",network:"Network",networkExpoUnstable:"Network (Expo, unstable)",showNetworkRequestBlocking:"Show Network request blocking",networkRequestBlocking:"Network request blocking",showNetworkConditions:"Show Network conditions",networkConditions:"Network conditions",diskCache:"disk cache",networkThrottling:"network throttling",showSearch:"Show Search",search:"Search",recordNetworkLog:"Record network log",stopRecordingNetworkLog:"Stop recording network log",hideRequestDetails:"Hide request details",colorcodeResourceTypes:"Color-code resource types",colorCode:"color code",resourceType:"resource type",colorCodeByResourceType:"Color code by resource type",useDefaultColors:"Use default colors",groupNetworkLogByFrame:"Group network log by frame",netWork:"network",frame:"frame",group:"group",groupNetworkLogItemsByFrame:"Group network log items by frame",dontGroupNetworkLogItemsByFrame:"Don't group network log items by frame",clear:"Clear network log",addNetworkRequestBlockingPattern:"Add network request blocking pattern",removeAllNetworkRequestBlockingPatterns:"Remove all network request blocking patterns"},j=t.i18n.registerUIStrings("panels/network/network-meta.ts",W),K=t.i18n.getLazilyComputedLocalizedString.bind(void 0,j),G=t.i18n.getLocalizedString.bind(void 0,j);let H;async function Y(){return H||(H=await import("../../panels/network/network.js")),H}function Q(e){return void 0===H?[]:e(H)}n.ViewManager.registerViewExtension({location:"panel",id:"network",commandPrompt:K(W.showNetwork),title:()=>o.Runtime.experiments.isEnabled(o.Runtime.RNExperimentName.ENABLE_NETWORK_PANEL)?G(W.network):G(W.networkExpoUnstable),order:40,isPreviewFeature:!0,condition:o.Runtime.conditions.reactNativeUnstableNetworkPanel,loadView:async()=>(await Y()).NetworkPanel.NetworkPanel.instance()}),n.ViewManager.registerViewExtension({location:"drawer-view",id:"network.blocked-urls",commandPrompt:K(W.showNetworkRequestBlocking),title:K(W.networkRequestBlocking),persistence:"closeable",order:60,loadView:async()=>new((await Y()).BlockedURLsPane.BlockedURLsPane)}),n.ViewManager.registerViewExtension({location:"drawer-view",id:"network.config",commandPrompt:K(W.showNetworkConditions),title:K(W.networkConditions),persistence:"closeable",order:40,tags:[K(W.diskCache),K(W.networkThrottling),t.i18n.lockedLazyString("useragent"),t.i18n.lockedLazyString("user agent"),t.i18n.lockedLazyString("user-agent")],loadView:async()=>(await Y()).NetworkConfigView.NetworkConfigView.instance()}),n.ViewManager.registerViewExtension({location:"network-sidebar",id:"network.search-network-tab",commandPrompt:K(W.showSearch),title:K(W.search),persistence:"permanent",loadView:async()=>(await Y()).NetworkPanel.SearchNetworkView.instance()}),n.ActionRegistration.registerActionExtension({actionId:"network.toggle-recording",category:"NETWORK",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>Q((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await Y()).NetworkPanel.ActionDelegate),options:[{value:!0,title:K(W.recordNetworkLog)},{value:!1,title:K(W.stopRecordingNetworkLog)}],bindings:[{shortcut:"Ctrl+E",platform:"windows,linux"},{shortcut:"Meta+E",platform:"mac"}]}),n.ActionRegistration.registerActionExtension({actionId:"network.clear",category:"NETWORK",title:K(W.clear),iconClass:"clear",loadActionDelegate:async()=>new((await Y()).NetworkPanel.ActionDelegate),contextTypes:()=>Q((e=>[e.NetworkPanel.NetworkPanel])),bindings:[{shortcut:"Ctrl+L"},{shortcut:"Meta+K",platform:"mac"}]}),n.ActionRegistration.registerActionExtension({actionId:"network.hide-request-details",category:"NETWORK",title:K(W.hideRequestDetails),contextTypes:()=>Q((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await Y()).NetworkPanel.ActionDelegate),bindings:[{shortcut:"Esc"}]}),n.ActionRegistration.registerActionExtension({actionId:"network.search",category:"NETWORK",title:K(W.search),contextTypes:()=>Q((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await Y()).NetworkPanel.ActionDelegate),bindings:[{platform:"mac",shortcut:"Meta+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+F",keybindSets:["devToolsDefault","vsCode"]}]}),n.ActionRegistration.registerActionExtension({actionId:"network.add-network-request-blocking-pattern",category:"NETWORK",title:K(W.addNetworkRequestBlockingPattern),iconClass:"plus",contextTypes:()=>Q((e=>[e.BlockedURLsPane.BlockedURLsPane])),loadActionDelegate:async()=>new((await Y()).BlockedURLsPane.ActionDelegate)}),n.ActionRegistration.registerActionExtension({actionId:"network.remove-all-network-request-blocking-patterns",category:"NETWORK",title:K(W.removeAllNetworkRequestBlockingPatterns),iconClass:"clear",contextTypes:()=>Q((e=>[e.BlockedURLsPane.BlockedURLsPane])),loadActionDelegate:async()=>new((await Y()).BlockedURLsPane.ActionDelegate)}),e.Settings.registerSettingExtension({category:"NETWORK",storageType:"Synced",title:K(W.colorcodeResourceTypes),settingName:"network-color-code-resource-types",settingType:"boolean",defaultValue:!1,tags:[K(W.colorCode),K(W.resourceType)],options:[{value:!0,title:K(W.colorCodeByResourceType)},{value:!1,title:K(W.useDefaultColors)}]}),e.Settings.registerSettingExtension({category:"NETWORK",storageType:"Synced",title:K(W.groupNetworkLogByFrame),settingName:"network.group-by-frame",settingType:"boolean",defaultValue:!1,tags:[K(W.netWork),K(W.frame),K(W.group)],options:[{value:!0,title:K(W.groupNetworkLogItemsByFrame)},{value:!1,title:K(W.dontGroupNetworkLogItemsByFrame)}]}),n.ViewManager.registerLocationResolver({name:"network-sidebar",category:"NETWORK",loadResolver:async()=>(await Y()).NetworkPanel.NetworkPanel.instance()}),n.ContextMenu.registerProvider({contextTypes:()=>[i.NetworkRequest.NetworkRequest,i.Resource.Resource,s.UISourceCode.UISourceCode,l.NetworkRequest.TimelineNetworkRequest],loadProvider:async()=>(await Y()).NetworkPanel.NetworkPanel.instance(),experiment:void 0}),e.Revealer.registerRevealer({contextTypes:()=>[i.NetworkRequest.NetworkRequest],destination:e.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await Y()).NetworkPanel.RequestRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[c.UIRequestLocation.UIRequestLocation],destination:void 0,loadRevealer:async()=>new((await Y()).NetworkPanel.RequestLocationRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[c.NetworkRequestId.NetworkRequestId],destination:e.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await Y()).NetworkPanel.RequestIdRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[c.UIFilter.UIRequestFilter,r.ExtensionServer.RevealableNetworkRequestFilter],destination:e.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await Y()).NetworkPanel.NetworkLogWithFilterRevealer)});const J={title:"Components ⚛",command:"Show React DevTools Components panel"},$=t.i18n.registerUIStrings("panels/react_devtools/react_devtools_components-meta.ts",J),X=t.i18n.getLazilyComputedLocalizedString.bind(void 0,$);let Z;n.ViewManager.registerViewExtension({location:"panel",id:"react-devtools-components",title:X(J.title),commandPrompt:X(J.command),persistence:"permanent",order:1e3,loadView:async()=>new((await async function(){return Z||(Z=await import("../../panels/react_devtools/react_devtools.js")),Z}()).ReactDevToolsComponentsView.ReactDevToolsComponentsViewImpl)});const ee={title:"Profiler ⚛",command:"Show React DevTools Profiler panel"},te=t.i18n.registerUIStrings("panels/react_devtools/react_devtools_profiler-meta.ts",ee),oe=t.i18n.getLazilyComputedLocalizedString.bind(void 0,te);let ne;n.ViewManager.registerViewExtension({location:"panel",id:"react-devtools-profiler",title:oe(ee.title),commandPrompt:oe(ee.command),persistence:"permanent",order:1e3,loadView:async()=>new((await async function(){return ne||(ne=await import("../../panels/react_devtools/react_devtools.js")),ne}()).ReactDevToolsProfilerView.ReactDevToolsProfilerViewImpl)});const ie={rnWelcome:"Welcome",showRnWelcome:"Show React Native Welcome panel",debuggerBrandName:"React Native DevTools"},ae=t.i18n.registerUIStrings("panels/rn_welcome/rn_welcome-meta.ts",ie),re=t.i18n.getLazilyComputedLocalizedString.bind(void 0,ae);let se;n.ViewManager.registerViewExtension({location:"panel",id:"rn-welcome",title:re(ie.rnWelcome),commandPrompt:re(ie.showRnWelcome),order:-10,persistence:"permanent",loadView:async()=>(await async function(){return se||(se=await import("../../panels/rn_welcome/rn_welcome.js")),se}()).RNWelcome.RNWelcomeImpl.instance({debuggerBrandName:re(ie.debuggerBrandName),showBetaLabel:!1,showDocs:!0}),experiment:"react-native-specific-ui"});const le={performance:"Performance",showPerformance:"Show Performance",record:"Record",stop:"Stop",recordAndReload:"Record and reload",saveProfile:"Save profile…",loadProfile:"Load profile…",previousFrame:"Previous frame",nextFrame:"Next frame",showRecentTimelineSessions:"Show recent timeline sessions",previousRecording:"Previous recording",nextRecording:"Next recording",hideChromeFrameInLayersView:"Hide `chrome` frame in Layers view"},ce=t.i18n.registerUIStrings("panels/timeline/timeline-meta.ts",le),de=t.i18n.getLazilyComputedLocalizedString.bind(void 0,ce);let ge;async function me(){return ge||(ge=await import("../../panels/timeline/timeline.js")),ge}function ue(e){return void 0===ge?[]:e(ge)}n.ViewManager.registerViewExtension({location:"panel",id:"timeline",title:de(le.performance),commandPrompt:de(le.showPerformance),order:50,experiment:!0===globalThis.FB_ONLY__enablePerformance?void 0:"enable-performance-panel",loadView:async()=>(await me()).TimelinePanel.TimelinePanel.instance()}),n.ActionRegistration.registerActionExtension({actionId:"timeline.toggle-recording",category:"PERFORMANCE",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>ue((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await me()).TimelinePanel.ActionDelegate),options:[{value:!0,title:de(le.record)},{value:!1,title:de(le.stop)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),n.ActionRegistration.registerActionExtension({actionId:"timeline.record-reload",iconClass:"refresh",contextTypes:()=>ue((e=>[e.TimelinePanel.TimelinePanel])),category:"PERFORMANCE",title:de(le.recordAndReload),loadActionDelegate:async()=>new((await me()).TimelinePanel.ActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+E"},{platform:"mac",shortcut:"Meta+Shift+E"}],experiment:"!react-native-specific-ui"}),n.ActionRegistration.registerActionExtension({category:"PERFORMANCE",actionId:"timeline.save-to-file",contextTypes:()=>ue((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await me()).TimelinePanel.ActionDelegate),title:de(le.saveProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+S"},{platform:"mac",shortcut:"Meta+S"}]}),n.ActionRegistration.registerActionExtension({category:"PERFORMANCE",actionId:"timeline.load-from-file",contextTypes:()=>ue((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await me()).TimelinePanel.ActionDelegate),title:de(le.loadProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+O"},{platform:"mac",shortcut:"Meta+O"}]}),n.ActionRegistration.registerActionExtension({actionId:"timeline.jump-to-previous-frame",category:"PERFORMANCE",title:de(le.previousFrame),contextTypes:()=>ue((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await me()).TimelinePanel.ActionDelegate),bindings:[{shortcut:"["}]}),n.ActionRegistration.registerActionExtension({actionId:"timeline.jump-to-next-frame",category:"PERFORMANCE",title:de(le.nextFrame),contextTypes:()=>ue((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await me()).TimelinePanel.ActionDelegate),bindings:[{shortcut:"]"}]}),n.ActionRegistration.registerActionExtension({actionId:"timeline.show-history",loadActionDelegate:async()=>new((await me()).TimelinePanel.ActionDelegate),category:"PERFORMANCE",title:de(le.showRecentTimelineSessions),contextTypes:()=>ue((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+H"},{platform:"mac",shortcut:"Meta+Y"}]}),n.ActionRegistration.registerActionExtension({actionId:"timeline.previous-recording",category:"PERFORMANCE",loadActionDelegate:async()=>new((await me()).TimelinePanel.ActionDelegate),title:de(le.previousRecording),contextTypes:()=>ue((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Alt+Left"},{platform:"mac",shortcut:"Meta+Left"}]}),n.ActionRegistration.registerActionExtension({actionId:"timeline.next-recording",category:"PERFORMANCE",loadActionDelegate:async()=>new((await me()).TimelinePanel.ActionDelegate),title:de(le.nextRecording),contextTypes:()=>ue((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Alt+Right"},{platform:"mac",shortcut:"Meta+Right"}]}),e.Settings.registerSettingExtension({category:"PERFORMANCE",storageType:"Synced",title:de(le.hideChromeFrameInLayersView),settingName:"frame-viewer-hide-chrome-window",settingType:"boolean",defaultValue:!1}),e.Linkifier.registerLinkifier({contextTypes:()=>ue((e=>[e.CLSLinkifier.CLSRect])),loadLinkifier:async()=>(await me()).CLSLinkifier.Linkifier.instance()}),n.ContextMenu.registerItem({location:"timelineMenu/open",actionId:"timeline.load-from-file",order:10}),n.ContextMenu.registerItem({location:"timelineMenu/open",actionId:"timeline.save-to-file",order:15});class we{static#e;#t;#o;#n;constructor(){}static instance(){return this.#e||(this.#e=new we),this.#e}setAppInfo(e,t){this.#t=e,this.#o=t,this.#i()}setSuffix(e){this.#n=e,this.#i()}#i(){const e=[];this.#t&&e.push(this.#t),this.#o&&e.push(`(${this.#o})`),this.#n&&e.push(this.#n),e.push("- React Native DevTools"),document.title=e.join(" ")}}const pe={connectionStatusDisconnectedTooltip:"Debugging connection was closed",connectionStatusDisconnectedLabel:"Reconnect DevTools"},ve=t.i18n.registerUIStrings("entrypoints/rn_fusebox/ConnectionStatusToolbarItem.ts",pe),he=t.i18n.getLazilyComputedLocalizedString.bind(void 0,ve);let Re;class ye extends i.TargetManager.Observer{#a=new n.Toolbar.ToolbarButton("");constructor(){super(),this.#a.setVisible(!1),this.#a.element.classList.add("fusebox-connection-status"),this.#a.addEventListener("Click",this.#r.bind(this)),i.TargetManager.TargetManager.instance().observeTargets(this,{scoped:!0})}static instance(){return Re||(Re=new ye),Re}targetAdded(e){this.#s(e)}targetRemoved(e){this.#s(e)}#s(e){const t=i.TargetManager.TargetManager.instance().rootTarget();this.#a.setTitle(he(pe.connectionStatusDisconnectedTooltip)()),this.#a.setText(he(pe.connectionStatusDisconnectedLabel)()),this.#a.setVisible(!t),t||this.#l(e)}#l(t){e.Settings.Settings.instance().moduleSetting("preserve-console-log").get()||t.model(i.ConsoleModel.ConsoleModel)?.addMessage(new i.ConsoleModel.ConsoleMessage(t.model(i.RuntimeModel.RuntimeModel),"recommendation","info","[React Native] Console messages are currently cleared upon DevTools disconnection. You can preserve logs in settings: ",{type:i.ConsoleModel.FrontendMessageType.System,context:"fusebox_preserve_log_rec"}))}#r(){window.location.reload()}item(){return this.#a}}const fe={reloadRequiredForPerformancePanelMessage:"[Profiling build first run] One or more settings have changed. Please reload to access the Performance panel.",reloadRequiredForNetworkPanelMessage:"Network panel is now available for dogfooding. Please reload to access it."},ke=t.i18n.registerUIStrings("entrypoints/rn_fusebox/FuseboxExperimentsObserver.ts",fe),be=t.i18n.getLocalizedString.bind(void 0,ke);d.rnPerfMetrics.registerPerfMetricsGlobalPostMessageHandler(),d.rnPerfMetrics.registerGlobalErrorReporting(),d.rnPerfMetrics.setLaunchId(o.Runtime.Runtime.queryParam("launchId")),d.rnPerfMetrics.setAppId(o.Runtime.Runtime.queryParam("appId")),d.rnPerfMetrics.setTelemetryInfo(JSON.parse(o.Runtime.Runtime.queryParam("telemetryInfo")||"{}")),d.rnPerfMetrics.entryPointLoadingStarted("rn_fusebox");const Te={networkTitle:"React Native",showReactNative:"Show React Native",sendFeedback:"[FB-only] Send feedback"},Se=t.i18n.registerUIStrings("entrypoints/rn_fusebox/rn_fusebox.ts",Te),Ae=t.i18n.getLazilyComputedLocalizedString.bind(void 0,Se);let Ne;if(n.ViewManager.maybeRemoveViewExtension("network.blocked-urls"),n.ViewManager.maybeRemoveViewExtension("network.config"),n.ViewManager.maybeRemoveViewExtension("coverage"),n.ViewManager.maybeRemoveViewExtension("linear-memory-inspector"),n.ViewManager.maybeRemoveViewExtension("rendering"),n.ViewManager.maybeRemoveViewExtension("issues-pane"),n.ViewManager.maybeRemoveViewExtension("sensors"),n.ViewManager.maybeRemoveViewExtension("devices"),n.ViewManager.maybeRemoveViewExtension("emulation-locations"),n.ViewManager.maybeRemoveViewExtension("throttling-conditions"),g.RNExperimentsImpl.setIsReactNativeEntryPoint(!0),g.RNExperimentsImpl.Instance.enableExperimentsByDefault(["js-heap-profiler-enable","react-native-specific-ui"]),document.addEventListener("visibilitychange",(()=>{d.rnPerfMetrics.browserVisibilityChanged(document.visibilityState)})),i.SDKModel.SDKModel.register(i.ReactNativeApplicationModel.ReactNativeApplicationModel,{capabilities:0,autostart:!0,early:!0}),n.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-network",title:Ae(Te.networkTitle),commandPrompt:Ae(Te.showReactNative),order:2,persistence:"permanent",loadView:async()=>(await async function(){return Ne||(Ne=await import("../../panels/sources/sources.js")),Ne}()).SourcesNavigator.NetworkNavigatorView.instance()}),self.runtime=o.Runtime.Runtime.instance({forceNew:!0}),new m.MainImpl.MainImpl,globalThis.FB_ONLY__reactNativeFeedbackLink){const e=globalThis.FB_ONLY__reactNativeFeedbackLink,t="react-native-send-feedback",o={handleAction:(o,n)=>n===t&&(d.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(e),!0)};n.ActionRegistration.registerActionExtension({category:"GLOBAL",actionId:t,title:Ae(Te.sendFeedback),loadActionDelegate:async()=>o,iconClass:"bug"}),n.Toolbar.registerToolbarItem({location:"main-toolbar-right",actionId:t,showLabel:!0})}n.Toolbar.registerToolbarItem({location:"main-toolbar-right",loadItem:async()=>ye.instance()}),new class{constructor(e){e.observeModels(i.ReactNativeApplicationModel.ReactNativeApplicationModel,this)}modelAdded(e){e.ensureEnabled(),e.addEventListener("MetadataUpdated",this.#c,this)}modelRemoved(e){e.removeEventListener("MetadataUpdated",this.#c,this)}#c(e){const{appDisplayName:t,deviceName:o}=e.data;we.instance().setAppInfo(t,o)}}(i.TargetManager.TargetManager.instance()),new class{constructor(e){e.observeModels(i.ReactNativeApplicationModel.ReactNativeApplicationModel,this)}modelAdded(e){e.ensureEnabled(),e.addEventListener("MetadataUpdated",this.#c,this)}modelRemoved(e){e.removeEventListener("MetadataUpdated",this.#c,this)}#c(e){const{unstable_isProfilingBuild:t,unstable_networkInspectionEnabled:o}=e.data;t&&(we.instance().setSuffix("[PROFILING]"),this.#d(),this.#g()),o&&this.#m()}#d(){n.InspectorView.InspectorView.instance().closeDrawer();const e=n.ViewManager.ViewManager.instance(),t=e.resolveLocation("panel"),o=e.resolveLocation("drawer-view");Promise.all([t,o]).then((([e,t])=>{n.ViewManager.getRegisteredViewExtensions().forEach((o=>{if("drawer-view"===o.location())t?.removeView(o);else switch(o.viewId()){case"console":case"heap-profiler":case"live-heap-profile":case"sources":case"network":case"react-devtools-components":case"react-devtools-profiler":e?.removeView(o)}}))}))}#g(){if(!o.Runtime.experiments.isEnabled("enable-performance-panel")){o.Runtime.experiments.setEnabled("enable-performance-panel",!0);const e=n.InspectorView?.InspectorView?.instance();e&&e.displayReloadRequiredWarning(be(fe.reloadRequiredForPerformancePanelMessage))}}#m(){o.Runtime.experiments.isEnabled("enable-network-panel")||(o.Runtime.experiments.setEnabled("enable-network-panel",!0),n.InspectorView?.InspectorView?.instance()?.displayReloadRequiredWarning(be(fe.reloadRequiredForNetworkPanelMessage)))}}(i.TargetManager.TargetManager.instance()),d.rnPerfMetrics.entryPointLoadingFinished("rn_fusebox");
