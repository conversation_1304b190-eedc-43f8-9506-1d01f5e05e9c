import { Group, OrthographicCamera, PerspectiveCamera } from 'three';
import { HandlesContext } from './context.js';
import { HandlesProperties, ScaleHandles, TransformHandlesSpace } from './index.js';
import { HandleOptions } from '../store.js';
import { RotateHandles } from './rotate/index.js';
import { TranslateHandles } from './translate/index.js';
export type TransformHandlesMode = 'rotate' | 'scale' | 'translate';
export declare class TransformHandles extends Group {
    private readonly xAxisHighlight;
    private readonly yAxisHighlight;
    private readonly zAxisHighlight;
    handles?: TranslateHandles | RotateHandles | ScaleHandles;
    readonly context: HandlesContext;
    constructor(getOptions?: () => HandleOptions<unknown>);
    set space(space: TransformHandlesSpace | undefined);
    get space(): TransformHandlesSpace | undefined;
    update(time: number, camera: PerspectiveCamera | OrthographicCamera): void;
    bind(mode: TransformHandlesMode, options?: HandlesProperties): () => void;
}
