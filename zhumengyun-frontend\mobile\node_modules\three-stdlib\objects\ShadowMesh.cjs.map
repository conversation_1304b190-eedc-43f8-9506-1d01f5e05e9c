{"version": 3, "file": "ShadowMesh.cjs", "sources": ["../../src/objects/ShadowMesh.js"], "sourcesContent": ["import { Matrix4, <PERSON><PERSON>, MeshBasicMaterial, EqualStencilFunc, IncrementStencilOp } from 'three'\n\n/**\n * A shadow Mesh that follows a shadow-casting Mesh in the scene, but is confined to a single plane.\n */\n\nconst _shadowMatrix = /* @__PURE__ */ new Matrix4()\n\nclass ShadowMesh extends Mesh {\n  constructor(mesh) {\n    const shadowMaterial = new MeshBasicMaterial({\n      color: 0x000000,\n      transparent: true,\n      opacity: 0.6,\n      depthWrite: false,\n      stencilWrite: true,\n      stencilFunc: EqualStencilFunc,\n      stencilRef: 0,\n      stencilZPass: IncrementStencilOp,\n    })\n\n    super(mesh.geometry, shadowMaterial)\n\n    this.isShadowMesh = true\n\n    this.meshMatrix = mesh.matrixWorld\n\n    this.frustumCulled = false\n    this.matrixAutoUpdate = false\n  }\n\n  update(plane, lightPosition4D) {\n    // based on https://www.opengl.org/archives/resources/features/StencilTalk/tsld021.htm\n\n    const dot =\n      plane.normal.x * lightPosition4D.x +\n      plane.normal.y * lightPosition4D.y +\n      plane.normal.z * lightPosition4D.z +\n      -plane.constant * lightPosition4D.w\n\n    const sme = _shadowMatrix.elements\n\n    sme[0] = dot - lightPosition4D.x * plane.normal.x\n    sme[4] = -lightPosition4D.x * plane.normal.y\n    sme[8] = -lightPosition4D.x * plane.normal.z\n    sme[12] = -lightPosition4D.x * -plane.constant\n\n    sme[1] = -lightPosition4D.y * plane.normal.x\n    sme[5] = dot - lightPosition4D.y * plane.normal.y\n    sme[9] = -lightPosition4D.y * plane.normal.z\n    sme[13] = -lightPosition4D.y * -plane.constant\n\n    sme[2] = -lightPosition4D.z * plane.normal.x\n    sme[6] = -lightPosition4D.z * plane.normal.y\n    sme[10] = dot - lightPosition4D.z * plane.normal.z\n    sme[14] = -lightPosition4D.z * -plane.constant\n\n    sme[3] = -lightPosition4D.w * plane.normal.x\n    sme[7] = -lightPosition4D.w * plane.normal.y\n    sme[11] = -lightPosition4D.w * plane.normal.z\n    sme[15] = dot - lightPosition4D.w * -plane.constant\n\n    this.matrix.multiplyMatrices(_shadowMatrix, this.meshMatrix)\n  }\n}\n\nexport { ShadowMesh }\n"], "names": ["Matrix4", "<PERSON><PERSON>", "MeshBasicMaterial", "EqualStencilFunc", "IncrementStencilOp"], "mappings": ";;;AAMA,MAAM,gBAAgC,oBAAIA,MAAAA,QAAS;AAEnD,MAAM,mBAAmBC,MAAAA,KAAK;AAAA,EAC5B,YAAY,MAAM;AAChB,UAAM,iBAAiB,IAAIC,wBAAkB;AAAA,MAC3C,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,aAAaC,MAAgB;AAAA,MAC7B,YAAY;AAAA,MACZ,cAAcC,MAAkB;AAAA,IACtC,CAAK;AAED,UAAM,KAAK,UAAU,cAAc;AAEnC,SAAK,eAAe;AAEpB,SAAK,aAAa,KAAK;AAEvB,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AAAA,EACzB;AAAA,EAED,OAAO,OAAO,iBAAiB;AAG7B,UAAM,MACJ,MAAM,OAAO,IAAI,gBAAgB,IACjC,MAAM,OAAO,IAAI,gBAAgB,IACjC,MAAM,OAAO,IAAI,gBAAgB,IACjC,CAAC,MAAM,WAAW,gBAAgB;AAEpC,UAAM,MAAM,cAAc;AAE1B,QAAI,CAAC,IAAI,MAAM,gBAAgB,IAAI,MAAM,OAAO;AAChD,QAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,MAAM,OAAO;AAC3C,QAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,MAAM,OAAO;AAC3C,QAAI,EAAE,IAAI,CAAC,gBAAgB,IAAI,CAAC,MAAM;AAEtC,QAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,MAAM,OAAO;AAC3C,QAAI,CAAC,IAAI,MAAM,gBAAgB,IAAI,MAAM,OAAO;AAChD,QAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,MAAM,OAAO;AAC3C,QAAI,EAAE,IAAI,CAAC,gBAAgB,IAAI,CAAC,MAAM;AAEtC,QAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,MAAM,OAAO;AAC3C,QAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,MAAM,OAAO;AAC3C,QAAI,EAAE,IAAI,MAAM,gBAAgB,IAAI,MAAM,OAAO;AACjD,QAAI,EAAE,IAAI,CAAC,gBAAgB,IAAI,CAAC,MAAM;AAEtC,QAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,MAAM,OAAO;AAC3C,QAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,MAAM,OAAO;AAC3C,QAAI,EAAE,IAAI,CAAC,gBAAgB,IAAI,MAAM,OAAO;AAC5C,QAAI,EAAE,IAAI,MAAM,gBAAgB,IAAI,CAAC,MAAM;AAE3C,SAAK,OAAO,iBAAiB,eAAe,KAAK,UAAU;AAAA,EAC5D;AACH;;"}