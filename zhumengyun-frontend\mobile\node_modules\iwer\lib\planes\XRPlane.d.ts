/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_PLANE } from '../private.js';
import type { XRFrame } from '../frameloop/XRFrame.js';
import { XRRigidTransform } from '../primitives/XRRigidTransform.js';
import { XRSemanticLabels } from '../labels/labels.js';
import { XRSpace } from '../spaces/XRSpace.js';
export declare enum XRPlaneOrientation {
    Horizontal = "horizontal",
    Vertical = "vertical"
}
/**
 * XRPlane orientation mapping from semantic labels.
 * For more details, see the {@link https://github.com/immersive-web/semantic-labels | Semantic Labels Documentation}.
 */
export declare const XREntityOrientation: Partial<Record<XRSemanticLabels, XRPlaneOrientation>>;
export declare class XRPlane {
    [P_PLANE]: {
        nativePlane: NativePlane;
        frame: XRFrame;
        planeSpace: XRSpace;
        polygon: DOMPointReadOnly[];
        lastChangedTime: DOMHighResTimeStamp;
        semanticLabel?: XRSemanticLabels;
        orientation?: XRPlaneOrientation;
    };
    constructor(nativePlane: NativePlane, planeSpace: XRSpace, polygon: DOMPointReadOnly[], semanticLabel?: XRSemanticLabels);
    get planeSpace(): XRSpace;
    get polygon(): ReadonlyArray<DOMPointReadOnly>;
    get orientation(): XRPlaneOrientation | undefined;
    get lastChangedTime(): number;
    get semanticLabel(): XRSemanticLabels | undefined;
}
export declare class XRPlaneSet extends Set<XRPlane> {
}
export declare class NativePlane {
    transform: XRRigidTransform;
    polygon: DOMPointReadOnly[];
    semanticLabel: XRSemanticLabels;
    constructor(transform: XRRigidTransform, polygon: DOMPointReadOnly[], semanticLabel: XRSemanticLabels);
}
//# sourceMappingURL=XRPlane.d.ts.map