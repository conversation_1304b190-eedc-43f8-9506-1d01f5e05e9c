export type XRSessionFeatureRequest = 'required' | true | false;
export type XRSessionInitOptions = {
    /**
     * whether the session has bounds
     * false means unbounded (only available in AR)
     * true means bounded (allows to reference the bounding space)
     * undefined means bounded but no access to bounding space
     * @default undefined
     */
    bounded?: boolean | undefined;
    /**
     * @default true
     */
    anchors?: XRSessionFeatureRequest;
    /**
     * @default true
     */
    handTracking?: XRSessionFeatureRequest;
    /**
     * @default true
     */
    bodyTracking?: XRSessionFeatureRequest;
    /**
     * @default true
     */
    layers?: XRSessionFeatureRequest;
    /**
     * @default true
     */
    meshDetection?: XRSessionFeatureRequest;
    /**
     * @default true
     */
    planeDetection?: XRSessionFeatureRequest;
    /**
     * @default false
     */
    depthSensing?: XRSessionFeatureRequest;
    /**
     * overrides the session init object
     * use with caution
     * @default undefined
     */
    customSessionInit?: XRSessionInit;
    /**
     * @default true
     */
    hitTest?: XRSessionFeatureRequest;
    /**
     * @default true
     */
    domOverlay?: XRSessionFeatureRequest | Element;
};
export declare function buildXRSessionInit(mode: XRSessionMode, domOverlayRoot: Element | undefined, { anchors, handTracking, layers, meshDetection, planeDetection, customSessionInit, depthSensing, hitTest, domOverlay, bodyTracking, //until 6.7 since breaking change
bounded, }?: XRSessionInitOptions): XRSessionInit;
