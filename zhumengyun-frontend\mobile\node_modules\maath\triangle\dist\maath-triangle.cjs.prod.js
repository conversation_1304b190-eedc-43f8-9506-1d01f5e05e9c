'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var triangle_dist_maathTriangle = require('../../dist/triangle-9e5a8229.cjs.prod.js');
require('three');
require('../../dist/matrix-e0b2acc5.cjs.prod.js');
require('../../dist/isNativeReflectConstruct-9acebf01.cjs.prod.js');



exports.arePointsCollinear = triangle_dist_maathTriangle.arePointsCollinear;
exports.doThreePointsMakeARight = triangle_dist_maathTriangle.doThreePointsMakeARight;
exports.getCircumcircle = triangle_dist_maathTriangle.getCircumcircle;
exports.isPointInCircumcircle = triangle_dist_maathTriangle.isPointInCircumcircle;
exports.isPointInTriangle = triangle_dist_maathTriangle.isPointInTriangle;
exports.isTriangleClockwise = triangle_dist_maathTriangle.isTriangleClockwise;
exports.triangleDeterminant = triangle_dist_maathTriangle.triangleDeterminant;
