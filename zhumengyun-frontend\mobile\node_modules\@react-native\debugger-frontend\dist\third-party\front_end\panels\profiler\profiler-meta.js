import*as e from"../../core/i18n/i18n.js";import"../../core/root/root.js";import*as o from"../../core/sdk/sdk.js";import*as i from"../../ui/legacy/legacy.js";let t;const r={memory:"Memory",liveHeapProfile:"Live Heap Profile",startRecordingHeapAllocations:"Start recording heap allocations",stopRecordingHeapAllocations:"Stop recording heap allocations",startRecordingHeapAllocationsAndReload:"Start recording heap allocations and reload the page",startStopRecording:"Start/stop recording",showMemory:"Show Memory",showLiveHeapProfile:"Show Live Heap Profile",clearAllProfiles:"Clear all profiles",saveProfile:"Save profile…",loadProfile:"Load profile…",deleteProfile:"Delete profile"},a=e.i18n.registerUIStrings("panels/profiler/profiler-meta.ts",r),l=e.i18n.getLazilyComputedLocalizedString.bind(void 0,a);async function n(){return t||(t=await import("./profiler.js")),t}function s(e){return void 0===t?[]:e(t)}i.ViewManager.registerViewExtension({location:"panel",id:"heap-profiler",commandPrompt:l(r.showMemory),title:l(r.memory),order:60,loadView:async()=>(await n()).HeapProfilerPanel.HeapProfilerPanel.instance(),experiment:"js-heap-profiler-enable"}),i.ViewManager.registerViewExtension({location:"drawer-view",id:"live-heap-profile",commandPrompt:l(r.showLiveHeapProfile),title:l(r.liveHeapProfile),persistence:"closeable",order:100,loadView:async()=>(await n()).LiveHeapProfileView.LiveHeapProfileView.instance(),experiment:"live-heap-profile"}),i.ActionRegistration.registerActionExtension({actionId:"live-heap-profile.toggle-recording",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,loadActionDelegate:async()=>new((await n()).LiveHeapProfileView.ActionDelegate),category:"MEMORY",experiment:"live-heap-profile",options:[{value:!0,title:l(r.startRecordingHeapAllocations)},{value:!1,title:l(r.stopRecordingHeapAllocations)}]}),i.ActionRegistration.registerActionExtension({actionId:"live-heap-profile.start-with-reload",iconClass:"refresh",loadActionDelegate:async()=>new((await n()).LiveHeapProfileView.ActionDelegate),category:"MEMORY",experiment:"live-heap-profile",title:l(r.startRecordingHeapAllocationsAndReload)}),i.ActionRegistration.registerActionExtension({actionId:"profiler.heap-toggle-recording",category:"MEMORY",iconClass:"record-start",title:l(r.startStopRecording),toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>s((e=>[e.HeapProfilerPanel.HeapProfilerPanel])),loadActionDelegate:async()=>(await n()).HeapProfilerPanel.HeapProfilerPanel.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),i.ActionRegistration.registerActionExtension({actionId:"profiler.clear-all",category:"MEMORY",iconClass:"clear",contextTypes:()=>s((e=>[e.ProfilesPanel.ProfilesPanel])),loadActionDelegate:async()=>new((await n()).ProfilesPanel.ActionDelegate),title:l(r.clearAllProfiles)}),i.ActionRegistration.registerActionExtension({actionId:"profiler.load-from-file",category:"MEMORY",iconClass:"import",contextTypes:()=>s((e=>[e.ProfilesPanel.ProfilesPanel])),loadActionDelegate:async()=>new((await n()).ProfilesPanel.ActionDelegate),title:l(r.loadProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+O"},{platform:"mac",shortcut:"Meta+O"}]}),i.ActionRegistration.registerActionExtension({actionId:"profiler.save-to-file",category:"MEMORY",iconClass:"download",contextTypes:()=>s((e=>[e.ProfileHeader.ProfileHeader])),loadActionDelegate:async()=>new((await n()).ProfilesPanel.ActionDelegate),title:l(r.saveProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+S"},{platform:"mac",shortcut:"Meta+S"}]}),i.ActionRegistration.registerActionExtension({actionId:"profiler.delete-profile",category:"MEMORY",iconClass:"download",contextTypes:()=>s((e=>[e.ProfileHeader.ProfileHeader])),loadActionDelegate:async()=>new((await n()).ProfilesPanel.ActionDelegate),title:l(r.deleteProfile)}),i.ContextMenu.registerProvider({contextTypes:()=>[o.RemoteObject.RemoteObject],loadProvider:async()=>(await n()).HeapProfilerPanel.HeapProfilerPanel.instance(),experiment:void 0}),i.ContextMenu.registerItem({location:"profilerMenu/default",actionId:"profiler.save-to-file",order:10}),i.ContextMenu.registerItem({location:"profilerMenu/default",actionId:"profiler.delete-profile",order:11});
