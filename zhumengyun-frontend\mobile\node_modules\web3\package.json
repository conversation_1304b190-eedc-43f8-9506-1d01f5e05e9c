{"name": "web3", "version": "4.16.0", "description": "Ethereum JavaScript API", "main": "./lib/commonjs/index.js", "module": "./lib/esm/index.js", "exports": {".": {"types": "./lib/types/index.d.ts", "import": "./lib/esm/index.js", "require": "./lib/commonjs/index.js"}}, "repository": "https://github.com/ChainSafe/web3.js", "engines": {"node": ">=14.0.0", "npm": ">=6.12.0"}, "author": "ChainSafe Systems", "browser": "./dist/web3.min.js", "license": "LGPL-3.0", "keywords": ["Ethereum", "JavaScript", "API"], "files": ["lib/**/*", "src/**/*", "dist/**/*"], "scripts": {"clean": "rimraf dist && rimraf lib", "prebuild": "yarn clean", "version:output": "[ -z $npm_package_version ] && rm ./src/version.ts || echo \"/* eslint-disable header/header */ export const Web3PkgInfo = { version: '$npm_package_version' };\" > ./src/version.ts", "build": "yarn version:output && concurrently --kill-others-on-fail \"yarn:build:*(!web|check)\"", "build:cjs": "tsc --build tsconfig.cjs.json && echo '{\"type\": \"commonjs\"}' > ./lib/commonjs/package.json", "build:esm": "tsc --build tsconfig.esm.json && echo '{\"type\": \"module\"}' > ./lib/esm/package.json", "build:types": "tsc --build tsconfig.types.json", "build:web": "npx webpack", "build:web:analyze": "npx webpack --config ./webpack.analyze.js", "build:check": "node -e \"require('./lib')\"", "lint": "eslint --cache --cache-strategy content --ext .ts .", "lint:fix": "eslint --fix --ext .js,.ts .", "format": "prettier --write '**/*'", "test": "jest --config=./test/unit/jest.config.js", "test:coverage:unit": "jest --config=./test/unit/jest.config.js --coverage=true --coverage-reporters=text", "test:coverage:integration": "jest --config=./test/integration/jest.config.js --forceExit --coverage=true --coverage-reporters=text", "test:e2e:mainnet": "jest --config=./test/e2e/jest.config.js --forceExit --runInBand", "test:e2e:sepolia": "jest --config=./test/e2e/jest.config.js --forceExit --runInBand", "test:sync:integration": "jest --config=./test/integration/jest.config.js ./test/integration/sync.test.ts", "test:ci": "jest --coverage=true --coverage-reporters=json --verbose", "test:watch": "npm test -- --watch", "test:unit": "jest --config=./test/unit/jest.config.js", "test:integration": "jest --config=./test/integration/jest.config.js --forceExit", "test:integration:stress": "jest --config=./test/stress/jest.config.js --forceExit", "test:blackbox:hardhat:http": "./scripts/black_box_test.sh hardhat http", "test:blackbox:hardhat:ws": "./scripts/black_box_test.sh hardhat ws", "test:blackbox:geth:http": "./scripts/black_box_test.sh geth http", "test:blackbox:geth:ws": "./scripts/black_box_test.sh geth ws", "test:blackbox:infura:http": "./scripts/black_box_test.sh infura http", "test:blackbox:infura:ws": "./scripts/black_box_test.sh infura ws", "test:e2e:chrome:stress": "npx cypress run --headless --browser chrome", "test:benchmark": "npx ts-node --preferTsExts ../web3/test/benchmark/benchmark.ts"}, "devDependencies": {"@truffle/hdwallet-provider": "^2.0.12", "@types/benchmark": "^2.1.5", "@types/jest": "^28.1.6", "@typescript-eslint/eslint-plugin": "^5.30.7", "@typescript-eslint/parser": "^5.30.7", "benchmark": "^2.1.4", "eslint": "^8.20.0", "eslint-config-base-web3": "0.1.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "ethereum-cryptography": "^2.1.2", "ganache": "^7.5.0", "hardhat": "^2.19.4", "in3": "^3.3.3", "jest": "^29.7.0", "jest-extended": "^3.0.1", "prettier": "^2.7.1", "ts-jest": "^29.1.1", "typescript": "^5.5.4", "web3-providers-ipc": "^4.0.7"}, "dependencies": {"web3-core": "^4.7.1", "web3-errors": "^1.3.1", "web3-eth": "^4.11.1", "web3-eth-abi": "^4.4.1", "web3-eth-accounts": "^4.3.1", "web3-eth-contract": "^4.7.2", "web3-eth-ens": "^4.4.0", "web3-eth-iban": "^4.0.7", "web3-eth-personal": "^4.1.0", "web3-net": "^4.1.0", "web3-providers-http": "^4.2.0", "web3-providers-ws": "^4.0.8", "web3-rpc-methods": "^1.3.0", "web3-rpc-providers": "^1.0.0-rc.4", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "gitHead": "aa197b8f7914ff336423c4081459c9bf7867c4b3"}