/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_JOINT_POSE } from '../private.js';
import { XRPose } from './XRPose.js';
import { XRRigidTransform } from '../primitives/XRRigidTransform.js';
export declare class XRJointPose extends XRPose {
    [P_JOINT_POSE]: {
        radius: number;
    };
    constructor(transform: XRRigidTransform, radius: number, emulatedPosition?: boolean, linearVelocity?: DOMPointReadOnly | undefined, angularVelocity?: DOMPointReadOnly | undefined);
    get radius(): number;
}
//# sourceMappingURL=XRJointPose.d.ts.map