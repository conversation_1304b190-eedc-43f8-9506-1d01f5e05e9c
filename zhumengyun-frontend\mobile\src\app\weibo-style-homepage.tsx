'use client'

import { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'

export default function WeiboStyleHomepage() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isLiked, setIsLiked] = useState(false)
  const [isSaved, setIsSaved] = useState(false)
  const [isFollowing, setIsFollowing] = useState(false)
  const [showComments, setShowComments] = useState(false)
  const [showShare, setShowShare] = useState(false)
  const [isPlaying, setIsPlaying] = useState(true)
  const [currentTime, setCurrentTime] = useState(42)
  const [duration, setDuration] = useState(46)
  const [showSidebar, setShowSidebar] = useState(false)
  const [showUserProfile, setShowUserProfile] = useState(false)
  const [showVideoInfo, setShowVideoInfo] = useState(false)
  const [volume, setVolume] = useState(80)
  const [showVolumeControl, setShowVolumeControl] = useState(false)

  // 视频内容数据
  const videoContent = [
    {
      id: 1,
      title: "亚洲巡回演唱会来西亚柔佛新山萨尔萨舞 编舞课堂萨尔萨舞蹈 #白小白编舞",
      author: "Ticki白小白",
      authorAvatar: "/api/placeholder/40/40",
      verified: true,
      followers: "4个朋友关注",
      videoUrl: "/api/placeholder/400/600",
      likes: 134,
      shares: 39,
      saves: 85,
      comments: 17,
      tags: ["#白小白编舞", "@微信时刻"],
      emoji: "💃",
      subtitle: "萨尔萨舞蹈教学",
      description: "专业编舞课堂"
    },
    {
      id: 2,
      title: "AI驱动的智慧城市建设项目展示 #NextGen2025 #智慧城市",
      author: "AI建筑大师",
      authorAvatar: "/api/placeholder/40/40",
      verified: true,
      followers: "1.2万粉丝",
      videoUrl: "/api/placeholder/400/600",
      likes: 2340,
      shares: 156,
      saves: 890,
      comments: 234,
      tags: ["#NextGen2025", "#智慧城市", "#AI建筑"],
      emoji: "🏗️",
      subtitle: "AI智慧城市设计",
      description: "未来建筑新体验"
    },
    {
      id: 3,
      title: "元宇宙虚拟展厅设计案例分享 #元宇宙 #VR设计",
      author: "VR设计师小李",
      authorAvatar: "/api/placeholder/40/40",
      verified: true,
      followers: "8.5k粉丝",
      videoUrl: "/api/placeholder/400/600",
      likes: 1890,
      shares: 234,
      saves: 567,
      comments: 123,
      tags: ["#元宇宙", "#VR设计", "#虚拟展厅"],
      emoji: "🌌",
      subtitle: "元宇宙虚拟展厅",
      description: "沉浸式3D体验"
    }
  ]

  const currentContent = videoContent[currentIndex]

  // 左侧导航菜单
  const sidebarItems = [
    { name: '首页', icon: '🏠', active: true },
    { name: '推荐', icon: '🔥', active: false },
    { name: '关注', icon: '👥', active: false },
    { name: '朋友', icon: '👫', active: false },
    { name: '直播', icon: '📺', active: false },
    { name: '生活', icon: '🌟', active: false },
    { name: '知识', icon: '📚', active: false },
    { name: '音乐', icon: '🎵', active: false },
    { name: '游戏', icon: '🎮', active: false },
    { name: '影视', icon: '🎬', active: false },
    { name: '旅行', icon: '✈️', active: false },
    { name: '美食', icon: '🍜', active: false }
  ]

  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k'
    }
    return num.toString()
  }

  // 格式化时间
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // 交互处理函数
  const handleLike = useCallback(() => setIsLiked(!isLiked), [isLiked])
  const handleSave = useCallback(() => setIsSaved(!isSaved), [isSaved])
  const handleFollow = useCallback(() => setIsFollowing(!isFollowing), [isFollowing])
  const handleComment = useCallback(() => setShowComments(true), [])
  const handleShare = useCallback(() => setShowShare(true), [])
  const togglePlay = useCallback(() => setIsPlaying(!isPlaying), [isPlaying])

  // 视频切换函数
  const nextVideo = useCallback(() => {
    setCurrentIndex((prev) => (prev + 1) % videoContent.length)
    setIsLiked(false)
    setIsSaved(false)
    setIsPlaying(true)
  }, [videoContent.length])

  const prevVideo = useCallback(() => {
    setCurrentIndex((prev) => (prev - 1 + videoContent.length) % videoContent.length)
    setIsLiked(false)
    setIsSaved(false)
    setIsPlaying(true)
  }, [videoContent.length])

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowUp') {
        prevVideo()
      } else if (e.key === 'ArrowDown') {
        nextVideo()
      } else if (e.key === ' ') {
        e.preventDefault()
        togglePlay()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [nextVideo, prevVideo, togglePlay])

  return (
    <div className="h-screen bg-black flex overflow-hidden">
      {/* 左侧导航栏 */}
      <div className="w-20 bg-gray-900/50 backdrop-blur-sm flex flex-col items-center py-4 space-y-1">
        {/* Logo */}
        <div className="mb-6">
          <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">M</span>
          </div>
          <div className="text-orange-500 text-xs mt-1 font-medium">视频号</div>
        </div>

        {/* 导航菜单 */}
        <div className="flex-1 space-y-2">
          {sidebarItems.map((item, index) => (
            <button
              key={index}
              className={`w-12 h-12 rounded-lg flex flex-col items-center justify-center text-xs transition-colors ${
                item.active 
                  ? 'bg-orange-500/20 text-orange-500' 
                  : 'text-gray-400 hover:text-white hover:bg-white/10'
              }`}
            >
              <span className="text-lg mb-1">{item.icon}</span>
              <span className="text-[10px]">{item.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* 主视频区域 */}
      <div className="flex-1 relative">
        {/* 视频背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900 via-pink-800 to-orange-700">
          <div className="w-full h-full bg-black/20 flex items-center justify-center">
            {/* 模拟视频内容 */}
            <div className="w-full max-w-md h-full bg-gradient-to-br from-purple-600 via-pink-500 to-red-500 relative overflow-hidden">
              {/* 视频内容区域 */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white">
                  <div className="w-32 h-32 bg-white/20 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <span className="text-4xl">{currentContent.emoji}</span>
                  </div>
                  <h3 className="text-lg font-bold mb-2">{currentContent.subtitle}</h3>
                  <p className="text-sm opacity-80">{currentContent.description}</p>
                </div>
              </div>

              {/* 播放控制 */}
              {!isPlaying && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <button 
                    onClick={togglePlay}
                    className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm"
                  >
                    <span className="text-white text-2xl ml-1">▶️</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 底部视频控制栏 */}
        <div className="absolute bottom-20 left-0 right-0 px-4">
          {/* 进度条 */}
          <div className="flex items-center space-x-3 mb-4">
            <button onClick={togglePlay} className="text-white">
              {isPlaying ? '⏸️' : '▶️'}
            </button>
            <span className="text-white text-sm">{formatTime(currentTime)}</span>
            <div className="flex-1 h-1 bg-white/30 rounded-full">
              <div 
                className="h-full bg-white rounded-full transition-all duration-300"
                style={{ width: `${(currentTime / duration) * 100}%` }}
              />
            </div>
            <span className="text-white text-sm">{formatTime(duration)}</span>
            <div className="flex space-x-2">
              <button className="text-white text-lg">⏰</button>
              <button className="text-white text-lg">🔊</button>
              <button className="text-white text-lg">⚙️</button>
              <button className="text-white text-lg">📱</button>
              <button className="text-white text-lg">⛶</button>
            </div>
          </div>

          {/* 视频信息 */}
          <div className="text-white">
            <p className="text-sm mb-2">{currentContent.title}</p>
            <div className="flex items-center space-x-3">
              <Image
                src={currentContent.authorAvatar}
                alt={currentContent.author}
                width={32}
                height={32}
                className="w-8 h-8 rounded-full object-cover"
              />
              <div>
                <div className="flex items-center space-x-1">
                  <span className="text-sm font-medium">{currentContent.author}</span>
                  {currentContent.verified && <span className="text-yellow-400">🔥</span>}
                </div>
                <span className="text-xs text-gray-300">{currentContent.followers}</span>
              </div>
              <button className="text-xs text-gray-300 bg-white/20 px-2 py-1 rounded">
                已关注
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 右侧交互按钮 */}
      <div className="w-16 flex flex-col items-center justify-end pb-32 space-y-6">
        {/* 视频切换指示器 */}
        <div className="flex flex-col items-center space-y-4 mb-8">
          <button
            onClick={prevVideo}
            className="w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-white/30 transition-colors"
          >
            ↑
          </button>

          <div className="flex flex-col space-y-1">
            {videoContent.map((_, index) => (
              <div
                key={index}
                className={`w-1 h-4 rounded-full transition-colors ${
                  index === currentIndex ? 'bg-white' : 'bg-white/30'
                }`}
              />
            ))}
          </div>

          <button
            onClick={nextVideo}
            className="w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-white/30 transition-colors"
          >
            ↓
          </button>
        </div>
        {/* 点赞 */}
        <motion.button
          onClick={handleLike}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="flex flex-col items-center space-y-1"
        >
          <div className={`w-12 h-12 rounded-full flex items-center justify-center transition-all ${
            isLiked ? 'bg-red-500 scale-110' : 'bg-white/20 backdrop-blur-sm'
          }`}>
            <svg className={`w-6 h-6 ${isLiked ? 'text-white' : 'text-white'}`} fill="currentColor" viewBox="0 0 24 24">
              <path d="M7.493 18.75c-.425 0-.82-.236-.975-.632A7.48 7.48 0 016 15.375c0-1.75.599-3.358 1.602-4.634.151-.192.373-.309.6-.397.473-.183.89-.514 1.212-.924a9.042 9.042 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75 2.25 2.25 0 012.25 2.25c0 1.152-.26 2.243-.723 3.218-.266.558-.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H14.23c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23h-.777zM2.331 10.977a11.969 11.969 0 00-.831 4.398 12 12 0 00.52 3.507c.26.85 1.084 1.368 1.973 1.368H4.9c.445 0 .72-.498.523-.898a8.963 8.963 0 01-.924-3.977c0-1.708.476-3.305 1.302-4.666.245-.403-.028-.959-.5-.959H4.25c-.832 0-1.612.453-1.918 1.227z"/>
            </svg>
          </div>
          <span className={`text-xs transition-colors ${isLiked ? 'text-red-400' : 'text-white'}`}>
            {isLiked ? currentContent.likes + 1 : currentContent.likes}
          </span>
        </motion.button>

        {/* 分享 */}
        <button
          onClick={handleShare}
          className="flex flex-col items-center space-y-1"
        >
          <div className="w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
            </svg>
          </div>
          <span className="text-white text-xs">{currentContent.shares}</span>
        </button>

        {/* 收藏 */}
        <motion.button
          onClick={handleSave}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="flex flex-col items-center space-y-1"
        >
          <div className={`w-12 h-12 rounded-full flex items-center justify-center transition-all ${
            isSaved ? 'bg-yellow-500 scale-110' : 'bg-white/20 backdrop-blur-sm'
          }`}>
            <svg className={`w-6 h-6 ${isSaved ? 'text-white' : 'text-white'}`} fill={isSaved ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          </div>
          <span className={`text-xs transition-colors ${isSaved ? 'text-yellow-400' : 'text-white'}`}>
            {isSaved ? currentContent.saves + 1 : currentContent.saves}
          </span>
        </motion.button>

        {/* 评论 */}
        <button
          onClick={handleComment}
          className="flex flex-col items-center space-y-1"
        >
          <div className="w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
          <span className="text-white text-xs">{currentContent.comments}</span>
        </button>
      </div>

      {/* 顶部搜索栏 */}
      <div className="absolute top-4 left-24 right-20 flex items-center justify-between">
        <div className="flex-1 max-w-md">
          <div className="relative">
            <input
              type="text"
              placeholder="搜索"
              className="w-full bg-black/30 backdrop-blur-sm border border-white/20 rounded-full px-4 py-2 text-white placeholder-gray-400 text-sm"
            />
            <button className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              🔍
            </button>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          <button className="text-white text-xl">📷</button>
          <button className="text-white text-xl">👤</button>
        </div>
      </div>

      {/* 评论弹窗 */}
      <AnimatePresence>
        {showComments && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end"
            onClick={() => setShowComments(false)}
          >
            <motion.div
              initial={{ y: '100%' }}
              animate={{ y: 0 }}
              exit={{ y: '100%' }}
              className="w-full bg-gray-900 rounded-t-3xl p-6 max-h-[70vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-white text-lg font-bold">评论 {currentContent.comments}</h3>
                <button
                  onClick={() => setShowComments(false)}
                  className="text-gray-400 hover:text-white"
                >
                  ✕
                </button>
              </div>

              {/* 评论列表 */}
              <div className="space-y-4 mb-6">
                {[
                  { user: '建筑师小王', content: '这个设计太棒了！', time: '2分钟前', likes: 12 },
                  { user: '设计爱好者', content: 'AI技术真的改变了建筑行业', time: '5分钟前', likes: 8 },
                  { user: '学生小李', content: '想学习这个技术', time: '10分钟前', likes: 3 }
                ].map((comment, index) => (
                  <div key={index} className="flex space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">{comment.user[0]}</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-white text-sm font-medium">{comment.user}</span>
                        <span className="text-gray-400 text-xs">{comment.time}</span>
                      </div>
                      <p className="text-gray-300 text-sm mb-2">{comment.content}</p>
                      <div className="flex items-center space-x-4">
                        <button className="flex items-center space-x-1 text-gray-400 hover:text-red-400">
                          <span className="text-xs">👍</span>
                          <span className="text-xs">{comment.likes}</span>
                        </button>
                        <button className="text-gray-400 hover:text-white text-xs">回复</button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 评论输入 */}
              <div className="flex space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">我</span>
                </div>
                <div className="flex-1 flex space-x-2">
                  <input
                    type="text"
                    placeholder="说点什么..."
                    className="flex-1 bg-gray-800 border border-gray-700 rounded-full px-4 py-2 text-white placeholder-gray-400 text-sm focus:outline-none focus:border-purple-500"
                  />
                  <button className="bg-purple-500 hover:bg-purple-600 rounded-full px-6 py-2 text-white text-sm font-medium">
                    发送
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 分享弹窗 */}
      <AnimatePresence>
        {showShare && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
            onClick={() => setShowShare(false)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="bg-gray-900 rounded-3xl p-6 max-w-sm w-full mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-white text-lg font-bold">分享到</h3>
                <button
                  onClick={() => setShowShare(false)}
                  className="text-gray-400 hover:text-white"
                >
                  ✕
                </button>
              </div>

              <div className="grid grid-cols-4 gap-4 mb-6">
                {[
                  { name: '微信', icon: '💬', color: 'bg-green-500' },
                  { name: '朋友圈', icon: '🌟', color: 'bg-green-600' },
                  { name: '微博', icon: '📱', color: 'bg-red-500' },
                  { name: '抖音', icon: '🎵', color: 'bg-black' },
                  { name: 'QQ', icon: '🐧', color: 'bg-blue-500' },
                  { name: 'QQ空间', icon: '⭐', color: 'bg-yellow-500' },
                  { name: '复制链接', icon: '🔗', color: 'bg-gray-600' },
                  { name: '更多', icon: '⋯', color: 'bg-gray-500' }
                ].map((platform, index) => (
                  <button
                    key={index}
                    className="flex flex-col items-center space-y-2 p-3 rounded-xl hover:bg-gray-800 transition-colors"
                  >
                    <div className={`w-12 h-12 ${platform.color} rounded-xl flex items-center justify-center`}>
                      <span className="text-white text-lg">{platform.icon}</span>
                    </div>
                    <span className="text-white text-xs">{platform.name}</span>
                  </button>
                ))}
              </div>

              <button
                onClick={() => setShowShare(false)}
                className="w-full py-3 bg-gray-800 hover:bg-gray-700 rounded-xl text-white font-medium transition-colors"
              >
                取消
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
