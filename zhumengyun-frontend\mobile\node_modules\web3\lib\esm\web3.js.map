{"version": 3, "file": "web3.js", "sourceRoot": "", "sources": ["../../src/web3.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AACF,gDAAgD;AAChD,OAAO,EACN,WAAW,EAIX,mBAAmB,GACnB,MAAM,WAAW,CAAC;AACnB,OAAO,EAAE,OAAO,EAA0B,uBAAuB,EAAE,MAAM,UAAU,CAAC;AACpF,OAAO,QAAQ,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAE,GAAG,EAAE,iBAAiB,EAAE,MAAM,cAAc,CAAC;AACtD,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AACrC,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAE,GAAG,EAAE,MAAM,UAAU,CAAC;AAC/B,OAAO,KAAK,KAAK,MAAM,YAAY,CAAC;AACpC,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,qBAAqB,EAAE,MAAM,YAAY,CAAC;AAC5E,OAAO,EAAE,OAAO,EAAE,MAAM,oBAAoB,CAAC;AAS7C,OAAO,EAAE,wBAAwB,EAAE,MAAM,aAAa,CAAC;AACvD,OAAO,GAAG,MAAM,UAAU,CAAC;AAC3B,OAAO,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAC;AAEvD,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAC3C,OAAO,EAAE,uBAAuB,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAErF,MAAM,OAAO,IAIX,SAAQ,WAAmF;IAiB5F,YACC,oBAG2E,OAAO;;QAElF,IACC,SAAS,CAAC,iBAAiB,CAAC;YAC5B,CAAC,OAAO,iBAAiB,KAAK,QAAQ,IAAI,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;YAC1E,CAAC,OAAO,iBAAiB,KAAK,QAAQ;gBACrC,CAAC,mBAAmB,CAAC,iBAAwD,CAAC;gBAC9E,CAAE,iBAA4C,CAAC,QAAQ,CAAC,EACxD,CAAC;YACF,OAAO,CAAC,IAAI,CACX,+GAA+G,CAC/G,CAAC;QACH,CAAC;QAED,IAAI,kBAAkB,GAA4C,EAAE,CAAC;QACrE,IACC,OAAO,iBAAiB,KAAK,QAAQ;YACrC,mBAAmB,CAAC,iBAAuC,CAAC,EAC3D,CAAC;YACF,kBAAkB,CAAC,QAAQ,GAAG,iBAGT,CAAC;QACvB,CAAC;aAAM,IAAI,iBAAiB,EAAE,CAAC;YAC9B,kBAAkB,GAAG,iBAA2C,CAAC;QAClE,CAAC;aAAM,CAAC;YACP,kBAAkB,GAAG,EAAE,CAAC;QACzB,CAAC;QAED,kBAAkB,CAAC,uBAAuB,GAAG,gCAEzC,uBAAuB,GAEvB,CAAC,MAAA,kBAAkB,CAAC,uBAAuB,mCAAI,EAAE,CAAC,CACrB,CAAC;QAElC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAC1B,MAAM,QAAQ,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAE9C,4BAA4B;QAC5B,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC/B,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;QAEjC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,uDAAuD;QACvD,4DAA4D;QAC5D,MAAM,IAAI,GAAG,IAAI,CAAC;QAElB,MAAM,eAAyC,SAAQ,QAAa;YAiBnE,YACC,aAAkB,EAClB,yBAAyD,EACzD,8BAAoD,EACpD,qBAAgD,EAChD,YAAyB;gBAEzB,IACC,qBAAqB,CAAC,yBAAyB,CAAC;oBAChD,qBAAqB,CAAC,8BAA8B,CAAC,EACpD,CAAC;oBACF,MAAM,IAAI,wBAAwB,CACjC,2DAA2D,CAC3D,CAAC;gBACH,CAAC;gBACD,IAAI,OAA2B,CAAC;gBAChC,IAAI,OAAO,GAAW,EAAE,CAAC;gBACzB,IAAI,OAA0B,CAAC;gBAC/B,IAAI,UAAkC,CAAC;gBAEvC,8CAA8C;gBAC9C,IACC,CAAC,SAAS,CAAC,yBAAyB,CAAC;oBACrC,OAAO,yBAAyB,KAAK,QAAQ;oBAC7C,OAAO,yBAAyB,KAAK,QAAQ,EAC5C,CAAC;oBACF,MAAM,IAAI,wBAAwB,EAAE,CAAC;gBACtC,CAAC;gBAED,IAAI,OAAO,yBAAyB,KAAK,QAAQ,EAAE,CAAC;oBACnD,OAAO,GAAG,yBAAyB,CAAC;gBACrC,CAAC;gBACD,IAAI,qBAAqB,CAAC,yBAAyB,CAAC,EAAE,CAAC;oBACtD,OAAO,GAAG,yBAAmC,CAAC;gBAC/C,CAAC;qBAAM,IAAI,qBAAqB,CAAC,8BAA8B,CAAC,EAAE,CAAC;oBAClE,OAAO,GAAG,8BAAwC,CAAC;gBACpD,CAAC;qBAAM,CAAC;oBACP,OAAO,GAAG,EAAE,CAAC;gBACd,CAAC;gBAED,IAAI,yBAAyB,YAAY,WAAW,EAAE,CAAC;oBACtD,OAAO,GAAG,yBAAyB,CAAC;gBACrC,CAAC;qBAAM,IAAI,8BAA8B,YAAY,WAAW,EAAE,CAAC;oBAClE,OAAO,GAAG,8BAA8B,CAAC;gBAC1C,CAAC;qBAAM,IAAI,qBAAqB,YAAY,WAAW,EAAE,CAAC;oBACzD,OAAO,GAAG,qBAAqB,CAAC;gBACjC,CAAC;qBAAM,CAAC;oBACP,OAAO,GAAG,IAAI,CAAC,gBAAgB,EAAuB,CAAC;gBACxD,CAAC;gBAED,IAAI,YAAY,EAAE,CAAC;oBAClB,UAAU,GAAG,YAAY,CAAC;gBAC3B,CAAC;qBAAM,IAAI,YAAY,CAAC,8BAA8B,CAAC,EAAE,CAAC;oBACzD,UAAU,GAAG,8BAA4C,CAAC;gBAC3D,CAAC;qBAAM,IAAI,YAAY,CAAC,qBAAqB,CAAC,EAAE,CAAC;oBAChD,UAAU,GAAG,qBAAqB,CAAC;gBACpC,CAAC;gBAED,KAAK,CAAC,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;gBAC5D,KAAK,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;gBAErC,gDAAgD;gBAChD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;oBACrB,gDAAgD;oBAChD,MAAM,YAAY,GAAG,GAAG,CAAC,wBAAwB,EAAE,CAAC;oBACpD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC;wBAC9B,KAAK,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;oBAC9C,CAAC;gBACF,CAAC;YACF,CAAC;SACD;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE9B,aAAa;QACb,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE;YAC7B,aAAa;YACb,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,iBAAiB,CAAC,IAAI,CAAC,EAAE,4CAA4C;YAExF,eAAe;YACf,IAAI;YAEJ,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;YAClB,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;YAE5B,6BAA6B;YAC7B,QAAQ,EAAE,eAAe;YAEzB,cAAc;YACd,GAAG;YAEH,kBAAkB;YAClB,QAAQ;SACR,CAAC,CAAC;IACJ,CAAC;;AApLa,YAAO,GAAG,WAAW,CAAC,OAAO,CAAC;AAC9B,UAAK,GAAG,KAAK,CAAC;AACd,4BAAuB,GAAG,uBAAuB,CAAC;AAClD,4BAAuB,GAAG,uBAAuB,CAAC;AAClD,YAAO,GAAG;IACvB,OAAO;IACP,IAAI;IACJ,GAAG;IACH,GAAG;IACH,QAAQ;CACR,CAAC;AA4KH,eAAe,IAAI,CAAC"}