/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_MESH } from '../private.js';
export class XRMesh {
    constructor(nativeMesh, meshSpace, vertices, indices, semanticLabel) {
        this[P_MESH] = {
            nativeMesh,
            frame: undefined,
            meshSpace,
            vertices,
            indices,
            lastChangedTime: performance.now(),
            semanticLabel,
        };
    }
    get meshSpace() {
        return this[P_MESH].meshSpace;
    }
    get vertices() {
        return this[P_MESH].vertices;
    }
    get indices() {
        return this[P_MESH].indices;
    }
    get lastChangedTime() {
        return this[P_MESH].lastChangedTime;
    }
    get semanticLabel() {
        return this[P_MESH].semanticLabel;
    }
}
export class XRMeshSet extends Set {
}
export class NativeMesh {
    constructor(transform, vertices, indices, semanticLabel) {
        this.transform = transform;
        this.vertices = vertices;
        this.indices = indices;
        this.semanticLabel = semanticLabel;
    }
}
//# sourceMappingURL=XRMesh.js.map