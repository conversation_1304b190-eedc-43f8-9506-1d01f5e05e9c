import { Object3D } from 'three';
import { type XRControllerGamepadState, type XRC<PERSON>rollerLayout, type XRControllerLayoutLoaderOptions } from './controller/index.js';
import { type XRHandInputSource, type XRHandLoaderOptions, type XRHandPoseState } from './hand/index.js';
export type XRInputSourceStates = ReadonlyArray<XRInputSourceState>;
export type XRInputSourceState = XRInputSourceStateMap[keyof XRInputSourceStateMap];
export declare function isXRInputSourceState(val: unknown): val is XRInputSourceState;
export type XRHandState = {
    id: string;
    type: 'hand';
    isPrimary: boolean;
    inputSource: XRHandInputSource;
    events: ReadonlyArray<XRInputSourceEvent>;
    pose: XRHandPoseState;
    assetPath: string;
    object?: Object3D;
};
export type XRControllerState = {
    id: string;
    type: 'controller';
    isPrimary: boolean;
    inputSource: XRInputSource;
    events: ReadonlyArray<XRInputSourceEvent>;
    gamepad: XRControllerGamepadState;
    layout: XRControllerLayout;
    object?: Object3D;
};
export type XRTransientPointerState = {
    id: string;
    type: 'transientPointer';
    isPrimary: boolean;
    inputSource: XRInputSource;
    events: ReadonlyArray<XRInputSourceEvent>;
};
export type XRGazeState = {
    id: string;
    type: 'gaze';
    isPrimary: boolean;
    inputSource: XRInputSource;
    events: ReadonlyArray<XRInputSourceEvent>;
};
export type XRScreenInputState = {
    id: string;
    type: 'screenInput';
    isPrimary: boolean;
    inputSource: XRInputSource;
    events: ReadonlyArray<XRInputSourceEvent>;
};
export type XRInputSourceStateMap = {
    hand: XRHandState;
    controller: XRControllerState;
    transientPointer: XRTransientPointerState;
    gaze: XRGazeState;
    screenInput: XRScreenInputState;
};
export declare function createSyncXRInputSourceStates(addController: (controllerState: XRControllerState) => void, options: (XRControllerLayoutLoaderOptions & XRHandLoaderOptions) | undefined): (session: XRSession, current: ReadonlyArray<XRInputSourceState>, changes: Array<{
    isPrimary: boolean;
    added?: XRInputSourceArray | ReadonlyArray<XRInputSource>;
    removed?: XRInputSourceArray | ReadonlyArray<XRInputSource>;
}> | "remove-all") => ReadonlyArray<XRInputSourceState>;
