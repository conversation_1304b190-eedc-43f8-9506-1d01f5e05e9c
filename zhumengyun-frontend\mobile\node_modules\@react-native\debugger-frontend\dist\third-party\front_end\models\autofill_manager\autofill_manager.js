import*as e from"../../core/common/common.js";import*as s from"../../core/host/host.js";import*as t from"../../core/platform/platform.js";import*as i from"../../core/root/root.js";import*as l from"../../core/sdk/sdk.js";import*as a from"../../ui/legacy/legacy.js";let d;class o extends e.ObjectWrapper.ObjectWrapper{#e;#s="";#t=[];#i=[];#l=null;constructor(){super(),l.TargetManager.TargetManager.instance().addModelListener(l.AutofillModel.AutofillModel,"AddressFormFilled",this.#a,this,{scoped:!0}),this.#e=e.Settings.Settings.instance().createSetting("auto-open-autofill-view-on-event",!0)}static instance(e={forceNew:null}){const{forceNew:s}=e;return d&&!s||(d=new o),d}async#a({data:e}){i.Runtime.experiments.isEnabled("autofill-view")&&this.#e.get()?(await a.ViewManager.ViewManager.instance().showView("autofill-view"),s.userMetrics.actionTaken(s.UserMetrics.Action.AutofillReceivedAndTabAutoOpened)):s.userMetrics.actionTaken(s.UserMetrics.Action.AutofillReceived),this.#l=e.autofillModel,this.#d(e.event),this.#s&&this.dispatchEventToListeners("AddressFormFilled",{address:this.#s,filledFields:this.#t,matches:this.#i,autofillModel:this.#l})}getLastFilledAddressForm(){return this.#s&&this.#l?{address:this.#s,filledFields:this.#t,matches:this.#i,autofillModel:this.#l}:null}#d({addressUi:e,filledFields:s}){this.#s=e.addressFields.map((e=>(e=>e.fields.filter((e=>e.value.length)).map((e=>e.value)).join(" "))(e))).filter((e=>e.length)).join("\n"),this.#t=s,this.#i=[];for(let e=0;e<this.#t.length;e++){if(""===this.#t[e].value)continue;const s=t.StringUtilities.escapeForRegExp(this.#t[e].value.replaceAll(/\s/g," ")).replaceAll(/([.,]+)\s/g,"$1? "),i=this.#s.replaceAll(/\s/g," ").matchAll(new RegExp(s,"g"));for(const s of i)void 0!==s.index&&this.#i.push({startIndex:s.index,endIndex:s.index+s[0].length,filledFieldIndex:e})}}}var r=Object.freeze({__proto__:null,AutofillManager:o});export{r as AutofillManager};
