{"version": 3, "file": "socket_provider.js", "sourceRoot": "", "sources": ["../../src/socket_provider.ts"], "names": [], "mappings": ";;;;;;;;;AAyCA,OAAO,EACN,eAAe,EACf,sBAAsB,EACtB,kBAAkB,EAClB,qCAAqC,EACrC,kCAAkC,EAClC,uBAAuB,EACvB,mBAAmB,GACnB,MAAM,aAAa,CAAC;AACrB,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAC;AAC7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,4BAA4B,CAAC;AACjE,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,mBAAmB,EAAE,MAAM,4BAA4B,CAAC;AACjE,OAAO,KAAK,OAAO,MAAM,eAAe,CAAC;AAQzC,MAAM,4BAA4B,GAAG;IACpC,aAAa,EAAE,IAAI;IACnB,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,CAAC;CACd,CAAC;AAEF,MAAM,iBAAiB,GAAG,IAAI,CAAC,CAAC,mEAAmE;AAEnG,MAAM,OAAgB,cAKpB,SAAQ,eAAoB;IAY7B,IAAW,gBAAgB;QAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAC/B,CAAC;IAOD;;;;;OAKG;IACH,YACC,UAAkB,EAClB,aAAuB,EACvB,gBAA4C;QAE5C,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,iBAAiB,GAAG,YAAY,CAAC;QAEtC,yGAAyG;QACzG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEhD,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC;YAAE,MAAM,IAAI,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAEtF,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,iBAAiB,mCAClB,4BAA4B,GAC5B,CAAC,gBAAgB,aAAhB,gBAAgB,cAAhB,gBAAgB,GAAI,EAAE,CAAC,CAC3B,CAAC;QAEF,IAAI,CAAC,qBAAqB,GAAG,IAAI,GAAG,EAA+C,CAAC;QACpF,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,EAA+C,CAAC;QAEjF,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,mBAAmB,GAAG,IAAI,mBAAmB,CACjD,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,iBAAiB,CAAC,aAAa,CACpC,CAAC;QACF,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;YACrC,IAAI,CAAC,YAAY,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAC7B,CAAC;IAES,KAAK;QACd,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,OAAO;QACb,IAAI,CAAC;YACJ,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,iBAAiB,GAAG,YAAY,CAAC;YACtC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC5B,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACZ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBAC1B,IAAI,CAAC,iBAAiB,GAAG,cAAc,CAAC;gBACxC,IAAI,CAAC,IAAK,CAAW,CAAC,OAAO,EAAE,CAAC;oBAC/B,MAAM,IAAI,eAAe,CACxB,6BAA6B,IAAI,CAAC,WAAW,aAC3C,CAAW,CAAC,OACd,EAAE,CACF,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACP,MAAM,IAAI,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAChD,CAAC;YACF,CAAC;iBAAM,CAAC;gBACP,YAAY,CAAC,GAAG,EAAE;oBACjB,IAAI,CAAC,UAAU,EAAE,CAAC;gBACnB,CAAC,CAAC,CAAC;YACJ,CAAC;QACF,CAAC;IACF,CAAC;IAeD,kDAAkD;IACxC,qBAAqB,CAAC,IAAY;QAC3C,OAAO,CAAC,CAAC,IAAI,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,kDAAkD;IAC3C,0BAA0B;QAChC,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;IACxC,CAAC;IAED;;;OAGG;IACH,kDAAkD;IAC3C,wBAAwB;QAC9B,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;IACrC,CAAC;IAED;;;OAGG;IACH,kDAAkD;IAC3C,qBAAqB;QAC3B,OAAO,IAAI,CAAC;IACb,CAAC;IA2BM,EAAE,CACR,IAA+B,EAC/B,QAG+B;QAE/B,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACvC,CAAC;IA8BM,IAAI,CACV,IAA+B,EAC/B,QAG+B;QAE/B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACzC,CAAC;IAiCM,cAAc,CACpB,IAA+B,EAC/B,QAG+B;QAE/B,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACnD,CAAC;IAES,aAAa,CAAC,IAAY,EAAE,IAAa;QAClD,IAAI,CAAC,iBAAiB,GAAG,cAAc,CAAC;QACxC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,IAAa,EAAE,IAAa;QAC7C,MAAM,cAAc,GAAG,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,iBAAiB,CAAC;QACjD,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,IAAI,CAAC,SAAS,EAAE,KAAK,cAAc,EAAE,CAAC;YACzC,IAAI,CAAC,sBAAsB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QACnD,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED;;;;;;OAMG;IACU,cAAc;6DAAC,IAAa,EAAE,IAAa,EAAE,eAAe,GAAG,KAAK,EAAE,EAAE,GAAG,IAAI;YAC3F,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,MAAM,UAAU,GAAG,GAAS,EAAE;gBAC7B,OAAA,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;oBACrB,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE;wBACjC,IAAI,eAAe,IAAI,YAAY,IAAI,CAAC,EAAE,CAAC;4BAC1C,IAAI,CAAC,WAAW,EAAE,CAAC;wBACpB,CAAC;wBACD,IACC,IAAI,CAAC,0BAA0B,EAAE,KAAK,CAAC;4BACvC,IAAI,CAAC,wBAAwB,EAAE,KAAK,CAAC,EACpC,CAAC;4BACF,aAAa,CAAC,QAAQ,CAAC,CAAC;4BACxB,OAAO,CAAC,IAAI,CAAC,CAAC;wBACf,CAAC;wBACD,YAAY,IAAI,CAAC,CAAC;oBACnB,CAAC,EAAE,EAAE,CAAC,CAAC;gBACR,CAAC,CAAC,CAAA;cAAA,CAAC;YAEJ,MAAM,UAAU,EAAE,CAAC;YACnB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7B,CAAC;KAAA;IAED;;;OAGG;IACI,kBAAkB,CAAC,IAAY;QACrC,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAES,QAAQ,CAAC,KAAiB;QACnC,8CAA8C;QAC9C,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,CAAC,UAAU,EAAE,CAAC;QACnB,CAAC;aAAM,CAAC;YACP,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACzC,CAAC;IACF,CAAC;IAED;;OAEG;IACI,KAAK;QACX,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAChC,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QAEnC,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC5B,CAAC;IAES,UAAU;QACnB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO;QACR,CAAC;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAC9B,CAAC,OAAyC,EAAE,GAAc,EAAE,EAAE;gBAC7D,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,kCAAkC,EAAE,CAAC,CAAC;gBACzE,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACrC,CAAC,CACD,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;YAClE,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAC;YAC7B,UAAU,CAAC,GAAG,EAAE;gBACf,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAC9B,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,qBAAqB;gBACrC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAC7B,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC;aAAM,CAAC;YACP,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAC5B,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,IAAI,CAAC,aAAa,CAAC,IAAI,CACtB,OAAO,EACP,IAAI,qCAAqC,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAC7E,CAAC;QACH,CAAC;IACF,CAAC;IAED;;OAEG;IACU,OAAO,CAGlB,OAAoC;;YACrC,IAAI,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBACvC,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC5C,CAAC;YACD,2CAA2C;YAC3C,IAAI,IAAI,CAAC,SAAS,EAAE,KAAK,cAAc,EAAE,CAAC;gBACzC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,CAAC;YAED,MAAM,SAAS,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC;gBAChD,CAAC,CAAE,OAA0C,CAAC,CAAC,CAAC,CAAC,EAAE;gBACnD,CAAC,CAAE,OAAqC,CAAC,EAAE,CAAC;YAE7C,IAAI,CAAC,SAAS,EAAE,CAAC;gBAChB,MAAM,IAAI,mBAAmB,CAAC,wBAAwB,CAAC,CAAC;YACzD,CAAC;YAED,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5C,MAAM,IAAI,uBAAuB,CAAC,SAAS,CAAC,CAAC;YAC9C,CAAC;YACD,MAAM,eAAe,GAAG,IAAI,mBAAmB,EAAyC,CAAC;YACzF,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBAC7B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YACH,MAAM,OAAO,GAA0E;gBACtF,OAAO,EAAE,OAAO;gBAChB,eAAe;aACf,CAAC;YAEF,IAAI,IAAI,CAAC,SAAS,EAAE,KAAK,YAAY,EAAE,CAAC;gBACvC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBAEnD,OAAO,OAAO,CAAC,eAAe,CAAC;YAChC,CAAC;YAED,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAEhD,IAAI,CAAC;gBACJ,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACrC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAChB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAE1C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACzC,CAAC;YAED,OAAO,eAAe,CAAC;QACxB,CAAC;KAAA;IAES,UAAU;QACnB,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;QACrC,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,KAAK,CAAC,UAAU,EAAE,CAAC;QACnB,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC7B,CAAC;IAEO,oBAAoB;QAC3B,KAAK,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,EAAE,CAAC;YAChE,IAAI,CAAC;gBACJ,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,OAAmC,CAAC,CAAC;gBAC9D,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACtC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YACxC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAChB,gCAAgC;gBAChC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACtC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACzC,CAAC;QACF,CAAC;IACF,CAAC;IAES,UAAU,CAAC,KAAmB;QACvC,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAC9C,IAAI,SAAS,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpD,OAAO;QACR,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YAClC,IACC,OAAO,CAAC,0BAA0B,CAAC,QAA+B,CAAC;gBAClE,QAAgC,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,EACjE,CAAC;gBACF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAC7C,OAAO;YACR,CAAC;YAED,MAAM,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC;gBAClD,CAAC,CAAE,QAA4C,CAAC,CAAC,CAAC,CAAC,EAAE;gBACrD,CAAC,CAAE,QAAiD,CAAC,EAAE,CAAC;YAEzD,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAE3D,IAAI,CAAC,WAAW,EAAE,CAAC;gBAClB,OAAO;YACR,CAAC;YAED,IACC,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC;gBACjC,OAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC;gBACtC,OAAO,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EACpC,CAAC;gBACF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAC7C,WAAW,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC/C,CAAC;YAED,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC;IACF,CAAC;IAEM,WAAW,CAAC,KAAuB;QACzC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAES,YAAY,CAAC,KAAuB;QAC7C,IAAI,IAAI,CAAC,qBAAqB,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CACjC,CAAC,OAAyC,EAAE,GAAc,EAAE,EAAE;gBAC7D,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC;gBAClE,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACxC,CAAC,CACD,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAC9B,CAAC,OAAyC,EAAE,GAAc,EAAE,EAAE;gBAC7D,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC;gBAClE,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACrC,CAAC,CACD,CAAC;QACH,CAAC;QAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAC/B,CAAC;CACD"}