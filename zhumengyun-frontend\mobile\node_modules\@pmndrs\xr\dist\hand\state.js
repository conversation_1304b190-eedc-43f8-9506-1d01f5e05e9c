import { getXRHandAssetPath } from './model.js';
import { createHandPoseState, updateXRHandPoseState } from './pose.js';
export function isXRHandInputSource(inputSource) {
    return inputSource.hand != null;
}
export function createXRHandState(id, inputSource, options, events, isPrimary) {
    return {
        id,
        isPrimary,
        type: 'hand',
        inputSource: inputSource,
        pose: createHandPoseState(inputSource.hand),
        assetPath: getXRHandAssetPath(inputSource.handedness, options),
        events,
    };
}
export function updateXRHandState({ inputSource, pose }, frame, manager) {
    updateXRHandPoseState(pose, frame, inputSource.hand, manager, inputSource.handedness);
}
