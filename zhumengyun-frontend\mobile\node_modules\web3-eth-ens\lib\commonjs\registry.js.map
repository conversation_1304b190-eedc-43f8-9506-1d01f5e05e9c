{"version": 3, "file": "registry.js", "sourceRoot": "", "sources": ["../../src/registry.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;;;;;;;;;;;AAGF,yDAA6C;AAE7C,6DAA0D;AAC1D,mEAAgE;AAChE,2CAAgD;AAChD,yCAAsC;AAEtC,MAAa,QAAQ;IAIpB,YAAmB,OAA0B,EAAE,qBAA+B;QAC7E,IAAI,CAAC,QAAQ,GAAG,IAAI,4BAAQ,CAC3B,+BAAc,EACd,qBAAqB,aAArB,qBAAqB,cAArB,qBAAqB,GAAI,6BAAiB,CAAC,IAAI,EAC/C,OAAO,CACP,CAAC;QAEF,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACxB,CAAC;IAEY,QAAQ,CAAC,IAAY;;YACjC,IAAI;gBACH,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,IAAA,mBAAQ,EAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAElE,OAAO,MAAM,CAAC;aACd;YAAC,OAAO,KAAK,EAAE;gBACf,MAAM,IAAI,KAAK,EAAE,CAAC,CAAC,sGAAsG;aACzH;QACF,CAAC;KAAA;IAEY,MAAM,CAAC,IAAY;;YAC/B,IAAI;gBACH,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAA,mBAAQ,EAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;aACxD;YAAC,OAAO,KAAK,EAAE;gBACf,MAAM,IAAI,KAAK,EAAE,CAAC,CAAC,sGAAsG;aACzH;QACF,CAAC;KAAA;IAEY,YAAY,CAAC,IAAY;;YACrC,IAAI;gBACH,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,IAAA,mBAAQ,EAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAE1E,OAAO,OAAO,CAAC;aACf;YAAC,OAAO,KAAK,EAAE;gBACf,MAAM,IAAI,KAAK,EAAE,CAAC,CAAC,sGAAsG;aACzH;QACF,CAAC;KAAA;IAEY,WAAW,CAAC,IAAY;;YACpC,IAAI;gBACH,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO;qBAC1B,QAAQ,CAAC,IAAA,mBAAQ,EAAC,IAAI,CAAC,CAAC;qBACxB,IAAI,EAAE;qBACN,IAAI,CAAC,OAAO,CAAC,EAAE;oBACf,wCAAwC;oBACxC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;wBAChC,MAAM,QAAQ,GAAG,IAAI,4BAAQ,CAAC,qCAAiB,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;wBACxE,0EAA0E;wBAC1E,OAAO,QAAQ,CAAC;qBAChB;oBACD,MAAM,IAAI,KAAK,EAAE,CAAC;gBACnB,CAAC,CAAC,CAAC;aACJ;YAAC,OAAO,KAAK,EAAE;gBACf,MAAM,IAAI,KAAK,EAAE,CAAC,CAAC,sGAAsG;aACzH;QACF,CAAC;KAAA;IAED,IAAW,MAAM;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;IAC7B,CAAC;CACD;AAhED,4BAgEC"}