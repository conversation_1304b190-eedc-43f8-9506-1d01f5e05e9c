import { forwardHtmlEvents } from '@pmndrs/pointer-events';
import { createXRStore as createXRStoreImpl } from '../store.js';
import { setupSyncXRElements } from './elements.js';
export function createXRStore(canvas, scene, getCamera, xr, options) {
    const htmlEventForward = options?.htmlInput === false ? undefined : forwardHtmlEvents(canvas, getCamera, scene, options?.htmlInput);
    const updatesList = [];
    const store = createXRStoreImpl(options);
    store.setWebXRManager(xr);
    let cleanupSyncElements;
    const unsubscribeOrigin = store.subscribe((state, prevState) => {
        if (state.origin === prevState.origin) {
            return;
        }
        cleanupSyncElements?.();
        cleanupSyncElements =
            state.origin != null ? setupSyncXRElements(scene, getCamera, store, state.origin, updatesList) : undefined;
    });
    return Object.assign(store, {
        destroy() {
            unsubscribeOrigin();
            htmlEventForward?.destroy();
            cleanupSyncElements?.();
            store.destroy();
        },
        update(frame, delta) {
            htmlEventForward?.update();
            if (frame == null) {
                return;
            }
            store.onBeforeFrame(scene, getCamera(), frame);
            const length = updatesList.length;
            for (let i = 0; i < length; i++) {
                updatesList[i](frame, delta);
            }
            store.onBeforeRender();
        },
    });
}
