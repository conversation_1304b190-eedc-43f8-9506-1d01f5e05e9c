/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { Gamepad } from '../gamepad/Gamepad.js';
import { XRSpace } from '../spaces/XRSpace.js';
import { P_CONTROLLER, P_GAMEPAD, P_TRACKED_INPUT } from '../private.js';
import { XRInputSource, XRTargetRayMode, } from '../input/XRInputSource.js';
import { XRTrackedInput } from './XRTrackedInput.js';
export class XRController extends XRTrackedInput {
    constructor(controllerConfig, handedness, globalSpace) {
        if (!controllerConfig.layout[handedness]) {
            throw new DOMException('Handedness not supported', 'InvalidStateError');
        }
        const targetRaySpace = new XRSpace(globalSpace);
        const gripSpace = controllerConfig.layout[handedness].gripOffsetMatrix
            ? new XRSpace(targetRaySpace, controllerConfig.layout[handedness].gripOffsetMatrix)
            : undefined;
        const profiles = [
            controllerConfig.profileId,
            ...controllerConfig.fallbackProfileIds,
        ];
        const inputSource = new XRInputSource(handedness, XRTargetRayMode.TrackedPointer, profiles, targetRaySpace, new Gamepad(controllerConfig.layout[handedness].gamepad), gripSpace);
        super(inputSource);
        this[P_CONTROLLER] = {
            profileId: controllerConfig.profileId,
            gamepadConfig: controllerConfig.layout[handedness].gamepad,
        };
    }
    get gamepadConfig() {
        return this[P_CONTROLLER].gamepadConfig;
    }
    get profileId() {
        return this[P_CONTROLLER].profileId;
    }
    updateButtonValue(id, value) {
        if (value > 1 || value < 0) {
            console.warn(`Out-of-range value ${value} provided for button ${id}.`);
            return;
        }
        const gamepadButton = this[P_TRACKED_INPUT].inputSource.gamepad[P_GAMEPAD].buttonsMap[id];
        if (gamepadButton) {
            if (gamepadButton[P_GAMEPAD].type === 'binary' &&
                value != 1 &&
                value != 0) {
                console.warn(`Non-binary value ${value} provided for binary button ${id}.`);
                return;
            }
            gamepadButton[P_GAMEPAD].pendingValue = value;
        }
        else {
            console.warn(`Current controller does not have button ${id}.`);
        }
    }
    updateButtonTouch(id, touched) {
        const gamepadButton = this[P_TRACKED_INPUT].inputSource.gamepad[P_GAMEPAD].buttonsMap[id];
        if (gamepadButton) {
            gamepadButton[P_GAMEPAD].touched = touched;
        }
        else {
            console.warn(`Current controller does not have button ${id}.`);
        }
    }
    updateAxis(id, type, value) {
        if (value > 1 || value < -1) {
            console.warn(`Out-of-range value ${value} provided for ${id} axes.`);
            return;
        }
        const axesById = this[P_TRACKED_INPUT].inputSource.gamepad[P_GAMEPAD].axesMap[id];
        if (axesById) {
            if (type === 'x-axis') {
                axesById.x = value;
            }
            else if (type === 'y-axis') {
                axesById.y = value;
            }
        }
        else {
            console.warn(`Current controller does not have ${id} axes.`);
        }
    }
    updateAxes(id, x, y) {
        if (x > 1 || x < -1 || y > 1 || y < -1) {
            console.warn(`Out-of-range value x:${x}, y:${y} provided for ${id} axes.`);
            return;
        }
        const axesById = this[P_TRACKED_INPUT].inputSource.gamepad[P_GAMEPAD].axesMap[id];
        if (axesById) {
            axesById.x = x;
            axesById.y = y;
        }
        else {
            console.warn(`Current controller does not have ${id} axes.`);
        }
    }
}
//# sourceMappingURL=XRController.js.map