{"name": "react-native", "version": "0.80.1", "description": "A framework for building native apps using React", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/facebook/react-native.git", "directory": "packages/react-native"}, "homepage": "https://reactnative.dev/", "keywords": ["react", "react-native", "android", "ios", "mobile", "cross-platform", "app-framework", "mobile-development"], "bugs": "https://github.com/facebook/react-native/issues", "engines": {"node": ">=18"}, "bin": {"react-native": "cli.js"}, "main": "./index.js", "types": "types", "exports": {".": {"react-native-strict-api": "./types_generated/index.d.ts", "react-native-strict-api-UNSAFE-ALLOW-SUBPATHS": "./types_generated/index.d.ts", "types": "./types/index.d.ts", "default": "./index.js"}, "./*": {"react-native-strict-api": null, "react-native-strict-api-UNSAFE-ALLOW-SUBPATHS": "./types_generated/*.d.ts", "types": "./*.d.ts", "default": "./*.js"}, "./*.js": {"react-native-strict-api": null, "default": "./*.js"}, "./Libraries/*.d.ts": {"react-native-strict-api": null, "default": "./Libraries/*.d.ts"}, "./scripts/*": "./scripts/*", "./src/*": {"react-native-strict-api": null, "react-native-strict-api-UNSAFE-ALLOW-SUBPATHS": "./types_generated/src/*.d.ts", "default": "./src/*.js"}, "./types/*.d.ts": {"react-native-strict-api": null, "default": "./types/*.d.ts"}, "./gradle/*": null, "./React/*": null, "./ReactAndroid/*": null, "./ReactApple/*": null, "./ReactCommon/*": null, "./sdks/*": null, "./src/fb_internal/*": "./src/fb_internal/*", "./third-party-podspecs/*": null, "./types/*": null, "./types_generated/*": null, "./package.json": "./package.json"}, "jest-junit": {"outputDirectory": "reports/junit", "outputName": "js-test-results.xml"}, "files": ["build.gradle.kts", "cli.js", "flow", "gradle.properties", "gradle/libs.versions.toml", "index.js", "index.js.flow", "interface.js", "jest-preset.js", "jest", "Libraries", "LICENSE", "React-Core.podspec", "react-native.config.js", "React.podspec", "React", "!React/Fabric/RCTThirdPartyFabricComponentsProvider.*", "ReactAndroid", "!ReactAndroid/.cxx", "!ReactAndroid/build", "!ReactAndroid/external-artifacts/artifacts", "!ReactAndroid/external-artifacts/build", "!ReactAndroid/hermes-engine/.cxx", "!ReactAndroid/hermes-engine/build", "!ReactAndroid/src/main/third-party", "!ReactAndroid/src/test", "ReactApple", "ReactCommon", "README.md", "rn-get-polyfills.js", "scripts/bundle.js", "scripts/cocoapods", "scripts/codegen", "scripts/compose-source-maps.js", "scripts/find-node-for-xcode.sh", "scripts/generate-codegen-artifacts.js", "scripts/generate-provider-cli.js", "scripts/generate-specs-cli.js", "scripts/hermes/hermes-utils.js", "scripts/hermes/prepare-hermes-for-build.js", "scripts/ios-configure-glog.sh", "scripts/native_modules.rb", "scripts/node-binary.sh", "scripts/packager-reporter.js", "scripts/packager.sh", "scripts/react_native_pods_utils/script_phases.rb", "scripts/react_native_pods_utils/script_phases.sh", "scripts/react_native_pods.rb", "scripts/react-native-xcode.sh", "scripts/xcode/ccache-clang.sh", "scripts/xcode/ccache-clang++.sh", "scripts/xcode/ccache.conf", "scripts/xcode/with-environment.sh", "sdks/.hermesversion", "sdks/hermes-engine", "sdks/hermesc", "settings.gradle.kts", "src", "!src/private/testing", "third-party-podspecs", "types", "types_generated", "!**/__docs__/**", "!**/__fixtures__/**", "!**/__flowtests__/**", "!**/__mocks__/**", "!**/__tests__/**", "!**/__typetests__/**"], "scripts": {"prepack": "node ./scripts/prepack.js", "featureflags": "node ./scripts/featureflags/index.js"}, "peerDependencies": {"@types/react": "^19.1.0", "react": "^19.1.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "dependencies": {"@jest/create-cache-key-function": "^29.7.0", "@react-native/assets-registry": "0.80.1", "@react-native/codegen": "0.80.1", "@react-native/community-cli-plugin": "0.80.1", "@react-native/gradle-plugin": "0.80.1", "@react-native/js-polyfills": "0.80.1", "@react-native/normalize-colors": "0.80.1", "@react-native/virtualized-lists": "0.80.1", "abort-controller": "^3.0.0", "anser": "^1.4.9", "ansi-regex": "^5.0.0", "babel-jest": "^29.7.0", "babel-plugin-syntax-hermes-parser": "0.28.1", "base64-js": "^1.5.1", "chalk": "^4.0.0", "commander": "^12.0.0", "flow-enums-runtime": "^0.0.6", "glob": "^7.1.1", "invariant": "^2.2.4", "jest-environment-node": "^29.7.0", "memoize-one": "^5.0.0", "metro-runtime": "^0.82.2", "metro-source-map": "^0.82.2", "nullthrows": "^1.1.1", "pretty-format": "^29.7.0", "promise": "^8.3.0", "react-devtools-core": "^6.1.1", "react-refresh": "^0.14.0", "regenerator-runtime": "^0.13.2", "scheduler": "0.26.0", "semver": "^7.1.3", "stacktrace-parser": "^0.1.10", "whatwg-fetch": "^3.0.0", "ws": "^6.2.3", "yargs": "^17.6.2"}, "codegenConfig": {"libraries": [{"name": "FBReactNativeSpec", "type": "modules", "ios": {"modules": {"AccessibilityManager": {"unstableRequiresMainQueueSetup": true}, "Appearance": {"unstableRequiresMainQueueSetup": true}, "AppState": {"unstableRequiresMainQueueSetup": true}, "DeviceInfo": {"unstableRequiresMainQueueSetup": true}, "PlatformConstants": {"unstableRequiresMainQueueSetup": true}, "StatusBarManager": {"unstableRequiresMainQueueSetup": true}}}, "android": {}, "jsSrcsDir": "src"}, {"name": "rncore", "type": "components", "ios": {}, "android": {}, "jsSrcsDir": "src"}]}}