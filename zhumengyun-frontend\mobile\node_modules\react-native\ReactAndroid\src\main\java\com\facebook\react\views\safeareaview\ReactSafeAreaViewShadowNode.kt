/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.facebook.react.views.safeareaview

import com.facebook.react.common.annotations.internal.LegacyArchitecture
import com.facebook.react.uimanager.LayoutShadowNode

@LegacyArchitecture internal class ReactSafeAreaViewShadowNode : LayoutShadowNode() {}
