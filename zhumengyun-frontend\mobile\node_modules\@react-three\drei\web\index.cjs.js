"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("./Html.cjs.js"),r=require("./CycleRaycast.cjs.js"),s=require("./useCursor.cjs.js"),o=require("./Loader.cjs.js"),t=require("./ScrollControls.cjs.js"),i=require("./PresentationControls.cjs.js"),a=require("./KeyboardControls.cjs.js"),c=require("./Select.cjs.js"),u=require("./View.cjs.js"),n=require("./pivotControls/index.cjs.js"),p=require("./ScreenVideoTexture.cjs.js"),j=require("./WebcamVideoTexture.cjs.js"),l=require("./FaceControls.cjs.js"),x=require("./DragControls.cjs.js"),d=require("./FaceLandmarker.cjs.js"),q=require("./Facemesh.cjs.js"),m=require("../core/Billboard.cjs.js"),C=require("../core/ScreenSpace.cjs.js"),h=require("../core/ScreenSizer.cjs.js"),S=require("../core/QuadraticBezierLine.cjs.js"),M=require("../core/CubicBezierLine.cjs.js"),T=require("../core/CatmullRomLine.cjs.js"),b=require("../core/Line.cjs.js"),P=require("../core/PositionalAudio.cjs.js"),g=require("../core/Text.cjs.js"),f=require("../core/Text3D.cjs.js"),v=require("../core/Effects.cjs.js"),F=require("../core/GradientTexture.cjs.js"),B=require("../core/Image.cjs.js"),A=require("../core/Edges.cjs.js"),L=require("../core/Outlines.cjs.js"),D=require("../core/Trail.cjs.js"),R=require("../core/Sampler.cjs.js"),k=require("../core/ComputedAttribute.cjs.js"),E=require("../core/Clone.cjs.js"),G=require("../core/MarchingCubes.cjs.js"),y=require("../core/Decal.cjs.js"),w=require("../core/Svg.cjs.js"),z=require("../core/Gltf.cjs.js"),I=require("../core/AsciiRenderer.cjs.js"),V=require("../core/Splat.cjs.js"),O=require("../core/OrthographicCamera.cjs.js"),H=require("../core/PerspectiveCamera.cjs.js"),K=require("../core/CubeCamera.cjs.js"),W=require("../core/DeviceOrientationControls.cjs.js"),Q=require("../core/FlyControls.cjs.js"),N=require("../core/MapControls.cjs.js"),U=require("../core/OrbitControls.cjs.js"),X=require("../core/TrackballControls.cjs.js"),_=require("../core/ArcballControls.cjs.js"),J=require("../core/TransformControls.cjs.js"),Y=require("../core/PointerLockControls.cjs.js"),Z=require("../core/FirstPersonControls.cjs.js"),$=require("../core/CameraControls.cjs.js"),ee=require("../core/MotionPathControls.cjs.js"),re=require("../core/GizmoHelper.cjs.js"),se=require("../core/GizmoViewcube.cjs.js"),oe=require("../core/GizmoViewport.cjs.js"),te=require("../core/Grid.cjs.js"),ie=require("../core/CubeTexture.cjs.js"),ae=require("../core/Fbx.cjs.js"),ce=require("../core/Ktx2.cjs.js"),ue=require("../core/Progress.cjs.js"),ne=require("../core/Texture.cjs.js"),pe=require("../core/VideoTexture.cjs.js"),je=require("../core/useFont.cjs.js"),le=require("../core/useSpriteLoader.cjs.js"),xe=require("../core/Helper.cjs.js"),de=require("../core/Stats.cjs.js"),qe=require("../core/StatsGl.cjs.js"),me=require("../core/useDepthBuffer.cjs.js"),Ce=require("../core/useAspect.cjs.js"),he=require("../core/useCamera.cjs.js"),Se=require("../core/DetectGPU.cjs.js"),Me=require("../core/Bvh.cjs.js"),Te=require("../core/useContextBridge.cjs.js"),be=require("../core/useAnimations.cjs.js"),Pe=require("../core/Fbo.cjs.js"),ge=require("../core/useIntersect.cjs.js"),fe=require("../core/useBoxProjectedEnv.cjs.js"),ve=require("../core/BBAnchor.cjs.js"),Fe=require("../core/TrailTexture.cjs.js"),Be=require("../core/Example.cjs.js"),Ae=require("../core/SpriteAnimator.cjs.js"),Le=require("../core/CurveModifier.cjs.js"),De=require("../core/MeshDistortMaterial.cjs.js"),Re=require("../core/MeshWobbleMaterial.cjs.js"),ke=require("../core/MeshReflectorMaterial.cjs.js"),Ee=require("../core/MeshRefractionMaterial.cjs.js"),Ge=require("../core/MeshTransmissionMaterial.cjs.js"),ye=require("../core/MeshDiscardMaterial.cjs.js"),we=require("../core/MultiMaterial.cjs.js"),ze=require("../core/PointMaterial.cjs.js"),Ie=require("../core/shaderMaterial.cjs.js"),Ve=require("../core/softShadows.cjs.js"),Oe=require("../core/shapes.cjs.js"),He=require("../core/RoundedBox.cjs.js"),Ke=require("../core/ScreenQuad.cjs.js"),We=require("../core/Center.cjs.js"),Qe=require("../core/Resize.cjs.js"),Ne=require("../core/Bounds.cjs.js"),Ue=require("../core/CameraShake.cjs.js"),Xe=require("../core/Float.cjs.js"),_e=require("../core/Stage.cjs.js"),Je=require("../core/Backdrop.cjs.js"),Ye=require("../core/Shadow.cjs.js"),Ze=require("../core/Caustics.cjs.js"),$e=require("../core/ContactShadows.cjs.js"),er=require("../core/AccumulativeShadows.cjs.js"),rr=require("../core/Reflector.cjs.js"),sr=require("../core/SpotLight.cjs.js"),or=require("../core/Environment.cjs.js"),tr=require("../core/Lightformer.cjs.js"),ir=require("../core/Sky.cjs.js"),ar=require("../core/Stars.cjs.js"),cr=require("../core/Cloud.cjs.js"),ur=require("../core/Sparkles.cjs.js"),nr=require("../core/useEnvironment.cjs.js"),pr=require("../core/MatcapTexture.cjs.js"),jr=require("../core/NormalTexture.cjs.js"),lr=require("../core/Wireframe.cjs.js"),xr=require("../core/ShadowAlpha.cjs.js"),dr=require("../core/Points.cjs.js"),qr=require("../core/Instances.cjs.js"),mr=require("../core/Segments.cjs.js"),Cr=require("../core/Detailed.cjs.js"),hr=require("../core/Preload.cjs.js"),Sr=require("../core/BakeShadows.cjs.js"),Mr=require("../core/meshBounds.cjs.js"),Tr=require("../core/AdaptiveDpr.cjs.js"),br=require("../core/AdaptiveEvents.cjs.js"),Pr=require("../core/PerformanceMonitor.cjs.js"),gr=require("../core/RenderTexture.cjs.js"),fr=require("../core/RenderCubeTexture.cjs.js"),vr=require("../core/Mask.cjs.js"),Fr=require("../core/Hud.cjs.js"),Br=require("../core/Fisheye.cjs.js"),Ar=require("../core/MeshPortalMaterial.cjs.js"),Lr=require("../core/calculateScaleFactor.cjs.js");require("@babel/runtime/helpers/extends"),require("react"),require("react-dom/client"),require("three"),require("@react-three/fiber"),require("zustand"),require("maath"),require("@react-spring/three"),require("@use-gesture/react"),require("zustand/middleware"),require("three-stdlib"),require("zustand/shallow"),require("tunnel-rat"),require("./pivotControls/AxisArrow.cjs.js"),require("./pivotControls/context.cjs.js"),require("./pivotControls/AxisRotator.cjs.js"),require("./pivotControls/PlaneSlider.cjs.js"),require("./pivotControls/ScalingSphere.cjs.js"),require("suspend-react"),require("hls.js"),require("troika-three-text"),require("../helpers/constants.cjs.js"),require("meshline"),require("camera-controls"),require("stats.js"),require("../helpers/useEffectfulState.cjs.js"),require("stats-gl"),require("detect-gpu"),require("three-mesh-bvh"),require("react-composer"),require("../helpers/deprecated.cjs.js"),require("../materials/BlurPass.cjs.js"),require("../materials/ConvolutionMaterial.cjs.js"),require("../materials/MeshReflectorMaterial.cjs.js"),require("../materials/MeshRefractionMaterial.cjs.js"),require("../materials/DiscardMaterial.cjs.js"),require("@monogrid/gainmap-js"),require("../helpers/environment-assets.cjs.js"),require("../materials/SpotLightMaterial.cjs.js"),require("../materials/WireframeMaterial.cjs.js"),exports.Html=e.Html,exports.CycleRaycast=r.CycleRaycast,exports.useCursor=s.useCursor,exports.Loader=o.Loader,exports.Scroll=t.Scroll,exports.ScrollControls=t.ScrollControls,exports.useScroll=t.useScroll,exports.PresentationControls=i.PresentationControls,exports.KeyboardControls=a.KeyboardControls,exports.useKeyboardControls=a.useKeyboardControls,exports.Select=c.Select,exports.useSelect=c.useSelect,exports.View=u.View,exports.PivotControls=n.PivotControls,exports.ScreenVideoTexture=p.ScreenVideoTexture,exports.WebcamVideoTexture=j.WebcamVideoTexture,exports.FaceControls=l.FaceControls,exports.useFaceControls=l.useFaceControls,exports.DragControls=x.DragControls,exports.FaceLandmarker=d.FaceLandmarker,exports.FaceLandmarkerDefaults=d.FaceLandmarkerDefaults,exports.useFaceLandmarker=d.useFaceLandmarker,exports.Facemesh=q.Facemesh,exports.FacemeshDatas=q.FacemeshDatas,exports.FacemeshEye=q.FacemeshEye,exports.FacemeshEyeDefaults=q.FacemeshEyeDefaults,exports.Billboard=m.Billboard,exports.ScreenSpace=C.ScreenSpace,exports.ScreenSizer=h.ScreenSizer,exports.QuadraticBezierLine=S.QuadraticBezierLine,exports.CubicBezierLine=M.CubicBezierLine,exports.CatmullRomLine=T.CatmullRomLine,exports.Line=b.Line,exports.PositionalAudio=P.PositionalAudio,exports.Text=g.Text,exports.Text3D=f.Text3D,exports.Effects=v.Effects,exports.isWebGL2Available=v.isWebGL2Available,exports.GradientTexture=F.GradientTexture,exports.GradientType=F.GradientType,exports.Image=B.Image,exports.Edges=A.Edges,exports.Outlines=L.Outlines,exports.Trail=D.Trail,exports.useTrail=D.useTrail,exports.Sampler=R.Sampler,exports.useSurfaceSampler=R.useSurfaceSampler,exports.ComputedAttribute=k.ComputedAttribute,exports.Clone=E.Clone,exports.MarchingCube=G.MarchingCube,exports.MarchingCubes=G.MarchingCubes,exports.MarchingPlane=G.MarchingPlane,exports.Decal=y.Decal,exports.Svg=w.Svg,exports.Gltf=z.Gltf,exports.useGLTF=z.useGLTF,exports.AsciiRenderer=I.AsciiRenderer,exports.Splat=V.Splat,exports.OrthographicCamera=O.OrthographicCamera,exports.PerspectiveCamera=H.PerspectiveCamera,exports.CubeCamera=K.CubeCamera,exports.useCubeCamera=K.useCubeCamera,exports.DeviceOrientationControls=W.DeviceOrientationControls,exports.FlyControls=Q.FlyControls,exports.MapControls=N.MapControls,exports.OrbitControls=U.OrbitControls,exports.TrackballControls=X.TrackballControls,exports.ArcballControls=_.ArcballControls,exports.TransformControls=J.TransformControls,exports.PointerLockControls=Y.PointerLockControls,exports.FirstPersonControls=Z.FirstPersonControls,exports.CameraControls=$.CameraControls,exports.MotionPathControls=ee.MotionPathControls,exports.useMotion=ee.useMotion,exports.GizmoHelper=re.GizmoHelper,exports.useGizmoContext=re.useGizmoContext,exports.GizmoViewcube=se.GizmoViewcube,exports.GizmoViewport=oe.GizmoViewport,exports.Grid=te.Grid,exports.CubeTexture=ie.CubeTexture,exports.useCubeTexture=ie.useCubeTexture,exports.Fbx=ae.Fbx,exports.useFBX=ae.useFBX,exports.Ktx2=ce.Ktx2,exports.useKTX2=ce.useKTX2,exports.Progress=ue.Progress,exports.useProgress=ue.useProgress,exports.IsObject=ne.IsObject,exports.Texture=ne.Texture,exports.useTexture=ne.useTexture,exports.VideoTexture=pe.VideoTexture,exports.useVideoTexture=pe.useVideoTexture,exports.useFont=je.useFont,exports.checkIfFrameIsEmpty=le.checkIfFrameIsEmpty,exports.getFirstFrame=le.getFirstFrame,exports.useSpriteLoader=le.useSpriteLoader,exports.Helper=xe.Helper,exports.useHelper=xe.useHelper,exports.Stats=de.Stats,exports.StatsGl=qe.StatsGl,exports.useDepthBuffer=me.useDepthBuffer,exports.useAspect=Ce.useAspect,exports.useCamera=he.useCamera,exports.DetectGPU=Se.DetectGPU,exports.useDetectGPU=Se.useDetectGPU,exports.Bvh=Me.Bvh,exports.useBVH=Me.useBVH,exports.useContextBridge=Te.useContextBridge,exports.useAnimations=be.useAnimations,exports.Fbo=Pe.Fbo,exports.useFBO=Pe.useFBO,exports.useIntersect=ge.useIntersect,exports.useBoxProjectedEnv=fe.useBoxProjectedEnv,exports.BBAnchor=ve.BBAnchor,exports.TrailTexture=Fe.TrailTexture,exports.useTrailTexture=Fe.useTrailTexture,exports.Example=Be.Example,exports.SpriteAnimator=Ae.SpriteAnimator,exports.useSpriteAnimator=Ae.useSpriteAnimator,exports.CurveModifier=Le.CurveModifier,exports.MeshDistortMaterial=De.MeshDistortMaterial,exports.MeshWobbleMaterial=Re.MeshWobbleMaterial,exports.MeshReflectorMaterial=ke.MeshReflectorMaterial,exports.MeshRefractionMaterial=Ee.MeshRefractionMaterial,exports.MeshTransmissionMaterial=Ge.MeshTransmissionMaterial,exports.MeshDiscardMaterial=ye.MeshDiscardMaterial,exports.MultiMaterial=we.MultiMaterial,exports.PointMaterial=ze.PointMaterial,exports.PointMaterialImpl=ze.PointMaterialImpl,exports.shaderMaterial=Ie.shaderMaterial,exports.SoftShadows=Ve.SoftShadows,exports.Box=Oe.Box,exports.Capsule=Oe.Capsule,exports.Circle=Oe.Circle,exports.Cone=Oe.Cone,exports.Cylinder=Oe.Cylinder,exports.Dodecahedron=Oe.Dodecahedron,exports.Extrude=Oe.Extrude,exports.Icosahedron=Oe.Icosahedron,exports.Lathe=Oe.Lathe,exports.Octahedron=Oe.Octahedron,exports.Plane=Oe.Plane,exports.Polyhedron=Oe.Polyhedron,exports.Ring=Oe.Ring,exports.Shape=Oe.Shape,exports.Sphere=Oe.Sphere,exports.Tetrahedron=Oe.Tetrahedron,exports.Torus=Oe.Torus,exports.TorusKnot=Oe.TorusKnot,exports.Tube=Oe.Tube,exports.RoundedBox=He.RoundedBox,exports.ScreenQuad=Ke.ScreenQuad,exports.Center=We.Center,exports.Resize=Qe.Resize,exports.Bounds=Ne.Bounds,exports.useBounds=Ne.useBounds,exports.CameraShake=Ue.CameraShake,exports.Float=Xe.Float,exports.Stage=_e.Stage,exports.Backdrop=Je.Backdrop,exports.Shadow=Ye.Shadow,exports.Caustics=Ze.Caustics,exports.ContactShadows=$e.ContactShadows,exports.AccumulativeShadows=er.AccumulativeShadows,exports.RandomizedLight=er.RandomizedLight,exports.accumulativeContext=er.accumulativeContext,exports.Reflector=rr.Reflector,exports.SpotLight=sr.SpotLight,exports.SpotLightShadow=sr.SpotLightShadow,exports.Environment=or.Environment,exports.EnvironmentCube=or.EnvironmentCube,exports.EnvironmentMap=or.EnvironmentMap,exports.EnvironmentPortal=or.EnvironmentPortal,exports.Lightformer=tr.Lightformer,exports.Sky=ir.Sky,exports.calcPosFromAngles=ir.calcPosFromAngles,exports.Stars=ar.Stars,exports.Cloud=cr.Cloud,exports.CloudInstance=cr.CloudInstance,exports.Clouds=cr.Clouds,exports.Sparkles=ur.Sparkles,exports.useEnvironment=nr.useEnvironment,exports.MatcapTexture=pr.MatcapTexture,exports.useMatcapTexture=pr.useMatcapTexture,exports.NormalTexture=jr.NormalTexture,exports.useNormalTexture=jr.useNormalTexture,exports.Wireframe=lr.Wireframe,exports.ShadowAlpha=xr.ShadowAlpha,exports.Point=dr.Point,exports.Points=dr.Points,exports.PointsBuffer=dr.PointsBuffer,exports.PositionPoint=dr.PositionPoint,exports.Instance=qr.Instance,exports.InstancedAttribute=qr.InstancedAttribute,exports.Instances=qr.Instances,exports.Merged=qr.Merged,exports.PositionMesh=qr.PositionMesh,exports.createInstances=qr.createInstances,exports.Segment=mr.Segment,exports.SegmentObject=mr.SegmentObject,exports.Segments=mr.Segments,exports.Detailed=Cr.Detailed,exports.Preload=hr.Preload,exports.BakeShadows=Sr.BakeShadows,exports.meshBounds=Mr.meshBounds,exports.AdaptiveDpr=Tr.AdaptiveDpr,exports.AdaptiveEvents=br.AdaptiveEvents,exports.PerformanceMonitor=Pr.PerformanceMonitor,exports.usePerformanceMonitor=Pr.usePerformanceMonitor,exports.RenderTexture=gr.RenderTexture,exports.RenderCubeTexture=fr.RenderCubeTexture,exports.Mask=vr.Mask,exports.useMask=vr.useMask,exports.Hud=Fr.Hud,exports.Fisheye=Br.Fisheye,exports.MeshPortalMaterial=Ar.MeshPortalMaterial,exports.calculateScaleFactor=Lr.calculateScaleFactor;
