{"version": 3, "file": "HTMLMesh.js", "sources": ["../../src/interactive/HTMLMesh.js"], "sourcesContent": ["import { CanvasTexture, LinearFilter, Mesh, MeshBasicMaterial, PlaneGeometry, Color } from 'three'\n\nclass HTMLMesh extends Mesh {\n  constructor(dom) {\n    const texture = new HTMLTexture(dom)\n\n    const geometry = new PlaneGeometry(texture.image.width * 0.001, texture.image.height * 0.001)\n    const material = new MeshBasicMaterial({ map: texture, toneMapped: false, transparent: true })\n\n    super(geometry, material)\n\n    function onEvent(event) {\n      material.map.dispatchDOMEvent(event)\n    }\n\n    this.addEventListener('mousedown', onEvent)\n    this.addEventListener('mousemove', onEvent)\n    this.addEventListener('mouseup', onEvent)\n    this.addEventListener('click', onEvent)\n\n    this.dispose = function () {\n      geometry.dispose()\n      material.dispose()\n\n      material.map.dispose()\n\n      canvases.delete(dom)\n\n      this.removeEventListener('mousedown', onEvent)\n      this.removeEventListener('mousemove', onEvent)\n      this.removeEventListener('mouseup', onEvent)\n      this.removeEventListener('click', onEvent)\n    }\n  }\n}\n\nclass HTMLTexture extends CanvasTexture {\n  constructor(dom) {\n    super(html2canvas(dom))\n\n    this.dom = dom\n\n    this.anisotropy = 16\n    if ('colorSpace' in this) this.colorSpace = 'srgb'\n    else this.encoding = 3001 // sRGBEncoding\n    this.minFilter = LinearFilter\n    this.magFilter = LinearFilter\n\n    // Create an observer on the DOM, and run html2canvas update in the next loop\n    const observer = new MutationObserver(() => {\n      if (!this.scheduleUpdate) {\n        // ideally should use xr.requestAnimationFrame, here setTimeout to avoid passing the renderer\n        this.scheduleUpdate = setTimeout(() => this.update(), 16)\n      }\n    })\n\n    const config = { attributes: true, childList: true, subtree: true, characterData: true }\n    observer.observe(dom, config)\n\n    this.observer = observer\n  }\n\n  dispatchDOMEvent(event) {\n    if (event.data) {\n      htmlevent(this.dom, event.type, event.data.x, event.data.y)\n    }\n  }\n\n  update() {\n    this.image = html2canvas(this.dom)\n    this.needsUpdate = true\n\n    this.scheduleUpdate = null\n  }\n\n  dispose() {\n    if (this.observer) {\n      this.observer.disconnect()\n    }\n\n    this.scheduleUpdate = clearTimeout(this.scheduleUpdate)\n\n    super.dispose()\n  }\n}\n\n//\n\nconst canvases = new WeakMap()\n\nfunction html2canvas(element) {\n  const range = document.createRange()\n  const color = new Color()\n\n  function Clipper(context) {\n    const clips = []\n    let isClipping = false\n\n    function doClip() {\n      if (isClipping) {\n        isClipping = false\n        context.restore()\n      }\n\n      if (clips.length === 0) return\n\n      let minX = -Infinity,\n        minY = -Infinity\n      let maxX = Infinity,\n        maxY = Infinity\n\n      for (let i = 0; i < clips.length; i++) {\n        const clip = clips[i]\n\n        minX = Math.max(minX, clip.x)\n        minY = Math.max(minY, clip.y)\n        maxX = Math.min(maxX, clip.x + clip.width)\n        maxY = Math.min(maxY, clip.y + clip.height)\n      }\n\n      context.save()\n      context.beginPath()\n      context.rect(minX, minY, maxX - minX, maxY - minY)\n      context.clip()\n\n      isClipping = true\n    }\n\n    return {\n      add: function (clip) {\n        clips.push(clip)\n        doClip()\n      },\n\n      remove: function () {\n        clips.pop()\n        doClip()\n      },\n    }\n  }\n\n  function drawText(style, x, y, string) {\n    if (string !== '') {\n      if (style.textTransform === 'uppercase') {\n        string = string.toUpperCase()\n      }\n\n      context.font = style.fontWeight + ' ' + style.fontSize + ' ' + style.fontFamily\n      context.textBaseline = 'top'\n      context.fillStyle = style.color\n      context.fillText(string, x, y + parseFloat(style.fontSize) * 0.1)\n    }\n  }\n\n  function buildRectPath(x, y, w, h, r) {\n    if (w < 2 * r) r = w / 2\n    if (h < 2 * r) r = h / 2\n\n    context.beginPath()\n    context.moveTo(x + r, y)\n    context.arcTo(x + w, y, x + w, y + h, r)\n    context.arcTo(x + w, y + h, x, y + h, r)\n    context.arcTo(x, y + h, x, y, r)\n    context.arcTo(x, y, x + w, y, r)\n    context.closePath()\n  }\n\n  function drawBorder(style, which, x, y, width, height) {\n    const borderWidth = style[which + 'Width']\n    const borderStyle = style[which + 'Style']\n    const borderColor = style[which + 'Color']\n\n    if (\n      borderWidth !== '0px' &&\n      borderStyle !== 'none' &&\n      borderColor !== 'transparent' &&\n      borderColor !== 'rgba(0, 0, 0, 0)'\n    ) {\n      context.strokeStyle = borderColor\n      context.lineWidth = parseFloat(borderWidth)\n      context.beginPath()\n      context.moveTo(x, y)\n      context.lineTo(x + width, y + height)\n      context.stroke()\n    }\n  }\n\n  function drawElement(element, style) {\n    let x = 0,\n      y = 0,\n      width = 0,\n      height = 0\n\n    if (element.nodeType === Node.TEXT_NODE) {\n      // text\n\n      range.selectNode(element)\n\n      const rect = range.getBoundingClientRect()\n\n      x = rect.left - offset.left - 0.5\n      y = rect.top - offset.top - 0.5\n      width = rect.width\n      height = rect.height\n\n      drawText(style, x, y, element.nodeValue.trim())\n    } else if (element.nodeType === Node.COMMENT_NODE) {\n      return\n    } else if (element instanceof HTMLCanvasElement) {\n      // Canvas element\n      if (element.style.display === 'none') return\n\n      context.save()\n      const dpr = window.devicePixelRatio\n      context.scale(1 / dpr, 1 / dpr)\n      context.drawImage(element, 0, 0)\n      context.restore()\n    } else {\n      if (element.style.display === 'none') return\n\n      const rect = element.getBoundingClientRect()\n\n      x = rect.left - offset.left - 0.5\n      y = rect.top - offset.top - 0.5\n      width = rect.width\n      height = rect.height\n\n      style = window.getComputedStyle(element)\n\n      // Get the border of the element used for fill and border\n\n      buildRectPath(x, y, width, height, parseFloat(style.borderRadius))\n\n      const backgroundColor = style.backgroundColor\n\n      if (backgroundColor !== 'transparent' && backgroundColor !== 'rgba(0, 0, 0, 0)') {\n        context.fillStyle = backgroundColor\n        context.fill()\n      }\n\n      // If all the borders match then stroke the round rectangle\n\n      const borders = ['borderTop', 'borderLeft', 'borderBottom', 'borderRight']\n\n      let match = true\n      let prevBorder = null\n\n      for (const border of borders) {\n        if (prevBorder !== null) {\n          match =\n            style[border + 'Width'] === style[prevBorder + 'Width'] &&\n            style[border + 'Color'] === style[prevBorder + 'Color'] &&\n            style[border + 'Style'] === style[prevBorder + 'Style']\n        }\n\n        if (match === false) break\n\n        prevBorder = border\n      }\n\n      if (match === true) {\n        // They all match so stroke the rectangle from before allows for border-radius\n\n        const width = parseFloat(style.borderTopWidth)\n\n        if (\n          style.borderTopWidth !== '0px' &&\n          style.borderTopStyle !== 'none' &&\n          style.borderTopColor !== 'transparent' &&\n          style.borderTopColor !== 'rgba(0, 0, 0, 0)'\n        ) {\n          context.strokeStyle = style.borderTopColor\n          context.lineWidth = width\n          context.stroke()\n        }\n      } else {\n        // Otherwise draw individual borders\n\n        drawBorder(style, 'borderTop', x, y, width, 0)\n        drawBorder(style, 'borderLeft', x, y, 0, height)\n        drawBorder(style, 'borderBottom', x, y + height, width, 0)\n        drawBorder(style, 'borderRight', x + width, y, 0, height)\n      }\n\n      if (element instanceof HTMLInputElement) {\n        let accentColor = style.accentColor\n\n        if (accentColor === undefined || accentColor === 'auto') accentColor = style.color\n\n        color.set(accentColor)\n\n        const luminance = Math.sqrt(0.299 * color.r ** 2 + 0.587 * color.g ** 2 + 0.114 * color.b ** 2)\n        const accentTextColor = luminance < 0.5 ? 'white' : '#111111'\n\n        if (element.type === 'radio') {\n          buildRectPath(x, y, width, height, height)\n\n          context.fillStyle = 'white'\n          context.strokeStyle = accentColor\n          context.lineWidth = 1\n          context.fill()\n          context.stroke()\n\n          if (element.checked) {\n            buildRectPath(x + 2, y + 2, width - 4, height - 4, height)\n\n            context.fillStyle = accentColor\n            context.strokeStyle = accentTextColor\n            context.lineWidth = 2\n            context.fill()\n            context.stroke()\n          }\n        }\n\n        if (element.type === 'checkbox') {\n          buildRectPath(x, y, width, height, 2)\n\n          context.fillStyle = element.checked ? accentColor : 'white'\n          context.strokeStyle = element.checked ? accentTextColor : accentColor\n          context.lineWidth = 1\n          context.stroke()\n          context.fill()\n\n          if (element.checked) {\n            const currentTextAlign = context.textAlign\n\n            context.textAlign = 'center'\n\n            const properties = {\n              color: accentTextColor,\n              fontFamily: style.fontFamily,\n              fontSize: height + 'px',\n              fontWeight: 'bold',\n            }\n\n            drawText(properties, x + width / 2, y, '✔')\n\n            context.textAlign = currentTextAlign\n          }\n        }\n\n        if (element.type === 'range') {\n          const [min, max, value] = ['min', 'max', 'value'].map((property) => parseFloat(element[property]))\n          const position = ((value - min) / (max - min)) * (width - height)\n\n          buildRectPath(x, y + height / 4, width, height / 2, height / 4)\n          context.fillStyle = accentTextColor\n          context.strokeStyle = accentColor\n          context.lineWidth = 1\n          context.fill()\n          context.stroke()\n\n          buildRectPath(x, y + height / 4, position + height / 2, height / 2, height / 4)\n          context.fillStyle = accentColor\n          context.fill()\n\n          buildRectPath(x + position, y, height, height, height / 2)\n          context.fillStyle = accentColor\n          context.fill()\n        }\n\n        if (element.type === 'color' || element.type === 'text' || element.type === 'number') {\n          clipper.add({ x: x, y: y, width: width, height: height })\n\n          drawText(style, x + parseInt(style.paddingLeft), y + parseInt(style.paddingTop), element.value)\n\n          clipper.remove()\n        }\n      }\n    }\n\n    /*\n\t\t// debug\n\t\tcontext.strokeStyle = '#' + Math.random().toString( 16 ).slice( - 3 );\n\t\tcontext.strokeRect( x - 0.5, y - 0.5, width + 1, height + 1 );\n\t\t*/\n\n    const isClipping = style.overflow === 'auto' || style.overflow === 'hidden'\n\n    if (isClipping) clipper.add({ x: x, y: y, width: width, height: height })\n\n    for (let i = 0; i < element.childNodes.length; i++) {\n      drawElement(element.childNodes[i], style)\n    }\n\n    if (isClipping) clipper.remove()\n  }\n\n  const offset = element.getBoundingClientRect()\n\n  let canvas = canvases.get(element)\n\n  if (canvas === undefined) {\n    canvas = document.createElement('canvas')\n    canvas.width = offset.width\n    canvas.height = offset.height\n    canvases.set(element, canvas)\n  }\n\n  const context = canvas.getContext('2d' /*, { alpha: false }*/)\n\n  const clipper = new Clipper(context)\n\n  // console.time( 'drawElement' );\n\n  drawElement(element)\n\n  // console.timeEnd( 'drawElement' );\n\n  return canvas\n}\n\nfunction htmlevent(element, event, x, y) {\n  const mouseEventInit = {\n    clientX: x * element.offsetWidth + element.offsetLeft,\n    clientY: y * element.offsetHeight + element.offsetTop,\n    view: element.ownerDocument.defaultView,\n  }\n\n  window.dispatchEvent(new MouseEvent(event, mouseEventInit))\n\n  const rect = element.getBoundingClientRect()\n\n  x = x * rect.width + rect.left\n  y = y * rect.height + rect.top\n\n  function traverse(element) {\n    if (element.nodeType !== Node.TEXT_NODE && element.nodeType !== Node.COMMENT_NODE) {\n      const rect = element.getBoundingClientRect()\n\n      if (x > rect.left && x < rect.right && y > rect.top && y < rect.bottom) {\n        element.dispatchEvent(new MouseEvent(event, mouseEventInit))\n\n        if (\n          element instanceof HTMLInputElement &&\n          element.type === 'range' &&\n          (event === 'mousedown' || event === 'click')\n        ) {\n          const [min, max] = ['min', 'max'].map((property) => parseFloat(element[property]))\n\n          const width = rect.width\n          const offsetX = x - rect.x\n          const proportion = offsetX / width\n          element.value = min + (max - min) * proportion\n          element.dispatchEvent(new InputEvent('input', { bubbles: true }))\n        }\n      }\n\n      for (let i = 0; i < element.childNodes.length; i++) {\n        traverse(element.childNodes[i])\n      }\n    }\n  }\n\n  traverse(element)\n}\n\nexport { HTMLMesh }\n"], "names": ["context", "element", "width", "rect"], "mappings": ";AAEA,MAAM,iBAAiB,KAAK;AAAA,EAC1B,YAAY,KAAK;AACf,UAAM,UAAU,IAAI,YAAY,GAAG;AAEnC,UAAM,WAAW,IAAI,cAAc,QAAQ,MAAM,QAAQ,MAAO,QAAQ,MAAM,SAAS,IAAK;AAC5F,UAAM,WAAW,IAAI,kBAAkB,EAAE,KAAK,SAAS,YAAY,OAAO,aAAa,MAAM;AAE7F,UAAM,UAAU,QAAQ;AAExB,aAAS,QAAQ,OAAO;AACtB,eAAS,IAAI,iBAAiB,KAAK;AAAA,IACpC;AAED,SAAK,iBAAiB,aAAa,OAAO;AAC1C,SAAK,iBAAiB,aAAa,OAAO;AAC1C,SAAK,iBAAiB,WAAW,OAAO;AACxC,SAAK,iBAAiB,SAAS,OAAO;AAEtC,SAAK,UAAU,WAAY;AACzB,eAAS,QAAS;AAClB,eAAS,QAAS;AAElB,eAAS,IAAI,QAAS;AAEtB,eAAS,OAAO,GAAG;AAEnB,WAAK,oBAAoB,aAAa,OAAO;AAC7C,WAAK,oBAAoB,aAAa,OAAO;AAC7C,WAAK,oBAAoB,WAAW,OAAO;AAC3C,WAAK,oBAAoB,SAAS,OAAO;AAAA,IAC1C;AAAA,EACF;AACH;AAEA,MAAM,oBAAoB,cAAc;AAAA,EACtC,YAAY,KAAK;AACf,UAAM,YAAY,GAAG,CAAC;AAEtB,SAAK,MAAM;AAEX,SAAK,aAAa;AAClB,QAAI,gBAAgB;AAAM,WAAK,aAAa;AAAA;AACvC,WAAK,WAAW;AACrB,SAAK,YAAY;AACjB,SAAK,YAAY;AAGjB,UAAM,WAAW,IAAI,iBAAiB,MAAM;AAC1C,UAAI,CAAC,KAAK,gBAAgB;AAExB,aAAK,iBAAiB,WAAW,MAAM,KAAK,OAAQ,GAAE,EAAE;AAAA,MACzD;AAAA,IACP,CAAK;AAED,UAAM,SAAS,EAAE,YAAY,MAAM,WAAW,MAAM,SAAS,MAAM,eAAe,KAAM;AACxF,aAAS,QAAQ,KAAK,MAAM;AAE5B,SAAK,WAAW;AAAA,EACjB;AAAA,EAED,iBAAiB,OAAO;AACtB,QAAI,MAAM,MAAM;AACd,gBAAU,KAAK,KAAK,MAAM,MAAM,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC;AAAA,IAC3D;AAAA,EACF;AAAA,EAED,SAAS;AACP,SAAK,QAAQ,YAAY,KAAK,GAAG;AACjC,SAAK,cAAc;AAEnB,SAAK,iBAAiB;AAAA,EACvB;AAAA,EAED,UAAU;AACR,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,WAAY;AAAA,IAC3B;AAED,SAAK,iBAAiB,aAAa,KAAK,cAAc;AAEtD,UAAM,QAAS;AAAA,EAChB;AACH;AAIA,MAAM,WAAW,oBAAI,QAAS;AAE9B,SAAS,YAAY,SAAS;AAC5B,QAAM,QAAQ,SAAS,YAAa;AACpC,QAAM,QAAQ,IAAI,MAAO;AAEzB,WAAS,QAAQA,UAAS;AACxB,UAAM,QAAQ,CAAE;AAChB,QAAI,aAAa;AAEjB,aAAS,SAAS;AAChB,UAAI,YAAY;AACd,qBAAa;AACb,QAAAA,SAAQ,QAAS;AAAA,MAClB;AAED,UAAI,MAAM,WAAW;AAAG;AAExB,UAAI,OAAO,WACT,OAAO;AACT,UAAI,OAAO,UACT,OAAO;AAET,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,OAAO,MAAM,CAAC;AAEpB,eAAO,KAAK,IAAI,MAAM,KAAK,CAAC;AAC5B,eAAO,KAAK,IAAI,MAAM,KAAK,CAAC;AAC5B,eAAO,KAAK,IAAI,MAAM,KAAK,IAAI,KAAK,KAAK;AACzC,eAAO,KAAK,IAAI,MAAM,KAAK,IAAI,KAAK,MAAM;AAAA,MAC3C;AAED,MAAAA,SAAQ,KAAM;AACd,MAAAA,SAAQ,UAAW;AACnB,MAAAA,SAAQ,KAAK,MAAM,MAAM,OAAO,MAAM,OAAO,IAAI;AACjD,MAAAA,SAAQ,KAAM;AAEd,mBAAa;AAAA,IACd;AAED,WAAO;AAAA,MACL,KAAK,SAAU,MAAM;AACnB,cAAM,KAAK,IAAI;AACf,eAAQ;AAAA,MACT;AAAA,MAED,QAAQ,WAAY;AAClB,cAAM,IAAK;AACX,eAAQ;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAED,WAAS,SAAS,OAAO,GAAG,GAAG,QAAQ;AACrC,QAAI,WAAW,IAAI;AACjB,UAAI,MAAM,kBAAkB,aAAa;AACvC,iBAAS,OAAO,YAAa;AAAA,MAC9B;AAED,cAAQ,OAAO,MAAM,aAAa,MAAM,MAAM,WAAW,MAAM,MAAM;AACrE,cAAQ,eAAe;AACvB,cAAQ,YAAY,MAAM;AAC1B,cAAQ,SAAS,QAAQ,GAAG,IAAI,WAAW,MAAM,QAAQ,IAAI,GAAG;AAAA,IACjE;AAAA,EACF;AAED,WAAS,cAAc,GAAG,GAAG,GAAG,GAAG,GAAG;AACpC,QAAI,IAAI,IAAI;AAAG,UAAI,IAAI;AACvB,QAAI,IAAI,IAAI;AAAG,UAAI,IAAI;AAEvB,YAAQ,UAAW;AACnB,YAAQ,OAAO,IAAI,GAAG,CAAC;AACvB,YAAQ,MAAM,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC;AACvC,YAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC;AACvC,YAAQ,MAAM,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;AAC/B,YAAQ,MAAM,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC;AAC/B,YAAQ,UAAW;AAAA,EACpB;AAED,WAAS,WAAW,OAAO,OAAO,GAAG,GAAG,OAAO,QAAQ;AACrD,UAAM,cAAc,MAAM,QAAQ,OAAO;AACzC,UAAM,cAAc,MAAM,QAAQ,OAAO;AACzC,UAAM,cAAc,MAAM,QAAQ,OAAO;AAEzC,QACE,gBAAgB,SAChB,gBAAgB,UAChB,gBAAgB,iBAChB,gBAAgB,oBAChB;AACA,cAAQ,cAAc;AACtB,cAAQ,YAAY,WAAW,WAAW;AAC1C,cAAQ,UAAW;AACnB,cAAQ,OAAO,GAAG,CAAC;AACnB,cAAQ,OAAO,IAAI,OAAO,IAAI,MAAM;AACpC,cAAQ,OAAQ;AAAA,IACjB;AAAA,EACF;AAED,WAAS,YAAYC,UAAS,OAAO;AACnC,QAAI,IAAI,GACN,IAAI,GACJ,QAAQ,GACR,SAAS;AAEX,QAAIA,SAAQ,aAAa,KAAK,WAAW;AAGvC,YAAM,WAAWA,QAAO;AAExB,YAAM,OAAO,MAAM,sBAAuB;AAE1C,UAAI,KAAK,OAAO,OAAO,OAAO;AAC9B,UAAI,KAAK,MAAM,OAAO,MAAM;AAC5B,cAAQ,KAAK;AACb,eAAS,KAAK;AAEd,eAAS,OAAO,GAAG,GAAGA,SAAQ,UAAU,MAAM;AAAA,IAC/C,WAAUA,SAAQ,aAAa,KAAK,cAAc;AACjD;AAAA,IACN,WAAeA,oBAAmB,mBAAmB;AAE/C,UAAIA,SAAQ,MAAM,YAAY;AAAQ;AAEtC,cAAQ,KAAM;AACd,YAAM,MAAM,OAAO;AACnB,cAAQ,MAAM,IAAI,KAAK,IAAI,GAAG;AAC9B,cAAQ,UAAUA,UAAS,GAAG,CAAC;AAC/B,cAAQ,QAAS;AAAA,IACvB,OAAW;AACL,UAAIA,SAAQ,MAAM,YAAY;AAAQ;AAEtC,YAAM,OAAOA,SAAQ,sBAAuB;AAE5C,UAAI,KAAK,OAAO,OAAO,OAAO;AAC9B,UAAI,KAAK,MAAM,OAAO,MAAM;AAC5B,cAAQ,KAAK;AACb,eAAS,KAAK;AAEd,cAAQ,OAAO,iBAAiBA,QAAO;AAIvC,oBAAc,GAAG,GAAG,OAAO,QAAQ,WAAW,MAAM,YAAY,CAAC;AAEjE,YAAM,kBAAkB,MAAM;AAE9B,UAAI,oBAAoB,iBAAiB,oBAAoB,oBAAoB;AAC/E,gBAAQ,YAAY;AACpB,gBAAQ,KAAM;AAAA,MACf;AAID,YAAM,UAAU,CAAC,aAAa,cAAc,gBAAgB,aAAa;AAEzE,UAAI,QAAQ;AACZ,UAAI,aAAa;AAEjB,iBAAW,UAAU,SAAS;AAC5B,YAAI,eAAe,MAAM;AACvB,kBACE,MAAM,SAAS,OAAO,MAAM,MAAM,aAAa,OAAO,KACtD,MAAM,SAAS,OAAO,MAAM,MAAM,aAAa,OAAO,KACtD,MAAM,SAAS,OAAO,MAAM,MAAM,aAAa,OAAO;AAAA,QACzD;AAED,YAAI,UAAU;AAAO;AAErB,qBAAa;AAAA,MACd;AAED,UAAI,UAAU,MAAM;AAGlB,cAAMC,SAAQ,WAAW,MAAM,cAAc;AAE7C,YACE,MAAM,mBAAmB,SACzB,MAAM,mBAAmB,UACzB,MAAM,mBAAmB,iBACzB,MAAM,mBAAmB,oBACzB;AACA,kBAAQ,cAAc,MAAM;AAC5B,kBAAQ,YAAYA;AACpB,kBAAQ,OAAQ;AAAA,QACjB;AAAA,MACT,OAAa;AAGL,mBAAW,OAAO,aAAa,GAAG,GAAG,OAAO,CAAC;AAC7C,mBAAW,OAAO,cAAc,GAAG,GAAG,GAAG,MAAM;AAC/C,mBAAW,OAAO,gBAAgB,GAAG,IAAI,QAAQ,OAAO,CAAC;AACzD,mBAAW,OAAO,eAAe,IAAI,OAAO,GAAG,GAAG,MAAM;AAAA,MACzD;AAED,UAAID,oBAAmB,kBAAkB;AACvC,YAAI,cAAc,MAAM;AAExB,YAAI,gBAAgB,UAAa,gBAAgB;AAAQ,wBAAc,MAAM;AAE7E,cAAM,IAAI,WAAW;AAErB,cAAM,YAAY,KAAK,KAAK,QAAQ,MAAM,KAAK,IAAI,QAAQ,MAAM,KAAK,IAAI,QAAQ,MAAM,KAAK,CAAC;AAC9F,cAAM,kBAAkB,YAAY,MAAM,UAAU;AAEpD,YAAIA,SAAQ,SAAS,SAAS;AAC5B,wBAAc,GAAG,GAAG,OAAO,QAAQ,MAAM;AAEzC,kBAAQ,YAAY;AACpB,kBAAQ,cAAc;AACtB,kBAAQ,YAAY;AACpB,kBAAQ,KAAM;AACd,kBAAQ,OAAQ;AAEhB,cAAIA,SAAQ,SAAS;AACnB,0BAAc,IAAI,GAAG,IAAI,GAAG,QAAQ,GAAG,SAAS,GAAG,MAAM;AAEzD,oBAAQ,YAAY;AACpB,oBAAQ,cAAc;AACtB,oBAAQ,YAAY;AACpB,oBAAQ,KAAM;AACd,oBAAQ,OAAQ;AAAA,UACjB;AAAA,QACF;AAED,YAAIA,SAAQ,SAAS,YAAY;AAC/B,wBAAc,GAAG,GAAG,OAAO,QAAQ,CAAC;AAEpC,kBAAQ,YAAYA,SAAQ,UAAU,cAAc;AACpD,kBAAQ,cAAcA,SAAQ,UAAU,kBAAkB;AAC1D,kBAAQ,YAAY;AACpB,kBAAQ,OAAQ;AAChB,kBAAQ,KAAM;AAEd,cAAIA,SAAQ,SAAS;AACnB,kBAAM,mBAAmB,QAAQ;AAEjC,oBAAQ,YAAY;AAEpB,kBAAM,aAAa;AAAA,cACjB,OAAO;AAAA,cACP,YAAY,MAAM;AAAA,cAClB,UAAU,SAAS;AAAA,cACnB,YAAY;AAAA,YACb;AAED,qBAAS,YAAY,IAAI,QAAQ,GAAG,GAAG,GAAG;AAE1C,oBAAQ,YAAY;AAAA,UACrB;AAAA,QACF;AAED,YAAIA,SAAQ,SAAS,SAAS;AAC5B,gBAAM,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,OAAO,OAAO,OAAO,EAAE,IAAI,CAAC,aAAa,WAAWA,SAAQ,QAAQ,CAAC,CAAC;AACjG,gBAAM,YAAa,QAAQ,QAAQ,MAAM,QAAS,QAAQ;AAE1D,wBAAc,GAAG,IAAI,SAAS,GAAG,OAAO,SAAS,GAAG,SAAS,CAAC;AAC9D,kBAAQ,YAAY;AACpB,kBAAQ,cAAc;AACtB,kBAAQ,YAAY;AACpB,kBAAQ,KAAM;AACd,kBAAQ,OAAQ;AAEhB,wBAAc,GAAG,IAAI,SAAS,GAAG,WAAW,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC;AAC9E,kBAAQ,YAAY;AACpB,kBAAQ,KAAM;AAEd,wBAAc,IAAI,UAAU,GAAG,QAAQ,QAAQ,SAAS,CAAC;AACzD,kBAAQ,YAAY;AACpB,kBAAQ,KAAM;AAAA,QACf;AAED,YAAIA,SAAQ,SAAS,WAAWA,SAAQ,SAAS,UAAUA,SAAQ,SAAS,UAAU;AACpF,kBAAQ,IAAI,EAAE,GAAM,GAAM,OAAc,OAAc,CAAE;AAExD,mBAAS,OAAO,IAAI,SAAS,MAAM,WAAW,GAAG,IAAI,SAAS,MAAM,UAAU,GAAGA,SAAQ,KAAK;AAE9F,kBAAQ,OAAQ;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAQD,UAAM,aAAa,MAAM,aAAa,UAAU,MAAM,aAAa;AAEnE,QAAI;AAAY,cAAQ,IAAI,EAAE,GAAM,GAAM,OAAc,OAAc,CAAE;AAExE,aAAS,IAAI,GAAG,IAAIA,SAAQ,WAAW,QAAQ,KAAK;AAClD,kBAAYA,SAAQ,WAAW,CAAC,GAAG,KAAK;AAAA,IACzC;AAED,QAAI;AAAY,cAAQ,OAAQ;AAAA,EACjC;AAED,QAAM,SAAS,QAAQ,sBAAuB;AAE9C,MAAI,SAAS,SAAS,IAAI,OAAO;AAEjC,MAAI,WAAW,QAAW;AACxB,aAAS,SAAS,cAAc,QAAQ;AACxC,WAAO,QAAQ,OAAO;AACtB,WAAO,SAAS,OAAO;AACvB,aAAS,IAAI,SAAS,MAAM;AAAA,EAC7B;AAED,QAAM,UAAU,OAAO;AAAA,IAAW;AAAA;AAAA,EAA4B;AAE9D,QAAM,UAAU,IAAI,QAAQ,OAAO;AAInC,cAAY,OAAO;AAInB,SAAO;AACT;AAEA,SAAS,UAAU,SAAS,OAAO,GAAG,GAAG;AACvC,QAAM,iBAAiB;AAAA,IACrB,SAAS,IAAI,QAAQ,cAAc,QAAQ;AAAA,IAC3C,SAAS,IAAI,QAAQ,eAAe,QAAQ;AAAA,IAC5C,MAAM,QAAQ,cAAc;AAAA,EAC7B;AAED,SAAO,cAAc,IAAI,WAAW,OAAO,cAAc,CAAC;AAE1D,QAAM,OAAO,QAAQ,sBAAuB;AAE5C,MAAI,IAAI,KAAK,QAAQ,KAAK;AAC1B,MAAI,IAAI,KAAK,SAAS,KAAK;AAE3B,WAAS,SAASA,UAAS;AACzB,QAAIA,SAAQ,aAAa,KAAK,aAAaA,SAAQ,aAAa,KAAK,cAAc;AACjF,YAAME,QAAOF,SAAQ,sBAAuB;AAE5C,UAAI,IAAIE,MAAK,QAAQ,IAAIA,MAAK,SAAS,IAAIA,MAAK,OAAO,IAAIA,MAAK,QAAQ;AACtE,QAAAF,SAAQ,cAAc,IAAI,WAAW,OAAO,cAAc,CAAC;AAE3D,YACEA,oBAAmB,oBACnBA,SAAQ,SAAS,YAChB,UAAU,eAAe,UAAU,UACpC;AACA,gBAAM,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,KAAK,EAAE,IAAI,CAAC,aAAa,WAAWA,SAAQ,QAAQ,CAAC,CAAC;AAEjF,gBAAM,QAAQE,MAAK;AACnB,gBAAM,UAAU,IAAIA,MAAK;AACzB,gBAAM,aAAa,UAAU;AAC7B,UAAAF,SAAQ,QAAQ,OAAO,MAAM,OAAO;AACpC,UAAAA,SAAQ,cAAc,IAAI,WAAW,SAAS,EAAE,SAAS,KAAI,CAAE,CAAC;AAAA,QACjE;AAAA,MACF;AAED,eAAS,IAAI,GAAG,IAAIA,SAAQ,WAAW,QAAQ,KAAK;AAClD,iBAASA,SAAQ,WAAW,CAAC,CAAC;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAED,WAAS,OAAO;AAClB;"}