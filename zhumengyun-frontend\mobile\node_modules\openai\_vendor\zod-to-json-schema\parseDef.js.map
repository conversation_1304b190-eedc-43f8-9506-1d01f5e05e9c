{"version": 3, "file": "parseDef.js", "sourceRoot": "", "sources": ["../../src/_vendor/zod-to-json-schema/parseDef.ts"], "names": [], "mappings": ";;;AAAA,6BAAwD;AACxD,0CAAgE;AAChE,8CAAsE;AACtE,gDAAyE;AACzE,kDAA4E;AAC5E,kDAAoD;AACpD,8CAAgD;AAChD,4CAAmE;AACnE,kDAAoD;AACpD,kDAAoD;AACpD,4CAAmE;AACnE,4DAAoF;AACpF,kDAA4E;AAC5E,0CAAgE;AAChE,wDAAqF;AACrF,8CAAsE;AACtE,4CAAmE;AACnE,oDAA+E;AAC/E,gDAAyE;AACzE,gDAAyE;AACzE,oDAAsD;AACtD,oDAAsD;AACtD,kDAAoD;AACpD,gDAAyE;AACzE,0CAAgE;AAChE,gDAAyE;AACzE,8CAAsE;AACtE,sDAAkF;AAClF,8CAAsE;AACtE,kDAA4E;AAE5E,oDAAsD;AACtD,0CAA2C;AAsC3C,SAAgB,QAAQ,CACtB,GAAe,EACf,IAAU,EACV,eAAe,GAAG,KAAK;IAEvB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAEpC,IAAI,IAAI,CAAC,QAAQ,EAAE;QACjB,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;QAE7E,IAAI,cAAc,KAAK,wBAAc,EAAE;YACrC,OAAO,cAAc,CAAC;SACvB;KACF;IAED,IAAI,QAAQ,IAAI,CAAC,eAAe,EAAE;QAChC,MAAM,UAAU,GAAG,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAE3C,IAAI,UAAU,KAAK,SAAS,EAAE;YAC5B,IAAI,MAAM,IAAI,UAAU,EAAE;gBACxB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;aACpC;YAED,OAAO,UAAU,CAAC;SACnB;KACF;IAED,MAAM,OAAO,GAAS,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;IAE7E,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAE5B,MAAM,UAAU,GAAG,YAAY,CAAC,GAAG,EAAG,GAAW,CAAC,QAAQ,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;IAEnF,IAAI,UAAU,EAAE;QACd,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;KAChC;IAED,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;IAEhC,OAAO,UAAU,CAAC;AACpB,CAAC;AAxCD,4BAwCC;AAED,MAAM,OAAO,GAAG,CACd,IAAU,EACV,IAAU,EAME,EAAE;IACd,QAAQ,IAAI,CAAC,YAAY,EAAE;QACzB,KAAK,MAAM;YACT,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QACvC,oFAAoF;QACpF,0FAA0F;QAC1F,oFAAoF;QACpF,qDAAqD;QACrD,EAAE;QACF,2CAA2C;QAC3C,mFAAmF;QACnF,kFAAkF;QAClF,KAAK,iBAAiB;YACpB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEjE,yEAAyE;YACzE,gCAAgC;YAChC,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,KAAK,eAAe,EAAE;gBAC/D,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;aACnC;YAED,OAAO,EAAE,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QAC3E,KAAK,UAAU;YACb,OAAO,EAAE,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAChE,KAAK,MAAM,CAAC;QACZ,KAAK,MAAM,CAAC,CAAC;YACX,IACE,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM;gBAC1C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,EACpE;gBACA,OAAO,CAAC,IAAI,CAAC,mCAAmC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;gBAEjG,OAAO,EAAE,CAAC;aACX;YAED,OAAO,IAAI,CAAC,YAAY,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;SACtD;KACF;AACH,CAAC,CAAC;AAEF,MAAM,eAAe,GAAG,CAAC,KAAe,EAAE,KAAe,EAAE,EAAE;IAC3D,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,CAAC,GAAG,KAAK,CAAC,MAAM,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAChD,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;YAAE,MAAM;KAClC;IACD,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACtE,CAAC,CAAC;AAEF,MAAM,YAAY,GAAG,CACnB,GAAQ,EACR,QAA+B,EAC/B,IAAU,EACV,eAAwB,EACK,EAAE;IAC/B,QAAQ,QAAQ,EAAE;QAChB,KAAK,2BAAqB,CAAC,SAAS;YAClC,OAAO,IAAA,uBAAc,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACnC,KAAK,2BAAqB,CAAC,SAAS;YAClC,OAAO,IAAA,uBAAc,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACnC,KAAK,2BAAqB,CAAC,SAAS;YAClC,OAAO,IAAA,uBAAc,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACnC,KAAK,2BAAqB,CAAC,SAAS;YAClC,OAAO,IAAA,uBAAc,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACnC,KAAK,2BAAqB,CAAC,UAAU;YACnC,OAAO,IAAA,yBAAe,GAAE,CAAC;QAC3B,KAAK,2BAAqB,CAAC,OAAO;YAChC,OAAO,IAAA,mBAAY,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACjC,KAAK,2BAAqB,CAAC,YAAY;YACrC,OAAO,IAAA,6BAAiB,GAAE,CAAC;QAC7B,KAAK,2BAAqB,CAAC,OAAO;YAChC,OAAO,IAAA,mBAAY,EAAC,IAAI,CAAC,CAAC;QAC5B,KAAK,2BAAqB,CAAC,QAAQ;YACjC,OAAO,IAAA,qBAAa,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAClC,KAAK,2BAAqB,CAAC,QAAQ,CAAC;QACpC,KAAK,2BAAqB,CAAC,qBAAqB;YAC9C,OAAO,IAAA,qBAAa,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAClC,KAAK,2BAAqB,CAAC,eAAe;YACxC,OAAO,IAAA,mCAAoB,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACzC,KAAK,2BAAqB,CAAC,QAAQ;YACjC,OAAO,IAAA,qBAAa,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAClC,KAAK,2BAAqB,CAAC,SAAS;YAClC,OAAO,IAAA,uBAAc,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACnC,KAAK,2BAAqB,CAAC,UAAU;YACnC,OAAO,IAAA,yBAAe,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACpC,KAAK,2BAAqB,CAAC,OAAO;YAChC,OAAO,IAAA,mBAAY,EAAC,GAAG,CAAC,CAAC;QAC3B,KAAK,2BAAqB,CAAC,aAAa;YACtC,OAAO,IAAA,+BAAkB,EAAC,GAAG,CAAC,CAAC;QACjC,KAAK,2BAAqB,CAAC,WAAW;YACpC,OAAO,IAAA,2BAAgB,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACrC,KAAK,2BAAqB,CAAC,WAAW;YACpC,OAAO,IAAA,2BAAgB,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACrC,KAAK,2BAAqB,CAAC,MAAM;YAC/B,OAAO,IAAA,iBAAW,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAChC,KAAK,2BAAqB,CAAC,MAAM;YAC/B,OAAO,IAAA,iBAAW,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAChC,KAAK,2BAAqB,CAAC,OAAO;YAChC,OAAO,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC3C,KAAK,2BAAqB,CAAC,UAAU;YACnC,OAAO,IAAA,yBAAe,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACpC,KAAK,2BAAqB,CAAC,MAAM,CAAC;QAClC,KAAK,2BAAqB,CAAC,QAAQ;YACjC,OAAO,IAAA,qBAAa,GAAE,CAAC;QACzB,KAAK,2BAAqB,CAAC,UAAU;YACnC,OAAO,IAAA,yBAAe,EAAC,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;QACrD,KAAK,2BAAqB,CAAC,MAAM;YAC/B,OAAO,IAAA,iBAAW,GAAE,CAAC;QACvB,KAAK,2BAAqB,CAAC,UAAU;YACnC,OAAO,IAAA,yBAAe,GAAE,CAAC;QAC3B,KAAK,2BAAqB,CAAC,UAAU;YACnC,OAAO,IAAA,yBAAe,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACpC,KAAK,2BAAqB,CAAC,UAAU;YACnC,OAAO,IAAA,yBAAe,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACpC,KAAK,2BAAqB,CAAC,WAAW;YACpC,OAAO,IAAA,2BAAgB,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACrC,KAAK,2BAAqB,CAAC,QAAQ;YACjC,OAAO,IAAA,qBAAa,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAClC,KAAK,2BAAqB,CAAC,WAAW;YACpC,OAAO,IAAA,2BAAgB,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACrC,KAAK,2BAAqB,CAAC,WAAW,CAAC;QACvC,KAAK,2BAAqB,CAAC,OAAO,CAAC;QACnC,KAAK,2BAAqB,CAAC,SAAS;YAClC,OAAO,SAAS,CAAC;QACnB;YACE,OAAO,CAAC,CAAC,CAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC;KAC9C;AACH,CAAC,CAAC;AAEF,MAAM,OAAO,GAAG,CAAC,GAAe,EAAE,IAAU,EAAE,UAA2B,EAAmB,EAAE;IAC5F,IAAI,GAAG,CAAC,WAAW,EAAE;QACnB,UAAU,CAAC,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC;QAEzC,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,UAAU,CAAC,mBAAmB,GAAG,GAAG,CAAC,WAAW,CAAC;SAClD;KACF;IACD,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC"}