import { ColorRepresentation, Vector3 } from 'three';
import { HandlesContext } from '../context.js';
import { HandlesProperties } from '../index.js';
import { RegisteredHandle } from '../registered.js';
export declare class PlaneTranslateHandle extends RegisteredHandle {
    constructor(context: HandlesContext, tag: 'xy' | 'yz' | 'xz', tagPrefix?: string, axisVectors?: [Vector3, Vector3]);
    bind(defaultColor: ColorRepresentation, defaultHoverColor: ColorRepresentation, config?: HandlesProperties): (() => void) | undefined;
}
