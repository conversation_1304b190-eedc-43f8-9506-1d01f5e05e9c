{"version": 3, "file": "transactionFactory.js", "sourceRoot": "", "sources": ["../../../src/tx/transactionFactory.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;;AAGF,2CAA0C;AAC1C,iDAAsE;AACtE,mEAAsE;AACtE,mEAAuE;AACvE,iEAAqD;AAWrD,MAAM,YAAY,GAAkD,IAAI,GAAG,EAAE,CAAC;AAE9E,kEAAkE;AAClE,MAAa,kBAAkB;IAC9B,iEAAiE;IACjE,wFAAwF;IACxF,gBAAuB,CAAC;IAEjB,MAAM,CAAC,SAAS,CAAC,MAAe;QACtC,OAAO,MAAM,CAAC,IAAA,6BAAkB,EAAC,IAAA,uBAAY,EAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC;IAEM,MAAM,CAAC,uBAAuB,CACpC,IAAa,EACb,OAAuB;QAEvB,MAAM,MAAM,GAAG,kBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAClD,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACnC,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,UAAU,CACvB,MAAiC,EACjC,YAAuB,EAAE;QAEzB,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACtD,4BAA4B;YAC5B,OAAO,kCAAW,CAAC,UAAU,CAAC,MAAgB,EAAE,SAAS,CAAC,CAAC;QAC5D,CAAC;QACD,MAAM,MAAM,GAAG,kBAAkB,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;YAClB,OAAO,kCAAW,CAAC,UAAU,CAAC,MAAgB,EAAE,SAAS,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;YAClB,yEAAyE;YACzE,OAAO,oDAA4B,CAAC,UAAU;YAC7C,yEAAyE;YAChD,MAAM,EAC/B,SAAS,CACT,CAAC;QACH,CAAC;QACD,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;YAClB,OAAO,mDAA2B,CAAC,UAAU;YAC5C,yEAAyE;YACjD,MAAM,EAC9B,SAAS,CACT,CAAC;QACH,CAAC;QACD,MAAM,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,UAAU,EAAE,CAAC;YAClC,OAAO,gBAAgB,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,CAAqB,CAAC;QAC3E,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,8BAA8B,MAAM,gBAAgB,CAAC,CAAC;IACvE,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,kBAAkB,CAC/B,IAAgB,EAChB,YAAuB,EAAE;QAEzB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;YACrB,sBAAsB;YACtB,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;gBACjB,KAAK,CAAC;oBACL,OAAO,oDAA4B,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBACvE,KAAK,CAAC;oBACL,OAAO,mDAA2B,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBACtE,OAAO,CAAC,CAAC,CAAC;oBACT,MAAM,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3D,IAAI,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,gBAAgB,EAAE,CAAC;wBACxC,OAAO,gBAAgB,CAAC,gBAAgB,CACvC,IAAI,EACJ,SAAS,CACW,CAAC;oBACvB,CAAC;oBAED,MAAM,IAAI,KAAK,CAAC,4BAA4B,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;gBAChE,CAAC;YACF,CAAC;QACF,CAAC;aAAM,CAAC;YACP,OAAO,kCAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACtD,CAAC;IACF,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,iBAAiB,CAAC,IAA+B,EAAE,YAAuB,EAAE;QACzF,IAAI,IAAA,yBAAY,EAAC,IAAI,CAAC,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,6BAA6B;YAC7B,OAAO,kCAAW,CAAC,eAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACrD,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;IAClE,CAAC;CACD;AA/GD,gDA+GC"}