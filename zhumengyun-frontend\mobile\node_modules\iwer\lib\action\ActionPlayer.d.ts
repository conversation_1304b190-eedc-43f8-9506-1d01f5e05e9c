/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_ACTION_PLAYER } from '../private.js';
import { XRInputSource } from '../input/XRInputSource.js';
import { XRReferenceSpace } from '../spaces/XRReferenceSpace.js';
import { quat, vec3 } from 'gl-matrix';
import { InputSchema } from './ActionRecorder.js';
import { XREye } from '../views/XRView.js';
import { XRSpace } from '../spaces/XRSpace.js';
export interface CompressedRecording {
    schema: {
        0: number;
        1: InputSchema;
    }[];
    frames: any[];
}
type ProcessedInputData = {
    targetRayTransform: number[];
    gripTransform?: number[];
    handTransforms?: any[];
    buttons?: {
        0: 0 | 1;
        1: 0 | 1;
        2: number;
    }[];
    axes?: number[];
};
export declare class ActionPlayer {
    [P_ACTION_PLAYER]: {
        refSpace: XRReferenceSpace;
        inputSources: Map<number, {
            active: boolean;
            source: XRInputSource;
        }>;
        inputSchemas: Map<number, InputSchema>;
        frames: any[];
        recordedFramePointer: number;
        startingTimeStamp: DOMHighResTimeStamp;
        endingTimeStamp: DOMHighResTimeStamp;
        playbackTime: DOMHighResTimeStamp;
        actualTimeStamp?: DOMHighResTimeStamp;
        playing: boolean;
        viewerSpace: XRReferenceSpace;
        viewSpaces: {
            [key in XREye]: XRSpace;
        };
        vec3: vec3;
        quat: quat;
    };
    constructor(refSpace: XRReferenceSpace, recording: {
        schema: {
            0: number;
            1: InputSchema;
        }[];
        frames: any[];
    }, ipd: number);
    play(): void;
    stop(): void;
    get playing(): boolean;
    get viewerSpace(): XRReferenceSpace;
    get viewSpaces(): {
        none: XRSpace;
        left: XRSpace;
        right: XRSpace;
    };
    get inputSources(): XRInputSource[];
    playFrame(): void;
    updateInputSource(inputSource: XRInputSource, schema: InputSchema, lastInputData: ProcessedInputData, nextInputData: ProcessedInputData, alpha: number): void;
    updateXRSpaceFromMergedFrames(space: XRSpace, lastTransform: number[], nextTransform: number[], alpha: number): void;
    processRawInputData(inputDataRaw: any[]): {
        index: any;
        inputData: ProcessedInputData;
    };
}
export declare const mergeTransform: (f1: number[], f2: number[], alpha: number, position: vec3, quaternion: quat) => void;
export {};
//# sourceMappingURL=ActionPlayer.d.ts.map