import { Group, OrthographicCamera, PerspectiveCamera } from 'three';
import { HandlesContext } from '../context.js';
import { HandlesProperties } from '../index.js';
export declare class ScaleHandles extends Group {
    private readonly context;
    size?: number | undefined;
    fixed?: boolean | undefined;
    private readonly scaleX;
    private readonly scaleY;
    private readonly scaleZ;
    private readonly scaleNegX;
    private readonly scaleNegY;
    private readonly scaleNegZ;
    private readonly translationX;
    private readonly translationY;
    private readonly translationZ;
    private readonly translationNegX;
    private readonly translationNegY;
    private readonly translationNegZ;
    private readonly translationXY;
    private readonly translationYZ;
    private readonly translationXZ;
    constructor(context: HandlesContext, size?: number | undefined, fixed?: boolean | undefined);
    update(camera: PerspectiveCamera | OrthographicCamera): void;
    bind(options?: HandlesProperties): () => void;
}
