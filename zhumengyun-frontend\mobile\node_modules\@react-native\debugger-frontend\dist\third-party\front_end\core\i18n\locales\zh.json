{"core/common/ResourceType.ts | cspviolationreport": {"message": "CSPViolationReport"}, "core/common/ResourceType.ts | css": {"message": "CSS"}, "core/common/ResourceType.ts | doc": {"message": "文档"}, "core/common/ResourceType.ts | document": {"message": "文档"}, "core/common/ResourceType.ts | eventsource": {"message": "EventSource"}, "core/common/ResourceType.ts | fetch": {"message": "<PERSON>tch"}, "core/common/ResourceType.ts | fetchAndXHR": {"message": "Fetch 和 XHR"}, "core/common/ResourceType.ts | font": {"message": "字体"}, "core/common/ResourceType.ts | image": {"message": "图片"}, "core/common/ResourceType.ts | img": {"message": "图片"}, "core/common/ResourceType.ts | javascript": {"message": "JavaScript"}, "core/common/ResourceType.ts | js": {"message": "JS"}, "core/common/ResourceType.ts | manifest": {"message": "清单"}, "core/common/ResourceType.ts | media": {"message": "媒体"}, "core/common/ResourceType.ts | other": {"message": "其他"}, "core/common/ResourceType.ts | ping": {"message": "<PERSON>"}, "core/common/ResourceType.ts | preflight": {"message": "预检"}, "core/common/ResourceType.ts | script": {"message": "脚本"}, "core/common/ResourceType.ts | signedexchange": {"message": "SignedExchange"}, "core/common/ResourceType.ts | stylesheet": {"message": "样式表"}, "core/common/ResourceType.ts | texttrack": {"message": "TextTrack"}, "core/common/ResourceType.ts | wasm": {"message": "<PERSON><PERSON>"}, "core/common/ResourceType.ts | webassembly": {"message": "WebAssembly"}, "core/common/ResourceType.ts | webbundle": {"message": "WebBundle"}, "core/common/ResourceType.ts | websocket": {"message": "WebSocket"}, "core/common/ResourceType.ts | webtransport": {"message": "WebTransport"}, "core/common/ResourceType.ts | ws": {"message": "WS"}, "core/common/Revealer.ts | applicationPanel": {"message": "“应用”面板"}, "core/common/Revealer.ts | changesDrawer": {"message": "变更抽屉式导航栏"}, "core/common/Revealer.ts | developerResourcesPanel": {"message": "“开发者资源”面板"}, "core/common/Revealer.ts | elementsPanel": {"message": "元素面板"}, "core/common/Revealer.ts | issuesView": {"message": "“问题”视图"}, "core/common/Revealer.ts | memoryInspectorPanel": {"message": "“内存检查器”面板"}, "core/common/Revealer.ts | networkPanel": {"message": "“网络”面板"}, "core/common/Revealer.ts | sourcesPanel": {"message": "“来源”面板"}, "core/common/Revealer.ts | stylesSidebar": {"message": "样式边栏"}, "core/common/SettingRegistration.ts | adorner": {"message": "装饰器"}, "core/common/SettingRegistration.ts | appearance": {"message": "外观"}, "core/common/SettingRegistration.ts | console": {"message": "控制台"}, "core/common/SettingRegistration.ts | debugger": {"message": "调试程序"}, "core/common/SettingRegistration.ts | elements": {"message": "元素"}, "core/common/SettingRegistration.ts | extension": {"message": "扩展名"}, "core/common/SettingRegistration.ts | global": {"message": "全局"}, "core/common/SettingRegistration.ts | grid": {"message": "网格"}, "core/common/SettingRegistration.ts | memory": {"message": "内存"}, "core/common/SettingRegistration.ts | mobile": {"message": "移动设备"}, "core/common/SettingRegistration.ts | network": {"message": "网络"}, "core/common/SettingRegistration.ts | performance": {"message": "性能"}, "core/common/SettingRegistration.ts | persistence": {"message": "持久性"}, "core/common/SettingRegistration.ts | rendering": {"message": "渲染"}, "core/common/SettingRegistration.ts | sources": {"message": "源代码/来源"}, "core/common/SettingRegistration.ts | sync": {"message": "同步"}, "core/host/InspectorFrontendHost.ts | devtoolsS": {"message": "DevTools - {PH1}"}, "core/host/ResourceLoader.ts | cacheError": {"message": "缓存错误"}, "core/host/ResourceLoader.ts | certificateError": {"message": "证书错误"}, "core/host/ResourceLoader.ts | certificateManagerError": {"message": "证书管理工具错误"}, "core/host/ResourceLoader.ts | connectionError": {"message": "连接错误"}, "core/host/ResourceLoader.ts | decodingDataUrlFailed": {"message": "数据网址解码失败"}, "core/host/ResourceLoader.ts | dnsResolverError": {"message": "DNS 解析器错误"}, "core/host/ResourceLoader.ts | ftpError": {"message": "FTP 错误"}, "core/host/ResourceLoader.ts | httpError": {"message": "HTTP 错误"}, "core/host/ResourceLoader.ts | httpErrorStatusCodeSS": {"message": "HTTP 错误：状态代码 {PH1}，{PH2}"}, "core/host/ResourceLoader.ts | invalidUrl": {"message": "网址无效"}, "core/host/ResourceLoader.ts | signedExchangeError": {"message": "Signed Exchange 错误"}, "core/host/ResourceLoader.ts | systemError": {"message": "系统错误"}, "core/host/ResourceLoader.ts | unknownError": {"message": "未知错误"}, "core/i18n/time-utilities.ts | fdays": {"message": "{PH1} 天"}, "core/i18n/time-utilities.ts | fhrs": {"message": "{PH1} 小时"}, "core/i18n/time-utilities.ts | fmin": {"message": "{PH1} 分钟"}, "core/i18n/time-utilities.ts | fmms": {"message": "{PH1} 微秒"}, "core/i18n/time-utilities.ts | fms": {"message": "{PH1} 毫秒"}, "core/i18n/time-utilities.ts | fs": {"message": "{PH1} 秒"}, "core/sdk/CPUProfilerModel.ts | profileD": {"message": "性能分析报告 {PH1}"}, "core/sdk/CSSStyleSheetHeader.ts | couldNotFindTheOriginalStyle": {"message": "无法找到原始样式表。"}, "core/sdk/CSSStyleSheetHeader.ts | thereWasAnErrorRetrievingThe": {"message": "检索源代码样式时出错。"}, "core/sdk/ChildTargetManager.ts | main": {"message": "主要"}, "core/sdk/CompilerSourceMappingContentProvider.ts | couldNotLoadContentForSS": {"message": "无法加载 {PH1} 的内容（{PH2}）"}, "core/sdk/ConsoleModel.ts | bfcacheNavigation": {"message": "前往 {PH1} 的导航已从往返缓存中恢复（参见 https://web.dev/bfcache/）"}, "core/sdk/ConsoleModel.ts | failedToSaveToTempVariable": {"message": "未能保存到临时变量中。"}, "core/sdk/ConsoleModel.ts | navigatedToS": {"message": "已转到 {PH1}"}, "core/sdk/ConsoleModel.ts | profileSFinished": {"message": "性能分析报告“{PH1}”已完成。"}, "core/sdk/ConsoleModel.ts | profileSStarted": {"message": "性能分析报告“{PH1}”已开始。"}, "core/sdk/DebuggerModel.ts | block": {"message": "代码块"}, "core/sdk/DebuggerModel.ts | catchBlock": {"message": "Catch 代码块"}, "core/sdk/DebuggerModel.ts | closure": {"message": "闭包"}, "core/sdk/DebuggerModel.ts | expression": {"message": "表达式"}, "core/sdk/DebuggerModel.ts | global": {"message": "全局"}, "core/sdk/DebuggerModel.ts | local": {"message": "本地"}, "core/sdk/DebuggerModel.ts | module": {"message": "模块"}, "core/sdk/DebuggerModel.ts | script": {"message": "脚本"}, "core/sdk/DebuggerModel.ts | withBlock": {"message": "With 代码块"}, "core/sdk/NetworkManager.ts | fast4G": {"message": "高速 4G"}, "core/sdk/NetworkManager.ts | fastG": {"message": "低速 4G"}, "core/sdk/NetworkManager.ts | noContentForPreflight": {"message": "对于预检请求，没有可显示的内容"}, "core/sdk/NetworkManager.ts | noContentForRedirect": {"message": "没有可显示的内容，因为此请求被重定向了"}, "core/sdk/NetworkManager.ts | noContentForWebSocket": {"message": "尚不支持显示 WebSockets 内容"}, "core/sdk/NetworkManager.ts | noThrottling": {"message": "已停用节流模式"}, "core/sdk/NetworkManager.ts | offline": {"message": "离线"}, "core/sdk/NetworkManager.ts | requestWasBlockedByDevtoolsS": {"message": "请求被 DevTools 屏蔽：“{PH1}”"}, "core/sdk/NetworkManager.ts | sFailedLoadingSS": {"message": "{PH1} 加载失败：{PH2}“{PH3}”。"}, "core/sdk/NetworkManager.ts | sFinishedLoadingSS": {"message": "{PH1} 已完成加载：{PH2}“{PH3}”。"}, "core/sdk/NetworkManager.ts | slowG": {"message": "3G"}, "core/sdk/NetworkRequest.ts | anUnknownErrorWasEncounteredWhenTrying": {"message": "尝试存储此 Cookie 时发生未知错误。"}, "core/sdk/NetworkRequest.ts | binary": {"message": "（二进制）"}, "core/sdk/NetworkRequest.ts | blockedReasonInvalidDomain": {"message": "尝试通过 Set-Cookie 标头设置 Cookie 的操作被禁止了，因为此标头的“Domain”属性对当前的主机网址而言无效。"}, "core/sdk/NetworkRequest.ts | blockedReasonInvalidPrefix": {"message": "尝试通过 Set-Cookie 标头设置 Cookie 的操作被禁止了，因为此标头在名称中使用了“__Secure-”或“__Host-”前缀，该做法违反了包含这些前缀的 Cookie 所适用的附加规则，如 https://tools.ietf.org/html/draft-west-cookie-prefixes-05 中所定义。"}, "core/sdk/NetworkRequest.ts | blockedReasonOverwriteSecure": {"message": "尝试通过 Set-Cookie 标头设置 Cookie 的操作被禁止了，因为此标头不是通过安全连接发送的，而且会覆盖具有“Secure”属性的 Cookie。"}, "core/sdk/NetworkRequest.ts | blockedReasonSameSiteNoneInsecure": {"message": "尝试通过 Set-Cookie 标头设置 Cookie 的操作被禁止了，因为此标头具有“SameSite=None”属性但缺少使用“SameSite=None”所需的“Secure”属性。"}, "core/sdk/NetworkRequest.ts | blockedReasonSameSiteStrictLax": {"message": "尝试通过 Set-Cookie 标头设置 Cookie 的操作被禁止了，因为此标头具有“{PH1}”属性但来自一个跨网站响应，而该响应并不是对顶级导航操作的响应。"}, "core/sdk/NetworkRequest.ts | blockedReasonSameSiteUnspecifiedTreatedAsLax": {"message": "此 Set-Cookie 标头未指定“SameSite”属性，默认为“SameSite=Lax,”，并且已被屏蔽，因为它来自一个跨网站响应，而该响应并不是对顶级导航操作的响应。此 Set-Cookie 必须在设置时指定“SameSite=None”，才能跨网站使用。"}, "core/sdk/NetworkRequest.ts | blockedReasonSecureOnly": {"message": "尝试通过 Set-Cookie 标头设置 Cookie 的操作被禁止了，因为此标头具有“Secure”属性但不是通过安全连接接收的。"}, "core/sdk/NetworkRequest.ts | domainMismatch": {"message": "此 Cookie 已被屏蔽，因为请求网址的网域与此 Cookie 的网域不完全一致，也不是此 Cookie 的“Domain”属性值的子网域。"}, "core/sdk/NetworkRequest.ts | exemptionReasonCorsOptIn": {"message": "选择启用 CORS 后，可以使用此 Cookie。如需了解详情，请访问：goo.gle/cors"}, "core/sdk/NetworkRequest.ts | exemptionReasonEnterprisePolicy": {"message": "Chrome 企业版政策允许使用此 Cookie。如需了解详情，请访问：goo.gle/ce-3pc"}, "core/sdk/NetworkRequest.ts | exemptionReasonStorageAccessAPI": {"message": "Storage Access API 允许使用此 Cookie。如需了解详情，请访问：goo.gle/saa"}, "core/sdk/NetworkRequest.ts | exemptionReasonTPCDDeprecationTrial": {"message": "在第三方 Cookie 逐步淘汰弃用试用期内允许使用此 Cookie。"}, "core/sdk/NetworkRequest.ts | exemptionReasonTPCDHeuristics": {"message": "根据过去的第三方 Cookie 逐步淘汰经验，这是允许使用的 Cookie。如需了解详情，请访问：goo.gle/hbe"}, "core/sdk/NetworkRequest.ts | exemptionReasonTPCDMetadata": {"message": "在第三方 Cookie 弃用试用宽限期内允许使用此 Cookie。如需了解详情，请访问：goo.gle/ps-dt。"}, "core/sdk/NetworkRequest.ts | exemptionReasonTopLevelStorageAccessAPI": {"message": "顶级 Storage Access API 允许使用此 Cookie。如需了解详情，请访问：goo.gle/saa-top"}, "core/sdk/NetworkRequest.ts | exemptionReasonUserSetting": {"message": "用户偏好设置允许使用此 Cookie。"}, "core/sdk/NetworkRequest.ts | nameValuePairExceedsMaxSize": {"message": "此 Cookie 已被屏蔽，因为它太大。名称和值的总大小不得超过 4096 个字符。"}, "core/sdk/NetworkRequest.ts | notOnPath": {"message": "此 Cookie 已被屏蔽，因为它的路径与请求网址的路径不完全匹配或不是其超目录。"}, "core/sdk/NetworkRequest.ts | samePartyFromCrossPartyContext": {"message": "此 Cookie 已被屏蔽，因为它具有“SameParty”属性，但相应请求是跨多方请求。由于资源网址的网域和资源所属框架/文档的网域不是同一 First-Party Set 的所有者或成员，因此系统判定这是跨多方请求。"}, "core/sdk/NetworkRequest.ts | sameSiteLax": {"message": "此 Cookie 已被屏蔽，因为它具有“SameSite=Lax”属性，但相应请求是通过另一网站发出的，而且不是由顶级导航操作发出的。"}, "core/sdk/NetworkRequest.ts | sameSiteNoneInsecure": {"message": "此 Cookie 已被屏蔽，因为它具有“SameSite=None”属性但未被标记为“Secure”。无“SameSite”限制的 Cookie 必须被标记为“Secure”并通过安全连接发送。"}, "core/sdk/NetworkRequest.ts | sameSiteStrict": {"message": "此 Cookie 已被屏蔽，因为它具有“SameSite=Strict”属性但相应请求是通过另一网站发出的。这包括其他网站发出的顶级导航请求。"}, "core/sdk/NetworkRequest.ts | sameSiteUnspecifiedTreatedAsLax": {"message": "此 Cookie 在存储时未指定“SameSite”属性，因而采用了默认值“SameSite=Lax”；该 Cookie 已被屏蔽，因为相应请求来自其他网站，而且不是由顶级导航操作发出。此 Cookie 必须在设置时指定“SameSite=None”，才能跨网站使用。"}, "core/sdk/NetworkRequest.ts | schemefulSameSiteLax": {"message": "此 Cookie 已被屏蔽，因为它具有“SameSite=Lax”属性，但相应请求是跨网站请求且不是由顶级导航操作发出的。由于网址架构与当前网站的架构不同，因此系统判定这是跨网站请求。"}, "core/sdk/NetworkRequest.ts | schemefulSameSiteStrict": {"message": "此 Cookie 已被屏蔽，因为它具有“SameSite=Strict”属性但相应请求是跨网站请求。这包括其他网站发出的顶级导航请求。由于网址架构与当前网站的架构不同，因此系统判定这是跨网站请求。"}, "core/sdk/NetworkRequest.ts | schemefulSameSiteUnspecifiedTreatedAsLax": {"message": "此 Cookie 在存储时未指定“SameSite”属性，默认为“SameSite=Lax\"”，并且已被屏蔽，因为相应请求是跨网站请求，而且不是由顶级导航操作发出。由于网址架构与当前网站的架构不同，因此系统判定这是跨网站请求。"}, "core/sdk/NetworkRequest.ts | secureOnly": {"message": "此 Cookie 已被屏蔽，因为它具有“Secure”属性但相应连接不安全。"}, "core/sdk/NetworkRequest.ts | setcookieHeaderIsIgnoredIn": {"message": "Set-Cookie 标头在以下网址的响应中被忽略：{PH1}。名称和值的总大小不得超过 4096 个字符。"}, "core/sdk/NetworkRequest.ts | theSchemeOfThisConnectionIsNot": {"message": "此连接的架构不能存储 Cookie。"}, "core/sdk/NetworkRequest.ts | thirdPartyPhaseout": {"message": "此 Cookie 因为第三方 Cookie 逐步淘汰机制而被屏蔽。前往“问题”标签页了解详情。"}, "core/sdk/NetworkRequest.ts | thisSetcookieDidntSpecifyASamesite": {"message": "此 Set-Cookie 标头未指定“SameSite”属性，默认为“SameSite=Lax\"”，并且已被屏蔽，因为它来自一个跨网站响应，而该响应并不是对顶级导航操作的响应。该响应之所以被视为跨网站，是因为网址架构与当前网站的架构不同。"}, "core/sdk/NetworkRequest.ts | thisSetcookieHadADisallowedCharacter": {"message": "此 Set-Cookie 标头包含不允许使用的字符（禁用的 ASCII 控制字符，或出现在 Cookie 的名称、值、属性名称或属性值中间的制表符）。"}, "core/sdk/NetworkRequest.ts | thisSetcookieHadInvalidSyntax": {"message": "此 Set-Cookie 标头的语法无效。"}, "core/sdk/NetworkRequest.ts | thisSetcookieWasBlockedBecauseItHadTheSameparty": {"message": "尝试通过 Set-Cookie 标头设置 Cookie 的操作被禁止了，因为此标头具有“SameParty”属性，但相应请求是跨多方请求。该请求之所以被视为跨多方，是因为资源网址的网域和资源所属框架/文档的网域不是同一 First-Party Set 的所有者或成员。"}, "core/sdk/NetworkRequest.ts | thisSetcookieWasBlockedBecauseItHadTheSamepartyAttribute": {"message": "尝试通过 Set-Cookie 标头设置 Cookie 的操作被禁止了，因为此标头既具有“SameParty”属性又具有与该属性冲突的其他属性。Chrome 要求那些使用“SameParty”属性的 Cookie 也使用“Secure”属性且不受“SameSite=Strict”限制。"}, "core/sdk/NetworkRequest.ts | thisSetcookieWasBlockedBecauseItHadTheSamesiteStrictLax": {"message": "尝试通过 Set-Cookie 标头设置 Cookie 的操作被禁止了，因为此标头具有“{PH1}”属性但来自一个跨网站响应，而该响应并不是对顶级导航操作的响应。该响应之所以被视为跨网站，是因为网址架构与当前网站的架构不同。"}, "core/sdk/NetworkRequest.ts | thisSetcookieWasBlockedBecauseTheNameValuePairExceedsMaxSize": {"message": "尝试通过 Set-Cookie 标头设置 Cookie 的操作被禁止了，因为此 Cookie 太大。名称和值的总大小不得超过 4096 个字符。"}, "core/sdk/NetworkRequest.ts | thisSetcookieWasBlockedDueThirdPartyPhaseout": {"message": "此 Cookie 的设置因为第三方 Cookie 逐步淘汰机制而被屏蔽。前往“问题”标签页了解详情。"}, "core/sdk/NetworkRequest.ts | thisSetcookieWasBlockedDueToUser": {"message": "尝试通过 Set-Cookie 标头设置 Cookie 的操作被禁止了，因为用户指定了偏好设置。"}, "core/sdk/NetworkRequest.ts | unknownError": {"message": "尝试发送此 Cookie 时发生未知错误。"}, "core/sdk/NetworkRequest.ts | userPreferences": {"message": "此 Cookie 因用户偏好设置而被屏蔽。"}, "core/sdk/OverlayModel.ts | pausedInDebugger": {"message": "已在调试程序中暂停"}, "core/sdk/PageResourceLoader.ts | loadCanceledDueToReloadOf": {"message": "由于系统重新加载已检查的网页，加载操作已取消"}, "core/sdk/Script.ts | scriptRemovedOrDeleted": {"message": "脚本已移除或已删除。"}, "core/sdk/Script.ts | unableToFetchScriptSource": {"message": "无法获取脚本源代码。"}, "core/sdk/ServerTiming.ts | deprecatedSyntaxFoundPleaseUse": {"message": "发现已弃用的语法。请使用：<name>;dur=<duration>;desc=<description>"}, "core/sdk/ServerTiming.ts | duplicateParameterSIgnored": {"message": "已忽略重复参数“{PH1}”。"}, "core/sdk/ServerTiming.ts | extraneousTrailingCharacters": {"message": "无关的尾随字符。"}, "core/sdk/ServerTiming.ts | noValueFoundForParameterS": {"message": "找不到“{PH1}”参数的值。"}, "core/sdk/ServerTiming.ts | unableToParseSValueS": {"message": "无法解析“{PH1}”值：“{PH2}”。"}, "core/sdk/ServerTiming.ts | unrecognizedParameterS": {"message": "无法识别的参数“{PH1}”。"}, "core/sdk/ServiceWorkerCacheModel.ts | serviceworkercacheagentError": {"message": "删除缓存中的缓存条目 {PH1} 时出现 ServiceWorkerCacheAgent 错误：{PH2}"}, "core/sdk/ServiceWorkerManager.ts | activated": {"message": "已启用"}, "core/sdk/ServiceWorkerManager.ts | activating": {"message": "正在启用"}, "core/sdk/ServiceWorkerManager.ts | installed": {"message": "已安装"}, "core/sdk/ServiceWorkerManager.ts | installing": {"message": "正在安装"}, "core/sdk/ServiceWorkerManager.ts | new": {"message": "新版"}, "core/sdk/ServiceWorkerManager.ts | redundant": {"message": "冗余"}, "core/sdk/ServiceWorkerManager.ts | running": {"message": "正在运行"}, "core/sdk/ServiceWorkerManager.ts | sSS": {"message": "{PH1} #{PH2}（{PH3}）"}, "core/sdk/ServiceWorkerManager.ts | starting": {"message": "正在启动"}, "core/sdk/ServiceWorkerManager.ts | stopped": {"message": "已停止"}, "core/sdk/ServiceWorkerManager.ts | stopping": {"message": "正在停止"}, "core/sdk/sdk-meta.ts | achromatopsia": {"message": "全色盲（无法感知任何颜色）"}, "core/sdk/sdk-meta.ts | blurredVision": {"message": "视力模糊"}, "core/sdk/sdk-meta.ts | captureAsyncStackTraces": {"message": "捕获异步堆栈轨迹"}, "core/sdk/sdk-meta.ts | customFormatters": {"message": "自定义格式设置工具"}, "core/sdk/sdk-meta.ts | deuteranopia": {"message": "绿色盲（无法感知绿色）"}, "core/sdk/sdk-meta.ts | disableAsyncStackTraces": {"message": "停用异步堆栈轨迹"}, "core/sdk/sdk-meta.ts | disableAvifFormat": {"message": "停用 AVIF 格式"}, "core/sdk/sdk-meta.ts | disableCache": {"message": "停用缓存（在开发者工具已打开时）"}, "core/sdk/sdk-meta.ts | disableJavascript": {"message": "停用 JavaScript"}, "core/sdk/sdk-meta.ts | disableLocalFonts": {"message": "停用本地字体"}, "core/sdk/sdk-meta.ts | disableNetworkRequestBlocking": {"message": "停用网络请求屏蔽"}, "core/sdk/sdk-meta.ts | disableWebpFormat": {"message": "停用 WebP 格式"}, "core/sdk/sdk-meta.ts | doNotCaptureAsyncStackTraces": {"message": "不捕获异步堆栈轨迹"}, "core/sdk/sdk-meta.ts | doNotEmulateAFocusedPage": {"message": "不模拟所聚焦的网页"}, "core/sdk/sdk-meta.ts | doNotEmulateAnyVisionDeficiency": {"message": "不模拟任何视觉缺陷"}, "core/sdk/sdk-meta.ts | doNotEmulateCss": {"message": "不模拟 CSS {PH1}"}, "core/sdk/sdk-meta.ts | doNotEmulateCssMediaType": {"message": "不模拟 CSS 媒体类型"}, "core/sdk/sdk-meta.ts | doNotExtendGridLines": {"message": "不延长网格线"}, "core/sdk/sdk-meta.ts | doNotHighlightAdFrames": {"message": "不突出显示广告框架"}, "core/sdk/sdk-meta.ts | doNotPauseOnExceptions": {"message": "不在遇到异常时暂停"}, "core/sdk/sdk-meta.ts | doNotPreserveLogUponNavigation": {"message": "浏览时不保留日志"}, "core/sdk/sdk-meta.ts | doNotShowGridNamedAreas": {"message": "不显示网格命名区域"}, "core/sdk/sdk-meta.ts | doNotShowGridTrackSizes": {"message": "不显示网格轨迹大小"}, "core/sdk/sdk-meta.ts | doNotShowRulersOnHover": {"message": "在鼠标指针悬停时不显示标尺"}, "core/sdk/sdk-meta.ts | emulateAFocusedPage": {"message": "模拟所聚焦的网页"}, "core/sdk/sdk-meta.ts | emulateAchromatopsia": {"message": "模拟全色盲（无法感知任何颜色）"}, "core/sdk/sdk-meta.ts | emulateAutoDarkMode": {"message": "模拟自动深色模式"}, "core/sdk/sdk-meta.ts | emulateBlurredVision": {"message": "模拟视力模糊"}, "core/sdk/sdk-meta.ts | emulateCss": {"message": "模拟 CSS {PH1}"}, "core/sdk/sdk-meta.ts | emulateCssMediaFeature": {"message": "模拟 CSS 媒体功能 {PH1}"}, "core/sdk/sdk-meta.ts | emulateCssMediaType": {"message": "模拟 CSS 媒体类型"}, "core/sdk/sdk-meta.ts | emulateCssPrintMediaType": {"message": "模拟 CSS 打印媒体类型"}, "core/sdk/sdk-meta.ts | emulateCssScreenMediaType": {"message": "模拟 CSS 屏幕媒体类型"}, "core/sdk/sdk-meta.ts | emulateDeuteranopia": {"message": "模拟绿色盲（无法感知绿色）"}, "core/sdk/sdk-meta.ts | emulateProtanopia": {"message": "模拟红色盲（无法感知红色）"}, "core/sdk/sdk-meta.ts | emulateReducedContrast": {"message": "模拟对比度下降"}, "core/sdk/sdk-meta.ts | emulateTritanopia": {"message": "模拟蓝色盲（无法感知蓝色）"}, "core/sdk/sdk-meta.ts | emulateVisionDeficiencies": {"message": "模拟视觉缺陷"}, "core/sdk/sdk-meta.ts | enableAvifFormat": {"message": "启用 AVIF 格式"}, "core/sdk/sdk-meta.ts | enableCache": {"message": "启用缓存"}, "core/sdk/sdk-meta.ts | enableJavascript": {"message": "启用 JavaScript"}, "core/sdk/sdk-meta.ts | enableLocalFonts": {"message": "启用本地字体"}, "core/sdk/sdk-meta.ts | enableNetworkRequestBlocking": {"message": "启用网络请求屏蔽功能"}, "core/sdk/sdk-meta.ts | enableRemoteFileLoading": {"message": "允许 DevTools 从远程文件路径加载资源（比如源映射关系）。为安全起见，此功能默认处于停用状态。"}, "core/sdk/sdk-meta.ts | enableWebpFormat": {"message": "启用 WebP 格式"}, "core/sdk/sdk-meta.ts | extendGridLines": {"message": "延长网格线"}, "core/sdk/sdk-meta.ts | hideCoreWebVitalsOverlay": {"message": "隐藏核心网页指标叠加层"}, "core/sdk/sdk-meta.ts | hideFramesPerSecondFpsMeter": {"message": "隐藏每秒帧数 (FPS) 计量器"}, "core/sdk/sdk-meta.ts | hideLayerBorders": {"message": "隐藏图层边框"}, "core/sdk/sdk-meta.ts | hideLayoutShiftRegions": {"message": "隐藏布局偏移区域"}, "core/sdk/sdk-meta.ts | hideLineLabels": {"message": "隐藏网格线标签"}, "core/sdk/sdk-meta.ts | hidePaintFlashingRectangles": {"message": "隐藏突出显示的矩形绘制区域"}, "core/sdk/sdk-meta.ts | hideScrollPerformanceBottlenecks": {"message": "隐藏滚动性能瓶颈"}, "core/sdk/sdk-meta.ts | highlightAdFrames": {"message": "突出显示广告框架"}, "core/sdk/sdk-meta.ts | networkRequestBlocking": {"message": "屏蔽网络请求"}, "core/sdk/sdk-meta.ts | noEmulation": {"message": "无模拟"}, "core/sdk/sdk-meta.ts | pauseOnExceptions": {"message": "遇到异常时暂停"}, "core/sdk/sdk-meta.ts | preserveLogUponNavigation": {"message": "在浏览时保留日志"}, "core/sdk/sdk-meta.ts | print": {"message": "打印"}, "core/sdk/sdk-meta.ts | protanopia": {"message": "红色盲（无法感知红色）"}, "core/sdk/sdk-meta.ts | query": {"message": "查询"}, "core/sdk/sdk-meta.ts | reducedContrast": {"message": "对比度下降"}, "core/sdk/sdk-meta.ts | screen": {"message": "屏幕"}, "core/sdk/sdk-meta.ts | showAreaNames": {"message": "显示区域名称"}, "core/sdk/sdk-meta.ts | showCoreWebVitalsOverlay": {"message": "显示核心网页指标叠加层"}, "core/sdk/sdk-meta.ts | showFramesPerSecondFpsMeter": {"message": "显示每秒帧数 (FPS) 计量器"}, "core/sdk/sdk-meta.ts | showGridNamedAreas": {"message": "显示网格命名区域"}, "core/sdk/sdk-meta.ts | showGridTrackSizes": {"message": "显示网格轨迹大小"}, "core/sdk/sdk-meta.ts | showLayerBorders": {"message": "显示图层边框"}, "core/sdk/sdk-meta.ts | showLayoutShiftRegions": {"message": "显示布局偏移区域"}, "core/sdk/sdk-meta.ts | showLineLabels": {"message": "显示网格线标签"}, "core/sdk/sdk-meta.ts | showLineNames": {"message": "显示网格线名称"}, "core/sdk/sdk-meta.ts | showLineNumbers": {"message": "显示行号"}, "core/sdk/sdk-meta.ts | showPaintFlashingRectangles": {"message": "显示突出显示的矩形绘制区域"}, "core/sdk/sdk-meta.ts | showRulersOnHover": {"message": "在鼠标指针悬停时显示标尺"}, "core/sdk/sdk-meta.ts | showScrollPerformanceBottlenecks": {"message": "显示滚动性能瓶颈"}, "core/sdk/sdk-meta.ts | showTrackSizes": {"message": "显示轨迹大小"}, "core/sdk/sdk-meta.ts | tritanopia": {"message": "蓝色盲（无法感知蓝色）"}, "entrypoints/inspector_main/InspectorMain.ts | javascriptIsDisabled": {"message": "JavaScript 已停用"}, "entrypoints/inspector_main/InspectorMain.ts | main": {"message": "主要"}, "entrypoints/inspector_main/InspectorMain.ts | openDedicatedTools": {"message": "打开 Node.js 的专用开发者工具"}, "entrypoints/inspector_main/InspectorMain.ts | tab": {"message": "标签页"}, "entrypoints/inspector_main/OutermostTargetSelector.ts | targetNotSelected": {"message": "页面：未选择"}, "entrypoints/inspector_main/OutermostTargetSelector.ts | targetS": {"message": "页面：{PH1}"}, "entrypoints/inspector_main/RenderingOptions.ts | coreWebVitals": {"message": "核心网页指标"}, "entrypoints/inspector_main/RenderingOptions.ts | disableAvifImageFormat": {"message": "停用 AVIF 图片格式"}, "entrypoints/inspector_main/RenderingOptions.ts | disableLocalFonts": {"message": "停用本地字体"}, "entrypoints/inspector_main/RenderingOptions.ts | disableWebpImageFormat": {"message": "停用 WebP 图片格式"}, "entrypoints/inspector_main/RenderingOptions.ts | disablesLocalSourcesInFontface": {"message": "在 @font-face 规则中停用 local() 来源。需要重新加载网页才能应用。"}, "entrypoints/inspector_main/RenderingOptions.ts | emulateAFocusedPage": {"message": "模拟所聚焦的网页"}, "entrypoints/inspector_main/RenderingOptions.ts | emulateAutoDarkMode": {"message": "启用自动深色模式"}, "entrypoints/inspector_main/RenderingOptions.ts | emulatesAFocusedPage": {"message": "使页面保持聚焦状态。常用于调试消失的元素。"}, "entrypoints/inspector_main/RenderingOptions.ts | emulatesAutoDarkMode": {"message": "启用自动深色模式并将 prefers-color-scheme 设为 dark。"}, "entrypoints/inspector_main/RenderingOptions.ts | forcesCssColorgamutMediaFeature": {"message": "强制使用 CSS color-gamut 媒体功能"}, "entrypoints/inspector_main/RenderingOptions.ts | forcesCssForcedColors": {"message": "强制执行 CSS forced-colors 媒体功能"}, "entrypoints/inspector_main/RenderingOptions.ts | forcesCssPreferscolorschemeMedia": {"message": "强制使用 CSS prefers-color-scheme 媒体功能"}, "entrypoints/inspector_main/RenderingOptions.ts | forcesCssPreferscontrastMedia": {"message": "强制使用 CSS prefers-contrast 媒体功能"}, "entrypoints/inspector_main/RenderingOptions.ts | forcesCssPrefersreduceddataMedia": {"message": "强制使用 CSS prefers-reduced-data 媒体功能"}, "entrypoints/inspector_main/RenderingOptions.ts | forcesCssPrefersreducedmotion": {"message": "强制使用 CSS prefers-reduced-motion 媒体功能"}, "entrypoints/inspector_main/RenderingOptions.ts | forcesCssPrefersreducedtransparencyMedia": {"message": "强制使用 CSS prefers-reduced-transparency 媒体功能"}, "entrypoints/inspector_main/RenderingOptions.ts | forcesMediaTypeForTestingPrint": {"message": "强制采用测试打印和屏幕样式的媒体类型"}, "entrypoints/inspector_main/RenderingOptions.ts | forcesVisionDeficiencyEmulation": {"message": "强制模拟视觉缺陷"}, "entrypoints/inspector_main/RenderingOptions.ts | frameRenderingStats": {"message": "帧渲染统计信息"}, "entrypoints/inspector_main/RenderingOptions.ts | highlightAdFrames": {"message": "突出显示广告框架"}, "entrypoints/inspector_main/RenderingOptions.ts | highlightsAreasOfThePageBlueThat": {"message": "突出显示网页上偏移的区域（蓝色）。可能不适合患有光敏性癫痫的用户。"}, "entrypoints/inspector_main/RenderingOptions.ts | highlightsAreasOfThePageGreen": {"message": "突出显示需要重新绘制的网页区域（绿色）。可能不适合患有光敏性癫痫的用户。"}, "entrypoints/inspector_main/RenderingOptions.ts | highlightsElementsTealThatCan": {"message": "突出显示可能会减慢滚动速度的元素（蓝绿色），包括轻触和滚轮事件处理脚本以及其他主线程滚动情况。"}, "entrypoints/inspector_main/RenderingOptions.ts | highlightsFramesRedDetectedToBe": {"message": "突出显示被检测为广告的框架（红色）。"}, "entrypoints/inspector_main/RenderingOptions.ts | layerBorders": {"message": "图层边框"}, "entrypoints/inspector_main/RenderingOptions.ts | layoutShiftRegions": {"message": "布局偏移区域"}, "entrypoints/inspector_main/RenderingOptions.ts | paintFlashing": {"message": "突出显示绘制区域"}, "entrypoints/inspector_main/RenderingOptions.ts | plotsFrameThroughputDropped": {"message": "绘制帧吞吐量、丢帧分布和 GPU 内存。"}, "entrypoints/inspector_main/RenderingOptions.ts | requiresAPageReloadToApplyAnd": {"message": "需要重新加载网页，才能对图片请求采用和停用缓存。"}, "entrypoints/inspector_main/RenderingOptions.ts | scrollingPerformanceIssues": {"message": "滚动性能问题"}, "entrypoints/inspector_main/RenderingOptions.ts | showsAnOverlayWithCoreWebVitals": {"message": "根据核心网页指标显示叠加层。"}, "entrypoints/inspector_main/RenderingOptions.ts | showsLayerBordersOrangeoliveAnd": {"message": "显示图层边框（橙色/橄榄色）和图块（青色）。"}, "entrypoints/inspector_main/inspector_main-meta.ts | autoOpenDevTools": {"message": "为弹出式窗口自动打开 DevTools"}, "entrypoints/inspector_main/inspector_main-meta.ts | blockAds": {"message": "屏蔽此网站上的广告"}, "entrypoints/inspector_main/inspector_main-meta.ts | colorVisionDeficiency": {"message": "色觉缺陷"}, "entrypoints/inspector_main/inspector_main-meta.ts | cssMediaFeature": {"message": "CSS 媒体功能"}, "entrypoints/inspector_main/inspector_main-meta.ts | cssMediaType": {"message": "CSS 媒体类型"}, "entrypoints/inspector_main/inspector_main-meta.ts | disablePaused": {"message": "停用已暂停的状态叠加层"}, "entrypoints/inspector_main/inspector_main-meta.ts | doNotAutoOpen": {"message": "不为弹出式窗口自动打开 DevTools"}, "entrypoints/inspector_main/inspector_main-meta.ts | forceAdBlocking": {"message": "在此网站上强制屏蔽广告"}, "entrypoints/inspector_main/inspector_main-meta.ts | fps": {"message": "fps"}, "entrypoints/inspector_main/inspector_main-meta.ts | hardReloadPage": {"message": "强制重新加载网页"}, "entrypoints/inspector_main/inspector_main-meta.ts | layout": {"message": "布局"}, "entrypoints/inspector_main/inspector_main-meta.ts | paint": {"message": "绘制"}, "entrypoints/inspector_main/inspector_main-meta.ts | reloadPage": {"message": "重新加载网页"}, "entrypoints/inspector_main/inspector_main-meta.ts | rendering": {"message": "渲染"}, "entrypoints/inspector_main/inspector_main-meta.ts | showAds": {"message": "在此网站上显示广告（如果允许）"}, "entrypoints/inspector_main/inspector_main-meta.ts | showRendering": {"message": "显示“渲染”工具"}, "entrypoints/inspector_main/inspector_main-meta.ts | toggleCssPrefersColorSchemeMedia": {"message": "开启/关闭 CSS 媒体功能 prefers-color-scheme"}, "entrypoints/inspector_main/inspector_main-meta.ts | visionDeficiency": {"message": "视觉缺陷"}, "entrypoints/js_app/js_app.ts | main": {"message": "主要"}, "entrypoints/js_app/js_app.ts | networkTitle": {"message": "脚本"}, "entrypoints/js_app/js_app.ts | showNode": {"message": "显示脚本"}, "entrypoints/main/MainImpl.ts | customizeAndControlDevtools": {"message": "自定义和控制 DevTools"}, "entrypoints/main/MainImpl.ts | dockSide": {"message": "停靠侧"}, "entrypoints/main/MainImpl.ts | dockSideNaviation": {"message": "使用向左键和向右键可浏览选项"}, "entrypoints/main/MainImpl.ts | dockToBottom": {"message": "停靠至底部"}, "entrypoints/main/MainImpl.ts | dockToLeft": {"message": "停靠至左侧"}, "entrypoints/main/MainImpl.ts | dockToRight": {"message": "停靠至右侧"}, "entrypoints/main/MainImpl.ts | focusDebuggee": {"message": "焦点页面"}, "entrypoints/main/MainImpl.ts | help": {"message": "帮助"}, "entrypoints/main/MainImpl.ts | hideConsoleDrawer": {"message": "隐藏控制台抽屉栏"}, "entrypoints/main/MainImpl.ts | moreTools": {"message": "更多工具"}, "entrypoints/main/MainImpl.ts | placementOfDevtoolsRelativeToThe": {"message": "DevTools 相对于网页的位置。（按 {PH1} 即可恢复上一个位置）"}, "entrypoints/main/MainImpl.ts | showConsoleDrawer": {"message": "显示控制台抽屉栏"}, "entrypoints/main/MainImpl.ts | undockIntoSeparateWindow": {"message": "取消停靠至单独的窗口"}, "entrypoints/main/main-meta.ts | auto": {"message": "自动"}, "entrypoints/main/main-meta.ts | bottom": {"message": "底部"}, "entrypoints/main/main-meta.ts | browserLanguage": {"message": "浏览器界面语言"}, "entrypoints/main/main-meta.ts | cancelSearch": {"message": "取消搜索"}, "entrypoints/main/main-meta.ts | darkCapital": {"message": "深色"}, "entrypoints/main/main-meta.ts | darkLower": {"message": "深色"}, "entrypoints/main/main-meta.ts | devtoolsDefault": {"message": "DevTools（默认）"}, "entrypoints/main/main-meta.ts | dockToBottom": {"message": "停靠至底部"}, "entrypoints/main/main-meta.ts | dockToLeft": {"message": "停靠至左侧"}, "entrypoints/main/main-meta.ts | dockToRight": {"message": "停靠至右侧"}, "entrypoints/main/main-meta.ts | enableCtrlShortcutToSwitchPanels": {"message": "启用 Ctrl + 1-9 快捷键切换面板"}, "entrypoints/main/main-meta.ts | enableShortcutToSwitchPanels": {"message": "启用 ⌘ + 1-9 快捷键切换面板"}, "entrypoints/main/main-meta.ts | enableSync": {"message": "启用设置同步"}, "entrypoints/main/main-meta.ts | findNextResult": {"message": "查找下一个结果"}, "entrypoints/main/main-meta.ts | findPreviousResult": {"message": "查找上一个结果"}, "entrypoints/main/main-meta.ts | focusDebuggee": {"message": "焦点页面"}, "entrypoints/main/main-meta.ts | horizontal": {"message": "横向"}, "entrypoints/main/main-meta.ts | language": {"message": "语言："}, "entrypoints/main/main-meta.ts | left": {"message": "左侧"}, "entrypoints/main/main-meta.ts | lightCapital": {"message": "浅色"}, "entrypoints/main/main-meta.ts | lightLower": {"message": "浅色"}, "entrypoints/main/main-meta.ts | nextPanel": {"message": "下一个面板"}, "entrypoints/main/main-meta.ts | panelLayout": {"message": "面板布局："}, "entrypoints/main/main-meta.ts | previousPanel": {"message": "上一个面板"}, "entrypoints/main/main-meta.ts | reloadDevtools": {"message": "重新加载 DevTools"}, "entrypoints/main/main-meta.ts | resetZoomLevel": {"message": "重置缩放级别"}, "entrypoints/main/main-meta.ts | restoreLastDockPosition": {"message": "恢复上一个停靠位置"}, "entrypoints/main/main-meta.ts | right": {"message": "右侧"}, "entrypoints/main/main-meta.ts | searchAsYouTypeCommand": {"message": "启用即输即搜功能"}, "entrypoints/main/main-meta.ts | searchAsYouTypeSetting": {"message": "即输即搜"}, "entrypoints/main/main-meta.ts | searchInPanel": {"message": "在面板中搜索"}, "entrypoints/main/main-meta.ts | searchOnEnterCommand": {"message": "停用即输即搜功能（按 Enter 键即可搜索）"}, "entrypoints/main/main-meta.ts | switchToDarkTheme": {"message": "切换到深色主题"}, "entrypoints/main/main-meta.ts | switchToLightTheme": {"message": "切换到浅色主题"}, "entrypoints/main/main-meta.ts | switchToSystemPreferredColor": {"message": "切换到系统首选颜色主题"}, "entrypoints/main/main-meta.ts | systemPreference": {"message": "系统偏好设置"}, "entrypoints/main/main-meta.ts | theme": {"message": "主题："}, "entrypoints/main/main-meta.ts | toggleDrawer": {"message": "显示/隐藏抽屉栏"}, "entrypoints/main/main-meta.ts | undockIntoSeparateWindow": {"message": "取消停靠至单独的窗口"}, "entrypoints/main/main-meta.ts | undocked": {"message": "已取消停靠"}, "entrypoints/main/main-meta.ts | useAutomaticPanelLayout": {"message": "使用自动面板布局"}, "entrypoints/main/main-meta.ts | useHorizontalPanelLayout": {"message": "使用水平面板布局"}, "entrypoints/main/main-meta.ts | useVerticalPanelLayout": {"message": "使用垂直面板布局"}, "entrypoints/main/main-meta.ts | vertical": {"message": "纵向"}, "entrypoints/main/main-meta.ts | zoomIn": {"message": "放大"}, "entrypoints/main/main-meta.ts | zoomOut": {"message": "缩小"}, "entrypoints/node_app/NodeConnectionsPanel.ts | addConnection": {"message": "添加网络连接"}, "entrypoints/node_app/NodeConnectionsPanel.ts | networkAddressEgLocalhost": {"message": "网络地址（例如，localhost:9229）"}, "entrypoints/node_app/NodeConnectionsPanel.ts | noConnectionsSpecified": {"message": "未指定连接"}, "entrypoints/node_app/NodeConnectionsPanel.ts | nodejsDebuggingGuide": {"message": "Node.js 调试指南"}, "entrypoints/node_app/NodeConnectionsPanel.ts | specifyNetworkEndpointAnd": {"message": "只要您指定网络端点，DevTools 就会自动连接到该端点。如需了解详情，请阅读 {PH1}。"}, "entrypoints/node_app/NodeMain.ts | main": {"message": "主要"}, "entrypoints/node_app/NodeMain.ts | nodejsS": {"message": "Node.js：{PH1}"}, "entrypoints/node_app/node_app.ts | connection": {"message": "网络连接"}, "entrypoints/node_app/node_app.ts | networkTitle": {"message": "节点"}, "entrypoints/node_app/node_app.ts | node": {"message": "节点"}, "entrypoints/node_app/node_app.ts | showConnection": {"message": "显示“连接”"}, "entrypoints/node_app/node_app.ts | showNode": {"message": "显示节点"}, "entrypoints/worker_app/WorkerMain.ts | main": {"message": "主要"}, "generated/Deprecation.ts | AuthorizationCoveredByWildcard": {"message": "处理 CORS Access-Control-Allow-Headers 时，授权将不在通配符 (*) 的涵盖范围内。"}, "generated/Deprecation.ts | CSSCustomStateDeprecatedSyntax": {"message": ":--customstatename 已被弃用。请改用 :state(customstatename) 语法。"}, "generated/Deprecation.ts | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "若要停用默认 Cast 集成，应使用 disableRemotePlayback 属性，而非 -internal-media-controls-overlay-cast-button 选择器。"}, "generated/Deprecation.ts | CSSValueAppearanceNonStandard": {"message": "CSS appearance 值 inner-spin-button、media-slider、media-sliderthumb、media-volume-slider、media-volume-sliderthumb、push-button、searchfield-cancel-button、slider-horizontal、sliderthumb-horizontal、sliderthumb-vertical、square-button 未标准化，将会被移除。"}, "generated/Deprecation.ts | CSSValueAppearanceSliderVertical": {"message": "CSS appearance 值 slider-vertical 未标准化，将会被移除。"}, "generated/Deprecation.ts | CanRequestURLHTTPContainingNewline": {"message": "如果对应的网址同时包含已移除的空白字符 \\(n|r|t) 和小于字符 (<)，资源请求会被屏蔽。请从元素属性值等位置移除换行符并编码小于字符，以便加载这些资源。"}, "generated/Deprecation.ts | ChromeLoadTimesConnectionInfo": {"message": "chrome.loadTimes() 已被弃用，请改用标准化 API：Navigation Timing 2。"}, "generated/Deprecation.ts | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "chrome.loadTimes() 已被弃用，请改用标准化 API：Paint Timing。"}, "generated/Deprecation.ts | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "chrome.loadTimes() 已被弃用，请改用标准化 API：Navigation Timing 2 中的 nextHopProtocol。"}, "generated/Deprecation.ts | CookieWithTruncatingChar": {"message": "包含 \\(0|r|n) 字符的 Cookie 将被拒，而不是被截断。"}, "generated/Deprecation.ts | CrossOriginAccessBasedOnDocumentDomain": {"message": "通过设置 document.domain 放宽同源政策的功能已被弃用，并将默认处于停用状态。此弃用警告针对的是通过设置 document.domain 启用的跨源访问。"}, "generated/Deprecation.ts | CrossOriginWindowAlert": {"message": "从跨源 iframe 触发 window.alert 的功能已被弃用，日后将被移除。"}, "generated/Deprecation.ts | CrossOriginWindowConfirm": {"message": "从跨源 iframe 触发 window.confirm 的功能已被弃用，日后将被移除。"}, "generated/Deprecation.ts | DOMMutationEvents": {"message": "DOM 变更事件（包括 DOMSubtreeModified、DOMNodeInserted、DOMNodeRemoved、DOMNodeRemovedFromDocument、DOMNodeInsertedIntoDocument 和 DOMCharacterDataModified，详见 https://w3c.github.io/uievents/#legacy-event-types）已被弃用，并将被移除。请改用 MutationObserver。"}, "generated/Deprecation.ts | DataUrlInSvgUse": {"message": "对 SVGUseElement 中 data: URL 的支持已被弃用，日后将被移除。"}, "generated/Deprecation.ts | DocumentDomainSettingWithoutOriginAgentClusterHeader": {"message": "通过设置 document.domain 放宽同源政策的功能已被弃用，并将默认处于停用状态。若要继续使用此功能，请通过发送 Origin-Agent-Cluster: ?0 标头以及文档和框架的 HTTP 响应来选择停用以源为键的代理集群。如需了解详情，请访问 https://developer.chrome.com/blog/immutable-document-domain/。"}, "generated/Deprecation.ts | GeolocationInsecureOrigin": {"message": "getCurrentPosition() 和 watchPosition() 不再适用于不安全的源。若要使用此功能，您应考虑将您的应用转移到安全的源，例如 HTTPS。如需了解详情，请访问 https://goo.gle/chrome-insecure-origins。"}, "generated/Deprecation.ts | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "getCurrentPosition() 和 watchPosition() 不再适用于不安全的源。若要使用此功能，您应考虑将您的应用转移到安全的源，例如 HTTPS。如需了解详情，请访问 https://goo.gle/chrome-insecure-origins。"}, "generated/Deprecation.ts | GetInnerHTML": {"message": "getInnerHTML() 函数已被弃用，很快将会从此浏览器中移除。请改用 getHTML()。"}, "generated/Deprecation.ts | GetUserMediaInsecureOrigin": {"message": "getUserMedia() 不再适用于不安全的源。若要使用此功能，您应考虑将您的应用转移到安全的源，例如 HTTPS。如需了解详情，请访问 https://goo.gle/chrome-insecure-origins。"}, "generated/Deprecation.ts | HostCandidateAttributeGetter": {"message": "RTCPeerConnectionIceErrorEvent.hostCandidate 已被弃用。请改用 RTCPeerConnectionIceErrorEvent.address 或 RTCPeerConnectionIceErrorEvent.port。"}, "generated/Deprecation.ts | IdentityInCanMakePaymentEvent": {"message": "canmakepayment Service Worker 事件中的商家源和任意数据已被弃用，并将被移除：topOrigin、paymentRequestOrigin、methodData、modifiers。"}, "generated/Deprecation.ts | InsecurePrivateNetworkSubresourceRequest": {"message": "该网站向网络请求了一项子资源，而且仅仅因为其用户的特权网络位置而能够访问此项资源。此类请求会向互联网公开非公用设备和服务器，这会增加跨站请求伪造 (CSRF) 攻击和/或信息泄露的风险。为降低这类风险，Chrome 不再支持从非安全上下文发起针对非公用子资源的请求，并将开始阻止此类请求。"}, "generated/Deprecation.ts | InterestGroupDailyUpdateUrl": {"message": "向 joinAdInterestGroup() 传递的 InterestGroups 所含 dailyUpdateUrl 字段已被重命名为 updateUrl，以便更准确地反映其行为。"}, "generated/Deprecation.ts | LocalCSSFileExtensionRejected": {"message": "无法从 file: 网址加载 CSS，除非它们以 .css 文件扩展名结尾。"}, "generated/Deprecation.ts | MediaSourceAbortRemove": {"message": "由于规范变更，使用 SourceBuffer.abort() 中止 remove() 的异步范围移除的功能已被弃用。日后我们会移除相应支持。您应改为监听 updateend 事件。abort() 只应用于中止异步媒体附加或重置解析状态。"}, "generated/Deprecation.ts | MediaSourceDurationTruncatingBuffered": {"message": "由于规范变更，我们不再支持将 MediaSource.duration 设为低于任何缓冲编码帧的最高呈现时间戳。日后我们将不再支持隐式移除被截断的缓冲媒体。您应改为对 newDuration < oldDuration 的所有 sourceBuffers 执行显式 remove(newDuration, oldDuration)。"}, "generated/Deprecation.ts | NoSysexWebMIDIWithoutPermission": {"message": "即使 MIDIOptions 中未指定 sysex，Web MIDI 也会请求获得使用许可。"}, "generated/Deprecation.ts | NotificationInsecureOrigin": {"message": "无法再从不安全的源使用 Notification API。您应考虑将您的应用转移到安全的源，例如 HTTPS。如需了解详情，请访问 https://goo.gle/chrome-insecure-origins。"}, "generated/Deprecation.ts | NotificationPermissionRequestedIframe": {"message": "无法再从跨源 iframe 中请求 Notification API 权限。您应考虑改为从顶级框架中请求权限，或者打开一个新窗口。"}, "generated/Deprecation.ts | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap 中的 imageOrientation: 'none' 选项已被弃用。请改用带有 {imageOrientation: 'from-image'} 选项的 createImageBitmap。"}, "generated/Deprecation.ts | ObsoleteWebRtcCipherSuite": {"message": "您的合作伙伴正在协商某个已过时的 (D)TLS 版本。请与您的合作伙伴联系，以解决此问题。"}, "generated/Deprecation.ts | OverflowVisibleOnReplacedElement": {"message": "为 img、video 和 canvas 标记指定 overflow: visible 可能会导致这些标记在元素边界之外生成视觉内容。请参阅 https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md。"}, "generated/Deprecation.ts | PaymentInstruments": {"message": "paymentManager.instruments 已被弃用。请改用即时安装方式安装付款处理程序。"}, "generated/Deprecation.ts | PaymentRequestCSPViolation": {"message": "您的 PaymentRequest 调用已绕过内容安全政策 (CSP) connect-src 指令。此绕过方式已被弃用。请将 PaymentRequest API 中的付款方式标识符（在 supportedMethods 字段中）添加到 CSP connect-src 指令中。"}, "generated/Deprecation.ts | PersistentQuotaType": {"message": "StorageType.persistent 已被弃用。请改用标准化 navigator.storage。"}, "generated/Deprecation.ts | PictureSourceSrc": {"message": "带有 <picture> 父级的 <source src> 无效，因此会被忽略。请改用 <source srcset>。"}, "generated/Deprecation.ts | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame 因供应商而异。请改用标准 cancelAnimationFrame。"}, "generated/Deprecation.ts | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame 因供应商而异。请改用标准 requestAnimationFrame。"}, "generated/Deprecation.ts | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen 已被弃用。请改用 Document.fullscreenElement。"}, "generated/Deprecation.ts | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() 已被弃用。请改用 Element.requestFullscreen()。"}, "generated/Deprecation.ts | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() 已被弃用。请改用 Element.requestFullscreen()。"}, "generated/Deprecation.ts | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() 已被弃用。请改用 Document.exitFullscreen()。"}, "generated/Deprecation.ts | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() 已被弃用。请改用 Document.exitFullscreen()。"}, "generated/Deprecation.ts | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen 已被弃用。请改用 Document.fullscreenEnabled。"}, "generated/Deprecation.ts | PrivacySandboxExtensionsAPI": {"message": "我们即将弃用 API chrome.privacy.websites.privacySandboxEnabled，但为了保持向后兼容性，该 API 可持续使用到 M113 版。届时，请改用 chrome.privacy.websites.topicsEnabled、chrome.privacy.websites.fledgeEnabled 和 chrome.privacy.websites.adMeasurementEnabled。请参阅 https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled。"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpFalse": {"message": "约束条件 DtlsSrtpKeyAgreement 已被移除。您已为此约束条件指定 false 值，系统会将这种情况解读为尝试使用已被移除的 SDES key negotiation 方法。此功能已被移除；请改用支持 DTLS key negotiation的服务。"}, "generated/Deprecation.ts | RTCConstraintEnableDtlsSrtpTrue": {"message": "约束条件 DtlsSrtpKeyAgreement 已被移除。您已为此约束条件指定 true 值，这没有任何作用，但为整洁起见，您可以移除此约束条件。"}, "generated/Deprecation.ts | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "基于回调的 getStats() 已被弃用，并将被移除。请改用符合规范的 getStats()。"}, "generated/Deprecation.ts | RangeExpand": {"message": "Range.expand() 已被弃用。请改用 Selection.modify()。"}, "generated/Deprecation.ts | RequestedSubresourceWithEmbeddedCredentials": {"message": "如果对应的网址包含嵌入式凭据（例如 **********************/），子资源请求会被屏蔽。"}, "generated/Deprecation.ts | RtcpMuxPolicyNegotiate": {"message": "rtcpMuxPolicy 选项已被弃用，并将被移除。"}, "generated/Deprecation.ts | SharedArrayBufferConstructedWithoutIsolation": {"message": "SharedArrayBuffer 将要求进行跨域隔离。如需了解详情，请访问 https://developer.chrome.com/blog/enabling-shared-array-buffer/。"}, "generated/Deprecation.ts | TextToSpeech_DisallowedByAutoplay": {"message": "无需用户激活的 speechSynthesis.speak() 已被弃用，并将被移除。"}, "generated/Deprecation.ts | UnloadHandler": {"message": "卸载事件监听器已被弃用，并且将被移除。"}, "generated/Deprecation.ts | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "扩展程序应选择启用跨域隔离，以便继续使用 SharedArrayBuffer。请参阅 https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/。"}, "generated/Deprecation.ts | WebSQL": {"message": "Web SQL 已被弃用。请使用 SQLite WebAssembly 或 Indexed Database"}, "generated/Deprecation.ts | XHRJSONEncodingDetection": {"message": "XMLHttpRequest 中的响应 JSON 不再支持 UTF-16"}, "generated/Deprecation.ts | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "主线程上的同步 XMLHttpRequest 已被弃用，因为它会对最终用户的体验产生不利影响。如需更多帮助，请访问 https://xhr.spec.whatwg.org/。"}, "generated/Deprecation.ts | XRSupportsSession": {"message": "supportsSession() 已被弃用。请改用 isSessionSupported() 并查看已解析的布尔值。"}, "models/bindings/ContentProviderBasedProject.ts | unknownErrorLoadingFile": {"message": "加载文件时发生未知错误"}, "models/bindings/DebuggerLanguagePlugins.ts | debugSymbolsIncomplete": {"message": "函数“{PH1}”的调试信息不完整"}, "models/bindings/DebuggerLanguagePlugins.ts | errorInDebuggerLanguagePlugin": {"message": "调试程序语言插件发生错误：{PH1}"}, "models/bindings/DebuggerLanguagePlugins.ts | failedToLoadDebugSymbolsFor": {"message": "[{PH1}] 无法加载 {PH2} 的调试符号（{PH3}）"}, "models/bindings/DebuggerLanguagePlugins.ts | failedToLoadDebugSymbolsForFunction": {"message": "未找到函数“{PH1}”的调试信息"}, "models/bindings/DebuggerLanguagePlugins.ts | loadedDebugSymbolsForButDidnt": {"message": "[{PH1}] 已加载 {PH2} 的调试符号，但找不到任何源文件"}, "models/bindings/DebuggerLanguagePlugins.ts | loadedDebugSymbolsForFound": {"message": "[{PH1}] 已加载 {PH2} 的调试符号，找到了 {PH3} 个源文件"}, "models/bindings/DebuggerLanguagePlugins.ts | loadingDebugSymbolsFor": {"message": "[{PH1}] 正在加载 {PH2} 的调试符号…"}, "models/bindings/DebuggerLanguagePlugins.ts | loadingDebugSymbolsForVia": {"message": "[{PH1}] 正在通过 {PH3} 加载 {PH2} 的调试符号…"}, "models/bindings/IgnoreListManager.ts | addAllContentScriptsToIgnoreList": {"message": "将所有扩展程序脚本添加到忽略列表"}, "models/bindings/IgnoreListManager.ts | addAllThirdPartyScriptsToIgnoreList": {"message": "将所有第三方脚本添加到忽略列表"}, "models/bindings/IgnoreListManager.ts | addDirectoryToIgnoreList": {"message": "将目录添加到忽略列表"}, "models/bindings/IgnoreListManager.ts | addScriptToIgnoreList": {"message": "向忽略列表添加脚本"}, "models/bindings/IgnoreListManager.ts | removeFromIgnoreList": {"message": "从忽略列表中移除"}, "models/bindings/ResourceScriptMapping.ts | liveEditCompileFailed": {"message": "LiveEdit 编译失败：{PH1}"}, "models/bindings/ResourceScriptMapping.ts | liveEditFailed": {"message": "LiveEdit 失败：{PH1}"}, "models/emulation/DeviceModeModel.ts | devicePixelRatioMustBeANumberOr": {"message": "设备像素比必须为数字或为空。"}, "models/emulation/DeviceModeModel.ts | devicePixelRatioMustBeGreater": {"message": "设备像素比必须大于或等于 {PH1}。"}, "models/emulation/DeviceModeModel.ts | devicePixelRatioMustBeLessThanOr": {"message": "设备像素比必须小于或等于 {PH1}。"}, "models/emulation/DeviceModeModel.ts | heightCannotBeEmpty": {"message": "高度不能为空。"}, "models/emulation/DeviceModeModel.ts | heightMustBeANumber": {"message": "高度必须是数字。"}, "models/emulation/DeviceModeModel.ts | heightMustBeGreaterThanOrEqualTo": {"message": "高度必须大于或等于 {PH1}。"}, "models/emulation/DeviceModeModel.ts | heightMustBeLessThanOrEqualToS": {"message": "高度必须小于或等于 {PH1}。"}, "models/emulation/DeviceModeModel.ts | widthCannotBeEmpty": {"message": "宽度不能为空。"}, "models/emulation/DeviceModeModel.ts | widthMustBeANumber": {"message": "宽度必须是数字。"}, "models/emulation/DeviceModeModel.ts | widthMustBeGreaterThanOrEqualToS": {"message": "宽度必须大于或等于 {PH1}。"}, "models/emulation/DeviceModeModel.ts | widthMustBeLessThanOrEqualToS": {"message": "宽度必须小于或等于 {PH1}。"}, "models/emulation/EmulatedDevices.ts | laptopWithHiDPIScreen": {"message": "配有 HiDPI 屏幕的笔记本电脑"}, "models/emulation/EmulatedDevices.ts | laptopWithMDPIScreen": {"message": "配有 MDPI 屏幕的笔记本电脑"}, "models/emulation/EmulatedDevices.ts | laptopWithTouch": {"message": "配有触控装置的笔记本电脑"}, "models/har/Writer.ts | collectingContent": {"message": "正在收集内容…"}, "models/har/Writer.ts | writingFile": {"message": "正在写入文件…"}, "models/issues_manager/BounceTrackingIssue.ts | bounceTrackingMitigations": {"message": "跳出跟踪缓解措施"}, "models/issues_manager/ClientHintIssue.ts | clientHintsInfrastructure": {"message": "客户端提示基础架构"}, "models/issues_manager/ContentSecurityPolicyIssue.ts | contentSecurityPolicyEval": {"message": "内容安全政策 - Eval"}, "models/issues_manager/ContentSecurityPolicyIssue.ts | contentSecurityPolicyInlineCode": {"message": "内容安全政策 - 内嵌代码"}, "models/issues_manager/ContentSecurityPolicyIssue.ts | contentSecurityPolicySource": {"message": "内容安全政策 - 来源许可名单"}, "models/issues_manager/ContentSecurityPolicyIssue.ts | trustedTypesFixViolations": {"message": "Trusted Types - 修正违规问题"}, "models/issues_manager/ContentSecurityPolicyIssue.ts | trustedTypesPolicyViolation": {"message": "Trusted Types - 违反政策"}, "models/issues_manager/CookieDeprecationMetadataIssue.ts | thirdPartyPhaseoutExplained": {"message": "准备好逐步停用第三方 Cookie"}, "models/issues_manager/CookieIssue.ts | aSecure": {"message": "安全的"}, "models/issues_manager/CookieIssue.ts | anInsecure": {"message": "不安全的"}, "models/issues_manager/CookieIssue.ts | consoleTpcdErrorMessage": {"message": "Privacy Sandbox 已在 Chrome 中屏蔽第三方 Cookie。"}, "models/issues_manager/CookieIssue.ts | consoleTpcdWarningMessage": {"message": "在未来的 Chrome 版本中，Privacy Sandbox 将屏蔽第三方 Cookie。"}, "models/issues_manager/CookieIssue.ts | fileCrosSiteRedirectBug": {"message": "提交错误"}, "models/issues_manager/CookieIssue.ts | firstPartySetsExplained": {"message": "First-Party Sets 和 SameParty 属性"}, "models/issues_manager/CookieIssue.ts | howSchemefulSamesiteWorks": {"message": "Schemeful Same-Site 的运作方式"}, "models/issues_manager/CookieIssue.ts | samesiteCookiesExplained": {"message": "SameSite Cookie 说明"}, "models/issues_manager/CookieIssue.ts | thirdPartyPhaseoutExplained": {"message": "准备好逐步停用第三方 Cookie"}, "models/issues_manager/CorsIssue.ts | CORS": {"message": "跨域资源共享 (CORS)"}, "models/issues_manager/CorsIssue.ts | corsPrivateNetworkAccess": {"message": "专用网络访问"}, "models/issues_manager/CrossOriginEmbedderPolicyIssue.ts | coopAndCoep": {"message": "COOP 和 COEP"}, "models/issues_manager/CrossOriginEmbedderPolicyIssue.ts | samesiteAndSameorigin": {"message": "Same-Site 和 Same-Origin"}, "models/issues_manager/DeprecationIssue.ts | feature": {"message": "如需了解详情，请参阅功能状态页面。"}, "models/issues_manager/DeprecationIssue.ts | milestone": {"message": "此变更将从里程碑 {milestone} 生效。"}, "models/issues_manager/DeprecationIssue.ts | title": {"message": "使用了已弃用的功能"}, "models/issues_manager/FederatedAuthRequestIssue.ts | fedCm": {"message": "Federated Credential Management API"}, "models/issues_manager/FederatedAuthUserInfoRequestIssue.ts | fedCmUserInfo": {"message": "Federated Credential Management User Info API"}, "models/issues_manager/GenericIssue.ts | autocompleteAttributePageTitle": {"message": "HTML 属性：自动补全"}, "models/issues_manager/GenericIssue.ts | corbExplainerPageTitle": {"message": "CORB 解释器"}, "models/issues_manager/GenericIssue.ts | crossOriginPortalPostMessage": {"message": "门户 - 同源通信渠道"}, "models/issues_manager/GenericIssue.ts | howDoesAutofillWorkPageTitle": {"message": "自动填充功能是如何运作的？"}, "models/issues_manager/GenericIssue.ts | inputFormElementPageTitle": {"message": "表单输入元素"}, "models/issues_manager/GenericIssue.ts | labelFormlementsPageTitle": {"message": "标签元素"}, "models/issues_manager/HeavyAdIssue.ts | handlingHeavyAdInterventions": {"message": "针对消耗过多资源的广告的干预措施"}, "models/issues_manager/Issue.ts | breakingChangeIssue": {"message": "破坏性更改问题：网页在即将发布的 Chrome 版本中可能无法正常运行"}, "models/issues_manager/Issue.ts | breakingChanges": {"message": "重大变更"}, "models/issues_manager/Issue.ts | improvementIssue": {"message": "可改进的问题：网页有改进空间"}, "models/issues_manager/Issue.ts | improvements": {"message": "改进"}, "models/issues_manager/Issue.ts | pageErrorIssue": {"message": "网页错误问题：网页无法正常运行"}, "models/issues_manager/Issue.ts | pageErrors": {"message": "网页错误"}, "models/issues_manager/LowTextContrastIssue.ts | colorAndContrastAccessibility": {"message": "颜色和对比度无障碍功能"}, "models/issues_manager/MixedContentIssue.ts | preventingMixedContent": {"message": "防止混合内容"}, "models/issues_manager/QuirksModeIssue.ts | documentCompatibilityMode": {"message": "文档兼容模式"}, "models/issues_manager/SharedArrayBufferIssue.ts | enablingSharedArrayBuffer": {"message": "启用 SharedArrayBuffer"}, "models/issues_manager/SharedDictionaryIssue.ts | compressionDictionaryTransport": {"message": "压缩字典传输"}, "models/logs/NetworkLog.ts | anonymous": {"message": "<匿名>"}, "models/logs/logs-meta.ts | clear": {"message": "清除"}, "models/logs/logs-meta.ts | doNotPreserveLogOnPageReload": {"message": "重新加载/浏览网页时不保留日志"}, "models/logs/logs-meta.ts | preserve": {"message": "保留"}, "models/logs/logs-meta.ts | preserveLog": {"message": "保留日志"}, "models/logs/logs-meta.ts | preserveLogOnPageReload": {"message": "重新加载/浏览网页时保留日志"}, "models/logs/logs-meta.ts | recordNetworkLog": {"message": "录制网络日志"}, "models/logs/logs-meta.ts | reset": {"message": "重置"}, "models/persistence/EditFileSystemView.ts | add": {"message": "添加"}, "models/persistence/EditFileSystemView.ts | enterAPath": {"message": "输入路径"}, "models/persistence/EditFileSystemView.ts | enterAUniquePath": {"message": "请输入唯一路径"}, "models/persistence/EditFileSystemView.ts | excludedFolders": {"message": "排除的文件夹"}, "models/persistence/EditFileSystemView.ts | folderPath": {"message": "文件夹路径"}, "models/persistence/EditFileSystemView.ts | none": {"message": "无"}, "models/persistence/EditFileSystemView.ts | sViaDevtools": {"message": "{PH1}（通过 .devtools）"}, "models/persistence/IsolatedFileSystem.ts | blobCouldNotBeLoaded": {"message": "无法加载 blob。"}, "models/persistence/IsolatedFileSystem.ts | cantReadFileSS": {"message": "无法读取文件：{PH1} - {PH2}"}, "models/persistence/IsolatedFileSystem.ts | fileSystemErrorS": {"message": "文件系统错误：{PH1}"}, "models/persistence/IsolatedFileSystem.ts | linkedToS": {"message": "已链接到 {PH1}"}, "models/persistence/IsolatedFileSystemManager.ts | unableToAddFilesystemS": {"message": "无法添加文件系统：{PH1}"}, "models/persistence/PersistenceActions.ts | openInContainingFolder": {"message": "在包含此文件的文件夹中打开"}, "models/persistence/PersistenceActions.ts | overrideContent": {"message": "替换内容"}, "models/persistence/PersistenceActions.ts | overrideSourceMappedFileExplanation": {"message": "“{PH1}”是源代码映射文件，无法替换。"}, "models/persistence/PersistenceActions.ts | overrideSourceMappedFileWarning": {"message": "改为替换“{PH1}”？"}, "models/persistence/PersistenceActions.ts | saveAs": {"message": "另存为…"}, "models/persistence/PersistenceActions.ts | saveImage": {"message": "保存图片"}, "models/persistence/PersistenceActions.ts | saveWasmFailed": {"message": "无法将 WASM 模块保存到磁盘。很可能是模块太大。"}, "models/persistence/PersistenceActions.ts | showOverrides": {"message": "显示所有替换项"}, "models/persistence/PersistenceUtils.ts | linkedToS": {"message": "已链接到 {PH1}"}, "models/persistence/PersistenceUtils.ts | linkedToSourceMapS": {"message": "已链接到来源映射的网址：{PH1}"}, "models/persistence/PlatformFileSystem.ts | unableToReadFilesWithThis": {"message": "PlatformFileSystem 无法读取文件。"}, "models/persistence/WorkspaceSettingsTab.ts | addFolder": {"message": "添加文件夹…"}, "models/persistence/WorkspaceSettingsTab.ts | folderExcludePattern": {"message": "文件夹排除模式"}, "models/persistence/WorkspaceSettingsTab.ts | mappingsAreInferredAutomatically": {"message": "映射是系统自动推断出的。"}, "models/persistence/WorkspaceSettingsTab.ts | remove": {"message": "移除"}, "models/persistence/WorkspaceSettingsTab.ts | workspace": {"message": "工作区"}, "models/persistence/persistence-meta.ts | disableOverrideNetworkRequests": {"message": "禁止替换网络请求"}, "models/persistence/persistence-meta.ts | enableLocalOverrides": {"message": "启用本地替换"}, "models/persistence/persistence-meta.ts | enableOverrideNetworkRequests": {"message": "启用覆盖网络请求"}, "models/persistence/persistence-meta.ts | interception": {"message": "拦截"}, "models/persistence/persistence-meta.ts | network": {"message": "网络"}, "models/persistence/persistence-meta.ts | override": {"message": "override"}, "models/persistence/persistence-meta.ts | request": {"message": "请求"}, "models/persistence/persistence-meta.ts | rewrite": {"message": "重写"}, "models/persistence/persistence-meta.ts | showWorkspace": {"message": "显示工作区设置"}, "models/persistence/persistence-meta.ts | workspace": {"message": "工作区"}, "models/timeline_model/TimelineJSProfile.ts | threadS": {"message": "线程 {PH1}"}, "models/workspace/UISourceCode.ts | index": {"message": "（索引）"}, "models/workspace/UISourceCode.ts | thisFileWasChangedExternally": {"message": "此文件已在外部发生更改。是否要重新加载？"}, "panels/accessibility/ARIAAttributesView.ts | ariaAttributes": {"message": "ARIA 属性"}, "panels/accessibility/ARIAAttributesView.ts | noAriaAttributes": {"message": "没有任何 ARIA 属性"}, "panels/accessibility/AXBreadcrumbsPane.ts | accessibilityTree": {"message": "无障碍功能树"}, "panels/accessibility/AXBreadcrumbsPane.ts | fullTreeExperimentDescription": {"message": "无障碍功能树已移至 DOM 树的右上角。"}, "panels/accessibility/AXBreadcrumbsPane.ts | fullTreeExperimentName": {"message": "启用整页模式的无障碍功能树"}, "panels/accessibility/AXBreadcrumbsPane.ts | ignored": {"message": "已忽略"}, "panels/accessibility/AXBreadcrumbsPane.ts | reloadRequired": {"message": "需要重新加载才能使更改生效。"}, "panels/accessibility/AXBreadcrumbsPane.ts | scrollIntoView": {"message": "滚动到视野范围内"}, "panels/accessibility/AccessibilityNodeView.ts | accessibilityNodeNotExposed": {"message": "未公开无障碍功能节点"}, "panels/accessibility/AccessibilityNodeView.ts | ancestorChildrenAreAll": {"message": "祖先的后代均为展示性元素："}, "panels/accessibility/AccessibilityNodeView.ts | computedProperties": {"message": "计算后的属性"}, "panels/accessibility/AccessibilityNodeView.ts | elementHasEmptyAltText": {"message": "元素包含空的替代文本。"}, "panels/accessibility/AccessibilityNodeView.ts | elementHasPlaceholder": {"message": "元素包含 {PH1}。"}, "panels/accessibility/AccessibilityNodeView.ts | elementIsHiddenBy": {"message": "元素已被正在使用的模态对话框隐藏："}, "panels/accessibility/AccessibilityNodeView.ts | elementIsHiddenByChildTree": {"message": "元素被子树隐藏："}, "panels/accessibility/AccessibilityNodeView.ts | elementIsInAnInertSubTree": {"message": "此元素位于具有 inert 属性的子树中："}, "panels/accessibility/AccessibilityNodeView.ts | elementIsInert": {"message": "元素为 inert。"}, "panels/accessibility/AccessibilityNodeView.ts | elementIsNotRendered": {"message": "元素未渲染。"}, "panels/accessibility/AccessibilityNodeView.ts | elementIsNotVisible": {"message": "元素不可见。"}, "panels/accessibility/AccessibilityNodeView.ts | elementIsPlaceholder": {"message": "元素为 {PH1}。"}, "panels/accessibility/AccessibilityNodeView.ts | elementIsPresentational": {"message": "这是一个演示性元素。"}, "panels/accessibility/AccessibilityNodeView.ts | elementNotInteresting": {"message": "无障碍功能引擎不感兴趣的元素。"}, "panels/accessibility/AccessibilityNodeView.ts | elementsInheritsPresentational": {"message": "元素从以下项目继承展示性角色："}, "panels/accessibility/AccessibilityNodeView.ts | invalidSource": {"message": "来源无效。"}, "panels/accessibility/AccessibilityNodeView.ts | labelFor": {"message": "标签所属元素"}, "panels/accessibility/AccessibilityNodeView.ts | noAccessibilityNode": {"message": "没有无障碍功能节点"}, "panels/accessibility/AccessibilityNodeView.ts | noNodeWithThisId": {"message": "未找到此 ID 对应的节点。"}, "panels/accessibility/AccessibilityNodeView.ts | noTextContent": {"message": "无文本内容。"}, "panels/accessibility/AccessibilityNodeView.ts | notSpecified": {"message": "未指定"}, "panels/accessibility/AccessibilityNodeView.ts | partOfLabelElement": {"message": "属于标签元素："}, "panels/accessibility/AccessibilityNodeView.ts | placeholderIsPlaceholderOnAncestor": {"message": "在祖先节点上 {PH1} 为 {PH2}："}, "panels/accessibility/AccessibilityStrings.ts | aHumanreadableVersionOfTheValue": {"message": "简单易懂的范围微件值（如有必要）。"}, "panels/accessibility/AccessibilityStrings.ts | activeDescendant": {"message": "活跃后代"}, "panels/accessibility/AccessibilityStrings.ts | atomicLiveRegions": {"message": "Atomic（动态区域）"}, "panels/accessibility/AccessibilityStrings.ts | busyLiveRegions": {"message": "Busy（动态区域）"}, "panels/accessibility/AccessibilityStrings.ts | canSetValue": {"message": "可以设置值"}, "panels/accessibility/AccessibilityStrings.ts | checked": {"message": "已选中"}, "panels/accessibility/AccessibilityStrings.ts | contents": {"message": "目录"}, "panels/accessibility/AccessibilityStrings.ts | controls": {"message": "控件"}, "panels/accessibility/AccessibilityStrings.ts | describedBy": {"message": "说明元素"}, "panels/accessibility/AccessibilityStrings.ts | description": {"message": "说明"}, "panels/accessibility/AccessibilityStrings.ts | disabled": {"message": "已停用"}, "panels/accessibility/AccessibilityStrings.ts | editable": {"message": "可修改"}, "panels/accessibility/AccessibilityStrings.ts | elementOrElementsWhichFormThe": {"message": "一个或多个构成此元素的说明的元素。"}, "panels/accessibility/AccessibilityStrings.ts | elementOrElementsWhichMayFormThe": {"message": "此元素的名称中可能包含的一个或多个元素。"}, "panels/accessibility/AccessibilityStrings.ts | elementOrElementsWhichShouldBe": {"message": "一个或多个应视为此元素的子项（尽管不是 DOM 中的子项）的元素。"}, "panels/accessibility/AccessibilityStrings.ts | elementOrElementsWhoseContentOr": {"message": "内容或存在状态由此微件控制的一个或多个元素。"}, "panels/accessibility/AccessibilityStrings.ts | elementToWhichTheUserMayChooseTo": {"message": "用户在转到当前元素后可以选择转到的元素，而非按 DOM 顺序的下一个元素。"}, "panels/accessibility/AccessibilityStrings.ts | expanded": {"message": "已展开"}, "panels/accessibility/AccessibilityStrings.ts | focusable": {"message": "可聚焦"}, "panels/accessibility/AccessibilityStrings.ts | focused": {"message": "已聚焦"}, "panels/accessibility/AccessibilityStrings.ts | forARangeWidgetTheMaximumAllowed": {"message": "（针对范围微件）允许的最大值。"}, "panels/accessibility/AccessibilityStrings.ts | forARangeWidgetTheMinimumAllowed": {"message": "（针对范围微件）允许的最小值。"}, "panels/accessibility/AccessibilityStrings.ts | fromAttribute": {"message": "所属的属性"}, "panels/accessibility/AccessibilityStrings.ts | fromCaption": {"message": "来自 caption"}, "panels/accessibility/AccessibilityStrings.ts | fromDescription": {"message": "源自：description"}, "panels/accessibility/AccessibilityStrings.ts | fromLabel": {"message": "来自 label"}, "panels/accessibility/AccessibilityStrings.ts | fromLabelFor": {"message": "来自 label（for= 属性）"}, "panels/accessibility/AccessibilityStrings.ts | fromLabelWrapped": {"message": "来自封装该元素的 label"}, "panels/accessibility/AccessibilityStrings.ts | fromLegend": {"message": "来自 legend"}, "panels/accessibility/AccessibilityStrings.ts | fromNativeHtml": {"message": "来自原生 HTML"}, "panels/accessibility/AccessibilityStrings.ts | fromPlaceholderAttribute": {"message": "所属的占位符属性"}, "panels/accessibility/AccessibilityStrings.ts | fromRubyAnnotation": {"message": "来自 Ruby 注解"}, "panels/accessibility/AccessibilityStrings.ts | fromStyle": {"message": "所属样式"}, "panels/accessibility/AccessibilityStrings.ts | fromTitle": {"message": "From title"}, "panels/accessibility/AccessibilityStrings.ts | hasAutocomplete": {"message": "支持自动补全"}, "panels/accessibility/AccessibilityStrings.ts | hasPopup": {"message": "带有弹出式组件"}, "panels/accessibility/AccessibilityStrings.ts | help": {"message": "帮助"}, "panels/accessibility/AccessibilityStrings.ts | ifAndHowThisElementCanBeEdited": {"message": "此元素是否可修改以及如何修改。"}, "panels/accessibility/AccessibilityStrings.ts | ifThisElementMayReceiveLive": {"message": "如果此元素可能接收到实时更新，用户是否会在变更时看到整个实时区域，还是仅看到变更的节点。"}, "panels/accessibility/AccessibilityStrings.ts | ifThisElementMayReceiveLiveUpdates": {"message": "如果此元素可能接收到实时更新，哪些类型的更新应触发通知。"}, "panels/accessibility/AccessibilityStrings.ts | ifThisElementMayReceiveLiveUpdatesThe": {"message": "如果此元素可以收到实时更新，它即是所在动态区域的根元素。"}, "panels/accessibility/AccessibilityStrings.ts | ifTrueThisElementCanReceiveFocus": {"message": "若为 true，则此元素可以获得焦点。"}, "panels/accessibility/AccessibilityStrings.ts | ifTrueThisElementCurrentlyCannot": {"message": "若为 true，则当前无法与此元素交互。"}, "panels/accessibility/AccessibilityStrings.ts | ifTrueThisElementCurrentlyHas": {"message": "如果为 true，则表示此元素目前已获得焦点。"}, "panels/accessibility/AccessibilityStrings.ts | ifTrueThisElementMayBeInteracted": {"message": "若为 true，那么您可以与此元素进行交互，但其值无法更改。"}, "panels/accessibility/AccessibilityStrings.ts | ifTrueThisElementsUserentered": {"message": "如果为 true，用户为此元素输入的值便不符合验证要求。"}, "panels/accessibility/AccessibilityStrings.ts | implicit": {"message": "隐式"}, "panels/accessibility/AccessibilityStrings.ts | implicitValue": {"message": "隐式值。"}, "panels/accessibility/AccessibilityStrings.ts | indicatesThePurposeOfThisElement": {"message": "指示此元素的用途，例如微件的界面习语，或文档内的结构角色。"}, "panels/accessibility/AccessibilityStrings.ts | invalidUserEntry": {"message": "用户输入无效"}, "panels/accessibility/AccessibilityStrings.ts | labeledBy": {"message": "标签添加者："}, "panels/accessibility/AccessibilityStrings.ts | level": {"message": "级别"}, "panels/accessibility/AccessibilityStrings.ts | liveRegion": {"message": "实时区域"}, "panels/accessibility/AccessibilityStrings.ts | liveRegionRoot": {"message": "动态区域的根级"}, "panels/accessibility/AccessibilityStrings.ts | maximumValue": {"message": "最大值"}, "panels/accessibility/AccessibilityStrings.ts | minimumValue": {"message": "最小值"}, "panels/accessibility/AccessibilityStrings.ts | multiline": {"message": "多行"}, "panels/accessibility/AccessibilityStrings.ts | multiselectable": {"message": "可多选"}, "panels/accessibility/AccessibilityStrings.ts | orientation": {"message": "方向"}, "panels/accessibility/AccessibilityStrings.ts | pressed": {"message": "已按下"}, "panels/accessibility/AccessibilityStrings.ts | readonlyString": {"message": "只读"}, "panels/accessibility/AccessibilityStrings.ts | relatedElement": {"message": "相关元素"}, "panels/accessibility/AccessibilityStrings.ts | relevantLiveRegions": {"message": "相关（动态区域）"}, "panels/accessibility/AccessibilityStrings.ts | requiredString": {"message": "必需"}, "panels/accessibility/AccessibilityStrings.ts | role": {"message": "角色"}, "panels/accessibility/AccessibilityStrings.ts | selectedString": {"message": "已选择"}, "panels/accessibility/AccessibilityStrings.ts | theAccessibleDescriptionForThis": {"message": "此元素的可访问说明。"}, "panels/accessibility/AccessibilityStrings.ts | theComputedHelpTextForThis": {"message": "此元素经计算得出的帮助文本。"}, "panels/accessibility/AccessibilityStrings.ts | theComputedNameOfThisElement": {"message": "此元素经计算得出的名称。"}, "panels/accessibility/AccessibilityStrings.ts | theDescendantOfThisElementWhich": {"message": "此元素的活跃后代；即应向其委托焦点的元素。"}, "panels/accessibility/AccessibilityStrings.ts | theHierarchicalLevelOfThis": {"message": "此元素的层级。"}, "panels/accessibility/AccessibilityStrings.ts | theValueOfThisElementThisMayBe": {"message": "此元素的值；可以由用户或开发者提供，视元素而定。"}, "panels/accessibility/AccessibilityStrings.ts | value": {"message": "值"}, "panels/accessibility/AccessibilityStrings.ts | valueDescription": {"message": "值说明"}, "panels/accessibility/AccessibilityStrings.ts | valueFromAttribute": {"message": "来自属性的值。"}, "panels/accessibility/AccessibilityStrings.ts | valueFromDescriptionElement": {"message": "来自 description 元素的值。"}, "panels/accessibility/AccessibilityStrings.ts | valueFromElementContents": {"message": "来自元素内容的值。"}, "panels/accessibility/AccessibilityStrings.ts | valueFromFigcaptionElement": {"message": "来自 figcaption 元素的值。"}, "panels/accessibility/AccessibilityStrings.ts | valueFromLabelElement": {"message": "来自 label 元素的值。"}, "panels/accessibility/AccessibilityStrings.ts | valueFromLabelElementWithFor": {"message": "来自具有 for= 属性的 label 元素的值。"}, "panels/accessibility/AccessibilityStrings.ts | valueFromLabelElementWrapped": {"message": "来自封装该元素的 label 元素的值。"}, "panels/accessibility/AccessibilityStrings.ts | valueFromLegendElement": {"message": "来自 legend 元素的值。"}, "panels/accessibility/AccessibilityStrings.ts | valueFromNativeHtmlRuby": {"message": "来自普通 HTML Ruby 注解的值。"}, "panels/accessibility/AccessibilityStrings.ts | valueFromNativeHtmlUnknownSource": {"message": "来自原生 HTML 的值（未知来源）。"}, "panels/accessibility/AccessibilityStrings.ts | valueFromPlaceholderAttribute": {"message": "来自 placeholder 属性的值。"}, "panels/accessibility/AccessibilityStrings.ts | valueFromRelatedElement": {"message": "相关元素中的值。"}, "panels/accessibility/AccessibilityStrings.ts | valueFromStyle": {"message": "来自样式的值。"}, "panels/accessibility/AccessibilityStrings.ts | valueFromTableCaption": {"message": "来自 table caption 的值。"}, "panels/accessibility/AccessibilityStrings.ts | valueFromTitleAttribute": {"message": "来自标题属性的值。"}, "panels/accessibility/AccessibilityStrings.ts | whetherAUserMaySelectMoreThanOne": {"message": "用户是否会从此微件选择多个选项。"}, "panels/accessibility/AccessibilityStrings.ts | whetherAndWhatPriorityOfLive": {"message": "此元素是否需要实时更新以及需要何种实时更新优先级。"}, "panels/accessibility/AccessibilityStrings.ts | whetherAndWhatTypeOfAutocomplete": {"message": "此元素目前是否提供了自动补全建议以及提供了何种自动补全建议。"}, "panels/accessibility/AccessibilityStrings.ts | whetherTheOptionRepresentedBy": {"message": "目前是否已选择此元素所表示的选项。"}, "panels/accessibility/AccessibilityStrings.ts | whetherTheValueOfThisElementCan": {"message": "是否可以设置此元素的值。"}, "panels/accessibility/AccessibilityStrings.ts | whetherThisCheckboxRadioButtonOr": {"message": "此复选框、单选按钮或树状目录项处于选中状态、未选中状态还是混合状态（例如，既有已选中的子级，也有未选中的子级）。"}, "panels/accessibility/AccessibilityStrings.ts | whetherThisElementHasCausedSome": {"message": "此元素是否导致某类内容（例如菜单）弹出。"}, "panels/accessibility/AccessibilityStrings.ts | whetherThisElementIsARequired": {"message": "此元素是否为表单中的必填字段。"}, "panels/accessibility/AccessibilityStrings.ts | whetherThisElementOrAnother": {"message": "此元素或它控制的另一个分组元素是否已展开。"}, "panels/accessibility/AccessibilityStrings.ts | whetherThisElementOrItsSubtree": {"message": "此元素或其子树当前是否正在更新（因此可能状态不一致）。"}, "panels/accessibility/AccessibilityStrings.ts | whetherThisLinearElements": {"message": "此线性元素的方向是水平方向还是垂直方向。"}, "panels/accessibility/AccessibilityStrings.ts | whetherThisTextBoxMayHaveMore": {"message": "此文本框是否可以拥有超过一行内容。"}, "panels/accessibility/AccessibilityStrings.ts | whetherThisToggleButtonIs": {"message": "此切换按钮当前是否处于已按下状态。"}, "panels/accessibility/SourceOrderView.ts | noSourceOrderInformation": {"message": "无可用的源代码顺序信息"}, "panels/accessibility/SourceOrderView.ts | showSourceOrder": {"message": "显示源代码顺序"}, "panels/accessibility/SourceOrderView.ts | sourceOrderViewer": {"message": "来源顺序查看器"}, "panels/accessibility/SourceOrderView.ts | thereMayBeADelayInDisplaying": {"message": "在为包含很多子项的元素显示源代码顺序时，可能会有延迟"}, "panels/accessibility/accessibility-meta.ts | accessibility": {"message": "无障碍功能"}, "panels/accessibility/accessibility-meta.ts | shoAccessibility": {"message": "显示“无障碍功能”"}, "panels/animation/AnimationTimeline.ts | animationPreviewS": {"message": "动画预览 {PH1}"}, "panels/animation/AnimationTimeline.ts | animationPreviews": {"message": "动画预览"}, "panels/animation/AnimationTimeline.ts | clearAll": {"message": "全部清除"}, "panels/animation/AnimationTimeline.ts | pause": {"message": "暂停"}, "panels/animation/AnimationTimeline.ts | pauseAll": {"message": "全部暂停"}, "panels/animation/AnimationTimeline.ts | pauseTimeline": {"message": "暂停时间轴"}, "panels/animation/AnimationTimeline.ts | playTimeline": {"message": "播放时间轴"}, "panels/animation/AnimationTimeline.ts | playbackRatePlaceholder": {"message": "{PH1}%"}, "panels/animation/AnimationTimeline.ts | playbackRates": {"message": "播放速率"}, "panels/animation/AnimationTimeline.ts | replayTimeline": {"message": "重放时间轴"}, "panels/animation/AnimationTimeline.ts | resumeAll": {"message": "全部恢复"}, "panels/animation/AnimationTimeline.ts | selectAnEffectAboveToInspectAnd": {"message": "从上方选择一种效果以检查和修改。"}, "panels/animation/AnimationTimeline.ts | setSpeedToS": {"message": "将速度设为 {PH1}"}, "panels/animation/AnimationTimeline.ts | waitingForAnimations": {"message": "正在等待动画…"}, "panels/animation/AnimationUI.ts | animationEndpointSlider": {"message": "动画端点滑块"}, "panels/animation/AnimationUI.ts | animationKeyframeSlider": {"message": "动画关键帧滑块"}, "panels/animation/AnimationUI.ts | sSlider": {"message": "{PH1} 滑块"}, "panels/animation/animation-meta.ts | animations": {"message": "动画"}, "panels/animation/animation-meta.ts | showAnimations": {"message": "显示“动画”工具"}, "panels/application/AppManifestView.ts | aUrlInTheManifestContainsA": {"message": "清单中的某个网址包含用户名、密码或端口"}, "panels/application/AppManifestView.ts | actualHeightSpxOfSSDoesNotMatch": {"message": "{PH2} {PH3} 的实际高度（{PH1} 像素）与指定高度（{PH4} 像素）不匹配"}, "panels/application/AppManifestView.ts | actualSizeSspxOfSSDoesNotMatch": {"message": "{PH3} {PH4} 的实际大小（{PH1}×{PH2} 像素）与指定大小（{PH5}×{PH6} 像素）不一致"}, "panels/application/AppManifestView.ts | actualWidthSpxOfSSDoesNotMatch": {"message": "{PH2} {PH3} 的实际宽度（{PH1} 像素）与指定宽度（{PH4} 像素）不一致"}, "panels/application/AppManifestView.ts | appIdExplainer": {"message": "浏览器会根据此 ID 判断清单是否应该更新现有应用，或者清单是否引用的是可安装的新 Web 应用。"}, "panels/application/AppManifestView.ts | appIdNote": {"message": "{PH1}您未在清单中指定 {PH2}，而是使用了 {PH3}。若要指定一个与当前标识符匹配的应用 ID，请将“{PH4}”字段设为 {PH5}{PH6}。"}, "panels/application/AppManifestView.ts | avoidPurposeAnyAndMaskable": {"message": "最好不要使用“any maskable”的“purpose”来声明图标。否则，相应图标可能会因内边距过大或过小而无法在某些平台上正确显示。"}, "panels/application/AppManifestView.ts | backgroundColor": {"message": "背景颜色"}, "panels/application/AppManifestView.ts | computedAppId": {"message": "计算出的应用 ID"}, "panels/application/AppManifestView.ts | copiedToClipboard": {"message": "已将建议 ID {PH1} 复制到剪贴板"}, "panels/application/AppManifestView.ts | copyToClipboard": {"message": "将建议的 ID 复制到剪贴板"}, "panels/application/AppManifestView.ts | couldNotCheckServiceWorker": {"message": "如果清单不含“start_url”字段，则无法检查 service worker"}, "panels/application/AppManifestView.ts | couldNotDownloadARequiredIcon": {"message": "无法从清单下载必需的图标"}, "panels/application/AppManifestView.ts | customizePwaTitleBar": {"message": "自定义 PWA 标题栏的窗口控件叠加层"}, "panels/application/AppManifestView.ts | description": {"message": "说明"}, "panels/application/AppManifestView.ts | descriptionMayBeTruncated": {"message": "说明可能被截断了。"}, "panels/application/AppManifestView.ts | display": {"message": "显示"}, "panels/application/AppManifestView.ts | documentationOnMaskableIcons": {"message": "有关可遮罩式图标的文档"}, "panels/application/AppManifestView.ts | downloadedIconWasEmptyOr": {"message": "下载的图标为空或已损坏"}, "panels/application/AppManifestView.ts | errorsAndWarnings": {"message": "错误和警告"}, "panels/application/AppManifestView.ts | formFactor": {"message": "设备规格"}, "panels/application/AppManifestView.ts | icon": {"message": "图标"}, "panels/application/AppManifestView.ts | icons": {"message": "图标"}, "panels/application/AppManifestView.ts | identity": {"message": "身份"}, "panels/application/AppManifestView.ts | imageFromS": {"message": "图片来自 {PH1}"}, "panels/application/AppManifestView.ts | installability": {"message": "可安装性"}, "panels/application/AppManifestView.ts | label": {"message": "标签"}, "panels/application/AppManifestView.ts | learnMore": {"message": "了解详情"}, "panels/application/AppManifestView.ts | manifestContainsDisplayoverride": {"message": "清单包含“display_override”字段，并且第一个受支持的显示模式必须是“standalone”、“fullscreen”或“minimal-ui”之一"}, "panels/application/AppManifestView.ts | manifestCouldNotBeFetchedIsEmpty": {"message": "清单无法提取、为空或无法解析"}, "panels/application/AppManifestView.ts | manifestDisplayPropertyMustBeOne": {"message": "清单“display”属性必须是“standalone”、“fullscreen”或“minimal-ui”之一"}, "panels/application/AppManifestView.ts | manifestDoesNotContainANameOr": {"message": "清单未包含“name”或“short_name”字段"}, "panels/application/AppManifestView.ts | manifestDoesNotContainASuitable": {"message": "清单未包含合适的图标 - 必须采用 PNG、SVG 或 WebP 格式，且大小至少要为 {PH1} 像素；必须设置“sizes”属性；如果设置了“purpose”属性，其中必须包含“any”。"}, "panels/application/AppManifestView.ts | manifestSpecifies": {"message": "清单指定了“prefer_related_applications: true”"}, "panels/application/AppManifestView.ts | manifestStartUrlIsNotValid": {"message": "清单“start_url”无效"}, "panels/application/AppManifestView.ts | name": {"message": "名称"}, "panels/application/AppManifestView.ts | needHelpReadOurS": {"message": "需要帮助？不妨阅读 {PH1}。"}, "panels/application/AppManifestView.ts | newNoteUrl": {"message": "新备注网址"}, "panels/application/AppManifestView.ts | noPlayStoreIdProvided": {"message": "未提供 Play 商店 ID"}, "panels/application/AppManifestView.ts | noScreenshotsForRicherPWAInstallOnDesktop": {"message": "桌面设备不支持信息更丰富的 PWA 安装界面。请至少添加 1 张“form_factor”设置为“wide”的屏幕截图。"}, "panels/application/AppManifestView.ts | noScreenshotsForRicherPWAInstallOnMobile": {"message": "移动设备不支持信息更丰富的 PWA 安装界面。请至少添加 1 张如下屏幕截图：“form_factor”未设置或设为“wide”以外的值。"}, "panels/application/AppManifestView.ts | noSuppliedIconIsAtLeastSpxSquare": {"message": "未提供不小于 {PH1} 正方形像素的 PNG、SVG 或 WebP 格式图标，而且 purpose 属性设为“any”或未设置。"}, "panels/application/AppManifestView.ts | note": {"message": "注意："}, "panels/application/AppManifestView.ts | orientation": {"message": "方向"}, "panels/application/AppManifestView.ts | pageDoesNotWorkOffline": {"message": "该网页无法离线使用"}, "panels/application/AppManifestView.ts | pageDoesNotWorkOfflineThePage": {"message": "该网页无法离线使用。从 Chrome 93 开始，可安装性标准已发生变化，与该网站对应的应用将不再可安装。如需了解详情，请参阅 {PH1}。"}, "panels/application/AppManifestView.ts | pageHasNoManifestLinkUrl": {"message": "网页没有清单 <link> URL"}, "panels/application/AppManifestView.ts | pageIsLoadedInAnIncognitoWindow": {"message": "该网页是在无痕式窗口中加载的"}, "panels/application/AppManifestView.ts | pageIsNotLoadedInTheMainFrame": {"message": "该网页不是在主框架中加载的"}, "panels/application/AppManifestView.ts | pageIsNotServedFromASecureOrigin": {"message": "该网页不是从安全来源提供的"}, "panels/application/AppManifestView.ts | platform": {"message": "平台"}, "panels/application/AppManifestView.ts | preferrelatedapplicationsIsOnly": {"message": "仅 Android 设备上的 Chrome Beta 版和稳定版支持“prefer_related_applications”。"}, "panels/application/AppManifestView.ts | presentation": {"message": "演示文稿"}, "panels/application/AppManifestView.ts | protocolHandlers": {"message": "协议处理程序"}, "panels/application/AppManifestView.ts | sSDoesNotSpecifyItsSizeInThe": {"message": "{PH1} {PH2} 未在清单中指定其大小"}, "panels/application/AppManifestView.ts | sSFailedToLoad": {"message": "{PH1} {PH2} 无法加载"}, "panels/application/AppManifestView.ts | sSHeightDoesNotComplyWithRatioRequirement": {"message": "{PH1} {PH2} 高度不能超过宽度的 2.3 倍"}, "panels/application/AppManifestView.ts | sSShouldHaveSquareIcon": {"message": "大多数操作系统都需要方形图标。请在数组中包含至少一个方形图标。"}, "panels/application/AppManifestView.ts | sSShouldSpecifyItsSizeAs": {"message": "{PH1} {PH2} 应将其大小指定为 [width]x[height]"}, "panels/application/AppManifestView.ts | sSSizeShouldBeAtLeast320": {"message": "{PH1} {PH2} 尺寸至少应为 320×320"}, "panels/application/AppManifestView.ts | sSSizeShouldBeAtMost3840": {"message": "{PH1} {PH2} 的大小不应超过 3840×3840"}, "panels/application/AppManifestView.ts | sSWidthDoesNotComplyWithRatioRequirement": {"message": "{PH1} {PH2} 宽度不能超过高度的 2.3 倍"}, "panels/application/AppManifestView.ts | sSrcIsNotSet": {"message": "未设置 {PH1} 的“src”"}, "panels/application/AppManifestView.ts | sUrlSFailedToParse": {"message": "未能解析{PH1}网址“{PH2}”"}, "panels/application/AppManifestView.ts | screenshot": {"message": "屏幕截图"}, "panels/application/AppManifestView.ts | screenshotPixelSize": {"message": "屏幕截图 {url} 应将一种像素尺寸 [width]x[height]（而非 any）指定为尺寸列表中的第一个条目。"}, "panels/application/AppManifestView.ts | screenshotS": {"message": "屏幕截图 #{PH1}"}, "panels/application/AppManifestView.ts | screenshotsMustHaveSameAspectRatio": {"message": "“form_factor”相同的所有屏幕截图的宽高比必须与第一张具有该“form_factor”的屏幕截图的宽高比相同。某些屏幕截图将会被忽略。"}, "panels/application/AppManifestView.ts | selectWindowControlsOverlayEmulationOs": {"message": "在以下操作系统中模拟窗口控件叠加层："}, "panels/application/AppManifestView.ts | shortName": {"message": "简称"}, "panels/application/AppManifestView.ts | shortcutS": {"message": "快捷方式 {PH1}"}, "panels/application/AppManifestView.ts | shortcutSShouldIncludeAXPixel": {"message": "第 #{PH1} 个快捷方式应当包含 96x96 像素的图标"}, "panels/application/AppManifestView.ts | shortcutsMayBeNotAvailable": {"message": "快捷键的数量上限因平台而异。某些快捷键可能不适用。"}, "panels/application/AppManifestView.ts | showOnlyTheMinimumSafeAreaFor": {"message": "仅显示可遮盖式图标的最小安全区域"}, "panels/application/AppManifestView.ts | startUrl": {"message": "起始网址"}, "panels/application/AppManifestView.ts | theAppIsAlreadyInstalled": {"message": "该应用已安装"}, "panels/application/AppManifestView.ts | thePlayStoreAppUrlAndPlayStoreId": {"message": "Play 商店应用网址与 Play 商店 ID 不符"}, "panels/application/AppManifestView.ts | theSpecifiedApplicationPlatform": {"message": "指定的应用平台在 Android 设备上不受支持"}, "panels/application/AppManifestView.ts | themeColor": {"message": "主题颜色"}, "panels/application/AppManifestView.ts | tooManyScreenshotsForDesktop": {"message": "桌面设备上最多只能显示 8 张屏幕截图。其余屏幕截图将会被忽略。"}, "panels/application/AppManifestView.ts | tooManyScreenshotsForMobile": {"message": "移动设备上最多只能显示 5 张屏幕截图。其余屏幕截图将会被忽略。"}, "panels/application/AppManifestView.ts | url": {"message": "网址"}, "panels/application/AppManifestView.ts | wcoFound": {"message": "Chrome 已成功找到 {PH3} 中 {PH2} 字段的 {PH1} 值。"}, "panels/application/AppManifestView.ts | wcoNeedHelpReadMore": {"message": "需要帮助？请参阅 {PH1}。"}, "panels/application/AppManifestView.ts | wcoNotFound": {"message": "建议您在清单中定义 {PH1}，以便使用 Window Controls Overlay API 并自定义应用的标题栏。"}, "panels/application/AppManifestView.ts | windowControlsOverlay": {"message": "窗口控件叠加层"}, "panels/application/ApplicationPanelSidebar.ts | appManifest": {"message": "应用清单"}, "panels/application/ApplicationPanelSidebar.ts | application": {"message": "应用"}, "panels/application/ApplicationPanelSidebar.ts | applicationSidebarPanel": {"message": "应用面板边栏"}, "panels/application/ApplicationPanelSidebar.ts | backgroundServices": {"message": "后台服务"}, "panels/application/ApplicationPanelSidebar.ts | beforeInvokeAlert": {"message": "{PH1}：调用后可滚动至清单中的这一部分"}, "panels/application/ApplicationPanelSidebar.ts | clear": {"message": "清除"}, "panels/application/ApplicationPanelSidebar.ts | cookies": {"message": "<PERSON><PERSON>"}, "panels/application/ApplicationPanelSidebar.ts | cookiesUsedByFramesFromS": {"message": "来自 {PH1} 的框架使用的 Cookie"}, "panels/application/ApplicationPanelSidebar.ts | documentNotAvailable": {"message": "文档不可用"}, "panels/application/ApplicationPanelSidebar.ts | frames": {"message": "帧"}, "panels/application/ApplicationPanelSidebar.ts | indexeddb": {"message": "IndexedDB"}, "panels/application/ApplicationPanelSidebar.ts | keyPathS": {"message": "关键路径：{PH1}"}, "panels/application/ApplicationPanelSidebar.ts | localFiles": {"message": "本地文件"}, "panels/application/ApplicationPanelSidebar.ts | localStorage": {"message": "本地存储空间"}, "panels/application/ApplicationPanelSidebar.ts | manifest": {"message": "清单"}, "panels/application/ApplicationPanelSidebar.ts | noManifestDetected": {"message": "未检测到任何清单"}, "panels/application/ApplicationPanelSidebar.ts | onInvokeAlert": {"message": "已滚动至{PH1}"}, "panels/application/ApplicationPanelSidebar.ts | onInvokeManifestAlert": {"message": "清单：调用后可滚动至清单顶部"}, "panels/application/ApplicationPanelSidebar.ts | openedWindows": {"message": "已打开的窗口"}, "panels/application/ApplicationPanelSidebar.ts | refreshIndexeddb": {"message": "刷新 IndexedDB"}, "panels/application/ApplicationPanelSidebar.ts | sessionStorage": {"message": "会话存储空间"}, "panels/application/ApplicationPanelSidebar.ts | storage": {"message": "存储"}, "panels/application/ApplicationPanelSidebar.ts | theContentOfThisDocumentHasBeen": {"message": "已通过“document.write()”动态生成此文档的内容。"}, "panels/application/ApplicationPanelSidebar.ts | thirdPartyPhaseout": {"message": "来自 {PH1} 的 Cookie 可能因第三方 Cookie 逐步淘汰机制而被屏蔽。"}, "panels/application/ApplicationPanelSidebar.ts | versionS": {"message": "版本：{PH1}"}, "panels/application/ApplicationPanelSidebar.ts | versionSEmpty": {"message": "版本：{PH1}（空）"}, "panels/application/ApplicationPanelSidebar.ts | webWorkers": {"message": "网络工作器"}, "panels/application/ApplicationPanelSidebar.ts | windowWithoutTitle": {"message": "没有标题的窗口"}, "panels/application/ApplicationPanelSidebar.ts | worker": {"message": "worker"}, "panels/application/BackForwardCacheTreeElement.ts | backForwardCache": {"message": "往返缓存"}, "panels/application/BackgroundServiceView.ts | backgroundFetch": {"message": "后台提取"}, "panels/application/BackgroundServiceView.ts | backgroundServices": {"message": "后台服务"}, "panels/application/BackgroundServiceView.ts | backgroundSync": {"message": "后台同步"}, "panels/application/BackgroundServiceView.ts | clear": {"message": "清除"}, "panels/application/BackgroundServiceView.ts | clickTheRecordButtonSOrHitSTo": {"message": "点击录制按钮 {PH1} 或按 {PH2} 即可开始录制。"}, "panels/application/BackgroundServiceView.ts | devtoolsWillRecordAllSActivity": {"message": "对于所有{PH1}活动，DevTools 会记录最多 3 天，即使处于关闭状态也不例外。"}, "panels/application/BackgroundServiceView.ts | empty": {"message": "空白"}, "panels/application/BackgroundServiceView.ts | event": {"message": "事件"}, "panels/application/BackgroundServiceView.ts | instanceId": {"message": "实例 ID"}, "panels/application/BackgroundServiceView.ts | learnMore": {"message": "了解详情"}, "panels/application/BackgroundServiceView.ts | noMetadataForThisEvent": {"message": "此事件无任何元数据"}, "panels/application/BackgroundServiceView.ts | notifications": {"message": "通知"}, "panels/application/BackgroundServiceView.ts | origin": {"message": "来源"}, "panels/application/BackgroundServiceView.ts | paymentHandler": {"message": "付款处理程序"}, "panels/application/BackgroundServiceView.ts | periodicBackgroundSync": {"message": "定期后台同步"}, "panels/application/BackgroundServiceView.ts | pushMessaging": {"message": "推送消息"}, "panels/application/BackgroundServiceView.ts | recordingSActivity": {"message": "正在录制{PH1}活动…"}, "panels/application/BackgroundServiceView.ts | saveEvents": {"message": "保存事件"}, "panels/application/BackgroundServiceView.ts | selectAnEntryToViewMetadata": {"message": "选择条目以查看元数据"}, "panels/application/BackgroundServiceView.ts | showEventsForOtherStorageKeys": {"message": "显示来自其他存储分区的事件"}, "panels/application/BackgroundServiceView.ts | showEventsFromOtherDomains": {"message": "显示其他网域的事件"}, "panels/application/BackgroundServiceView.ts | startRecordingEvents": {"message": "启动录制事件"}, "panels/application/BackgroundServiceView.ts | stopRecordingEvents": {"message": "停止记录事件"}, "panels/application/BackgroundServiceView.ts | storageKey": {"message": "存储空间密钥"}, "panels/application/BackgroundServiceView.ts | swScope": {"message": "Service Worker 范围"}, "panels/application/BackgroundServiceView.ts | timestamp": {"message": "时间戳"}, "panels/application/BounceTrackingMitigationsTreeElement.ts | bounceTrackingMitigations": {"message": "跳出跟踪缓解措施"}, "panels/application/CookieItemsView.ts | clearAllCookies": {"message": "清除所有 <PERSON>ie"}, "panels/application/CookieItemsView.ts | clearFilteredCookies": {"message": "清除过滤出的 Cookie"}, "panels/application/CookieItemsView.ts | cookies": {"message": "<PERSON><PERSON>"}, "panels/application/CookieItemsView.ts | numberOfCookiesShownInTableS": {"message": "表格中显示的 Cookie 数量：{PH1}"}, "panels/application/CookieItemsView.ts | onlyShowCookiesWhichHaveAn": {"message": "仅显示那些有相关问题的 Cookie"}, "panels/application/CookieItemsView.ts | onlyShowCookiesWithAnIssue": {"message": "仅显示有问题的 Cookie"}, "panels/application/CookieItemsView.ts | selectACookieToPreviewItsValue": {"message": "选择一个 Cookie 以预览其值"}, "panels/application/CookieItemsView.ts | showUrlDecoded": {"message": "显示已解码的网址"}, "panels/application/DOMStorageItemsView.ts | domStorage": {"message": "DOM 存储空间"}, "panels/application/DOMStorageItemsView.ts | domStorageItemDeleted": {"message": "此存储空间项已被删除。"}, "panels/application/DOMStorageItemsView.ts | domStorageItems": {"message": "DOM 存储项"}, "panels/application/DOMStorageItemsView.ts | domStorageItemsCleared": {"message": "DOM 存储项已被清除"}, "panels/application/DOMStorageItemsView.ts | domStorageNumberEntries": {"message": "表格中所显示条目的数量：{PH1}"}, "panels/application/DOMStorageItemsView.ts | key": {"message": "键"}, "panels/application/DOMStorageItemsView.ts | selectAValueToPreview": {"message": "选择一个值以预览"}, "panels/application/DOMStorageItemsView.ts | value": {"message": "值"}, "panels/application/IndexedDBViews.ts | clearObjectStore": {"message": "清除对象仓库"}, "panels/application/IndexedDBViews.ts | collapse": {"message": "收起"}, "panels/application/IndexedDBViews.ts | dataMayBeStale": {"message": "数据可能已过时"}, "panels/application/IndexedDBViews.ts | deleteDatabase": {"message": "删除数据库"}, "panels/application/IndexedDBViews.ts | deleteSelected": {"message": "删除所选项"}, "panels/application/IndexedDBViews.ts | expandRecursively": {"message": "以递归方式展开"}, "panels/application/IndexedDBViews.ts | filterByKey": {"message": "按键过滤（显示大于或等于某些值的键）"}, "panels/application/IndexedDBViews.ts | idb": {"message": "IDB"}, "panels/application/IndexedDBViews.ts | indexedDb": {"message": "Indexed DB"}, "panels/application/IndexedDBViews.ts | keyGeneratorValueS": {"message": "密钥生成器值：{PH1}"}, "panels/application/IndexedDBViews.ts | keyPath": {"message": "键路径： "}, "panels/application/IndexedDBViews.ts | keyString": {"message": "键"}, "panels/application/IndexedDBViews.ts | objectStores": {"message": "对象存储区"}, "panels/application/IndexedDBViews.ts | pleaseConfirmDeleteOfSDatabase": {"message": "请确认您要删除“{PH1}”数据库。"}, "panels/application/IndexedDBViews.ts | primaryKey": {"message": "主键"}, "panels/application/IndexedDBViews.ts | refresh": {"message": "刷新"}, "panels/application/IndexedDBViews.ts | refreshDatabase": {"message": "刷新数据库"}, "panels/application/IndexedDBViews.ts | showNextPage": {"message": "显示下一页"}, "panels/application/IndexedDBViews.ts | showPreviousPage": {"message": "显示上一页"}, "panels/application/IndexedDBViews.ts | someEntriesMayHaveBeenModified": {"message": "部分条目可能已被修改"}, "panels/application/IndexedDBViews.ts | totalEntriesS": {"message": "总条目数：{PH1}"}, "panels/application/IndexedDBViews.ts | valueString": {"message": "值"}, "panels/application/IndexedDBViews.ts | version": {"message": "版本"}, "panels/application/InterestGroupStorageView.ts | clickToDisplayBody": {"message": "点击任意兴趣群体事件即可显示该群体的当前状态"}, "panels/application/InterestGroupStorageView.ts | noDataAvailable": {"message": "没有所选兴趣群体的任何详细信息。浏览器可能已退出该群体。"}, "panels/application/InterestGroupTreeElement.ts | interestGroups": {"message": "兴趣群体"}, "panels/application/OpenedWindowDetailsView.ts | accessToOpener": {"message": "访问打开者"}, "panels/application/OpenedWindowDetailsView.ts | clickToRevealInElementsPanel": {"message": "点击即可在“元素”面板中显示"}, "panels/application/OpenedWindowDetailsView.ts | closed": {"message": "已关闭"}, "panels/application/OpenedWindowDetailsView.ts | crossoriginEmbedderPolicy": {"message": "跨源嵌入器政策"}, "panels/application/OpenedWindowDetailsView.ts | document": {"message": "文档"}, "panels/application/OpenedWindowDetailsView.ts | no": {"message": "否"}, "panels/application/OpenedWindowDetailsView.ts | openerFrame": {"message": "Opener 框架"}, "panels/application/OpenedWindowDetailsView.ts | reportingTo": {"message": "报告对象："}, "panels/application/OpenedWindowDetailsView.ts | security": {"message": "安全"}, "panels/application/OpenedWindowDetailsView.ts | securityIsolation": {"message": "安全与隔离"}, "panels/application/OpenedWindowDetailsView.ts | showsWhetherTheOpenedWindowIs": {"message": "显示打开的窗口是否能够访问其打开者，反之亦然"}, "panels/application/OpenedWindowDetailsView.ts | type": {"message": "类型"}, "panels/application/OpenedWindowDetailsView.ts | unknown": {"message": "未知"}, "panels/application/OpenedWindowDetailsView.ts | url": {"message": "网址"}, "panels/application/OpenedWindowDetailsView.ts | webWorker": {"message": "网络工作器"}, "panels/application/OpenedWindowDetailsView.ts | windowWithoutTitle": {"message": "没有标题的窗口"}, "panels/application/OpenedWindowDetailsView.ts | worker": {"message": "worker"}, "panels/application/OpenedWindowDetailsView.ts | yes": {"message": "是"}, "panels/application/PreloadingTreeElement.ts | rules": {"message": "规则"}, "panels/application/PreloadingTreeElement.ts | speculations": {"message": "推测"}, "panels/application/PreloadingTreeElement.ts | speculativeLoads": {"message": "推测加载"}, "panels/application/ReportingApiReportsView.ts | clickToDisplayBody": {"message": "点击任一报告即可显示相应正文"}, "panels/application/ReportingApiTreeElement.ts | reportingApi": {"message": "Reporting API"}, "panels/application/ServiceWorkerCacheTreeElement.ts | cacheStorage": {"message": "缓存空间"}, "panels/application/ServiceWorkerCacheTreeElement.ts | delete": {"message": "删除"}, "panels/application/ServiceWorkerCacheTreeElement.ts | refreshCaches": {"message": "刷新缓存"}, "panels/application/ServiceWorkerCacheViews.ts | cache": {"message": "缓存"}, "panels/application/ServiceWorkerCacheViews.ts | deleteSelected": {"message": "删除所选项"}, "panels/application/ServiceWorkerCacheViews.ts | filterByPath": {"message": "按路径过滤"}, "panels/application/ServiceWorkerCacheViews.ts | headers": {"message": "标头"}, "panels/application/ServiceWorkerCacheViews.ts | matchingEntriesS": {"message": "匹配的条目数：{PH1}"}, "panels/application/ServiceWorkerCacheViews.ts | name": {"message": "名称"}, "panels/application/ServiceWorkerCacheViews.ts | preview": {"message": "预览"}, "panels/application/ServiceWorkerCacheViews.ts | refresh": {"message": "刷新"}, "panels/application/ServiceWorkerCacheViews.ts | selectACacheEntryAboveToPreview": {"message": "选择上方的缓存条目进行预览"}, "panels/application/ServiceWorkerCacheViews.ts | serviceWorkerCache": {"message": "Service Worker 缓存"}, "panels/application/ServiceWorkerCacheViews.ts | timeCached": {"message": "缓存时长"}, "panels/application/ServiceWorkerCacheViews.ts | totalEntriesS": {"message": "总条目数：{PH1}"}, "panels/application/ServiceWorkerCacheViews.ts | varyHeaderWarning": {"message": "⚠️ 匹配此条目时将 ignoreVary 设为 true"}, "panels/application/ServiceWorkerUpdateCycleView.ts | endTimeS": {"message": "结束时间：{PH1}"}, "panels/application/ServiceWorkerUpdateCycleView.ts | startTimeS": {"message": "开始时间：{PH1}"}, "panels/application/ServiceWorkerUpdateCycleView.ts | timeline": {"message": "时间轴"}, "panels/application/ServiceWorkerUpdateCycleView.ts | updateActivity": {"message": "更新活动"}, "panels/application/ServiceWorkerUpdateCycleView.ts | version": {"message": "版本"}, "panels/application/ServiceWorkersView.ts | bypassForNetwork": {"message": "绕过以转到网络"}, "panels/application/ServiceWorkersView.ts | bypassTheServiceWorkerAndLoad": {"message": "绕过 service worker 并从网络加载资源"}, "panels/application/ServiceWorkersView.ts | clients": {"message": "客户端"}, "panels/application/ServiceWorkersView.ts | focus": {"message": "聚焦"}, "panels/application/ServiceWorkersView.ts | inspect": {"message": "检查"}, "panels/application/ServiceWorkersView.ts | networkRequests": {"message": "网络请求"}, "panels/application/ServiceWorkersView.ts | onPageReloadForceTheService": {"message": "网页重新加载时，强制更新并激活 service worker"}, "panels/application/ServiceWorkersView.ts | periodicSync": {"message": "定期同步"}, "panels/application/ServiceWorkersView.ts | periodicSyncTag": {"message": "定期同步标记"}, "panels/application/ServiceWorkersView.ts | pushData": {"message": "推送数据"}, "panels/application/ServiceWorkersView.ts | pushString": {"message": "推送"}, "panels/application/ServiceWorkersView.ts | receivedS": {"message": "接收时间：{PH1}"}, "panels/application/ServiceWorkersView.ts | routers": {"message": "路由器"}, "panels/application/ServiceWorkersView.ts | sActivatedAndIsS": {"message": "已激活 #{PH1} 次，当前{PH2}"}, "panels/application/ServiceWorkersView.ts | sDeleted": {"message": "{PH1} - 已删除"}, "panels/application/ServiceWorkersView.ts | sIsRedundant": {"message": "第 {PH1} 项为多余项"}, "panels/application/ServiceWorkersView.ts | sRegistrationErrors": {"message": "{PH1} 个注册错误"}, "panels/application/ServiceWorkersView.ts | sTryingToInstall": {"message": "#{PH1} 正在尝试安装"}, "panels/application/ServiceWorkersView.ts | sWaitingToActivate": {"message": "#{PH1} 个正在等待激活"}, "panels/application/ServiceWorkersView.ts | seeAllRegistrations": {"message": "查看所有注册"}, "panels/application/ServiceWorkersView.ts | serviceWorkerForS": {"message": "{PH1} 的 Service worker"}, "panels/application/ServiceWorkersView.ts | serviceWorkersFromOtherOrigins": {"message": "来自其他来源的 Service Worker"}, "panels/application/ServiceWorkersView.ts | source": {"message": "来源"}, "panels/application/ServiceWorkersView.ts | startString": {"message": "开始"}, "panels/application/ServiceWorkersView.ts | status": {"message": "状态"}, "panels/application/ServiceWorkersView.ts | stopString": {"message": "停止"}, "panels/application/ServiceWorkersView.ts | syncString": {"message": "同步"}, "panels/application/ServiceWorkersView.ts | syncTag": {"message": "同步标记"}, "panels/application/ServiceWorkersView.ts | testPushMessageFromDevtools": {"message": "测试来自 DevTools 的推送消息。"}, "panels/application/ServiceWorkersView.ts | unregister": {"message": "取消注册"}, "panels/application/ServiceWorkersView.ts | unregisterServiceWorker": {"message": "取消注册 Service Worker"}, "panels/application/ServiceWorkersView.ts | update": {"message": "更新"}, "panels/application/ServiceWorkersView.ts | updateCycle": {"message": "更新周期"}, "panels/application/ServiceWorkersView.ts | updateOnReload": {"message": "重新加载时更新"}, "panels/application/ServiceWorkersView.ts | workerS": {"message": "工作器：{PH1}"}, "panels/application/SharedStorageEventsView.ts | clickToDisplayBody": {"message": "点击任一共享存储空间事件即可显示事件参数。"}, "panels/application/SharedStorageItemsView.ts | key": {"message": "键"}, "panels/application/SharedStorageItemsView.ts | selectAValueToPreview": {"message": "选择一个值以预览"}, "panels/application/SharedStorageItemsView.ts | sharedStorage": {"message": "共享存储空间"}, "panels/application/SharedStorageItemsView.ts | sharedStorageFilteredItemsCleared": {"message": "已清除共享存储空间中被滤除的项"}, "panels/application/SharedStorageItemsView.ts | sharedStorageItemDeleted": {"message": "此存储空间项已被删除。"}, "panels/application/SharedStorageItemsView.ts | sharedStorageItemEditCanceled": {"message": "已取消修改此存储空间项。"}, "panels/application/SharedStorageItemsView.ts | sharedStorageItemEdited": {"message": "此存储空间项已被修改。"}, "panels/application/SharedStorageItemsView.ts | sharedStorageItems": {"message": "共享存储空间项"}, "panels/application/SharedStorageItemsView.ts | sharedStorageItemsCleared": {"message": "已清除共享存储空间项"}, "panels/application/SharedStorageItemsView.ts | sharedStorageNumberEntries": {"message": "表格中所显示条目的数量：{PH1}"}, "panels/application/SharedStorageItemsView.ts | value": {"message": "值"}, "panels/application/SharedStorageListTreeElement.ts | sharedStorage": {"message": "共享存储空间"}, "panels/application/StorageBucketsTreeElement.ts | storageBuckets": {"message": "存储桶"}, "panels/application/StorageItemsView.ts | clearAll": {"message": "全部清除"}, "panels/application/StorageItemsView.ts | deleteSelected": {"message": "删除所选项"}, "panels/application/StorageItemsView.ts | refresh": {"message": "刷新"}, "panels/application/StorageItemsView.ts | refreshedStatus": {"message": "已刷新表"}, "panels/application/StorageView.ts | SiteDataCleared": {"message": "已清除网站数据"}, "panels/application/StorageView.ts | application": {"message": "应用"}, "panels/application/StorageView.ts | cacheStorage": {"message": "缓存空间"}, "panels/application/StorageView.ts | clearSiteData": {"message": "清除网站数据"}, "panels/application/StorageView.ts | clearing": {"message": "正在清除…"}, "panels/application/StorageView.ts | cookies": {"message": "<PERSON><PERSON>"}, "panels/application/StorageView.ts | fileSystem": {"message": "文件系统"}, "panels/application/StorageView.ts | includingThirdPartyCookies": {"message": "包括第三方 <PERSON>ie"}, "panels/application/StorageView.ts | indexDB": {"message": "IndexedDB"}, "panels/application/StorageView.ts | internalError": {"message": "内部错误"}, "panels/application/StorageView.ts | learnMore": {"message": "了解详情"}, "panels/application/StorageView.ts | localAndSessionStorage": {"message": "本地存储空间和会话存储空间"}, "panels/application/StorageView.ts | mb": {"message": "MB"}, "panels/application/StorageView.ts | numberMustBeNonNegative": {"message": "数字必须是非负数"}, "panels/application/StorageView.ts | numberMustBeSmaller": {"message": "数字必须小于 {PH1}"}, "panels/application/StorageView.ts | other": {"message": "其他"}, "panels/application/StorageView.ts | pleaseEnterANumber": {"message": "请输入一个数字"}, "panels/application/StorageView.ts | sFailedToLoad": {"message": "{PH1}（无法加载）"}, "panels/application/StorageView.ts | serviceWorkers": {"message": "Service Worker"}, "panels/application/StorageView.ts | simulateCustomStorage": {"message": "模拟自定义存储空间配额"}, "panels/application/StorageView.ts | storageQuotaIsLimitedIn": {"message": "在无痕模式下，存储空间配额有限。"}, "panels/application/StorageView.ts | storageQuotaUsed": {"message": "已使用 {PH1} 存储空间配额，共 {PH2}"}, "panels/application/StorageView.ts | storageQuotaUsedWithBytes": {"message": "已使用 {PH1} 字节（共 {PH2} 字节存储空间配额）"}, "panels/application/StorageView.ts | storageTitle": {"message": "存储"}, "panels/application/StorageView.ts | storageUsage": {"message": "存储空间用量"}, "panels/application/StorageView.ts | storageWithCustomMarker": {"message": "{PH1}（自定义）"}, "panels/application/StorageView.ts | unregisterServiceWorker": {"message": "取消注册 Service Worker"}, "panels/application/StorageView.ts | usage": {"message": "用量"}, "panels/application/StorageView.ts | webSql": {"message": "Web SQL"}, "panels/application/TrustTokensTreeElement.ts | trustTokens": {"message": "私密状态令牌"}, "panels/application/application-meta.ts | application": {"message": "应用"}, "panels/application/application-meta.ts | clearSiteData": {"message": "清除网站数据"}, "panels/application/application-meta.ts | clearSiteDataIncludingThirdparty": {"message": "清除网站数据（包括第三方 Cookie）"}, "panels/application/application-meta.ts | pwa": {"message": "pwa"}, "panels/application/application-meta.ts | showApplication": {"message": "显示应用"}, "panels/application/application-meta.ts | startRecordingEvents": {"message": "启动录制事件"}, "panels/application/application-meta.ts | stopRecordingEvents": {"message": "停止记录事件"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET": {"message": "只有通过 GET 请求进行加载的网页才能储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK": {"message": "只有状态代码为 2XX 的网页才能被缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution": {"message": "Chrome 检测到一项在储存于缓存期间执行 JavaScript 的意图。"}, "panels/application/components/BackForwardCacheStrings.ts | appBanner": {"message": "已请求 AppBanner 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled": {"message": "往返缓存被相关 flag 停用了。请在此设备上访问 chrome://flags/#back-forward-cache 以从本地启用该功能。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine": {"message": "往返缓存已被命令行停用。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory": {"message": "因为内存不足，往返缓存已被停用。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate": {"message": "委托行为不支持往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender": {"message": "已针对预渲染程序停用往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | broadcastChannel": {"message": "该网页无法缓存，因为它包含的 BroadcastChannel 实例具有已注册的监听器。"}, "panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore": {"message": "含 cache-control:no-store 标头的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | cacheFlushed": {"message": "缓存被刻意清除了。"}, "panels/application/components/BackForwardCacheStrings.ts | cacheLimit": {"message": "该网页被逐出了缓存，以使另一个网页能够缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | containsPlugins": {"message": "包含插件的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileChooser": {"message": "使用 FileChooser API 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess": {"message": "使用 File System Access API 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost": {"message": "使用媒体设备调度程序的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay": {"message": "媒体播放器正在播放内容时用户就离开了网页。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSession": {"message": "使用 MediaSession API 并设置了播放状态的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService": {"message": "使用 MediaSession API 并设置了操作处理程序的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentScreenReader": {"message": "往返缓存已被停用，因为受屏幕阅读器影响。"}, "panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler": {"message": "使用 SecurityHandler 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentSerial": {"message": "使用 Serial API 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI": {"message": "使用 WebAuthetication API 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth": {"message": "使用 WebBluetooth API 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | contentWebUSB": {"message": "使用 WebUSB API 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | cookieDisabled": {"message": "往返缓存已被停用，因为在使用 Cache-Control: no-store 的网页上 Cookie 处于停用状态。"}, "panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet": {"message": "使用专用 Worker 或 Worklet 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | documentLoaded": {"message": "该文档还未加载完毕时用户就离开了。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager": {"message": "用户离开网页时，系统显示了应用横幅。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager": {"message": "用户离开网页时，系统显示了 Chrome 密码管理器。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "用户离开网页时，DOM 提取正在进行。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource": {"message": "用户离开网页时，系统显示了 DOM Distiller Viewer。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging": {"message": "往返缓存已被停用，因为扩展程序使用了 Messaging API。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort": {"message": "在进入往返缓存之前，采用长期有效连接的扩展程序应断开连接。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame": {"message": "采用长期有效连接的扩展程序试图将消息发送到往返缓存中的框架。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderExtensions": {"message": "往返缓存已被停用，因为受扩展程序影响。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog": {"message": "用户离开网页时，该网页上显示了模态对话框（例如表单重新提交）或 HTTP 密码对话框。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage": {"message": "用户离开网页时，系统显示了离线版网页。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper": {"message": "用户离开网页时，系统显示了 Out-Of-Memory Intervention 栏。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager": {"message": "用户离开网页时，系统显示了权限请求。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper": {"message": "用户离开网页时，系统显示了弹出式内容拦截器。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails": {"message": "用户离开网页时，系统显示了安全浏览详情。"}, "panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "“安全浏览”功能认定该网页有滥用性质，因此拦截了弹出式窗口。"}, "panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "在该网页储存于往返缓存期间，有一个 Service Worker 被启用了。"}, "panels/application/components/BackForwardCacheStrings.ts | errorDocument": {"message": "往返缓存已被停用，因为文档出错了。"}, "panels/application/components/BackForwardCacheStrings.ts | fencedFramesEmbedder": {"message": "采用 FencedFrame 的网页无法存储在 bfcache 中。"}, "panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit": {"message": "该网页被逐出了缓存，以使另一个网页能够缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess": {"message": "已被授予媒体流访问权的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | haveInnerContents": {"message": "使用门户的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | idleManager": {"message": "使用 IdleManager 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection": {"message": "具备开放的 IndexedDB 连接的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | indexedDBEvent": {"message": "往返缓存已被停用，因为发生了 IndexedDB 事件。"}, "panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI": {"message": "使用了不符合条件的 API。"}, "panels/application/components/BackForwardCacheStrings.ts | injectedJavascript": {"message": "已被扩展程序注入 JavaScript 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet": {"message": "已被扩展程序注入 StyleSheet 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | internalError": {"message": "内部出错了。"}, "panels/application/components/BackForwardCacheStrings.ts | jsNetworkRequestReceivedCacheControlNoStoreResource": {"message": "往返缓存已被停用，因为部分 JavaScript 网络请求收到了包含 Cache-Control: no-store 标头的资源。"}, "panels/application/components/BackForwardCacheStrings.ts | keepaliveRequest": {"message": "往返缓存已被停用，因为这是一项 keepalive 请求。"}, "panels/application/components/BackForwardCacheStrings.ts | keyboardLock": {"message": "使用“键盘锁定”功能的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | loading": {"message": "该网页还未加载完毕时用户就离开了。"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache": {"message": "主资源包含 ache-control:no-cache 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore": {"message": "主资源包含 cache-control:no-store 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring": {"message": "该网页还没从往返缓存中恢复时导航就被取消了。"}, "panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit": {"message": "该网页被逐出了缓存，因为有一项使用中的网络连接收到了太多数据。Chrome 会限制网页在缓存期间可接收的数据量。"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "包含传输中的 fetch() 或 XHR 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected": {"message": "该网页被逐出了往返缓存，因为有一项使用中的网络请求涉及了重定向。"}, "panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout": {"message": "该网页被逐出了缓存，因为有一项网络连接处于开放状态的时间太长。Chrome 会限制网页在缓存期间可接收数据的时长。"}, "panels/application/components/BackForwardCacheStrings.ts | noResponseHead": {"message": "不含有效响应标头的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | notMainFrame": {"message": "导航是在主框架之外的某个框架中发生的。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction": {"message": "正在针对已建立索引的数据库处理事务的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket": {"message": "包含传输中的网络请求的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch": {"message": "包含传输中的 fetch() 网络请求的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers": {"message": "包含传输中的网络请求的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR": {"message": "包含传输中的 XHR 网络请求的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | paymentManager": {"message": "使用 PaymentManager 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | pictureInPicture": {"message": "使用“画中画”功能的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | portal": {"message": "使用门户的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | printing": {"message": "显示打印界面的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist": {"message": "该网页是使用“window.open()”打开的，而另一个标签页引用了该网页；或者，该网页打开了一个窗口。"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed": {"message": "储存在往返缓存中的网页的渲染程序进程崩溃了。"}, "panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled": {"message": "储存于往返缓存中的网页的渲染程序进程被终止了。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission": {"message": "已请求音频截取权的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors": {"message": "已请求传感器使用权的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission": {"message": "已请求后台同步或提取权限的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission": {"message": "已请求 MIDI 权限的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission": {"message": "已请求通知权限的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant": {"message": "已请求存储空间使用权的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission": {"message": "已请求视频拍摄权的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS": {"message": "只有网址架构为 HTTP / HTTPS 的网页才能被缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim": {"message": "在储存于往返缓存期间，该网页被一个 Service Worker 认领了。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage": {"message": "有一个 Service Worker 尝试向储存于往返缓存中的网页发送 MessageEvent。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration": {"message": "在网页储存于往返缓存期间，ServiceWorker 被取消注册了。"}, "panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation": {"message": "该网页被逐出了往返缓存，因为有一个 Service Worker 被启用了。"}, "panels/application/components/BackForwardCacheStrings.ts | sessionRestored": {"message": "Chrome 重启了，因而清除了往返缓存条目。"}, "panels/application/components/BackForwardCacheStrings.ts | sharedWorker": {"message": "使用 SharedWorker 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | speechRecognizer": {"message": "使用 SpeechRecognizer 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | speechSynthesis": {"message": "使用 SpeechSynthesis 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating": {"message": "该网页上某个 iframe 发起的导航并未完成。"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache": {"message": "子资源包含 ache-control:no-cache 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore": {"message": "子资源包含 cache-control:no-store 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | timeout": {"message": "该网页超出了往返缓存中的储存时长上限，因而已过期。"}, "panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache": {"message": "该网页在储存至往返缓存时超时了（可能是因为 pagehide 处理程序长时间运行）。"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame": {"message": "该网页的主框架中含有一款卸载处理程序。"}, "panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame": {"message": "该网页的子框架中含有一款卸载处理程序。"}, "panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers": {"message": "浏览器更改了用户代理替换标头。"}, "panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess": {"message": "已被授予视频/音频录制权的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webDatabase": {"message": "使用 WebDatabase 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webHID": {"message": "使用 WebHID 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webLocks": {"message": "使用 WebLocks 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webNfc": {"message": "使用 WebNfc 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webOTPService": {"message": "使用 WebOTPService 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webRTC": {"message": "使用 WebRTC 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webRTCSticky": {"message": "往返缓存已被停用，因为使用了 WebRTC。"}, "panels/application/components/BackForwardCacheStrings.ts | webShare": {"message": "使用 Webshare 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webSocket": {"message": "使用 WebSocket 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webSocketSticky": {"message": "往返缓存已被停用，因为使用了 WebSocket。"}, "panels/application/components/BackForwardCacheStrings.ts | webTransport": {"message": "使用 WebTransport 的网页无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheStrings.ts | webTransportSticky": {"message": "往返缓存已被停用，因为使用了 WebTransport。"}, "panels/application/components/BackForwardCacheStrings.ts | webXR": {"message": "使用 WebXR 的网页目前无法储存至往返缓存。"}, "panels/application/components/BackForwardCacheView.ts | backForwardCacheTitle": {"message": "往返缓存"}, "panels/application/components/BackForwardCacheView.ts | blankURLTitle": {"message": "缺少网址 [{PH1}]"}, "panels/application/components/BackForwardCacheView.ts | blockingExtensionId": {"message": "扩展程序 ID： "}, "panels/application/components/BackForwardCacheView.ts | circumstantial": {"message": "无法解决"}, "panels/application/components/BackForwardCacheView.ts | circumstantialExplanation": {"message": "这些原因无法作为行动依据，即：缓存被阻止的原因超出了该网页的直接控制范围。"}, "panels/application/components/BackForwardCacheView.ts | filesPerIssue": {"message": "{n,plural, =1{# 个文件}other{# 个文件}}"}, "panels/application/components/BackForwardCacheView.ts | framesPerIssue": {"message": "{n,plural, =1{# 个框架}other{# 个框架}}"}, "panels/application/components/BackForwardCacheView.ts | framesTitle": {"message": "框架"}, "panels/application/components/BackForwardCacheView.ts | issuesInMultipleFrames": {"message": "{n,plural, =1{在 {m} 个框架中发现了 # 个问题。}other{在 {m} 个框架中发现了 # 个问题。}}"}, "panels/application/components/BackForwardCacheView.ts | issuesInSingleFrame": {"message": "{n,plural, =1{在 1 个框架中发现了 # 个问题。}other{在 1 个框架中发现了 # 个问题。}}"}, "panels/application/components/BackForwardCacheView.ts | learnMore": {"message": "了解详情：往返缓存资格条件"}, "panels/application/components/BackForwardCacheView.ts | mainFrame": {"message": "主框架"}, "panels/application/components/BackForwardCacheView.ts | neverUseUnload": {"message": "了解详情：一律不使用卸载处理程序"}, "panels/application/components/BackForwardCacheView.ts | normalNavigation": {"message": "不是从往返缓存中恢复的：若要触发往返缓存，请使用 Chrome 的“后退”/“前进”按钮，或者使用下面的“测试”按钮自动离开并返回。"}, "panels/application/components/BackForwardCacheView.ts | pageSupportNeeded": {"message": "有待解决"}, "panels/application/components/BackForwardCacheView.ts | pageSupportNeededExplanation": {"message": "这些原因可作为行动依据，即：网页在排除这些原因后便能储存至往返缓存。"}, "panels/application/components/BackForwardCacheView.ts | restoredFromBFCache": {"message": "从往返缓存中成功恢复了。"}, "panels/application/components/BackForwardCacheView.ts | runTest": {"message": "测试往返缓存"}, "panels/application/components/BackForwardCacheView.ts | runningTest": {"message": "正在运行测试"}, "panels/application/components/BackForwardCacheView.ts | supportPending": {"message": "尚不支持"}, "panels/application/components/BackForwardCacheView.ts | supportPendingExplanation": {"message": "Chrome 将会针对这些原因提供应对支持，即：在 Chrome 的某个未来版本中，这些原因将不会阻止该网页储存至往返缓存。"}, "panels/application/components/BackForwardCacheView.ts | unavailable": {"message": "不可用"}, "panels/application/components/BackForwardCacheView.ts | unknown": {"message": "状态不明"}, "panels/application/components/BackForwardCacheView.ts | url": {"message": "网址："}, "panels/application/components/BounceTrackingMitigationsView.ts | bounceTrackingMitigationsTitle": {"message": "跳出跟踪缓解措施"}, "panels/application/components/BounceTrackingMitigationsView.ts | checkingPotentialTrackers": {"message": "正在检查是否有潜在的跳出跟踪网站。"}, "panels/application/components/BounceTrackingMitigationsView.ts | featureDisabled": {"message": "跳出跟踪缓解措施已被停用。如需启用，请将{PH1}对应的标志设置为“已启用且支持删除”。"}, "panels/application/components/BounceTrackingMitigationsView.ts | featureFlag": {"message": "“跳出跟踪缓解措施”功能标志"}, "panels/application/components/BounceTrackingMitigationsView.ts | forceRun": {"message": "强制运行"}, "panels/application/components/BounceTrackingMitigationsView.ts | learnMore": {"message": "了解详情：反弹跟踪缓解措施"}, "panels/application/components/BounceTrackingMitigationsView.ts | noPotentialBounceTrackersIdentified": {"message": "未清除任何潜在跳出跟踪网站的状态。这是因为未发现任何潜在跳出跟踪网站，或者未屏蔽第三方 Cookie。"}, "panels/application/components/BounceTrackingMitigationsView.ts | runningMitigations": {"message": "正在运行"}, "panels/application/components/BounceTrackingMitigationsView.ts | stateDeletedFor": {"message": "已删除以下网站的状态："}, "panels/application/components/EndpointsGrid.ts | noEndpointsToDisplay": {"message": "没有可显示的端点"}, "panels/application/components/FrameDetailsView.ts | aFrameAncestorIsAnInsecure": {"message": "祖先框架是非安全上下文"}, "panels/application/components/FrameDetailsView.ts | adStatus": {"message": "广告状态"}, "panels/application/components/FrameDetailsView.ts | additionalInformation": {"message": "其他信息"}, "panels/application/components/FrameDetailsView.ts | apiAvailability": {"message": "API 可用性"}, "panels/application/components/FrameDetailsView.ts | availabilityOfCertainApisDepends": {"message": "某些 API 的可用性取决于正在被跨源隔离的文档。"}, "panels/application/components/FrameDetailsView.ts | available": {"message": "可用"}, "panels/application/components/FrameDetailsView.ts | availableNotTransferable": {"message": "可用，不可传输"}, "panels/application/components/FrameDetailsView.ts | availableTransferable": {"message": "可用，可传输"}, "panels/application/components/FrameDetailsView.ts | child": {"message": "子级"}, "panels/application/components/FrameDetailsView.ts | childDescription": {"message": "此框架已被识别为某个广告的子框架"}, "panels/application/components/FrameDetailsView.ts | clickToRevealInElementsPanel": {"message": "点击即可在“元素”面板中显示"}, "panels/application/components/FrameDetailsView.ts | clickToRevealInNetworkPanel": {"message": "点击即可在“网络”面板中显示"}, "panels/application/components/FrameDetailsView.ts | clickToRevealInNetworkPanelMight": {"message": "点击即可在“网络”面板中显示（可能需要重新加载页面）"}, "panels/application/components/FrameDetailsView.ts | clickToRevealInSourcesPanel": {"message": "点击即可在“来源”面板中显示"}, "panels/application/components/FrameDetailsView.ts | contentSecurityPolicy": {"message": "内容安全政策 (CSP)"}, "panels/application/components/FrameDetailsView.ts | createdByAdScriptExplanation": {"message": "创建此框架时使用的(async) stack中有广告脚本。检查此框架的创建stack trace或许有助于深入了解这一问题。"}, "panels/application/components/FrameDetailsView.ts | creationStackTrace": {"message": "框架创建Stack Trace"}, "panels/application/components/FrameDetailsView.ts | creationStackTraceExplanation": {"message": "此框架是以编程方式创建的。stack trace会显示相应的创建位置。"}, "panels/application/components/FrameDetailsView.ts | creatorAdScript": {"message": "创作者广告脚本"}, "panels/application/components/FrameDetailsView.ts | crossoriginIsolated": {"message": "已跨源隔离"}, "panels/application/components/FrameDetailsView.ts | document": {"message": "文档"}, "panels/application/components/FrameDetailsView.ts | frameId": {"message": "框架 ID"}, "panels/application/components/FrameDetailsView.ts | learnMore": {"message": "了解详情"}, "panels/application/components/FrameDetailsView.ts | localhostIsAlwaysASecureContext": {"message": "Localhost 始终是安全的上下文"}, "panels/application/components/FrameDetailsView.ts | matchedBlockingRuleExplanation": {"message": "此框架被视为广告框架，因为它目前（或以前）的主文档是广告资源。"}, "panels/application/components/FrameDetailsView.ts | measureMemory": {"message": "衡量内存"}, "panels/application/components/FrameDetailsView.ts | no": {"message": "否"}, "panels/application/components/FrameDetailsView.ts | none": {"message": "无"}, "panels/application/components/FrameDetailsView.ts | origin": {"message": "源"}, "panels/application/components/FrameDetailsView.ts | originTrialsExplanation": {"message": "您可以通过源试用来使用新功能或实验性功能。"}, "panels/application/components/FrameDetailsView.ts | ownerElement": {"message": "所有者元素"}, "panels/application/components/FrameDetailsView.ts | parentIsAdExplanation": {"message": "此框架被视为广告框架，因为它的父框架是广告框架。"}, "panels/application/components/FrameDetailsView.ts | reportingTo": {"message": "报告对象："}, "panels/application/components/FrameDetailsView.ts | requiresCrossoriginIsolated": {"message": "需要跨域隔离的上下文"}, "panels/application/components/FrameDetailsView.ts | root": {"message": "根"}, "panels/application/components/FrameDetailsView.ts | rootDescription": {"message": "此框架已被识别为广告的根框架"}, "panels/application/components/FrameDetailsView.ts | secureContext": {"message": "安全上下文"}, "panels/application/components/FrameDetailsView.ts | securityIsolation": {"message": "安全与隔离"}, "panels/application/components/FrameDetailsView.ts | sharedarraybufferConstructorIs": {"message": "可以使用 SharedArrayBuffer 这一构造函数，且可通过 postMessage 传输 SABs"}, "panels/application/components/FrameDetailsView.ts | sharedarraybufferConstructorIsAvailable": {"message": "可以使用 SharedArrayBuffer 这一构造函数，但无法通过 postMessage 传输 SABs"}, "panels/application/components/FrameDetailsView.ts | theFramesSchemeIsInsecure": {"message": "框架的架构不安全"}, "panels/application/components/FrameDetailsView.ts | thePerformanceAPI": {"message": "可以使用 performance.measureUserAgentSpecificMemory() API"}, "panels/application/components/FrameDetailsView.ts | thePerformancemeasureuseragentspecificmemory": {"message": "无法使用 performance.measureUserAgentSpecificMemory() API"}, "panels/application/components/FrameDetailsView.ts | thisAdditionalDebugging": {"message": "显示此额外（调试）信息是因为已启用“协议监视器”实验。"}, "panels/application/components/FrameDetailsView.ts | transferRequiresCrossoriginIsolatedPermission": {"message": "若要传输 SharedArrayBuffer，必须启用以下权限政策："}, "panels/application/components/FrameDetailsView.ts | unavailable": {"message": "不可用"}, "panels/application/components/FrameDetailsView.ts | unreachableUrl": {"message": "无法访问的网址"}, "panels/application/components/FrameDetailsView.ts | url": {"message": "网址"}, "panels/application/components/FrameDetailsView.ts | willRequireCrossoriginIsolated": {"message": "⚠️ 未来将需要跨源隔离上下文"}, "panels/application/components/FrameDetailsView.ts | yes": {"message": "是"}, "panels/application/components/InterestGroupAccessGrid.ts | allInterestGroupStorageEvents": {"message": "所有兴趣群体存储事件。"}, "panels/application/components/InterestGroupAccessGrid.ts | eventTime": {"message": "事件时间"}, "panels/application/components/InterestGroupAccessGrid.ts | eventType": {"message": "权限类型"}, "panels/application/components/InterestGroupAccessGrid.ts | groupName": {"message": "名称"}, "panels/application/components/InterestGroupAccessGrid.ts | groupOwner": {"message": "所有者"}, "panels/application/components/InterestGroupAccessGrid.ts | noEvents": {"message": "未记录任何兴趣群体事件。"}, "panels/application/components/OriginTrialTreeView.ts | expiryTime": {"message": "到期时间"}, "panels/application/components/OriginTrialTreeView.ts | isThirdParty": {"message": "第三方"}, "panels/application/components/OriginTrialTreeView.ts | matchSubDomains": {"message": "子域名匹配"}, "panels/application/components/OriginTrialTreeView.ts | noTrialTokens": {"message": "没有试用令牌"}, "panels/application/components/OriginTrialTreeView.ts | origin": {"message": "源"}, "panels/application/components/OriginTrialTreeView.ts | rawTokenText": {"message": "原始令牌"}, "panels/application/components/OriginTrialTreeView.ts | status": {"message": "令牌状态"}, "panels/application/components/OriginTrialTreeView.ts | token": {"message": "令牌"}, "panels/application/components/OriginTrialTreeView.ts | tokens": {"message": "{PH1} 个令牌"}, "panels/application/components/OriginTrialTreeView.ts | trialName": {"message": "试用版名称"}, "panels/application/components/OriginTrialTreeView.ts | usageRestriction": {"message": "使用限制"}, "panels/application/components/PermissionsPolicySection.ts | allowedFeatures": {"message": "允许的功能"}, "panels/application/components/PermissionsPolicySection.ts | clickToShowHeader": {"message": "点击即可显示哪项请求的“Permissions-Policy”HTTP 标头会停用此功能。"}, "panels/application/components/PermissionsPolicySection.ts | clickToShowIframe": {"message": "点击即可在“元素”面板中显示不允许使用此功能的最顶部 iframe。"}, "panels/application/components/PermissionsPolicySection.ts | disabledByFencedFrame": {"message": "已在 fencedframe 内停用"}, "panels/application/components/PermissionsPolicySection.ts | disabledByHeader": {"message": "已被“Permissions-Policy”标头停用"}, "panels/application/components/PermissionsPolicySection.ts | disabledByIframe": {"message": "未纳入 iframe 的“allow”属性中"}, "panels/application/components/PermissionsPolicySection.ts | disabledFeatures": {"message": "停用的功能"}, "panels/application/components/PermissionsPolicySection.ts | hideDetails": {"message": "隐藏详细信息"}, "panels/application/components/PermissionsPolicySection.ts | showDetails": {"message": "显示详细信息"}, "panels/application/components/ProtocolHandlersView.ts | dropdownLabel": {"message": "选择协议处理程序"}, "panels/application/components/ProtocolHandlersView.ts | manifest": {"message": "清单"}, "panels/application/components/ProtocolHandlersView.ts | needHelpReadOur": {"message": "需要帮助？请阅读 {PH1} 上的文章。"}, "panels/application/components/ProtocolHandlersView.ts | protocolDetected": {"message": "在 {PH1} 中找到了有效注册的协议处理程序。安装应用后，测试已注册的协议。"}, "panels/application/components/ProtocolHandlersView.ts | protocolHandlerRegistrations": {"message": "PWA 的网址协议处理程序注册"}, "panels/application/components/ProtocolHandlersView.ts | protocolNotDetected": {"message": "在 {PH1} 中定义协议处理程序，以便在用户安装您的应用时将它注册为自定义协议的处理程序。"}, "panels/application/components/ProtocolHandlersView.ts | testProtocol": {"message": "测试协议"}, "panels/application/components/ProtocolHandlersView.ts | textboxLabel": {"message": "协议处理程序的查询参数或端点"}, "panels/application/components/ProtocolHandlersView.ts | textboxPlaceholder": {"message": "输入网址"}, "panels/application/components/ReportsGrid.ts | destination": {"message": "目标端点"}, "panels/application/components/ReportsGrid.ts | generatedAt": {"message": "生成时间"}, "panels/application/components/ReportsGrid.ts | noReportsToDisplay": {"message": "没有可显示的报告"}, "panels/application/components/ReportsGrid.ts | status": {"message": "状态"}, "panels/application/components/SharedStorageAccessGrid.ts | allSharedStorageEvents": {"message": "此网页的所有共享存储空间事件。"}, "panels/application/components/SharedStorageAccessGrid.ts | eventParams": {"message": "可选事件参数"}, "panels/application/components/SharedStorageAccessGrid.ts | eventTime": {"message": "事件时间"}, "panels/application/components/SharedStorageAccessGrid.ts | eventType": {"message": "权限类型"}, "panels/application/components/SharedStorageAccessGrid.ts | mainFrameId": {"message": "主框架 ID"}, "panels/application/components/SharedStorageAccessGrid.ts | noEvents": {"message": "未记录任何共享存储空间事件。"}, "panels/application/components/SharedStorageAccessGrid.ts | ownerOrigin": {"message": "所有者源"}, "panels/application/components/SharedStorageAccessGrid.ts | sharedStorage": {"message": "共享存储空间"}, "panels/application/components/SharedStorageMetadataView.ts | budgetExplanation": {"message": "此源在 24 小时内还可泄露多少数据（以位熵为单位）"}, "panels/application/components/SharedStorageMetadataView.ts | creation": {"message": "创建时间"}, "panels/application/components/SharedStorageMetadataView.ts | entropyBudget": {"message": "围栏框架的熵预算"}, "panels/application/components/SharedStorageMetadataView.ts | notYetCreated": {"message": "尚未创建"}, "panels/application/components/SharedStorageMetadataView.ts | numBytesUsed": {"message": "已用字节数"}, "panels/application/components/SharedStorageMetadataView.ts | numEntries": {"message": "条目数"}, "panels/application/components/SharedStorageMetadataView.ts | resetBudget": {"message": "重置预算"}, "panels/application/components/SharedStorageMetadataView.ts | sharedStorage": {"message": "共享存储空间"}, "panels/application/components/StackTrace.ts | cannotRenderStackTrace": {"message": "无法渲染堆栈轨迹"}, "panels/application/components/StackTrace.ts | creationStackTrace": {"message": "框架创建Stack Trace"}, "panels/application/components/StackTrace.ts | showLess": {"message": "收起"}, "panels/application/components/StackTrace.ts | showSMoreFrames": {"message": "{n,plural, =1{显示另外 # 个框架}other{显示另外 # 个框架}}"}, "panels/application/components/StorageMetadataView.ts | bucketName": {"message": "存储桶名称"}, "panels/application/components/StorageMetadataView.ts | confirmBucketDeletion": {"message": "删除“{PH1}”存储桶？"}, "panels/application/components/StorageMetadataView.ts | defaultBucket": {"message": "默认存储桶"}, "panels/application/components/StorageMetadataView.ts | deleteBucket": {"message": "删除存储桶"}, "panels/application/components/StorageMetadataView.ts | durability": {"message": "耐用性"}, "panels/application/components/StorageMetadataView.ts | expiration": {"message": "过期时间"}, "panels/application/components/StorageMetadataView.ts | isOpaque": {"message": "不透明"}, "panels/application/components/StorageMetadataView.ts | isThirdParty": {"message": "属于第三方"}, "panels/application/components/StorageMetadataView.ts | loading": {"message": "正在加载…"}, "panels/application/components/StorageMetadataView.ts | no": {"message": "否"}, "panels/application/components/StorageMetadataView.ts | none": {"message": "无"}, "panels/application/components/StorageMetadataView.ts | opaque": {"message": "（不透明）"}, "panels/application/components/StorageMetadataView.ts | origin": {"message": "源"}, "panels/application/components/StorageMetadataView.ts | persistent": {"message": "是永久的"}, "panels/application/components/StorageMetadataView.ts | quota": {"message": "配额"}, "panels/application/components/StorageMetadataView.ts | topLevelSite": {"message": "顶级网站"}, "panels/application/components/StorageMetadataView.ts | yes": {"message": "是"}, "panels/application/components/StorageMetadataView.ts | yesBecauseAncestorChainHasCrossSite": {"message": "是，因为祖先链包含第三方源"}, "panels/application/components/StorageMetadataView.ts | yesBecauseKeyIsOpaque": {"message": "是，因为存储密钥是不透明的"}, "panels/application/components/StorageMetadataView.ts | yesBecauseOriginNotInTopLevelSite": {"message": "是，因为相应源不属于该顶级网站"}, "panels/application/components/StorageMetadataView.ts | yesBecauseTopLevelIsOpaque": {"message": "是，因为顶级网站是不透明的"}, "panels/application/components/TrustTokensView.ts | allStoredTrustTokensAvailableIn": {"message": "在此浏览器实例中可用的所有已存储私密状态令牌。"}, "panels/application/components/TrustTokensView.ts | deleteTrustTokens": {"message": "删除 {PH1} 颁发的所有已存储私密状态令牌。"}, "panels/application/components/TrustTokensView.ts | issuer": {"message": "颁发者"}, "panels/application/components/TrustTokensView.ts | noTrustTokensStored": {"message": "尚未存储任何私密状态令牌。"}, "panels/application/components/TrustTokensView.ts | storedTokenCount": {"message": "已存储的令牌数"}, "panels/application/components/TrustTokensView.ts | trustTokens": {"message": "私密状态令牌"}, "panels/application/preloading/PreloadingView.ts | filterAllPreloads": {"message": "所有推测加载"}, "panels/application/preloading/PreloadingView.ts | filterFilterByRuleSet": {"message": "按规则集过滤"}, "panels/application/preloading/PreloadingView.ts | statusFailure": {"message": "失败"}, "panels/application/preloading/PreloadingView.ts | statusNotTriggered": {"message": "未触发"}, "panels/application/preloading/PreloadingView.ts | statusPending": {"message": "待处理"}, "panels/application/preloading/PreloadingView.ts | statusReady": {"message": "已就绪"}, "panels/application/preloading/PreloadingView.ts | statusRunning": {"message": "正在运行"}, "panels/application/preloading/PreloadingView.ts | statusSuccess": {"message": "成功"}, "panels/application/preloading/PreloadingView.ts | validityInvalid": {"message": "无效"}, "panels/application/preloading/PreloadingView.ts | validitySomeRulesInvalid": {"message": "部分规则无效"}, "panels/application/preloading/PreloadingView.ts | validityValid": {"message": "有效"}, "panels/application/preloading/components/MismatchedPreloadingGrid.ts | action": {"message": "操作"}, "panels/application/preloading/components/MismatchedPreloadingGrid.ts | status": {"message": "状态"}, "panels/application/preloading/components/MismatchedPreloadingGrid.ts | statusFailure": {"message": "失败"}, "panels/application/preloading/components/MismatchedPreloadingGrid.ts | statusNotTriggered": {"message": "未触发"}, "panels/application/preloading/components/MismatchedPreloadingGrid.ts | statusPending": {"message": "待处理"}, "panels/application/preloading/components/MismatchedPreloadingGrid.ts | statusReady": {"message": "已就绪"}, "panels/application/preloading/components/MismatchedPreloadingGrid.ts | statusRunning": {"message": "正在运行"}, "panels/application/preloading/components/MismatchedPreloadingGrid.ts | statusSuccess": {"message": "成功"}, "panels/application/preloading/components/MismatchedPreloadingGrid.ts | url": {"message": "网址"}, "panels/application/preloading/components/PreloadingDetailsReportView.ts | buttonClickToInspect": {"message": "点击即可检查预渲染的页面"}, "panels/application/preloading/components/PreloadingDetailsReportView.ts | buttonClickToRevealRuleSet": {"message": "点击即可显示规则集"}, "panels/application/preloading/components/PreloadingDetailsReportView.ts | buttonInspect": {"message": "检查"}, "panels/application/preloading/components/PreloadingDetailsReportView.ts | detailedStatusFailure": {"message": "推测加载失败。"}, "panels/application/preloading/components/PreloadingDetailsReportView.ts | detailedStatusNotTriggered": {"message": "推测加载尝试尚未触发。"}, "panels/application/preloading/components/PreloadingDetailsReportView.ts | detailedStatusPending": {"message": "推测加载尝试符合条件，但尚待处理。"}, "panels/application/preloading/components/PreloadingDetailsReportView.ts | detailedStatusReady": {"message": "推测加载完毕，结果已就绪，可供下次导航使用。"}, "panels/application/preloading/components/PreloadingDetailsReportView.ts | detailedStatusRunning": {"message": "正在运行推测加载。"}, "panels/application/preloading/components/PreloadingDetailsReportView.ts | detailedStatusSuccess": {"message": "推测加载完毕，且已用于导航。"}, "panels/application/preloading/components/PreloadingDetailsReportView.ts | detailsAction": {"message": "操作"}, "panels/application/preloading/components/PreloadingDetailsReportView.ts | detailsDetailedInformation": {"message": "详细信息"}, "panels/application/preloading/components/PreloadingDetailsReportView.ts | detailsFailureReason": {"message": "失败原因"}, "panels/application/preloading/components/PreloadingDetailsReportView.ts | detailsRuleSet": {"message": "规则集"}, "panels/application/preloading/components/PreloadingDetailsReportView.ts | detailsStatus": {"message": "状态"}, "panels/application/preloading/components/PreloadingDetailsReportView.ts | selectAnElementForMoreDetails": {"message": "选择一个元素即可了解更多详情"}, "panels/application/preloading/components/PreloadingDisabledInfobar.ts | descriptionDisabledByBatterySaver": {"message": "由于操作系统启用了省电模式，推测加载已停用。"}, "panels/application/preloading/components/PreloadingDisabledInfobar.ts | descriptionDisabledByDataSaver": {"message": "由于操作系统启用了“流量节省程序”模式，推测加载已停用。"}, "panels/application/preloading/components/PreloadingDisabledInfobar.ts | descriptionDisabledByHoldbackPrefetchSpeculationRules": {"message": "系统已强制启用预提取功能，因为开发者工具已打开。关闭开发者工具后，系统将停用预提取功能，因为此浏览器会话属于用于比较性能的对照组。"}, "panels/application/preloading/components/PreloadingDisabledInfobar.ts | descriptionDisabledByHoldbackPrerenderSpeculationRules": {"message": "由于开发者工具已打开，因此系统已强制启用预渲染功能。关闭开发者工具后，系统将停用预渲染功能，因为此浏览器会话属于用于比较性能的对照组。"}, "panels/application/preloading/components/PreloadingDisabledInfobar.ts | descriptionDisabledByPreference": {"message": "推测加载由于用户设置或扩展程序而停用。若要更新您的偏好设置，请前往 {PH1}。若要停用任何阻止推测加载的扩展程序，请前往 {PH2}。"}, "panels/application/preloading/components/PreloadingDisabledInfobar.ts | extensionsSettings": {"message": "扩展程序设置"}, "panels/application/preloading/components/PreloadingDisabledInfobar.ts | footerLearnMore": {"message": "了解详情"}, "panels/application/preloading/components/PreloadingDisabledInfobar.ts | headerDisabledByBatterySaver": {"message": "省电模式"}, "panels/application/preloading/components/PreloadingDisabledInfobar.ts | headerDisabledByDataSaver": {"message": "流量节省程序"}, "panels/application/preloading/components/PreloadingDisabledInfobar.ts | headerDisabledByHoldbackPrefetchSpeculationRules": {"message": "预提取功能曾被停用，但现已被强制启用"}, "panels/application/preloading/components/PreloadingDisabledInfobar.ts | headerDisabledByHoldbackPrerenderSpeculationRules": {"message": "预渲染功能之前已停用，但现已强制启用"}, "panels/application/preloading/components/PreloadingDisabledInfobar.ts | headerDisabledByPreference": {"message": "用户设置或扩展程序"}, "panels/application/preloading/components/PreloadingDisabledInfobar.ts | infobarPreloadingIsDisabled": {"message": "已停用推测加载"}, "panels/application/preloading/components/PreloadingDisabledInfobar.ts | infobarPreloadingIsForceEnabled": {"message": "已强制启用推测加载"}, "panels/application/preloading/components/PreloadingDisabledInfobar.ts | preloadingPagesSettings": {"message": "预加载网页设置"}, "panels/application/preloading/components/PreloadingDisabledInfobar.ts | titleReasonsPreventingPreloading": {"message": "阻止推测加载的原因"}, "panels/application/preloading/components/PreloadingGrid.ts | action": {"message": "操作"}, "panels/application/preloading/components/PreloadingGrid.ts | ruleSet": {"message": "规则集"}, "panels/application/preloading/components/PreloadingGrid.ts | status": {"message": "状态"}, "panels/application/preloading/components/PreloadingMismatchedHeadersGrid.ts | activationNavigationValue": {"message": "激活导航中的值"}, "panels/application/preloading/components/PreloadingMismatchedHeadersGrid.ts | headerName": {"message": "标头名称"}, "panels/application/preloading/components/PreloadingMismatchedHeadersGrid.ts | initialNavigationValue": {"message": "初始导航中的值"}, "panels/application/preloading/components/PreloadingMismatchedHeadersGrid.ts | missing": {"message": "（缺失）"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchEvictedAfterCandidateRemoved": {"message": "预提取操作已被舍弃，因为发起页中不再有推测规则可以触发针对此网址的预提取操作。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchEvictedForNewerPrefetch": {"message": "预提取操作已被舍弃，因为发起页中已有太多正在进行的预提取操作，而此操作为最早的一项。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchFailedIneligibleRedirect": {"message": "预提取已重定向，但重定向网址无法进行预提取。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchFailedInvalidRedirect": {"message": "预提取已重定向，但重定向出现问题。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchFailedMIMENotSupported": {"message": "预提取失败，因为响应的 Content-Type 标头不受支持。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchFailedNetError": {"message": "预提取失败，因为发生了网络连接错误。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchFailedNon2XX": {"message": "预提取失败，因为 HTTP 响应的状态代码不是 2xx。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchFailedPerPageLimitExceeded": {"message": "未执行预提取，因为发起页中已有太多正在进行的预提取。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchIneligibleRetryAfter": {"message": "之前针对该源执行的预提取操作收到了 HTTP 503 响应，响应中包含的 Retry-After 标头尚未过期。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchIsPrivacyDecoy": {"message": "无法预提取该网址，因为相应源已有注册的 Service Worker 或跨网站 Cookie，但系统仍将预提取的资源放置在网络上且没有使用，以掩盖用户以前与该源存在某种关系。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchIsStale": {"message": "由于预提取资源和使用资源之间的间隔时间过长，预提取的资源被舍弃了。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchNotEligibleBatterySaverEnabled": {"message": "未执行预提取，因为系统启用了“省电模式”设置。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchNotEligibleBrowserContextOffTheRecord": {"message": "未执行预提取，因为浏览器处于无痕模式或访客模式。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchNotEligibleDataSaverEnabled": {"message": "未执行预提取，因为操作系统处于省流量模式。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchNotEligibleExistingProxy": {"message": "无法预提取该网址，因为在默认网络环境中，该网址已配置为使用代理服务器。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchNotEligibleHostIsNonUnique": {"message": "无法预提取该网址，因为该网址的主机不是唯一的（例如，不可公开路由的 IP 地址或不受注册表控制的主机名），但预提取需要通过代理才能进行。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchNotEligibleNonDefaultStoragePartition": {"message": "无法预提取该网址，因为该网址使用的是非默认存储分区。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchNotEligiblePreloadingDisabled": {"message": "未执行预提取，因为推测加载已停用。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchNotEligibleSameSiteCrossOriginPrefetchRequiredProxy": {"message": "无法预提取该网址，因为无法将默认网络环境配置为针对同网站跨源预提取请求使用预提取代理。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchNotEligibleSchemeIsNotHttps": {"message": "无法预提取该网址，因为该网址的 Scheme 不是 https:。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchNotEligibleUserHasCookies": {"message": "无法预提取该网址，因为预提取的是跨网站资源，但用户已有相应源的 Cookie。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchNotEligibleUserHasServiceWorker": {"message": "无法预提取该网址，因为相应源已有注册的 Service Worker，但系统目前不支持该 Service Worker。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchNotUsedCookiesChanged": {"message": "未使用预提取功能，因为预提取的是跨网站资源。此外，系统在预提取进行期间已为相应网址添加了 Cookie，因此预提取的响应现已过期。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchNotUsedProbeFailed": {"message": "预提取被您的互联网服务提供商或网络管理员阻止了。"}, "panels/application/preloading/components/PreloadingString.ts | PrefetchProxyNotAvailable": {"message": "尝试设置与预提取代理的连接时发生网络连接错误。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusActivatedDuringMainFrameNavigation": {"message": "在发起页的主框架导航期间激活的预渲染网页。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusActivatedWithAuxiliaryBrowsingContexts": {"message": "未使用预渲染，因为激活期间有其他窗口存在对发起页的有效 opener 引用，目前不支持这种情况。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusActivationFramePolicyNotCompatible": {"message": "未使用预渲染，因为就沙盒标记或权限政策而言，发起页与预渲染页面不兼容。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusActivationNavigationParameterMismatch": {"message": "未使用预渲染，因为激活期间计算的导航参数（例如 HTTP 标头）不同于原始预渲染导航请求期间计算的参数。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusActivationUrlHasEffectiveUrl": {"message": "未使用预渲染，因为激活期间导航的有效网址不同于它的常规网址。（例如，“新标签页”页面或托管的应用。）"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusAllPrerenderingCanceled": {"message": "由于某些原因（例如在预渲染期间调用了 WebViewCompat.addWebMessageListener()），浏览器卸载了所有预渲染的网页。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusAudioOutputDeviceRequested": {"message": "预渲染的网页请求进行音频输出，但目前不支持该操作。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusBatterySaverEnabled": {"message": "未执行预渲染，因为用户已请求浏览器降低耗电量。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusBlockedByClient": {"message": "已阻止加载部分资源。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusClientCertRequested": {"message": "预渲染导航要求提供 HTTP 客户端证书。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusCrossSiteNavigationInInitialNavigation": {"message": "预渲染导航失败，因为定向到了跨网站网址。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusCrossSiteNavigationInMainFrameNavigation": {"message": "预渲染的网页导航到了跨网站网址。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusCrossSiteRedirectInInitialNavigation": {"message": "预渲染导航失败，因为预渲染的网址重定向到了跨网站网址。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusCrossSiteRedirectInMainFrameNavigation": {"message": "预渲染的网页导航到的网址重定向到了跨网站网址。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusDataSaverEnabled": {"message": "未执行预渲染，因为用户已请求浏览器节省数据流量。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusDownload": {"message": "预渲染的网页尝试发起下载，但该操作目前不受支持。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusHasEffectiveUrl": {"message": "发起页无法执行预渲染，因为该页面的有效网址不同于它的常规网址。（例如，“新标签页”页面或托管的应用。）"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusInvalidSchemeNavigation": {"message": "由于网址的架构不是 http: 或 https:，因此无法进行预渲染。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusInvalidSchemeRedirect": {"message": "预渲染导航失败，因为它重定向到的网址不是 http: 或 https: 架构。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusJavaScriptInterfaceAdded": {"message": "由于 WebView.addJavascriptInterface() 已注入新的 JavaScript 接口，预渲染的网页已被卸载。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusJavaScriptInterfaceRemoved": {"message": "由于 WebView.removeJavascriptInterface() 已移除 JavaScript 接口，预渲染的网页已被卸载。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusLoginAuthRequested": {"message": "预渲染导航要求进行 HTTP 身份验证，但目前不支持这种验证。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusLowEndDevice": {"message": "未执行预渲染，因为此设备没有足够的系统总内存来支持预渲染。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusMainFrameNavigation": {"message": "预渲染的网页自行导航到了另一个网址，但该网址目前不受支持。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusMaxNumOfRunningEagerPrerendersExceeded": {"message": "未执行紧急程度为“紧急”的预渲染，因为发起页中已有太多正在进行的预渲染。请移除其他“紧急”推测规则，以便执行进一步的预渲染。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusMaxNumOfRunningEmbedderPrerendersExceeded": {"message": "未执行浏览器触发的预渲染，因为发起页中已有太多正在进行的预渲染。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusMaxNumOfRunningNonEagerPrerendersExceeded": {"message": "由于正在开始新的非紧急预渲染，旧的非紧急预渲染（即紧急程度为“一般”或“暂缓”，且由鼠标在链接上方悬停或点击链接而触发的预渲染）已自动取消。如果用户再次与该链接互动，就会重新触发预渲染。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusMemoryLimitExceeded": {"message": "未执行预渲染，因为浏览器的内存用量超出了预渲染内存上限。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusMemoryPressureAfterTriggered": {"message": "由于浏览器面临极大的内存压力，因此预渲染的网页被卸载了。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusMemoryPressureOnTrigger": {"message": "未执行预渲染，因为浏览器面临极大的内存压力。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusMixedContent": {"message": "预渲染的网页包含混合内容。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusMojoBinderPolicy": {"message": "预渲染的页面使用了被禁用的 JavaScript API，而且该 API 目前不受支持。（内部 Mojo 接口：{PH1}）"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusNavigationBadHttpStatus": {"message": "预渲染导航失败，因为 HTTP 响应状态代码不是 2xx。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusNavigationRequestBlockedByCsp": {"message": "预渲染导航被内容安全政策阻止了。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusNavigationRequestNetworkError": {"message": "预渲染导航遇到了网络连接错误。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusPreloadingDisabled": {"message": "由于用户在其浏览器设置中停用了预加载，因此无法进行预渲染"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusPrerenderingDisabledByDevTools": {"message": "未执行预渲染，因为已使用开发者工具停用预渲染。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusPrerenderingUrlHasEffectiveUrl": {"message": "预渲染导航失败，因为此次导航的有效网址不同于它的常规网址。（例如，“新标签页”页面或托管的应用。）"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusPrimaryMainFrameRendererProcessCrashed": {"message": "发起页崩溃了。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusPrimaryMainFrameRendererProcessKilled": {"message": "发起页被终止了。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusRedirectedPrerenderingUrlHasEffectiveUrl": {"message": "预渲染导航失败，因为此次导航重定向到的有效网址不同于它的常规网址。（例如，“新标签页”页面或托管的应用。）"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusRendererProcessCrashed": {"message": "预渲染的网页崩溃了。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusRendererProcessKilled": {"message": "预渲染的网页被终止了。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusSameSiteCrossOriginNavigationNotOptInInInitialNavigation": {"message": "预渲染导航失败，因为此次导航重定向到了同网站跨源网址，但目标响应未包含相应的 Supports-Loading-Mode 标头。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusSameSiteCrossOriginNavigationNotOptInInMainFrameNavigation": {"message": "预渲染的网页导航到了同网站跨源网址，但目标响应未包含相应的 Supports-Loading-Mode 标头。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusSameSiteCrossOriginRedirectNotOptInInInitialNavigation": {"message": "预渲染导航失败，因为预渲染的网址重定向到了同网站跨源网址，但目标响应未包含相应的 Supports-Loading-Mode 标头。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusSameSiteCrossOriginRedirectNotOptInInMainFrameNavigation": {"message": "预渲染的网页导航到的网址重定向到了同网站跨源网址，但目标响应未包含相应的 Supports-Loading-Mode 标头。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusSpeculationRuleRemoved": {"message": "预渲染的网页已被卸载，因为发起页从 <script type=\"speculationrules\"> 中移除了相应的预渲染规则。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusSslCertificateError": {"message": "预渲染导航失败，因为 SSL 证书无效。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusTimeoutBackgrounded": {"message": "预渲染的网页被舍弃了，因为发起页已转至后台运行且已运行很长时间。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusTriggerBackgrounded": {"message": "由于发起页已转至后台运行，因此预渲染的网页被舍弃了。"}, "panels/application/preloading/components/PreloadingString.ts | prerenderFinalStatusUaChangeRequiresReload": {"message": "预渲染导航过程中用户代理发生了更改。"}, "panels/application/preloading/components/PreloadingString.ts | statusFailure": {"message": "失败"}, "panels/application/preloading/components/PreloadingString.ts | statusNotTriggered": {"message": "未触发"}, "panels/application/preloading/components/PreloadingString.ts | statusPending": {"message": "待处理"}, "panels/application/preloading/components/PreloadingString.ts | statusReady": {"message": "已就绪"}, "panels/application/preloading/components/PreloadingString.ts | statusRunning": {"message": "正在运行"}, "panels/application/preloading/components/PreloadingString.ts | statusSuccess": {"message": "成功"}, "panels/application/preloading/components/RuleSetGrid.ts | buttonClickToRevealInElementsPanel": {"message": "点击即可在“元素”面板中显示"}, "panels/application/preloading/components/RuleSetGrid.ts | buttonClickToRevealInNetworkPanel": {"message": "点击即可在“网络”面板中显示"}, "panels/application/preloading/components/RuleSetGrid.ts | buttonRevealPreloadsAssociatedWithRuleSet": {"message": "显示与此规则集关联的推测加载"}, "panels/application/preloading/components/RuleSetGrid.ts | errors": {"message": "{errorCount,plural, =1{# 个错误}other{# 个错误}}"}, "panels/application/preloading/components/RuleSetGrid.ts | ruleSet": {"message": "规则集"}, "panels/application/preloading/components/RuleSetGrid.ts | status": {"message": "状态"}, "panels/application/preloading/components/UsedPreloadingView.ts | badgeFailure": {"message": "失败"}, "panels/application/preloading/components/UsedPreloadingView.ts | badgeFailureWithCount": {"message": "{n,plural, =1{有 # 项已失败}other{有 # 项已失败}}"}, "panels/application/preloading/components/UsedPreloadingView.ts | badgeInProgressWithCount": {"message": "{n,plural, =1{有 # 项正在执行}other{有 # 项正在执行}}"}, "panels/application/preloading/components/UsedPreloadingView.ts | badgeNoSpeculativeLoads": {"message": "无推测加载"}, "panels/application/preloading/components/UsedPreloadingView.ts | badgeNotTriggeredWithCount": {"message": "{n,plural, =1{有 # 项未触发}other{有 # 项未触发}}"}, "panels/application/preloading/components/UsedPreloadingView.ts | badgeSuccess": {"message": "成功"}, "panels/application/preloading/components/UsedPreloadingView.ts | badgeSuccessWithCount": {"message": "{n,plural, =1{成功 # 次}other{成功 # 次}}"}, "panels/application/preloading/components/UsedPreloadingView.ts | currentURL": {"message": "当前网址"}, "panels/application/preloading/components/UsedPreloadingView.ts | detailsFailureReason": {"message": "失败原因"}, "panels/application/preloading/components/UsedPreloadingView.ts | downgradedPrefetchUsed": {"message": "发起页尝试预渲染此页面的网址。预渲染失败，但生成的响应正文仍被用作预提取。"}, "panels/application/preloading/components/UsedPreloadingView.ts | learnMore": {"message": "如要详细了解推测加载，请访问 developer.chrome.com"}, "panels/application/preloading/components/UsedPreloadingView.ts | mismatchedHeadersDetail": {"message": "HTTP 请求标头不匹配"}, "panels/application/preloading/components/UsedPreloadingView.ts | noPreloads": {"message": "发起页未尝试推测加载此页面的网址。"}, "panels/application/preloading/components/UsedPreloadingView.ts | prefetchFailed": {"message": "发起页尝试预提取此页面的网址，但预提取失败，所以系统改为执行完整的导航。"}, "panels/application/preloading/components/UsedPreloadingView.ts | prefetchUsed": {"message": "已成功预提取此页面。"}, "panels/application/preloading/components/UsedPreloadingView.ts | preloadedURLs": {"message": "发起页正在推测加载的网址"}, "panels/application/preloading/components/UsedPreloadingView.ts | prerenderFailed": {"message": "发起页尝试预渲染此页面的网址，但预渲染失败，所以系统改为执行完整的导航。"}, "panels/application/preloading/components/UsedPreloadingView.ts | prerenderUsed": {"message": "此页面已成功预渲染。"}, "panels/application/preloading/components/UsedPreloadingView.ts | speculationsInitiatedByThisPage": {"message": "由此网页发起的推测"}, "panels/application/preloading/components/UsedPreloadingView.ts | speculativeLoadingStatusForThisPage": {"message": "此网页的推测加载状态"}, "panels/application/preloading/components/UsedPreloadingView.ts | viewAllRules": {"message": "查看所有推测规则"}, "panels/application/preloading/components/UsedPreloadingView.ts | viewAllSpeculations": {"message": "查看所有推测"}, "panels/autofill/AutofillView.ts | addressPreview": {"message": "地址预览"}, "panels/autofill/AutofillView.ts | attr": {"message": "属性"}, "panels/autofill/AutofillView.ts | autoShow": {"message": "自动打开此面板"}, "panels/autofill/AutofillView.ts | autoShowTooltip": {"message": "当系统检测到自动填充活动时，自动打开自动填充面板。"}, "panels/autofill/AutofillView.ts | autocompleteAttribute": {"message": "自动补全属性"}, "panels/autofill/AutofillView.ts | formField": {"message": "表单字段"}, "panels/autofill/AutofillView.ts | formInspector": {"message": "表单检查器"}, "panels/autofill/AutofillView.ts | heur": {"message": "启发法"}, "panels/autofill/AutofillView.ts | inferredByHeuristics": {"message": "基于启发法的推理"}, "panels/autofill/AutofillView.ts | learnMore": {"message": "了解详情"}, "panels/autofill/AutofillView.ts | predictedAutofillValue": {"message": "预测的自动填充值"}, "panels/autofill/AutofillView.ts | sendFeedback": {"message": "发送反馈"}, "panels/autofill/AutofillView.ts | toStartDebugging": {"message": "若要开始调试自动填充功能，请使用 Chrome 的自动填充菜单填充地址表单。"}, "panels/autofill/AutofillView.ts | value": {"message": "值"}, "panels/autofill/autofill-meta.ts | autofill": {"message": "自动填充"}, "panels/autofill/autofill-meta.ts | showAutofill": {"message": "显示自动填充"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | animation": {"message": "动画"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | auctionWorklet": {"message": "广告竞价 Worklet"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | breakpointHit": {"message": "断点命中"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | canvas": {"message": "画布"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | clipboard": {"message": "剪贴板"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | control": {"message": "控制"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | device": {"message": "设备"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | domMutation": {"message": "DOM 变更"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | dragDrop": {"message": "拖/放"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | geolocation": {"message": "地理定位"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | keyboard": {"message": "键盘"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | load": {"message": "加载"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | media": {"message": "媒体"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | mouse": {"message": "鼠标"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | notification": {"message": "通知"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | parse": {"message": "解析"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | pictureinpicture": {"message": "画中画"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | pointer": {"message": "指针"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | script": {"message": "脚本"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | sharedStorageWorklet": {"message": "共享存储空间 Worklet"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | timer": {"message": "定时器"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | touch": {"message": "轻触"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | trustedTypeViolations": {"message": "Trusted Type 违规问题"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | webaudio": {"message": "WebAudio"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | window": {"message": "窗口"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | worker": {"message": "Worker"}, "panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | xhr": {"message": "XHR"}, "panels/browser_debugger/DOMBreakpointsSidebarPane.ts | attributeModified": {"message": "已修改属性"}, "panels/browser_debugger/DOMBreakpointsSidebarPane.ts | breakOn": {"message": "发生中断的条件"}, "panels/browser_debugger/DOMBreakpointsSidebarPane.ts | breakpointHit": {"message": "断点命中"}, "panels/browser_debugger/DOMBreakpointsSidebarPane.ts | breakpointRemoved": {"message": "移除了断点"}, "panels/browser_debugger/DOMBreakpointsSidebarPane.ts | breakpointSet": {"message": "断点设置"}, "panels/browser_debugger/DOMBreakpointsSidebarPane.ts | checked": {"message": "已勾选"}, "panels/browser_debugger/DOMBreakpointsSidebarPane.ts | domBreakpointsList": {"message": "DOM 断点列表"}, "panels/browser_debugger/DOMBreakpointsSidebarPane.ts | noBreakpoints": {"message": "无断点"}, "panels/browser_debugger/DOMBreakpointsSidebarPane.ts | nodeRemoved": {"message": "节点已移除"}, "panels/browser_debugger/DOMBreakpointsSidebarPane.ts | removeAllDomBreakpoints": {"message": "移除所有 DOM 断点"}, "panels/browser_debugger/DOMBreakpointsSidebarPane.ts | removeBreakpoint": {"message": "移除断点"}, "panels/browser_debugger/DOMBreakpointsSidebarPane.ts | revealDomNodeInElementsPanel": {"message": "在“元素”面板中显示 DOM 节点"}, "panels/browser_debugger/DOMBreakpointsSidebarPane.ts | sBreakpointHit": {"message": "遇到{PH1}断点"}, "panels/browser_debugger/DOMBreakpointsSidebarPane.ts | sS": {"message": "{PH1}：{PH2}"}, "panels/browser_debugger/DOMBreakpointsSidebarPane.ts | sSS": {"message": "{PH1}：{PH2}，{PH3}"}, "panels/browser_debugger/DOMBreakpointsSidebarPane.ts | subtreeModified": {"message": "已修改子树"}, "panels/browser_debugger/DOMBreakpointsSidebarPane.ts | unchecked": {"message": "未选中"}, "panels/browser_debugger/XHRBreakpointsSidebarPane.ts | addBreakpoint": {"message": "添加断点"}, "panels/browser_debugger/XHRBreakpointsSidebarPane.ts | addXhrfetchBreakpoint": {"message": "添加 XHR/Fetch 断点"}, "panels/browser_debugger/XHRBreakpointsSidebarPane.ts | anyXhrOrFetch": {"message": "任何 XHR 或提取"}, "panels/browser_debugger/XHRBreakpointsSidebarPane.ts | breakWhenUrlContains": {"message": "网址包含以下内容时中断："}, "panels/browser_debugger/XHRBreakpointsSidebarPane.ts | breakpointHit": {"message": "断点命中"}, "panels/browser_debugger/XHRBreakpointsSidebarPane.ts | noBreakpoints": {"message": "无断点"}, "panels/browser_debugger/XHRBreakpointsSidebarPane.ts | removeAllBreakpoints": {"message": "移除所有断点"}, "panels/browser_debugger/XHRBreakpointsSidebarPane.ts | removeBreakpoint": {"message": "移除断点"}, "panels/browser_debugger/XHRBreakpointsSidebarPane.ts | urlBreakpoint": {"message": "网址断点"}, "panels/browser_debugger/XHRBreakpointsSidebarPane.ts | urlContainsS": {"message": "网址包含“{PH1}”"}, "panels/browser_debugger/XHRBreakpointsSidebarPane.ts | xhrfetchBreakpoints": {"message": "XHR/提取断点"}, "panels/browser_debugger/browser_debugger-meta.ts | contentScripts": {"message": "内容脚本"}, "panels/browser_debugger/browser_debugger-meta.ts | cspViolationBreakpoints": {"message": "CSP 违规断点"}, "panels/browser_debugger/browser_debugger-meta.ts | domBreakpoints": {"message": "DOM 断点"}, "panels/browser_debugger/browser_debugger-meta.ts | eventListenerBreakpoints": {"message": "事件监听器断点"}, "panels/browser_debugger/browser_debugger-meta.ts | globalListeners": {"message": "全局监听器"}, "panels/browser_debugger/browser_debugger-meta.ts | overrides": {"message": "替换"}, "panels/browser_debugger/browser_debugger-meta.ts | page": {"message": "网页"}, "panels/browser_debugger/browser_debugger-meta.ts | refreshGlobalListeners": {"message": "刷新全局监听器"}, "panels/browser_debugger/browser_debugger-meta.ts | showContentScripts": {"message": "显示内容脚本"}, "panels/browser_debugger/browser_debugger-meta.ts | showCspViolationBreakpoints": {"message": "显示 CSP 违规断点"}, "panels/browser_debugger/browser_debugger-meta.ts | showDomBreakpoints": {"message": "显示 DOM 断点"}, "panels/browser_debugger/browser_debugger-meta.ts | showEventListenerBreakpoints": {"message": "显示事件监听器断点"}, "panels/browser_debugger/browser_debugger-meta.ts | showGlobalListeners": {"message": "显示“全局监听器\""}, "panels/browser_debugger/browser_debugger-meta.ts | showOverrides": {"message": "显示“替换”工具"}, "panels/browser_debugger/browser_debugger-meta.ts | showPage": {"message": "显示“网页”标签页"}, "panels/browser_debugger/browser_debugger-meta.ts | showXhrfetchBreakpoints": {"message": "显示 XHR/提取断点"}, "panels/browser_debugger/browser_debugger-meta.ts | xhrfetchBreakpoints": {"message": "XHR/提取断点"}, "panels/changes/ChangesSidebar.ts | sFromSourceMap": {"message": "{PH1}（来自来源映射）"}, "panels/changes/ChangesView.ts | binaryData": {"message": "二进制数据"}, "panels/changes/ChangesView.ts | copy": {"message": "复制"}, "panels/changes/ChangesView.ts | noChanges": {"message": "无更改"}, "panels/changes/ChangesView.ts | sDeletions": {"message": "{n,plural, =1{# 行删除代码 (-)}other{# 行删除代码 (-)}}"}, "panels/changes/ChangesView.ts | sInsertions": {"message": "{n,plural, =1{# 行插入代码 (+)}other{# 行插入代码 (+)}}"}, "panels/changes/changes-meta.ts | changes": {"message": "变更"}, "panels/changes/changes-meta.ts | copyAllChangesFromCurrentFile": {"message": "复制当前文件中的所有更改"}, "panels/changes/changes-meta.ts | revertAllChangesToCurrentFile": {"message": "还原对当前文件所做的所有更改"}, "panels/changes/changes-meta.ts | showChanges": {"message": "显示“更改”工具"}, "panels/console/ConsoleContextSelector.ts | extension": {"message": "扩展程序"}, "panels/console/ConsoleContextSelector.ts | javascriptContextNotSelected": {"message": "JavaScript 上下文：未选择"}, "panels/console/ConsoleContextSelector.ts | javascriptContextS": {"message": "JavaScript 上下文：{PH1}"}, "panels/console/ConsolePinPane.ts | evaluateAllowingSideEffects": {"message": "评估，允许有副作用"}, "panels/console/ConsolePinPane.ts | expression": {"message": "表达式"}, "panels/console/ConsolePinPane.ts | liveExpressionEditor": {"message": "实时表达式编辑器"}, "panels/console/ConsolePinPane.ts | notAvailable": {"message": "不可用"}, "panels/console/ConsolePinPane.ts | removeAllExpressions": {"message": "移除所有表达式"}, "panels/console/ConsolePinPane.ts | removeBlankExpression": {"message": "移除空白表达式"}, "panels/console/ConsolePinPane.ts | removeExpression": {"message": "移除表达式"}, "panels/console/ConsolePinPane.ts | removeExpressionS": {"message": "移除表达式：{PH1}"}, "panels/console/ConsolePrompt.ts | allowPasting": {"message": "允许粘贴"}, "panels/console/ConsolePrompt.ts | consolePrompt": {"message": "控制台提示"}, "panels/console/ConsolePrompt.ts | selfXssWarning": {"message": "警告：请勿将您不理解或未自行检查的代码粘贴到开发者工具控制台中。这可能会导致攻击者趁机窃取您的身份信息或控制您的计算机。请在下方输入“{PH1}”，然后按 Enter 键允许粘贴。"}, "panels/console/ConsoleSidebar.ts | dErrors": {"message": "{n,plural, =0{无错误}=1{# 个错误}other{# 个错误}}"}, "panels/console/ConsoleSidebar.ts | dInfo": {"message": "{n,plural, =0{无信息}=1{# 条信息}other{# 条信息}}"}, "panels/console/ConsoleSidebar.ts | dMessages": {"message": "{n,plural, =0{没有任何消息}=1{# 条消息}other{# 条消息}}"}, "panels/console/ConsoleSidebar.ts | dUserMessages": {"message": "{n,plural, =0{没有用户消息}=1{# 条用户消息}other{# 条用户消息}}"}, "panels/console/ConsoleSidebar.ts | dVerbose": {"message": "{n,plural, =0{无详细消息}=1{# 条详细消息}other{# 条详细消息}}"}, "panels/console/ConsoleSidebar.ts | dWarnings": {"message": "{n,plural, =0{无警告}=1{# 条警告}other{# 条警告}}"}, "panels/console/ConsoleSidebar.ts | other": {"message": "<其他>"}, "panels/console/ConsoleView.ts | allLevels": {"message": "所有级别"}, "panels/console/ConsoleView.ts | autocompleteFromHistory": {"message": "根据历史记录自动补全"}, "panels/console/ConsoleView.ts | consoleCleared": {"message": "已清除控制台"}, "panels/console/ConsoleView.ts | consoleSettings": {"message": "控制台设置"}, "panels/console/ConsoleView.ts | consoleSidebarHidden": {"message": "控制台边栏处于隐藏状态"}, "panels/console/ConsoleView.ts | consoleSidebarShown": {"message": "控制台边栏处于显示状态"}, "panels/console/ConsoleView.ts | copyVisibleStyledSelection": {"message": "复制带有可见样式的选择内容"}, "panels/console/ConsoleView.ts | customLevels": {"message": "自定义级别"}, "panels/console/ConsoleView.ts | default": {"message": "默认"}, "panels/console/ConsoleView.ts | defaultLevels": {"message": "默认级别"}, "panels/console/ConsoleView.ts | doNotClearLogOnPageReload": {"message": "网页重新加载/导航时不清除日志"}, "panels/console/ConsoleView.ts | eagerlyEvaluateTextInThePrompt": {"message": "及早评估提示文字"}, "panels/console/ConsoleView.ts | egEventdCdnUrlacom": {"message": "例如：/eventd/ -cdn url:a.com"}, "panels/console/ConsoleView.ts | errors": {"message": "错误"}, "panels/console/ConsoleView.ts | filteredMessagesInConsole": {"message": "控制台中有 {PH1} 条消息"}, "panels/console/ConsoleView.ts | findStringInLogs": {"message": "在日志中查找字符串"}, "panels/console/ConsoleView.ts | groupSimilarMessagesInConsole": {"message": "在控制台中对相似消息进行分组"}, "panels/console/ConsoleView.ts | hideAll": {"message": "隐藏全部"}, "panels/console/ConsoleView.ts | hideConsoleSidebar": {"message": "隐藏控制台边栏"}, "panels/console/ConsoleView.ts | hideMessagesFromS": {"message": "隐藏来自 {PH1} 的消息"}, "panels/console/ConsoleView.ts | hideNetwork": {"message": "隐藏网络"}, "panels/console/ConsoleView.ts | info": {"message": "信息"}, "panels/console/ConsoleView.ts | issueToolbarClickToGoToTheIssuesTab": {"message": "点击即可转到“问题”标签页"}, "panels/console/ConsoleView.ts | issueToolbarClickToView": {"message": "点击即可查看 {issueEnumeration}"}, "panels/console/ConsoleView.ts | issueToolbarTooltipGeneral": {"message": "某些问题不再生成控制台消息，但会显示在“问题”标签页中。"}, "panels/console/ConsoleView.ts | issuesWithColon": {"message": "{n,plural, =0{无问题}=1{# 个问题：}other{# 个问题：}}"}, "panels/console/ConsoleView.ts | logLevelS": {"message": "日志级别：{PH1}"}, "panels/console/ConsoleView.ts | logXMLHttpRequests": {"message": "记录 XMLHttpRequest"}, "panels/console/ConsoleView.ts | onlyShowMessagesFromTheCurrentContext": {"message": "仅显示来自当前上下文（top、iframe、worker、扩展程序）的消息"}, "panels/console/ConsoleView.ts | overriddenByFilterSidebar": {"message": "已被过滤器边栏覆盖"}, "panels/console/ConsoleView.ts | preserveLog": {"message": "保留日志"}, "panels/console/ConsoleView.ts | replayXhr": {"message": "重放 XHR"}, "panels/console/ConsoleView.ts | sHidden": {"message": "{n,plural, =1{# 条已隐藏}other{# 条已隐藏}}"}, "panels/console/ConsoleView.ts | sOnly": {"message": "仅限{PH1}"}, "panels/console/ConsoleView.ts | saveAs": {"message": "另存为…"}, "panels/console/ConsoleView.ts | searching": {"message": "正在搜索…"}, "panels/console/ConsoleView.ts | selectedContextOnly": {"message": "仅限已选择的上下文"}, "panels/console/ConsoleView.ts | showConsoleSidebar": {"message": "显示控制台边栏"}, "panels/console/ConsoleView.ts | showCorsErrorsInConsole": {"message": "在控制台中显示CORS错误"}, "panels/console/ConsoleView.ts | treatEvaluationAsUserActivation": {"message": "将评估视为用户激活行为"}, "panels/console/ConsoleView.ts | verbose": {"message": "详细"}, "panels/console/ConsoleView.ts | warnings": {"message": "警告"}, "panels/console/ConsoleView.ts | writingFile": {"message": "正在写入文件…"}, "panels/console/ConsoleViewMessage.ts | Mxx": {"message": " M<XX>"}, "panels/console/ConsoleViewMessage.ts | assertionFailed": {"message": "断言失败： "}, "panels/console/ConsoleViewMessage.ts | attribute": {"message": "<attribute>"}, "panels/console/ConsoleViewMessage.ts | clearAllMessagesWithS": {"message": "使用 {PH1} 清除所有消息"}, "panels/console/ConsoleViewMessage.ts | cndBreakpoint": {"message": "条件断点"}, "panels/console/ConsoleViewMessage.ts | console": {"message": "控制台"}, "panels/console/ConsoleViewMessage.ts | consoleWasCleared": {"message": "控制台数据已被清除"}, "panels/console/ConsoleViewMessage.ts | consoleclearWasPreventedDueTo": {"message": "由于要“保留日志”，系统已阻止 console.clear() 发挥作用"}, "panels/console/ConsoleViewMessage.ts | deprecationS": {"message": "[Deprecation] {PH1}"}, "panels/console/ConsoleViewMessage.ts | error": {"message": "错误"}, "panels/console/ConsoleViewMessage.ts | errorS": {"message": "{n,plural, =1{错误，已重复 # 次}other{错误，已重复 # 次}}"}, "panels/console/ConsoleViewMessage.ts | exception": {"message": "<exception>"}, "panels/console/ConsoleViewMessage.ts | explainThisError": {"message": "了解此错误"}, "panels/console/ConsoleViewMessage.ts | explainThisMessage": {"message": "了解此消息"}, "panels/console/ConsoleViewMessage.ts | explainThisWarning": {"message": "了解此警告"}, "panels/console/ConsoleViewMessage.ts | functionWasResolvedFromBound": {"message": "函数已根据绑定的函数进行解析。"}, "panels/console/ConsoleViewMessage.ts | index": {"message": "（索引）"}, "panels/console/ConsoleViewMessage.ts | interventionS": {"message": "[Intervention] {PH1}"}, "panels/console/ConsoleViewMessage.ts | logpoint": {"message": "日志点"}, "panels/console/ConsoleViewMessage.ts | repeatS": {"message": "{n,plural, =1{已重复 # 次}other{已重复 # 次}}"}, "panels/console/ConsoleViewMessage.ts | someEvent": {"message": "<某些> 事件"}, "panels/console/ConsoleViewMessage.ts | stackMessageCollapsed": {"message": "堆栈表格已收起"}, "panels/console/ConsoleViewMessage.ts | stackMessageExpanded": {"message": "堆栈表格已展开"}, "panels/console/ConsoleViewMessage.ts | thisValueWasEvaluatedUponFirst": {"message": "此值是在第一次展开时评估得出的。之后可能已发生更改。"}, "panels/console/ConsoleViewMessage.ts | thisValueWillNotBeCollectedUntil": {"message": "在清除控制台之前，系统不会收集此值。"}, "panels/console/ConsoleViewMessage.ts | tookNms": {"message": "用时 <N> 毫秒"}, "panels/console/ConsoleViewMessage.ts | url": {"message": "<URL>"}, "panels/console/ConsoleViewMessage.ts | value": {"message": "值"}, "panels/console/ConsoleViewMessage.ts | violationS": {"message": "[Violation] {PH1}"}, "panels/console/ConsoleViewMessage.ts | warning": {"message": "警告"}, "panels/console/ConsoleViewMessage.ts | warningS": {"message": "{n,plural, =1{警告，已重复 # 次}other{警告，已重复 # 次}}"}, "panels/console/console-meta.ts | autocompleteFromHistory": {"message": "根据历史记录自动补全"}, "panels/console/console-meta.ts | autocompleteOnEnter": {"message": "按 Enter 键时接受自动补全建议"}, "panels/console/console-meta.ts | clearConsole": {"message": "清除控制台"}, "panels/console/console-meta.ts | clearConsoleHistory": {"message": "清除控制台历史记录"}, "panels/console/console-meta.ts | collapseConsoleTraceMessagesByDefault": {"message": "不自动展开 console.trace() 消息"}, "panels/console/console-meta.ts | console": {"message": "控制台"}, "panels/console/console-meta.ts | createLiveExpression": {"message": "创建实时表达式"}, "panels/console/console-meta.ts | doNotAutocompleteFromHistory": {"message": "不根据历史记录自动补全"}, "panels/console/console-meta.ts | doNotAutocompleteOnEnter": {"message": "按 Enter 键时不接受自动补全建议"}, "panels/console/console-meta.ts | doNotEagerlyEvaluateConsole": {"message": "不即时评估控制台提示文字"}, "panels/console/console-meta.ts | doNotGroupSimilarMessagesIn": {"message": "不要对控制台中的类似消息分组"}, "panels/console/console-meta.ts | doNotShowCorsErrorsIn": {"message": "不在控制台中显示CORS错误"}, "panels/console/console-meta.ts | doNotTreatEvaluationAsUser": {"message": "请勿将评估视为用户激活行为"}, "panels/console/console-meta.ts | eagerEvaluation": {"message": "及早评估"}, "panels/console/console-meta.ts | eagerlyEvaluateConsolePromptText": {"message": "即时评估控制台提示文字"}, "panels/console/console-meta.ts | evaluateTriggersUserActivation": {"message": "将代码评估视为用户操作"}, "panels/console/console-meta.ts | expandConsoleTraceMessagesByDefault": {"message": "自动展开 console.trace() 消息"}, "panels/console/console-meta.ts | groupSimilarMessagesInConsole": {"message": "在控制台中对相似消息进行分组"}, "panels/console/console-meta.ts | hideNetworkMessages": {"message": "隐藏网络消息"}, "panels/console/console-meta.ts | hideTimestamps": {"message": "隐藏时间戳"}, "panels/console/console-meta.ts | logXmlhttprequests": {"message": "记录 XMLHttpRequest"}, "panels/console/console-meta.ts | onlyShowMessagesFromTheCurrent": {"message": "仅显示来自当前上下文（top、iframe、worker、扩展程序）的消息"}, "panels/console/console-meta.ts | selectedContextOnly": {"message": "仅限已选择的上下文"}, "panels/console/console-meta.ts | showConsole": {"message": "显示控制台"}, "panels/console/console-meta.ts | showCorsErrorsInConsole": {"message": "在控制台中显示CORS错误"}, "panels/console/console-meta.ts | showMessagesFromAllContexts": {"message": "显示来自所有上下文的消息"}, "panels/console/console-meta.ts | showNetworkMessages": {"message": "显示网络消息"}, "panels/console/console-meta.ts | showTimestamps": {"message": "显示时间戳"}, "panels/console/console-meta.ts | timestamps": {"message": "时间戳"}, "panels/console/console-meta.ts | toggleConsole": {"message": "切换控制台"}, "panels/console/console-meta.ts | treatEvaluationAsUserActivation": {"message": "将评估视为用户激活行为"}, "panels/console_counters/WarningErrorCounter.ts | openConsoleToViewS": {"message": "打开控制台即可查看 {PH1}"}, "panels/console_counters/WarningErrorCounter.ts | openIssuesToView": {"message": "{n,plural, =1{打开“问题”即可查看 # 个问题：}other{打开“问题”即可查看 # 个问题：}}"}, "panels/console_counters/WarningErrorCounter.ts | sErrors": {"message": "{n,plural, =1{# 个错误}other{# 个错误}}"}, "panels/console_counters/WarningErrorCounter.ts | sWarnings": {"message": "{n,plural, =1{# 条警告}other{# 条警告}}"}, "panels/coverage/CoverageListView.ts | codeCoverage": {"message": "代码覆盖率"}, "panels/coverage/CoverageListView.ts | css": {"message": "CSS"}, "panels/coverage/CoverageListView.ts | jsCoverageWithPerBlock": {"message": "按块粒度衡量的 JS 覆盖率：JavaScript 代码块执行后，相应块将标记为已覆盖。"}, "panels/coverage/CoverageListView.ts | jsCoverageWithPerFunction": {"message": "按函数粒度得出的 JS 覆盖范围：某个函数一旦执行，整个函数便会被标记为已覆盖。"}, "panels/coverage/CoverageListView.ts | jsPerBlock": {"message": "JS（按块）"}, "panels/coverage/CoverageListView.ts | jsPerFunction": {"message": "JS（按函数）"}, "panels/coverage/CoverageListView.ts | sBytes": {"message": "{n,plural, =1{# 个字节}other{# 个字节}}"}, "panels/coverage/CoverageListView.ts | sBytesS": {"message": "{n,plural, =1{# 个字节，{percentage}}other{# 个字节，{percentage}}}"}, "panels/coverage/CoverageListView.ts | sBytesSBelongToBlocksOf": {"message": "有 {PH1} 个字节 ({PH2}) 位于尚未执行的 JavaScript 块中。"}, "panels/coverage/CoverageListView.ts | sBytesSBelongToBlocksOfJavascript": {"message": "有 {PH1} 个字节 ({PH2}) 位于已执行至少 1 次的 JavaScript 代码块中。"}, "panels/coverage/CoverageListView.ts | sBytesSBelongToFunctionsThatHave": {"message": "有 {PH1} 个字节 ({PH2}) 位于尚未执行的函数中。"}, "panels/coverage/CoverageListView.ts | sBytesSBelongToFunctionsThatHaveExecuted": {"message": "有 {PH1} 个字节 ({PH2}) 位于已执行至少 1 次的函数中。"}, "panels/coverage/CoverageListView.ts | sOfFileUnusedSOfFileUsed": {"message": "{PH1}% 的文件未使用，{PH2}% 的文件已使用"}, "panels/coverage/CoverageListView.ts | totalBytes": {"message": "总字节数"}, "panels/coverage/CoverageListView.ts | type": {"message": "类型"}, "panels/coverage/CoverageListView.ts | unusedBytes": {"message": "未使用的字节数"}, "panels/coverage/CoverageListView.ts | url": {"message": "网址"}, "panels/coverage/CoverageListView.ts | usageVisualization": {"message": "使用情况可视化图表"}, "panels/coverage/CoverageView.ts | activationNoCapture": {"message": "无法捕获覆盖率信息，因为该网页是在后台预渲染的。"}, "panels/coverage/CoverageView.ts | all": {"message": "全部"}, "panels/coverage/CoverageView.ts | bfcacheNoCapture": {"message": "无法捕获覆盖率信息，因为该网页是使用往返缓存提供的。"}, "panels/coverage/CoverageView.ts | chooseCoverageGranularityPer": {"message": "选择覆盖范围粒度：“按函数”的开销很低，“按块”的开销很高。"}, "panels/coverage/CoverageView.ts | clickTheRecordButtonSToStart": {"message": "点击“记录”按钮 {PH1} 即可开始记录覆盖范围。"}, "panels/coverage/CoverageView.ts | clickTheReloadButtonSToReloadAnd": {"message": "点击“重新加载”按钮 {PH1} 即可重新加载并开始记录覆盖范围。"}, "panels/coverage/CoverageView.ts | contentScripts": {"message": "内容脚本"}, "panels/coverage/CoverageView.ts | css": {"message": "CSS"}, "panels/coverage/CoverageView.ts | filterByUrl": {"message": "按网址过滤"}, "panels/coverage/CoverageView.ts | filterCoverageByType": {"message": "按类型过滤覆盖率"}, "panels/coverage/CoverageView.ts | filteredSTotalS": {"message": "过滤后：{PH1}；总计：{PH2}"}, "panels/coverage/CoverageView.ts | includeExtensionContentScripts": {"message": "添加扩展程序内容脚本"}, "panels/coverage/CoverageView.ts | javascript": {"message": "JavaScript"}, "panels/coverage/CoverageView.ts | perBlock": {"message": "按块"}, "panels/coverage/CoverageView.ts | perFunction": {"message": "按函数"}, "panels/coverage/CoverageView.ts | reloadPrompt": {"message": "点击“重新加载”按钮 {PH1} 即可重新加载并获取覆盖率。"}, "panels/coverage/CoverageView.ts | sOfSSUsedSoFarSUnused": {"message": "到目前为止，已使用 {PH1} ({PH3}%)，共 {PH2}，还有 {PH4} 未使用。"}, "panels/coverage/coverage-meta.ts | clearCoverage": {"message": "清除覆盖率数据"}, "panels/coverage/coverage-meta.ts | coverage": {"message": "覆盖率"}, "panels/coverage/coverage-meta.ts | exportCoverage": {"message": "导出覆盖率数据"}, "panels/coverage/coverage-meta.ts | instrumentCoverage": {"message": "插桩覆盖范围"}, "panels/coverage/coverage-meta.ts | showCoverage": {"message": "显示覆盖范围"}, "panels/coverage/coverage-meta.ts | startInstrumentingCoverageAnd": {"message": "开始检测覆盖率，并重新加载网页"}, "panels/coverage/coverage-meta.ts | stopInstrumentingCoverageAndShow": {"message": "停止检测覆盖率并显示结果"}, "panels/css_overview/CSSOverviewCompletedView.ts | aa": {"message": "AA"}, "panels/css_overview/CSSOverviewCompletedView.ts | aaa": {"message": "AAA"}, "panels/css_overview/CSSOverviewCompletedView.ts | apca": {"message": "APCA"}, "panels/css_overview/CSSOverviewCompletedView.ts | attributeSelectors": {"message": "属性选择器"}, "panels/css_overview/CSSOverviewCompletedView.ts | backgroundColorsS": {"message": "背景颜色：{PH1}"}, "panels/css_overview/CSSOverviewCompletedView.ts | borderColorsS": {"message": "边框颜色：{PH1}"}, "panels/css_overview/CSSOverviewCompletedView.ts | classSelectors": {"message": "类选择器"}, "panels/css_overview/CSSOverviewCompletedView.ts | colors": {"message": "颜色"}, "panels/css_overview/CSSOverviewCompletedView.ts | contrastIssues": {"message": "对比度问题"}, "panels/css_overview/CSSOverviewCompletedView.ts | contrastIssuesS": {"message": "对比度问题：{PH1}"}, "panels/css_overview/CSSOverviewCompletedView.ts | contrastRatio": {"message": "对比度"}, "panels/css_overview/CSSOverviewCompletedView.ts | cssOverviewElements": {"message": "CSS 概览元素"}, "panels/css_overview/CSSOverviewCompletedView.ts | declaration": {"message": "声明"}, "panels/css_overview/CSSOverviewCompletedView.ts | element": {"message": "元素"}, "panels/css_overview/CSSOverviewCompletedView.ts | elements": {"message": "元素"}, "panels/css_overview/CSSOverviewCompletedView.ts | externalStylesheets": {"message": "外部样式表"}, "panels/css_overview/CSSOverviewCompletedView.ts | fillColorsS": {"message": "填充颜色：{PH1}"}, "panels/css_overview/CSSOverviewCompletedView.ts | fontInfo": {"message": "字体信息"}, "panels/css_overview/CSSOverviewCompletedView.ts | idSelectors": {"message": "ID 选择器"}, "panels/css_overview/CSSOverviewCompletedView.ts | inlineStyleElements": {"message": "内嵌样式元素"}, "panels/css_overview/CSSOverviewCompletedView.ts | mediaQueries": {"message": "媒体查询数量"}, "panels/css_overview/CSSOverviewCompletedView.ts | nOccurrences": {"message": "{n,plural, =1{# 次}other{# 次}}"}, "panels/css_overview/CSSOverviewCompletedView.ts | nonsimpleSelectors": {"message": "非简单选择器"}, "panels/css_overview/CSSOverviewCompletedView.ts | overviewSummary": {"message": "概览摘要"}, "panels/css_overview/CSSOverviewCompletedView.ts | showElement": {"message": "显示元素"}, "panels/css_overview/CSSOverviewCompletedView.ts | source": {"message": "来源"}, "panels/css_overview/CSSOverviewCompletedView.ts | styleRules": {"message": "样式规则"}, "panels/css_overview/CSSOverviewCompletedView.ts | textColorSOverSBackgroundResults": {"message": "文本颜色 {PH1} 与背景色 {PH2} 搭配将导致 {PH3} 个元素的对比度较低"}, "panels/css_overview/CSSOverviewCompletedView.ts | textColorsS": {"message": "文字颜色：{PH1}"}, "panels/css_overview/CSSOverviewCompletedView.ts | thereAreNoFonts": {"message": "没有使用字体。"}, "panels/css_overview/CSSOverviewCompletedView.ts | thereAreNoMediaQueries": {"message": "没有任何媒体查询。"}, "panels/css_overview/CSSOverviewCompletedView.ts | thereAreNoUnusedDeclarations": {"message": "没有未使用的声明。"}, "panels/css_overview/CSSOverviewCompletedView.ts | typeSelectors": {"message": "类型选择器"}, "panels/css_overview/CSSOverviewCompletedView.ts | universalSelectors": {"message": "通用选择器"}, "panels/css_overview/CSSOverviewCompletedView.ts | unusedDeclarations": {"message": "未使用的声明"}, "panels/css_overview/CSSOverviewProcessingView.ts | cancel": {"message": "取消"}, "panels/css_overview/CSSOverviewSidebarPanel.ts | clearOverview": {"message": "清除概览"}, "panels/css_overview/CSSOverviewSidebarPanel.ts | cssOverviewPanelSidebar": {"message": "CSS 概览面板边栏"}, "panels/css_overview/CSSOverviewUnusedDeclarations.ts | bottomAppliedToAStatically": {"message": "Bottom值已应用于静态放置的元素"}, "panels/css_overview/CSSOverviewUnusedDeclarations.ts | heightAppliedToAnInlineElement": {"message": "Height值已应用于内嵌元素"}, "panels/css_overview/CSSOverviewUnusedDeclarations.ts | leftAppliedToAStatically": {"message": "Left值已应用于静态放置的元素"}, "panels/css_overview/CSSOverviewUnusedDeclarations.ts | rightAppliedToAStatically": {"message": "Right值已应用于静态放置的元素"}, "panels/css_overview/CSSOverviewUnusedDeclarations.ts | topAppliedToAStatically": {"message": "Top值已应用于静态放置的元素"}, "panels/css_overview/CSSOverviewUnusedDeclarations.ts | verticalAlignmentAppliedTo": {"message": "已对既非 inline 也非 table-cell 的元素采用垂直对齐样式"}, "panels/css_overview/CSSOverviewUnusedDeclarations.ts | widthAppliedToAnInlineElement": {"message": "Width值已应用于内嵌元素"}, "panels/css_overview/components/CSSOverviewStartView.ts | captureOverview": {"message": "捕获概览"}, "panels/css_overview/components/CSSOverviewStartView.ts | capturePageCSSOverview": {"message": "捕获您网页的 CSS 概况"}, "panels/css_overview/components/CSSOverviewStartView.ts | identifyCSSImprovements": {"message": "发掘潜在的 CSS 改进机会"}, "panels/css_overview/components/CSSOverviewStartView.ts | identifyCSSImprovementsWithExampleIssues": {"message": "发掘潜在的 CSS 改进机会（例如低对比度问题、未使用的声明、颜色或字体不匹配）"}, "panels/css_overview/components/CSSOverviewStartView.ts | locateAffectedElements": {"message": "在“元素”面板中找到受影响的元素"}, "panels/css_overview/components/CSSOverviewStartView.ts | quickStartWithCSSOverview": {"message": "快速入门：开始使用新的“CSS 概览”面板"}, "panels/css_overview/css_overview-meta.ts | cssOverview": {"message": "CSS 概览"}, "panels/css_overview/css_overview-meta.ts | showCssOverview": {"message": "显示“CSS 概览”"}, "panels/developer_resources/DeveloperResourcesListView.ts | copyInitiatorUrl": {"message": "复制启动器网址"}, "panels/developer_resources/DeveloperResourcesListView.ts | copyUrl": {"message": "复制网址"}, "panels/developer_resources/DeveloperResourcesListView.ts | developerResources": {"message": "开发者资源"}, "panels/developer_resources/DeveloperResourcesListView.ts | error": {"message": "错误"}, "panels/developer_resources/DeveloperResourcesListView.ts | failure": {"message": "失败"}, "panels/developer_resources/DeveloperResourcesListView.ts | initiator": {"message": "启动器"}, "panels/developer_resources/DeveloperResourcesListView.ts | pending": {"message": "待处理"}, "panels/developer_resources/DeveloperResourcesListView.ts | sBytes": {"message": "{n,plural, =1{# 个字节}other{# 个字节}}"}, "panels/developer_resources/DeveloperResourcesListView.ts | status": {"message": "状态"}, "panels/developer_resources/DeveloperResourcesListView.ts | success": {"message": "成功"}, "panels/developer_resources/DeveloperResourcesListView.ts | totalBytes": {"message": "总字节数"}, "panels/developer_resources/DeveloperResourcesListView.ts | url": {"message": "网址"}, "panels/developer_resources/DeveloperResourcesView.ts | enableLoadingThroughTarget": {"message": "通过网站加载"}, "panels/developer_resources/DeveloperResourcesView.ts | filterByText": {"message": "按网址和错误过滤"}, "panels/developer_resources/DeveloperResourcesView.ts | loadHttpsDeveloperResources": {"message": "通过您检查的网站（而不是通过开发者工具）加载 HTTP(S) 开发者资源"}, "panels/developer_resources/DeveloperResourcesView.ts | resources": {"message": "{n,plural, =1{# 项资源}other{# 项资源}}"}, "panels/developer_resources/DeveloperResourcesView.ts | resourcesCurrentlyLoading": {"message": "共有 {PH1} 项资源，正在加载 {PH2} 项"}, "panels/developer_resources/developer_resources-meta.ts | developerResources": {"message": "开发者资源"}, "panels/developer_resources/developer_resources-meta.ts | showDeveloperResources": {"message": "显示“开发者资源”"}, "panels/elements/CSSRuleValidator.ts | flexGridContainerPropertyRuleFix": {"message": "请尝试在容器元素上设置 {PROPERTY_NAME}，或改用 {ALTERNATIVE_PROPERTY_NAME}。"}, "panels/elements/CSSRuleValidator.ts | flexGridContainerPropertyRuleReason": {"message": "此元素属于 {CONTAINER_DISPLAY_NAME} 项（即 {CONTAINER_DISPLAY_NAME} 容器的子元素），但 {PROPERTY_NAME} 仅适用于容器。"}, "panels/elements/CSSRuleValidator.ts | fontVariationSettingsWarning": {"message": "“{PH1}”设置的值 {PH2} 超出了字体系列“{PH5}”的支持范围 [{PH3}，{PH4}]。"}, "panels/elements/CSSRuleValidator.ts | ruleViolatedByParentElementRuleFix": {"message": "不妨尝试将父级元素的 {EXISTING_PARENT_ELEMENT_RULE} 属性设为 {TARGET_PARENT_ELEMENT_RULE}。"}, "panels/elements/CSSRuleValidator.ts | ruleViolatedByParentElementRuleReason": {"message": "父元素的 {REASON_PROPERTY_DECLARATION_CODE} 属性可防止 {AFFECTED_PROPERTY_DECLARATION_CODE} 产生影响。"}, "panels/elements/CSSRuleValidator.ts | ruleViolatedBySameElementRuleChangeFlexOrGrid": {"message": "请尝试添加 {DISPLAY_GRID_RULE} 或 {DISPLAY_FLEX_RULE}，将此元素置于容器中。"}, "panels/elements/CSSRuleValidator.ts | ruleViolatedBySameElementRuleChangeSuggestion": {"message": "不妨尝试将 {EXISTING_PROPERTY_DECLARATION} 属性设为 {TARGET_PROPERTY_DECLARATION}。"}, "panels/elements/CSSRuleValidator.ts | ruleViolatedBySameElementRuleFix": {"message": "不妨尝试将 {PROPERTY_NAME} 设为除 {PROPERTY_VALUE} 之外的某个值。"}, "panels/elements/CSSRuleValidator.ts | ruleViolatedBySameElementRuleReason": {"message": "{REASON_PROPERTY_DECLARATION_CODE} 属性可防止 {AFFECTED_PROPERTY_DECLARATION_CODE} 产生影响。"}, "panels/elements/ClassesPaneWidget.ts | addNewClass": {"message": "添加新类"}, "panels/elements/ClassesPaneWidget.ts | classSAdded": {"message": "已添加 {PH1} 类"}, "panels/elements/ClassesPaneWidget.ts | classesSAdded": {"message": "已添加 {PH1} 类"}, "panels/elements/ClassesPaneWidget.ts | elementClasses": {"message": "元素类"}, "panels/elements/ColorSwatchPopoverIcon.ts | openCubicBezierEditor": {"message": "打开三次贝塞尔曲线编辑器"}, "panels/elements/ColorSwatchPopoverIcon.ts | openShadowEditor": {"message": "打开阴影编辑器"}, "panels/elements/ComputedStyleWidget.ts | group": {"message": "组合"}, "panels/elements/ComputedStyleWidget.ts | navigateToSelectorSource": {"message": "转到选择器源代码"}, "panels/elements/ComputedStyleWidget.ts | navigateToStyle": {"message": "转到“样式”"}, "panels/elements/ComputedStyleWidget.ts | noMatchingProperty": {"message": "无相符属性"}, "panels/elements/ComputedStyleWidget.ts | showAll": {"message": "全部显示"}, "panels/elements/DOMLinkifier.ts | node": {"message": "<节点>"}, "panels/elements/ElementStatePaneWidget.ts | emulateFocusedPage": {"message": "模拟已聚焦的网页"}, "panels/elements/ElementStatePaneWidget.ts | emulatesAFocusedPage": {"message": "使页面保持聚焦状态。常用于调试消失的元素。"}, "panels/elements/ElementStatePaneWidget.ts | forceElementState": {"message": "强制设置元素状态"}, "panels/elements/ElementStatePaneWidget.ts | toggleElementState": {"message": "启用/停用元素状态"}, "panels/elements/ElementsPanel.ts | computed": {"message": "计算样式"}, "panels/elements/ElementsPanel.ts | computedStylesHidden": {"message": "“计算样式”边栏处于隐藏状态"}, "panels/elements/ElementsPanel.ts | computedStylesShown": {"message": "“计算样式”边栏处于显示状态"}, "panels/elements/ElementsPanel.ts | domTreeExplorer": {"message": "DOM 树浏览器"}, "panels/elements/ElementsPanel.ts | elementStateS": {"message": "元素状态：{PH1}"}, "panels/elements/ElementsPanel.ts | findByStringSelectorOrXpath": {"message": "按字符串、选择器或 XPath 查找"}, "panels/elements/ElementsPanel.ts | hideComputedStylesSidebar": {"message": "隐藏“计算样式”边栏"}, "panels/elements/ElementsPanel.ts | nodeCannotBeFoundInTheCurrent": {"message": "无法在当前网页中找到相应节点。"}, "panels/elements/ElementsPanel.ts | revealInElementsPanel": {"message": "在“元素”面板中显示"}, "panels/elements/ElementsPanel.ts | showComputedStylesSidebar": {"message": "显示“计算样式”边栏"}, "panels/elements/ElementsPanel.ts | sidePanelContent": {"message": "侧边栏中的内容"}, "panels/elements/ElementsPanel.ts | sidePanelToolbar": {"message": "侧边栏中的工具栏"}, "panels/elements/ElementsPanel.ts | styles": {"message": "样式"}, "panels/elements/ElementsPanel.ts | switchToAccessibilityTreeView": {"message": "切换到无障碍功能树状视图"}, "panels/elements/ElementsPanel.ts | switchToDomTreeView": {"message": "切换到 DOM 树状视图"}, "panels/elements/ElementsPanel.ts | theDeferredDomNodeCouldNotBe": {"message": "延迟的 DOM 节点无法解析为有效节点。"}, "panels/elements/ElementsPanel.ts | theRemoteObjectCouldNotBe": {"message": "远程对象无法解析为有效节点。"}, "panels/elements/ElementsTreeElement.ts | addAttribute": {"message": "添加属性"}, "panels/elements/ElementsTreeElement.ts | captureNodeScreenshot": {"message": "截取节点屏幕截图"}, "panels/elements/ElementsTreeElement.ts | children": {"message": "子元素："}, "panels/elements/ElementsTreeElement.ts | collapseChildren": {"message": "收起子级"}, "panels/elements/ElementsTreeElement.ts | copy": {"message": "复制"}, "panels/elements/ElementsTreeElement.ts | copyElement": {"message": "复制元素"}, "panels/elements/ElementsTreeElement.ts | copyFullXpath": {"message": "复制完整 XPath"}, "panels/elements/ElementsTreeElement.ts | copyJsPath": {"message": "复制 JS 路径"}, "panels/elements/ElementsTreeElement.ts | copyOuterhtml": {"message": "复制 outerHTML"}, "panels/elements/ElementsTreeElement.ts | copySelector": {"message": "复制 selector"}, "panels/elements/ElementsTreeElement.ts | copyStyles": {"message": "复制样式"}, "panels/elements/ElementsTreeElement.ts | copyXpath": {"message": "复制 XPath"}, "panels/elements/ElementsTreeElement.ts | cut": {"message": "剪切"}, "panels/elements/ElementsTreeElement.ts | deleteElement": {"message": "删除元素"}, "panels/elements/ElementsTreeElement.ts | disableFlexMode": {"message": "停用灵活模式"}, "panels/elements/ElementsTreeElement.ts | disableGridMode": {"message": "停用网格模式"}, "panels/elements/ElementsTreeElement.ts | disableScrollSnap": {"message": "停用滚动贴靠叠加层"}, "panels/elements/ElementsTreeElement.ts | duplicateElement": {"message": "复制粘贴元素"}, "panels/elements/ElementsTreeElement.ts | editAsHtml": {"message": "以 HTML 格式修改"}, "panels/elements/ElementsTreeElement.ts | editAttribute": {"message": "修改属性"}, "panels/elements/ElementsTreeElement.ts | editText": {"message": "修改文本"}, "panels/elements/ElementsTreeElement.ts | enableFlexMode": {"message": "启用灵活模式"}, "panels/elements/ElementsTreeElement.ts | enableGridMode": {"message": "启用网格模式"}, "panels/elements/ElementsTreeElement.ts | enableScrollSnap": {"message": "启用滚动贴靠叠加层"}, "panels/elements/ElementsTreeElement.ts | expandRecursively": {"message": "以递归方式展开"}, "panels/elements/ElementsTreeElement.ts | focus": {"message": "聚焦"}, "panels/elements/ElementsTreeElement.ts | forceState": {"message": "强制执行状态"}, "panels/elements/ElementsTreeElement.ts | hideElement": {"message": "隐藏元素"}, "panels/elements/ElementsTreeElement.ts | openMediaPanel": {"message": "跳转到“媒体”面板"}, "panels/elements/ElementsTreeElement.ts | paste": {"message": "粘贴"}, "panels/elements/ElementsTreeElement.ts | scrollIntoView": {"message": "滚动到视野范围内"}, "panels/elements/ElementsTreeElement.ts | showFrameDetails": {"message": "显示 iframe 详细信息"}, "panels/elements/ElementsTreeElement.ts | showPopoverTarget": {"message": "显示弹出式窗口目标"}, "panels/elements/ElementsTreeElement.ts | thisFrameWasIdentifiedAsAnAd": {"message": "此框架被确定为广告框架"}, "panels/elements/ElementsTreeElement.ts | useSInTheConsoleToReferToThis": {"message": "在控制台中使用 {PH1} 表示此元素。"}, "panels/elements/ElementsTreeElement.ts | valueIsTooLargeToEdit": {"message": "<值过大，无法修改>"}, "panels/elements/ElementsTreeOutline.ts | adornerSettings": {"message": "标志设置…"}, "panels/elements/ElementsTreeOutline.ts | pageDom": {"message": "网页 DOM"}, "panels/elements/ElementsTreeOutline.ts | reveal": {"message": "显示"}, "panels/elements/ElementsTreeOutline.ts | showAllNodesDMore": {"message": "显示所有节点（另外 {PH1} 个）"}, "panels/elements/ElementsTreeOutline.ts | storeAsGlobalVariable": {"message": "存储为全局变量"}, "panels/elements/EventListenersWidget.ts | all": {"message": "全部"}, "panels/elements/EventListenersWidget.ts | ancestors": {"message": "祖先"}, "panels/elements/EventListenersWidget.ts | blocking": {"message": "屏蔽"}, "panels/elements/EventListenersWidget.ts | eventListenersCategory": {"message": "事件监听器类别"}, "panels/elements/EventListenersWidget.ts | frameworkListeners": {"message": "Framework监听器"}, "panels/elements/EventListenersWidget.ts | passive": {"message": "被动式"}, "panels/elements/EventListenersWidget.ts | resolveEventListenersBoundWith": {"message": "解析与框架绑定的事件监听器"}, "panels/elements/EventListenersWidget.ts | showListenersOnTheAncestors": {"message": "显示祖先中的监听器"}, "panels/elements/LayersWidget.ts | cssLayersTitle": {"message": "CSS 级联层"}, "panels/elements/LayersWidget.ts | toggleCSSLayers": {"message": "显示/隐藏　CSS 图层视图"}, "panels/elements/MarkerDecorator.ts | domBreakpoint": {"message": "DOM 断点"}, "panels/elements/MarkerDecorator.ts | elementIsHidden": {"message": "元素处于隐藏状态"}, "panels/elements/NodeStackTraceWidget.ts | noStackTraceAvailable": {"message": "无可用堆栈轨迹"}, "panels/elements/PlatformFontsWidget.ts | dGlyphs": {"message": "{n,plural, =1{（# 个字形）}other{（# 个字形）}}"}, "panels/elements/PlatformFontsWidget.ts | familyName": {"message": "姓氏"}, "panels/elements/PlatformFontsWidget.ts | fontOrigin": {"message": "字体来源"}, "panels/elements/PlatformFontsWidget.ts | localFile": {"message": "本地文件"}, "panels/elements/PlatformFontsWidget.ts | networkResource": {"message": "网络资源"}, "panels/elements/PlatformFontsWidget.ts | postScriptName": {"message": "PostScript 名称"}, "panels/elements/PlatformFontsWidget.ts | renderedFonts": {"message": "渲染的字体"}, "panels/elements/PropertiesWidget.ts | noMatchingProperty": {"message": "无相符属性"}, "panels/elements/PropertiesWidget.ts | showAll": {"message": "全部显示"}, "panels/elements/PropertiesWidget.ts | showAllTooltip": {"message": "取消选中该设置后，系统会仅显示已定义了非 null 值的属性"}, "panels/elements/PropertyRenderer.ts | cssPropertyName": {"message": "CSS 属性名称：{PH1}"}, "panels/elements/PropertyRenderer.ts | cssPropertyValue": {"message": "CSS 属性值：{PH1}"}, "panels/elements/StylePropertiesSection.ts | constructedStylesheet": {"message": "构造的样式表"}, "panels/elements/StylePropertiesSection.ts | copyAllCSSChanges": {"message": "复制所有 CSS 更改"}, "panels/elements/StylePropertiesSection.ts | copyAllDeclarations": {"message": "复制所有声明"}, "panels/elements/StylePropertiesSection.ts | copyRule": {"message": "复制规则"}, "panels/elements/StylePropertiesSection.ts | copySelector": {"message": "复制 selector"}, "panels/elements/StylePropertiesSection.ts | cssSelector": {"message": "CSS 选择器"}, "panels/elements/StylePropertiesSection.ts | injectedStylesheet": {"message": "注入的样式表"}, "panels/elements/StylePropertiesSection.ts | insertStyleRuleBelow": {"message": "在下方插入样式规则"}, "panels/elements/StylePropertiesSection.ts | sattributesStyle": {"message": "{PH1}[属性样式]"}, "panels/elements/StylePropertiesSection.ts | showAllPropertiesSMore": {"message": "显示所有属性（另外 {PH1} 个）"}, "panels/elements/StylePropertiesSection.ts | styleAttribute": {"message": "style属性"}, "panels/elements/StylePropertiesSection.ts | userAgentStylesheet": {"message": "用户代理样式表"}, "panels/elements/StylePropertiesSection.ts | viaInspector": {"message": "通过检查器"}, "panels/elements/StylePropertyTreeElement.ts | copyAllCSSChanges": {"message": "复制所有 CSS 更改"}, "panels/elements/StylePropertyTreeElement.ts | copyAllCssDeclarationsAsJs": {"message": "以 JS 格式复制所有声明"}, "panels/elements/StylePropertyTreeElement.ts | copyAllDeclarations": {"message": "复制所有声明"}, "panels/elements/StylePropertyTreeElement.ts | copyCssDeclarationAsJs": {"message": "以 JS 格式复制声明"}, "panels/elements/StylePropertyTreeElement.ts | copyDeclaration": {"message": "复制声明"}, "panels/elements/StylePropertyTreeElement.ts | copyProperty": {"message": "复制属性"}, "panels/elements/StylePropertyTreeElement.ts | copyRule": {"message": "复制规则"}, "panels/elements/StylePropertyTreeElement.ts | copyValue": {"message": "复制值"}, "panels/elements/StylePropertyTreeElement.ts | flexboxEditorButton": {"message": "打开 flexbox 编辑器"}, "panels/elements/StylePropertyTreeElement.ts | gridEditorButton": {"message": "打开grid编辑器"}, "panels/elements/StylePropertyTreeElement.ts | openColorPickerS": {"message": "打开颜色选择器。{PH1}"}, "panels/elements/StylePropertyTreeElement.ts | revealInSourcesPanel": {"message": "在“来源”面板中显示"}, "panels/elements/StylePropertyTreeElement.ts | shiftClickToChangeColorFormat": {"message": "按住 Shift 键并点击即可更改颜色格式。"}, "panels/elements/StylePropertyTreeElement.ts | togglePropertyAndContinueEditing": {"message": "切换属性并继续编辑"}, "panels/elements/StylePropertyTreeElement.ts | viewComputedValue": {"message": "查看计算得出的值"}, "panels/elements/StylesSidebarPane.ts | automaticDarkMode": {"message": "自动深色模式"}, "panels/elements/StylesSidebarPane.ts | clickToRevealLayer": {"message": "点击即可在级联层树中显示级联层"}, "panels/elements/StylesSidebarPane.ts | copiedToClipboard": {"message": "已复制到剪贴板"}, "panels/elements/StylesSidebarPane.ts | copyAllCSSChanges": {"message": "复制 CSS 更改"}, "panels/elements/StylesSidebarPane.ts | incrementdecrementWithMousewheelHundred": {"message": "使用鼠标滚轮或向上/向下键调大/调小。{PH1}：±100、Shift：±10、Alt：±0.1"}, "panels/elements/StylesSidebarPane.ts | incrementdecrementWithMousewheelOne": {"message": "使用鼠标滚轮或向上/向下键调大/调小。{PH1}：R ±1、Shift：G ±1、Alt：B ±1"}, "panels/elements/StylesSidebarPane.ts | inheritedFromSPseudoOf": {"message": "继承自：{PH1}以下项的伪元素 "}, "panels/elements/StylesSidebarPane.ts | inheritedFroms": {"message": "继承自 "}, "panels/elements/StylesSidebarPane.ts | invalidPropertyValue": {"message": "属性值无效"}, "panels/elements/StylesSidebarPane.ts | invalidString": {"message": "{PH1}，属性名称：{PH2}，属性值：{PH3}"}, "panels/elements/StylesSidebarPane.ts | layer": {"message": "级联层"}, "panels/elements/StylesSidebarPane.ts | noMatchingSelectorOrStyle": {"message": "找不到匹配的选择器或样式"}, "panels/elements/StylesSidebarPane.ts | pseudoSElement": {"message": "伪 ::{PH1} 元素"}, "panels/elements/StylesSidebarPane.ts | specificity": {"message": "明确性：{PH1}"}, "panels/elements/StylesSidebarPane.ts | toggleRenderingEmulations": {"message": "显示/隐藏常用渲染模拟"}, "panels/elements/StylesSidebarPane.ts | unknownPropertyName": {"message": "属性名称未知"}, "panels/elements/StylesSidebarPane.ts | visibleSelectors": {"message": "{n,plural, =1{下方列出了 # 个可见选择器}other{下方列出了 # 个可见选择器}}"}, "panels/elements/TopLayerContainer.ts | reveal": {"message": "显示"}, "panels/elements/components/AccessibilityTreeNode.ts | ignored": {"message": "已忽略"}, "panels/elements/components/AdornerSettingsPane.ts | closeButton": {"message": "关闭"}, "panels/elements/components/AdornerSettingsPane.ts | settingsTitle": {"message": "显示标志"}, "panels/elements/components/AnchorFunctionLinkSwatch.ts | jumpToAnchorNode": {"message": "跳转到锚标记节点"}, "panels/elements/components/CSSHintDetailsView.ts | learnMore": {"message": "了解详情"}, "panels/elements/components/CSSPropertyDocsView.ts | dontShow": {"message": "不显示"}, "panels/elements/components/CSSPropertyDocsView.ts | learnMore": {"message": "了解详情"}, "panels/elements/components/CSSVariableValueView.ts | invalidPropertyValue": {"message": "属性值无效，类型应该为 {type}"}, "panels/elements/components/CSSVariableValueView.ts | registeredPropertyLinkTitle": {"message": "查看已注册的属性"}, "panels/elements/components/CSSVariableValueView.ts | sIsNotDefined": {"message": "{PH1} 未定义"}, "panels/elements/components/ElementsBreadcrumbs.ts | breadcrumbs": {"message": "DOM 树面包屑导航"}, "panels/elements/components/ElementsBreadcrumbs.ts | scrollLeft": {"message": "向左滚动"}, "panels/elements/components/ElementsBreadcrumbs.ts | scrollRight": {"message": "向右滚动"}, "panels/elements/components/ElementsBreadcrumbsUtils.ts | text": {"message": "（文本）"}, "panels/elements/components/ElementsTreeExpandButton.ts | expand": {"message": "展开"}, "panels/elements/components/LayoutPane.ts | chooseElementOverlayColor": {"message": "为此元素选择叠加层颜色"}, "panels/elements/components/LayoutPane.ts | colorPickerOpened": {"message": "颜色选择器已打开。"}, "panels/elements/components/LayoutPane.ts | flexbox": {"message": "Flexbox"}, "panels/elements/components/LayoutPane.ts | flexboxOverlays": {"message": "Flexbox 叠加层"}, "panels/elements/components/LayoutPane.ts | grid": {"message": "网格"}, "panels/elements/components/LayoutPane.ts | gridOverlays": {"message": "网格叠加层"}, "panels/elements/components/LayoutPane.ts | noFlexboxLayoutsFoundOnThisPage": {"message": "在此网页上找不到 Flexbox 布局"}, "panels/elements/components/LayoutPane.ts | noGridLayoutsFoundOnThisPage": {"message": "在此网页上找不到网格布局"}, "panels/elements/components/LayoutPane.ts | overlayDisplaySettings": {"message": "叠加层显示设置"}, "panels/elements/components/LayoutPane.ts | showElementInTheElementsPanel": {"message": "在“元素”面板中显示元素"}, "panels/elements/components/StylePropertyEditor.ts | deselectButton": {"message": "移除 {propertyName}：{propertyValue}"}, "panels/elements/components/StylePropertyEditor.ts | selectButton": {"message": "添加 {propertyName}：{propertyValue}"}, "panels/elements/elements-meta.ts | captureAreaScreenshot": {"message": "截取区域屏幕截图"}, "panels/elements/elements-meta.ts | copyStyles": {"message": "复制样式"}, "panels/elements/elements-meta.ts | disableDomWordWrap": {"message": "停用 DOM 自动换行"}, "panels/elements/elements-meta.ts | duplicateElement": {"message": "复制粘贴元素"}, "panels/elements/elements-meta.ts | editAsHtml": {"message": "以 HTML 格式修改"}, "panels/elements/elements-meta.ts | elements": {"message": "元素"}, "panels/elements/elements-meta.ts | enableDomWordWrap": {"message": "启用 DOM 自动换行"}, "panels/elements/elements-meta.ts | eventListeners": {"message": "事件监听器"}, "panels/elements/elements-meta.ts | hideElement": {"message": "隐藏元素"}, "panels/elements/elements-meta.ts | hideHtmlComments": {"message": "隐藏 HTML 注释"}, "panels/elements/elements-meta.ts | layout": {"message": "布局"}, "panels/elements/elements-meta.ts | newStyleRule": {"message": "新建样式规则"}, "panels/elements/elements-meta.ts | properties": {"message": "属性"}, "panels/elements/elements-meta.ts | redo": {"message": "重做"}, "panels/elements/elements-meta.ts | refreshEventListeners": {"message": "刷新事件监听器"}, "panels/elements/elements-meta.ts | revealDomNodeOnHover": {"message": "在鼠标悬停时显示 DOM 节点"}, "panels/elements/elements-meta.ts | selectAnElementInThePageTo": {"message": "选择网页中的相应元素即可进行检查"}, "panels/elements/elements-meta.ts | showCSSDocumentationTooltip": {"message": "显示 CSS 文档提示"}, "panels/elements/elements-meta.ts | showComputedStyles": {"message": "显示计算出的样式"}, "panels/elements/elements-meta.ts | showDetailedInspectTooltip": {"message": "显示详细检查提示"}, "panels/elements/elements-meta.ts | showElements": {"message": "显示“元素”面板"}, "panels/elements/elements-meta.ts | showEventListeners": {"message": "显示事件监听器"}, "panels/elements/elements-meta.ts | showHtmlComments": {"message": "显示 HTML 注释"}, "panels/elements/elements-meta.ts | showLayout": {"message": "显示“布局”工具"}, "panels/elements/elements-meta.ts | showProperties": {"message": "显示“属性”工具"}, "panels/elements/elements-meta.ts | showStackTrace": {"message": "显示“堆栈轨迹”工具"}, "panels/elements/elements-meta.ts | showStyles": {"message": "显示样式"}, "panels/elements/elements-meta.ts | showUserAgentShadowDOM": {"message": "显示用户代理 Shadow DOM"}, "panels/elements/elements-meta.ts | stackTrace": {"message": "堆栈轨迹"}, "panels/elements/elements-meta.ts | toggleEyeDropper": {"message": "显示/隐藏颜色提取器"}, "panels/elements/elements-meta.ts | undo": {"message": "撤消"}, "panels/elements/elements-meta.ts | wordWrap": {"message": "自动换行"}, "panels/emulation/DeviceModeToolbar.ts | addDevicePixelRatio": {"message": "添加设备像素比"}, "panels/emulation/DeviceModeToolbar.ts | addDeviceType": {"message": "添加设备类型"}, "panels/emulation/DeviceModeToolbar.ts | autoadjustZoom": {"message": "自动调整缩放级别"}, "panels/emulation/DeviceModeToolbar.ts | closeDevtools": {"message": "关闭 DevTools"}, "panels/emulation/DeviceModeToolbar.ts | defaultF": {"message": "默认值：{PH1}"}, "panels/emulation/DeviceModeToolbar.ts | devicePixelRatio": {"message": "设备像素比"}, "panels/emulation/DeviceModeToolbar.ts | devicePosture": {"message": "设备的折叠状态"}, "panels/emulation/DeviceModeToolbar.ts | deviceType": {"message": "设备类型"}, "panels/emulation/DeviceModeToolbar.ts | dimensions": {"message": "尺寸"}, "panels/emulation/DeviceModeToolbar.ts | edit": {"message": "修改…"}, "panels/emulation/DeviceModeToolbar.ts | experimentalWebPlatformFeature": {"message": "“Experimental Web Platform Feature”flag 已启用。点击即可将其停用。"}, "panels/emulation/DeviceModeToolbar.ts | experimentalWebPlatformFeatureFlag": {"message": "“Experimental Web Platform Feature”flag 已停用。点击即可将其启用。"}, "panels/emulation/DeviceModeToolbar.ts | fitToWindowF": {"message": "适合窗口大小 ({PH1}%)"}, "panels/emulation/DeviceModeToolbar.ts | heightLeaveEmptyForFull": {"message": "高度（留空即表示全高显示网页）"}, "panels/emulation/DeviceModeToolbar.ts | hideDeviceFrame": {"message": "隐藏设备边框"}, "panels/emulation/DeviceModeToolbar.ts | hideMediaQueries": {"message": "隐藏媒体查询"}, "panels/emulation/DeviceModeToolbar.ts | hideRulers": {"message": "隐藏标尺"}, "panels/emulation/DeviceModeToolbar.ts | landscape": {"message": "横向"}, "panels/emulation/DeviceModeToolbar.ts | moreOptions": {"message": "更多选项"}, "panels/emulation/DeviceModeToolbar.ts | none": {"message": "无"}, "panels/emulation/DeviceModeToolbar.ts | portrait": {"message": "纵向"}, "panels/emulation/DeviceModeToolbar.ts | removeDevicePixelRatio": {"message": "移除设备像素比"}, "panels/emulation/DeviceModeToolbar.ts | removeDeviceType": {"message": "移除设备类型"}, "panels/emulation/DeviceModeToolbar.ts | resetToDefaults": {"message": "重置为默认值"}, "panels/emulation/DeviceModeToolbar.ts | responsive": {"message": "自适应"}, "panels/emulation/DeviceModeToolbar.ts | rotate": {"message": "旋转"}, "panels/emulation/DeviceModeToolbar.ts | screenOrientationOptions": {"message": "屏幕方向选项"}, "panels/emulation/DeviceModeToolbar.ts | showDeviceFrame": {"message": "显示设备边框"}, "panels/emulation/DeviceModeToolbar.ts | showMediaQueries": {"message": "显示媒体查询"}, "panels/emulation/DeviceModeToolbar.ts | showRulers": {"message": "显示标尺"}, "panels/emulation/DeviceModeToolbar.ts | toggleDualscreenMode": {"message": "开启/关闭双屏模式"}, "panels/emulation/DeviceModeToolbar.ts | width": {"message": "宽度"}, "panels/emulation/DeviceModeToolbar.ts | zoom": {"message": "缩放"}, "panels/emulation/DeviceModeView.ts | doubleclickForFullHeight": {"message": "双击即可全高显示"}, "panels/emulation/DeviceModeView.ts | laptop": {"message": "笔记本电脑"}, "panels/emulation/DeviceModeView.ts | laptopL": {"message": "大型笔记本电脑"}, "panels/emulation/DeviceModeView.ts | mobileL": {"message": "大型移动设备"}, "panels/emulation/DeviceModeView.ts | mobileM": {"message": "中型移动设备"}, "panels/emulation/DeviceModeView.ts | mobileS": {"message": "小型移动设备"}, "panels/emulation/DeviceModeView.ts | tablet": {"message": "平板电脑"}, "panels/emulation/MediaQueryInspector.ts | revealInSourceCode": {"message": "在源代码中显示"}, "panels/emulation/emulation-meta.ts | captureFullSizeScreenshot": {"message": "截取完整尺寸的屏幕截图"}, "panels/emulation/emulation-meta.ts | captureNodeScreenshot": {"message": "当前节点屏幕截图"}, "panels/emulation/emulation-meta.ts | captureScreenshot": {"message": "截取屏幕截图"}, "panels/emulation/emulation-meta.ts | device": {"message": "设备"}, "panels/emulation/emulation-meta.ts | hideDeviceFrame": {"message": "隐藏设备边框"}, "panels/emulation/emulation-meta.ts | hideMediaQueries": {"message": "隐藏媒体查询"}, "panels/emulation/emulation-meta.ts | hideRulers": {"message": "在设备模式工具栏中隐藏标尺"}, "panels/emulation/emulation-meta.ts | showDeviceFrame": {"message": "显示设备边框"}, "panels/emulation/emulation-meta.ts | showMediaQueries": {"message": "显示媒体查询"}, "panels/emulation/emulation-meta.ts | showRulers": {"message": "在设备模式工具栏中显示标尺"}, "panels/emulation/emulation-meta.ts | toggleDeviceToolbar": {"message": "显示/隐藏设备工具栏"}, "panels/event_listeners/EventListenersView.ts | deleteEventListener": {"message": "删除事件监听器"}, "panels/event_listeners/EventListenersView.ts | noEventListeners": {"message": "无事件监听器"}, "panels/event_listeners/EventListenersView.ts | passive": {"message": "被动式"}, "panels/event_listeners/EventListenersView.ts | revealInElementsPanel": {"message": "在“元素”面板中显示"}, "panels/event_listeners/EventListenersView.ts | togglePassive": {"message": "开启/关闭被动式监听器"}, "panels/event_listeners/EventListenersView.ts | toggleWhetherEventListenerIs": {"message": "将事件监听器状态切换为被动或屏蔽"}, "panels/explain/components/ConsoleInsight.ts | back": {"message": "返回"}, "panels/explain/components/ConsoleInsight.ts | cancel": {"message": "取消"}, "panels/explain/components/ConsoleInsight.ts | closeInsight": {"message": "关闭说明"}, "panels/explain/components/ConsoleInsight.ts | consoleMessage": {"message": "控制台消息"}, "panels/explain/components/ConsoleInsight.ts | continue": {"message": "继续"}, "panels/explain/components/ConsoleInsight.ts | disableFeature": {"message": "停用此功能"}, "panels/explain/components/ConsoleInsight.ts | error": {"message": "开发者工具遇到错误"}, "panels/explain/components/ConsoleInsight.ts | errorBody": {"message": "出了点问题。请重试。"}, "panels/explain/components/ConsoleInsight.ts | generating": {"message": "正在生成说明…"}, "panels/explain/components/ConsoleInsight.ts | inputData": {"message": "用于了解此消息的数据"}, "panels/explain/components/ConsoleInsight.ts | insight": {"message": "说明"}, "panels/explain/components/ConsoleInsight.ts | learnMore": {"message": "了解详情"}, "panels/explain/components/ConsoleInsight.ts | networkRequest": {"message": "网络请求"}, "panels/explain/components/ConsoleInsight.ts | next": {"message": "下一页"}, "panels/explain/components/ConsoleInsight.ts | notAvailable": {"message": "此功能不可用"}, "panels/explain/components/ConsoleInsight.ts | notLoggedIn": {"message": "只有当您使用 Google 账号登录 Chrome 时，才能使用此功能。"}, "panels/explain/components/ConsoleInsight.ts | offline": {"message": "请检查互联网连接情况，然后重试。"}, "panels/explain/components/ConsoleInsight.ts | offlineHeader": {"message": "开发者工具无法连接到互联网"}, "panels/explain/components/ConsoleInsight.ts | opensInNewTab": {"message": "（在新标签页中打开）"}, "panels/explain/components/ConsoleInsight.ts | relatedCode": {"message": "相关代码"}, "panels/explain/components/ConsoleInsight.ts | reloadRecommendation": {"message": "请重新加载此页面，以获取此消息的相关网络请求数据，以便创建更好的数据分析。"}, "panels/explain/components/ConsoleInsight.ts | report": {"message": "举报法律问题"}, "panels/explain/components/ConsoleInsight.ts | search": {"message": "改用搜索功能"}, "panels/explain/components/ConsoleInsight.ts | signInToUse": {"message": "登录以使用此功能"}, "panels/explain/components/ConsoleInsight.ts | stackTrace": {"message": "堆栈轨迹"}, "panels/explain/components/ConsoleInsight.ts | syncIsOff": {"message": "此功能需要您开启 Chrome 同步功能。"}, "panels/explain/components/ConsoleInsight.ts | thumbsDown": {"message": "不喜欢"}, "panels/explain/components/ConsoleInsight.ts | thumbsUp": {"message": "我喜欢"}, "panels/explain/components/ConsoleInsight.ts | updateSettings": {"message": "更新设置"}, "panels/explain/explain-meta.ts | ageRestricted": {"message": "此功能仅面向年满 18 周岁的用户提供。"}, "panels/explain/explain-meta.ts | enableConsoleInsights": {"message": "利用 AI 了解控制台消息"}, "panels/explain/explain-meta.ts | explainThisError": {"message": "了解此错误"}, "panels/explain/explain-meta.ts | explainThisMessage": {"message": "了解此消息"}, "panels/explain/explain-meta.ts | explainThisWarning": {"message": "了解此警告"}, "panels/explain/explain-meta.ts | geoRestricted": {"message": "此功能在您所在的地区不可用。"}, "panels/explain/explain-meta.ts | policyRestricted": {"message": "贵组织已关闭此功能。如需了解详情，请与您的管理员联系。"}, "panels/explain/explain-meta.ts | rolloutRestricted": {"message": "此功能目前正在逐步推出。敬请期待。"}, "panels/explain/explain-meta.ts | wrongLocale": {"message": "如需使用此功能，请将开发者工具设置中的语言偏好设置更新为英语。"}, "panels/freestyler/FreestylerPanel.ts | clearMessages": {"message": "清除消息"}, "panels/freestyler/FreestylerPanel.ts | sendFeedback": {"message": "发送反馈"}, "panels/freestyler/components/FreestylerChatUi.ts | emptyStateText": {"message": "您需要什么帮助？"}, "panels/freestyler/components/FreestylerChatUi.ts | inputDisclaimer": {"message": "Freestyler 可能会显示不准确的信息，并且可能无法正确理解命令"}, "panels/freestyler/components/FreestylerChatUi.ts | inputPlaceholder": {"message": "向 Freestyler 提问，或输入 / 以查看命令"}, "panels/freestyler/components/FreestylerChatUi.ts | selectAnElement": {"message": "选择元素"}, "panels/freestyler/components/FreestylerChatUi.ts | sendButtonTitle": {"message": "发送"}, "panels/freestyler/freestyler-meta.ts | freestyler": {"message": "Freestyler"}, "panels/freestyler/freestyler-meta.ts | showFreestyler": {"message": "显示 Freestyler"}, "panels/issues/AffectedBlockedByResponseView.ts | blockedResource": {"message": "已拦截的资源"}, "panels/issues/AffectedBlockedByResponseView.ts | nRequests": {"message": "{n,plural, =1{# 项请求}other{# 项请求}}"}, "panels/issues/AffectedBlockedByResponseView.ts | parentFrame": {"message": "父框架"}, "panels/issues/AffectedBlockedByResponseView.ts | requestC": {"message": "请求"}, "panels/issues/AffectedCookiesView.ts | domain": {"message": "网域"}, "panels/issues/AffectedCookiesView.ts | filterSetCookieTitle": {"message": "在“网络”面板中显示包含此 Set-Cookie 标头的网络请求"}, "panels/issues/AffectedCookiesView.ts | nCookies": {"message": "{n,plural, =1{# 个 Cookie}other{# 个 Cookie}}"}, "panels/issues/AffectedCookiesView.ts | nRawCookieLines": {"message": "{n,plural, =1{1 个原始 Set-Cookie 标头}other{# 个原始 Set-Cookie 标头}}"}, "panels/issues/AffectedCookiesView.ts | name": {"message": "名称"}, "panels/issues/AffectedCookiesView.ts | path": {"message": "路径"}, "panels/issues/AffectedDirectivesView.ts | blocked": {"message": "已屏蔽"}, "panels/issues/AffectedDirectivesView.ts | clickToRevealTheViolatingDomNode": {"message": "点击即可在“元素”面板显示违规 DOM 节点"}, "panels/issues/AffectedDirectivesView.ts | directiveC": {"message": "指令"}, "panels/issues/AffectedDirectivesView.ts | element": {"message": "元素"}, "panels/issues/AffectedDirectivesView.ts | nDirectives": {"message": "{n,plural, =1{# 条指令}other{# 条指令}}"}, "panels/issues/AffectedDirectivesView.ts | reportonly": {"message": "仅报告"}, "panels/issues/AffectedDirectivesView.ts | resourceC": {"message": "资源"}, "panels/issues/AffectedDirectivesView.ts | sourceLocation": {"message": "源代码位置"}, "panels/issues/AffectedDirectivesView.ts | status": {"message": "状态"}, "panels/issues/AffectedDocumentsInQuirksModeView.ts | documentInTheDOMTree": {"message": "DOM 树中的文档"}, "panels/issues/AffectedDocumentsInQuirksModeView.ts | mode": {"message": "模式"}, "panels/issues/AffectedDocumentsInQuirksModeView.ts | nDocuments": {"message": "{n,plural, =1{ 个文档}other{  个文档}}"}, "panels/issues/AffectedDocumentsInQuirksModeView.ts | url": {"message": "网址"}, "panels/issues/AffectedElementsView.ts | nElements": {"message": "{n,plural, =1{# 个元素}other{# 个元素}}"}, "panels/issues/AffectedElementsWithLowContrastView.ts | contrastRatio": {"message": "对比度"}, "panels/issues/AffectedElementsWithLowContrastView.ts | element": {"message": "元素"}, "panels/issues/AffectedElementsWithLowContrastView.ts | minimumAA": {"message": "最低 AA 对比度"}, "panels/issues/AffectedElementsWithLowContrastView.ts | minimumAAA": {"message": "最低 AAA 对比度"}, "panels/issues/AffectedElementsWithLowContrastView.ts | textSize": {"message": "文字大小"}, "panels/issues/AffectedElementsWithLowContrastView.ts | textWeight": {"message": "文本字重"}, "panels/issues/AffectedHeavyAdView.ts | cpuPeakLimit": {"message": "CPU 峰值上限"}, "panels/issues/AffectedHeavyAdView.ts | cpuTotalLimit": {"message": "CPU 总限制"}, "panels/issues/AffectedHeavyAdView.ts | frameUrl": {"message": "框架网址"}, "panels/issues/AffectedHeavyAdView.ts | limitExceeded": {"message": "超出限额"}, "panels/issues/AffectedHeavyAdView.ts | nResources": {"message": "{n,plural, =1{# 项资源}other{# 项资源}}"}, "panels/issues/AffectedHeavyAdView.ts | networkLimit": {"message": "网络限制"}, "panels/issues/AffectedHeavyAdView.ts | removed": {"message": "已移除"}, "panels/issues/AffectedHeavyAdView.ts | resolutionStatus": {"message": "解决状态"}, "panels/issues/AffectedHeavyAdView.ts | warned": {"message": "已警告"}, "panels/issues/AffectedMetadataAllowedSitesView.ts | nAllowedSites": {"message": "{n,plural, =1{已允许 1 个网站访问 Cookie}other{已允许 # 个网站访问 Cookie}}"}, "panels/issues/AffectedResourcesView.ts | clickToRevealTheFramesDomNodeIn": {"message": "点击即可在“元素”面板中显示相应框架的 DOM 节点"}, "panels/issues/AffectedResourcesView.ts | unavailable": {"message": "无法使用了"}, "panels/issues/AffectedResourcesView.ts | unknown": {"message": "未知"}, "panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | aSharedarraybufferWas": {"message": "SharedArrayBuffer 已在非跨域隔离的上下文中实例化"}, "panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | blocked": {"message": "已屏蔽"}, "panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | instantiation": {"message": "实例化"}, "panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | nViolations": {"message": "{n,plural, =1{# 项违规行为}other{# 项违规行为}}"}, "panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | sharedarraybufferWasTransferedTo": {"message": "SharedArrayBuffer 已转移到非跨域隔离的上下文"}, "panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | sourceLocation": {"message": "源位置"}, "panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | status": {"message": "状态"}, "panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | transfer": {"message": "传输"}, "panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | trigger": {"message": "触发因素"}, "panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | warning": {"message": "警告"}, "panels/issues/AffectedSourcesView.ts | nSources": {"message": "{n,plural, =1{# 个来源}other{# 个来源}}"}, "panels/issues/AffectedTrackingSitesView.ts | nTrackingSites": {"message": "{n,plural, =1{有 1 个可能会跟踪的网站}other{有 # 个可能会跟踪的网站}}"}, "panels/issues/AttributionReportingIssueDetailsView.ts | element": {"message": "元素"}, "panels/issues/AttributionReportingIssueDetailsView.ts | invalidHeaderValue": {"message": "无效的标头值"}, "panels/issues/AttributionReportingIssueDetailsView.ts | nViolations": {"message": "{n,plural, =1{# 项违规行为}other{# 项违规行为}}"}, "panels/issues/AttributionReportingIssueDetailsView.ts | request": {"message": "请求"}, "panels/issues/AttributionReportingIssueDetailsView.ts | untrustworthyOrigin": {"message": "不可信的源"}, "panels/issues/CorsIssueDetailsView.ts | allowCredentialsValueFromHeader": {"message": "Access-Control-Allow-Credentials 标头值"}, "panels/issues/CorsIssueDetailsView.ts | allowedOrigin": {"message": "允许的来源（根据标头）"}, "panels/issues/CorsIssueDetailsView.ts | blocked": {"message": "已屏蔽"}, "panels/issues/CorsIssueDetailsView.ts | disallowedRequestHeader": {"message": "禁止的请求标头"}, "panels/issues/CorsIssueDetailsView.ts | disallowedRequestMethod": {"message": "禁止的请求方法"}, "panels/issues/CorsIssueDetailsView.ts | failedRequest": {"message": "失败的请求"}, "panels/issues/CorsIssueDetailsView.ts | header": {"message": "标头"}, "panels/issues/CorsIssueDetailsView.ts | initiatorAddressSpace": {"message": "启动器地址"}, "panels/issues/CorsIssueDetailsView.ts | initiatorContext": {"message": "启动器上下文"}, "panels/issues/CorsIssueDetailsView.ts | insecure": {"message": "不安全"}, "panels/issues/CorsIssueDetailsView.ts | invalidValue": {"message": "无效值（若有）"}, "panels/issues/CorsIssueDetailsView.ts | nRequests": {"message": "{n,plural, =1{# 项请求}other{# 项请求}}"}, "panels/issues/CorsIssueDetailsView.ts | preflightDisallowedRedirect": {"message": "对预检的响应是重定向"}, "panels/issues/CorsIssueDetailsView.ts | preflightInvalidStatus": {"message": "预检请求的 HTTP 状态表明未成功"}, "panels/issues/CorsIssueDetailsView.ts | preflightRequest": {"message": "预检请求"}, "panels/issues/CorsIssueDetailsView.ts | preflightRequestIfProblematic": {"message": "预检请求（如果出现问题）"}, "panels/issues/CorsIssueDetailsView.ts | problem": {"message": "问题"}, "panels/issues/CorsIssueDetailsView.ts | problemInvalidValue": {"message": "无效值"}, "panels/issues/CorsIssueDetailsView.ts | problemMissingHeader": {"message": "缺少标头"}, "panels/issues/CorsIssueDetailsView.ts | problemMultipleValues": {"message": "多个值"}, "panels/issues/CorsIssueDetailsView.ts | request": {"message": "请求"}, "panels/issues/CorsIssueDetailsView.ts | resourceAddressSpace": {"message": "资源地址"}, "panels/issues/CorsIssueDetailsView.ts | secure": {"message": "安全"}, "panels/issues/CorsIssueDetailsView.ts | sourceLocation": {"message": "源位置"}, "panels/issues/CorsIssueDetailsView.ts | status": {"message": "状态"}, "panels/issues/CorsIssueDetailsView.ts | unsupportedScheme": {"message": "架构不受支持"}, "panels/issues/CorsIssueDetailsView.ts | warning": {"message": "警告"}, "panels/issues/GenericIssueDetailsView.ts | frameId": {"message": "框架"}, "panels/issues/GenericIssueDetailsView.ts | nResources": {"message": "{n,plural, =1{# 项资源}other{# 项资源}}"}, "panels/issues/GenericIssueDetailsView.ts | violatingNode": {"message": "违规节点"}, "panels/issues/HiddenIssuesRow.ts | hiddenIssues": {"message": "已隐藏的问题"}, "panels/issues/HiddenIssuesRow.ts | unhideAll": {"message": "取消隐藏全部"}, "panels/issues/IssueKindView.ts | hideAllCurrentBreakingChanges": {"message": "隐藏当前的所有重大变更"}, "panels/issues/IssueKindView.ts | hideAllCurrentImprovements": {"message": "隐藏当前的所有改进"}, "panels/issues/IssueKindView.ts | hideAllCurrentPageErrors": {"message": "隐藏当前的所有网页错误"}, "panels/issues/IssueView.ts | affectedResources": {"message": "受影响的资源"}, "panels/issues/IssueView.ts | automaticallyUpgraded": {"message": "已自动升级"}, "panels/issues/IssueView.ts | blocked": {"message": "已屏蔽"}, "panels/issues/IssueView.ts | hideIssuesLikeThis": {"message": "隐藏与此类似的问题"}, "panels/issues/IssueView.ts | learnMoreS": {"message": "了解详情：{PH1}"}, "panels/issues/IssueView.ts | nRequests": {"message": "{n,plural, =1{# 项请求}other{# 项请求}}"}, "panels/issues/IssueView.ts | nResources": {"message": "{n,plural, =1{# 项资源}other{# 项资源}}"}, "panels/issues/IssueView.ts | name": {"message": "名称"}, "panels/issues/IssueView.ts | restrictionStatus": {"message": "限制状态"}, "panels/issues/IssueView.ts | unhideIssuesLikeThis": {"message": "取消隐藏与此类似的问题"}, "panels/issues/IssueView.ts | warned": {"message": "已警告"}, "panels/issues/IssuesPane.ts | attributionReporting": {"message": "Attribution Reporting API"}, "panels/issues/IssuesPane.ts | contentSecurityPolicy": {"message": "内容安全政策"}, "panels/issues/IssuesPane.ts | cors": {"message": "跨域资源共享"}, "panels/issues/IssuesPane.ts | crossOriginEmbedderPolicy": {"message": "跨域嵌入器政策"}, "panels/issues/IssuesPane.ts | generic": {"message": "一般"}, "panels/issues/IssuesPane.ts | groupByCategory": {"message": "按类别分组"}, "panels/issues/IssuesPane.ts | groupByKind": {"message": "按种类分组"}, "panels/issues/IssuesPane.ts | groupDisplayedIssuesUnder": {"message": "将显示的问题归入关联的类别下"}, "panels/issues/IssuesPane.ts | groupDisplayedIssuesUnderKind": {"message": "将所显示的问题分组为“网页错误”、“重大变更”和“改进”"}, "panels/issues/IssuesPane.ts | heavyAds": {"message": "过度消耗资源的广告"}, "panels/issues/IssuesPane.ts | includeCookieIssuesCausedBy": {"message": "包含由第三方网站导致的 Cookie 问题"}, "panels/issues/IssuesPane.ts | includeThirdpartyCookieIssues": {"message": "包含第三方 Cookie 问题"}, "panels/issues/IssuesPane.ts | lowTextContrast": {"message": "低文字对比度"}, "panels/issues/IssuesPane.ts | mixedContent": {"message": "混合内容"}, "panels/issues/IssuesPane.ts | noIssuesDetectedSoFar": {"message": "截至目前未检测到任何问题"}, "panels/issues/IssuesPane.ts | onlyThirdpartyCookieIssues": {"message": "目前仅检测到第三方 Cookie 问题"}, "panels/issues/IssuesPane.ts | other": {"message": "其他"}, "panels/issues/IssuesPane.ts | quirksMode": {"message": "Quirks 模式"}, "panels/issues/IssuesPane.ts | samesiteCookie": {"message": "SameSite <PERSON>"}, "panels/issues/components/HideIssuesMenu.ts | tooltipTitle": {"message": "隐藏问题"}, "panels/issues/issues-meta.ts | issues": {"message": "问题"}, "panels/issues/issues-meta.ts | showIssues": {"message": "显示“问题”工具"}, "panels/js_timeline/js_timeline-meta.ts | performance": {"message": "性能"}, "panels/js_timeline/js_timeline-meta.ts | record": {"message": "录制"}, "panels/js_timeline/js_timeline-meta.ts | showPerformance": {"message": "显示“性能”工具"}, "panels/js_timeline/js_timeline-meta.ts | showRecentTimelineSessions": {"message": "显示近期时间轴会话"}, "panels/js_timeline/js_timeline-meta.ts | startProfilingAndReloadPage": {"message": "开始分析并重新加载网页"}, "panels/js_timeline/js_timeline-meta.ts | stop": {"message": "停止"}, "panels/layer_viewer/LayerDetailsView.ts | compositingReasons": {"message": "合成原因"}, "panels/layer_viewer/LayerDetailsView.ts | containingBlocRectangleDimensions": {"message": "包含块 {PH1} × {PH2}（位于 {PH3}, {PH4}）"}, "panels/layer_viewer/LayerDetailsView.ts | mainThreadScrollingReason": {"message": "主线程滚动原因"}, "panels/layer_viewer/LayerDetailsView.ts | memoryEstimate": {"message": "内存估计值"}, "panels/layer_viewer/LayerDetailsView.ts | nearestLayerShiftingContaining": {"message": "最近的图层移动包含块"}, "panels/layer_viewer/LayerDetailsView.ts | nearestLayerShiftingStickyBox": {"message": "最近的图层移位粘滞框"}, "panels/layer_viewer/LayerDetailsView.ts | nonFastScrollable": {"message": "不可快速滚动"}, "panels/layer_viewer/LayerDetailsView.ts | paintCount": {"message": "绘制次数"}, "panels/layer_viewer/LayerDetailsView.ts | paintProfiler": {"message": "绘制性能剖析器"}, "panels/layer_viewer/LayerDetailsView.ts | repaintsOnScroll": {"message": "滚动时重新渲染"}, "panels/layer_viewer/LayerDetailsView.ts | scrollRectangleDimensions": {"message": "{PH1} {PH2} × {PH3}（位于 {PH4},{PH5}）"}, "panels/layer_viewer/LayerDetailsView.ts | selectALayerToSeeItsDetails": {"message": "选择某个层以查看其详情"}, "panels/layer_viewer/LayerDetailsView.ts | size": {"message": "大小"}, "panels/layer_viewer/LayerDetailsView.ts | slowScrollRegions": {"message": "缓慢滚动区域"}, "panels/layer_viewer/LayerDetailsView.ts | stickyAncenstorLayersS": {"message": "{PH1}：{PH2} ({PH3})"}, "panels/layer_viewer/LayerDetailsView.ts | stickyBoxRectangleDimensions": {"message": "粘滞框 {PH1} × {PH2}（位于：{PH3}, {PH4}）"}, "panels/layer_viewer/LayerDetailsView.ts | stickyPositionConstraint": {"message": "粘性位置限制"}, "panels/layer_viewer/LayerDetailsView.ts | touchEventHandler": {"message": "触摸事件处理脚本"}, "panels/layer_viewer/LayerDetailsView.ts | unnamed": {"message": "<未命名>"}, "panels/layer_viewer/LayerDetailsView.ts | updateRectangleDimensions": {"message": "{PH1} × {PH2}（位于 {PH3}, {PH4}）"}, "panels/layer_viewer/LayerDetailsView.ts | wheelEventHandler": {"message": "滚轮事件处理脚本"}, "panels/layer_viewer/LayerTreeOutline.ts | layersTreePane": {"message": "图层树窗格"}, "panels/layer_viewer/LayerTreeOutline.ts | showPaintProfiler": {"message": "显示“绘制性能剖析器”"}, "panels/layer_viewer/LayerTreeOutline.ts | updateChildDimension": {"message": " ({PH1} × {PH2})"}, "panels/layer_viewer/LayerViewHost.ts | showInternalLayers": {"message": "显示内部层"}, "panels/layer_viewer/Layers3DView.ts | cantDisplayLayers": {"message": "无法显示图层，"}, "panels/layer_viewer/Layers3DView.ts | checkSForPossibleReasons": {"message": "请检查 {PH1} 以了解可能的原因。"}, "panels/layer_viewer/Layers3DView.ts | dLayersView": {"message": "3D 图层视图"}, "panels/layer_viewer/Layers3DView.ts | deprecationWarning": {"message": "“图层”面板可能即将被弃用。在我们做出决定之前，请分享您的想法和疑虑。"}, "panels/layer_viewer/Layers3DView.ts | layerInformationIsNotYet": {"message": "尚无层信息。"}, "panels/layer_viewer/Layers3DView.ts | paints": {"message": "渲染"}, "panels/layer_viewer/Layers3DView.ts | resetView": {"message": "重置视图"}, "panels/layer_viewer/Layers3DView.ts | sendFeedback": {"message": "发送反馈"}, "panels/layer_viewer/Layers3DView.ts | showPaintProfiler": {"message": "显示“绘制性能剖析器”"}, "panels/layer_viewer/Layers3DView.ts | slowScrollRects": {"message": "慢速滚动方框"}, "panels/layer_viewer/Layers3DView.ts | webglSupportIsDisabledInYour": {"message": "您的浏览器已停用 WebGL 支持。"}, "panels/layer_viewer/PaintProfilerView.ts | bitmap": {"message": "位图"}, "panels/layer_viewer/PaintProfilerView.ts | commandLog": {"message": "命令日志"}, "panels/layer_viewer/PaintProfilerView.ts | misc": {"message": "其他"}, "panels/layer_viewer/PaintProfilerView.ts | profiling": {"message": "正在进行性能分析…"}, "panels/layer_viewer/PaintProfilerView.ts | profilingResults": {"message": "性能分析结果"}, "panels/layer_viewer/PaintProfilerView.ts | shapes": {"message": "图形"}, "panels/layer_viewer/PaintProfilerView.ts | text": {"message": "文本"}, "panels/layer_viewer/TransformController.ts | panModeX": {"message": "平移模式 (X)"}, "panels/layer_viewer/TransformController.ts | resetTransform": {"message": "重置转换 (0)"}, "panels/layer_viewer/TransformController.ts | rotateModeV": {"message": "旋转模式 (V)"}, "panels/layer_viewer/layer_viewer-meta.ts | panOrRotateDown": {"message": "向下平移或旋转"}, "panels/layer_viewer/layer_viewer-meta.ts | panOrRotateLeft": {"message": "向左平移或旋转"}, "panels/layer_viewer/layer_viewer-meta.ts | panOrRotateRight": {"message": "向右平移或旋转"}, "panels/layer_viewer/layer_viewer-meta.ts | panOrRotateUp": {"message": "向上平移或旋转"}, "panels/layer_viewer/layer_viewer-meta.ts | resetView": {"message": "重置视图"}, "panels/layer_viewer/layer_viewer-meta.ts | switchToPanMode": {"message": "切换到平移模式"}, "panels/layer_viewer/layer_viewer-meta.ts | switchToRotateMode": {"message": "切换到旋转模式"}, "panels/layer_viewer/layer_viewer-meta.ts | zoomIn": {"message": "放大"}, "panels/layer_viewer/layer_viewer-meta.ts | zoomOut": {"message": "缩小"}, "panels/layers/LayersPanel.ts | details": {"message": "详细信息"}, "panels/layers/LayersPanel.ts | profiler": {"message": "分析器"}, "panels/layers/layers-meta.ts | layers": {"message": "图层"}, "panels/layers/layers-meta.ts | showLayers": {"message": "显示“图层”工具"}, "panels/lighthouse/LighthouseController.ts | accessibility": {"message": "无障碍功能"}, "panels/lighthouse/LighthouseController.ts | applyMobileEmulation": {"message": "应用移动设备模拟"}, "panels/lighthouse/LighthouseController.ts | applyMobileEmulationDuring": {"message": "在审核期间应用移动设备模拟"}, "panels/lighthouse/LighthouseController.ts | atLeastOneCategoryMustBeSelected": {"message": "必须选择至少 1 个类别。"}, "panels/lighthouse/LighthouseController.ts | bestPractices": {"message": "最佳做法"}, "panels/lighthouse/LighthouseController.ts | canOnlyAuditHttphttpsPages": {"message": "只能审核使用 HTTP 或 HTTPS 的网页。请前往其他网页。"}, "panels/lighthouse/LighthouseController.ts | clearStorage": {"message": "清除存储数据"}, "panels/lighthouse/LighthouseController.ts | desktop": {"message": "桌面设备"}, "panels/lighthouse/LighthouseController.ts | devtoolsThrottling": {"message": "开发者工具节流（高级）"}, "panels/lighthouse/LighthouseController.ts | doesThisPageFollowBestPractices": {"message": "此网页是否遵循现代 Web 开发的最佳做法"}, "panels/lighthouse/LighthouseController.ts | enableJavaScriptSampling": {"message": "在 Lighthouse 运行期间启用 JavaScript 采样。查看轨迹时，这将在性能面板中提供更多执行详细信息，但 CPU 开销较高，并且可能会影响页面的性能。"}, "panels/lighthouse/LighthouseController.ts | enableSampling": {"message": "启用 JS 采样"}, "panels/lighthouse/LighthouseController.ts | howLongDoesThisAppTakeToShow": {"message": "此应用需要多长时间才会显示内容并变为可以使用"}, "panels/lighthouse/LighthouseController.ts | indexeddb": {"message": "IndexedDB"}, "panels/lighthouse/LighthouseController.ts | isThisPageOptimizedForSearch": {"message": "该网页是否已经过优化，以提升其在搜索引擎结果中的排名"}, "panels/lighthouse/LighthouseController.ts | isThisPageUsableByPeopleWith": {"message": "此网页是否可供残障人士使用"}, "panels/lighthouse/LighthouseController.ts | javaScriptDisabled": {"message": "JavaScript 已被停用。您需要启用 JavaScript 才能审核此网页。若要启用 JavaScript，请打开“命令”菜单，然后运行“启用 JavaScript”命令。"}, "panels/lighthouse/LighthouseController.ts | lighthouseMode": {"message": "Lighthouse 模式"}, "panels/lighthouse/LighthouseController.ts | localStorage": {"message": "本地存储空间"}, "panels/lighthouse/LighthouseController.ts | mobile": {"message": "移动设备"}, "panels/lighthouse/LighthouseController.ts | multipleTabsAreBeingControlledBy": {"message": "多个标签页正受到同一个 service worker 的控制。请关闭同一来源的其他标签页以审核此网页。"}, "panels/lighthouse/LighthouseController.ts | navigation": {"message": "导航（默认）"}, "panels/lighthouse/LighthouseController.ts | navigationTooltip": {"message": "导航模式旨在分析网页加载情况，与最初的 Lighthouse 报告完全一样。"}, "panels/lighthouse/LighthouseController.ts | performance": {"message": "性能"}, "panels/lighthouse/LighthouseController.ts | resetStorageLocalstorage": {"message": "在审核前重置存储空间（cache 和 service workers 等）。（适用于性能和 PWA 测试）"}, "panels/lighthouse/LighthouseController.ts | runLighthouseInMode": {"message": "在导航模式、时间跨度模式或快照模式下运行 Lighthouse"}, "panels/lighthouse/LighthouseController.ts | seo": {"message": "SEO"}, "panels/lighthouse/LighthouseController.ts | simulateASlowerPageLoadBasedOn": {"message": "模拟节流会根据初始未节流加载的数据来模拟较慢的网页加载速度。开发者工具节流确实会减慢网页的加载速度。"}, "panels/lighthouse/LighthouseController.ts | simulatedThrottling": {"message": "模拟节流（默认）"}, "panels/lighthouse/LighthouseController.ts | snapshot": {"message": "快照"}, "panels/lighthouse/LighthouseController.ts | snapshotTooltip": {"message": "快照模式旨在分析处于特定状态（通常是用户互动之后）的网页。"}, "panels/lighthouse/LighthouseController.ts | thereMayBeStoredDataAffectingLoadingPlural": {"message": "下列位置可能存储了会影响加载性能的数据：{PH1}。请在无痕式窗口中审核此页面，以防止这些资源影响您的得分。"}, "panels/lighthouse/LighthouseController.ts | thereMayBeStoredDataAffectingSingular": {"message": "下列位置可能存储了会影响加载性能的数据：{PH1}。请在无痕式窗口中审核此网页，以防止这些资源影响您的得分。"}, "panels/lighthouse/LighthouseController.ts | throttlingMethod": {"message": "节流方法"}, "panels/lighthouse/LighthouseController.ts | timespan": {"message": "时间跨度"}, "panels/lighthouse/LighthouseController.ts | timespanTooltip": {"message": "时间跨度模式旨在分析任意时间段（通常包含用户互动）。"}, "panels/lighthouse/LighthouseController.ts | webSql": {"message": "Web SQL"}, "panels/lighthouse/LighthousePanel.ts | cancelling": {"message": "正在取消"}, "panels/lighthouse/LighthousePanel.ts | clearAll": {"message": "全部清除"}, "panels/lighthouse/LighthousePanel.ts | dropLighthouseJsonHere": {"message": "将 Lighthouse JSON 拖到此处"}, "panels/lighthouse/LighthousePanel.ts | lighthouseSettings": {"message": "Lighthouse 设置"}, "panels/lighthouse/LighthousePanel.ts | performAnAudit": {"message": "执行审核…"}, "panels/lighthouse/LighthousePanel.ts | printing": {"message": "正在打印"}, "panels/lighthouse/LighthousePanel.ts | thePrintPopupWindowIsOpenPlease": {"message": "弹出式窗口“打印”已打开。请关闭它以继续操作。"}, "panels/lighthouse/LighthouseReportSelector.ts | newReport": {"message": "（新报告）"}, "panels/lighthouse/LighthouseReportSelector.ts | reports": {"message": "报告"}, "panels/lighthouse/LighthouseStartView.ts | analyzeNavigation": {"message": "分析网页加载情况"}, "panels/lighthouse/LighthouseStartView.ts | analyzeSnapshot": {"message": "分析网页状态"}, "panels/lighthouse/LighthouseStartView.ts | categories": {"message": "类别"}, "panels/lighthouse/LighthouseStartView.ts | device": {"message": "设备"}, "panels/lighthouse/LighthouseStartView.ts | generateLighthouseReport": {"message": "生成 Lighthouse 报告"}, "panels/lighthouse/LighthouseStartView.ts | learnMore": {"message": "了解详情"}, "panels/lighthouse/LighthouseStartView.ts | mode": {"message": "模式"}, "panels/lighthouse/LighthouseStartView.ts | startTimespan": {"message": "启用时间跨度模式"}, "panels/lighthouse/LighthouseStatusView.ts | OfGlobalMobileUsersInWereOnGOrG": {"message": "2016 年，全球有 75% 的手机用户使用的是 2G 或 3G 网络 [来源：GSMA Mobile]"}, "panels/lighthouse/LighthouseStatusView.ts | OfMobilePagesTakeNearlySeconds": {"message": "70% 的移动版页面需要将近 7 秒的时间才能在屏幕上显示首屏视觉内容。[来源：Think with Google]"}, "panels/lighthouse/LighthouseStatusView.ts | SecondsIsTheAverageTimeAMobile": {"message": "移动网页使用 3G 网络连接完成加载所需的平均时间为 19 秒。[来源：Google DoubleClick blog]"}, "panels/lighthouse/LighthouseStatusView.ts | ahSorryWeRanIntoAnError": {"message": "抱歉！出错了。"}, "panels/lighthouse/LighthouseStatusView.ts | almostThereLighthouseIsNow": {"message": "即将完成！Lighthouse 正在为您生成报告。"}, "panels/lighthouse/LighthouseStatusView.ts | asPageLoadTimeIncreasesFromOne": {"message": "当网页加载用时从 1 秒增加到 7 秒时，移动网站访问者的跳出概率会增大 113%。[来源：Think with Google]"}, "panels/lighthouse/LighthouseStatusView.ts | asTheNumberOfElementsOnAPage": {"message": "随着网页上元素的数量从 400 增加到 6000，转化率下降了 95%。[来源：Think with Google]"}, "panels/lighthouse/LighthouseStatusView.ts | auditingS": {"message": "正在审核 {PH1}"}, "panels/lighthouse/LighthouseStatusView.ts | auditingYourWebPage": {"message": "正在评估您的网页"}, "panels/lighthouse/LighthouseStatusView.ts | byReducingTheResponseSizeOfJson": {"message": "通过降低显示评论所需的 JSON 的响应大小，Instagram 的展示次数得到了提升 [来源：WPO Stats]"}, "panels/lighthouse/LighthouseStatusView.ts | cancel": {"message": "取消"}, "panels/lighthouse/LighthouseStatusView.ts | cancelling": {"message": "正在取消…"}, "panels/lighthouse/LighthouseStatusView.ts | fastFactMessageWithPlaceholder": {"message": "💡 {PH1}"}, "panels/lighthouse/LighthouseStatusView.ts | ifASiteTakesSecondToBecome": {"message": "如果某个网站需要花费 >1 秒的时间才能进入可互动的状态，用户就会对该网站失去兴趣，并且会不愿完成网页任务 [来源：Google Developers Blog]"}, "panels/lighthouse/LighthouseStatusView.ts | ifThisIssueIsReproduciblePlease": {"message": "如果此问题可重现，请在 Lighthouse GitHub 代码库中报告此问题。"}, "panels/lighthouse/LighthouseStatusView.ts | lighthouseIsGatheringInformation": {"message": "Lighthouse 正在收集该网页的相关信息以计算您的得分。"}, "panels/lighthouse/LighthouseStatusView.ts | lighthouseIsLoadingThePage": {"message": "Lighthouse 正在加载网页。"}, "panels/lighthouse/LighthouseStatusView.ts | lighthouseIsLoadingYourPage": {"message": "Lighthouse 正在加载您的网页"}, "panels/lighthouse/LighthouseStatusView.ts | lighthouseIsLoadingYourPageWith": {"message": "Lighthouse 正在使用节流功能加载您的网页，以便衡量 3G 网络下移动设备的性能。"}, "panels/lighthouse/LighthouseStatusView.ts | lighthouseIsLoadingYourPageWithMobile": {"message": "Lighthouse 正在通过移动设备模拟加载您的网页。"}, "panels/lighthouse/LighthouseStatusView.ts | lighthouseIsLoadingYourPageWithThrottling": {"message": "Lighthouse 正在使用节流功能加载您的网页，以便衡量 3G 网络下慢速桌面设备的性能。"}, "panels/lighthouse/LighthouseStatusView.ts | lighthouseIsWarmingUp": {"message": "Lighthouse 正在预热…"}, "panels/lighthouse/LighthouseStatusView.ts | lighthouseOnlySimulatesMobile": {"message": "Lighthouse 仅模拟移动端性能；若要衡量在实际设备上的性能，请访问 WebPageTest.org [来源：Lighthouse 团队]"}, "panels/lighthouse/LighthouseStatusView.ts | loading": {"message": "正在加载…"}, "panels/lighthouse/LighthouseStatusView.ts | mbTakesAMinimumOfSecondsTo": {"message": "通过一般的 3G 网络连接下载 1MB 内容至少需要 5 秒 [来源：WebPageTest 和 DevTools 3G 定义]。"}, "panels/lighthouse/LighthouseStatusView.ts | rebuildingPinterestPagesFor": {"message": "Pinterest 以优化性能为目的重新构建网页后，转化率提升了 15% [来源：WPO Stats]"}, "panels/lighthouse/LighthouseStatusView.ts | theAverageUserDeviceCostsLess": {"message": "用户设备平均成本低于 200 美元。[来源：International Data Corporation]"}, "panels/lighthouse/LighthouseStatusView.ts | tryToNavigateToTheUrlInAFresh": {"message": "尝试在新的 Chrome 个人资料名下转到相应网址，并且不打开任何其他标签页或扩展程序，然后重试。"}, "panels/lighthouse/LighthouseStatusView.ts | walmartSawAIncreaseInRevenueFor": {"message": "Walmart 发现，网页加载用时每减少 100 毫秒，收入就会增加 1% [来源：WPO Stats]"}, "panels/lighthouse/LighthouseTimespanView.ts | cancel": {"message": "取消"}, "panels/lighthouse/LighthouseTimespanView.ts | endTimespan": {"message": "结束时间跨度模式"}, "panels/lighthouse/LighthouseTimespanView.ts | timespanStarted": {"message": "时间跨度模式已启用，与网页互动"}, "panels/lighthouse/LighthouseTimespanView.ts | timespanStarting": {"message": "时间跨度模式正在开启…"}, "panels/lighthouse/lighthouse-meta.ts | showLighthouse": {"message": "显示 Lighthouse"}, "panels/linear_memory_inspector/LinearMemoryInspectorController.ts | couldNotOpenLinearMemory": {"message": "无法打开线性内存检查器：无法找到缓冲区。"}, "panels/linear_memory_inspector/LinearMemoryInspectorController.ts | revealInMemoryInspectorPanel": {"message": "在“内存检查器”面板中显示"}, "panels/linear_memory_inspector/LinearMemoryInspectorPane.ts | noOpenInspections": {"message": "没有待处理的检查"}, "panels/linear_memory_inspector/components/LinearMemoryHighlightChipList.ts | deleteHighlight": {"message": "停止突出显示此内存"}, "panels/linear_memory_inspector/components/LinearMemoryHighlightChipList.ts | jumpToAddress": {"message": "跳转到此内存"}, "panels/linear_memory_inspector/components/LinearMemoryInspector.ts | addressHasToBeANumberBetweenSAnd": {"message": "地址必须是一个介于 {PH1} 和 {PH2} 之间的数字"}, "panels/linear_memory_inspector/components/LinearMemoryNavigator.ts | enterAddress": {"message": "输入地址"}, "panels/linear_memory_inspector/components/LinearMemoryNavigator.ts | goBackInAddressHistory": {"message": "回顾地址历史记录"}, "panels/linear_memory_inspector/components/LinearMemoryNavigator.ts | goForwardInAddressHistory": {"message": "在地址历史记录中前进"}, "panels/linear_memory_inspector/components/LinearMemoryNavigator.ts | nextPage": {"message": "下一页"}, "panels/linear_memory_inspector/components/LinearMemoryNavigator.ts | previousPage": {"message": "上一页"}, "panels/linear_memory_inspector/components/LinearMemoryNavigator.ts | refresh": {"message": "刷新"}, "panels/linear_memory_inspector/components/LinearMemoryValueInterpreter.ts | changeEndianness": {"message": "更改Endianness"}, "panels/linear_memory_inspector/components/LinearMemoryValueInterpreter.ts | toggleValueTypeSettings": {"message": "开启/关闭值类型设置"}, "panels/linear_memory_inspector/components/ValueInterpreterDisplay.ts | addressOutOfRange": {"message": "地址超出内存范围"}, "panels/linear_memory_inspector/components/ValueInterpreterDisplay.ts | changeValueTypeMode": {"message": "更改模式"}, "panels/linear_memory_inspector/components/ValueInterpreterDisplay.ts | jumpToPointer": {"message": "跳转到地址"}, "panels/linear_memory_inspector/components/ValueInterpreterDisplay.ts | signedValue": {"message": "Signed的值"}, "panels/linear_memory_inspector/components/ValueInterpreterDisplay.ts | unsignedValue": {"message": "Unsigned的值"}, "panels/linear_memory_inspector/components/ValueInterpreterDisplayUtils.ts | notApplicable": {"message": "不适用"}, "panels/linear_memory_inspector/components/ValueInterpreterSettings.ts | otherGroup": {"message": "其他"}, "panels/linear_memory_inspector/linear_memory_inspector-meta.ts | memoryInspector": {"message": "内存检查器"}, "panels/linear_memory_inspector/linear_memory_inspector-meta.ts | showMemoryInspector": {"message": "显示“内存检查器”"}, "panels/media/EventDisplayTable.ts | eventDisplay": {"message": "事件显示"}, "panels/media/EventDisplayTable.ts | eventName": {"message": "事件名称"}, "panels/media/EventDisplayTable.ts | timestamp": {"message": "时间戳"}, "panels/media/EventDisplayTable.ts | value": {"message": "值"}, "panels/media/EventTimelineView.ts | bufferingStatus": {"message": "缓冲状态"}, "panels/media/EventTimelineView.ts | playbackStatus": {"message": "播放状态"}, "panels/media/PlayerDetailView.ts | events": {"message": "事件"}, "panels/media/PlayerDetailView.ts | messages": {"message": "消息"}, "panels/media/PlayerDetailView.ts | playerEvents": {"message": "播放器事件"}, "panels/media/PlayerDetailView.ts | playerMessages": {"message": "播放器消息"}, "panels/media/PlayerDetailView.ts | playerProperties": {"message": "播放器属性"}, "panels/media/PlayerDetailView.ts | playerTimeline": {"message": "播放器时间轴"}, "panels/media/PlayerDetailView.ts | properties": {"message": "属性"}, "panels/media/PlayerDetailView.ts | timeline": {"message": "时间轴"}, "panels/media/PlayerListView.ts | hideAllOthers": {"message": "隐藏其他所有条目"}, "panels/media/PlayerListView.ts | hidePlayer": {"message": "隐藏播放器"}, "panels/media/PlayerListView.ts | players": {"message": "播放器"}, "panels/media/PlayerListView.ts | savePlayerInfo": {"message": "保存播放器信息"}, "panels/media/PlayerMessagesView.ts | all": {"message": "全部"}, "panels/media/PlayerMessagesView.ts | custom": {"message": "自定义"}, "panels/media/PlayerMessagesView.ts | debug": {"message": "调试"}, "panels/media/PlayerMessagesView.ts | default": {"message": "默认"}, "panels/media/PlayerMessagesView.ts | error": {"message": "错误"}, "panels/media/PlayerMessagesView.ts | errorCauseLabel": {"message": "原因："}, "panels/media/PlayerMessagesView.ts | errorCodeLabel": {"message": "错误代码："}, "panels/media/PlayerMessagesView.ts | errorDataLabel": {"message": "数据："}, "panels/media/PlayerMessagesView.ts | errorGroupLabel": {"message": "错误组："}, "panels/media/PlayerMessagesView.ts | errorStackLabel": {"message": "堆栈轨迹："}, "panels/media/PlayerMessagesView.ts | filterByLogMessages": {"message": "按日志消息过滤"}, "panels/media/PlayerMessagesView.ts | info": {"message": "信息"}, "panels/media/PlayerMessagesView.ts | logLevel": {"message": "日志级别："}, "panels/media/PlayerMessagesView.ts | warning": {"message": "警告"}, "panels/media/PlayerPropertiesView.ts | audio": {"message": "音频"}, "panels/media/PlayerPropertiesView.ts | bitrate": {"message": "比特率"}, "panels/media/PlayerPropertiesView.ts | decoder": {"message": "解码器"}, "panels/media/PlayerPropertiesView.ts | decoderName": {"message": "解码器名称"}, "panels/media/PlayerPropertiesView.ts | decryptingDemuxer": {"message": "正在为 demuxer 解密"}, "panels/media/PlayerPropertiesView.ts | duration": {"message": "时长"}, "panels/media/PlayerPropertiesView.ts | encoderName": {"message": "编码器名称"}, "panels/media/PlayerPropertiesView.ts | fileSize": {"message": "文件大小"}, "panels/media/PlayerPropertiesView.ts | frameRate": {"message": "帧速率"}, "panels/media/PlayerPropertiesView.ts | hardwareDecoder": {"message": "硬件解码器"}, "panels/media/PlayerPropertiesView.ts | hardwareEncoder": {"message": "硬件编码器"}, "panels/media/PlayerPropertiesView.ts | noDecoder": {"message": "无解码器"}, "panels/media/PlayerPropertiesView.ts | noEncoder": {"message": "无解码器"}, "panels/media/PlayerPropertiesView.ts | noTextTracks": {"message": "无文本轨道"}, "panels/media/PlayerPropertiesView.ts | playbackFrameTitle": {"message": "播放框架标题"}, "panels/media/PlayerPropertiesView.ts | playbackFrameUrl": {"message": "播放框架网址"}, "panels/media/PlayerPropertiesView.ts | properties": {"message": "属性"}, "panels/media/PlayerPropertiesView.ts | rangeHeaderSupport": {"message": "支持 Range 标头"}, "panels/media/PlayerPropertiesView.ts | rendererName": {"message": "渲染程序名称"}, "panels/media/PlayerPropertiesView.ts | resolution": {"message": "分辨率"}, "panels/media/PlayerPropertiesView.ts | singleoriginPlayback": {"message": "单源播放"}, "panels/media/PlayerPropertiesView.ts | startTime": {"message": "开始时间"}, "panels/media/PlayerPropertiesView.ts | streaming": {"message": "在线播放"}, "panels/media/PlayerPropertiesView.ts | textTrack": {"message": "文本轨道"}, "panels/media/PlayerPropertiesView.ts | track": {"message": "曲目"}, "panels/media/PlayerPropertiesView.ts | video": {"message": "视频"}, "panels/media/PlayerPropertiesView.ts | videoFreezingScore": {"message": "视频冻结得分"}, "panels/media/PlayerPropertiesView.ts | videoPlaybackRoughness": {"message": "视频播放质量差距"}, "panels/media/media-meta.ts | media": {"message": "媒体"}, "panels/media/media-meta.ts | showMedia": {"message": "显示“媒体”"}, "panels/media/media-meta.ts | video": {"message": "视频"}, "panels/mobile_throttling/MobileThrottlingSelector.ts | advanced": {"message": "高级"}, "panels/mobile_throttling/MobileThrottlingSelector.ts | disabled": {"message": "已停用"}, "panels/mobile_throttling/MobileThrottlingSelector.ts | presets": {"message": "预设"}, "panels/mobile_throttling/NetworkPanelIndicator.ts | acceptedEncodingOverrideSet": {"message": "接受的 Content-Encoding 标头集合已被开发者工具修改，请查看“网络状况”面板"}, "panels/mobile_throttling/NetworkPanelIndicator.ts | networkThrottlingIsEnabled": {"message": "已启用网络节流"}, "panels/mobile_throttling/NetworkPanelIndicator.ts | requestsMayBeBlocked": {"message": "请求可能会被屏蔽，请查看“网络请求屏蔽”面板"}, "panels/mobile_throttling/NetworkPanelIndicator.ts | requestsMayBeOverridden": {"message": "请求可能会在本地被替换，请查看“来源”面板"}, "panels/mobile_throttling/NetworkThrottlingSelector.ts | custom": {"message": "自定义"}, "panels/mobile_throttling/NetworkThrottlingSelector.ts | disabled": {"message": "已停用"}, "panels/mobile_throttling/NetworkThrottlingSelector.ts | presets": {"message": "预设"}, "panels/mobile_throttling/ThrottlingManager.ts | add": {"message": "添加…"}, "panels/mobile_throttling/ThrottlingManager.ts | addS": {"message": "添加{PH1}"}, "panels/mobile_throttling/ThrottlingManager.ts | cpuThrottling": {"message": "CPU 节流"}, "panels/mobile_throttling/ThrottlingManager.ts | cpuThrottlingIsEnabled": {"message": "CPU 节流已启用"}, "panels/mobile_throttling/ThrottlingManager.ts | dSlowdown": {"message": "{PH1} 倍降速"}, "panels/mobile_throttling/ThrottlingManager.ts | excessConcurrency": {"message": "超过默认值可能会降低系统性能。"}, "panels/mobile_throttling/ThrottlingManager.ts | forceDisconnectedFromNetwork": {"message": "强制断开网络连接"}, "panels/mobile_throttling/ThrottlingManager.ts | hardwareConcurrency": {"message": "硬件并发"}, "panels/mobile_throttling/ThrottlingManager.ts | hardwareConcurrencyIsEnabled": {"message": "硬件并发替换已启用"}, "panels/mobile_throttling/ThrottlingManager.ts | hardwareConcurrencySettingTooltip": {"message": "替换页面上 navgator.hardwareConcurrency 报告的值"}, "panels/mobile_throttling/ThrottlingManager.ts | noThrottling": {"message": "已停用节流模式"}, "panels/mobile_throttling/ThrottlingManager.ts | offline": {"message": "离线"}, "panels/mobile_throttling/ThrottlingManager.ts | resetConcurrency": {"message": "重置为默认值"}, "panels/mobile_throttling/ThrottlingManager.ts | sS": {"message": "{PH1}：{PH2}"}, "panels/mobile_throttling/ThrottlingManager.ts | throttling": {"message": "节流"}, "panels/mobile_throttling/ThrottlingPresets.ts | checkNetworkAndPerformancePanels": {"message": "检查“网络”和“性能”面板"}, "panels/mobile_throttling/ThrottlingPresets.ts | custom": {"message": "自定义"}, "panels/mobile_throttling/ThrottlingPresets.ts | fastGXCpuSlowdown": {"message": "快速 3G 和 4 倍 CPU 降速"}, "panels/mobile_throttling/ThrottlingPresets.ts | lowendMobile": {"message": "低端手机"}, "panels/mobile_throttling/ThrottlingPresets.ts | midtierMobile": {"message": "中端移动设备"}, "panels/mobile_throttling/ThrottlingPresets.ts | noInternetConnectivity": {"message": "未连接到互联网"}, "panels/mobile_throttling/ThrottlingPresets.ts | noThrottling": {"message": "已停用节流模式"}, "panels/mobile_throttling/ThrottlingPresets.ts | slowGXCpuSlowdown": {"message": "慢速 3G 和 6 倍 CPU 降速"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | addCustomProfile": {"message": "添加自定义配置文件…"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | dms": {"message": "{PH1} ms"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | download": {"message": "下载"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | dskbits": {"message": "{PH1} kbit/s"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | fsmbits": {"message": "{PH1} Mbit/s"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | latency": {"message": "延迟时间"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | latencyMustBeAnIntegerBetweenSms": {"message": "延迟时间必须是介于 {PH1} ms到 {PH2} ms（含端点值）之间的整数"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | networkThrottlingProfiles": {"message": "网络节流性能分析报告"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | off": {"message": "已停用"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | on": {"message": "已启用"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | optional": {"message": "可选"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | packet": {"message": "数据包"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | packetLoss": {"message": "丟包率"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | packetLossMustBeAnIntegerBetweenSpct": {"message": "丢包率必须是介于 {PH1}% 到 {PH2}%（含端点值）之间的数字"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | packetQueueLength": {"message": "数据包队列长度"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | packetQueueLengthMustBeAnIntegerGreaterOrEqualToZero": {"message": "数据包队列长度必须大于或等于 0"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | packetReordering": {"message": "数据包重新排序"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | percent": {"message": "百分比"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | profileName": {"message": "性能分析报告名称"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | profileNameCharactersLengthMust": {"message": "配置文件名称的长度必须介于 1 到 {PH1} 个字符之间（包括端点值）"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | sMustBeANumberBetweenSkbsToSkbs": {"message": "{PH1}必须是介于 {PH2} kbit/s到 {PH3} kbit/s（含端点值）之间的数字"}, "panels/mobile_throttling/ThrottlingSettingsTab.ts | upload": {"message": "上传"}, "panels/mobile_throttling/mobile_throttling-meta.ts | device": {"message": "设备"}, "panels/mobile_throttling/mobile_throttling-meta.ts | enableFastGThrottling": {"message": "启用快速 3G 节流"}, "panels/mobile_throttling/mobile_throttling-meta.ts | enableSlowGThrottling": {"message": "启用低速 3G 节流"}, "panels/mobile_throttling/mobile_throttling-meta.ts | goOffline": {"message": "转为离线模式"}, "panels/mobile_throttling/mobile_throttling-meta.ts | goOnline": {"message": "恢复在线状态"}, "panels/mobile_throttling/mobile_throttling-meta.ts | showThrottling": {"message": "显示“节流”"}, "panels/mobile_throttling/mobile_throttling-meta.ts | throttling": {"message": "节流"}, "panels/mobile_throttling/mobile_throttling-meta.ts | throttlingTag": {"message": "节流"}, "panels/network/BinaryResourceView.ts | binaryViewType": {"message": "二进制视图类型"}, "panels/network/BinaryResourceView.ts | copiedAsBase": {"message": "已经以 Base64 格式复制"}, "panels/network/BinaryResourceView.ts | copiedAsHex": {"message": "已经以 Hex 格式复制"}, "panels/network/BinaryResourceView.ts | copiedAsUtf": {"message": "已经以 UTF-8 格式复制"}, "panels/network/BinaryResourceView.ts | copyAsBase": {"message": "以 Base64 格式复制"}, "panels/network/BinaryResourceView.ts | copyAsHex": {"message": "以 Hex 格式复制"}, "panels/network/BinaryResourceView.ts | copyAsUtf": {"message": "以 UTF-8 格式复制"}, "panels/network/BinaryResourceView.ts | copyToClipboard": {"message": "复制到剪贴板"}, "panels/network/BinaryResourceView.ts | hexViewer": {"message": "Hex查看器"}, "panels/network/BlockedURLsPane.ts | addNetworkRequestBlockingPattern": {"message": "添加网络请求屏蔽模式"}, "panels/network/BlockedURLsPane.ts | addPattern": {"message": "添加模式"}, "panels/network/BlockedURLsPane.ts | dBlocked": {"message": "已屏蔽 {PH1} 个"}, "panels/network/BlockedURLsPane.ts | enableNetworkRequestBlocking": {"message": "启用网络请求屏蔽功能"}, "panels/network/BlockedURLsPane.ts | itemDeleted": {"message": "已成功删除此列表项"}, "panels/network/BlockedURLsPane.ts | networkRequestsAreNotBlockedS": {"message": "未屏蔽网络请求。{PH1}"}, "panels/network/BlockedURLsPane.ts | patternAlreadyExists": {"message": "模式已经存在。"}, "panels/network/BlockedURLsPane.ts | patternInputCannotBeEmpty": {"message": "模式输入不能为空。"}, "panels/network/BlockedURLsPane.ts | textPatternToBlockMatching": {"message": "用于屏蔽匹配请求的文本模式；请使用 * 作为通配符"}, "panels/network/EventSourceMessagesView.ts | clearAll": {"message": "全部清除"}, "panels/network/EventSourceMessagesView.ts | copyMessage": {"message": "复制消息"}, "panels/network/EventSourceMessagesView.ts | data": {"message": "数据"}, "panels/network/EventSourceMessagesView.ts | eventSource": {"message": "事件来源"}, "panels/network/EventSourceMessagesView.ts | filterByRegex": {"message": "使用正则表达式进行过滤（例如：https?）"}, "panels/network/EventSourceMessagesView.ts | id": {"message": "ID"}, "panels/network/EventSourceMessagesView.ts | time": {"message": "时间"}, "panels/network/EventSourceMessagesView.ts | type": {"message": "类型"}, "panels/network/NetworkConfigView.ts | acceptedEncoding": {"message": "接受的 Content-Encoding"}, "panels/network/NetworkConfigView.ts | caching": {"message": "缓存"}, "panels/network/NetworkConfigView.ts | clientHintsStatusText": {"message": "用户代理已更新。"}, "panels/network/NetworkConfigView.ts | custom": {"message": "自定义…"}, "panels/network/NetworkConfigView.ts | customUserAgentFieldIsRequired": {"message": "“自定义用户代理”字段是必填项"}, "panels/network/NetworkConfigView.ts | disableCache": {"message": "停用缓存"}, "panels/network/NetworkConfigView.ts | enterACustomUserAgent": {"message": "输入自定义用户代理"}, "panels/network/NetworkConfigView.ts | networkConditionsPanelShown": {"message": "已显示“网络状况”面板"}, "panels/network/NetworkConfigView.ts | networkThrottling": {"message": "网络节流"}, "panels/network/NetworkConfigView.ts | selectAutomatically": {"message": "使用浏览器默认设置"}, "panels/network/NetworkConfigView.ts | userAgent": {"message": "用户代理"}, "panels/network/NetworkDataGridNode.ts | alternativeJobWonRace": {"message": "Chrome 使用了由“Alt-Svc”标头引发的 HTTP/3 连接，因为该连接在与使用不同 HTTP 版本建立的连接竞争时胜出。"}, "panels/network/NetworkDataGridNode.ts | alternativeJobWonWithoutRace": {"message": "Chrome 使用了由“Alt-Svc”标头引发的 HTTP/3 连接，该连接未与使用不同 HTTP 版本建立的连接发生竞争。"}, "panels/network/NetworkDataGridNode.ts | blockedTooltip": {"message": "此请求因配置有误的响应标头而被屏蔽，点击即可查看这些标头"}, "panels/network/NetworkDataGridNode.ts | blockeds": {"message": "（已屏蔽：{PH1}）"}, "panels/network/NetworkDataGridNode.ts | broken": {"message": "Chrome 未尝试建立 HTTP/3 连接，因为该连接已被标记为损坏。"}, "panels/network/NetworkDataGridNode.ts | canceled": {"message": "（已取消）"}, "panels/network/NetworkDataGridNode.ts | corsError": {"message": "CORS 错误"}, "panels/network/NetworkDataGridNode.ts | crossoriginResourceSharingErrorS": {"message": "跨域资源共享错误：{PH1}"}, "panels/network/NetworkDataGridNode.ts | csp": {"message": "csp"}, "panels/network/NetworkDataGridNode.ts | data": {"message": "（数据）"}, "panels/network/NetworkDataGridNode.ts | devtools": {"message": "DevTools"}, "panels/network/NetworkDataGridNode.ts | diskCache": {"message": "(disk cache)"}, "panels/network/NetworkDataGridNode.ts | dnsAlpnH3JobWonRace": {"message": "Chrome 使用了 HTTP/3 连接，因为 DNS record表明支持 HTTP/3，该连接在与使用不同 HTTP 版本建立的连接竞争时胜出。"}, "panels/network/NetworkDataGridNode.ts | dnsAlpnH3JobWonWithoutRace": {"message": "Chrome 使用了 HTTP/3 连接，因为 DNS record表明支持 HTTP/3。该连接未与使用不同 HTTP 版本建立的连接发生竞争。"}, "panels/network/NetworkDataGridNode.ts | earlyHints": {"message": "早期提示"}, "panels/network/NetworkDataGridNode.ts | failed": {"message": "（失败）"}, "panels/network/NetworkDataGridNode.ts | finished": {"message": "已完成"}, "panels/network/NetworkDataGridNode.ts | initialPriorityToolTip": {"message": "{PH1}，初始优先级：{PH2}"}, "panels/network/NetworkDataGridNode.ts | level": {"message": "级别 1"}, "panels/network/NetworkDataGridNode.ts | mainJobWonRace": {"message": "Chrome 使用了该协议，因为它在与使用 HTTP/3 建立的连接竞争时胜出。"}, "panels/network/NetworkDataGridNode.ts | mappingMissing": {"message": "Chrome 未使用替代 HTTP 版本，因为在发出请求时没有任何可用的替代协议信息，但响应包含“Alt-Svc”标头。"}, "panels/network/NetworkDataGridNode.ts | matchedToServiceWorkerRouter": {"message": "与 ServiceWorker router#{PH1} 匹配，资源大小：{PH2}"}, "panels/network/NetworkDataGridNode.ts | matchedToServiceWorkerRouterWithNetworkSource": {"message": "与 ServiceWorker router#{PH1} 匹配，已通过网络传输 {PH2}，资源大小：{PH3}"}, "panels/network/NetworkDataGridNode.ts | memoryCache": {"message": "（内存缓存）"}, "panels/network/NetworkDataGridNode.ts | origin": {"message": "来源"}, "panels/network/NetworkDataGridNode.ts | other": {"message": "其他"}, "panels/network/NetworkDataGridNode.ts | otherC": {"message": "其他"}, "panels/network/NetworkDataGridNode.ts | parser": {"message": "解析器"}, "panels/network/NetworkDataGridNode.ts | pending": {"message": "待处理"}, "panels/network/NetworkDataGridNode.ts | pendingq": {"message": "（待处理）"}, "panels/network/NetworkDataGridNode.ts | prefetchCache": {"message": "（预提取缓存）"}, "panels/network/NetworkDataGridNode.ts | preflight": {"message": "预检"}, "panels/network/NetworkDataGridNode.ts | preload": {"message": "预加载"}, "panels/network/NetworkDataGridNode.ts | push": {"message": "推送 / "}, "panels/network/NetworkDataGridNode.ts | redirect": {"message": "重定向"}, "panels/network/NetworkDataGridNode.ts | requestContentHeadersOverridden": {"message": "请求内容和标头均已被替换"}, "panels/network/NetworkDataGridNode.ts | requestContentOverridden": {"message": "请求内容已被替换"}, "panels/network/NetworkDataGridNode.ts | requestHeadersOverridden": {"message": "请求标头已被替换"}, "panels/network/NetworkDataGridNode.ts | sPreflight": {"message": "{PH1} + 预检"}, "panels/network/NetworkDataGridNode.ts | script": {"message": "脚本"}, "panels/network/NetworkDataGridNode.ts | selectPreflightRequest": {"message": "选择预定流程请求"}, "panels/network/NetworkDataGridNode.ts | selectTheRequestThatTriggered": {"message": "选择触发了此预定流程的请求"}, "panels/network/NetworkDataGridNode.ts | servedFromDiskCacheResourceSizeS": {"message": "通过磁盘缓存提供，资源大小：{PH1}"}, "panels/network/NetworkDataGridNode.ts | servedFromMemoryCacheResource": {"message": "由内存缓存提供，资源大小：{PH1}"}, "panels/network/NetworkDataGridNode.ts | servedFromPrefetchCacheResource": {"message": "由预提取缓存提供，资源大小：{PH1}"}, "panels/network/NetworkDataGridNode.ts | servedFromServiceWorkerResource": {"message": "由 ServiceWorker 提供，资源大小：{PH1}"}, "panels/network/NetworkDataGridNode.ts | servedFromSignedHttpExchange": {"message": "通过 Signed HTTP Exchange 提供，资源大小：{PH1}"}, "panels/network/NetworkDataGridNode.ts | servedFromWebBundle": {"message": "由 Web Bundle 提供，资源大小：{PH1}"}, "panels/network/NetworkDataGridNode.ts | serviceWorker": {"message": "(ServiceWorker)"}, "panels/network/NetworkDataGridNode.ts | signedexchange": {"message": "signed-exchange"}, "panels/network/NetworkDataGridNode.ts | thirdPartyPhaseout": {"message": "此请求的 Cookie 因第三方 Cookie 逐步淘汰机制而被屏蔽。前往“问题”标签页了解详情。"}, "panels/network/NetworkDataGridNode.ts | timeSubtitleTooltipText": {"message": "延迟时间（收到响应的时间 - 发起请求的时间）"}, "panels/network/NetworkDataGridNode.ts | unknown": {"message": "（未知）"}, "panels/network/NetworkDataGridNode.ts | unknownExplanation": {"message": "无法在此处显示该请求的状态，因为发出该请求的网页在传输该请求的过程中被卸载了。您可以使用 chrome://net-export 来捕获网络日志并查看所有请求详情。"}, "panels/network/NetworkDataGridNode.ts | webBundle": {"message": "(Web Bundle)"}, "panels/network/NetworkDataGridNode.ts | webBundleError": {"message": "Web Bundle 错误"}, "panels/network/NetworkDataGridNode.ts | webBundleInnerRequest": {"message": "由 Web Bundle 传回"}, "panels/network/NetworkItemView.ts | containsOverriddenHeaders": {"message": "此响应中包含被开发者工具替换的标头"}, "panels/network/NetworkItemView.ts | cookies": {"message": "<PERSON><PERSON>"}, "panels/network/NetworkItemView.ts | eventstream": {"message": "EventStream"}, "panels/network/NetworkItemView.ts | headers": {"message": "标头"}, "panels/network/NetworkItemView.ts | initiator": {"message": "启动器"}, "panels/network/NetworkItemView.ts | messages": {"message": "消息"}, "panels/network/NetworkItemView.ts | payload": {"message": "载荷"}, "panels/network/NetworkItemView.ts | preview": {"message": "预览"}, "panels/network/NetworkItemView.ts | rawResponseData": {"message": "原始响应数据"}, "panels/network/NetworkItemView.ts | requestAndResponseCookies": {"message": "请求和响应 Cookie"}, "panels/network/NetworkItemView.ts | requestAndResponseTimeline": {"message": "请求和响应时间轴"}, "panels/network/NetworkItemView.ts | requestInitiatorCallStack": {"message": "请求启动器调用堆栈"}, "panels/network/NetworkItemView.ts | response": {"message": "响应"}, "panels/network/NetworkItemView.ts | responseIsOverridden": {"message": "此响应已被开发者工具替换"}, "panels/network/NetworkItemView.ts | responsePreview": {"message": "响应预览"}, "panels/network/NetworkItemView.ts | signedexchangeError": {"message": "SignedExchange 错误"}, "panels/network/NetworkItemView.ts | thirdPartyPhaseout": {"message": "Cookie 因第三方 Cookie 逐步淘汰机制而被屏蔽。"}, "panels/network/NetworkItemView.ts | timing": {"message": "时间"}, "panels/network/NetworkItemView.ts | trustTokenOperationDetails": {"message": "私密状态令牌操作详情"}, "panels/network/NetworkItemView.ts | trustTokens": {"message": "私密状态令牌"}, "panels/network/NetworkItemView.ts | websocketMessages": {"message": "WebSocket 消息"}, "panels/network/NetworkLogView.ts | allStrings": {"message": "全部"}, "panels/network/NetworkLogView.ts | areYouSureYouWantToClearBrowser": {"message": "确定要清除浏览器缓存吗？"}, "panels/network/NetworkLogView.ts | areYouSureYouWantToClearBrowserCookies": {"message": "确定要清除浏览器 Cookie 吗？"}, "panels/network/NetworkLogView.ts | blockRequestDomain": {"message": "屏蔽请求网域"}, "panels/network/NetworkLogView.ts | blockRequestUrl": {"message": "屏蔽请求网址"}, "panels/network/NetworkLogView.ts | blockedRequests": {"message": "被屏蔽的请求"}, "panels/network/NetworkLogView.ts | chromeExtensions": {"message": "隐藏扩展程序网址"}, "panels/network/NetworkLogView.ts | clearBrowserCache": {"message": "清除浏览器缓存"}, "panels/network/NetworkLogView.ts | clearBrowserCookies": {"message": "清除浏览器 Cookie"}, "panels/network/NetworkLogView.ts | copy": {"message": "复制"}, "panels/network/NetworkLogView.ts | copyAllAsCurl": {"message": "以 cURL 格式复制所有内容"}, "panels/network/NetworkLogView.ts | copyAllAsCurlBash": {"message": "以 cURL (bash) 格式复制所有内容"}, "panels/network/NetworkLogView.ts | copyAllAsCurlCmd": {"message": "以 cURL (cmd) 格式复制所有内容"}, "panels/network/NetworkLogView.ts | copyAllAsFetch": {"message": "以 fetch 格式复制所有内容"}, "panels/network/NetworkLogView.ts | copyAllAsHar": {"message": "以 HAR 格式复制所有内容"}, "panels/network/NetworkLogView.ts | copyAllAsNodejsFetch": {"message": "以 fetch (Node.js) 格式复制所有内容"}, "panels/network/NetworkLogView.ts | copyAllAsPowershell": {"message": "以 PowerShell 格式复制所有内容"}, "panels/network/NetworkLogView.ts | copyAllURLs": {"message": "复制所有网址"}, "panels/network/NetworkLogView.ts | copyAsCurl": {"message": "以 cURL 格式复制"}, "panels/network/NetworkLogView.ts | copyAsCurlBash": {"message": "以 cURL (bash) 格式复制"}, "panels/network/NetworkLogView.ts | copyAsCurlCmd": {"message": "以 cURL (cmd) 格式复制"}, "panels/network/NetworkLogView.ts | copyAsFetch": {"message": "以 fetch 格式复制"}, "panels/network/NetworkLogView.ts | copyAsNodejsFetch": {"message": "以 fetch (Node.js) 格式复制"}, "panels/network/NetworkLogView.ts | copyAsPowershell": {"message": "以 PowerShell 格式复制"}, "panels/network/NetworkLogView.ts | copyRequestHeaders": {"message": "复制请求标头"}, "panels/network/NetworkLogView.ts | copyResponse": {"message": "复制响应"}, "panels/network/NetworkLogView.ts | copyResponseHeaders": {"message": "复制响应标头"}, "panels/network/NetworkLogView.ts | copyStacktrace": {"message": "复制堆栈轨迹"}, "panels/network/NetworkLogView.ts | copyURL": {"message": "复制网址"}, "panels/network/NetworkLogView.ts | domcontentloadedS": {"message": "DOMContentLoaded：{PH1}"}, "panels/network/NetworkLogView.ts | dropHarFilesHere": {"message": "将 HAR 文件拖放到此处"}, "panels/network/NetworkLogView.ts | finishS": {"message": "完成用时：{PH1}"}, "panels/network/NetworkLogView.ts | hasBlockedCookies": {"message": "被屏蔽的响应 Cookie"}, "panels/network/NetworkLogView.ts | hideChromeExtension": {"message": "隐藏“chrome-extension://”网址"}, "panels/network/NetworkLogView.ts | hideDataUrls": {"message": "隐藏数据网址"}, "panels/network/NetworkLogView.ts | hidesDataAndBlobUrls": {"message": "隐藏“data:”和“blob:”网址"}, "panels/network/NetworkLogView.ts | invertFilter": {"message": "反转"}, "panels/network/NetworkLogView.ts | invertsFilter": {"message": "反转搜索过滤器"}, "panels/network/NetworkLogView.ts | learnMore": {"message": "了解详情"}, "panels/network/NetworkLogView.ts | loadS": {"message": "加载时间：{PH1}"}, "panels/network/NetworkLogView.ts | moreFilters": {"message": "更多过滤条件"}, "panels/network/NetworkLogView.ts | networkDataAvailable": {"message": "网络数据可用"}, "panels/network/NetworkLogView.ts | onlyShowBlockedRequests": {"message": "仅显示已屏蔽的请求"}, "panels/network/NetworkLogView.ts | onlyShowRequestsWithBlockedCookies": {"message": "仅显示带有被屏蔽的响应 Cookie 的请求"}, "panels/network/NetworkLogView.ts | onlyShowThirdPartyRequests": {"message": "仅显示不是由页面源发出的请求"}, "panels/network/NetworkLogView.ts | overTwoTypesSelected": {"message": "{PH1}、{PH2}…"}, "panels/network/NetworkLogView.ts | overrideHeaders": {"message": "替换标头"}, "panels/network/NetworkLogView.ts | performARequestOrHitSToRecordThe": {"message": "执行某项请求或按 {PH1} 即可记录重新加载。"}, "panels/network/NetworkLogView.ts | recordToDisplayNetworkActivity": {"message": "如果想让当前界面显示网络活动，请开始记录网络日志 ({PH1})。"}, "panels/network/NetworkLogView.ts | recordingNetworkActivity": {"message": "正在录制网络活动…"}, "panels/network/NetworkLogView.ts | replayXhr": {"message": "重放 XHR"}, "panels/network/NetworkLogView.ts | requestTypes": {"message": "请求类型"}, "panels/network/NetworkLogView.ts | requestTypesToInclude": {"message": "要包含的请求类型"}, "panels/network/NetworkLogView.ts | requestTypesTooltip": {"message": "按类型过滤请求"}, "panels/network/NetworkLogView.ts | sBResourcesLoadedByThePage": {"message": "网页加载了 {PH1} B 的资源"}, "panels/network/NetworkLogView.ts | sBSBResourcesLoadedByThePage": {"message": "网页加载的资源大小为 {PH1} B，资源总大小为 {PH2} B"}, "panels/network/NetworkLogView.ts | sBSBTransferredOverNetwork": {"message": "已通过网络传输 {PH1} B（共 {PH2} B）"}, "panels/network/NetworkLogView.ts | sBTransferredOverNetwork": {"message": "已通过网络传输 {PH1} B"}, "panels/network/NetworkLogView.ts | sRequests": {"message": "{PH1} 个请求"}, "panels/network/NetworkLogView.ts | sResources": {"message": "{PH1} 项资源"}, "panels/network/NetworkLogView.ts | sSRequests": {"message": "第 {PH1} 项请求，共 {PH2} 项"}, "panels/network/NetworkLogView.ts | sSResources": {"message": "所选资源大小为 {PH1}，共 {PH2}"}, "panels/network/NetworkLogView.ts | sSTransferred": {"message": "已传输 {PH1}，共 {PH2}"}, "panels/network/NetworkLogView.ts | sTransferred": {"message": "已传输 {PH1}"}, "panels/network/NetworkLogView.ts | saveAllAsHarWithContent": {"message": "以 HAR 格式保存所有内容"}, "panels/network/NetworkLogView.ts | showOnly": {"message": "只显示 {PH1}"}, "panels/network/NetworkLogView.ts | showOnlyHideRequests": {"message": "仅显示/隐藏请求"}, "panels/network/NetworkLogView.ts | thirdParty": {"message": "第三方请求"}, "panels/network/NetworkLogView.ts | twoTypesSelected": {"message": "{PH1}、{PH2}"}, "panels/network/NetworkLogView.ts | unblockS": {"message": "取消屏蔽 {PH1}"}, "panels/network/NetworkLogViewColumns.ts | connectionId": {"message": "连接 ID"}, "panels/network/NetworkLogViewColumns.ts | content": {"message": "内容"}, "panels/network/NetworkLogViewColumns.ts | cookies": {"message": "<PERSON><PERSON>"}, "panels/network/NetworkLogViewColumns.ts | domain": {"message": "网域"}, "panels/network/NetworkLogViewColumns.ts | endTime": {"message": "结束时间"}, "panels/network/NetworkLogViewColumns.ts | hasOverrides": {"message": "包含替换项"}, "panels/network/NetworkLogViewColumns.ts | initiator": {"message": "启动器"}, "panels/network/NetworkLogViewColumns.ts | initiatorAddressSpace": {"message": "启动器地址空间"}, "panels/network/NetworkLogViewColumns.ts | latency": {"message": "延迟时间"}, "panels/network/NetworkLogViewColumns.ts | manageHeaderColumns": {"message": "管理标头列…"}, "panels/network/NetworkLogViewColumns.ts | method": {"message": "方法"}, "panels/network/NetworkLogViewColumns.ts | name": {"message": "名称"}, "panels/network/NetworkLogViewColumns.ts | networkLog": {"message": "网络日志"}, "panels/network/NetworkLogViewColumns.ts | path": {"message": "路径"}, "panels/network/NetworkLogViewColumns.ts | priority": {"message": "优先级"}, "panels/network/NetworkLogViewColumns.ts | protocol": {"message": "协议"}, "panels/network/NetworkLogViewColumns.ts | remoteAddress": {"message": "远程地址"}, "panels/network/NetworkLogViewColumns.ts | remoteAddressSpace": {"message": "远程地址空间"}, "panels/network/NetworkLogViewColumns.ts | responseHeaders": {"message": "响应标头"}, "panels/network/NetworkLogViewColumns.ts | responseTime": {"message": "响应时间"}, "panels/network/NetworkLogViewColumns.ts | scheme": {"message": "架构"}, "panels/network/NetworkLogViewColumns.ts | setCookies": {"message": "设置 <PERSON><PERSON>"}, "panels/network/NetworkLogViewColumns.ts | size": {"message": "大小"}, "panels/network/NetworkLogViewColumns.ts | startTime": {"message": "开始时间"}, "panels/network/NetworkLogViewColumns.ts | status": {"message": "状态"}, "panels/network/NetworkLogViewColumns.ts | text": {"message": "文本"}, "panels/network/NetworkLogViewColumns.ts | time": {"message": "时间"}, "panels/network/NetworkLogViewColumns.ts | totalDuration": {"message": "总时长"}, "panels/network/NetworkLogViewColumns.ts | type": {"message": "类型"}, "panels/network/NetworkLogViewColumns.ts | url": {"message": "网址"}, "panels/network/NetworkLogViewColumns.ts | waterfall": {"message": "瀑布"}, "panels/network/NetworkManageCustomHeadersView.ts | addCustomHeader": {"message": "添加自定义标头…"}, "panels/network/NetworkManageCustomHeadersView.ts | headerName": {"message": "标头名称"}, "panels/network/NetworkManageCustomHeadersView.ts | manageHeaderColumns": {"message": "管理标头列"}, "panels/network/NetworkManageCustomHeadersView.ts | noCustomHeaders": {"message": "无自定义标头"}, "panels/network/NetworkPanel.ts | captureScreenshots": {"message": "屏幕截图"}, "panels/network/NetworkPanel.ts | captureScreenshotsWhenLoadingA": {"message": "加载网页时截取屏幕截图"}, "panels/network/NetworkPanel.ts | close": {"message": "关闭"}, "panels/network/NetworkPanel.ts | disableCache": {"message": "停用缓存"}, "panels/network/NetworkPanel.ts | disableCacheWhileDevtoolsIsOpen": {"message": "停用缓存（在开发者工具已打开时）"}, "panels/network/NetworkPanel.ts | doNotClearLogOnPageReload": {"message": "网页重新加载/导航时不清除日志"}, "panels/network/NetworkPanel.ts | exportHar": {"message": "导出 HAR 文件…"}, "panels/network/NetworkPanel.ts | fetchingFrames": {"message": "正在提取框架…"}, "panels/network/NetworkPanel.ts | groupByFrame": {"message": "按框架分组"}, "panels/network/NetworkPanel.ts | groupRequestsByTopLevelRequest": {"message": "按顶级请求框架对请求分组"}, "panels/network/NetworkPanel.ts | hitSToReloadAndCaptureFilmstrip": {"message": "按 {PH1} 即可重新加载和截取幻灯影片。"}, "panels/network/NetworkPanel.ts | importHarFile": {"message": "导入 HAR 文件…"}, "panels/network/NetworkPanel.ts | moreNetworkConditions": {"message": "更多网络状况…"}, "panels/network/NetworkPanel.ts | networkSettings": {"message": "网络设置"}, "panels/network/NetworkPanel.ts | preserveLog": {"message": "保留日志"}, "panels/network/NetworkPanel.ts | recordingFrames": {"message": "正在录制框架…"}, "panels/network/NetworkPanel.ts | revealInNetworkPanel": {"message": "在“网络”面板中显示"}, "panels/network/NetworkPanel.ts | search": {"message": "搜索"}, "panels/network/NetworkPanel.ts | showMoreInformationInRequestRows": {"message": "在请求行中显示更多信息"}, "panels/network/NetworkPanel.ts | showOverview": {"message": "概览"}, "panels/network/NetworkPanel.ts | showOverviewOfNetworkRequests": {"message": "显示网络请求概览"}, "panels/network/NetworkPanel.ts | throttling": {"message": "节流"}, "panels/network/NetworkPanel.ts | useLargeRequestRows": {"message": "大请求行"}, "panels/network/NetworkSearchScope.ts | url": {"message": "网址"}, "panels/network/NetworkTimeCalculator.ts | sDownload": {"message": "下载速度为 {PH1}"}, "panels/network/NetworkTimeCalculator.ts | sFromCache": {"message": "{PH1}（来自缓存）"}, "panels/network/NetworkTimeCalculator.ts | sFromServiceworker": {"message": "{PH1}（来自 ServiceWorker）"}, "panels/network/NetworkTimeCalculator.ts | sLatency": {"message": "延迟时间：{PH1}"}, "panels/network/NetworkTimeCalculator.ts | sLatencySDownloadSTotal": {"message": "{PH1} 延迟时间，{PH2} 下载时间（总计 {PH3}）"}, "panels/network/RequestCookiesView.ts | cookiesThatWereReceivedFromThe": {"message": "通过响应的“set-cookie”标头从服务器接收的 Cookie"}, "panels/network/RequestCookiesView.ts | cookiesThatWereReceivedFromTheServer": {"message": "在响应的“set-cookie”标头中从服务器接收的格式不正确的 Cookie"}, "panels/network/RequestCookiesView.ts | cookiesThatWereSentToTheServerIn": {"message": "请求的“cookie”标头中已发送到服务器的 Cookie"}, "panels/network/RequestCookiesView.ts | learnMore": {"message": "了解详情"}, "panels/network/RequestCookiesView.ts | malformedResponseCookies": {"message": "格式不正确的响应 Cookie"}, "panels/network/RequestCookiesView.ts | noRequestCookiesWereSent": {"message": "未发送任何请求 <PERSON><PERSON>。"}, "panels/network/RequestCookiesView.ts | requestCookies": {"message": "请求 <PERSON><PERSON>"}, "panels/network/RequestCookiesView.ts | responseCookies": {"message": "响应 <PERSON><PERSON>"}, "panels/network/RequestCookiesView.ts | showFilteredOutRequestCookies": {"message": "显示滤除的请求 Cookie"}, "panels/network/RequestCookiesView.ts | siteHasCookieInOtherPartition": {"message": "此网站在另一个分区中有 Cookie，这些 Cookie 不是随该请求发送的。{PH1}"}, "panels/network/RequestCookiesView.ts | thisRequestHasNoCookies": {"message": "此请求不含任何 <PERSON><PERSON>。"}, "panels/network/RequestInitiatorView.ts | requestCallStack": {"message": "请求调用堆栈"}, "panels/network/RequestInitiatorView.ts | requestInitiatorChain": {"message": "请求启动器链"}, "panels/network/RequestInitiatorView.ts | thisRequestHasNoInitiatorData": {"message": "此请求没有任何启动器数据。"}, "panels/network/RequestPayloadView.ts | copyValue": {"message": "复制值"}, "panels/network/RequestPayloadView.ts | empty": {"message": "（空）"}, "panels/network/RequestPayloadView.ts | formData": {"message": "表单数据"}, "panels/network/RequestPayloadView.ts | queryStringParameters": {"message": "查询字符串参数"}, "panels/network/RequestPayloadView.ts | requestPayload": {"message": "请求载荷"}, "panels/network/RequestPayloadView.ts | showMore": {"message": "展开"}, "panels/network/RequestPayloadView.ts | unableToDecodeValue": {"message": "（无法解码值）"}, "panels/network/RequestPayloadView.ts | viewDecoded": {"message": "视图已解码"}, "panels/network/RequestPayloadView.ts | viewDecodedL": {"message": "视图已解码"}, "panels/network/RequestPayloadView.ts | viewParsed": {"message": "查看解析结果"}, "panels/network/RequestPayloadView.ts | viewParsedL": {"message": "查看已解析的结果"}, "panels/network/RequestPayloadView.ts | viewSource": {"message": "查看源代码"}, "panels/network/RequestPayloadView.ts | viewSourceL": {"message": "查看源代码"}, "panels/network/RequestPayloadView.ts | viewUrlEncoded": {"message": "查看网址编码格式的数据"}, "panels/network/RequestPayloadView.ts | viewUrlEncodedL": {"message": "查看网址编码格式的数据"}, "panels/network/RequestPreviewView.ts | failedToLoadResponseData": {"message": "无法加载响应数据"}, "panels/network/RequestPreviewView.ts | previewNotAvailable": {"message": "无法预览"}, "panels/network/RequestResponseView.ts | failedToLoadResponseData": {"message": "无法加载响应数据"}, "panels/network/RequestResponseView.ts | thisRequestHasNoResponseData": {"message": "此请求没有可用的响应数据。"}, "panels/network/RequestTimingView.ts | cacheStorageCacheNameS": {"message": "缓存空间缓存名称：{PH1}"}, "panels/network/RequestTimingView.ts | cacheStorageCacheNameUnknown": {"message": "缓存空间缓存名称：不明"}, "panels/network/RequestTimingView.ts | cautionRequestIsNotFinishedYet": {"message": "注意：尚未完成请求！"}, "panels/network/RequestTimingView.ts | connectionStart": {"message": "开始连接"}, "panels/network/RequestTimingView.ts | contentDownload": {"message": "下载内容"}, "panels/network/RequestTimingView.ts | dnsLookup": {"message": "DNS 查找"}, "panels/network/RequestTimingView.ts | duration": {"message": "时长"}, "panels/network/RequestTimingView.ts | durationC": {"message": "时长"}, "panels/network/RequestTimingView.ts | duringDevelopmentYouCanUseSToAdd": {"message": "在开发过程中，您可以使用 {PH1} 向此请求的服务器端时间信息添加数据洞见。"}, "panels/network/RequestTimingView.ts | explanation": {"message": "说明"}, "panels/network/RequestTimingView.ts | fallbackCode": {"message": "后备代码"}, "panels/network/RequestTimingView.ts | fromHttpCache": {"message": "来自 HTTP 缓存"}, "panels/network/RequestTimingView.ts | initialConnection": {"message": "初始连接"}, "panels/network/RequestTimingView.ts | label": {"message": "标签"}, "panels/network/RequestTimingView.ts | networkFetch": {"message": "网络提取"}, "panels/network/RequestTimingView.ts | originalRequest": {"message": "原始请求"}, "panels/network/RequestTimingView.ts | proxyNegotiation": {"message": "代理协商"}, "panels/network/RequestTimingView.ts | queuedAtS": {"message": "进入队列时间：{PH1}"}, "panels/network/RequestTimingView.ts | queueing": {"message": "正在排队"}, "panels/network/RequestTimingView.ts | readingPush": {"message": "读取 Push 消息"}, "panels/network/RequestTimingView.ts | receivingPush": {"message": "接收 Push 消息"}, "panels/network/RequestTimingView.ts | requestSent": {"message": "已发送请求"}, "panels/network/RequestTimingView.ts | requestToServiceworker": {"message": "向 ServiceWorker 发送的请求"}, "panels/network/RequestTimingView.ts | requestresponse": {"message": "请求/响应"}, "panels/network/RequestTimingView.ts | resourceScheduling": {"message": "资源调度"}, "panels/network/RequestTimingView.ts | respondwith": {"message": "respondWith"}, "panels/network/RequestTimingView.ts | responseReceived": {"message": "已收到响应"}, "panels/network/RequestTimingView.ts | retrievalTimeS": {"message": "检索时间：{PH1}"}, "panels/network/RequestTimingView.ts | serverPush": {"message": "服务器推送"}, "panels/network/RequestTimingView.ts | serverTiming": {"message": "服务器计时"}, "panels/network/RequestTimingView.ts | serviceworkerCacheStorage": {"message": "ServiceWorker 缓存存储空间"}, "panels/network/RequestTimingView.ts | sourceOfResponseS": {"message": "响应来源：{PH1}"}, "panels/network/RequestTimingView.ts | ssl": {"message": "SSL"}, "panels/network/RequestTimingView.ts | stalled": {"message": "已停止"}, "panels/network/RequestTimingView.ts | startedAtS": {"message": "开始时间：{PH1}"}, "panels/network/RequestTimingView.ts | startup": {"message": "启动"}, "panels/network/RequestTimingView.ts | theServerTimingApi": {"message": "Server Timing API"}, "panels/network/RequestTimingView.ts | time": {"message": "时间"}, "panels/network/RequestTimingView.ts | total": {"message": "总计"}, "panels/network/RequestTimingView.ts | unknown": {"message": "未知"}, "panels/network/RequestTimingView.ts | waitingTtfb": {"message": "正在等待服务器响应"}, "panels/network/RequestTimingView.ts | waterfall": {"message": "瀑布"}, "panels/network/ResourceWebSocketFrameView.ts | all": {"message": "全部"}, "panels/network/ResourceWebSocketFrameView.ts | binaryMessage": {"message": "二进制消息"}, "panels/network/ResourceWebSocketFrameView.ts | clearAll": {"message": "全部清除"}, "panels/network/ResourceWebSocketFrameView.ts | clearAllL": {"message": "全部清除"}, "panels/network/ResourceWebSocketFrameView.ts | connectionCloseMessage": {"message": "连接关闭消息"}, "panels/network/ResourceWebSocketFrameView.ts | continuationFrame": {"message": "延续框架"}, "panels/network/ResourceWebSocketFrameView.ts | copyMessage": {"message": "复制消息"}, "panels/network/ResourceWebSocketFrameView.ts | copyMessageD": {"message": "复制消息…"}, "panels/network/ResourceWebSocketFrameView.ts | data": {"message": "数据"}, "panels/network/ResourceWebSocketFrameView.ts | filter": {"message": "过滤"}, "panels/network/ResourceWebSocketFrameView.ts | filterUsingRegex": {"message": "使用正则表达式进行过滤，例如：(web)?socket"}, "panels/network/ResourceWebSocketFrameView.ts | length": {"message": "长度"}, "panels/network/ResourceWebSocketFrameView.ts | na": {"message": "不适用"}, "panels/network/ResourceWebSocketFrameView.ts | pingMessage": {"message": "<PERSON> 消息"}, "panels/network/ResourceWebSocketFrameView.ts | pongMessage": {"message": "Pong 消息"}, "panels/network/ResourceWebSocketFrameView.ts | receive": {"message": "接收"}, "panels/network/ResourceWebSocketFrameView.ts | sOpcodeS": {"message": "{PH1}（操作码 {PH2}）"}, "panels/network/ResourceWebSocketFrameView.ts | sOpcodeSMask": {"message": "{PH1}（操作码 {PH2}、掩码）"}, "panels/network/ResourceWebSocketFrameView.ts | selectMessageToBrowseItsContent": {"message": "选择消息以浏览其内容。"}, "panels/network/ResourceWebSocketFrameView.ts | send": {"message": "发送"}, "panels/network/ResourceWebSocketFrameView.ts | textMessage": {"message": "文本消息"}, "panels/network/ResourceWebSocketFrameView.ts | time": {"message": "时间"}, "panels/network/ResourceWebSocketFrameView.ts | webSocketFrame": {"message": "WebSocket 框架"}, "panels/network/SignedExchangeInfoView.ts | certificate": {"message": "证书"}, "panels/network/SignedExchangeInfoView.ts | certificateSha": {"message": "证书 SHA256"}, "panels/network/SignedExchangeInfoView.ts | certificateUrl": {"message": "证书网址"}, "panels/network/SignedExchangeInfoView.ts | date": {"message": "日期"}, "panels/network/SignedExchangeInfoView.ts | errors": {"message": "错误"}, "panels/network/SignedExchangeInfoView.ts | expires": {"message": "到期"}, "panels/network/SignedExchangeInfoView.ts | headerIntegrityHash": {"message": "标头完整性哈希"}, "panels/network/SignedExchangeInfoView.ts | integrity": {"message": "完整性"}, "panels/network/SignedExchangeInfoView.ts | issuer": {"message": "颁发者"}, "panels/network/SignedExchangeInfoView.ts | label": {"message": "标签"}, "panels/network/SignedExchangeInfoView.ts | learnmore": {"message": "了解详情"}, "panels/network/SignedExchangeInfoView.ts | requestUrl": {"message": "请求网址"}, "panels/network/SignedExchangeInfoView.ts | responseCode": {"message": "响应代码"}, "panels/network/SignedExchangeInfoView.ts | responseHeaders": {"message": "响应标头"}, "panels/network/SignedExchangeInfoView.ts | signature": {"message": "签名"}, "panels/network/SignedExchangeInfoView.ts | signedHttpExchange": {"message": "Signed HTTP Exchange"}, "panels/network/SignedExchangeInfoView.ts | subject": {"message": "主题"}, "panels/network/SignedExchangeInfoView.ts | validFrom": {"message": "生效时间："}, "panels/network/SignedExchangeInfoView.ts | validUntil": {"message": "截止日期："}, "panels/network/SignedExchangeInfoView.ts | validityUrl": {"message": "有效性网址"}, "panels/network/SignedExchangeInfoView.ts | viewCertificate": {"message": "查看证书"}, "panels/network/components/HeaderSectionRow.ts | activeClientExperimentVariation": {"message": "活跃的client experiment variation IDs。"}, "panels/network/components/HeaderSectionRow.ts | activeClientExperimentVariationIds": {"message": "触发服务器端行为的有效client experiment variation IDs。"}, "panels/network/components/HeaderSectionRow.ts | decoded": {"message": "已解码："}, "panels/network/components/HeaderSectionRow.ts | editHeader": {"message": "替换标头"}, "panels/network/components/HeaderSectionRow.ts | headerNamesOnlyLetters": {"message": "标头名称只能包含字母、数字、连字符或下划线"}, "panels/network/components/HeaderSectionRow.ts | learnMore": {"message": "了解详情"}, "panels/network/components/HeaderSectionRow.ts | learnMoreInTheIssuesTab": {"message": "前往“问题”标签页了解详情"}, "panels/network/components/HeaderSectionRow.ts | reloadPrompt": {"message": "刷新页面/请求即可使这些更改生效"}, "panels/network/components/HeaderSectionRow.ts | removeOverride": {"message": "移除此标头替换值"}, "panels/network/components/RequestHeaderSection.ts | learnMore": {"message": "了解详情"}, "panels/network/components/RequestHeaderSection.ts | onlyProvisionalHeadersAre": {"message": "仅预配标头可用，因为此请求并非通过网络发送，而是从本地缓存提供，其中不会存储原始请求标头。停用缓存即可查看完整请求标头。"}, "panels/network/components/RequestHeaderSection.ts | provisionalHeadersAreShown": {"message": "当前显示的是预配标头。"}, "panels/network/components/RequestHeaderSection.ts | provisionalHeadersAreShownDisableCache": {"message": "显示的是预配标头。停用缓存即可查看完整标头。"}, "panels/network/components/RequestHeadersView.ts | earlyHintsHeaders": {"message": "早期提示标头"}, "panels/network/components/RequestHeadersView.ts | fromDiskCache": {"message": "（来自磁盘缓存）"}, "panels/network/components/RequestHeadersView.ts | fromEarlyHints": {"message": "（来自早期提示）"}, "panels/network/components/RequestHeadersView.ts | fromMemoryCache": {"message": "（来自内存缓存）"}, "panels/network/components/RequestHeadersView.ts | fromPrefetchCache": {"message": "（来自预提取缓存）"}, "panels/network/components/RequestHeadersView.ts | fromServiceWorker": {"message": "（来自 service worker）"}, "panels/network/components/RequestHeadersView.ts | fromSignedexchange": {"message": "（来自 signed-exchange）"}, "panels/network/components/RequestHeadersView.ts | fromWebBundle": {"message": "（来自 Web Bundle）"}, "panels/network/components/RequestHeadersView.ts | general": {"message": "常规"}, "panels/network/components/RequestHeadersView.ts | raw": {"message": "原始"}, "panels/network/components/RequestHeadersView.ts | referrerPolicy": {"message": "引荐来源网址政策"}, "panels/network/components/RequestHeadersView.ts | remoteAddress": {"message": "远程地址"}, "panels/network/components/RequestHeadersView.ts | requestHeaders": {"message": "请求标头"}, "panels/network/components/RequestHeadersView.ts | requestMethod": {"message": "请求方法"}, "panels/network/components/RequestHeadersView.ts | requestUrl": {"message": "请求网址"}, "panels/network/components/RequestHeadersView.ts | responseHeaders": {"message": "响应标头"}, "panels/network/components/RequestHeadersView.ts | revealHeaderOverrides": {"message": "显示标头替换值定义"}, "panels/network/components/RequestHeadersView.ts | showMore": {"message": "展开"}, "panels/network/components/RequestHeadersView.ts | statusCode": {"message": "状态代码"}, "panels/network/components/RequestTrustTokensView.ts | aClientprovidedArgumentWas": {"message": "客户端提供的参数格式不正确或无效。"}, "panels/network/components/RequestTrustTokensView.ts | eitherNoInputsForThisOperation": {"message": "此操作没有任何可用输入，或者输出超过操作配额。"}, "panels/network/components/RequestTrustTokensView.ts | failure": {"message": "失败"}, "panels/network/components/RequestTrustTokensView.ts | issuer": {"message": "颁发者"}, "panels/network/components/RequestTrustTokensView.ts | issuers": {"message": "颁发者"}, "panels/network/components/RequestTrustTokensView.ts | numberOfIssuedTokens": {"message": "已颁发的令牌数量"}, "panels/network/components/RequestTrustTokensView.ts | parameters": {"message": "参数"}, "panels/network/components/RequestTrustTokensView.ts | refreshPolicy": {"message": "刷新政策"}, "panels/network/components/RequestTrustTokensView.ts | result": {"message": "结果"}, "panels/network/components/RequestTrustTokensView.ts | status": {"message": "状态"}, "panels/network/components/RequestTrustTokensView.ts | success": {"message": "成功"}, "panels/network/components/RequestTrustTokensView.ts | theKeysForThisPSTIssuerAreUnavailable": {"message": "此 PST 颁发者的密钥不可用。该颁发者可能需要通过 Chrome 注册流程进行注册。"}, "panels/network/components/RequestTrustTokensView.ts | theOperationFailedForAnUnknown": {"message": "由于未知原因，此操作失败。"}, "panels/network/components/RequestTrustTokensView.ts | theOperationWasFulfilledLocally": {"message": "操作在本地执行，未发送任何请求。"}, "panels/network/components/RequestTrustTokensView.ts | theOperationsResultWasServedFrom": {"message": "操作结果由缓存提供。"}, "panels/network/components/RequestTrustTokensView.ts | theServersResponseWasMalformedOr": {"message": "服务器响应格式不正确或无效。"}, "panels/network/components/RequestTrustTokensView.ts | topLevelOrigin": {"message": "顶级来源"}, "panels/network/components/RequestTrustTokensView.ts | type": {"message": "类型"}, "panels/network/components/ResponseHeaderSection.ts | addHeader": {"message": "添加标头"}, "panels/network/components/ResponseHeaderSection.ts | chooseThisOptionIfTheResourceAnd": {"message": "如果资源和文档由同一网站提供，请选择此选项。"}, "panels/network/components/ResponseHeaderSection.ts | onlyChooseThisOptionIfAn": {"message": "仅当包括此资源在内的任意网站不会带来安全风险时，才可选择此选项。"}, "panels/network/components/ResponseHeaderSection.ts | thisDocumentWasBlockedFrom": {"message": "此文档无法在由沙盒化 iframe 打开的弹出式窗口中加载，因为它指定了跨源 opener 政策。"}, "panels/network/components/ResponseHeaderSection.ts | toEmbedThisFrameInYourDocument": {"message": "如需在您的文档中嵌入此框架，则需要在响应中指定以下响应标头，以启用跨源嵌入器政策："}, "panels/network/components/ResponseHeaderSection.ts | toUseThisResourceFromADifferent": {"message": "为了从另一个来源使用此资源，服务器需要在响应标头中指定跨域资源政策："}, "panels/network/components/ResponseHeaderSection.ts | toUseThisResourceFromADifferentOrigin": {"message": "为了从另一个来源使用该资源，服务器可能会放宽跨域资源政策响应标头："}, "panels/network/components/ResponseHeaderSection.ts | toUseThisResourceFromADifferentSite": {"message": "为了从其他网站使用此资源，服务器可能会放宽跨域资源政策响应标头："}, "panels/network/components/WebBundleInfoView.ts | bundledResource": {"message": "捆绑的资源"}, "panels/network/network-meta.ts | addNetworkRequestBlockingPattern": {"message": "添加网络请求屏蔽模式"}, "panels/network/network-meta.ts | clear": {"message": "清除网络日志"}, "panels/network/network-meta.ts | colorCode": {"message": "颜色代码"}, "panels/network/network-meta.ts | colorCodeByResourceType": {"message": "按资源类型划分的颜色代码"}, "panels/network/network-meta.ts | colorcodeResourceTypes": {"message": "颜色代码资源类型"}, "panels/network/network-meta.ts | diskCache": {"message": "磁盘缓存"}, "panels/network/network-meta.ts | dontGroupNetworkLogItemsByFrame": {"message": "请勿按框架对网络日志内容分组"}, "panels/network/network-meta.ts | frame": {"message": "框架"}, "panels/network/network-meta.ts | group": {"message": "群组"}, "panels/network/network-meta.ts | groupNetworkLogByFrame": {"message": "按框架对网络日志分组"}, "panels/network/network-meta.ts | groupNetworkLogItemsByFrame": {"message": "按框架对网络日志内容进行分组"}, "panels/network/network-meta.ts | hideRequestDetails": {"message": "隐藏请求详情"}, "panels/network/network-meta.ts | netWork": {"message": "网络"}, "panels/network/network-meta.ts | network": {"message": "网络"}, "panels/network/network-meta.ts | networkConditions": {"message": "网络状况"}, "panels/network/network-meta.ts | networkRequestBlocking": {"message": "屏蔽网络请求"}, "panels/network/network-meta.ts | networkThrottling": {"message": "网络节流"}, "panels/network/network-meta.ts | recordNetworkLog": {"message": "录制网络日志"}, "panels/network/network-meta.ts | removeAllNetworkRequestBlockingPatterns": {"message": "移除所有网络请求屏蔽模式"}, "panels/network/network-meta.ts | resourceType": {"message": "资源类型"}, "panels/network/network-meta.ts | search": {"message": "搜索"}, "panels/network/network-meta.ts | showNetwork": {"message": "显示“网络”工具"}, "panels/network/network-meta.ts | showNetworkConditions": {"message": "显示“网络状况”"}, "panels/network/network-meta.ts | showNetworkRequestBlocking": {"message": "显示“网络请求屏蔽”"}, "panels/network/network-meta.ts | showSearch": {"message": "显示“搜索”工具"}, "panels/network/network-meta.ts | stopRecordingNetworkLog": {"message": "停止录制网络日志"}, "panels/network/network-meta.ts | useDefaultColors": {"message": "使用默认颜色"}, "panels/performance_monitor/PerformanceMonitor.ts | cpuUsage": {"message": "CPU 使用情况"}, "panels/performance_monitor/PerformanceMonitor.ts | documentFrames": {"message": "文档框架"}, "panels/performance_monitor/PerformanceMonitor.ts | documents": {"message": "文档"}, "panels/performance_monitor/PerformanceMonitor.ts | domNodes": {"message": "DOM 节点"}, "panels/performance_monitor/PerformanceMonitor.ts | graphsDisplayingARealtimeViewOf": {"message": "显示实时性能指标视图的图表"}, "panels/performance_monitor/PerformanceMonitor.ts | jsEventListeners": {"message": "JS 事件监听器"}, "panels/performance_monitor/PerformanceMonitor.ts | jsHeapSize": {"message": "JS 堆大小"}, "panels/performance_monitor/PerformanceMonitor.ts | layoutsSec": {"message": "布局个数/秒"}, "panels/performance_monitor/PerformanceMonitor.ts | paused": {"message": "已暂停"}, "panels/performance_monitor/PerformanceMonitor.ts | styleRecalcsSec": {"message": "样式重新计算次数/秒"}, "panels/performance_monitor/performance_monitor-meta.ts | activity": {"message": "活动"}, "panels/performance_monitor/performance_monitor-meta.ts | metrics": {"message": "指标"}, "panels/performance_monitor/performance_monitor-meta.ts | monitor": {"message": "监视器"}, "panels/performance_monitor/performance_monitor-meta.ts | performance": {"message": "性能"}, "panels/performance_monitor/performance_monitor-meta.ts | performanceMonitor": {"message": "性能监视器"}, "panels/performance_monitor/performance_monitor-meta.ts | showPerformanceMonitor": {"message": "显示“性能监视器”"}, "panels/performance_monitor/performance_monitor-meta.ts | systemMonitor": {"message": "系统监视器"}, "panels/profiler/HeapProfileView.ts | allocationSampling": {"message": "分配采样"}, "panels/profiler/HeapProfileView.ts | formatPercent": {"message": "{PH1}%"}, "panels/profiler/HeapProfileView.ts | heapProfilerIsRecording": {"message": "堆分析器正在记录"}, "panels/profiler/HeapProfileView.ts | itProvidesGoodApproximation": {"message": "此工具能非常可靠地估计分配情况，并按 JavaScript 执行堆栈细分结果。"}, "panels/profiler/HeapProfileView.ts | name": {"message": "名称"}, "panels/profiler/HeapProfileView.ts | profileD": {"message": "性能分析报告 {PH1}"}, "panels/profiler/HeapProfileView.ts | recordMemoryAllocations": {"message": "使用采样方法录制内存分配情况。"}, "panels/profiler/HeapProfileView.ts | recording": {"message": "正在录制…"}, "panels/profiler/HeapProfileView.ts | sBytes": {"message": "{PH1} 个字节"}, "panels/profiler/HeapProfileView.ts | samplingProfiles": {"message": "抽样分析"}, "panels/profiler/HeapProfileView.ts | selectedSizeS": {"message": "所选大小：{PH1}"}, "panels/profiler/HeapProfileView.ts | selfSize": {"message": "自身大小"}, "panels/profiler/HeapProfileView.ts | selfSizeBytes": {"message": "自身大小（以字节为单位）"}, "panels/profiler/HeapProfileView.ts | skb": {"message": "{PH1} kB"}, "panels/profiler/HeapProfileView.ts | startHeapProfiling": {"message": "开始堆分析"}, "panels/profiler/HeapProfileView.ts | stopHeapProfiling": {"message": "停止堆性能分析"}, "panels/profiler/HeapProfileView.ts | stopping": {"message": "正在停止…"}, "panels/profiler/HeapProfileView.ts | thisProfileTypeHasMinimal": {"message": "此性能分析类型的性能开销最低，可用于长时间运行的操作。"}, "panels/profiler/HeapProfileView.ts | totalSize": {"message": "总大小"}, "panels/profiler/HeapProfileView.ts | totalSizeBytes": {"message": "总大小（字节）"}, "panels/profiler/HeapProfileView.ts | url": {"message": "网址"}, "panels/profiler/HeapProfilerPanel.ts | revealInSummaryView": {"message": "在“摘要”视图中显示"}, "panels/profiler/HeapSnapshotDataGrids.ts | Deleted": {"message": "已删除 # 项"}, "panels/profiler/HeapSnapshotDataGrids.ts | Delta": {"message": "# 增量"}, "panels/profiler/HeapSnapshotDataGrids.ts | New": {"message": "新对象数"}, "panels/profiler/HeapSnapshotDataGrids.ts | allocSize": {"message": "分配大小"}, "panels/profiler/HeapSnapshotDataGrids.ts | allocation": {"message": "分配"}, "panels/profiler/HeapSnapshotDataGrids.ts | constructorString": {"message": "构造函数"}, "panels/profiler/HeapSnapshotDataGrids.ts | count": {"message": "计数"}, "panels/profiler/HeapSnapshotDataGrids.ts | distance": {"message": "距离"}, "panels/profiler/HeapSnapshotDataGrids.ts | distanceFromWindowObject": {"message": "与窗口对象的距离"}, "panels/profiler/HeapSnapshotDataGrids.ts | freedSize": {"message": "已释放的大小"}, "panels/profiler/HeapSnapshotDataGrids.ts | function": {"message": "函数"}, "panels/profiler/HeapSnapshotDataGrids.ts | heapSnapshotConstructors": {"message": "堆快照构造函数"}, "panels/profiler/HeapSnapshotDataGrids.ts | heapSnapshotDiff": {"message": "堆快照差异"}, "panels/profiler/HeapSnapshotDataGrids.ts | heapSnapshotRetainment": {"message": "堆快照保留"}, "panels/profiler/HeapSnapshotDataGrids.ts | liveCount": {"message": "实时计数"}, "panels/profiler/HeapSnapshotDataGrids.ts | liveSize": {"message": "实时大小"}, "panels/profiler/HeapSnapshotDataGrids.ts | object": {"message": "对象"}, "panels/profiler/HeapSnapshotDataGrids.ts | retainedSize": {"message": "保留的大小"}, "panels/profiler/HeapSnapshotDataGrids.ts | shallowSize": {"message": "浅层大小"}, "panels/profiler/HeapSnapshotDataGrids.ts | size": {"message": "大小"}, "panels/profiler/HeapSnapshotDataGrids.ts | sizeDelta": {"message": "大小增量"}, "panels/profiler/HeapSnapshotDataGrids.ts | sizeOfTheObjectItselfInBytes": {"message": "对象本身的大小（以字节为单位）"}, "panels/profiler/HeapSnapshotDataGrids.ts | sizeOfTheObjectPlusTheGraphIt": {"message": "对象加上其保留的图表的大小（以字节为单位）"}, "panels/profiler/HeapSnapshotGridNodes.ts | compiledCodeSummary": {"message": "V8 用于运行由 JavaScript 或 WebAssembly 定义的函数的内部数据。"}, "panels/profiler/HeapSnapshotGridNodes.ts | concatenatedStringSummary": {"message": "一个字符串，表示连接在一起的其他两个字符串的内容。"}, "panels/profiler/HeapSnapshotGridNodes.ts | contextSummary": {"message": "一个内部对象，其中包含来自 JavaScript 作用域的变量，在该作用域内创建的函数可能需要这些变量。"}, "panels/profiler/HeapSnapshotGridNodes.ts | descriptorArraySummary": {"message": "JavaScript 对象使用的属性名称的列表。"}, "panels/profiler/HeapSnapshotGridNodes.ts | detachedFromDomTree": {"message": "已从 DOM 树分离"}, "panels/profiler/HeapSnapshotGridNodes.ts | genericStringsTwoPlaceholders": {"message": "{PH1}、{PH2}"}, "panels/profiler/HeapSnapshotGridNodes.ts | ignoreThisRetainer": {"message": "忽略此保留器"}, "panels/profiler/HeapSnapshotGridNodes.ts | ignored": {"message": "已忽略"}, "panels/profiler/HeapSnapshotGridNodes.ts | inElement": {"message": "位于："}, "panels/profiler/HeapSnapshotGridNodes.ts | internalArray": {"message": "（内部数组）[]"}, "panels/profiler/HeapSnapshotGridNodes.ts | internalArraySummary": {"message": "类似于数组的内部数据结构（不是 JavaScript 数组）。"}, "panels/profiler/HeapSnapshotGridNodes.ts | internalNodeSummary": {"message": "由 V8 以外的组件分配的对象，例如 Blink 定义的 C++ 对象。"}, "panels/profiler/HeapSnapshotGridNodes.ts | mapSummary": {"message": "表示 JavaScript 对象（不是 JavaScript 映射）形状的内部对象。"}, "panels/profiler/HeapSnapshotGridNodes.ts | objectElementsSummary": {"message": "一个内部对象，用于将编入索引的属性（例如数组的内容）存储在 JavaScript 对象中。"}, "panels/profiler/HeapSnapshotGridNodes.ts | objectPropertiesSummary": {"message": "一个内部对象，用于将命名的属性存储在 JavaScript 对象中。"}, "panels/profiler/HeapSnapshotGridNodes.ts | previewIsNotAvailable": {"message": "无法预览"}, "panels/profiler/HeapSnapshotGridNodes.ts | revealInSummaryView": {"message": "在“摘要”视图中显示"}, "panels/profiler/HeapSnapshotGridNodes.ts | revealObjectSWithIdSInSummary": {"message": "在“摘要”视图中显示 ID 为 @{PH2} 的对象“{PH1}”"}, "panels/profiler/HeapSnapshotGridNodes.ts | slicedStringSummary": {"message": "一个字符串，表示另一个字符串中的部分字符。"}, "panels/profiler/HeapSnapshotGridNodes.ts | stopIgnoringThisRetainer": {"message": "停止忽略此保留器"}, "panels/profiler/HeapSnapshotGridNodes.ts | storeAsGlobalVariable": {"message": "存储为全局变量"}, "panels/profiler/HeapSnapshotGridNodes.ts | summary": {"message": "摘要"}, "panels/profiler/HeapSnapshotGridNodes.ts | userObjectReachableFromWindow": {"message": "可通过窗口访问的用户对象"}, "panels/profiler/HeapSnapshotProxy.ts | anErrorOccurredWhenACallToMethod": {"message": "请求调用“{PH1}”方法时出错"}, "panels/profiler/HeapSnapshotView.ts | AllocationTimelinesShowInstrumented": {"message": "分配时间轴显示了插桩的 JavaScript 内存分配随时间变化的情况。记录分析后，您可选择一个时间间隔，以查看已在其中分配且到录制结束时仍保持活跃状态的对象。使用此分析类型隔离内存泄漏。"}, "panels/profiler/HeapSnapshotView.ts | allObjects": {"message": "所有对象"}, "panels/profiler/HeapSnapshotView.ts | allocation": {"message": "分配"}, "panels/profiler/HeapSnapshotView.ts | allocationInstrumentationOn": {"message": "时间轴上的分配插桩"}, "panels/profiler/HeapSnapshotView.ts | allocationStack": {"message": "分配堆栈"}, "panels/profiler/HeapSnapshotView.ts | allocationTimelines": {"message": "分配时间轴"}, "panels/profiler/HeapSnapshotView.ts | baseSnapshot": {"message": "基础快照"}, "panels/profiler/HeapSnapshotView.ts | captureNumericValue": {"message": "在快照中添加数字值"}, "panels/profiler/HeapSnapshotView.ts | code": {"message": "代码"}, "panels/profiler/HeapSnapshotView.ts | comparison": {"message": "比较"}, "panels/profiler/HeapSnapshotView.ts | containment": {"message": "控制"}, "panels/profiler/HeapSnapshotView.ts | duplicatedStrings": {"message": "重复字符串"}, "panels/profiler/HeapSnapshotView.ts | exposeInternals": {"message": "公开内部设置（包括因实现而异的更多详情）"}, "panels/profiler/HeapSnapshotView.ts | filter": {"message": "过滤"}, "panels/profiler/HeapSnapshotView.ts | filterByClass": {"message": "按类过滤"}, "panels/profiler/HeapSnapshotView.ts | find": {"message": "查找"}, "panels/profiler/HeapSnapshotView.ts | heapMemoryUsage": {"message": "堆内存用量"}, "panels/profiler/HeapSnapshotView.ts | heapSnapshot": {"message": "堆快照"}, "panels/profiler/HeapSnapshotView.ts | heapSnapshotProfilesShowMemory": {"message": "堆快照性能分析会显示您网页的 JavaScript 对象和相关 DOM 节点中的内存分配情况。"}, "panels/profiler/HeapSnapshotView.ts | heapSnapshots": {"message": "堆快照"}, "panels/profiler/HeapSnapshotView.ts | jsArrays": {"message": "JS 数组"}, "panels/profiler/HeapSnapshotView.ts | liveObjects": {"message": "实时对象"}, "panels/profiler/HeapSnapshotView.ts | loading": {"message": "正在加载…"}, "panels/profiler/HeapSnapshotView.ts | objectsAllocatedBeforeS": {"message": "在{PH1} 之前分配的对象"}, "panels/profiler/HeapSnapshotView.ts | objectsAllocatedBetweenSAndS": {"message": "在{PH1} 和{PH2} 之间分配的对象"}, "panels/profiler/HeapSnapshotView.ts | objectsRetainedByConsole": {"message": "由开发者工具控制台保留的对象"}, "panels/profiler/HeapSnapshotView.ts | objectsRetainedByDetachedDomNodes": {"message": "由已分离的 DOM 节点保留的对象"}, "panels/profiler/HeapSnapshotView.ts | percentagePlaceholder": {"message": "{PH1}%"}, "panels/profiler/HeapSnapshotView.ts | perspective": {"message": "视角"}, "panels/profiler/HeapSnapshotView.ts | recordAllocationStacksExtra": {"message": "录制各项分配的堆栈轨迹（会产生额外的性能开销）"}, "panels/profiler/HeapSnapshotView.ts | recording": {"message": "正在录制…"}, "panels/profiler/HeapSnapshotView.ts | restoreIgnoredRetainers": {"message": "恢复被忽略的保留器"}, "panels/profiler/HeapSnapshotView.ts | retainers": {"message": "保留器"}, "panels/profiler/HeapSnapshotView.ts | savingD": {"message": "正在保存… {PH1}%"}, "panels/profiler/HeapSnapshotView.ts | selectedSizeS": {"message": "所选大小：{PH1}"}, "panels/profiler/HeapSnapshotView.ts | snapshotD": {"message": "快照 {PH1}"}, "panels/profiler/HeapSnapshotView.ts | snapshotting": {"message": "正在拍摄快照…"}, "panels/profiler/HeapSnapshotView.ts | stackWasNotRecordedForThisObject": {"message": "由于在开始录制这项性能分析之前已经分配了此对象，因此没有为此对象录制堆栈。"}, "panels/profiler/HeapSnapshotView.ts | startRecordingHeapProfile": {"message": "开始录制堆分析情况"}, "panels/profiler/HeapSnapshotView.ts | statistics": {"message": "统计信息"}, "panels/profiler/HeapSnapshotView.ts | stopRecordingHeapProfile": {"message": "停止录制堆性能分析报告"}, "panels/profiler/HeapSnapshotView.ts | strings": {"message": "字符串"}, "panels/profiler/HeapSnapshotView.ts | summary": {"message": "摘要"}, "panels/profiler/HeapSnapshotView.ts | systemObjects": {"message": "系统对象"}, "panels/profiler/HeapSnapshotView.ts | takeHeapSnapshot": {"message": "拍摄堆快照"}, "panels/profiler/HeapSnapshotView.ts | typedArrays": {"message": "类型化数组"}, "panels/profiler/IsolateSelector.ts | changeRate": {"message": "{PH1}/s"}, "panels/profiler/IsolateSelector.ts | decreasingBySPerSecond": {"message": "每秒降低 {PH1}"}, "panels/profiler/IsolateSelector.ts | empty": {"message": "（空）"}, "panels/profiler/IsolateSelector.ts | heapSizeChangeTrendOverTheLastS": {"message": "过去 {PH1} 分钟堆大小的变化趋势。"}, "panels/profiler/IsolateSelector.ts | heapSizeInUseByLiveJsObjects": {"message": "已发布的 JS 对象所使用的堆大小。"}, "panels/profiler/IsolateSelector.ts | increasingBySPerSecond": {"message": "正在按每秒 {PH1} 递增"}, "panels/profiler/IsolateSelector.ts | javascriptVmInstances": {"message": "JavaScript 虚拟机实例"}, "panels/profiler/IsolateSelector.ts | totalJsHeapSize": {"message": "JS 堆总大小"}, "panels/profiler/IsolateSelector.ts | totalPageJsHeapSizeAcrossAllVm": {"message": "所有虚拟机实例的网页 JS 堆总大小。"}, "panels/profiler/IsolateSelector.ts | totalPageJsHeapSizeChangeTrend": {"message": "过去 {PH1} 分钟总页面 JS 堆大小的变化趋势。"}, "panels/profiler/LiveHeapProfileView.ts | allocatedJsHeapSizeCurrentlyIn": {"message": "当前正在使用的已分配 JS 堆的大小"}, "panels/profiler/LiveHeapProfileView.ts | anonymousScriptS": {"message": "（匿名脚本 {PH1}）"}, "panels/profiler/LiveHeapProfileView.ts | heapProfile": {"message": "堆性能分析报告"}, "panels/profiler/LiveHeapProfileView.ts | jsHeap": {"message": "JS 堆"}, "panels/profiler/LiveHeapProfileView.ts | kb": {"message": "kB"}, "panels/profiler/LiveHeapProfileView.ts | numberOfVmsSharingTheSameScript": {"message": "共用同一脚本来源的虚拟机数量"}, "panels/profiler/LiveHeapProfileView.ts | scriptUrl": {"message": "脚本网址"}, "panels/profiler/LiveHeapProfileView.ts | urlOfTheScriptSource": {"message": "脚本来源的网址"}, "panels/profiler/LiveHeapProfileView.ts | vms": {"message": "虚拟机"}, "panels/profiler/ModuleUIStrings.ts | buildingAllocationStatistics": {"message": "正在建立分配统计信息…"}, "panels/profiler/ModuleUIStrings.ts | buildingDominatedNodes": {"message": "正在构建支配节点…"}, "panels/profiler/ModuleUIStrings.ts | buildingDominatorTree": {"message": "正在生成支配项树…"}, "panels/profiler/ModuleUIStrings.ts | buildingEdgeIndexes": {"message": "正在建立边缘索引…"}, "panels/profiler/ModuleUIStrings.ts | buildingLocations": {"message": "正在建立位置…"}, "panels/profiler/ModuleUIStrings.ts | buildingPostorderIndex": {"message": "正在构建后序索引…"}, "panels/profiler/ModuleUIStrings.ts | buildingRetainers": {"message": "正在构建保留器…"}, "panels/profiler/ModuleUIStrings.ts | calculatingDistances": {"message": "正在计算距离…"}, "panels/profiler/ModuleUIStrings.ts | calculatingNodeFlags": {"message": "正在计算节点标记…"}, "panels/profiler/ModuleUIStrings.ts | calculatingRetainedSizes": {"message": "正在计算保留的大小…"}, "panels/profiler/ModuleUIStrings.ts | calculatingSamples": {"message": "正在计算样本…"}, "panels/profiler/ModuleUIStrings.ts | calculatingShallowSizes": {"message": "正在计算浅层大小…"}, "panels/profiler/ModuleUIStrings.ts | calculatingStatistics": {"message": "正在计算统计值…"}, "panels/profiler/ModuleUIStrings.ts | done": {"message": "完成"}, "panels/profiler/ModuleUIStrings.ts | finishedProcessing": {"message": "处理已完成。"}, "panels/profiler/ModuleUIStrings.ts | loadingAllocationTracesD": {"message": "正在加载分配跟踪记录…{PH1}%"}, "panels/profiler/ModuleUIStrings.ts | loadingEdgesD": {"message": "正在加载边缘… {PH1}%"}, "panels/profiler/ModuleUIStrings.ts | loadingLocations": {"message": "正在加载位置…"}, "panels/profiler/ModuleUIStrings.ts | loadingNodesD": {"message": "正在加载节点… {PH1}%"}, "panels/profiler/ModuleUIStrings.ts | loadingSamples": {"message": "正在加载示例…"}, "panels/profiler/ModuleUIStrings.ts | loadingSnapshotInfo": {"message": "正在加载快照信息…"}, "panels/profiler/ModuleUIStrings.ts | loadingStrings": {"message": "正在加载字符串…"}, "panels/profiler/ModuleUIStrings.ts | parsingStrings": {"message": "正在解析字符串…"}, "panels/profiler/ModuleUIStrings.ts | processingSnapshot": {"message": "正在处理快照…"}, "panels/profiler/ModuleUIStrings.ts | propagatingDomState": {"message": "正在传播 DOM 状态…"}, "panels/profiler/ProfileDataGrid.ts | genericTextTwoPlaceholders": {"message": "{PH1}、{PH2}"}, "panels/profiler/ProfileDataGrid.ts | notOptimizedS": {"message": "未优化：{PH1}"}, "panels/profiler/ProfileLauncherView.ts | load": {"message": "加载个人资料"}, "panels/profiler/ProfileLauncherView.ts | selectJavascriptVmInstance": {"message": "选择 JavaScript 虚拟机实例"}, "panels/profiler/ProfileLauncherView.ts | selectProfilingType": {"message": "选择性能分析类型"}, "panels/profiler/ProfileLauncherView.ts | start": {"message": "开始"}, "panels/profiler/ProfileLauncherView.ts | stop": {"message": "停止"}, "panels/profiler/ProfileLauncherView.ts | takeSnapshot": {"message": "拍摄快照"}, "panels/profiler/ProfileSidebarTreeElement.ts | profileOptions": {"message": "个人资料选项"}, "panels/profiler/ProfileView.ts | chart": {"message": "图表"}, "panels/profiler/ProfileView.ts | excludeSelectedFunction": {"message": "排除所选函数"}, "panels/profiler/ProfileView.ts | failedToReadFile": {"message": "无法读取文件"}, "panels/profiler/ProfileView.ts | fileSReadErrorS": {"message": "文件“{PH1}”读取错误：{PH2}"}, "panels/profiler/ProfileView.ts | findByCostMsNameOrFile": {"message": "按所用时间（大于 50 毫秒）、名称或文件查找"}, "panels/profiler/ProfileView.ts | focusSelectedFunction": {"message": "聚焦所选函数"}, "panels/profiler/ProfileView.ts | function": {"message": "函数"}, "panels/profiler/ProfileView.ts | heavyBottomUp": {"message": "大量（自下而上）"}, "panels/profiler/ProfileView.ts | loaded": {"message": "已加载"}, "panels/profiler/ProfileView.ts | loading": {"message": "正在加载…"}, "panels/profiler/ProfileView.ts | loadingD": {"message": "正在加载…{PH1}%"}, "panels/profiler/ProfileView.ts | parsing": {"message": "正在解析…"}, "panels/profiler/ProfileView.ts | profile": {"message": "性能分析"}, "panels/profiler/ProfileView.ts | profileD": {"message": "性能分析报告 {PH1}"}, "panels/profiler/ProfileView.ts | profileViewMode": {"message": "性能分析报告视图模式"}, "panels/profiler/ProfileView.ts | profiler": {"message": "分析器"}, "panels/profiler/ProfileView.ts | restoreAllFunctions": {"message": "恢复所有函数"}, "panels/profiler/ProfileView.ts | treeTopDown": {"message": "树（自顶向下）"}, "panels/profiler/ProfilesPanel.ts | cantLoadFileSupportedFile": {"message": "无法加载文件。支持的文件扩展名：{PH1}。"}, "panels/profiler/ProfilesPanel.ts | cantLoadProfileWhileAnother": {"message": "无法加载这项分析，因为系统正在记录另一项分析"}, "panels/profiler/ProfilesPanel.ts | profileLoadingFailedS": {"message": "性能分析报告加载失败：{PH1}。"}, "panels/profiler/ProfilesPanel.ts | profiles": {"message": "性能分析"}, "panels/profiler/ProfilesPanel.ts | runD": {"message": "运行 {PH1}"}, "panels/profiler/profiler-meta.ts | clearAllProfiles": {"message": "清除所有性能分析数据"}, "panels/profiler/profiler-meta.ts | deleteProfile": {"message": "删除个人资料"}, "panels/profiler/profiler-meta.ts | liveHeapProfile": {"message": "实时堆性能分析报告"}, "panels/profiler/profiler-meta.ts | loadProfile": {"message": "加载性能分析报告…"}, "panels/profiler/profiler-meta.ts | memory": {"message": "内存"}, "panels/profiler/profiler-meta.ts | saveProfile": {"message": "保存性能分析报告…"}, "panels/profiler/profiler-meta.ts | showLiveHeapProfile": {"message": "显示实时堆分析"}, "panels/profiler/profiler-meta.ts | showMemory": {"message": "显示内存"}, "panels/profiler/profiler-meta.ts | startRecordingHeapAllocations": {"message": "开始录制堆分配量"}, "panels/profiler/profiler-meta.ts | startRecordingHeapAllocationsAndReload": {"message": "开始录制堆分配量，并重新加载网页"}, "panels/profiler/profiler-meta.ts | startStopRecording": {"message": "开始/停止录制"}, "panels/profiler/profiler-meta.ts | stopRecordingHeapAllocations": {"message": "停止记录堆分配"}, "panels/protocol_monitor/ProtocolMonitor.ts | CDPCommandEditorHidden": {"message": "已隐藏 CDP 命令编辑器"}, "panels/protocol_monitor/ProtocolMonitor.ts | CDPCommandEditorShown": {"message": "已显示 CDP 命令编辑器"}, "panels/protocol_monitor/ProtocolMonitor.ts | clearAll": {"message": "全部清除"}, "panels/protocol_monitor/ProtocolMonitor.ts | documentation": {"message": "文档"}, "panels/protocol_monitor/ProtocolMonitor.ts | editAndResend": {"message": "修改并重新发送"}, "panels/protocol_monitor/ProtocolMonitor.ts | elapsedTime": {"message": "用时"}, "panels/protocol_monitor/ProtocolMonitor.ts | filter": {"message": "过滤"}, "panels/protocol_monitor/ProtocolMonitor.ts | hideCDPCommandEditor": {"message": "隐藏 CDP 命令编辑器"}, "panels/protocol_monitor/ProtocolMonitor.ts | method": {"message": "方法"}, "panels/protocol_monitor/ProtocolMonitor.ts | noMessageSelected": {"message": "未选择任何消息"}, "panels/protocol_monitor/ProtocolMonitor.ts | record": {"message": "录制"}, "panels/protocol_monitor/ProtocolMonitor.ts | request": {"message": "请求"}, "panels/protocol_monitor/ProtocolMonitor.ts | response": {"message": "响应"}, "panels/protocol_monitor/ProtocolMonitor.ts | sMs": {"message": "{PH1} 毫秒"}, "panels/protocol_monitor/ProtocolMonitor.ts | save": {"message": "保存"}, "panels/protocol_monitor/ProtocolMonitor.ts | selectTarget": {"message": "选择一个目标"}, "panels/protocol_monitor/ProtocolMonitor.ts | sendRawCDPCommand": {"message": "发送原始 CDP 命令"}, "panels/protocol_monitor/ProtocolMonitor.ts | sendRawCDPCommandExplanation": {"message": "格式：'Domain.commandName' 表示不带参数的命令，也可将 '{\"command\":\"Domain.commandName\", \"parameters\": {...}}' 作为一个 JSON 对象表示带有参数的命令。系统还支持将 'cmd'/'method' 和 'args'/'params'/'arguments' 用作 JSON 对象的替代键。"}, "panels/protocol_monitor/ProtocolMonitor.ts | session": {"message": "会话"}, "panels/protocol_monitor/ProtocolMonitor.ts | showCDPCommandEditor": {"message": "显示 CDP 命令编辑器"}, "panels/protocol_monitor/ProtocolMonitor.ts | target": {"message": "目标"}, "panels/protocol_monitor/ProtocolMonitor.ts | timestamp": {"message": "时间戳"}, "panels/protocol_monitor/ProtocolMonitor.ts | type": {"message": "类型"}, "panels/protocol_monitor/components/JSONEditor.ts | addCustomProperty": {"message": "添加自定义属性"}, "panels/protocol_monitor/components/JSONEditor.ts | addParameter": {"message": "添加参数"}, "panels/protocol_monitor/components/JSONEditor.ts | deleteParameter": {"message": "删除参数"}, "panels/protocol_monitor/components/JSONEditor.ts | resetDefaultValue": {"message": "重置为默认值"}, "panels/protocol_monitor/components/Toolbar.ts | copyCommand": {"message": "复制命令"}, "panels/protocol_monitor/components/Toolbar.ts | sendCommandCmdEnter": {"message": "发送命令 - ⌘+Enter"}, "panels/protocol_monitor/components/Toolbar.ts | sendCommandCtrlEnter": {"message": "发送命令 - Ctrl+Enter"}, "panels/protocol_monitor/protocol_monitor-meta.ts | protocolMonitor": {"message": "协议监视器"}, "panels/protocol_monitor/protocol_monitor-meta.ts | showProtocolMonitor": {"message": "显示协议监视器"}, "panels/recorder/RecorderController.ts | continueReplay": {"message": "继续"}, "panels/recorder/RecorderController.ts | copyShortcut": {"message": "复制录制内容或所选步骤"}, "panels/recorder/RecorderController.ts | createRecording": {"message": "创建新录制"}, "panels/recorder/RecorderController.ts | deleteRecording": {"message": "删除录制内容"}, "panels/recorder/RecorderController.ts | export": {"message": "导出"}, "panels/recorder/RecorderController.ts | exportRecording": {"message": "导出"}, "panels/recorder/RecorderController.ts | exportViaExtensions": {"message": "通过扩展程序导出"}, "panels/recorder/RecorderController.ts | getExtensions": {"message": "获取扩展程序…"}, "panels/recorder/RecorderController.ts | importRecording": {"message": "导入录制内容"}, "panels/recorder/RecorderController.ts | noRecordings": {"message": "无录制内容"}, "panels/recorder/RecorderController.ts | numberOfRecordings": {"message": "项录制内容"}, "panels/recorder/RecorderController.ts | replayRecording": {"message": "重放录制的内容"}, "panels/recorder/RecorderController.ts | sendFeedback": {"message": "发送反馈"}, "panels/recorder/RecorderController.ts | startStopRecording": {"message": "开始/停止录制"}, "panels/recorder/RecorderController.ts | stepOverReplay": {"message": "执行 1 步"}, "panels/recorder/RecorderController.ts | toggleCode": {"message": "切换代码视图"}, "panels/recorder/components/CreateRecordingView.ts | cancelRecording": {"message": "取消录制"}, "panels/recorder/components/CreateRecordingView.ts | createRecording": {"message": "创建新录制"}, "panels/recorder/components/CreateRecordingView.ts | includeNecessarySelectors": {"message": "您必须选择 CSS、Pierce 或 XPath 作为您的选项之一。仅这些选择器保证会被记录，因为 ARIA 和文本选择器未必是独一无二的。"}, "panels/recorder/components/CreateRecordingView.ts | learnMore": {"message": "了解详情"}, "panels/recorder/components/CreateRecordingView.ts | recordingName": {"message": "录制内容名称"}, "panels/recorder/components/CreateRecordingView.ts | recordingNameIsRequired": {"message": "必须输入录制内容名称"}, "panels/recorder/components/CreateRecordingView.ts | selectorAttribute": {"message": "选择器属性"}, "panels/recorder/components/CreateRecordingView.ts | selectorTypeARIA": {"message": "ARIA"}, "panels/recorder/components/CreateRecordingView.ts | selectorTypeCSS": {"message": "CSS"}, "panels/recorder/components/CreateRecordingView.ts | selectorTypePierce": {"message": "<PERSON>"}, "panels/recorder/components/CreateRecordingView.ts | selectorTypeText": {"message": "文本"}, "panels/recorder/components/CreateRecordingView.ts | selectorTypeXPath": {"message": "XPath"}, "panels/recorder/components/CreateRecordingView.ts | selectorTypes": {"message": "记录时要使用的选择器类型"}, "panels/recorder/components/CreateRecordingView.ts | startRecording": {"message": "开始录制"}, "panels/recorder/components/ExtensionView.ts | closeView": {"message": "关闭"}, "panels/recorder/components/ExtensionView.ts | extension": {"message": "内容由某款浏览器扩展程序提供"}, "panels/recorder/components/RecordingListView.ts | createRecording": {"message": "创建新录制"}, "panels/recorder/components/RecordingListView.ts | deleteRecording": {"message": "删除录制内容"}, "panels/recorder/components/RecordingListView.ts | openRecording": {"message": "打开录制内容"}, "panels/recorder/components/RecordingListView.ts | playRecording": {"message": "播放录制内容"}, "panels/recorder/components/RecordingListView.ts | savedRecordings": {"message": "已保存的录制内容"}, "panels/recorder/components/RecordingView.ts | addAssertion": {"message": "添加断言"}, "panels/recorder/components/RecordingView.ts | cancelReplay": {"message": "取消重放"}, "panels/recorder/components/RecordingView.ts | default": {"message": "默认"}, "panels/recorder/components/RecordingView.ts | desktop": {"message": "桌面设备"}, "panels/recorder/components/RecordingView.ts | download": {"message": "下载速度：{value}"}, "panels/recorder/components/RecordingView.ts | editReplaySettings": {"message": "修改重放设置"}, "panels/recorder/components/RecordingView.ts | editTitle": {"message": "修改标题"}, "panels/recorder/components/RecordingView.ts | endRecording": {"message": "结束录制"}, "panels/recorder/components/RecordingView.ts | environment": {"message": "环境"}, "panels/recorder/components/RecordingView.ts | hideCode": {"message": "隐藏代码"}, "panels/recorder/components/RecordingView.ts | latency": {"message": "延迟时间：{value} 毫秒"}, "panels/recorder/components/RecordingView.ts | mobile": {"message": "移动设备"}, "panels/recorder/components/RecordingView.ts | network": {"message": "网络"}, "panels/recorder/components/RecordingView.ts | performancePanel": {"message": "性能面板"}, "panels/recorder/components/RecordingView.ts | recording": {"message": "正在录制…"}, "panels/recorder/components/RecordingView.ts | recordingIsBeingStopped": {"message": "正在停止录制…"}, "panels/recorder/components/RecordingView.ts | replaySettings": {"message": "重放设置"}, "panels/recorder/components/RecordingView.ts | requiredTitleError": {"message": "必须输入标题"}, "panels/recorder/components/RecordingView.ts | screenshotForSection": {"message": "此部分的屏幕截图"}, "panels/recorder/components/RecordingView.ts | showCode": {"message": "显示代码"}, "panels/recorder/components/RecordingView.ts | timeout": {"message": "超时时限：{value} 毫秒"}, "panels/recorder/components/RecordingView.ts | timeoutExplanation": {"message": "超时设置（以毫秒为单位）适用于重放录制内容时的每项操作。例如，如果 CSS 选择器标识的 DOM 元素未在指定超时时间内显示在网页上，重放会失败并返回错误。"}, "panels/recorder/components/RecordingView.ts | timeoutLabel": {"message": "超时时限"}, "panels/recorder/components/RecordingView.ts | upload": {"message": "上传速度：{value}"}, "panels/recorder/components/ReplaySection.ts | Replay": {"message": "重放"}, "panels/recorder/components/ReplaySection.ts | ReplayExtremelySlowButtonLabel": {"message": "速度极慢"}, "panels/recorder/components/ReplaySection.ts | ReplayExtremelySlowItemLabel": {"message": "极慢"}, "panels/recorder/components/ReplaySection.ts | ReplayNormalButtonLabel": {"message": "正常速度"}, "panels/recorder/components/ReplaySection.ts | ReplayNormalItemLabel": {"message": "正常（默认）"}, "panels/recorder/components/ReplaySection.ts | ReplaySlowButtonLabel": {"message": "慢速"}, "panels/recorder/components/ReplaySection.ts | ReplaySlowItemLabel": {"message": "慢"}, "panels/recorder/components/ReplaySection.ts | ReplayVerySlowButtonLabel": {"message": "速度很慢"}, "panels/recorder/components/ReplaySection.ts | ReplayVerySlowItemLabel": {"message": "非常慢"}, "panels/recorder/components/ReplaySection.ts | extensionGroup": {"message": "扩展程序"}, "panels/recorder/components/ReplaySection.ts | speedGroup": {"message": "速度"}, "panels/recorder/components/StartView.ts | createRecording": {"message": "创建新录制"}, "panels/recorder/components/StartView.ts | header": {"message": "衡量整个用户体验历程中的性能"}, "panels/recorder/components/StartView.ts | quickStart": {"message": "快速入门：了解开发者工具中新增的“记录器”面板"}, "panels/recorder/components/StartView.ts | step1": {"message": "录制您的网站或应用中常见的用户体验历程"}, "panels/recorder/components/StartView.ts | step2": {"message": "重放录制内容以检查流程能否正常运行"}, "panels/recorder/components/StartView.ts | step3": {"message": "生成详细的性能跟踪记录或者导出 Puppeteer 脚本以用于测试"}, "panels/recorder/components/StepEditor.ts | addAttribute": {"message": "添加{attributeName}"}, "panels/recorder/components/StepEditor.ts | addFrameIndex": {"message": "在框架树内添加框架索引"}, "panels/recorder/components/StepEditor.ts | addSelector": {"message": "添加选择器"}, "panels/recorder/components/StepEditor.ts | addSelectorPart": {"message": "添加选择器的一个部分"}, "panels/recorder/components/StepEditor.ts | deleteRow": {"message": "删除行"}, "panels/recorder/components/StepEditor.ts | notSaved": {"message": "未保存：{error}"}, "panels/recorder/components/StepEditor.ts | removeFrameIndex": {"message": "移除框架索引"}, "panels/recorder/components/StepEditor.ts | removeSelector": {"message": "移除选择器"}, "panels/recorder/components/StepEditor.ts | removeSelectorPart": {"message": "移除选择器的一个部分"}, "panels/recorder/components/StepEditor.ts | selectorPicker": {"message": "选择网页中的某个元素即可更新选择器"}, "panels/recorder/components/StepEditor.ts | unknownActionType": {"message": "操作类型不明。"}, "panels/recorder/components/StepView.ts | addBreakpoint": {"message": "添加断点"}, "panels/recorder/components/StepView.ts | addStepAfter": {"message": "在此后添加步骤"}, "panels/recorder/components/StepView.ts | addStepBefore": {"message": "在此前添加步骤"}, "panels/recorder/components/StepView.ts | breakpoints": {"message": "断点"}, "panels/recorder/components/StepView.ts | changeStepTitle": {"message": "更改"}, "panels/recorder/components/StepView.ts | clickStepTitle": {"message": "点击"}, "panels/recorder/components/StepView.ts | closeStepTitle": {"message": "关闭"}, "panels/recorder/components/StepView.ts | copyAs": {"message": "复制为以下格式"}, "panels/recorder/components/StepView.ts | customStepTitle": {"message": "自定义步骤"}, "panels/recorder/components/StepView.ts | doubleClickStepTitle": {"message": "双击"}, "panels/recorder/components/StepView.ts | elementRoleButton": {"message": "按钮"}, "panels/recorder/components/StepView.ts | elementRoleFallback": {"message": "元素"}, "panels/recorder/components/StepView.ts | elementRoleInput": {"message": "输入"}, "panels/recorder/components/StepView.ts | emulateNetworkConditionsStepTitle": {"message": "模拟网络状况"}, "panels/recorder/components/StepView.ts | hoverStepTitle": {"message": "悬停"}, "panels/recorder/components/StepView.ts | keyDownStepTitle": {"message": "按下按键"}, "panels/recorder/components/StepView.ts | keyUpStepTitle": {"message": "释放按键"}, "panels/recorder/components/StepView.ts | navigateStepTitle": {"message": "导航"}, "panels/recorder/components/StepView.ts | openStepActions": {"message": "打开步骤操作"}, "panels/recorder/components/StepView.ts | removeBreakpoint": {"message": "移除断点"}, "panels/recorder/components/StepView.ts | removeStep": {"message": "移除步骤"}, "panels/recorder/components/StepView.ts | scrollStepTitle": {"message": "滚动"}, "panels/recorder/components/StepView.ts | setViewportClickTitle": {"message": "设置视口"}, "panels/recorder/components/StepView.ts | stepManagement": {"message": "管理步骤"}, "panels/recorder/components/StepView.ts | waitForElementStepTitle": {"message": "等待元素"}, "panels/recorder/components/StepView.ts | waitForExpressionStepTitle": {"message": "等待表达式"}, "panels/recorder/models/RecorderSettings.ts | defaultRecordingName": {"message": "{DATE} {TIME} 录制的内容"}, "panels/recorder/recorder-meta.ts | createRecording": {"message": "创建新录制"}, "panels/recorder/recorder-meta.ts | recorder": {"message": "记录器"}, "panels/recorder/recorder-meta.ts | replayRecording": {"message": "重放录制的内容"}, "panels/recorder/recorder-meta.ts | showRecorder": {"message": "显示记录器"}, "panels/recorder/recorder-meta.ts | startStopRecording": {"message": "开始/停止录制"}, "panels/recorder/recorder-meta.ts | toggleCode": {"message": "切换代码视图"}, "panels/screencast/ScreencastApp.ts | toggleScreencast": {"message": "开启/关闭抓屏功能"}, "panels/screencast/ScreencastView.ts | addressBar": {"message": "地址栏"}, "panels/screencast/ScreencastView.ts | back": {"message": "返回"}, "panels/screencast/ScreencastView.ts | forward": {"message": "前进"}, "panels/screencast/ScreencastView.ts | mouseInput": {"message": "使用鼠标"}, "panels/screencast/ScreencastView.ts | profilingInProgress": {"message": "正在进行性能分析"}, "panels/screencast/ScreencastView.ts | reload": {"message": "重新加载"}, "panels/screencast/ScreencastView.ts | screencastViewOfDebugTarget": {"message": "调试目标的抓屏视图"}, "panels/screencast/ScreencastView.ts | theTabIsInactive": {"message": "此标签页不活跃"}, "panels/screencast/ScreencastView.ts | touchInput": {"message": "使用触控功能"}, "panels/search/SearchResultsPane.ts | lineS": {"message": "第 {PH1} 行"}, "panels/search/SearchResultsPane.ts | matchesCountS": {"message": "匹配项计数 {PH1}"}, "panels/search/SearchResultsPane.ts | showDMore": {"message": "再显示 {PH1} 项"}, "panels/search/SearchView.ts | clear": {"message": "清除搜索内容"}, "panels/search/SearchView.ts | clearInput": {"message": "清除"}, "panels/search/SearchView.ts | disableCaseSensitive": {"message": "停用区分大小写的搜索功能"}, "panels/search/SearchView.ts | disableRegularExpression": {"message": "停用正则表达式"}, "panels/search/SearchView.ts | enableCaseSensitive": {"message": "启用区分大小写的搜索功能"}, "panels/search/SearchView.ts | enableRegularExpression": {"message": "启用正则表达式"}, "panels/search/SearchView.ts | find": {"message": "查找"}, "panels/search/SearchView.ts | foundDMatchingLinesInDFiles": {"message": "在 {PH2} 个文件中找到 {PH1} 个匹配行。"}, "panels/search/SearchView.ts | foundDMatchingLinesInFile": {"message": "在 1 个文件中找到了 {PH1} 个匹配行。"}, "panels/search/SearchView.ts | foundMatchingLineInFile": {"message": "在 1 个文件中找到了 1 个匹配行。"}, "panels/search/SearchView.ts | indexing": {"message": "正在编入索引…"}, "panels/search/SearchView.ts | indexingInterrupted": {"message": "索引编制已中断。"}, "panels/search/SearchView.ts | noMatchesFound": {"message": "未找到匹配项。"}, "panels/search/SearchView.ts | refresh": {"message": "刷新"}, "panels/search/SearchView.ts | searchFinished": {"message": "搜索已完成。"}, "panels/search/SearchView.ts | searchInterrupted": {"message": "搜索已中断。"}, "panels/search/SearchView.ts | searching": {"message": "正在搜索…"}, "panels/security/SecurityModel.ts | cipherWithMAC": {"message": "包含 {PH2} 的 {PH1}"}, "panels/security/SecurityModel.ts | keyExchangeWithGroup": {"message": "密钥交换分组模式为 {PH2} 的 {PH1}"}, "panels/security/SecurityModel.ts | theSecurityOfThisPageIsUnknown": {"message": "此网页的安全性不明。"}, "panels/security/SecurityModel.ts | thisPageIsNotSecure": {"message": "这是一个不安全的网页。"}, "panels/security/SecurityModel.ts | thisPageIsNotSecureBrokenHttps": {"message": "这是一个不安全的网页（HTTPS 已遭破坏）。"}, "panels/security/SecurityModel.ts | thisPageIsSecureValidHttps": {"message": "这是一个安全的网页（HTTPS 有效）。"}, "panels/security/SecurityPanel.ts | activeContentWithCertificate": {"message": "包含证书错误的有效内容"}, "panels/security/SecurityPanel.ts | activeMixedContent": {"message": "主动型混合内容"}, "panels/security/SecurityPanel.ts | allResourcesOnThisPageAreServed": {"message": "此网页上的所有资源均以安全方式提供。"}, "panels/security/SecurityPanel.ts | allServedSecurely": {"message": "所有资源均以安全方式提供"}, "panels/security/SecurityPanel.ts | blockedMixedContent": {"message": "被屏蔽的混合内容"}, "panels/security/SecurityPanel.ts | certificate": {"message": "证书"}, "panels/security/SecurityPanel.ts | certificateExpiresSoon": {"message": "证书即将过期"}, "panels/security/SecurityPanel.ts | certificateTransparency": {"message": "证书透明度"}, "panels/security/SecurityPanel.ts | chromeHasDeterminedThatThisSiteS": {"message": "Chrome 已确定此网站可能是虚假网站或欺诈性网站。"}, "panels/security/SecurityPanel.ts | cipher": {"message": "加密算法"}, "panels/security/SecurityPanel.ts | connection": {"message": "网络连接"}, "panels/security/SecurityPanel.ts | contentWithCertificateErrors": {"message": "具有证书错误的内容"}, "panels/security/SecurityPanel.ts | enabled": {"message": "已启用"}, "panels/security/SecurityPanel.ts | encryptedClientHello": {"message": "已加密的 ClientHello"}, "panels/security/SecurityPanel.ts | flaggedByGoogleSafeBrowsing": {"message": "已被 Google 安全浏览标记"}, "panels/security/SecurityPanel.ts | hashAlgorithm": {"message": "哈希算法"}, "panels/security/SecurityPanel.ts | hideFullDetails": {"message": "隐藏完整的详细信息"}, "panels/security/SecurityPanel.ts | ifYouBelieveThisIsShownIn": {"message": "如果您认为系统不该向您显示这条提示，请访问 https://g.co/chrome/lookalike-warnings。"}, "panels/security/SecurityPanel.ts | ifYouBelieveThisIsShownInErrorSafety": {"message": "如果您认为系统不该向您显示这条提示，请访问 https://g.co/chrome/lookalike-warnings。"}, "panels/security/SecurityPanel.ts | info": {"message": "信息"}, "panels/security/SecurityPanel.ts | insecureSha": {"message": "不安全 (SHA-1)"}, "panels/security/SecurityPanel.ts | issuedAt": {"message": "颁发时间"}, "panels/security/SecurityPanel.ts | issuer": {"message": "颁发者"}, "panels/security/SecurityPanel.ts | keyExchange": {"message": "密钥交换"}, "panels/security/SecurityPanel.ts | logId": {"message": "日志 ID"}, "panels/security/SecurityPanel.ts | logName": {"message": "日志名称"}, "panels/security/SecurityPanel.ts | mainOrigin": {"message": "主要来源"}, "panels/security/SecurityPanel.ts | mainOriginNonsecure": {"message": "主要来源（非安全来源）"}, "panels/security/SecurityPanel.ts | mainOriginSecure": {"message": "主要来源（安全）"}, "panels/security/SecurityPanel.ts | missing": {"message": "缺失"}, "panels/security/SecurityPanel.ts | mixedContent": {"message": "混合内容"}, "panels/security/SecurityPanel.ts | na": {"message": "（不适用）"}, "panels/security/SecurityPanel.ts | noSecurityDetailsAreAvailableFor": {"message": "没有此来源的任何安全详情。"}, "panels/security/SecurityPanel.ts | noSecurityInformation": {"message": "无安全信息"}, "panels/security/SecurityPanel.ts | nonsecureForm": {"message": "不安全的表单"}, "panels/security/SecurityPanel.ts | nonsecureOrigins": {"message": "不安全的来源"}, "panels/security/SecurityPanel.ts | notSecure": {"message": "不安全"}, "panels/security/SecurityPanel.ts | notSecureBroken": {"message": "不安全（已损坏）"}, "panels/security/SecurityPanel.ts | obsoleteConnectionSettings": {"message": "过时的连接设置"}, "panels/security/SecurityPanel.ts | openFullCertificateDetails": {"message": "打开完整的证书详情"}, "panels/security/SecurityPanel.ts | origin": {"message": "来源"}, "panels/security/SecurityPanel.ts | overview": {"message": "概览"}, "panels/security/SecurityPanel.ts | possibleSpoofingUrl": {"message": "可能是仿冒网址"}, "panels/security/SecurityPanel.ts | protocol": {"message": "协议"}, "panels/security/SecurityPanel.ts | publickeypinningBypassed": {"message": "已绕过 Public-Key-Pinning"}, "panels/security/SecurityPanel.ts | publickeypinningWasBypassedByA": {"message": "本地根证书绕过了 Public-Key-Pinning。"}, "panels/security/SecurityPanel.ts | reloadThePageToRecordRequestsFor": {"message": "重新加载网页，以录制针对 HTTP 资源的请求。"}, "panels/security/SecurityPanel.ts | reloadToViewDetails": {"message": "重新加载即可查看详情"}, "panels/security/SecurityPanel.ts | resources": {"message": "资源"}, "panels/security/SecurityPanel.ts | rsaKeyExchangeIsObsoleteEnableAn": {"message": "RSA 密钥交换已过时。请启用基于 ECDHE 的加密套件。"}, "panels/security/SecurityPanel.ts | sIsObsoleteEnableAnAesgcmbased": {"message": "{PH1} 已过时。请启用基于 AES-GCM 的加密套件。"}, "panels/security/SecurityPanel.ts | sIsObsoleteEnableTlsOrLater": {"message": "{PH1} 已过时。请启用 TLS 1.2 或更高版本。"}, "panels/security/SecurityPanel.ts | sct": {"message": "SCT"}, "panels/security/SecurityPanel.ts | secure": {"message": "安全"}, "panels/security/SecurityPanel.ts | secureConnectionSettings": {"message": "安全连接设置"}, "panels/security/SecurityPanel.ts | secureOrigins": {"message": "安全的来源"}, "panels/security/SecurityPanel.ts | securityOverview": {"message": "安全性概览"}, "panels/security/SecurityPanel.ts | serverSignature": {"message": "服务器签名"}, "panels/security/SecurityPanel.ts | showFullDetails": {"message": "显示完整详情"}, "panels/security/SecurityPanel.ts | showLess": {"message": "收起"}, "panels/security/SecurityPanel.ts | showMoreSTotal": {"message": "展开（共 {PH1} 个）"}, "panels/security/SecurityPanel.ts | signatureAlgorithm": {"message": "签名算法"}, "panels/security/SecurityPanel.ts | signatureData": {"message": "签名数据"}, "panels/security/SecurityPanel.ts | source": {"message": "来源"}, "panels/security/SecurityPanel.ts | subject": {"message": "主题"}, "panels/security/SecurityPanel.ts | subjectAlternativeNameMissing": {"message": "缺少 Subject Alternative Name"}, "panels/security/SecurityPanel.ts | theCertificateChainForThisSite": {"message": "此网站的证书链包含使用 SHA-1 签署的证书。"}, "panels/security/SecurityPanel.ts | theCertificateForThisSiteDoesNot": {"message": "此网站的证书不包含任何内含域名或 IP 地址的 Subject Alternative Name 扩展项。"}, "panels/security/SecurityPanel.ts | theCertificateForThisSiteExpires": {"message": "此网站的证书将在 48 小时内过期，因此需要更新。"}, "panels/security/SecurityPanel.ts | theConnectionToThisSiteIs": {"message": "与此网站的连接已使用 {PH1}、{PH2} 和 {PH3} 进行加密和身份验证。"}, "panels/security/SecurityPanel.ts | theConnectionToThisSiteIsUsingA": {"message": "与此网站的连接使用的是 {PH1} 颁发的可信且有效的服务器证书。"}, "panels/security/SecurityPanel.ts | theSecurityDetailsAboveAreFrom": {"message": "上方的安全详情来自首个已检查的响应。"}, "panels/security/SecurityPanel.ts | theServerSignatureUsesShaWhichIs": {"message": "服务器签名使用的是过时的 SHA-1。请启用 SHA-2 签名算法。（请注意，这不同于证书中的签名。）"}, "panels/security/SecurityPanel.ts | thisIsAnErrorPage": {"message": "这是一个错误网页。"}, "panels/security/SecurityPanel.ts | thisOriginIsANonhttpsSecure": {"message": "此来源是非 HTTPS 安全来源。"}, "panels/security/SecurityPanel.ts | thisPageHasANonhttpsSecureOrigin": {"message": "此网页拥有非 HTTPS 来源。"}, "panels/security/SecurityPanel.ts | thisPageIncludesAFormWithA": {"message": "该网页中有一个包含不安全的“action”属性的表单。"}, "panels/security/SecurityPanel.ts | thisPageIncludesHttpResources": {"message": "此网页包含 HTTP 资源。"}, "panels/security/SecurityPanel.ts | thisPageIncludesResourcesThat": {"message": "此网页包含加载后出现证书错误的资源。"}, "panels/security/SecurityPanel.ts | thisPageIsDangerousFlaggedBy": {"message": "这是一个危险网页（已被 Google 安全浏览功能做了标记）。"}, "panels/security/SecurityPanel.ts | thisPageIsInsecureUnencrypted": {"message": "这是一个不安全的网页（未加密的 HTTP）。"}, "panels/security/SecurityPanel.ts | thisPageIsSuspicious": {"message": "此网页可疑"}, "panels/security/SecurityPanel.ts | thisPageIsSuspiciousFlaggedBy": {"message": "此网页可疑（已被 Chrome 标记）。"}, "panels/security/SecurityPanel.ts | thisRequestCompliesWithChromes": {"message": "此请求符合 Chrome 的证书透明度政策。"}, "panels/security/SecurityPanel.ts | thisRequestDoesNotComplyWith": {"message": "此请求不符合 Chrome 的证书透明度政策。"}, "panels/security/SecurityPanel.ts | thisResponseWasLoadedFromCache": {"message": "此响应是从缓存加载的。可能缺少一些安全性详细信息。"}, "panels/security/SecurityPanel.ts | thisSiteIsMissingAValidTrusted": {"message": "此网站缺少可信且有效的证书 ({PH1})。"}, "panels/security/SecurityPanel.ts | thisSitesHostnameLooksSimilarToP": {"message": "此网站的主机名看起来和 {PH1} 很相似。攻击者有时会通过对域名进行一些细微变更来仿冒网站，这样的变更通常很难被发现。"}, "panels/security/SecurityPanel.ts | toCheckThisPagesStatusVisit": {"message": "如需查看此网页的状态，请访问 g.co/safebrowsingstatus。"}, "panels/security/SecurityPanel.ts | unknownCanceled": {"message": "未知/已取消"}, "panels/security/SecurityPanel.ts | unknownField": {"message": "未知"}, "panels/security/SecurityPanel.ts | validAndTrusted": {"message": "有效且可信"}, "panels/security/SecurityPanel.ts | validFrom": {"message": "生效时间："}, "panels/security/SecurityPanel.ts | validUntil": {"message": "截止日期："}, "panels/security/SecurityPanel.ts | validationStatus": {"message": "验证状态"}, "panels/security/SecurityPanel.ts | viewCertificate": {"message": "查看证书"}, "panels/security/SecurityPanel.ts | viewDRequestsInNetworkPanel": {"message": "{n,plural, =1{查看“网络”面板中的 # 个请求}other{查看“网络”面板中的 # 个请求}}"}, "panels/security/SecurityPanel.ts | viewRequestsInNetworkPanel": {"message": "在“网络”面板中查看请求"}, "panels/security/SecurityPanel.ts | youHaveRecentlyAllowedContent": {"message": "您最近已允许在此网站上运行已加载但有证书错误的内容（如脚本或 iframe）。"}, "panels/security/SecurityPanel.ts | youHaveRecentlyAllowedNonsecure": {"message": "您最近曾允许在此网站上运行非安全内容（如脚本或 iframe）。"}, "panels/security/SecurityPanel.ts | yourConnectionToThisOriginIsNot": {"message": "您与此来源之间的连接不安全。"}, "panels/security/SecurityPanel.ts | yourPageRequestedNonsecure": {"message": "您的网页请求了被屏蔽的非安全资源。"}, "panels/security/security-meta.ts | security": {"message": "安全"}, "panels/security/security-meta.ts | showSecurity": {"message": "显示“安全”面板"}, "panels/sensors/LocationsSettingsTab.ts | addLocation": {"message": "添加位置…"}, "panels/sensors/LocationsSettingsTab.ts | customLocations": {"message": "自定义位置"}, "panels/sensors/LocationsSettingsTab.ts | lat": {"message": "纬度"}, "panels/sensors/LocationsSettingsTab.ts | latitude": {"message": "纬度"}, "panels/sensors/LocationsSettingsTab.ts | latitudeMustBeANumber": {"message": "纬度必须是数字"}, "panels/sensors/LocationsSettingsTab.ts | latitudeMustBeGreaterThanOrEqual": {"message": "纬度必须大于或等于 {PH1}"}, "panels/sensors/LocationsSettingsTab.ts | latitudeMustBeLessThanOrEqualToS": {"message": "纬度必须小于或等于 {PH1}"}, "panels/sensors/LocationsSettingsTab.ts | locale": {"message": "语言区域"}, "panels/sensors/LocationsSettingsTab.ts | localeMustContainAlphabetic": {"message": "语言区域必须包含字母字符"}, "panels/sensors/LocationsSettingsTab.ts | locationName": {"message": "位置名称"}, "panels/sensors/LocationsSettingsTab.ts | locationNameCannotBeEmpty": {"message": "位置名称不得为空"}, "panels/sensors/LocationsSettingsTab.ts | locationNameMustBeLessThanS": {"message": "位置名称必须少于 {PH1} 个字符"}, "panels/sensors/LocationsSettingsTab.ts | long": {"message": "经度"}, "panels/sensors/LocationsSettingsTab.ts | longitude": {"message": "经度"}, "panels/sensors/LocationsSettingsTab.ts | longitudeMustBeANumber": {"message": "经度必须是数字"}, "panels/sensors/LocationsSettingsTab.ts | longitudeMustBeGreaterThanOr": {"message": "经度必须大于或等于 {PH1}"}, "panels/sensors/LocationsSettingsTab.ts | longitudeMustBeLessThanOrEqualTo": {"message": "经度必须小于或等于 {PH1}"}, "panels/sensors/LocationsSettingsTab.ts | timezoneId": {"message": "时区 ID"}, "panels/sensors/LocationsSettingsTab.ts | timezoneIdMustContainAlphabetic": {"message": "时区 ID 必须包含字母字符"}, "panels/sensors/SensorsView.ts | adjustWithMousewheelOrUpdownKeys": {"message": "使用鼠标滚轮或向上/向下键进行调整。{PH1}：±10、Shift：±1、Alt：±0.01"}, "panels/sensors/SensorsView.ts | alpha": {"message": "α (Alpha)"}, "panels/sensors/SensorsView.ts | beta": {"message": "β (Beta)"}, "panels/sensors/SensorsView.ts | customOrientation": {"message": "自定义屏幕方向"}, "panels/sensors/SensorsView.ts | deviceOrientationSetToAlphaSBeta": {"message": "设备屏幕方向已设为 Alpha：{PH1}，Beta：{PH2}，Gamma：{PH3}"}, "panels/sensors/SensorsView.ts | displayDown": {"message": "屏幕向下"}, "panels/sensors/SensorsView.ts | displayUp": {"message": "屏幕向上"}, "panels/sensors/SensorsView.ts | enableOrientationToRotate": {"message": "允许旋转屏幕方向"}, "panels/sensors/SensorsView.ts | error": {"message": "错误"}, "panels/sensors/SensorsView.ts | forcesSelectedIdleStateEmulation": {"message": "强制模拟所选空闲状态"}, "panels/sensors/SensorsView.ts | forcesTouchInsteadOfClick": {"message": "只能轻触，不能点击"}, "panels/sensors/SensorsView.ts | gamma": {"message": "γ (Gamma)"}, "panels/sensors/SensorsView.ts | landscapeLeft": {"message": "横向（向左）"}, "panels/sensors/SensorsView.ts | landscapeRight": {"message": "横向（向右）"}, "panels/sensors/SensorsView.ts | latitude": {"message": "纬度"}, "panels/sensors/SensorsView.ts | locale": {"message": "语言区域"}, "panels/sensors/SensorsView.ts | location": {"message": "位置"}, "panels/sensors/SensorsView.ts | locationUnavailable": {"message": "无法获取位置信息"}, "panels/sensors/SensorsView.ts | longitude": {"message": "经度"}, "panels/sensors/SensorsView.ts | manage": {"message": "管理"}, "panels/sensors/SensorsView.ts | manageTheListOfLocations": {"message": "管理位置列表"}, "panels/sensors/SensorsView.ts | noOverride": {"message": "禁止替换"}, "panels/sensors/SensorsView.ts | off": {"message": "关闭"}, "panels/sensors/SensorsView.ts | orientation": {"message": "屏幕方向"}, "panels/sensors/SensorsView.ts | other": {"message": "其他…"}, "panels/sensors/SensorsView.ts | overrides": {"message": "替换"}, "panels/sensors/SensorsView.ts | portrait": {"message": "纵向"}, "panels/sensors/SensorsView.ts | portraitUpsideDown": {"message": "纵向（上下颠倒）"}, "panels/sensors/SensorsView.ts | presets": {"message": "预设"}, "panels/sensors/SensorsView.ts | reset": {"message": "重置"}, "panels/sensors/SensorsView.ts | resetDeviceOrientation": {"message": "重置设备屏幕方向"}, "panels/sensors/SensorsView.ts | shiftdragHorizontallyToRotate": {"message": "按 Shift 键并横向拖动即可绕 y 轴旋转"}, "panels/sensors/SensorsView.ts | timezoneId": {"message": "时区 ID"}, "panels/sensors/sensors-meta.ts | accelerometer": {"message": "加速度计"}, "panels/sensors/sensors-meta.ts | deviceOrientation": {"message": "设备屏幕方向"}, "panels/sensors/sensors-meta.ts | devicebased": {"message": "基于设备"}, "panels/sensors/sensors-meta.ts | emulateIdleDetectorState": {"message": "模拟空闲检测器状态"}, "panels/sensors/sensors-meta.ts | forceEnabled": {"message": "已强制启用"}, "panels/sensors/sensors-meta.ts | geolocation": {"message": "地理定位"}, "panels/sensors/sensors-meta.ts | locale": {"message": "语言区域"}, "panels/sensors/sensors-meta.ts | locales": {"message": "语言区域"}, "panels/sensors/sensors-meta.ts | locations": {"message": "位置"}, "panels/sensors/sensors-meta.ts | noIdleEmulation": {"message": "无空闲模拟"}, "panels/sensors/sensors-meta.ts | sensors": {"message": "传感器"}, "panels/sensors/sensors-meta.ts | showLocations": {"message": "显示位置"}, "panels/sensors/sensors-meta.ts | showSensors": {"message": "显示“传感器”工具"}, "panels/sensors/sensors-meta.ts | timezones": {"message": "时区"}, "panels/sensors/sensors-meta.ts | touch": {"message": "轻触"}, "panels/sensors/sensors-meta.ts | userActiveScreenLocked": {"message": "用户处于活跃状态，屏幕已锁定"}, "panels/sensors/sensors-meta.ts | userActiveScreenUnlocked": {"message": "用户处于活跃状态，屏幕已解锁"}, "panels/sensors/sensors-meta.ts | userIdleScreenLocked": {"message": "用户处于空闲状态，屏幕已锁定"}, "panels/sensors/sensors-meta.ts | userIdleScreenUnlocked": {"message": "用户处于空闲状态，屏幕已解锁"}, "panels/settings/FrameworkIgnoreListSettingsTab.ts | addFilenamePattern": {"message": "添加文件名模式"}, "panels/settings/FrameworkIgnoreListSettingsTab.ts | addPattern": {"message": "添加格式…"}, "panels/settings/FrameworkIgnoreListSettingsTab.ts | automaticallyIgnoreListKnownThirdPartyScripts": {"message": "来源映射中的已知第三方脚本"}, "panels/settings/FrameworkIgnoreListSettingsTab.ts | customExclusionRules": {"message": "自定义排除规则："}, "panels/settings/FrameworkIgnoreListSettingsTab.ts | debuggerWillSkipThroughThe": {"message": "调试程序将快速浏览脚本，且不会在遇到脚本抛出的异常时停止。"}, "panels/settings/FrameworkIgnoreListSettingsTab.ts | enableIgnoreListing": {"message": "启用忽略清单"}, "panels/settings/FrameworkIgnoreListSettingsTab.ts | enableIgnoreListingTooltip": {"message": "取消选中即可停用所有忽略清单"}, "panels/settings/FrameworkIgnoreListSettingsTab.ts | frameworkIgnoreList": {"message": "框架忽略列表"}, "panels/settings/FrameworkIgnoreListSettingsTab.ts | generalExclusionRules": {"message": "一般排除规则："}, "panels/settings/FrameworkIgnoreListSettingsTab.ts | ignoreListContentScripts": {"message": "由扩展程序注入的内容脚本"}, "panels/settings/FrameworkIgnoreListSettingsTab.ts | ignoreScriptsWhoseNamesMatchS": {"message": "忽略名称与“{PH1}”匹配的脚本"}, "panels/settings/FrameworkIgnoreListSettingsTab.ts | learnMore": {"message": "了解详情"}, "panels/settings/FrameworkIgnoreListSettingsTab.ts | pattern": {"message": "添加模式"}, "panels/settings/FrameworkIgnoreListSettingsTab.ts | patternAlreadyExists": {"message": "模式已存在"}, "panels/settings/FrameworkIgnoreListSettingsTab.ts | patternCannotBeEmpty": {"message": "模式不能为空"}, "panels/settings/FrameworkIgnoreListSettingsTab.ts | patternMustBeAValidRegular": {"message": "模式必须是有效的正则表达式"}, "panels/settings/KeybindsSettingsTab.ts | FullListOfDevtoolsKeyboard": {"message": "DevTools 键盘快捷键和手势的完整列表"}, "panels/settings/KeybindsSettingsTab.ts | ResetShortcutsForAction": {"message": "重置操作的快捷键"}, "panels/settings/KeybindsSettingsTab.ts | RestoreDefaultShortcuts": {"message": "恢复默认快捷键"}, "panels/settings/KeybindsSettingsTab.ts | addAShortcut": {"message": "添加快捷方式"}, "panels/settings/KeybindsSettingsTab.ts | confirmChanges": {"message": "确认更改"}, "panels/settings/KeybindsSettingsTab.ts | discardChanges": {"message": "舍弃更改"}, "panels/settings/KeybindsSettingsTab.ts | editShortcut": {"message": "修改快捷方式"}, "panels/settings/KeybindsSettingsTab.ts | keyboardShortcutsList": {"message": "键盘快捷键列表"}, "panels/settings/KeybindsSettingsTab.ts | matchShortcutsFromPreset": {"message": "与预设中的快捷键匹配"}, "panels/settings/KeybindsSettingsTab.ts | noShortcutForAction": {"message": "操作未设置快捷键"}, "panels/settings/KeybindsSettingsTab.ts | removeShortcut": {"message": "移除快捷键"}, "panels/settings/KeybindsSettingsTab.ts | shortcutChangesApplied": {"message": "已应用对快捷方式所做的更改"}, "panels/settings/KeybindsSettingsTab.ts | shortcutChangesDiscared": {"message": "已舍弃对快捷方式所做的更改"}, "panels/settings/KeybindsSettingsTab.ts | shortcutChangesRestored": {"message": "对快捷方式所做的更改已恢复为默认值"}, "panels/settings/KeybindsSettingsTab.ts | shortcutModified": {"message": "快捷方式已修改"}, "panels/settings/KeybindsSettingsTab.ts | shortcutRemoved": {"message": "已移除{PH1}快捷方式"}, "panels/settings/KeybindsSettingsTab.ts | shortcuts": {"message": "快捷键"}, "panels/settings/KeybindsSettingsTab.ts | shortcutsCannotContainOnly": {"message": "快捷键不能只包含辅助键。"}, "panels/settings/KeybindsSettingsTab.ts | thisShortcutIsInUseByS": {"message": "{PH1}正在使用此快捷键：{PH2}。"}, "panels/settings/SettingsScreen.ts | experiments": {"message": "实验"}, "panels/settings/SettingsScreen.ts | filterExperimentsLabel": {"message": "过滤"}, "panels/settings/SettingsScreen.ts | learnMore": {"message": "了解详情"}, "panels/settings/SettingsScreen.ts | noResults": {"message": "没有符合过滤条件的实验"}, "panels/settings/SettingsScreen.ts | oneOrMoreSettingsHaveChanged": {"message": "一项或多项设置已更改，需要重新加载才能生效。"}, "panels/settings/SettingsScreen.ts | preferences": {"message": "偏好设置"}, "panels/settings/SettingsScreen.ts | restoreDefaultsAndReload": {"message": "恢复默认值并重新加载"}, "panels/settings/SettingsScreen.ts | sendFeedback": {"message": "发送反馈"}, "panels/settings/SettingsScreen.ts | settings": {"message": "设置"}, "panels/settings/SettingsScreen.ts | shortcuts": {"message": "快捷键"}, "panels/settings/SettingsScreen.ts | theseExperimentsAreParticularly": {"message": "这些实验尤其不稳定。您需要自行承担启用后所产生的风险。"}, "panels/settings/SettingsScreen.ts | theseExperimentsCouldBeUnstable": {"message": "这些实验可能不稳定或不可靠，因此可能需要您重启 DevTools。"}, "panels/settings/SettingsScreen.ts | warning": {"message": "警告："}, "panels/settings/components/SyncSection.ts | preferencesSyncDisabled": {"message": "若要开启此设置，您必须先在 Chrome 中启用设置同步。"}, "panels/settings/components/SyncSection.ts | settings": {"message": "转到“设置”"}, "panels/settings/components/SyncSection.ts | signedIn": {"message": "登录 Chrome 时使用的账号："}, "panels/settings/components/SyncSection.ts | syncDisabled": {"message": "若要开启此设置，您必须先启用 Chrome 同步。"}, "panels/settings/emulation/DevicesSettingsTab.ts | addCustomDevice": {"message": "添加自定义设备…"}, "panels/settings/emulation/DevicesSettingsTab.ts | device": {"message": "设备"}, "panels/settings/emulation/DevicesSettingsTab.ts | deviceAddedOrUpdated": {"message": "已成功添加/更新设备“{PH1}”。"}, "panels/settings/emulation/DevicesSettingsTab.ts | deviceName": {"message": "设备名称"}, "panels/settings/emulation/DevicesSettingsTab.ts | deviceNameCannotBeEmpty": {"message": "设备名称不得为空。"}, "panels/settings/emulation/DevicesSettingsTab.ts | deviceNameMustBeLessThanS": {"message": "设备名称必须少于 {PH1} 个字符。"}, "panels/settings/emulation/DevicesSettingsTab.ts | devicePixelRatio": {"message": "设备像素比"}, "panels/settings/emulation/DevicesSettingsTab.ts | emulatedDevices": {"message": "模拟的设备"}, "panels/settings/emulation/DevicesSettingsTab.ts | height": {"message": "高度"}, "panels/settings/emulation/DevicesSettingsTab.ts | userAgentString": {"message": "用户代理字符串"}, "panels/settings/emulation/DevicesSettingsTab.ts | userAgentType": {"message": "用户代理类型"}, "panels/settings/emulation/DevicesSettingsTab.ts | width": {"message": "宽度"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | addBrand": {"message": "添加品牌"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | addedBrand": {"message": "已添加品牌行"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | architecture": {"message": "架构 (Sec-CH-UA-Arch)"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | architecturePlaceholder": {"message": "架构（例如 x86）"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | brandFullVersionListDelete": {"message": "从完整版本列表中删除品牌"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | brandName": {"message": "品牌"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | brandNameAriaLabel": {"message": "品牌 {PH1}"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | brandProperties": {"message": "用户代理属性"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | brandUserAgentDelete": {"message": "从“用户代理”部分中删除品牌"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | brandVersionAriaLabel": {"message": "版本 {PH1}"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | brandVersionPlaceholder": {"message": "版本（例如 87.0.4280.88）"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | deletedBrand": {"message": "已删除品牌行"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | deviceModel": {"message": "设备型号 (Sec-CH-UA-Model)"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | deviceProperties": {"message": "设备属性"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | fullBrowserVersion": {"message": "完整的浏览器版本 (Sec-CH-UA-Full-Browser-Version)"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | fullBrowserVersionPlaceholder": {"message": "完整的浏览器版本号（例如 87.0.4280.88）"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | fullVersionList": {"message": "完整版本列表 (Sec-CH-UA-Full-Version-List)"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | learnMore": {"message": "了解详情"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | mobileCheckboxLabel": {"message": "手机"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | notRepresentable": {"message": "无法显示为结构化标头字符串。"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | platformLabel": {"message": "平台 (Sec-CH-UA-Platform/Sec-CH-UA-Platform-Version)"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | platformPlaceholder": {"message": "平台（例如 Android）"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | platformProperties": {"message": "平台属性"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | platformVersion": {"message": "平台版本"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | significantBrandVersionPlaceholder": {"message": "重大版本（例如 87）"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | title": {"message": "用户代理客户端提示"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | update": {"message": "更新"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | userAgentClientHintsInfo": {"message": "用户代理客户端提示是用户代理字符串的替代文字，能以结构更完善的方式识别浏览器和设备，同时提供更好的隐私保护。"}, "panels/settings/emulation/components/UserAgentClientHintsForm.ts | useragent": {"message": "用户代理 (Sec-CH-UA)"}, "panels/settings/emulation/emulation-meta.ts | devices": {"message": "设备"}, "panels/settings/emulation/emulation-meta.ts | showDevices": {"message": "显示设备"}, "panels/settings/settings-meta.ts | documentation": {"message": "文档"}, "panels/settings/settings-meta.ts | experiments": {"message": "实验"}, "panels/settings/settings-meta.ts | ignoreList": {"message": "忽略列表"}, "panels/settings/settings-meta.ts | preferences": {"message": "偏好设置"}, "panels/settings/settings-meta.ts | settings": {"message": "设置"}, "panels/settings/settings-meta.ts | shortcuts": {"message": "快捷键"}, "panels/settings/settings-meta.ts | showExperiments": {"message": "显示实验"}, "panels/settings/settings-meta.ts | showIgnoreList": {"message": "显示“忽略列表”"}, "panels/settings/settings-meta.ts | showPreferences": {"message": "显示偏好设置"}, "panels/settings/settings-meta.ts | showShortcuts": {"message": "显示快捷键"}, "panels/snippets/ScriptSnippetFileSystem.ts | linkedTo": {"message": "已链接到 {PH1}"}, "panels/snippets/ScriptSnippetFileSystem.ts | scriptSnippet": {"message": "脚本代码段 #{PH1}"}, "panels/snippets/SnippetsQuickOpen.ts | noSnippetsFound": {"message": "找不到任何代码段。"}, "panels/snippets/SnippetsQuickOpen.ts | run": {"message": "运行"}, "panels/snippets/SnippetsQuickOpen.ts | snippet": {"message": "代码段"}, "panels/sources/AddSourceMapURLDialog.ts | add": {"message": "添加"}, "panels/sources/AddSourceMapURLDialog.ts | debugInfoUrl": {"message": "DWARF 符号网址： "}, "panels/sources/AddSourceMapURLDialog.ts | sourceMapUrl": {"message": "来源映射网址："}, "panels/sources/BreakpointEditDialog.ts | breakpoint": {"message": "断点"}, "panels/sources/BreakpointEditDialog.ts | breakpointType": {"message": "断点类型"}, "panels/sources/BreakpointEditDialog.ts | closeDialog": {"message": "关闭修改对话框并保存更改"}, "panels/sources/BreakpointEditDialog.ts | conditionalBreakpoint": {"message": "条件断点"}, "panels/sources/BreakpointEditDialog.ts | expressionToCheckBeforePausingEg": {"message": "在暂停之前要检查的表达式，例如 x > 5"}, "panels/sources/BreakpointEditDialog.ts | learnMoreOnBreakpointTypes": {"message": "了解详情：断点类型"}, "panels/sources/BreakpointEditDialog.ts | logAMessageToConsoleDoNotBreak": {"message": "将消息记录到控制台，不要中断"}, "panels/sources/BreakpointEditDialog.ts | logMessageEgXIsX": {"message": "记录消息，例如：'x is', x"}, "panels/sources/BreakpointEditDialog.ts | logpoint": {"message": "日志点"}, "panels/sources/BreakpointEditDialog.ts | pauseOnlyWhenTheConditionIsTrue": {"message": "仅在条件为 true 时暂停"}, "panels/sources/CSSPlugin.ts | addSourceMap": {"message": "添加源代码映射…"}, "panels/sources/CSSPlugin.ts | openColorPicker": {"message": "打开颜色选择器。"}, "panels/sources/CSSPlugin.ts | openCubicBezierEditor": {"message": "打开三次贝塞尔曲线编辑器。"}, "panels/sources/CallStackSidebarPane.ts | callFrameWarnings": {"message": "某些调用帧包含警告"}, "panels/sources/CallStackSidebarPane.ts | callStack": {"message": "调用堆栈"}, "panels/sources/CallStackSidebarPane.ts | copyStackTrace": {"message": "复制堆栈轨迹"}, "panels/sources/CallStackSidebarPane.ts | debugFileNotFound": {"message": "未能加载调试文件“{PH1}”。"}, "panels/sources/CallStackSidebarPane.ts | notPaused": {"message": "未暂停"}, "panels/sources/CallStackSidebarPane.ts | onIgnoreList": {"message": "在忽略列表中"}, "panels/sources/CallStackSidebarPane.ts | restartFrame": {"message": "重启帧"}, "panels/sources/CallStackSidebarPane.ts | showIgnorelistedFrames": {"message": "显示已列入忽略列表的帧"}, "panels/sources/CallStackSidebarPane.ts | showMore": {"message": "展开"}, "panels/sources/CategorizedBreakpointL10n.ts | animationFrameFired": {"message": "动画帧已触发"}, "panels/sources/CategorizedBreakpointL10n.ts | beforeBidderWorkletBiddingStart": {"message": "出价方出价阶段开始"}, "panels/sources/CategorizedBreakpointL10n.ts | beforeBidderWorkletReportingStart": {"message": "出价方报告阶段开始"}, "panels/sources/CategorizedBreakpointL10n.ts | beforeSellerWorkletReportingStart": {"message": "卖方报告阶段开始"}, "panels/sources/CategorizedBreakpointL10n.ts | beforeSellerWorkletScoringStart": {"message": "卖方打分阶段开始"}, "panels/sources/CategorizedBreakpointL10n.ts | cancelAnimationFrame": {"message": "取消动画帧"}, "panels/sources/CategorizedBreakpointL10n.ts | closeAudiocontext": {"message": "关闭 AudioContext"}, "panels/sources/CategorizedBreakpointL10n.ts | createAudiocontext": {"message": "创建 AudioContext"}, "panels/sources/CategorizedBreakpointL10n.ts | createCanvasContext": {"message": "创建画布背景"}, "panels/sources/CategorizedBreakpointL10n.ts | policyViolations": {"message": "违反政策"}, "panels/sources/CategorizedBreakpointL10n.ts | requestAnimationFrame": {"message": "请求动画帧"}, "panels/sources/CategorizedBreakpointL10n.ts | resumeAudiocontext": {"message": "恢复 AudioContext"}, "panels/sources/CategorizedBreakpointL10n.ts | scriptBlockedByContentSecurity": {"message": "脚本因内容安全政策而被屏蔽"}, "panels/sources/CategorizedBreakpointL10n.ts | scriptFirstStatement": {"message": "脚本的第一个语句"}, "panels/sources/CategorizedBreakpointL10n.ts | setInnerhtml": {"message": "设置 innerHTML"}, "panels/sources/CategorizedBreakpointL10n.ts | setTimeoutOrIntervalFired": {"message": "{PH1} 已触发"}, "panels/sources/CategorizedBreakpointL10n.ts | sinkViolations": {"message": "接收器违规行为"}, "panels/sources/CategorizedBreakpointL10n.ts | suspendAudiocontext": {"message": "暂停 AudioContext"}, "panels/sources/CategorizedBreakpointL10n.ts | webglErrorFired": {"message": "WebGL 错误已触发"}, "panels/sources/CategorizedBreakpointL10n.ts | webglWarningFired": {"message": "WebGL 警告已触发"}, "panels/sources/CoveragePlugin.ts | clickToShowCoveragePanel": {"message": "点击即可显示“覆盖率”面板"}, "panels/sources/CoveragePlugin.ts | coverageNa": {"message": "覆盖率：不适用"}, "panels/sources/CoveragePlugin.ts | coverageS": {"message": "覆盖率：{PH1}"}, "panels/sources/CoveragePlugin.ts | showDetails": {"message": "显示详细信息"}, "panels/sources/DebuggerPausedMessage.ts | attributeModifications": {"message": "属性修改"}, "panels/sources/DebuggerPausedMessage.ts | childSAdded": {"message": "已添加子{PH1}"}, "panels/sources/DebuggerPausedMessage.ts | debuggerPaused": {"message": "调试程序已暂停"}, "panels/sources/DebuggerPausedMessage.ts | descendantSAdded": {"message": "添加了后代{PH1}"}, "panels/sources/DebuggerPausedMessage.ts | descendantSRemoved": {"message": "后代{PH1}已移除"}, "panels/sources/DebuggerPausedMessage.ts | nodeRemoval": {"message": "移除节点"}, "panels/sources/DebuggerPausedMessage.ts | pausedBeforePotentialOutofmemory": {"message": "在内存不足可能导致崩溃之前已暂停"}, "panels/sources/DebuggerPausedMessage.ts | pausedOnAssertion": {"message": "已在断言部分暂停"}, "panels/sources/DebuggerPausedMessage.ts | pausedOnBreakpoint": {"message": "已在断点暂停"}, "panels/sources/DebuggerPausedMessage.ts | pausedOnCspViolation": {"message": "已在违反 CSP 时暂停"}, "panels/sources/DebuggerPausedMessage.ts | pausedOnDebuggedFunction": {"message": "已在已调试的函数上暂停"}, "panels/sources/DebuggerPausedMessage.ts | pausedOnEventListener": {"message": "已在事件监听器中暂停"}, "panels/sources/DebuggerPausedMessage.ts | pausedOnException": {"message": "已在遇到异常时暂停"}, "panels/sources/DebuggerPausedMessage.ts | pausedOnPromiseRejection": {"message": "已在 promise 遭拒时暂停"}, "panels/sources/DebuggerPausedMessage.ts | pausedOnS": {"message": "已在{PH1}暂停"}, "panels/sources/DebuggerPausedMessage.ts | pausedOnXhrOrFetch": {"message": "已在 XHR 或 fetch 中暂停"}, "panels/sources/DebuggerPausedMessage.ts | scriptBlockedDueToContent": {"message": "脚本因以下内容安全政策指令而被屏蔽：{PH1}"}, "panels/sources/DebuggerPausedMessage.ts | subtreeModifications": {"message": "子树修改"}, "panels/sources/DebuggerPausedMessage.ts | trustedTypePolicyViolation": {"message": "Trusted Type - 违反政策"}, "panels/sources/DebuggerPausedMessage.ts | trustedTypeSinkViolation": {"message": "Trusted Type - 接收器违规行为"}, "panels/sources/DebuggerPausedMessage.ts | webglErrorFiredS": {"message": "WebGL 错误已触发 ({PH1})"}, "panels/sources/DebuggerPlugin.ts | addBreakpoint": {"message": "添加断点"}, "panels/sources/DebuggerPlugin.ts | addConditionalBreakpoint": {"message": "添加条件断点…"}, "panels/sources/DebuggerPlugin.ts | addLogpoint": {"message": "添加日志点…"}, "panels/sources/DebuggerPlugin.ts | addSourceMap": {"message": "添加源代码映射…"}, "panels/sources/DebuggerPlugin.ts | addWasmDebugInfo": {"message": "添加 DWARF 调试信息…"}, "panels/sources/DebuggerPlugin.ts | associatedFilesAreAvailable": {"message": "相关文件可通过文件树或按相应的组合键 ({PH1}) 获取。"}, "panels/sources/DebuggerPlugin.ts | associatedFilesShouldBeAdded": {"message": "应将相关文件添加到文件树。您可以将这些已解析的源文件作为常规 JavaScript 文件进行调试。"}, "panels/sources/DebuggerPlugin.ts | configure": {"message": "配置"}, "panels/sources/DebuggerPlugin.ts | debugFileNotFound": {"message": "未能加载调试文件“{PH1}”。"}, "panels/sources/DebuggerPlugin.ts | debugInfoNotFound": {"message": "未能加载“{PH1}”的任何调试信息。"}, "panels/sources/DebuggerPlugin.ts | debuggingPowerReduced": {"message": "开发者工具无法显示已创建的源代码，但您可以调试已部署的代码。"}, "panels/sources/DebuggerPlugin.ts | disableBreakpoint": {"message": "{n,plural, =1{停用断点}other{停用行内所有断点}}"}, "panels/sources/DebuggerPlugin.ts | editBreakpoint": {"message": "修改断点…"}, "panels/sources/DebuggerPlugin.ts | enableBreakpoint": {"message": "{n,plural, =1{启用断点}other{启用行内所有断点}}"}, "panels/sources/DebuggerPlugin.ts | errorLoading": {"message": "加载网址 {PH1} 时出错：{PH2}"}, "panels/sources/DebuggerPlugin.ts | neverPauseHere": {"message": "一律不在此处暂停"}, "panels/sources/DebuggerPlugin.ts | openDeveloperResources": {"message": "在“开发者资源”面板中打开请求"}, "panels/sources/DebuggerPlugin.ts | reloadForSourceMap": {"message": "如需再次启用，请确保该文件不在忽略列表中并重新加载。"}, "panels/sources/DebuggerPlugin.ts | removeBreakpoint": {"message": "{n,plural, =1{移除 1 个断点}other{移除行内所有断点}}"}, "panels/sources/DebuggerPlugin.ts | removeFromIgnoreList": {"message": "从忽略列表中移除"}, "panels/sources/DebuggerPlugin.ts | showRequest": {"message": "显示请求"}, "panels/sources/DebuggerPlugin.ts | sourceMapFailed": {"message": "未能加载来源映射。"}, "panels/sources/DebuggerPlugin.ts | sourceMapLoaded": {"message": "已加载来源映射。"}, "panels/sources/DebuggerPlugin.ts | sourceMapSkipped": {"message": "已跳过此文件的来源映射。"}, "panels/sources/DebuggerPlugin.ts | theDebuggerWillSkipStepping": {"message": "调试程序将跳过逐步执行此脚本的过程，且不会在遇到异常时停止。"}, "panels/sources/DebuggerPlugin.ts | thisScriptIsOnTheDebuggersIgnore": {"message": "此脚本位于调试程序的忽略列表中"}, "panels/sources/FilteredUISourceCodeListProvider.ts | noFilesFound": {"message": "找不到任何文件"}, "panels/sources/FilteredUISourceCodeListProvider.ts | sIgnoreListed": {"message": "{PH1}（位于忽略列表中）"}, "panels/sources/GoToLineQuickOpen.ts | currentLineSTypeALineNumber": {"message": "当前行：{PH1}。输入介于 1 和 {PH2} 之间的行号即可转到相应行。"}, "panels/sources/GoToLineQuickOpen.ts | currentPositionXsTypeAnOffset": {"message": "当前位置：0x{PH1}。输入介于 0x{PH2} 和 0x{PH3} 之间的偏移值，即可转到相应位置。"}, "panels/sources/GoToLineQuickOpen.ts | goToLineS": {"message": "转到第 {PH1} 行。"}, "panels/sources/GoToLineQuickOpen.ts | goToLineSAndColumnS": {"message": "转到第 {PH1} 行第 {PH2} 列。"}, "panels/sources/GoToLineQuickOpen.ts | goToOffsetXs": {"message": "转到偏移值 0x{PH1}。"}, "panels/sources/GoToLineQuickOpen.ts | noFileSelected": {"message": "未选择任何文件。"}, "panels/sources/GoToLineQuickOpen.ts | noResultsFound": {"message": "未找到任何结果"}, "panels/sources/GoToLineQuickOpen.ts | typeANumberToGoToThatLine": {"message": "输入数字即可前往相应的行。"}, "panels/sources/InplaceFormatterEditorAction.ts | format": {"message": "格式"}, "panels/sources/InplaceFormatterEditorAction.ts | formatS": {"message": "格式化“{PH1}”"}, "panels/sources/NavigatorView.ts | actionCannotBeUndone": {"message": "此操作无法撤消。"}, "panels/sources/NavigatorView.ts | areYouSureYouWantToDeleteFolder": {"message": "确定要删除此文件夹及其内容吗？"}, "panels/sources/NavigatorView.ts | areYouSureYouWantToDeleteThis": {"message": "确定要删除此文件吗？"}, "panels/sources/NavigatorView.ts | areYouSureYouWantToExcludeThis": {"message": "确定要排除此文件夹吗？"}, "panels/sources/NavigatorView.ts | areYouSureYouWantToRemoveThis": {"message": "从工作区中移除“{PH1}”？"}, "panels/sources/NavigatorView.ts | authored": {"message": "已编写"}, "panels/sources/NavigatorView.ts | authoredTooltip": {"message": "包含原始来源"}, "panels/sources/NavigatorView.ts | delete": {"message": "删除"}, "panels/sources/NavigatorView.ts | deployed": {"message": "已部署"}, "panels/sources/NavigatorView.ts | deployedTooltip": {"message": "包含浏览器检测到的最终来源"}, "panels/sources/NavigatorView.ts | excludeFolder": {"message": "从工作区中排除"}, "panels/sources/NavigatorView.ts | makeACopy": {"message": "制作副本…"}, "panels/sources/NavigatorView.ts | newFile": {"message": "新文件"}, "panels/sources/NavigatorView.ts | noDomain": {"message": "（无网域）"}, "panels/sources/NavigatorView.ts | openFolder": {"message": "打开文件夹"}, "panels/sources/NavigatorView.ts | remove": {"message": "移除"}, "panels/sources/NavigatorView.ts | removeFolderFromWorkspace": {"message": "从工作区中移除"}, "panels/sources/NavigatorView.ts | rename": {"message": "重命名…"}, "panels/sources/NavigatorView.ts | sFromSourceMap": {"message": "{PH1}（来自来源映射）"}, "panels/sources/NavigatorView.ts | sIgnoreListed": {"message": "{PH1}（位于忽略列表中）"}, "panels/sources/NavigatorView.ts | searchInAllFiles": {"message": "在所有文件中搜索"}, "panels/sources/NavigatorView.ts | searchInFolder": {"message": "在文件夹中搜索"}, "panels/sources/NavigatorView.ts | workspaceStopSyncing": {"message": "这会停止将开发者工具中的更改同步到您的源代码。"}, "panels/sources/OutlineQuickOpen.ts | noFileSelected": {"message": "未选择任何文件。"}, "panels/sources/OutlineQuickOpen.ts | noResultsFound": {"message": "未找到任何结果"}, "panels/sources/OutlineQuickOpen.ts | openAJavascriptOrCssFileToSee": {"message": "打开 JavaScript 或 CSS 文件即可查看符号"}, "panels/sources/ProfilePlugin.ts | kb": {"message": "kB"}, "panels/sources/ProfilePlugin.ts | mb": {"message": "MB"}, "panels/sources/ProfilePlugin.ts | ms": {"message": "毫秒"}, "panels/sources/ResourceOriginPlugin.ts | fromS": {"message": "（来自 {PH1}）"}, "panels/sources/ResourceOriginPlugin.ts | sourceMappedFromS": {"message": "（从 {PH1} 映射的来源）"}, "panels/sources/ScopeChainSidebarPane.ts | closure": {"message": "闭包"}, "panels/sources/ScopeChainSidebarPane.ts | closureS": {"message": "闭包 ({PH1})"}, "panels/sources/ScopeChainSidebarPane.ts | exception": {"message": "异常"}, "panels/sources/ScopeChainSidebarPane.ts | loading": {"message": "正在加载…"}, "panels/sources/ScopeChainSidebarPane.ts | noVariables": {"message": "无变量"}, "panels/sources/ScopeChainSidebarPane.ts | notPaused": {"message": "未暂停"}, "panels/sources/ScopeChainSidebarPane.ts | returnValue": {"message": "返回值"}, "panels/sources/SnippetsPlugin.ts | ctrlenter": {"message": "Ctrl+Enter"}, "panels/sources/SnippetsPlugin.ts | enter": {"message": "⌘+Enter"}, "panels/sources/SourcesNavigator.ts | clearConfiguration": {"message": "清除配置"}, "panels/sources/SourcesNavigator.ts | createNewSnippet": {"message": "创建新代码段"}, "panels/sources/SourcesNavigator.ts | explainContentScripts": {"message": "查看扩展程序提供的内容脚本"}, "panels/sources/SourcesNavigator.ts | explainLocalOverrides": {"message": "在本地替换网络请求和 Web 内容以模拟远程资源"}, "panels/sources/SourcesNavigator.ts | explainSnippets": {"message": "保存您经常运行的 JavaScript 代码，以便随时再次运行"}, "panels/sources/SourcesNavigator.ts | explainWorkspace": {"message": "建议您设置工作区，以将修改内容直接同步到您开发的源代码"}, "panels/sources/SourcesNavigator.ts | learnMore": {"message": "了解详情"}, "panels/sources/SourcesNavigator.ts | newSnippet": {"message": "新代码段"}, "panels/sources/SourcesNavigator.ts | remove": {"message": "移除"}, "panels/sources/SourcesNavigator.ts | rename": {"message": "重命名…"}, "panels/sources/SourcesNavigator.ts | run": {"message": "运行"}, "panels/sources/SourcesNavigator.ts | saveAs": {"message": "另存为…"}, "panels/sources/SourcesNavigator.ts | selectFolderForOverrides": {"message": "选择放置替换项的文件夹"}, "panels/sources/SourcesPanel.ts | continueToHere": {"message": "继续执行到此处"}, "panels/sources/SourcesPanel.ts | copyS": {"message": "复制{PH1}"}, "panels/sources/SourcesPanel.ts | copyStringAsJSLiteral": {"message": "复制字符串作为 JavaScript 字面量"}, "panels/sources/SourcesPanel.ts | copyStringAsJSONLiteral": {"message": "复制字符串作为 JSON 字面量"}, "panels/sources/SourcesPanel.ts | copyStringContents": {"message": "复制字符串内容"}, "panels/sources/SourcesPanel.ts | debuggerHidden": {"message": "调试程序边栏处于隐藏状态"}, "panels/sources/SourcesPanel.ts | debuggerShown": {"message": "调试程序边栏处于显示状态"}, "panels/sources/SourcesPanel.ts | dropWorkspaceFolderHere": {"message": "将工作区文件夹拖放到此处"}, "panels/sources/SourcesPanel.ts | groupByAuthored": {"message": "按“已编写”/“已部署”分组"}, "panels/sources/SourcesPanel.ts | groupByFolder": {"message": "按文件夹分组"}, "panels/sources/SourcesPanel.ts | hideDebugger": {"message": "隐藏调试程序"}, "panels/sources/SourcesPanel.ts | hideIgnoreListed": {"message": "隐藏已列入忽略列表的来源"}, "panels/sources/SourcesPanel.ts | hideNavigator": {"message": "隐藏导航器"}, "panels/sources/SourcesPanel.ts | moreOptions": {"message": "更多选项"}, "panels/sources/SourcesPanel.ts | navigatorHidden": {"message": "导航器边栏处于隐藏状态"}, "panels/sources/SourcesPanel.ts | navigatorShown": {"message": "导航器边栏处于显示状态"}, "panels/sources/SourcesPanel.ts | openInSourcesPanel": {"message": "在“来源”面板中打开"}, "panels/sources/SourcesPanel.ts | pauseOnCaughtExceptions": {"message": "在遇到异常时暂停"}, "panels/sources/SourcesPanel.ts | resumeWithAllPausesBlockedForMs": {"message": "忽略所有暂停项达 500 毫秒并继续"}, "panels/sources/SourcesPanel.ts | revealInSidebar": {"message": "在导航器边栏中显示"}, "panels/sources/SourcesPanel.ts | showDebugger": {"message": "显示调试程序"}, "panels/sources/SourcesPanel.ts | showFunctionDefinition": {"message": "显示函数定义"}, "panels/sources/SourcesPanel.ts | showNavigator": {"message": "显示导航器"}, "panels/sources/SourcesPanel.ts | storeAsGlobalVariable": {"message": "存储为全局变量"}, "panels/sources/SourcesPanel.ts | terminateCurrentJavascriptCall": {"message": "终止当前 JavaScript 调用"}, "panels/sources/SourcesView.ts | openFile": {"message": "打开文件"}, "panels/sources/SourcesView.ts | runCommand": {"message": "运行命令"}, "panels/sources/SourcesView.ts | selectFolder": {"message": "选择文件夹"}, "panels/sources/SourcesView.ts | sourceViewActions": {"message": "源代码查看操作"}, "panels/sources/SourcesView.ts | workspaceDropInAFolderToSyncSources": {"message": "若要将所做修改同步到工作区，请将包含源代码的文件夹拖放到此处，或者"}, "panels/sources/TabbedEditorContainer.ts | areYouSureYouWantToCloseUnsaved": {"message": "确定要关闭未保存的文件 ({PH1}) 吗？"}, "panels/sources/TabbedEditorContainer.ts | changesToThisFileWereNotSavedTo": {"message": "对此文件做出的更改未保存到文件系统。"}, "panels/sources/TabbedEditorContainer.ts | unableToLoadThisContent": {"message": "无法加载此内容。"}, "panels/sources/ThreadsSidebarPane.ts | paused": {"message": "已暂停"}, "panels/sources/WatchExpressionsSidebarPane.ts | addPropertyPathToWatch": {"message": "向监视表达式添加属性路径"}, "panels/sources/WatchExpressionsSidebarPane.ts | addWatchExpression": {"message": "添加监视表达式"}, "panels/sources/WatchExpressionsSidebarPane.ts | copyValue": {"message": "复制值"}, "panels/sources/WatchExpressionsSidebarPane.ts | deleteAllWatchExpressions": {"message": "删除所有监视表达式"}, "panels/sources/WatchExpressionsSidebarPane.ts | deleteWatchExpression": {"message": "删除监视表达式"}, "panels/sources/WatchExpressionsSidebarPane.ts | noWatchExpressions": {"message": "没有监视表达式"}, "panels/sources/WatchExpressionsSidebarPane.ts | notAvailable": {"message": "<无法计算>"}, "panels/sources/WatchExpressionsSidebarPane.ts | refreshWatchExpressions": {"message": "刷新监视表达式"}, "panels/sources/components/BreakpointsView.ts | breakpointHit": {"message": "遇到{PH1}断点"}, "panels/sources/components/BreakpointsView.ts | checked": {"message": "已勾选"}, "panels/sources/components/BreakpointsView.ts | conditionCode": {"message": "条件：{PH1}"}, "panels/sources/components/BreakpointsView.ts | disableAllBreakpoints": {"message": "停用所有断点"}, "panels/sources/components/BreakpointsView.ts | disableAllBreakpointsInFile": {"message": "停用文件内所有断点"}, "panels/sources/components/BreakpointsView.ts | editCondition": {"message": "修改条件"}, "panels/sources/components/BreakpointsView.ts | editLogpoint": {"message": "修改日志点"}, "panels/sources/components/BreakpointsView.ts | enableAllBreakpoints": {"message": "启用所有断点"}, "panels/sources/components/BreakpointsView.ts | enableAllBreakpointsInFile": {"message": "启用文件内所有断点"}, "panels/sources/components/BreakpointsView.ts | indeterminate": {"message": "混合"}, "panels/sources/components/BreakpointsView.ts | logpointCode": {"message": "日志点：{PH1}"}, "panels/sources/components/BreakpointsView.ts | pauseOnCaughtExceptions": {"message": "在遇到异常时暂停"}, "panels/sources/components/BreakpointsView.ts | pauseOnUncaughtExceptions": {"message": "遇到未捕获的异常时暂停"}, "panels/sources/components/BreakpointsView.ts | removeAllBreakpoints": {"message": "移除所有断点"}, "panels/sources/components/BreakpointsView.ts | removeAllBreakpointsInFile": {"message": "移除文件内所有断点"}, "panels/sources/components/BreakpointsView.ts | removeBreakpoint": {"message": "移除断点"}, "panels/sources/components/BreakpointsView.ts | removeOtherBreakpoints": {"message": "移除其他断点"}, "panels/sources/components/BreakpointsView.ts | revealLocation": {"message": "显示位置"}, "panels/sources/components/BreakpointsView.ts | unchecked": {"message": "未选中"}, "panels/sources/components/HeadersView.ts | addHeader": {"message": "添加标题"}, "panels/sources/components/HeadersView.ts | addOverrideRule": {"message": "添加替换规则"}, "panels/sources/components/HeadersView.ts | errorWhenParsing": {"message": "解析“{PH1}”时出错。"}, "panels/sources/components/HeadersView.ts | learnMore": {"message": "了解详情"}, "panels/sources/components/HeadersView.ts | parsingErrorExplainer": {"message": "这很可能是由于“{PH1}”中存在语法错误。请尝试在外部编辑器中打开此文件以修正该错误，或者删除此文件然后重新创建替换项。"}, "panels/sources/components/HeadersView.ts | removeBlock": {"message": "移除此“ApplyTo”部分"}, "panels/sources/components/HeadersView.ts | removeHeader": {"message": "移除此标题"}, "panels/sources/sources-meta.ts | activateBreakpoints": {"message": "启用断点"}, "panels/sources/sources-meta.ts | addFolder": {"message": "添加文件夹"}, "panels/sources/sources-meta.ts | addFolderToWorkspace": {"message": "向工作区添加文件夹"}, "panels/sources/sources-meta.ts | addSelectedTextToWatches": {"message": "将所选文本添加至监视表达式"}, "panels/sources/sources-meta.ts | all": {"message": "全部"}, "panels/sources/sources-meta.ts | allowScrollingPastEndOfFile": {"message": "允许滚动范围超出文件末尾"}, "panels/sources/sources-meta.ts | autocompletion": {"message": "自动补全"}, "panels/sources/sources-meta.ts | automaticallyPrettyPrintMinifiedSources": {"message": "自动美观输出缩减过大小的源代码"}, "panels/sources/sources-meta.ts | automaticallyRevealFilesIn": {"message": "自动在边栏中显示文件"}, "panels/sources/sources-meta.ts | bracketClosing": {"message": "自动添加右括号"}, "panels/sources/sources-meta.ts | bracketMatching": {"message": "括号匹配"}, "panels/sources/sources-meta.ts | breakpoints": {"message": "断点"}, "panels/sources/sources-meta.ts | closeAll": {"message": "全部关闭"}, "panels/sources/sources-meta.ts | closeTheActiveTab": {"message": "关闭使用中的标签页"}, "panels/sources/sources-meta.ts | codeFolding": {"message": "代码折叠"}, "panels/sources/sources-meta.ts | createNewSnippet": {"message": "创建新代码段"}, "panels/sources/sources-meta.ts | cssSourceMaps": {"message": "CSS 源代码映射"}, "panels/sources/sources-meta.ts | deactivateBreakpoints": {"message": "停用断点"}, "panels/sources/sources-meta.ts | decrementCssUnitBy": {"message": "将 CSS 单位减少 {PH1}"}, "panels/sources/sources-meta.ts | detectIndentation": {"message": "检测缩进"}, "panels/sources/sources-meta.ts | disableAutoFocusOnDebuggerPaused": {"message": "触发断点后不聚焦于“来源”面板"}, "panels/sources/sources-meta.ts | disableAutocompletion": {"message": "停用自动补全功能"}, "panels/sources/sources-meta.ts | disableBracketClosing": {"message": "停用自动添加右括号的功能"}, "panels/sources/sources-meta.ts | disableBracketMatching": {"message": "停用括号匹配"}, "panels/sources/sources-meta.ts | disableCodeFolding": {"message": "停用代码折叠功能"}, "panels/sources/sources-meta.ts | disableCssSourceMaps": {"message": "停用 CSS 源代码映射"}, "panels/sources/sources-meta.ts | disableJavaScriptSourceMaps": {"message": "停用 JavaScript 源代码映射"}, "panels/sources/sources-meta.ts | disableTabMovesFocus": {"message": "停用通过 Tab 键移动焦点功能"}, "panels/sources/sources-meta.ts | disableWasmAutoStepping": {"message": "停用 Wasm 自动步进"}, "panels/sources/sources-meta.ts | disallowScrollingPastEndOfFile": {"message": "不允许滚动范围超出文件末尾"}, "panels/sources/sources-meta.ts | displayVariableValuesInlineWhile": {"message": "在调试时显示内嵌变量值"}, "panels/sources/sources-meta.ts | doNotAutomaticallyPrettyPrintMinifiedSources": {"message": "不自动美观输出缩减过大小的源代码"}, "panels/sources/sources-meta.ts | doNotAutomaticallyRevealFilesIn": {"message": "不自动在边栏中显示文件"}, "panels/sources/sources-meta.ts | doNotDetectIndentation": {"message": "不检测缩进"}, "panels/sources/sources-meta.ts | doNotDisplayVariableValuesInline": {"message": "调试时不以内嵌方式显示变量值"}, "panels/sources/sources-meta.ts | doNotSearchInAnonymousAndContent": {"message": "不在匿名和内容脚本中搜索"}, "panels/sources/sources-meta.ts | doNotShowWhitespaceCharacters": {"message": "不显示空格字符串"}, "panels/sources/sources-meta.ts | enableAutoFocusOnDebuggerPaused": {"message": "触发断点后聚焦于“来源”面板"}, "panels/sources/sources-meta.ts | enableAutocompletion": {"message": "启用自动补全功能"}, "panels/sources/sources-meta.ts | enableBracketClosing": {"message": "启用自动添加右括号的功能"}, "panels/sources/sources-meta.ts | enableBracketMatching": {"message": "启用括号匹配功能"}, "panels/sources/sources-meta.ts | enableCodeFolding": {"message": "启用代码折叠功能"}, "panels/sources/sources-meta.ts | enableCssSourceMaps": {"message": "启用 CSS 源代码映射"}, "panels/sources/sources-meta.ts | enableJavaScriptSourceMaps": {"message": "启用 JavaScript 源代码映射"}, "panels/sources/sources-meta.ts | enableTabMovesFocus": {"message": "启用通过 Tab 键移动焦点功能"}, "panels/sources/sources-meta.ts | enableWasmAutoStepping": {"message": "启用 Wasm 自动步进"}, "panels/sources/sources-meta.ts | evaluateSelectedTextInConsole": {"message": "在控制台中评估所选文本"}, "panels/sources/sources-meta.ts | file": {"message": "文件"}, "panels/sources/sources-meta.ts | goTo": {"message": "转到"}, "panels/sources/sources-meta.ts | goToAFunctionDeclarationruleSet": {"message": "转到函数声明/规则组"}, "panels/sources/sources-meta.ts | goToLine": {"message": "转到行"}, "panels/sources/sources-meta.ts | incrementCssUnitBy": {"message": "将 CSS 单位增加 {PH1}"}, "panels/sources/sources-meta.ts | javaScriptSourceMaps": {"message": "JavaScript 源代码映射"}, "panels/sources/sources-meta.ts | jumpToNextEditingLocation": {"message": "跳转到下一个修改位置"}, "panels/sources/sources-meta.ts | jumpToPreviousEditingLocation": {"message": "跳转到上一个修改位置"}, "panels/sources/sources-meta.ts | line": {"message": "行"}, "panels/sources/sources-meta.ts | nextCallFrame": {"message": "下一个调用帧"}, "panels/sources/sources-meta.ts | nextEditorTab": {"message": "下一个编辑器"}, "panels/sources/sources-meta.ts | none": {"message": "无"}, "panels/sources/sources-meta.ts | open": {"message": "打开"}, "panels/sources/sources-meta.ts | pauseScriptExecution": {"message": "暂停脚本执行"}, "panels/sources/sources-meta.ts | previousCallFrame": {"message": "上一个调用帧"}, "panels/sources/sources-meta.ts | previousEditorTab": {"message": "上一个编辑器"}, "panels/sources/sources-meta.ts | quickSource": {"message": "快速来源"}, "panels/sources/sources-meta.ts | rename": {"message": "重命名"}, "panels/sources/sources-meta.ts | resumeScriptExecution": {"message": "继续执行脚本"}, "panels/sources/sources-meta.ts | revealActiveFileInSidebar": {"message": "在导航器边栏中显示当前文件"}, "panels/sources/sources-meta.ts | runSnippet": {"message": "运行代码段"}, "panels/sources/sources-meta.ts | save": {"message": "保存"}, "panels/sources/sources-meta.ts | saveAll": {"message": "全部保存"}, "panels/sources/sources-meta.ts | scope": {"message": "作用域"}, "panels/sources/sources-meta.ts | search": {"message": "搜索"}, "panels/sources/sources-meta.ts | searchInAnonymousAndContent": {"message": "在匿名和内容脚本中搜索"}, "panels/sources/sources-meta.ts | showAllWhitespaceCharacters": {"message": "显示所有空格字符"}, "panels/sources/sources-meta.ts | showBreakpoints": {"message": "显示“断点”工具"}, "panels/sources/sources-meta.ts | showQuickSource": {"message": "显示“快速来源”工具"}, "panels/sources/sources-meta.ts | showScope": {"message": "显示“作用域”"}, "panels/sources/sources-meta.ts | showSearch": {"message": "显示“搜索”工具"}, "panels/sources/sources-meta.ts | showSnippets": {"message": "显示“代码段”工具"}, "panels/sources/sources-meta.ts | showSources": {"message": "显示“来源”工具"}, "panels/sources/sources-meta.ts | showThreads": {"message": "显示“线程”工具"}, "panels/sources/sources-meta.ts | showTrailingWhitespaceCharacters": {"message": "显示尾随空格字符"}, "panels/sources/sources-meta.ts | showWatch": {"message": "显示“监视”工具"}, "panels/sources/sources-meta.ts | showWhitespaceCharacters": {"message": "显示空格字符："}, "panels/sources/sources-meta.ts | showWorkspace": {"message": "显示工作区"}, "panels/sources/sources-meta.ts | snippets": {"message": "代码段"}, "panels/sources/sources-meta.ts | sources": {"message": "源代码/来源"}, "panels/sources/sources-meta.ts | step": {"message": "单步调试"}, "panels/sources/sources-meta.ts | stepIntoNextFunctionCall": {"message": "进入下一个函数调用"}, "panels/sources/sources-meta.ts | stepOutOfCurrentFunction": {"message": "跳出当前函数"}, "panels/sources/sources-meta.ts | stepOverNextFunctionCall": {"message": "跳过下一个函数调用"}, "panels/sources/sources-meta.ts | switchFile": {"message": "切换文件"}, "panels/sources/sources-meta.ts | symbol": {"message": "符号"}, "panels/sources/sources-meta.ts | tabMovesFocus": {"message": "按 Tab 键可移动焦点"}, "panels/sources/sources-meta.ts | threads": {"message": "线程"}, "panels/sources/sources-meta.ts | toggleBreakpoint": {"message": "切换断点"}, "panels/sources/sources-meta.ts | toggleBreakpointEnabled": {"message": "已启用切换断点快捷键"}, "panels/sources/sources-meta.ts | toggleBreakpointInputWindow": {"message": "开启/关闭断点输入窗口"}, "panels/sources/sources-meta.ts | toggleDebuggerSidebar": {"message": "开启/关闭调试程序边栏"}, "panels/sources/sources-meta.ts | toggleNavigatorSidebar": {"message": "开启/关闭导航器边栏"}, "panels/sources/sources-meta.ts | trailing": {"message": "尾随"}, "panels/sources/sources-meta.ts | wasmAutoStepping": {"message": "使用调试信息调试 Wasm 时，尽量别在 Wasm 字节码处暂停"}, "panels/sources/sources-meta.ts | watch": {"message": "监视"}, "panels/sources/sources-meta.ts | workspace": {"message": "工作区"}, "panels/timeline/AnimationsTrackAppender.ts | animations": {"message": "动画"}, "panels/timeline/AppenderUtils.ts | sSelfS": {"message": "{PH1}（自身耗时 {PH2}）"}, "panels/timeline/CountersGraph.ts | documents": {"message": "文档"}, "panels/timeline/CountersGraph.ts | gpuMemory": {"message": "GPU 内存"}, "panels/timeline/CountersGraph.ts | jsHeap": {"message": "JS 堆"}, "panels/timeline/CountersGraph.ts | listeners": {"message": "监听器"}, "panels/timeline/CountersGraph.ts | nodes": {"message": "节点"}, "panels/timeline/CountersGraph.ts | ss": {"message": "[{PH1} - {PH2}]"}, "panels/timeline/EventUICategory.ts | animation": {"message": "动画"}, "panels/timeline/EventUICategory.ts | animationFrameFired": {"message": "动画帧已触发"}, "panels/timeline/EventUICategory.ts | async": {"message": "异步"}, "panels/timeline/EventUICategory.ts | asyncTask": {"message": "异步任务"}, "panels/timeline/EventUICategory.ts | cacheModule": {"message": "缓存模块代码"}, "panels/timeline/EventUICategory.ts | cacheScript": {"message": "缓存脚本代码"}, "panels/timeline/EventUICategory.ts | cachedWasmModule": {"message": "缓存的 Wasm 模块"}, "panels/timeline/EventUICategory.ts | cancelAnimationFrame": {"message": "取消动画帧"}, "panels/timeline/EventUICategory.ts | cancelIdleCallback": {"message": "取消空闲回调"}, "panels/timeline/EventUICategory.ts | commit": {"message": "提交"}, "panels/timeline/EventUICategory.ts | compileCode": {"message": "编译代码"}, "panels/timeline/EventUICategory.ts | compileModule": {"message": "编译模块"}, "panels/timeline/EventUICategory.ts | compileScript": {"message": "编译脚本"}, "panels/timeline/EventUICategory.ts | compiledWasmModule": {"message": "已编译的 Wasm 模块"}, "panels/timeline/EventUICategory.ts | compositeLayers": {"message": "复合图层"}, "panels/timeline/EventUICategory.ts | computeIntersections": {"message": "计算相交部分"}, "panels/timeline/EventUICategory.ts | consoleTime": {"message": "控制台时间事件"}, "panels/timeline/EventUICategory.ts | cppGc": {"message": "CPP GC"}, "panels/timeline/EventUICategory.ts | createWebsocket": {"message": "创建 WebSocket"}, "panels/timeline/EventUICategory.ts | decrypt": {"message": "解密"}, "panels/timeline/EventUICategory.ts | decryptReply": {"message": "解密回复"}, "panels/timeline/EventUICategory.ts | deserializeCodeCache": {"message": "反序列化代码缓存"}, "panels/timeline/EventUICategory.ts | destroyWebsocket": {"message": "销毁 WebSocket"}, "panels/timeline/EventUICategory.ts | digest": {"message": "摘要"}, "panels/timeline/EventUICategory.ts | digestReply": {"message": "摘要回复"}, "panels/timeline/EventUICategory.ts | domGc": {"message": "DOM GC"}, "panels/timeline/EventUICategory.ts | domcontentloadedEvent": {"message": "DOMContentLoaded 事件"}, "panels/timeline/EventUICategory.ts | drawFrame": {"message": "绘制帧"}, "panels/timeline/EventUICategory.ts | drawing": {"message": "绘制"}, "panels/timeline/EventUICategory.ts | embedderCallback": {"message": "嵌入器回调"}, "panels/timeline/EventUICategory.ts | encrypt": {"message": "加密"}, "panels/timeline/EventUICategory.ts | encryptReply": {"message": "加密回复"}, "panels/timeline/EventUICategory.ts | evaluateModule": {"message": "评估模块"}, "panels/timeline/EventUICategory.ts | evaluateScript": {"message": "评估脚本"}, "panels/timeline/EventUICategory.ts | event": {"message": "事件"}, "panels/timeline/EventUICategory.ts | eventTiming": {"message": "事件时间"}, "panels/timeline/EventUICategory.ts | experience": {"message": "体验"}, "panels/timeline/EventUICategory.ts | finishLoading": {"message": "完成加载"}, "panels/timeline/EventUICategory.ts | fireIdleCallback": {"message": "触发空闲回调"}, "panels/timeline/EventUICategory.ts | firstContentfulPaint": {"message": "First Contentful Paint"}, "panels/timeline/EventUICategory.ts | firstPaint": {"message": "首次绘制"}, "panels/timeline/EventUICategory.ts | frameStart": {"message": "开始显示帧"}, "panels/timeline/EventUICategory.ts | frameStartMainThread": {"message": "帧开始（主线程）"}, "panels/timeline/EventUICategory.ts | frameStartedLoading": {"message": "帧开始加载"}, "panels/timeline/EventUICategory.ts | functionCall": {"message": "函数调用"}, "panels/timeline/EventUICategory.ts | gcEvent": {"message": "垃圾回收事件"}, "panels/timeline/EventUICategory.ts | gpu": {"message": "GPU"}, "panels/timeline/EventUICategory.ts | hitTest": {"message": "命中测试"}, "panels/timeline/EventUICategory.ts | idle": {"message": "空闲"}, "panels/timeline/EventUICategory.ts | imageDecode": {"message": "图片解码"}, "panels/timeline/EventUICategory.ts | installTimer": {"message": "安装定时器"}, "panels/timeline/EventUICategory.ts | invalidateLayout": {"message": "使布局失效"}, "panels/timeline/EventUICategory.ts | jsFrame": {"message": "JS 帧"}, "panels/timeline/EventUICategory.ts | largestContentfulPaint": {"message": "Largest Contentful Paint"}, "panels/timeline/EventUICategory.ts | layerize": {"message": "分层"}, "panels/timeline/EventUICategory.ts | layout": {"message": "布局"}, "panels/timeline/EventUICategory.ts | layoutShift": {"message": "布局偏移"}, "panels/timeline/EventUICategory.ts | loading": {"message": "加载"}, "panels/timeline/EventUICategory.ts | majorGc": {"message": "主要垃圾回收"}, "panels/timeline/EventUICategory.ts | messaging": {"message": "消息功能"}, "panels/timeline/EventUICategory.ts | minorGc": {"message": "次要垃圾回收"}, "panels/timeline/EventUICategory.ts | onMessage": {"message": "在消息上"}, "panels/timeline/EventUICategory.ts | onloadEvent": {"message": "Onload 事件"}, "panels/timeline/EventUICategory.ts | optimizeCode": {"message": "优化代码"}, "panels/timeline/EventUICategory.ts | other": {"message": "其他"}, "panels/timeline/EventUICategory.ts | paint": {"message": "绘制"}, "panels/timeline/EventUICategory.ts | paintImage": {"message": "绘制图片"}, "panels/timeline/EventUICategory.ts | paintSetup": {"message": "绘制设置"}, "panels/timeline/EventUICategory.ts | painting": {"message": "绘制"}, "panels/timeline/EventUICategory.ts | parseAndCompile": {"message": "解析和编译"}, "panels/timeline/EventUICategory.ts | parseHtml": {"message": "解析 HTML"}, "panels/timeline/EventUICategory.ts | parseStylesheet": {"message": "解析样式表"}, "panels/timeline/EventUICategory.ts | prePaint": {"message": "预先绘制"}, "panels/timeline/EventUICategory.ts | profilingOverhead": {"message": "分析开销"}, "panels/timeline/EventUICategory.ts | rasterizePaint": {"message": "光栅化绘制内容"}, "panels/timeline/EventUICategory.ts | rasterizing": {"message": "正在光栅化"}, "panels/timeline/EventUICategory.ts | recalculateStyle": {"message": "重新计算样式"}, "panels/timeline/EventUICategory.ts | receiveData": {"message": "接收数据"}, "panels/timeline/EventUICategory.ts | receiveResponse": {"message": "接收响应"}, "panels/timeline/EventUICategory.ts | receiveWebsocketHandshake": {"message": "接收 WebSocket 握手"}, "panels/timeline/EventUICategory.ts | removeTimer": {"message": "移除定时器"}, "panels/timeline/EventUICategory.ts | rendering": {"message": "渲染"}, "panels/timeline/EventUICategory.ts | requestAnimationFrame": {"message": "请求动画帧"}, "panels/timeline/EventUICategory.ts | requestIdleCallback": {"message": "请求空闲回调"}, "panels/timeline/EventUICategory.ts | requestMainThreadFrame": {"message": "请求主线程帧"}, "panels/timeline/EventUICategory.ts | runMicrotasks": {"message": "运行微任务"}, "panels/timeline/EventUICategory.ts | schedulePostMessage": {"message": "安排 postMessage 的时间"}, "panels/timeline/EventUICategory.ts | scheduleStyleRecalculation": {"message": "安排重新计算样式的时间"}, "panels/timeline/EventUICategory.ts | scripting": {"message": "执行脚本"}, "panels/timeline/EventUICategory.ts | scroll": {"message": "滚动"}, "panels/timeline/EventUICategory.ts | sendRequest": {"message": "发送请求"}, "panels/timeline/EventUICategory.ts | sendWebsocketHandshake": {"message": "发送 WebSocket 握手"}, "panels/timeline/EventUICategory.ts | sign": {"message": "签名"}, "panels/timeline/EventUICategory.ts | signReply": {"message": "签名回复"}, "panels/timeline/EventUICategory.ts | streamingCompileTask": {"message": "流式编译任务"}, "panels/timeline/EventUICategory.ts | streamingWasmResponse": {"message": "流式 Wasm 响应"}, "panels/timeline/EventUICategory.ts | system": {"message": "系统"}, "panels/timeline/EventUICategory.ts | task": {"message": "任务"}, "panels/timeline/EventUICategory.ts | timerFired": {"message": "定时器已触发"}, "panels/timeline/EventUICategory.ts | timestamp": {"message": "时间戳"}, "panels/timeline/EventUICategory.ts | updateLayer": {"message": "更新图层"}, "panels/timeline/EventUICategory.ts | updateLayerTree": {"message": "更新图层树"}, "panels/timeline/EventUICategory.ts | userTiming": {"message": "用户计时"}, "panels/timeline/EventUICategory.ts | verify": {"message": "验证"}, "panels/timeline/EventUICategory.ts | verifyReply": {"message": "验证回复"}, "panels/timeline/EventUICategory.ts | waitingForNetwork": {"message": "正在等待连接到网络"}, "panels/timeline/EventUICategory.ts | wasmModuleCacheHit": {"message": "Wasm 模块缓存命中"}, "panels/timeline/EventUICategory.ts | wasmModuleCacheInvalid": {"message": "Wasm 模块缓存无效"}, "panels/timeline/EventUICategory.ts | willSendRequest": {"message": "将发送请求"}, "panels/timeline/EventUICategory.ts | wsMessageReceived": {"message": "接收 WebSocket 消息"}, "panels/timeline/EventUICategory.ts | wsMessageSent": {"message": "发送 WebSocket 消息"}, "panels/timeline/EventUICategory.ts | xhrLoad": {"message": "XHR 加载"}, "panels/timeline/EventUICategory.ts | xhrReadyStateChange": {"message": "XHR 就绪状态变更"}, "panels/timeline/EventsTimelineTreeView.ts | Dms": {"message": "{PH1} 毫秒"}, "panels/timeline/EventsTimelineTreeView.ts | all": {"message": "全部"}, "panels/timeline/EventsTimelineTreeView.ts | durationFilter": {"message": "时长过滤器"}, "panels/timeline/EventsTimelineTreeView.ts | startTime": {"message": "开始时间"}, "panels/timeline/GPUTrackAppender.ts | gpu": {"message": "GPU"}, "panels/timeline/InteractionsTrackAppender.ts | interactions": {"message": "互动"}, "panels/timeline/IsolateSelector.ts | empty": {"message": "（空）"}, "panels/timeline/IsolateSelector.ts | selectJavascriptVmInstance": {"message": "选择 JavaScript 虚拟机实例"}, "panels/timeline/LayoutShiftsTrackAppender.ts | layoutShifts": {"message": "布局偏移"}, "panels/timeline/NetworkTrackAppender.ts | network": {"message": "网络"}, "panels/timeline/ThreadAppender.ts | anonymous": {"message": "（匿名）"}, "panels/timeline/ThreadAppender.ts | bidderWorklet": {"message": "出价方 Worklet"}, "panels/timeline/ThreadAppender.ts | bidderWorkletS": {"message": "出价方 Worklet - {PH1}"}, "panels/timeline/ThreadAppender.ts | dedicatedWorker": {"message": "专用 Worker"}, "panels/timeline/ThreadAppender.ts | eventDispatchS": {"message": "事件类型：{PH1}"}, "panels/timeline/ThreadAppender.ts | frameS": {"message": "帧 - {PH1}"}, "panels/timeline/ThreadAppender.ts | main": {"message": "主线程"}, "panels/timeline/ThreadAppender.ts | mainS": {"message": "主要 - {PH1}"}, "panels/timeline/ThreadAppender.ts | onIgnoreList": {"message": "在忽略列表中"}, "panels/timeline/ThreadAppender.ts | raster": {"message": "光栅"}, "panels/timeline/ThreadAppender.ts | rasterizerThreadS": {"message": "光栅器线程 {PH1}"}, "panels/timeline/ThreadAppender.ts | sellerWorklet": {"message": "卖方 Worklet"}, "panels/timeline/ThreadAppender.ts | sellerWorkletS": {"message": "卖方 Worklet - {PH1}"}, "panels/timeline/ThreadAppender.ts | threadPool": {"message": "线程池"}, "panels/timeline/ThreadAppender.ts | threadPoolThreadS": {"message": "线程池工作器 {PH1}"}, "panels/timeline/ThreadAppender.ts | threadS": {"message": "线程 {PH1}"}, "panels/timeline/ThreadAppender.ts | unknownWorklet": {"message": "竞价 Worklet"}, "panels/timeline/ThreadAppender.ts | unknownWorkletS": {"message": "竞价 Worklet - {PH1}"}, "panels/timeline/ThreadAppender.ts | workerS": {"message": "Worker - {PH1}"}, "panels/timeline/ThreadAppender.ts | workerSS": {"message": "Worker：{PH1} - {PH2}"}, "panels/timeline/ThreadAppender.ts | workletService": {"message": "竞价 Worklet 服务"}, "panels/timeline/ThreadAppender.ts | workletServiceS": {"message": "竞价 Worklet 服务 - {PH1}"}, "panels/timeline/TimelineController.ts | tracingNotSupported": {"message": "无法为这类目标提供性能跟踪记录"}, "panels/timeline/TimelineDetailsView.ts | bottomup": {"message": "自下而上"}, "panels/timeline/TimelineDetailsView.ts | callTree": {"message": "调用树"}, "panels/timeline/TimelineDetailsView.ts | eventLog": {"message": "事件日志"}, "panels/timeline/TimelineDetailsView.ts | layers": {"message": "图层"}, "panels/timeline/TimelineDetailsView.ts | paintProfiler": {"message": "绘制性能剖析器"}, "panels/timeline/TimelineDetailsView.ts | rangeSS": {"message": "范围：{PH1} - {PH2}"}, "panels/timeline/TimelineDetailsView.ts | selectorStats": {"message": "选择器统计信息"}, "panels/timeline/TimelineDetailsView.ts | summary": {"message": "摘要"}, "panels/timeline/TimelineEventOverview.ts | cpu": {"message": "CPU"}, "panels/timeline/TimelineEventOverview.ts | heap": {"message": "堆"}, "panels/timeline/TimelineEventOverview.ts | net": {"message": "网络"}, "panels/timeline/TimelineEventOverview.ts | sSDash": {"message": "{PH1} - {PH2}"}, "panels/timeline/TimelineFlameChartDataProvider.ts | droppedFrame": {"message": "丢弃的帧"}, "panels/timeline/TimelineFlameChartDataProvider.ts | frame": {"message": "帧"}, "panels/timeline/TimelineFlameChartDataProvider.ts | frames": {"message": "帧"}, "panels/timeline/TimelineFlameChartDataProvider.ts | idleFrame": {"message": "空闲帧"}, "panels/timeline/TimelineFlameChartDataProvider.ts | partiallyPresentedFrame": {"message": "部分呈现帧"}, "panels/timeline/TimelineFlameChartView.ts | sAtS": {"message": "{PH2}时显示的{PH1}"}, "panels/timeline/TimelineHistoryManager.ts | currentSessionSS": {"message": "当前会话：{PH1}。{PH2}"}, "panels/timeline/TimelineHistoryManager.ts | moments": {"message": "时刻"}, "panels/timeline/TimelineHistoryManager.ts | noRecordings": {"message": "（无录制内容）"}, "panels/timeline/TimelineHistoryManager.ts | sAgo": {"message": "（{PH1}前）"}, "panels/timeline/TimelineHistoryManager.ts | sD": {"message": "{PH1} #{PH2}"}, "panels/timeline/TimelineHistoryManager.ts | sH": {"message": "{PH1} 小时"}, "panels/timeline/TimelineHistoryManager.ts | sM": {"message": "{PH1} 分钟"}, "panels/timeline/TimelineHistoryManager.ts | selectTimelineSession": {"message": "选择时间轴会话"}, "panels/timeline/TimelineLandingPage.ts | afterRecordingSelectAnAreaOf": {"message": "录制结束后，在概览中以拖动方式选择感兴趣的区域。然后，使用鼠标滚轮或 {PH1} 组合键缩放并平移时间轴。{PH2}"}, "panels/timeline/TimelineLandingPage.ts | clickTheRecordButtonSOrHitSTo": {"message": "点击录制按钮“{PH1}”或按 {PH2} 即可开始录制新内容。"}, "panels/timeline/TimelineLandingPage.ts | clickTheReloadButtonSOrHitSTo": {"message": "点击重新加载按钮 {PH1} 或按 {PH2} 即可录制网页加载过程。"}, "panels/timeline/TimelineLandingPage.ts | learnmore": {"message": "了解详情"}, "panels/timeline/TimelineLandingPage.ts | wasd": {"message": "WASD"}, "panels/timeline/TimelineLoader.ts | malformedTimelineDataS": {"message": "格式有误的时间轴数据：{PH1}"}, "panels/timeline/TimelinePanel.ts | CpuThrottlingIsEnabled": {"message": "- CPU 节流已启用"}, "panels/timeline/TimelinePanel.ts | HardwareConcurrencyIsEnabled": {"message": "- 硬件并发替换已启用"}, "panels/timeline/TimelinePanel.ts | JavascriptSamplingIsDisabled": {"message": "- JavaScript 采样已停用"}, "panels/timeline/TimelinePanel.ts | NetworkThrottlingIsEnabled": {"message": "- 已启用网络节流功能"}, "panels/timeline/TimelinePanel.ts | SelectorStatsEnabled": {"message": "- 已启用选择器统计信息"}, "panels/timeline/TimelinePanel.ts | SignificantOverheadDueToPaint": {"message": "- 因绘制插桩而产生大量开销"}, "panels/timeline/TimelinePanel.ts | bufferUsage": {"message": "缓冲区使用情况"}, "panels/timeline/TimelinePanel.ts | captureScreenshots": {"message": "截取屏幕截图"}, "panels/timeline/TimelinePanel.ts | captureSettings": {"message": "录制设置"}, "panels/timeline/TimelinePanel.ts | capturesAdvancedPaint": {"message": "捕获高级绘制插桩，产生大量性能开销"}, "panels/timeline/TimelinePanel.ts | capturesSelectorStats": {"message": "捕获 CSS 选择器统计信息"}, "panels/timeline/TimelinePanel.ts | clear": {"message": "清除"}, "panels/timeline/TimelinePanel.ts | close": {"message": "关闭"}, "panels/timeline/TimelinePanel.ts | cpu": {"message": "CPU："}, "panels/timeline/TimelinePanel.ts | description": {"message": "说明"}, "panels/timeline/TimelinePanel.ts | disableJavascriptSamples": {"message": "停用 JavaScript 示例"}, "panels/timeline/TimelinePanel.ts | disablesJavascriptSampling": {"message": "停用 JavaScript 采样，减少在移动设备上运行时的开销"}, "panels/timeline/TimelinePanel.ts | downloadAfterError": {"message": "下载原始轨迹事件"}, "panels/timeline/TimelinePanel.ts | dropTimelineFileOrUrlHere": {"message": "将时间轴文件或网址拖放到此处"}, "panels/timeline/TimelinePanel.ts | enableAdvancedPaint": {"message": "启用高级绘制插桩（慢速）"}, "panels/timeline/TimelinePanel.ts | enableSelectorStats": {"message": "启用 CSS 选择器统计信息（较慢）"}, "panels/timeline/TimelinePanel.ts | failedToSaveTimelineSS": {"message": "未能保存时间轴：{PH1}（{PH2}）"}, "panels/timeline/TimelinePanel.ts | fixMe": {"message": "修复"}, "panels/timeline/TimelinePanel.ts | initializingProfiler": {"message": "正在初始化性能剖析器…"}, "panels/timeline/TimelinePanel.ts | loadProfile": {"message": "加载性能分析报告…"}, "panels/timeline/TimelinePanel.ts | loadingProfile": {"message": "正在加载性能分析报告…"}, "panels/timeline/TimelinePanel.ts | memory": {"message": "内存"}, "panels/timeline/TimelinePanel.ts | network": {"message": "网络："}, "panels/timeline/TimelinePanel.ts | networkConditions": {"message": "网络状况"}, "panels/timeline/TimelinePanel.ts | processingProfile": {"message": "正在处理性能分析报告…"}, "panels/timeline/TimelinePanel.ts | profiling": {"message": "正在进行性能分析…"}, "panels/timeline/TimelinePanel.ts | received": {"message": "已接收"}, "panels/timeline/TimelinePanel.ts | recordingFailed": {"message": "录制失败"}, "panels/timeline/TimelinePanel.ts | saveProfile": {"message": "保存性能分析报告…"}, "panels/timeline/TimelinePanel.ts | screenshots": {"message": "屏幕截图"}, "panels/timeline/TimelinePanel.ts | showMemoryTimeline": {"message": "显示内存时间轴"}, "panels/timeline/TimelinePanel.ts | ssec": {"message": "{PH1} 秒"}, "panels/timeline/TimelinePanel.ts | status": {"message": "状态"}, "panels/timeline/TimelinePanel.ts | stop": {"message": "停止"}, "panels/timeline/TimelinePanel.ts | stoppingTimeline": {"message": "正在停止时间轴…"}, "panels/timeline/TimelinePanel.ts | time": {"message": "时间"}, "panels/timeline/TimelineSelectorStatsView.ts | copyTable": {"message": "复制表"}, "panels/timeline/TimelineSelectorStatsView.ts | elapsed": {"message": "用时 (ms)"}, "panels/timeline/TimelineSelectorStatsView.ts | lineNumber": {"message": "第 {PH1}:{PH2} 行"}, "panels/timeline/TimelineSelectorStatsView.ts | matchAttempts": {"message": "尝试匹配次数"}, "panels/timeline/TimelineSelectorStatsView.ts | matchCount": {"message": "匹配数"}, "panels/timeline/TimelineSelectorStatsView.ts | rejectPercentage": {"message": "慢路径不匹配项所占的百分比"}, "panels/timeline/TimelineSelectorStatsView.ts | rejectPercentageExplanation": {"message": "无法通过 Bloom 过滤器快速排除的不匹配节点（匹配尝试次数 - 匹配次数）所占的百分比。占比越小越好。"}, "panels/timeline/TimelineSelectorStatsView.ts | selector": {"message": "选择器"}, "panels/timeline/TimelineSelectorStatsView.ts | selectorStats": {"message": "选择器统计信息"}, "panels/timeline/TimelineSelectorStatsView.ts | styleSheetId": {"message": "样式表"}, "panels/timeline/TimelineSelectorStatsView.ts | tableCopiedToClipboard": {"message": "表格已复制到剪贴板"}, "panels/timeline/TimelineSelectorStatsView.ts | totalForAllSelectors": {"message": "（所有选择器的总计）"}, "panels/timeline/TimelineSelectorStatsView.ts | unableToLink": {"message": "无法关联"}, "panels/timeline/TimelineSelectorStatsView.ts | unableToLinkViaStyleSheetId": {"message": "无法通过 {PH1} 关联"}, "panels/timeline/TimelineTreeView.ts | activity": {"message": "活动"}, "panels/timeline/TimelineTreeView.ts | chromeExtensionsOverhead": {"message": "[Chrome 扩展程序开销]"}, "panels/timeline/TimelineTreeView.ts | fms": {"message": "{PH1} 毫秒"}, "panels/timeline/TimelineTreeView.ts | groupBy": {"message": "分组依据"}, "panels/timeline/TimelineTreeView.ts | groupByActivity": {"message": "按活动分组"}, "panels/timeline/TimelineTreeView.ts | groupByCategory": {"message": "按类别分组"}, "panels/timeline/TimelineTreeView.ts | groupByDomain": {"message": "按网域分组"}, "panels/timeline/TimelineTreeView.ts | groupByFrame": {"message": "按帧分组"}, "panels/timeline/TimelineTreeView.ts | groupBySubdomain": {"message": "按子网域分组"}, "panels/timeline/TimelineTreeView.ts | groupByUrl": {"message": "按网址分组"}, "panels/timeline/TimelineTreeView.ts | heaviestStack": {"message": "执行用时最长的堆栈"}, "panels/timeline/TimelineTreeView.ts | heaviestStackHidden": {"message": "“执行用时最长的堆栈”边栏处于隐藏状态"}, "panels/timeline/TimelineTreeView.ts | heaviestStackShown": {"message": "“执行用时最长的堆栈”边栏处于显示状态"}, "panels/timeline/TimelineTreeView.ts | hideHeaviestStack": {"message": "隐藏“执行用时最长的堆栈”边栏"}, "panels/timeline/TimelineTreeView.ts | matchCase": {"message": "匹配大小写"}, "panels/timeline/TimelineTreeView.ts | matchWholeWord": {"message": "全字匹配"}, "panels/timeline/TimelineTreeView.ts | noGrouping": {"message": "未分组"}, "panels/timeline/TimelineTreeView.ts | page": {"message": "网页"}, "panels/timeline/TimelineTreeView.ts | percentPlaceholder": {"message": "{PH1}%"}, "panels/timeline/TimelineTreeView.ts | performance": {"message": "性能"}, "panels/timeline/TimelineTreeView.ts | selectItemForDetails": {"message": "选择项目即可查看详细信息。"}, "panels/timeline/TimelineTreeView.ts | selfTime": {"message": "自身耗时"}, "panels/timeline/TimelineTreeView.ts | showHeaviestStack": {"message": "显示“执行用时最长的堆栈”边栏"}, "panels/timeline/TimelineTreeView.ts | timelineStack": {"message": "时间轴堆栈"}, "panels/timeline/TimelineTreeView.ts | totalTime": {"message": "总时间"}, "panels/timeline/TimelineTreeView.ts | unattributed": {"message": "[未归因]"}, "panels/timeline/TimelineTreeView.ts | useRegularExpression": {"message": "使用正则表达式"}, "panels/timeline/TimelineTreeView.ts | vRuntime": {"message": "[V8 运行时]"}, "panels/timeline/TimelineUIUtils.ts | FromCache": {"message": " （来自缓存）"}, "panels/timeline/TimelineUIUtils.ts | FromMemoryCache": {"message": " （来自内存缓存）"}, "panels/timeline/TimelineUIUtils.ts | FromPush": {"message": " （来自推送）"}, "panels/timeline/TimelineUIUtils.ts | FromServiceWorker": {"message": " （来自 service worker）"}, "panels/timeline/TimelineUIUtils.ts | SSSResourceLoading": {"message": " （{PH1}{PH2} + {PH3}资源加载）"}, "panels/timeline/TimelineUIUtils.ts | UnknownNode": {"message": "[未知节点]"}, "panels/timeline/TimelineUIUtils.ts | aggregatedTime": {"message": "总时间"}, "panels/timeline/TimelineUIUtils.ts | allottedTime": {"message": "分配的时间"}, "panels/timeline/TimelineUIUtils.ts | animationFrameRequested": {"message": "已请求动画帧"}, "panels/timeline/TimelineUIUtils.ts | callbackFunction": {"message": "回调函数"}, "panels/timeline/TimelineUIUtils.ts | callbackId": {"message": "回调 ID"}, "panels/timeline/TimelineUIUtils.ts | collected": {"message": "已回收"}, "panels/timeline/TimelineUIUtils.ts | compilationCacheKind": {"message": "编译缓存种类"}, "panels/timeline/TimelineUIUtils.ts | compilationCacheSize": {"message": "编译缓存大小"}, "panels/timeline/TimelineUIUtils.ts | compilationCacheStatus": {"message": "编译缓存状态"}, "panels/timeline/TimelineUIUtils.ts | compile": {"message": "编译"}, "panels/timeline/TimelineUIUtils.ts | consumedCacheSize": {"message": "已使用的缓存大小"}, "panels/timeline/TimelineUIUtils.ts | cumulativeLayoutShifts": {"message": "Cumulative Layout Shift"}, "panels/timeline/TimelineUIUtils.ts | cumulativeScore": {"message": "累积分数"}, "panels/timeline/TimelineUIUtils.ts | currentClusterId": {"message": "当前集群 ID"}, "panels/timeline/TimelineUIUtils.ts | currentClusterScore": {"message": "当前集群分数"}, "panels/timeline/TimelineUIUtils.ts | decodedBody": {"message": "经过解码的正文"}, "panels/timeline/TimelineUIUtils.ts | details": {"message": "详细信息"}, "panels/timeline/TimelineUIUtils.ts | dimensions": {"message": "尺寸"}, "panels/timeline/TimelineUIUtils.ts | duration": {"message": "时长"}, "panels/timeline/TimelineUIUtils.ts | eagerCompile": {"message": "及早编译所有函数"}, "panels/timeline/TimelineUIUtils.ts | elementsAffected": {"message": "受影响的元素"}, "panels/timeline/TimelineUIUtils.ts | emptyPlaceholder": {"message": "{PH1}"}, "panels/timeline/TimelineUIUtils.ts | encodedData": {"message": "已编码的数据"}, "panels/timeline/TimelineUIUtils.ts | entryIsHidden": {"message": "（条目已隐藏）"}, "panels/timeline/TimelineUIUtils.ts | evolvedClsLink": {"message": "演变了"}, "panels/timeline/TimelineUIUtils.ts | failedToLoadScriptFromCache": {"message": "无法从缓存加载脚本"}, "panels/timeline/TimelineUIUtils.ts | firstInvalidated": {"message": "首次失效"}, "panels/timeline/TimelineUIUtils.ts | firstLayoutInvalidation": {"message": "首次布局失效"}, "panels/timeline/TimelineUIUtils.ts | frame": {"message": "帧"}, "panels/timeline/TimelineUIUtils.ts | function": {"message": "函数"}, "panels/timeline/TimelineUIUtils.ts | hadRecentInput": {"message": "包含最近输入的内容"}, "panels/timeline/TimelineUIUtils.ts | idleCallbackRequested": {"message": "已请求空闲回调"}, "panels/timeline/TimelineUIUtils.ts | imageUrl": {"message": "图片网址"}, "panels/timeline/TimelineUIUtils.ts | initialPriority": {"message": "初始优先级"}, "panels/timeline/TimelineUIUtils.ts | initiatedBy": {"message": "发起者"}, "panels/timeline/TimelineUIUtils.ts | initiatorFor": {"message": "发起者"}, "panels/timeline/TimelineUIUtils.ts | initiatorStackTrace": {"message": "启动器堆栈轨迹"}, "panels/timeline/TimelineUIUtils.ts | inputDelay": {"message": "输入延迟"}, "panels/timeline/TimelineUIUtils.ts | interactionID": {"message": "ID"}, "panels/timeline/TimelineUIUtils.ts | invalidationWithCallFrame": {"message": "{PH1} ({PH2})"}, "panels/timeline/TimelineUIUtils.ts | invalidations": {"message": "失效内容"}, "panels/timeline/TimelineUIUtils.ts | invokedByTimeout": {"message": "根据超时时长调用"}, "panels/timeline/TimelineUIUtils.ts | layerRoot": {"message": "图层根"}, "panels/timeline/TimelineUIUtils.ts | layoutForced": {"message": "已强制应用布局"}, "panels/timeline/TimelineUIUtils.ts | layoutRoot": {"message": "布局根"}, "panels/timeline/TimelineUIUtils.ts | learnMore": {"message": "了解详情"}, "panels/timeline/TimelineUIUtils.ts | loadFromCache": {"message": "从缓存加载"}, "panels/timeline/TimelineUIUtils.ts | location": {"message": "位置"}, "panels/timeline/TimelineUIUtils.ts | message": {"message": "消息"}, "panels/timeline/TimelineUIUtils.ts | mimeType": {"message": "MIME 类型"}, "panels/timeline/TimelineUIUtils.ts | module": {"message": "模块"}, "panels/timeline/TimelineUIUtils.ts | movedFrom": {"message": "来自："}, "panels/timeline/TimelineUIUtils.ts | movedTo": {"message": "移至："}, "panels/timeline/TimelineUIUtils.ts | networkRequest": {"message": "网络请求"}, "panels/timeline/TimelineUIUtils.ts | networkTransfer": {"message": "网络传输"}, "panels/timeline/TimelineUIUtils.ts | no": {"message": "否"}, "panels/timeline/TimelineUIUtils.ts | nodesThatNeedLayout": {"message": "需要布局的节点"}, "panels/timeline/TimelineUIUtils.ts | outsideBreadcrumbRange": {"message": "（面包屑导航范围之外）"}, "panels/timeline/TimelineUIUtils.ts | ownerElement": {"message": "所有者元素"}, "panels/timeline/TimelineUIUtils.ts | paintProfiler": {"message": "绘制性能剖析器"}, "panels/timeline/TimelineUIUtils.ts | parse": {"message": "解析"}, "panels/timeline/TimelineUIUtils.ts | pendingFor": {"message": "等待"}, "panels/timeline/TimelineUIUtils.ts | presentationDelay": {"message": "展示延迟时间"}, "panels/timeline/TimelineUIUtils.ts | preview": {"message": "预览"}, "panels/timeline/TimelineUIUtils.ts | priority": {"message": "优先级"}, "panels/timeline/TimelineUIUtils.ts | processingDuration": {"message": "处理用时"}, "panels/timeline/TimelineUIUtils.ts | producedCacheSize": {"message": "已产生的缓存大小"}, "panels/timeline/TimelineUIUtils.ts | range": {"message": "范围"}, "panels/timeline/TimelineUIUtils.ts | recalculationForced": {"message": "已强制重新计算"}, "panels/timeline/TimelineUIUtils.ts | relatedNode": {"message": "相关节点"}, "panels/timeline/TimelineUIUtils.ts | repeats": {"message": "重复"}, "panels/timeline/TimelineUIUtils.ts | requestMethod": {"message": "请求方法"}, "panels/timeline/TimelineUIUtils.ts | sAtS": {"message": "{PH2}时显示的{PH1}"}, "panels/timeline/TimelineUIUtils.ts | sAtSParentheses": {"message": "{PH1}（在 {PH2}时）"}, "panels/timeline/TimelineUIUtils.ts | sCLSInformation": {"message": "{PH1} 可能会导致用户体验不佳。此指标最近{PH2}。"}, "panels/timeline/TimelineUIUtils.ts | sChildren": {"message": "{PH1}（子级）"}, "panels/timeline/TimelineUIUtils.ts | sCollected": {"message": "已回收 {PH1}"}, "panels/timeline/TimelineUIUtils.ts | sOfS": {"message": "{PH1}/{PH2}"}, "panels/timeline/TimelineUIUtils.ts | sS": {"message": "{PH1}：{PH2}"}, "panels/timeline/TimelineUIUtils.ts | sSCurlyBrackets": {"message": "（{PH1}、{PH2}）"}, "panels/timeline/TimelineUIUtils.ts | sSDimensions": {"message": "{PH1} × {PH2}"}, "panels/timeline/TimelineUIUtils.ts | sSSquareBrackets": {"message": "{PH1} [{PH2}…]"}, "panels/timeline/TimelineUIUtils.ts | sSelectorStatsInfo": {"message": "选择“{PH1}”可收集详细的 CSS 选择器匹配统计信息。"}, "panels/timeline/TimelineUIUtils.ts | sSelf": {"message": "{PH1}（自身）"}, "panels/timeline/TimelineUIUtils.ts | sSs": {"message": "{PH1} [{PH2}…{PH3}]"}, "panels/timeline/TimelineUIUtils.ts | score": {"message": "得分"}, "panels/timeline/TimelineUIUtils.ts | script": {"message": "脚本"}, "panels/timeline/TimelineUIUtils.ts | scriptLoadedFromCache": {"message": "已从缓存加载脚本"}, "panels/timeline/TimelineUIUtils.ts | scriptNotEligibleToBeLoadedFromCache": {"message": "脚本不符合条件"}, "panels/timeline/TimelineUIUtils.ts | selectorStatsTitle": {"message": "选择器统计信息"}, "panels/timeline/TimelineUIUtils.ts | selfTime": {"message": "自身耗时"}, "panels/timeline/TimelineUIUtils.ts | size": {"message": "大小"}, "panels/timeline/TimelineUIUtils.ts | stackTrace": {"message": "堆栈轨迹"}, "panels/timeline/TimelineUIUtils.ts | state": {"message": "状态"}, "panels/timeline/TimelineUIUtils.ts | streamed": {"message": "流式"}, "panels/timeline/TimelineUIUtils.ts | stylesheetUrl": {"message": "样式表网址"}, "panels/timeline/TimelineUIUtils.ts | timeSpentInRendering": {"message": "渲染耗时"}, "panels/timeline/TimelineUIUtils.ts | timeout": {"message": "超时"}, "panels/timeline/TimelineUIUtils.ts | timerId": {"message": "定时器 ID"}, "panels/timeline/TimelineUIUtils.ts | timerInstalled": {"message": "定时器已安装"}, "panels/timeline/TimelineUIUtils.ts | timestamp": {"message": "时间戳"}, "panels/timeline/TimelineUIUtils.ts | totalTime": {"message": "总时间"}, "panels/timeline/TimelineUIUtils.ts | traceEvent": {"message": "跟踪事件"}, "panels/timeline/TimelineUIUtils.ts | type": {"message": "类型"}, "panels/timeline/TimelineUIUtils.ts | url": {"message": "网址"}, "panels/timeline/TimelineUIUtils.ts | warning": {"message": "警告"}, "panels/timeline/TimelineUIUtils.ts | yes": {"message": "是"}, "panels/timeline/TimingsTrackAppender.ts | timings": {"message": "时间"}, "panels/timeline/UIDevtoolsUtils.ts | async": {"message": "异步"}, "panels/timeline/UIDevtoolsUtils.ts | drawFrame": {"message": "绘制帧"}, "panels/timeline/UIDevtoolsUtils.ts | drawing": {"message": "绘制"}, "panels/timeline/UIDevtoolsUtils.ts | experience": {"message": "体验"}, "panels/timeline/UIDevtoolsUtils.ts | frameStart": {"message": "开始显示帧"}, "panels/timeline/UIDevtoolsUtils.ts | gpu": {"message": "GPU"}, "panels/timeline/UIDevtoolsUtils.ts | idle": {"message": "空闲"}, "panels/timeline/UIDevtoolsUtils.ts | layout": {"message": "布局"}, "panels/timeline/UIDevtoolsUtils.ts | loading": {"message": "加载"}, "panels/timeline/UIDevtoolsUtils.ts | messaging": {"message": "消息功能"}, "panels/timeline/UIDevtoolsUtils.ts | painting": {"message": "正在绘制"}, "panels/timeline/UIDevtoolsUtils.ts | rasterizing": {"message": "正在光栅化"}, "panels/timeline/UIDevtoolsUtils.ts | rendering": {"message": "渲染"}, "panels/timeline/UIDevtoolsUtils.ts | scripting": {"message": "执行脚本"}, "panels/timeline/UIDevtoolsUtils.ts | system": {"message": "系统"}, "panels/timeline/components/DetailsView.ts | forcedReflow": {"message": "已强制自动重排"}, "panels/timeline/components/DetailsView.ts | idleCallbackExecutionExtended": {"message": "空闲回调的执行时间超出截止时间 {PH1}"}, "panels/timeline/components/DetailsView.ts | longInteractionINP": {"message": "互动耗时非常长"}, "panels/timeline/components/DetailsView.ts | longTask": {"message": "长任务"}, "panels/timeline/components/DetailsView.ts | sIsALikelyPerformanceBottleneck": {"message": "{PH1}可能是性能瓶颈。"}, "panels/timeline/components/DetailsView.ts | sIsLikelyPoorPageResponsiveness": {"message": "“{PH1}”表明网页响应速度非常慢。"}, "panels/timeline/components/DetailsView.ts | sTookS": {"message": "{PH1}耗时 {PH2}。"}, "panels/timeline/components/DetailsView.ts | webSocketBytes": {"message": "{PH1} 个字节"}, "panels/timeline/components/DetailsView.ts | webSocketDataLength": {"message": "数据长度"}, "panels/timeline/components/DetailsView.ts | websocketProtocol": {"message": "WebSocket 协议"}, "panels/timeline/components/InteractionBreakdown.ts | inputDelay": {"message": "输入延迟"}, "panels/timeline/components/InteractionBreakdown.ts | presentationDelay": {"message": "展示延迟时间"}, "panels/timeline/components/InteractionBreakdown.ts | processingDuration": {"message": "处理用时"}, "panels/timeline/timeline-meta.ts | hideChromeFrameInLayersView": {"message": "在“图层”视图中隐藏 chrome 框架"}, "panels/timeline/timeline-meta.ts | loadProfile": {"message": "加载性能分析报告…"}, "panels/timeline/timeline-meta.ts | nextFrame": {"message": "下一帧"}, "panels/timeline/timeline-meta.ts | nextRecording": {"message": "下一项录制内容"}, "panels/timeline/timeline-meta.ts | performance": {"message": "性能"}, "panels/timeline/timeline-meta.ts | previousFrame": {"message": "上一帧"}, "panels/timeline/timeline-meta.ts | previousRecording": {"message": "上一项录制内容"}, "panels/timeline/timeline-meta.ts | record": {"message": "录制"}, "panels/timeline/timeline-meta.ts | saveProfile": {"message": "保存性能分析报告…"}, "panels/timeline/timeline-meta.ts | showPerformance": {"message": "显示“性能”工具"}, "panels/timeline/timeline-meta.ts | showRecentTimelineSessions": {"message": "显示近期时间轴会话"}, "panels/timeline/timeline-meta.ts | startProfilingAndReloadPage": {"message": "开始分析并重新加载网页"}, "panels/timeline/timeline-meta.ts | stop": {"message": "停止"}, "panels/web_audio/AudioContextContentBuilder.ts | callbackBufferSize": {"message": "回调缓冲区空间"}, "panels/web_audio/AudioContextContentBuilder.ts | callbackInterval": {"message": "回调时间间隔"}, "panels/web_audio/AudioContextContentBuilder.ts | currentTime": {"message": "当前时间"}, "panels/web_audio/AudioContextContentBuilder.ts | maxOutputChannels": {"message": "最大输出声道"}, "panels/web_audio/AudioContextContentBuilder.ts | renderCapacity": {"message": "渲染能力"}, "panels/web_audio/AudioContextContentBuilder.ts | sampleRate": {"message": "采样率"}, "panels/web_audio/AudioContextContentBuilder.ts | state": {"message": "状态"}, "panels/web_audio/AudioContextSelector.ts | audioContextS": {"message": "音频情境：{PH1}"}, "panels/web_audio/AudioContextSelector.ts | noRecordings": {"message": "（无录制内容）"}, "panels/web_audio/WebAudioView.ts | openAPageThatUsesWebAudioApiTo": {"message": "打开使用 Web Audio API 的网页开始监控。"}, "panels/web_audio/web_audio-meta.ts | audio": {"message": "audio"}, "panels/web_audio/web_audio-meta.ts | showWebaudio": {"message": "显示 WebAudio"}, "panels/web_audio/web_audio-meta.ts | webaudio": {"message": "WebAudio"}, "panels/webauthn/WebauthnPane.ts | actions": {"message": "操作"}, "panels/webauthn/WebauthnPane.ts | active": {"message": "活跃"}, "panels/webauthn/WebauthnPane.ts | add": {"message": "添加"}, "panels/webauthn/WebauthnPane.ts | addAuthenticator": {"message": "添加身份验证器"}, "panels/webauthn/WebauthnPane.ts | authenticatorS": {"message": "身份验证器 {PH1}"}, "panels/webauthn/WebauthnPane.ts | credentials": {"message": "凭据"}, "panels/webauthn/WebauthnPane.ts | editName": {"message": "修改名称"}, "panels/webauthn/WebauthnPane.ts | enableVirtualAuthenticator": {"message": "启用虚拟身份验证器环境"}, "panels/webauthn/WebauthnPane.ts | enterNewName": {"message": "输入新名称"}, "panels/webauthn/WebauthnPane.ts | export": {"message": "导出"}, "panels/webauthn/WebauthnPane.ts | id": {"message": "ID"}, "panels/webauthn/WebauthnPane.ts | isResident": {"message": "为常驻凭据"}, "panels/webauthn/WebauthnPane.ts | learnMore": {"message": "了解详情"}, "panels/webauthn/WebauthnPane.ts | newAuthenticator": {"message": "新建身份验证器"}, "panels/webauthn/WebauthnPane.ts | no": {"message": "否"}, "panels/webauthn/WebauthnPane.ts | noCredentialsTryCallingSFromYour": {"message": "没有凭据。请尝试从您的网站调用 {PH1}。"}, "panels/webauthn/WebauthnPane.ts | privateKeypem": {"message": "私钥.pem"}, "panels/webauthn/WebauthnPane.ts | protocol": {"message": "协议"}, "panels/webauthn/WebauthnPane.ts | remove": {"message": "移除"}, "panels/webauthn/WebauthnPane.ts | rpId": {"message": "依赖方 ID"}, "panels/webauthn/WebauthnPane.ts | saveName": {"message": "保存名称"}, "panels/webauthn/WebauthnPane.ts | setSAsTheActiveAuthenticator": {"message": "将{PH1} 设为有效的身份验证器"}, "panels/webauthn/WebauthnPane.ts | signCount": {"message": "签名数量"}, "panels/webauthn/WebauthnPane.ts | supportsLargeBlob": {"message": "支持大型 blob"}, "panels/webauthn/WebauthnPane.ts | supportsResidentKeys": {"message": "支持常驻密钥"}, "panels/webauthn/WebauthnPane.ts | supportsUserVerification": {"message": "支持用户验证"}, "panels/webauthn/WebauthnPane.ts | transport": {"message": "传输"}, "panels/webauthn/WebauthnPane.ts | useWebauthnForPhishingresistant": {"message": "使用 WebAuthn 进行身份验证以防网上诱骗"}, "panels/webauthn/WebauthnPane.ts | userHandle": {"message": "用户处理"}, "panels/webauthn/WebauthnPane.ts | uuid": {"message": "UUID"}, "panels/webauthn/WebauthnPane.ts | yes": {"message": "是"}, "panels/webauthn/webauthn-meta.ts | showWebauthn": {"message": "显示 WebAuthn"}, "panels/webauthn/webauthn-meta.ts | webauthn": {"message": "WebAuthn"}, "ui/components/data_grid/DataGrid.ts | enterToSort": {"message": "列排序状态：{PH1}。按 Enter 键即可应用排序过滤条件"}, "ui/components/data_grid/DataGrid.ts | headerOptions": {"message": "标头选项"}, "ui/components/data_grid/DataGrid.ts | resetColumns": {"message": "重置列"}, "ui/components/data_grid/DataGrid.ts | sortAsc": {"message": "升序"}, "ui/components/data_grid/DataGrid.ts | sortBy": {"message": "排序依据"}, "ui/components/data_grid/DataGrid.ts | sortDesc": {"message": "降序"}, "ui/components/data_grid/DataGrid.ts | sortNone": {"message": "无"}, "ui/components/data_grid/DataGridController.ts | sortInAscendingOrder": {"message": "{PH1} 目前是按升序排序"}, "ui/components/data_grid/DataGridController.ts | sortInDescendingOrder": {"message": "{PH1} 目前是按降序排序"}, "ui/components/data_grid/DataGridController.ts | sortingCanceled": {"message": "已取消{PH1}排序"}, "ui/components/dialogs/IconDialog.ts | close": {"message": "关闭"}, "ui/components/dialogs/ShortcutDialog.ts | close": {"message": "关闭"}, "ui/components/dialogs/ShortcutDialog.ts | dialogTitle": {"message": "键盘快捷键"}, "ui/components/dialogs/ShortcutDialog.ts | showShortcutTitle": {"message": "显示快捷键"}, "ui/components/diff_view/DiffView.ts | SkippingDMatchingLines": {"message": "（…正在跳过 {PH1} 个匹配行…）"}, "ui/components/diff_view/DiffView.ts | additions": {"message": "添加的项："}, "ui/components/diff_view/DiffView.ts | changesDiffViewer": {"message": "更改差异查看器"}, "ui/components/diff_view/DiffView.ts | deletions": {"message": "删除的项："}, "ui/components/issue_counter/IssueCounter.ts | breakingChanges": {"message": "{issueCount,plural, =1{# 项破坏性更改}other{# 项破坏性更改}}"}, "ui/components/issue_counter/IssueCounter.ts | pageErrors": {"message": "{issueCount,plural, =1{# 个网页错误}other{# 个网页错误}}"}, "ui/components/issue_counter/IssueCounter.ts | possibleImprovements": {"message": "{issueCount,plural, =1{# 个可以改进的问题}other{# 个可以改进的问题}}"}, "ui/components/issue_counter/IssueLinkIcon.ts | clickToShowIssue": {"message": "点击即可在“问题”标签页中显示问题"}, "ui/components/issue_counter/IssueLinkIcon.ts | clickToShowIssueWithTitle": {"message": "点击即可打开“问题”标签页并显示问题“{title}”"}, "ui/components/issue_counter/IssueLinkIcon.ts | issueUnavailable": {"message": "目前无法解决的问题"}, "ui/components/markdown_view/CodeBlock.ts | copied": {"message": "已复制到剪贴板"}, "ui/components/markdown_view/CodeBlock.ts | copy": {"message": "复制代码"}, "ui/components/markdown_view/CodeBlock.ts | disclaimer": {"message": "谨慎使用代码段"}, "ui/components/panel_feedback/FeedbackButton.ts | feedback": {"message": "反馈"}, "ui/components/panel_feedback/PanelFeedback.ts | previewFeature": {"message": "试用型功能"}, "ui/components/panel_feedback/PanelFeedback.ts | previewText": {"message": "我们的团队正在努力完善此功能，期待您与我们分享您的想法。"}, "ui/components/panel_feedback/PanelFeedback.ts | previewTextFeedbackLink": {"message": "向我们提供反馈。"}, "ui/components/panel_feedback/PanelFeedback.ts | videoAndDocumentation": {"message": "视频和文档"}, "ui/components/panel_feedback/PreviewToggle.ts | learnMoreLink": {"message": "了解详情"}, "ui/components/panel_feedback/PreviewToggle.ts | previewTextFeedbackLink": {"message": "向我们提供反馈。"}, "ui/components/panel_feedback/PreviewToggle.ts | shortFeedbackLink": {"message": "发送反馈"}, "ui/components/request_link_icon/RequestLinkIcon.ts | clickToShowRequestInTheNetwork": {"message": "点击即可打开“网络”面板并显示网址请求：{url}"}, "ui/components/request_link_icon/RequestLinkIcon.ts | requestUnavailableInTheNetwork": {"message": "无法在“网络”面板中显示相应请求，请尝试重新加载要检查的网页"}, "ui/components/request_link_icon/RequestLinkIcon.ts | shortenedURL": {"message": "缩短后的网址"}, "ui/components/survey_link/SurveyLink.ts | anErrorOccurredWithTheSurvey": {"message": "调查问卷发生错误"}, "ui/components/survey_link/SurveyLink.ts | openingSurvey": {"message": "正在打开调查问卷…"}, "ui/components/survey_link/SurveyLink.ts | thankYouForYourFeedback": {"message": "感谢您的反馈"}, "ui/components/text_editor/config.ts | codeEditor": {"message": "代码编辑器"}, "ui/components/text_editor/config.ts | sSuggestionSOfS": {"message": "{PH1}，这是第 {PH2} 条建议，共 {PH3} 条"}, "ui/legacy/ActionRegistration.ts | background_services": {"message": "后台服务"}, "ui/legacy/ActionRegistration.ts | changes": {"message": "变更"}, "ui/legacy/ActionRegistration.ts | console": {"message": "控制台"}, "ui/legacy/ActionRegistration.ts | debugger": {"message": "调试程序"}, "ui/legacy/ActionRegistration.ts | drawer": {"message": "抽屉式导航栏"}, "ui/legacy/ActionRegistration.ts | elements": {"message": "元素"}, "ui/legacy/ActionRegistration.ts | global": {"message": "全局"}, "ui/legacy/ActionRegistration.ts | help": {"message": "帮助"}, "ui/legacy/ActionRegistration.ts | javascript_profiler": {"message": "JavaScript 性能剖析器"}, "ui/legacy/ActionRegistration.ts | layers": {"message": "图层"}, "ui/legacy/ActionRegistration.ts | memory": {"message": "内存"}, "ui/legacy/ActionRegistration.ts | mobile": {"message": "移动设备"}, "ui/legacy/ActionRegistration.ts | navigation": {"message": "导航"}, "ui/legacy/ActionRegistration.ts | network": {"message": "网络"}, "ui/legacy/ActionRegistration.ts | performance": {"message": "性能"}, "ui/legacy/ActionRegistration.ts | recorder": {"message": "记录器"}, "ui/legacy/ActionRegistration.ts | rendering": {"message": "渲染"}, "ui/legacy/ActionRegistration.ts | resources": {"message": "资源"}, "ui/legacy/ActionRegistration.ts | screenshot": {"message": "屏幕截图"}, "ui/legacy/ActionRegistration.ts | settings": {"message": "设置"}, "ui/legacy/ActionRegistration.ts | sources": {"message": "来源"}, "ui/legacy/DockController.ts | close": {"message": "关闭"}, "ui/legacy/DockController.ts | devToolsDockedTo": {"message": "开发者工具已停靠至{PH1}"}, "ui/legacy/DockController.ts | devtoolsUndocked": {"message": "已取消停靠开发者工具"}, "ui/legacy/DockController.ts | dockToBottom": {"message": "停靠至底部"}, "ui/legacy/DockController.ts | dockToLeft": {"message": "停靠至左侧"}, "ui/legacy/DockController.ts | dockToRight": {"message": "停靠至右侧"}, "ui/legacy/DockController.ts | undockIntoSeparateWindow": {"message": "取消停靠至单独的窗口"}, "ui/legacy/EmptyWidget.ts | learnMore": {"message": "了解详情"}, "ui/legacy/FilterBar.ts | allStrings": {"message": "全部"}, "ui/legacy/FilterBar.ts | egSmalldUrlacomb": {"message": "例如：/small[d]+/ url:a.com/b"}, "ui/legacy/FilterBar.ts | filter": {"message": "过滤"}, "ui/legacy/FilterBar.ts | sclickToSelectMultipleTypes": {"message": "{PH1}点击可选择多个类型"}, "ui/legacy/Infobar.ts | close": {"message": "关闭"}, "ui/legacy/Infobar.ts | dontShowAgain": {"message": "不再显示"}, "ui/legacy/Infobar.ts | showMore": {"message": "展开"}, "ui/legacy/InspectorView.ts | closeDrawer": {"message": "关闭抽屉栏"}, "ui/legacy/InspectorView.ts | devToolsLanguageMissmatch": {"message": "DevTools 现已支持{PH1}！"}, "ui/legacy/InspectorView.ts | drawer": {"message": "工具抽屉式导航栏"}, "ui/legacy/InspectorView.ts | drawerHidden": {"message": "已隐藏抽屉式导航栏"}, "ui/legacy/InspectorView.ts | drawerShown": {"message": "已显示抽屉式导航栏"}, "ui/legacy/InspectorView.ts | mainToolbar": {"message": "主工具栏"}, "ui/legacy/InspectorView.ts | moreTools": {"message": "更多工具"}, "ui/legacy/InspectorView.ts | moveToBottom": {"message": "移至底部"}, "ui/legacy/InspectorView.ts | moveToTop": {"message": "移至顶部"}, "ui/legacy/InspectorView.ts | panels": {"message": "面板"}, "ui/legacy/InspectorView.ts | reloadDevtools": {"message": "重新加载 DevTools"}, "ui/legacy/InspectorView.ts | selectFolder": {"message": "选择文件夹"}, "ui/legacy/InspectorView.ts | selectOverrideFolder": {"message": "选择要用来存储替换文件的文件夹。"}, "ui/legacy/InspectorView.ts | setToBrowserLanguage": {"message": "始终以 Chrome 所用的语言显示"}, "ui/legacy/InspectorView.ts | setToSpecificLanguage": {"message": "将 DevTools 切换为{PH1}版"}, "ui/legacy/ListWidget.ts | addString": {"message": "添加"}, "ui/legacy/ListWidget.ts | cancelString": {"message": "取消"}, "ui/legacy/ListWidget.ts | changesSaved": {"message": "已保存对项目所做的更改"}, "ui/legacy/ListWidget.ts | editString": {"message": "修改"}, "ui/legacy/ListWidget.ts | removeString": {"message": "移除"}, "ui/legacy/ListWidget.ts | removedItem": {"message": "项目已被移除"}, "ui/legacy/ListWidget.ts | saveString": {"message": "保存"}, "ui/legacy/RemoteDebuggingTerminatedScreen.ts | debuggingConnectionWasClosed": {"message": "调试连接已关闭。原因： "}, "ui/legacy/RemoteDebuggingTerminatedScreen.ts | reconnectDevtools": {"message": "重新连接 DevTools"}, "ui/legacy/RemoteDebuggingTerminatedScreen.ts | reconnectWhenReadyByReopening": {"message": "准备就绪时，打开 DevTools 即可重新连接。"}, "ui/legacy/SearchableView.ts | accessibledOfD": {"message": "显示第 {PH1} 条结果（共 {PH2} 条）"}, "ui/legacy/SearchableView.ts | clearInput": {"message": "清除"}, "ui/legacy/SearchableView.ts | closeSearchBar": {"message": "关闭搜索栏"}, "ui/legacy/SearchableView.ts | dMatches": {"message": "{PH1} 条匹配结果"}, "ui/legacy/SearchableView.ts | dOfD": {"message": "{PH1} 个（共 {PH2} 个）"}, "ui/legacy/SearchableView.ts | disableCaseSensitive": {"message": "停用区分大小写的搜索功能"}, "ui/legacy/SearchableView.ts | disableFindAndReplace": {"message": "停用“查找和替换”功能"}, "ui/legacy/SearchableView.ts | disableRegularExpression": {"message": "停用正则表达式"}, "ui/legacy/SearchableView.ts | enableCaseSensitive": {"message": "启用区分大小写的搜索功能"}, "ui/legacy/SearchableView.ts | enableFindAndReplace": {"message": "查找和替换"}, "ui/legacy/SearchableView.ts | enableRegularExpression": {"message": "启用正则表达式"}, "ui/legacy/SearchableView.ts | findString": {"message": "查找"}, "ui/legacy/SearchableView.ts | matchString": {"message": "1 个匹配项"}, "ui/legacy/SearchableView.ts | replace": {"message": "替换"}, "ui/legacy/SearchableView.ts | replaceAll": {"message": "全部替换"}, "ui/legacy/SearchableView.ts | searchNext": {"message": "显示下一个结果"}, "ui/legacy/SearchableView.ts | searchPrevious": {"message": "显示上一个结果"}, "ui/legacy/SettingsUI.ts | oneOrMoreSettingsHaveChanged": {"message": "一项或多项设置已更改，需要重新加载才能生效。"}, "ui/legacy/SettingsUI.ts | srequiresReload": {"message": "*需要重新加载"}, "ui/legacy/SoftContextMenu.ts | checked": {"message": "已勾选"}, "ui/legacy/SoftContextMenu.ts | sS": {"message": "{PH1}、{PH2}"}, "ui/legacy/SoftContextMenu.ts | sSS": {"message": "{PH1}，{PH2}，{PH3}"}, "ui/legacy/SoftContextMenu.ts | unchecked": {"message": "未选中"}, "ui/legacy/SoftDropDown.ts | noItemSelected": {"message": "（未选择任何条目）"}, "ui/legacy/SuggestBox.ts | sSuggestionSOfS": {"message": "{PH1}，这是第 {PH2} 条建议，共 {PH3} 条"}, "ui/legacy/SuggestBox.ts | sSuggestionSSelected": {"message": "{PH1}，这条建议已被选中"}, "ui/legacy/TabbedPane.ts | close": {"message": "关闭"}, "ui/legacy/TabbedPane.ts | closeAll": {"message": "全部关闭"}, "ui/legacy/TabbedPane.ts | closeOthers": {"message": "关闭其他标签页"}, "ui/legacy/TabbedPane.ts | closeS": {"message": "关闭{PH1}"}, "ui/legacy/TabbedPane.ts | closeTabsToTheRight": {"message": "关闭右侧标签页"}, "ui/legacy/TabbedPane.ts | moreTabs": {"message": "更多标签页"}, "ui/legacy/TabbedPane.ts | previewFeature": {"message": "试用型功能"}, "ui/legacy/TargetCrashedScreen.ts | devtoolsWasDisconnectedFromThe": {"message": "DevTools 与网页的连接已断开。"}, "ui/legacy/TargetCrashedScreen.ts | oncePageIsReloadedDevtoolsWill": {"message": "网页重新加载后，DevTools 会自动重新连接。"}, "ui/legacy/Toolbar.ts | clearInput": {"message": "清除"}, "ui/legacy/Toolbar.ts | filter": {"message": "过滤"}, "ui/legacy/Toolbar.ts | notPressed": {"message": "未按下"}, "ui/legacy/Toolbar.ts | pressed": {"message": "已按下"}, "ui/legacy/UIUtils.ts | anonymous": {"message": "（匿名）"}, "ui/legacy/UIUtils.ts | anotherProfilerIsAlreadyActive": {"message": "已激活另一个性能剖析器"}, "ui/legacy/UIUtils.ts | asyncCall": {"message": "异步调用"}, "ui/legacy/UIUtils.ts | cancel": {"message": "取消"}, "ui/legacy/UIUtils.ts | close": {"message": "关闭"}, "ui/legacy/UIUtils.ts | copyFileName": {"message": "复制文件名"}, "ui/legacy/UIUtils.ts | copyLinkAddress": {"message": "复制链接地址"}, "ui/legacy/UIUtils.ts | ok": {"message": "确定"}, "ui/legacy/UIUtils.ts | openInNewTab": {"message": "在新标签页中打开"}, "ui/legacy/UIUtils.ts | promiseRejectedAsync": {"message": "Promise 遭拒（异步）"}, "ui/legacy/UIUtils.ts | promiseResolvedAsync": {"message": "Promise 已解析（异步）"}, "ui/legacy/ViewManager.ts | sPanel": {"message": "{PH1}面板"}, "ui/legacy/ViewRegistration.ts | drawer": {"message": "抽屉式导航栏"}, "ui/legacy/ViewRegistration.ts | drawer_sidebar": {"message": "抽屉式导航栏边栏"}, "ui/legacy/ViewRegistration.ts | elements": {"message": "元素"}, "ui/legacy/ViewRegistration.ts | network": {"message": "网络"}, "ui/legacy/ViewRegistration.ts | panel": {"message": "面板"}, "ui/legacy/ViewRegistration.ts | settings": {"message": "设置"}, "ui/legacy/ViewRegistration.ts | sources": {"message": "来源"}, "ui/legacy/components/color_picker/ContrastDetails.ts | aa": {"message": "AA"}, "ui/legacy/components/color_picker/ContrastDetails.ts | aaa": {"message": "AAA"}, "ui/legacy/components/color_picker/ContrastDetails.ts | apca": {"message": "APCA"}, "ui/legacy/components/color_picker/ContrastDetails.ts | contrastRatio": {"message": "对比度"}, "ui/legacy/components/color_picker/ContrastDetails.ts | noContrastInformationAvailable": {"message": "没有可用的对比度信息"}, "ui/legacy/components/color_picker/ContrastDetails.ts | pickBackgroundColor": {"message": "选择背景颜色"}, "ui/legacy/components/color_picker/ContrastDetails.ts | placeholderWithColon": {"message": "：{PH1}"}, "ui/legacy/components/color_picker/ContrastDetails.ts | showLess": {"message": "收起"}, "ui/legacy/components/color_picker/ContrastDetails.ts | showMore": {"message": "展开"}, "ui/legacy/components/color_picker/ContrastDetails.ts | toggleBackgroundColorPicker": {"message": "切换背景颜色选择器"}, "ui/legacy/components/color_picker/ContrastDetails.ts | useSuggestedColorStoFixLow": {"message": "使用建议的颜色 {PH1} 修正低对比度问题"}, "ui/legacy/components/color_picker/FormatPickerContextMenu.ts | colorClippedTooltipText": {"message": "为了与格式的色域匹配，系统移除了此颜色。实际结果为 {PH1}"}, "ui/legacy/components/color_picker/Spectrum.ts | addToPalette": {"message": "添加到调色板"}, "ui/legacy/components/color_picker/Spectrum.ts | changeAlpha": {"message": "更改 Alpha"}, "ui/legacy/components/color_picker/Spectrum.ts | changeColorFormat": {"message": "更改颜色格式"}, "ui/legacy/components/color_picker/Spectrum.ts | changeHue": {"message": "更改色调"}, "ui/legacy/components/color_picker/Spectrum.ts | clearPalette": {"message": "清除调色板"}, "ui/legacy/components/color_picker/Spectrum.ts | colorPalettes": {"message": "调色板"}, "ui/legacy/components/color_picker/Spectrum.ts | colorS": {"message": "颜色 {PH1}"}, "ui/legacy/components/color_picker/Spectrum.ts | copyColorToClipboard": {"message": "将颜色复制到剪贴板"}, "ui/legacy/components/color_picker/Spectrum.ts | hex": {"message": "十六进制"}, "ui/legacy/components/color_picker/Spectrum.ts | longclickOrLongpressSpaceToShow": {"message": "长按空格键即可显示 {PH1} 的替代阴影"}, "ui/legacy/components/color_picker/Spectrum.ts | pressArrowKeysMessage": {"message": "按箭头键（无论是否带辅助键）可移动色样位置。将箭头键与 Shift 键搭配使用可大幅移动位置，将箭头键与 Ctrl 键搭配使用可缩小位移幅度，将箭头键与 Alt 键搭配使用可进一步缩小位移幅度"}, "ui/legacy/components/color_picker/Spectrum.ts | previewPalettes": {"message": "预览调色板"}, "ui/legacy/components/color_picker/Spectrum.ts | removeAllToTheRight": {"message": "移除右侧的所有颜色"}, "ui/legacy/components/color_picker/Spectrum.ts | removeColor": {"message": "移除颜色"}, "ui/legacy/components/color_picker/Spectrum.ts | returnToColorPicker": {"message": "返回颜色选择器"}, "ui/legacy/components/color_picker/Spectrum.ts | sInS": {"message": "{PH2} 中的 {PH1}"}, "ui/legacy/components/color_picker/Spectrum.ts | toggleColorPicker": {"message": "颜色提取器 [{PH1}]"}, "ui/legacy/components/cookie_table/CookiesTable.ts | cookies": {"message": "<PERSON><PERSON>"}, "ui/legacy/components/cookie_table/CookiesTable.ts | editableCookies": {"message": "可修改的 <PERSON>ie"}, "ui/legacy/components/cookie_table/CookiesTable.ts | na": {"message": "不适用"}, "ui/legacy/components/cookie_table/CookiesTable.ts | name": {"message": "名称"}, "ui/legacy/components/cookie_table/CookiesTable.ts | opaquePartitionKey": {"message": "（不透明）"}, "ui/legacy/components/cookie_table/CookiesTable.ts | session": {"message": "会话"}, "ui/legacy/components/cookie_table/CookiesTable.ts | showIssueAssociatedWithThis": {"message": "显示与此 Cookie 相关的问题"}, "ui/legacy/components/cookie_table/CookiesTable.ts | showRequestsWithThisCookie": {"message": "显示涉及此 Cookie 的请求"}, "ui/legacy/components/cookie_table/CookiesTable.ts | size": {"message": "大小"}, "ui/legacy/components/cookie_table/CookiesTable.ts | sourcePortTooltip": {"message": "显示先前设定 Cookie 的来源端口（范围为 1-65535）。如果端口未知，则显示为 -1。"}, "ui/legacy/components/cookie_table/CookiesTable.ts | sourceSchemeTooltip": {"message": "显示先前设定 Cookie 的来源架构（Secure、NonSecure）。如果架构未知，则显示为“Unset”。"}, "ui/legacy/components/cookie_table/CookiesTable.ts | timeAfter": {"message": "{date}之后"}, "ui/legacy/components/cookie_table/CookiesTable.ts | timeAfterTooltip": {"message": "到期日期时间戳是 {seconds}，对应于 {date} 之后的一个日期"}, "ui/legacy/components/cookie_table/CookiesTable.ts | value": {"message": "值"}, "ui/legacy/components/data_grid/DataGrid.ts | addNew": {"message": "新增"}, "ui/legacy/components/data_grid/DataGrid.ts | checked": {"message": "已勾选"}, "ui/legacy/components/data_grid/DataGrid.ts | collapsed": {"message": "已收起"}, "ui/legacy/components/data_grid/DataGrid.ts | delete": {"message": "删除"}, "ui/legacy/components/data_grid/DataGrid.ts | editS": {"message": "修改“{PH1}”"}, "ui/legacy/components/data_grid/DataGrid.ts | emptyRowCreated": {"message": "已创建一个空的表格行。您可以双击此行或使用上下文菜单进行编辑。"}, "ui/legacy/components/data_grid/DataGrid.ts | expanded": {"message": "已展开"}, "ui/legacy/components/data_grid/DataGrid.ts | headerOptions": {"message": "标头选项"}, "ui/legacy/components/data_grid/DataGrid.ts | levelS": {"message": "级别 {PH1}"}, "ui/legacy/components/data_grid/DataGrid.ts | refresh": {"message": "刷新"}, "ui/legacy/components/data_grid/DataGrid.ts | resetColumns": {"message": "重置列"}, "ui/legacy/components/data_grid/DataGrid.ts | rowsS": {"message": "行数：{PH1}"}, "ui/legacy/components/data_grid/DataGrid.ts | sRowS": {"message": "{PH1}行{PH2}"}, "ui/legacy/components/data_grid/DataGrid.ts | sSUseTheUpAndDownArrowKeysTo": {"message": "{PH1} {PH2}，使用向上和向下箭头键可浏览表格中的各行并与之交互；使用浏览模式可逐个读取单元格。"}, "ui/legacy/components/data_grid/DataGrid.ts | sortByString": {"message": "排序依据"}, "ui/legacy/components/data_grid/ShowMoreDataGridNode.ts | showAllD": {"message": "显示全部 {PH1} 项"}, "ui/legacy/components/data_grid/ShowMoreDataGridNode.ts | showDAfter": {"message": "显示后 {PH1} 项"}, "ui/legacy/components/data_grid/ShowMoreDataGridNode.ts | showDBefore": {"message": "显示前 {PH1} 项"}, "ui/legacy/components/inline_editor/CSSShadowEditor.ts | blur": {"message": "模糊"}, "ui/legacy/components/inline_editor/CSSShadowEditor.ts | spread": {"message": "扩展"}, "ui/legacy/components/inline_editor/CSSShadowEditor.ts | type": {"message": "类型"}, "ui/legacy/components/inline_editor/CSSShadowEditor.ts | xOffset": {"message": "X 轴偏移"}, "ui/legacy/components/inline_editor/CSSShadowEditor.ts | yOffset": {"message": "Y 轴偏移"}, "ui/legacy/components/inline_editor/ColorSwatch.ts | shiftclickToChangeColorFormat": {"message": "按住 Shift 并点击即可更改颜色格式"}, "ui/legacy/components/inline_editor/FontEditor.ts | PleaseEnterAValidValueForSText": {"message": "* 请为 {PH1} 文本输入指定有效值"}, "ui/legacy/components/inline_editor/FontEditor.ts | cssProperties": {"message": "CSS 属性"}, "ui/legacy/components/inline_editor/FontEditor.ts | deleteS": {"message": "删除 {PH1}"}, "ui/legacy/components/inline_editor/FontEditor.ts | fallbackS": {"message": "后备选择器 {PH1}"}, "ui/legacy/components/inline_editor/FontEditor.ts | fontFamily": {"message": "字体系列"}, "ui/legacy/components/inline_editor/FontEditor.ts | fontSelectorDeletedAtIndexS": {"message": "已删除索引 {PH1} 处的字体选择器"}, "ui/legacy/components/inline_editor/FontEditor.ts | fontSize": {"message": "字号"}, "ui/legacy/components/inline_editor/FontEditor.ts | fontWeight": {"message": "字体粗细"}, "ui/legacy/components/inline_editor/FontEditor.ts | lineHeight": {"message": "行高"}, "ui/legacy/components/inline_editor/FontEditor.ts | sKeyValueSelector": {"message": "{PH1}键值对选择器"}, "ui/legacy/components/inline_editor/FontEditor.ts | sSliderInput": {"message": "{PH1}滑块输入"}, "ui/legacy/components/inline_editor/FontEditor.ts | sTextInput": {"message": "{PH1} 文本输入"}, "ui/legacy/components/inline_editor/FontEditor.ts | sToggleInputType": {"message": "{PH1} 切换输入类型"}, "ui/legacy/components/inline_editor/FontEditor.ts | sUnitInput": {"message": "{PH1}单位输入"}, "ui/legacy/components/inline_editor/FontEditor.ts | selectorInputMode": {"message": "选择器输入法"}, "ui/legacy/components/inline_editor/FontEditor.ts | sliderInputMode": {"message": "滑块输入法"}, "ui/legacy/components/inline_editor/FontEditor.ts | spacing": {"message": "间距"}, "ui/legacy/components/inline_editor/FontEditor.ts | thereIsNoValueToDeleteAtIndexS": {"message": "索引 {PH1} 处没有值可以删除"}, "ui/legacy/components/inline_editor/FontEditor.ts | thisPropertyIsSetToContainUnits": {"message": "此属性设为包含单元，但没有已定义的相应 unitsArray：{PH1}"}, "ui/legacy/components/inline_editor/FontEditor.ts | units": {"message": "单位"}, "ui/legacy/components/inline_editor/LinkSwatch.ts | sIsNotDefined": {"message": "{PH1} 未定义"}, "ui/legacy/components/object_ui/CustomPreviewComponent.ts | showAsJavascriptObject": {"message": "显示为 JavaScript 对象"}, "ui/legacy/components/object_ui/ObjectPopoverHelper.ts | learnMore": {"message": "了解详情"}, "ui/legacy/components/object_ui/ObjectPropertiesSection.ts | collapseChildren": {"message": "收起子级"}, "ui/legacy/components/object_ui/ObjectPropertiesSection.ts | copy": {"message": "复制"}, "ui/legacy/components/object_ui/ObjectPropertiesSection.ts | copyPropertyPath": {"message": "复制属性路径"}, "ui/legacy/components/object_ui/ObjectPropertiesSection.ts | copyValue": {"message": "复制值"}, "ui/legacy/components/object_ui/ObjectPropertiesSection.ts | dots": {"message": "（…）"}, "ui/legacy/components/object_ui/ObjectPropertiesSection.ts | exceptionS": {"message": "[异常：{PH1}]"}, "ui/legacy/components/object_ui/ObjectPropertiesSection.ts | expandRecursively": {"message": "以递归方式展开"}, "ui/legacy/components/object_ui/ObjectPropertiesSection.ts | invokePropertyGetter": {"message": "调用属性 getter"}, "ui/legacy/components/object_ui/ObjectPropertiesSection.ts | longTextWasTruncatedS": {"message": "长文本被截断 ({PH1})"}, "ui/legacy/components/object_ui/ObjectPropertiesSection.ts | noProperties": {"message": "无属性"}, "ui/legacy/components/object_ui/ObjectPropertiesSection.ts | revealInMemoryInpector": {"message": "在“内存检查器”面板中显示"}, "ui/legacy/components/object_ui/ObjectPropertiesSection.ts | showAllD": {"message": "显示全部 {PH1} 项"}, "ui/legacy/components/object_ui/ObjectPropertiesSection.ts | showMoreS": {"message": "显示更多 ({PH1})"}, "ui/legacy/components/object_ui/ObjectPropertiesSection.ts | stringIsTooLargeToEdit": {"message": "<字符串过长，无法修改>"}, "ui/legacy/components/object_ui/ObjectPropertiesSection.ts | unknown": {"message": "未知"}, "ui/legacy/components/object_ui/ObjectPropertiesSection.ts | valueNotAccessibleToTheDebugger": {"message": "调试程序无法获取值"}, "ui/legacy/components/object_ui/ObjectPropertiesSection.ts | valueUnavailable": {"message": "<没有可用的值>"}, "ui/legacy/components/object_ui/RemoteObjectPreviewFormatter.ts | empty": {"message": "空白"}, "ui/legacy/components/object_ui/RemoteObjectPreviewFormatter.ts | emptyD": {"message": "空属性 × {PH1}"}, "ui/legacy/components/object_ui/RemoteObjectPreviewFormatter.ts | thePropertyIsComputedWithAGetter": {"message": "此属性使用 getter 计算得出"}, "ui/legacy/components/perf_ui/BrickBreaker.ts | congrats": {"message": "恭喜，您赢了！"}, "ui/legacy/components/perf_ui/BrickBreaker.ts | ps": {"message": "附注：您还可以输入 fixme 来打开游戏"}, "ui/legacy/components/perf_ui/FilmStripView.ts | doubleclickToZoomImageClickTo": {"message": "双击即可缩放图片。点击即可查看之前的请求。"}, "ui/legacy/components/perf_ui/FilmStripView.ts | nextFrame": {"message": "下一帧"}, "ui/legacy/components/perf_ui/FilmStripView.ts | previousFrame": {"message": "上一帧"}, "ui/legacy/components/perf_ui/FilmStripView.ts | screenshot": {"message": "屏幕截图"}, "ui/legacy/components/perf_ui/FilmStripView.ts | screenshotForSSelectToView": {"message": "{PH1}的屏幕截图 - 选择即可查看先前的请求。"}, "ui/legacy/components/perf_ui/FlameChart.ts | addScriptToIgnoreList": {"message": "向忽略列表添加脚本"}, "ui/legacy/components/perf_ui/FlameChart.ts | enterTrackConfigurationMode": {"message": "配置轨道…"}, "ui/legacy/components/perf_ui/FlameChart.ts | exitTrackConfigurationMode": {"message": "完成轨道配置"}, "ui/legacy/components/perf_ui/FlameChart.ts | flameChart": {"message": "火焰图"}, "ui/legacy/components/perf_ui/FlameChart.ts | hideChildren": {"message": "隐藏子项"}, "ui/legacy/components/perf_ui/FlameChart.ts | hideFunction": {"message": "隐藏函数"}, "ui/legacy/components/perf_ui/FlameChart.ts | hideRepeatingChildren": {"message": "隐藏重复的子项"}, "ui/legacy/components/perf_ui/FlameChart.ts | removeScriptFromIgnoreList": {"message": "从忽略列表中移除脚本"}, "ui/legacy/components/perf_ui/FlameChart.ts | resetChildren": {"message": "重置子项"}, "ui/legacy/components/perf_ui/FlameChart.ts | resetTrace": {"message": "重置跟踪记录"}, "ui/legacy/components/perf_ui/FlameChart.ts | sCollapsed": {"message": "已收起{PH1}"}, "ui/legacy/components/perf_ui/FlameChart.ts | sExpanded": {"message": "已展开{PH1}"}, "ui/legacy/components/perf_ui/FlameChart.ts | sHovered": {"message": "已悬停在“{PH1}”上"}, "ui/legacy/components/perf_ui/FlameChart.ts | sSelected": {"message": "已选择“{PH1}”"}, "ui/legacy/components/perf_ui/NetworkPriorities.ts | high": {"message": "高"}, "ui/legacy/components/perf_ui/NetworkPriorities.ts | highest": {"message": "最高"}, "ui/legacy/components/perf_ui/NetworkPriorities.ts | low": {"message": "低"}, "ui/legacy/components/perf_ui/NetworkPriorities.ts | lowest": {"message": "最低"}, "ui/legacy/components/perf_ui/NetworkPriorities.ts | medium": {"message": "中"}, "ui/legacy/components/perf_ui/OverviewGrid.ts | leftResizer": {"message": "左窗口调整器"}, "ui/legacy/components/perf_ui/OverviewGrid.ts | overviewGridWindow": {"message": "概览网格窗口"}, "ui/legacy/components/perf_ui/OverviewGrid.ts | rightResizer": {"message": "右窗口大小调整器"}, "ui/legacy/components/perf_ui/PieChart.ts | total": {"message": "总计"}, "ui/legacy/components/perf_ui/perf_ui-meta.ts | collectGarbage": {"message": "回收垃圾"}, "ui/legacy/components/perf_ui/perf_ui-meta.ts | flamechartMouseWheelAction": {"message": "火焰图鼠标滚轮操作："}, "ui/legacy/components/perf_ui/perf_ui-meta.ts | hideLiveMemoryAllocation": {"message": "隐藏实时内存分配情况注解"}, "ui/legacy/components/perf_ui/perf_ui-meta.ts | liveMemoryAllocationAnnotations": {"message": "实时内存分配情况注解"}, "ui/legacy/components/perf_ui/perf_ui-meta.ts | scroll": {"message": "滚动"}, "ui/legacy/components/perf_ui/perf_ui-meta.ts | showLiveMemoryAllocation": {"message": "显示实时内存分配情况注解"}, "ui/legacy/components/perf_ui/perf_ui-meta.ts | zoom": {"message": "缩放"}, "ui/legacy/components/quick_open/CommandMenu.ts | command": {"message": "命令"}, "ui/legacy/components/quick_open/CommandMenu.ts | deprecated": {"message": "- 已被弃用"}, "ui/legacy/components/quick_open/CommandMenu.ts | noCommandsFound": {"message": "找不到任何命令"}, "ui/legacy/components/quick_open/CommandMenu.ts | oneOrMoreSettingsHaveChanged": {"message": "一项或多项设置已更改，需要重新加载才能生效。"}, "ui/legacy/components/quick_open/CommandMenu.ts | run": {"message": "运行"}, "ui/legacy/components/quick_open/FilteredListWidget.ts | noResultsFound": {"message": "未找到任何结果"}, "ui/legacy/components/quick_open/FilteredListWidget.ts | quickOpen": {"message": "快速打开"}, "ui/legacy/components/quick_open/FilteredListWidget.ts | quickOpenPrompt": {"message": "快速打开提示"}, "ui/legacy/components/quick_open/FilteredListWidget.ts | sItemSOfS": {"message": "{PH1}，第 {PH2} 项（共 {PH3} 项）"}, "ui/legacy/components/quick_open/QuickOpen.ts | typeToSeeAvailableCommands": {"message": "输入“?”即可查看可用命令"}, "ui/legacy/components/quick_open/quick_open-meta.ts | openFile": {"message": "打开文件"}, "ui/legacy/components/quick_open/quick_open-meta.ts | runCommand": {"message": "运行命令"}, "ui/legacy/components/source_frame/FontView.ts | font": {"message": "字体"}, "ui/legacy/components/source_frame/FontView.ts | previewOfFontFromS": {"message": "来自 {PH1} 的字体的预览"}, "ui/legacy/components/source_frame/ImageView.ts | copyImageAsDataUri": {"message": "以数据 URI 格式复制图片"}, "ui/legacy/components/source_frame/ImageView.ts | copyImageUrl": {"message": "复制图片网址"}, "ui/legacy/components/source_frame/ImageView.ts | dD": {"message": "{PH1} × {PH2}"}, "ui/legacy/components/source_frame/ImageView.ts | download": {"message": "download"}, "ui/legacy/components/source_frame/ImageView.ts | dropImageFileHere": {"message": "将图片文件拖放到此处"}, "ui/legacy/components/source_frame/ImageView.ts | image": {"message": "图片"}, "ui/legacy/components/source_frame/ImageView.ts | imageFromS": {"message": "图片来自 {PH1}"}, "ui/legacy/components/source_frame/ImageView.ts | openImageInNewTab": {"message": "在新标签页中打开图片"}, "ui/legacy/components/source_frame/ImageView.ts | saveImageAs": {"message": "图片另存为…"}, "ui/legacy/components/source_frame/JSONView.ts | find": {"message": "查找"}, "ui/legacy/components/source_frame/PreviewFactory.ts | nothingToPreview": {"message": "没有可预览的内容"}, "ui/legacy/components/source_frame/ResourceSourceFrame.ts | find": {"message": "查找"}, "ui/legacy/components/source_frame/SourceFrame.ts | allow": {"message": "允许"}, "ui/legacy/components/source_frame/SourceFrame.ts | allowPasting": {"message": "允许粘贴"}, "ui/legacy/components/source_frame/SourceFrame.ts | bytecodePositionXs": {"message": "字节码位置 0x{PH1}"}, "ui/legacy/components/source_frame/SourceFrame.ts | cancel": {"message": "取消"}, "ui/legacy/components/source_frame/SourceFrame.ts | dCharactersSelected": {"message": "已选择 {PH1} 个字符"}, "ui/legacy/components/source_frame/SourceFrame.ts | dLinesDCharactersSelected": {"message": "已选择 {PH1} 行，{PH2} 个字符"}, "ui/legacy/components/source_frame/SourceFrame.ts | dSelectionRegions": {"message": "{PH1} 个所选区域"}, "ui/legacy/components/source_frame/SourceFrame.ts | doNotPaste": {"message": "请勿将您不理解或未自行检查的代码粘贴到开发者工具中。这可能会导致攻击者趁机窃取您的身份信息或控制您的计算机。请在下方输入“{PH1}”以允许粘贴。"}, "ui/legacy/components/source_frame/SourceFrame.ts | doYouTrustThisCode": {"message": "您信任此代码吗？"}, "ui/legacy/components/source_frame/SourceFrame.ts | lineSColumnS": {"message": "第 {PH1} 行，第 {PH2} 列"}, "ui/legacy/components/source_frame/SourceFrame.ts | loading": {"message": "正在加载…"}, "ui/legacy/components/source_frame/SourceFrame.ts | prettyPrint": {"message": "美观输出"}, "ui/legacy/components/source_frame/SourceFrame.ts | source": {"message": "来源"}, "ui/legacy/components/source_frame/SourceFrame.ts | typeAllowPasting": {"message": "输入“{PH1}”"}, "ui/legacy/components/source_frame/XMLView.ts | find": {"message": "查找"}, "ui/legacy/components/source_frame/source_frame-meta.ts | Spaces": {"message": "2 个空格"}, "ui/legacy/components/source_frame/source_frame-meta.ts | defaultIndentation": {"message": "默认缩进："}, "ui/legacy/components/source_frame/source_frame-meta.ts | eSpaces": {"message": "8 个空格"}, "ui/legacy/components/source_frame/source_frame-meta.ts | fSpaces": {"message": "4 个空格"}, "ui/legacy/components/source_frame/source_frame-meta.ts | setIndentationToESpaces": {"message": "将缩进设为 8 个空格"}, "ui/legacy/components/source_frame/source_frame-meta.ts | setIndentationToFSpaces": {"message": "将缩进设为 4 个空格"}, "ui/legacy/components/source_frame/source_frame-meta.ts | setIndentationToSpaces": {"message": "将缩进设为 2 个空格"}, "ui/legacy/components/source_frame/source_frame-meta.ts | setIndentationToTabCharacter": {"message": "将缩进快捷键设置为制表符"}, "ui/legacy/components/source_frame/source_frame-meta.ts | tabCharacter": {"message": "制表符"}, "ui/legacy/components/utils/ImagePreview.ts | currentSource": {"message": "当前来源："}, "ui/legacy/components/utils/ImagePreview.ts | fileSize": {"message": "文件大小："}, "ui/legacy/components/utils/ImagePreview.ts | imageFromS": {"message": "图片来自 {PH1}"}, "ui/legacy/components/utils/ImagePreview.ts | intrinsicAspectRatio": {"message": "固定宽高比："}, "ui/legacy/components/utils/ImagePreview.ts | intrinsicSize": {"message": "固定尺寸："}, "ui/legacy/components/utils/ImagePreview.ts | renderedAspectRatio": {"message": "渲染时的宽高比："}, "ui/legacy/components/utils/ImagePreview.ts | renderedSize": {"message": "渲染的大小："}, "ui/legacy/components/utils/ImagePreview.ts | unknownSource": {"message": "未知来源"}, "ui/legacy/components/utils/JSPresentationUtils.ts | addToIgnore": {"message": "向忽略列表添加脚本"}, "ui/legacy/components/utils/JSPresentationUtils.ts | removeFromIgnore": {"message": "从忽略列表中移除"}, "ui/legacy/components/utils/JSPresentationUtils.ts | showLess": {"message": "收起"}, "ui/legacy/components/utils/JSPresentationUtils.ts | showSMoreFrames": {"message": "{n,plural, =1{显示另外 # 个框架}other{显示另外 # 个框架}}"}, "ui/legacy/components/utils/JSPresentationUtils.ts | unknownSource": {"message": "未知"}, "ui/legacy/components/utils/Linkifier.ts | auto": {"message": "自动"}, "ui/legacy/components/utils/Linkifier.ts | linkHandling": {"message": "链接处理："}, "ui/legacy/components/utils/Linkifier.ts | openUsingS": {"message": "使用{PH1}打开"}, "ui/legacy/components/utils/Linkifier.ts | reveal": {"message": "显示"}, "ui/legacy/components/utils/Linkifier.ts | revealInS": {"message": "在{PH1}中显示"}, "ui/legacy/components/utils/Linkifier.ts | unknown": {"message": "（未知）"}, "ui/legacy/components/utils/TargetDetachedDialog.ts | websocketDisconnected": {"message": "WebSocket 已断开连接"}}