"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react");function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var u=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,u.get?u:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var n=t(e),a=u(r);exports.MultiMaterial=function(e){const r=a.useRef(null);return a.useLayoutEffect((()=>{var e;const t=null==(e=r.current)?void 0:e.parent,u=null==t?void 0:t.geometry;if(u){const e=t.material;t.material=r.current.__r3f.objects;const n=[...u.groups];return u.clearGroups(),t.material.forEach(((e,r)=>{r<t.material.length-1&&(e.depthWrite=!1),u.addGroup(0,1/0,r)})),()=>{t.material=e,u.groups=n}}})),a.createElement("group",n.default({ref:r},e))};
