"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("three"),r=require("react");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var c=n(e),a=u(t),l=u(r);const i=l.forwardRef((({children:e,width:t,height:r,depth:n,box3:u,precise:i=!0,...o},f)=>{const s=l.useRef(null),d=l.useRef(null),m=l.useRef(null);return l.useLayoutEffect((()=>{d.current.matrixWorld.identity();let e=u||(new a.Box3).setFromObject(m.current,i);const c=e.max.x-e.min.x,l=e.max.y-e.min.y,o=e.max.z-e.min.z;let f=Math.max(c,l,o);t&&(f=c),r&&(f=l),n&&(f=o),d.current.scale.setScalar(1/f)}),[t,r,n,u,i]),l.useImperativeHandle(f,(()=>s.current),[]),l.createElement("group",c.default({ref:s},o),l.createElement("group",{ref:d},l.createElement("group",{ref:m},e)))}));exports.Resize=i;
