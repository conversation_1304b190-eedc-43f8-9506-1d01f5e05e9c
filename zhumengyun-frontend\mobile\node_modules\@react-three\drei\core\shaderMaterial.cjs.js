"use strict";function e(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var s=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,s.get?s:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}Object.defineProperty(exports,"__esModule",{value:!0});var t=e(require("three"));exports.shaderMaterial=function(e,r,s,n){const i=class extends t.ShaderMaterial{constructor(i={}){const a=Object.entries(e);super({uniforms:a.reduce(((e,[r,s])=>({...e,...t.UniformsUtils.clone({[r]:{value:s}})})),{}),vertexShader:r,fragmentShader:s}),this.key="",a.forEach((([e])=>Object.defineProperty(this,e,{get:()=>this.uniforms[e].value,set:t=>this.uniforms[e].value=t}))),Object.assign(this,i),n&&n(this)}};return i.key=t.MathUtils.generateUUID(),i};
