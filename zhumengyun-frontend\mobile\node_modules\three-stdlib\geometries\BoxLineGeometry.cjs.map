{"version": 3, "file": "BoxLineGeometry.cjs", "sources": ["../../src/geometries/BoxLineGeometry.js"], "sourcesContent": ["import { BufferGeometry, Float32BufferAttribute } from 'three'\n\nclass BoxLineGeometry extends BufferGeometry {\n  constructor(width, height, depth, widthSegments, heightSegments, depthSegments) {\n    super()\n\n    width = width || 1\n    height = height || 1\n    depth = depth || 1\n\n    widthSegments = Math.floor(widthSegments) || 1\n    heightSegments = Math.floor(heightSegments) || 1\n    depthSegments = Math.floor(depthSegments) || 1\n\n    const widthHalf = width / 2\n    const heightHalf = height / 2\n    const depthHalf = depth / 2\n\n    const segmentWidth = width / widthSegments\n    const segmentHeight = height / heightSegments\n    const segmentDepth = depth / depthSegments\n\n    const vertices = []\n\n    let x = -widthHalf,\n      y = -heightHalf,\n      z = -depthHalf\n\n    for (let i = 0; i <= widthSegments; i++) {\n      vertices.push(x, -heightHalf, -depthHalf, x, heightHalf, -depthHalf)\n      vertices.push(x, heightHalf, -depthHalf, x, heightHalf, depthHalf)\n      vertices.push(x, heightHalf, depthHalf, x, -heightHalf, depthHalf)\n      vertices.push(x, -heightHalf, depthHalf, x, -heightHalf, -depthHalf)\n\n      x += segmentWidth\n    }\n\n    for (let i = 0; i <= heightSegments; i++) {\n      vertices.push(-widthHalf, y, -depthHalf, widthHalf, y, -depthHalf)\n      vertices.push(widthHalf, y, -depthHalf, widthHalf, y, depthHalf)\n      vertices.push(widthHalf, y, depthHalf, -widthHalf, y, depthHalf)\n      vertices.push(-widthHalf, y, depthHalf, -widthHalf, y, -depthHalf)\n\n      y += segmentHeight\n    }\n\n    for (let i = 0; i <= depthSegments; i++) {\n      vertices.push(-widthHalf, -heightHalf, z, -widthHalf, heightHalf, z)\n      vertices.push(-widthHalf, heightHalf, z, widthHalf, heightHalf, z)\n      vertices.push(widthHalf, heightHalf, z, widthHalf, -heightHalf, z)\n      vertices.push(widthHalf, -heightHalf, z, -widthHalf, -heightHalf, z)\n\n      z += segmentDepth\n    }\n\n    this.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n  }\n}\n\nexport { BoxLineGeometry }\n"], "names": ["BufferGeometry", "Float32BufferAttribute"], "mappings": ";;;AAEA,MAAM,wBAAwBA,MAAAA,eAAe;AAAA,EAC3C,YAAY,OAAO,QAAQ,OAAO,eAAe,gBAAgB,eAAe;AAC9E,UAAO;AAEP,YAAQ,SAAS;AACjB,aAAS,UAAU;AACnB,YAAQ,SAAS;AAEjB,oBAAgB,KAAK,MAAM,aAAa,KAAK;AAC7C,qBAAiB,KAAK,MAAM,cAAc,KAAK;AAC/C,oBAAgB,KAAK,MAAM,aAAa,KAAK;AAE7C,UAAM,YAAY,QAAQ;AAC1B,UAAM,aAAa,SAAS;AAC5B,UAAM,YAAY,QAAQ;AAE1B,UAAM,eAAe,QAAQ;AAC7B,UAAM,gBAAgB,SAAS;AAC/B,UAAM,eAAe,QAAQ;AAE7B,UAAM,WAAW,CAAE;AAEnB,QAAI,IAAI,CAAC,WACP,IAAI,CAAC,YACL,IAAI,CAAC;AAEP,aAAS,IAAI,GAAG,KAAK,eAAe,KAAK;AACvC,eAAS,KAAK,GAAG,CAAC,YAAY,CAAC,WAAW,GAAG,YAAY,CAAC,SAAS;AACnE,eAAS,KAAK,GAAG,YAAY,CAAC,WAAW,GAAG,YAAY,SAAS;AACjE,eAAS,KAAK,GAAG,YAAY,WAAW,GAAG,CAAC,YAAY,SAAS;AACjE,eAAS,KAAK,GAAG,CAAC,YAAY,WAAW,GAAG,CAAC,YAAY,CAAC,SAAS;AAEnE,WAAK;AAAA,IACN;AAED,aAAS,IAAI,GAAG,KAAK,gBAAgB,KAAK;AACxC,eAAS,KAAK,CAAC,WAAW,GAAG,CAAC,WAAW,WAAW,GAAG,CAAC,SAAS;AACjE,eAAS,KAAK,WAAW,GAAG,CAAC,WAAW,WAAW,GAAG,SAAS;AAC/D,eAAS,KAAK,WAAW,GAAG,WAAW,CAAC,WAAW,GAAG,SAAS;AAC/D,eAAS,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,GAAG,CAAC,SAAS;AAEjE,WAAK;AAAA,IACN;AAED,aAAS,IAAI,GAAG,KAAK,eAAe,KAAK;AACvC,eAAS,KAAK,CAAC,WAAW,CAAC,YAAY,GAAG,CAAC,WAAW,YAAY,CAAC;AACnE,eAAS,KAAK,CAAC,WAAW,YAAY,GAAG,WAAW,YAAY,CAAC;AACjE,eAAS,KAAK,WAAW,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;AACjE,eAAS,KAAK,WAAW,CAAC,YAAY,GAAG,CAAC,WAAW,CAAC,YAAY,CAAC;AAEnE,WAAK;AAAA,IACN;AAED,SAAK,aAAa,YAAY,IAAIC,MAAAA,uBAAuB,UAAU,CAAC,CAAC;AAAA,EACtE;AACH;;"}