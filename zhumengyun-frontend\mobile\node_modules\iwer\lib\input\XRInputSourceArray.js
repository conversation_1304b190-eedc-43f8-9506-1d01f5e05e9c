var _a;
export const PRIVATE = Symbol('@immersive-web-polyfill/xr-input-source-array');
export class XRInputSourceArray {
    constructor() {
        this[_a] = { inputSources: [] };
    }
    [(_a = PRIVATE, Symbol.iterator)]() {
        return this[PRIVATE].inputSources.values();
    }
    get length() {
        return this[PRIVATE].inputSources.length;
    }
    getter(index) {
        return this[PRIVATE].inputSources[index];
    }
}
//# sourceMappingURL=XRInputSourceArray.js.map