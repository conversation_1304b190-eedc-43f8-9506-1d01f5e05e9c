import { ColorRepresentation, LineBasicMaterial, MeshBasicMaterial } from 'three';
import { HandlesContext } from './context.js';
export declare const handleXRayMaterialProperties: {
    depthTest: boolean;
    depthWrite: boolean;
    fog: boolean;
    toneMapped: boolean;
    transparent: boolean;
};
export declare function setupHandlesContextHoverMaterial(context: HandlesContext, material: MeshBasicMaterial | LineBasicMaterial, tag: string, { color, hoverColor, hoverOpacity, opacity, disabled, }: {
    color: ColorRepresentation;
    disabled?: boolean;
    opacity?: number;
    hoverColor?: ColorRepresentation;
    hoverOpacity?: number;
}): (() => void) | undefined;
