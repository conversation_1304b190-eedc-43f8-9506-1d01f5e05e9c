{"name": "@pmndrs/handle", "description": "framework agnostic expandable handle implementation for threejs", "author": "<PERSON><PERSON>", "type": "module", "homepage": "https://github.com/pmndrs/xr", "version": "6.6.19", "keywords": ["r3f", "xr", "ar", "vr", "three.js", "react", "typescript"], "repository": {"type": "git", "url": "**************:pmndrs/xr.git"}, "main": "dist/index.js", "license": "SEE LICENSE IN LICENSE", "dependencies": {"zustand": "^4.5.2", "@pmndrs/pointer-events": "~6.6.19"}, "files": ["dist"], "scripts": {"build": "tsc", "test": "mocha ./tests/*.spec.ts", "check:prettier": "prettier --check src", "check:eslint": "eslint \"src/**/*.{ts,tsx}\"", "fix:prettier": "prettier --write src", "fix:eslint": "eslint \"src/**/*.{ts,tsx}\" --fix"}}