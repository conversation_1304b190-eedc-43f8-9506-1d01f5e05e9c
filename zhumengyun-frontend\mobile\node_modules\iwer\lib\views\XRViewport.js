/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_VIEWPORT } from '../private.js';
export class XRViewport {
    constructor(x, y, width, height) {
        this[P_VIEWPORT] = { x, y, width, height };
    }
    get x() {
        return this[P_VIEWPORT].x;
    }
    get y() {
        return this[P_VIEWPORT].y;
    }
    get width() {
        return this[P_VIEWPORT].width;
    }
    get height() {
        return this[P_VIEWPORT].height;
    }
}
//# sourceMappingURL=XRViewport.js.map