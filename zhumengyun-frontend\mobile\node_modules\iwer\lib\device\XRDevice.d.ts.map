{"version": 3, "file": "XRDevice.d.ts", "sourceRoot": "", "sources": ["../../src/device/XRDevice.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EACN,QAAQ,EAKR,MAAM,eAAe,CAAC;AACvB,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,kBAAkB,CAAC;AACvD,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,MAAM,mBAAmB,CAAC;AACrE,OAAO,EACN,sBAAsB,EACtB,iBAAiB,EACjB,SAAS,EACT,aAAa,EACb,iBAAiB,EACjB,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EAAE,WAAW,EAAoB,MAAM,kBAAkB,CAAC;AACjE,OAAO,EACN,YAAY,EACZ,aAAa,EAEb,MAAM,2BAA2B,CAAC;AACnC,OAAO,EAAW,YAAY,EAAE,MAAM,2BAA2B,CAAC;AAClE,OAAO,EACN,gBAAgB,EAEhB,MAAM,+BAA+B,CAAC;AACvC,OAAO,EAAE,IAAI,EAAQ,MAAM,WAAW,CAAC;AAEvC,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AACzD,OAAO,EAAE,WAAW,EAAE,MAAM,6BAA6B,CAAC;AAE1D,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAWlD,OAAO,EAAE,QAAQ,EAAE,MAAM,+BAA+B,CAAC;AACzD,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AAErD,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AACpD,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAEjD,MAAM,MAAM,YAAY,GACrB,QAAQ,GACR,OAAO,GACP,aAAa,GACb,eAAe,GACf,WAAW,GACX,aAAa,GACb,SAAS,GACT,iBAAiB,GACjB,gBAAgB,GAChB,UAAU,GACV,eAAe,GACf,eAAe,CAAC;AAEnB,MAAM,WAAW,cAAc;IAC9B,IAAI,EAAE,MAAM,CAAC;IACb,gBAAgB,EAAE,kBAAkB,GAAG,SAAS,CAAC;IACjD,qBAAqB,EAAE,aAAa,EAAE,CAAC;IACvC,iBAAiB,EAAE,YAAY,EAAE,CAAC;IAClC,mBAAmB,EAAE,MAAM,EAAE,CAAC;IAC9B,yBAAyB,EAAE,OAAO,CAAC;IACnC,wBAAwB,EAAE,MAAM,CAAC;IACjC,qBAAqB,EAAE,OAAO,CAAC;SAC7B,WAAW,IAAI,aAAa,GAAG,sBAAsB;KACtD,CAAC,CAAC;IACH,eAAe,EAAE,iBAAiB,CAAC;IACnC,SAAS,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,WAAW,eAAe;IAC/B,GAAG,EAAE,MAAM,CAAC;IACZ,IAAI,EAAE,MAAM,CAAC;IACb,aAAa,EAAE,OAAO,CAAC;IACvB,eAAe,EAAE,OAAO,CAAC;IACzB,iBAAiB,EAAE,UAAU,CAAC;IAC9B,eAAe,EAAE,cAAc,CAAC;CAChC;AAUD,MAAM,WAAW,gBAAgB;IAChC,KAAK,QAAQ,EAAE,QAAQ,GAAG,KAAK,CAAC;CAChC;AACD,MAAM,WAAW,KAAK;IACrB,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IAC3B,IAAI,WAAW,IAAI,iBAAiB,CAAC;IACrC,IAAI,cAAc,IAAI,cAAc,CAAC;CACrC;AAED,MAAM,WAAW,cAAc;IAC9B,KAAK,QAAQ,EAAE,QAAQ,GAAG,0BAA0B,CAAC;CACrD;AACD,MAAM,WAAW,0BAA0B;IAC1C,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IAC3B,eAAe,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI,CAAC;IACjC,sBAAsB,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;IAC5C,aAAa,EAAE,OAAO,CAAC;IACvB,oBAAoB,EAAE,OAAO,CAAC;IAC9B,aAAa,EAAE,OAAO,CAAC;IACvB,IAAI,iBAAiB,IAAI,iBAAiB,CAAC;IAC3C,IAAI,aAAa,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC;IACtC,IAAI,aAAa,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;IACrC,qBAAqB,CAAC,SAAS,EAAE,IAAI,GAAG,IAAI,EAAE,CAAC;CAC/C;AAOD;;;GAGG;AACH,qBAAa,QAAQ;IACpB,SAAgB,OAAO,WAAW;IAElC,CAAC,QAAQ,CAAC,EAAE;QAEX,IAAI,EAAE,MAAM,CAAC;QACb,qBAAqB,EAAE,MAAM,EAAE,CAAC;QAChC,iBAAiB,EAAE,MAAM,EAAE,CAAC;QAC5B,mBAAmB,EAAE,MAAM,EAAE,CAAC;QAC9B,yBAAyB,EAAE,OAAO,CAAC;QACnC,wBAAwB,EAAE,MAAM,CAAC;QACjC,qBAAqB,EAAE,OAAO,CAAC;aAC7B,WAAW,IAAI,aAAa,GAAG,sBAAsB;SACtD,CAAC,CAAC;QACH,eAAe,EAAE,iBAAiB,CAAC;QACnC,SAAS,EAAE,MAAM,CAAC;QAGlB,QAAQ,EAAE,OAAO,CAAC;QAClB,UAAU,EAAE,UAAU,CAAC;QACvB,aAAa,EAAE,OAAO,CAAC;QACvB,GAAG,EAAE,MAAM,CAAC;QACZ,IAAI,EAAE,MAAM,CAAC;QACb,WAAW,EAAE;aAAG,GAAG,IAAI,YAAY,CAAC,CAAC,EAAE,YAAY;SAAE,CAAC;QACtD,KAAK,EAAE;aAAG,GAAG,IAAI,YAAY,CAAC,CAAC,EAAE,WAAW;SAAE,CAAC;QAC/C,gBAAgB,EAAE,YAAY,GAAG,MAAM,CAAC;QACxC,0BAA0B,EAAE,OAAO,CAAC;QACpC,eAAe,EAAE,iBAAiB,CAAC;QACnC,sBAAsB,EAAE,iBAAiB,GAAG,IAAI,CAAC;QACjD,QAAQ,EAAE,QAAQ,GAAG,IAAI,CAAC;QAE1B,MAAM,EAAE,IAAI,CAAC;QACb,WAAW,EAAE,WAAW,CAAC;QACzB,WAAW,EAAE,gBAAgB,CAAC;QAC9B,UAAU,EAAE;aAAG,GAAG,IAAI,KAAK,GAAG,OAAO;SAAE,CAAC;QAExC,UAAU,CAAC,EAAE;YACZ,MAAM,EAAE,iBAAiB,CAAC;YAC1B,MAAM,EAAE,WAAW,GAAG,IAAI,CAAC;YAC3B,KAAK,EAAE,MAAM,CAAC;YACd,MAAM,EAAE,MAAM,CAAC;YACf,MAAM,EAAE,MAAM,CAAC;SACf,CAAC;QACF,eAAe,EAAE,cAAc,CAAC;QAEhC,WAAW,EAAE,CAAC,KAAK,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,KAAK,UAAU,CAAC;QAC/D,WAAW,EAAE,MAAM,IAAI,CAAC;QACxB,cAAc,EAAE,CAAC,SAAS,EAAE,YAAY,GAAG,IAAI,KAAK,IAAI,CAAC;QACzD,YAAY,EAAE,MAAM,IAAI,CAAC;QACzB,YAAY,EAAE,CAAC,KAAK,EAAE,OAAO,KAAK,IAAI,CAAC;QAGvC,YAAY,CAAC,EAAE,YAAY,CAAC;QAG5B,KAAK,CAAC,EAAE,KAAK,CAAC;QACd,GAAG,CAAC,EAAE,0BAA0B,CAAC;KACjC,CAAC;gBAGD,YAAY,EAAE,cAAc,EAC5B,aAAa,GAAE,OAAO,CAAC,eAAe,CAAM;IAsO7C,cAAc,CAAC,YAAY,GAAE,GAAgB;IAgD7C,YAAY,CAAC,gBAAgB,EAAE,gBAAgB;IAI/C,UAAU,CAAC,cAAc,EAAE,cAAc;IAIzC,IAAI,qBAAqB,aAExB;IAED,IAAI,iBAAiB,aAEpB;IAED,IAAI,mBAAmB,aAEtB;IAED,IAAI,yBAAyB,YAE5B;IAED,IAAI,wBAAwB,WAE3B;IAED,IAAI,aAAa,IAIQ,OAAO,CAF/B;IAED,IAAI,aAAa,CAAC,KAAK,EAAE,OAAO,EAE/B;IAED,IAAI,GAAG,IAIQ,MAAM,CAFpB;IAED,IAAI,GAAG,CAAC,KAAK,EAAE,MAAM,EAEpB;IAED,IAAI,IAAI,IAIQ,MAAM,CAFrB;IAED,IAAI,IAAI,CAAC,KAAK,EAAE,MAAM,EAErB;IAED,IAAI,QAAQ,IAAI,OAAO,CAEtB;IAED,IAAI,UAAU,IAAI,UAAU,CAE3B;IAED,IAAI,WAAW,qBAMd;IAED,IAAI,UAAU;;;;MAMb;IAED,IAAI,WAAW;;;;MAEd;IAED,IAAI,KAAK;;;;MAER;IAED,IAAI,gBAAgB,IAIO,YAAY,GAAG,MAAM,CAF/C;IAED,IAAI,gBAAgB,CAAC,IAAI,EAAE,YAAY,GAAG,MAAM,EAM/C;IAED,IAAI,YAAY,IAAI,cAAc,EAAE,CASnC;IAED,IAAI,YAAY,IAAI,aAAa,EAAE,CAMlC;IAED,IAAI,eAAe,IAAI,cAAc,CAEpC;IAED,IAAI,gBAAgB,IAAI;QAAE,KAAK,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE,GAAG,SAAS,CAMpE;IAED,IAAI,aAAa,IAAI,SAAS,GAAG,SAAS,CAEzC;IAED,IAAI,cAAc,IAAI,OAAO,CAE5B;IAED,IAAI,IAAI,WAEP;IAED,mBAAmB,IAAI,IAAI;IAQ3B,QAAQ;IAyBR,IAAI,eAAe,sBAElB;IAGD,qBAAqB,CAAC,KAAK,EAAE,iBAAiB;IAc9C,kBAAkB,CACjB,QAAQ,EAAE,gBAAgB,EAC1B,SAAS,EAAE;QACV,MAAM,EAAE;YACP,CAAC,EAAE,MAAM,CAAC;YACV,CAAC,EAAE,WAAW,CAAC;SACf,EAAE,CAAC;QACJ,MAAM,EAAE,GAAG,EAAE,CAAC;KACd;IAUF,IAAI,KAAK,sBAER;IAED,IAAI,GAAG,2CAEN;CACD"}