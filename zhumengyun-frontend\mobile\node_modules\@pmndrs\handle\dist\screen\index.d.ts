import { ScreenHandleStore } from './store.js';
export declare function filterForOnePointerRightClickOrTwoPointer(map: ScreenHandleStore['map']): boolean;
export declare function filterForOnePointerLeftClick(map: ScreenHandleStore['map']): boolean;
export * from './camera.js';
export * from './store.js';
export * from './pan.js';
export * from './zoom.js';
export * from './rotate.js';
export * from './pan.js';
export * from './orbit.js';
export * from './map.js';
