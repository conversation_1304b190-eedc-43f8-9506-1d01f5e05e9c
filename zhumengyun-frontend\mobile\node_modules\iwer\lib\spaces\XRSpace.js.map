{"version": 3, "file": "XRSpace.js", "sourceRoot": "", "sources": ["../../src/spaces/XRSpace.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,IAAI,EAAQ,IAAI,EAAE,MAAM,WAAW,CAAC;AAE7C,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAExC,MAAM,OAAO,OAAQ,SAAQ,WAAW;IAOvC,YAAY,WAAqB,EAAE,YAAmB;QACrD,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,CAAC,GAAG;YACf,WAAW;YACX,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE;YACrE,QAAQ,EAAE,IAAI;SACd,CAAC;IACH,CAAC;CACD;AAED,MAAM,OAAO,WAAY,SAAQ,OAAO;IACvC;QACC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,4BAA4B;IAC9D,CAAC;CACD;AAED,MAAM,OAAO,YAAY;IACxB,uEAAuE;IACvE,MAAM,CAAC,oBAAoB,CAAC,KAAc,EAAE,QAAc;QACzD,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC;QACjD,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IAC9C,CAAC;IAED,0FAA0F;IAC1F,MAAM,CAAC,sBAAsB,CAAC,KAAc,EAAE,UAAgB;QAC7D,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC;QACjD,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAClC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;QAC/C,IAAI,CAAC,uBAAuB,CAAC,YAAY,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;IACrE,CAAC;IAED,sDAAsD;IACtD,MAAM,CAAC,kBAAkB,CAAC,KAAc,EAAE,MAAY;QACrD,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC;QACjD,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;IACjC,CAAC;IAED,yDAAyD;IACzD,MAAM,CAAC,2BAA2B,CACjC,KAAc,EACd,eAAqB,IAAI,CAAC,MAAM,EAAE;QAElC,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,WAAW;YAC9C,CAAC,CAAC,YAAY,CAAC,2BAA2B,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC;YACtE,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,kCAAkC;QAEpD,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,YAAY,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC;QACvE,OAAO,YAAY,CAAC;IACrB,CAAC;CACD"}