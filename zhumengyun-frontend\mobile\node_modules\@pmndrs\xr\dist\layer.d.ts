import { CylinderGeometry, Object3D, PlaneGeometry, SphereGeometry, Texture, WebGLRenderer, WebGLRenderTarget, WebXRManager } from 'three';
import { XRState, XRStore } from './store.js';
export type XRLayerEntry = {
    renderOrder: number;
    readonly layer: XRCylinderLayer | XRQuadLayer | XREquirectLayer;
    readonly object3D: Object3D;
};
export type XRLayerOptions = Pick<Partial<XRCylinderLayerInit & XRQuadLayerInit & XREquirectLayerInit>, 'layout' | 'mipLevels' | 'colorFormat' | 'depthFormat'> & Pick<Partial<XRMediaCylinderLayerInit & XRMediaQuadLayerInit & XRMediaEquirectLayerInit>, 'layout' | 'invertStereo'> & {
    shape?: XRLayerShape;
};
export type XRLayerSrc = HTMLVideoElement | Exclude<TexImageSource, VideoFrame | HTMLVideoElement> | WebGLRenderTarget;
export type XRLayerProperties = Pick<Partial<XRCylinderLayer & XRQuadLayer & XREquirectLayer>, 'centralAngle' | 'centralHorizontalAngle' | 'upperVerticalAngle' | 'lowerVerticalAngle' | 'blendTextureSourceAlpha' | 'chromaticAberrationCorrection' | 'quality'>;
export type XRLayerShape = 'cylinder' | 'equirect' | 'quad';
export declare function createXRLayer(src: XRLayerSrc, state: XRState<any>, originReferenceSpace: XRReferenceSpace, xrManager: WebXRManager, relativeTo: Object3D, options: XRLayerOptions, properties: XRLayerProperties): XRCylinderLayer | XRQuadLayer | XREquirectLayer | undefined;
declare module 'three' {
    interface WebGLRenderer {
        setRenderTargetTextures(renderTarget: WebGLRenderTarget, colorTexture: WebGLTexture, depthTexture?: WebGLTexture): void;
    }
}
export declare function setXRLayerRenderTarget(renderer: WebGLRenderer, renderTarget: WebGLRenderTarget, layerEntry: XRLayerEntry | undefined | null, frame: XRFrame | undefined): void;
export declare function createXRLayerGeometry(shape: XRLayerShape, properties: Pick<XRLayerProperties, 'centralAngle' | 'centralHorizontalAngle' | 'lowerVerticalAngle' | 'upperVerticalAngle'>): SphereGeometry | PlaneGeometry | CylinderGeometry;
export declare function updateXRLayerProperties(target: XRCylinderLayer | XRQuadLayer | XREquirectLayer, properties?: XRLayerProperties): void;
export declare function setupXRImageLayer(renderer: WebGLRenderer, store: XRStore<any>, layer: XRCompositionLayer, src: Exclude<TexImageSource, VideoFrame | HTMLVideoElement>): () => void;
export declare function waitForXRLayerSrcSize(src: XRLayerSrc | undefined): Promise<void>;
export declare function getXRLayerSrcTexture(src: XRLayerSrc): Texture;
export declare function updateXRLayerTransform(state: XRState<any>, target: XRCylinderLayer | XRQuadLayer | XREquirectLayer, centralAngle: number | undefined, relativeTo: Object3D): void;
export declare function getLayerShape(layer: XRCylinderLayer | XRQuadLayer | XREquirectLayer): XRLayerShape;
export declare function createXRLayerRenderTarget(pixelWidth: number, pixelHeight: number, dpr: number): WebGLRenderTarget<Texture>;
