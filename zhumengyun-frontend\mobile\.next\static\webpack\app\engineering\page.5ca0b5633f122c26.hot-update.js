"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/engineering/page",{

/***/ "(app-pages-browser)/./src/app/engineering/page.tsx":
/*!**************************************!*\
  !*** ./src/app/engineering/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DiscoveryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction DiscoveryPage() {\n    var _selectedContent_tags;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('video');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    // 交互状态\n    const [likedItems, setLikedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [savedItems, setSavedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [followedUsers, setFollowedUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [joinedGroups, setJoinedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'c1'\n    ]));\n    // 弹窗状态\n    const [showContentDetail, setShowContentDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showApplyProject, setShowApplyProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showShareModal, setShowShareModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNFTModal, setShowNFTModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTipModal, setShowTipModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSuccessToast, setShowSuccessToast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [toastMessage, setToastMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 选中的项目\n    const [selectedContent, setSelectedContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedProject, setSelectedProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 加载状态\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 筛选后的内容\n    const [filteredContent, setFilteredContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // 模拟发布的内容数据（对应发布页面的四大平台）\n    const discoveryContent = {\n        video: [\n            {\n                id: 'v1',\n                title: 'NextGen 2025智慧城市建设项目展示',\n                creator: '工程师·张三',\n                avatar: '👨‍💻',\n                thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop',\n                duration: '3:45',\n                views: 12500,\n                likes: 890,\n                publishTime: '2小时前',\n                tags: [\n                    '智慧城市',\n                    'AI技术',\n                    '建筑设计'\n                ],\n                description: '展示了最新的AI驱动智慧城市建设项目，包括智能交通系统和物联网基础设施...',\n                nftEnabled: true,\n                didVerified: true,\n                rewards: 120\n            },\n            {\n                id: 'v2',\n                title: 'AI辅助建筑设计全流程演示',\n                creator: '建筑师·王设计',\n                avatar: '🏗️',\n                thumbnail: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop',\n                duration: '5:20',\n                views: 8900,\n                likes: 567,\n                publishTime: '4小时前',\n                tags: [\n                    '建筑设计',\n                    'AI辅助',\n                    'BIM'\n                ],\n                description: '完整展示AI辅助建筑设计的全流程，从概念设计到施工图生成...',\n                nftEnabled: false,\n                didVerified: true,\n                rewards: 85\n            },\n            {\n                id: 'v3',\n                title: '元宇宙虚拟展厅设计案例',\n                creator: 'VR设计师·小李',\n                avatar: '🌌',\n                thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop',\n                duration: '4:15',\n                views: 15600,\n                likes: 1234,\n                publishTime: '6小时前',\n                tags: [\n                    '元宇宙',\n                    'VR设计',\n                    '虚拟展厅'\n                ],\n                description: '创新的元宇宙虚拟展厅设计，支持多人实时交互和3D展示...',\n                nftEnabled: true,\n                didVerified: true,\n                rewards: 150\n            }\n        ],\n        discovery: [\n            {\n                id: 'd1',\n                title: '2025年建筑行业AI应用趋势报告',\n                creator: '行业分析师·陈专家',\n                avatar: '📊',\n                images: [\n                    'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',\n                    'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop'\n                ],\n                readTime: '8分钟',\n                views: 5600,\n                likes: 234,\n                publishTime: '1小时前',\n                tags: [\n                    '行业报告',\n                    'AI应用',\n                    '建筑趋势'\n                ],\n                description: '深度分析2025年建筑行业AI应用的最新趋势，包括设计自动化、施工机器人等...',\n                nftEnabled: false,\n                didVerified: true,\n                rewards: 75\n            },\n            {\n                id: 'd2',\n                title: '智能建筑物联网系统设计指南',\n                creator: '物联网工程师·刘技术',\n                avatar: '🔗',\n                images: [\n                    'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop'\n                ],\n                readTime: '12分钟',\n                views: 3400,\n                likes: 189,\n                publishTime: '3小时前',\n                tags: [\n                    '物联网',\n                    '智能建筑',\n                    '系统设计'\n                ],\n                description: '详细介绍智能建筑物联网系统的设计原理、技术架构和实施方案...',\n                nftEnabled: true,\n                didVerified: true,\n                rewards: 95\n            }\n        ],\n        community: [\n            {\n                id: 'c1',\n                title: '建筑师AI工具使用经验分享',\n                creator: '建筑师联盟',\n                avatar: '🏗️',\n                groupType: '技术讨论',\n                members: 2340,\n                posts: 156,\n                publishTime: '30分钟前',\n                tags: [\n                    '经验分享',\n                    'AI工具',\n                    '建筑师'\n                ],\n                description: '分享各种AI工具在建筑设计中的实际应用经验，包括Midjourney、Stable Diffusion等...',\n                isJoined: true,\n                activity: 'high'\n            },\n            {\n                id: 'c2',\n                title: 'Web3建设者技术讨论群',\n                creator: 'Web3建设者',\n                avatar: '🌐',\n                groupType: '技术交流',\n                members: 1567,\n                posts: 89,\n                publishTime: '1小时前',\n                tags: [\n                    'Web3',\n                    '区块链',\n                    '技术讨论'\n                ],\n                description: '讨论Web3技术在建筑和工程领域的应用，包括DeFi、NFT、DAO等...',\n                isJoined: false,\n                activity: 'medium'\n            }\n        ],\n        ecosystem: [\n            {\n                id: 'e1',\n                title: '寻求AI建筑设计合作伙伴',\n                creator: '建筑事务所·王总',\n                avatar: '🏢',\n                budget: '50-100万',\n                duration: '3-6个月',\n                location: '北京',\n                publishTime: '2小时前',\n                tags: [\n                    '项目合作',\n                    'AI建筑',\n                    '设计服务'\n                ],\n                description: '我们正在开发一个大型商业综合体项目，需要AI建筑设计方面的合作伙伴...',\n                requirements: [\n                    'AI设计经验',\n                    'BIM技术',\n                    '团队规模10+'\n                ],\n                matchType: '技术合作',\n                status: 'open'\n            },\n            {\n                id: 'e2',\n                title: '智慧城市项目寻求技术团队',\n                creator: '政府采购部门',\n                avatar: '🏛️',\n                budget: '200-500万',\n                duration: '6-12个月',\n                location: '上海',\n                publishTime: '4小时前',\n                tags: [\n                    '政府项目',\n                    '智慧城市',\n                    '技术团队'\n                ],\n                description: '智慧城市基础设施建设项目，需要具备AI、物联网、大数据技术的团队...',\n                requirements: [\n                    '政府项目经验',\n                    '资质齐全',\n                    '技术实力强'\n                ],\n                matchType: '工程项目匹配',\n                status: 'open'\n            }\n        ]\n    };\n    // 分类选项\n    const categories = [\n        {\n            key: 'all',\n            name: '全部',\n            icon: '🌟'\n        },\n        {\n            key: 'ai',\n            name: 'AI技术',\n            icon: '🤖'\n        },\n        {\n            key: 'architecture',\n            name: '建筑设计',\n            icon: '🏗️'\n        },\n        {\n            key: 'smart-city',\n            name: '智慧城市',\n            icon: '🏙️'\n        },\n        {\n            key: 'web3',\n            name: 'Web3',\n            icon: '🌐'\n        },\n        {\n            key: 'iot',\n            name: '物联网',\n            icon: '🔗'\n        },\n        {\n            key: 'vr',\n            name: 'VR/AR',\n            icon: '🥽'\n        }\n    ];\n    // 显示成功提示\n    const showToast = (message)=>{\n        setToastMessage(message);\n        setShowSuccessToast(true);\n        setTimeout(()=>setShowSuccessToast(false), 3000);\n    };\n    // 搜索和筛选功能\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DiscoveryPage.useEffect\": ()=>{\n            const filterContent = {\n                \"DiscoveryPage.useEffect.filterContent\": ()=>{\n                    const filtered = {};\n                    Object.keys(discoveryContent).forEach({\n                        \"DiscoveryPage.useEffect.filterContent\": (type)=>{\n                            filtered[type] = discoveryContent[type].filter({\n                                \"DiscoveryPage.useEffect.filterContent\": (item)=>{\n                                    // 搜索筛选\n                                    const matchesSearch = !searchQuery || item.title.toLowerCase().includes(searchQuery.toLowerCase()) || item.creator.toLowerCase().includes(searchQuery.toLowerCase()) || item.tags.some({\n                                        \"DiscoveryPage.useEffect.filterContent\": (tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase())\n                                    }[\"DiscoveryPage.useEffect.filterContent\"]);\n                                    // 分类筛选\n                                    const matchesCategory = selectedCategory === 'all' || item.tags.some({\n                                        \"DiscoveryPage.useEffect.filterContent\": (tag)=>{\n                                            switch(selectedCategory){\n                                                case 'ai':\n                                                    return tag.includes('AI') || tag.includes('智能');\n                                                case 'architecture':\n                                                    return tag.includes('建筑') || tag.includes('设计');\n                                                case 'smart-city':\n                                                    return tag.includes('智慧城市') || tag.includes('城市');\n                                                case 'web3':\n                                                    return tag.includes('Web3') || tag.includes('区块链') || tag.includes('NFT');\n                                                case 'iot':\n                                                    return tag.includes('物联网') || tag.includes('IoT');\n                                                case 'vr':\n                                                    return tag.includes('VR') || tag.includes('AR') || tag.includes('元宇宙');\n                                                default:\n                                                    return true;\n                                            }\n                                        }\n                                    }[\"DiscoveryPage.useEffect.filterContent\"]);\n                                    return matchesSearch && matchesCategory;\n                                }\n                            }[\"DiscoveryPage.useEffect.filterContent\"]);\n                        }\n                    }[\"DiscoveryPage.useEffect.filterContent\"]);\n                    setFilteredContent(filtered);\n                }\n            }[\"DiscoveryPage.useEffect.filterContent\"];\n            filterContent();\n        }\n    }[\"DiscoveryPage.useEffect\"], [\n        searchQuery,\n        selectedCategory\n    ]);\n    // 点赞功能\n    const handleLike = (itemId)=>{\n        setLikedItems((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(itemId)) {\n                newSet.delete(itemId);\n                showToast('取消点赞');\n            } else {\n                newSet.add(itemId);\n                showToast('点赞成功');\n            }\n            return newSet;\n        });\n    };\n    // 收藏功能\n    const handleSave = (itemId)=>{\n        setSavedItems((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(itemId)) {\n                newSet.delete(itemId);\n                showToast('取消收藏');\n            } else {\n                newSet.add(itemId);\n                showToast('收藏成功');\n            }\n            return newSet;\n        });\n    };\n    // 关注功能\n    const handleFollow = (userId)=>{\n        setFollowedUsers((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(userId)) {\n                newSet.delete(userId);\n                showToast('取消关注');\n            } else {\n                newSet.add(userId);\n                showToast('关注成功');\n            }\n            return newSet;\n        });\n    };\n    // 观看视频\n    const handleWatchVideo = (video)=>{\n        setSelectedContent(video);\n        setShowContentDetail(true);\n    };\n    // 阅读文章\n    const handleReadArticle = (article)=>{\n        setSelectedContent(article);\n        setShowContentDetail(true);\n    };\n    // 分享功能\n    const handleShare = (content)=>{\n        setSelectedContent(content);\n        setShowShareModal(true);\n    };\n    // 加入/退出群组\n    const handleJoinGroup = (groupId)=>{\n        setJoinedGroups((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(groupId)) {\n                newSet.delete(groupId);\n                showToast('已退出群组');\n            } else {\n                newSet.add(groupId);\n                showToast('成功加入群组');\n            }\n            return newSet;\n        });\n    };\n    // 查看项目详情\n    const handleViewProject = (project)=>{\n        setSelectedProject(project);\n        setShowContentDetail(true);\n    };\n    // 申请项目\n    const handleApplyProject = (project)=>{\n        setSelectedProject(project);\n        setShowApplyProject(true);\n    };\n    // NFT购买\n    const handleBuyNFT = (content)=>{\n        setSelectedContent(content);\n        setShowNFTModal(true);\n    };\n    // 创作者打赏\n    const handleTipCreator = (content)=>{\n        setSelectedContent(content);\n        setShowTipModal(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 border-b border-white/10 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-r from-purple-400 to-blue-500 rounded-xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-bold text-lg\",\n                                                    children: \"\\uD83D\\uDD0D\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: \"内容发现\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-purple-300\",\n                                                        children: \"探索 • 学习 • 连接\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400\",\n                                                children: \"●\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" 15.6k 在线\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            placeholder: \"搜索内容、创作者、标签...\",\n                                            className: \"w-full border border-white/20 rounded-lg px-4 py-3 pl-10 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                            children: \"\\uD83D\\uDD0D\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 rounded-lg p-1 mb-4\",\n                                children: [\n                                    {\n                                        key: 'video',\n                                        title: '视频内容',\n                                        icon: '🎬'\n                                    },\n                                    {\n                                        key: 'discovery',\n                                        title: '图文发现',\n                                        icon: '📰'\n                                    },\n                                    {\n                                        key: 'community',\n                                        title: '社群讨论',\n                                        icon: '👥'\n                                    },\n                                    {\n                                        key: 'ecosystem',\n                                        title: '生态匹配',\n                                        icon: '🤝'\n                                    }\n                                ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(tab.key),\n                                        className: \"flex-1 flex flex-col items-center py-3 px-2 rounded-md text-xs font-medium transition-colors \".concat(activeTab === tab.key ? 'text-white border-b-2 border-white' : 'text-white/50 hover:text-white/70'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg mb-1\",\n                                                children: tab.icon\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tab.title\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, tab.key, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2 overflow-x-auto pb-2\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedCategory(category.key),\n                                        className: \"flex-shrink-0 flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors border border-white/20 \".concat(selectedCategory === category.key ? 'text-white border-white' : 'text-white/50 hover:text-white/70 hover:border-white/40'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: category.icon\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: category.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, category.key, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-4 pb-20\",\n                        children: [\n                            activeTab === 'video' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: (filteredContent.video || discoveryContent.video).map((video)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-xl overflow-hidden border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: video.thumbnail,\n                                                        alt: video.title,\n                                                        className: \"w-full h-48 object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute bottom-2 right-2 bg-black/70 px-2 py-1 rounded text-xs text-white\",\n                                                        children: video.duration\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-2 left-2 flex space-x-2\",\n                                                        children: [\n                                                            video.nftEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-purple-500 px-2 py-1 rounded-full text-xs font-medium\",\n                                                                children: \"\\uD83C\\uDFA8 NFT\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            video.didVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-blue-500 px-2 py-1 rounded-full text-xs font-medium\",\n                                                                children: \"\\uD83C\\uDD94 DID\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center\",\n                                                                children: video.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: video.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: [\n                                                                            video.creator,\n                                                                            \" • \",\n                                                                            video.publishTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-green-400 font-bold text-sm\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            video.rewards,\n                                                                            \" NGT\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 466,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: \"奖励\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 text-sm mb-3 line-clamp-2\",\n                                                        children: video.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2 mb-3\",\n                                                        children: video.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-purple-500/20 rounded text-xs text-purple-300\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    tag\n                                                                ]\n                                                            }, idx, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleLike(video.id),\n                                                                        className: \"flex flex-col items-center space-y-1 transition-colors \".concat(likedItems.has(video.id) ? 'text-red-400' : 'text-gray-400 hover:text-red-400'),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-8 h-8 bg-white/10 rounded-full flex items-center justify-center\",\n                                                                                children: \"\\uD83D\\uDC4D\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 490,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs\",\n                                                                                children: video.likes + (likedItems.has(video.id) ? 1 : 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 493,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 484,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleShare(video),\n                                                                        className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-8 h-8 bg-white/10 rounded-full flex items-center justify-center\",\n                                                                                children: \"\\uD83D\\uDD04\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 500,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs\",\n                                                                                children: \"33\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 503,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 496,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleSave(video.id),\n                                                                        className: \"flex flex-col items-center space-y-1 transition-colors \".concat(savedItems.has(video.id) ? 'text-yellow-400' : 'text-gray-400 hover:text-yellow-400'),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-8 h-8 bg-white/10 rounded-full flex items-center justify-center\",\n                                                                                children: \"❤️\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 512,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs\",\n                                                                                children: \"45\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 515,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 506,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-8 h-8 bg-white/10 rounded-full flex items-center justify-center\",\n                                                                                children: \"\\uD83D\\uDCAC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 519,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs\",\n                                                                                children: \"67\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 522,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    video.nftEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleBuyNFT(video),\n                                                                        className: \"px-3 py-1 bg-purple-500/20 border border-purple-500 rounded-lg text-xs font-medium hover:bg-purple-500/30 transition-colors\",\n                                                                        children: \"购买NFT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 528,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleWatchVideo(video),\n                                                                        className: \"px-3 py-1 bg-purple-500 rounded-lg text-xs font-medium hover:bg-purple-600 transition-colors\",\n                                                                        children: \"观看\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 535,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, video.id, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'discovery' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: (filteredContent.discovery || discoveryContent.discovery).map((article)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-xl overflow-hidden border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-sm\",\n                                                                    children: article.avatar\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: [\n                                                                            article.creator,\n                                                                            \" • \",\n                                                                            article.publishTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 561,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 560,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-green-400 font-bold text-sm\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            article.rewards,\n                                                                            \" NGT\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 564,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-bold text-white mb-2\",\n                                                            children: article.title\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 text-sm mb-3 line-clamp-3\",\n                                                            children: article.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2 mb-3\",\n                                                            children: article.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 bg-blue-500/20 rounded text-xs text-blue-300\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        tag\n                                                                    ]\n                                                                }, idx, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleLike(article.id),\n                                                                            className: \"flex flex-col items-center space-y-1 transition-colors \".concat(likedItems.has(article.id) ? 'text-red-400' : 'text-gray-400 hover:text-red-400'),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 bg-white/10 rounded-full flex items-center justify-center\",\n                                                                                    children: \"\\uD83D\\uDC4D\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 588,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs\",\n                                                                                    children: article.likes + (likedItems.has(article.id) ? 1 : 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 591,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 582,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleShare(article),\n                                                                            className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 bg-white/10 rounded-full flex items-center justify-center\",\n                                                                                    children: \"\\uD83D\\uDD04\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 598,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"33\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 601,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 594,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleSave(article.id),\n                                                                            className: \"flex flex-col items-center space-y-1 transition-colors \".concat(savedItems.has(article.id) ? 'text-yellow-400' : 'text-gray-400 hover:text-yellow-400'),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 bg-white/10 rounded-full flex items-center justify-center\",\n                                                                                    children: \"❤️\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 610,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"45\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 613,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 bg-white/10 rounded-full flex items-center justify-center\",\n                                                                                    children: \"\\uD83D\\uDCAC\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 617,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"67\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 620,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 616,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2\",\n                                                                    children: [\n                                                                        article.nftEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleBuyNFT(article),\n                                                                            className: \"px-3 py-1 bg-purple-500/20 border border-purple-500 rounded-lg text-xs font-medium hover:bg-purple-500/30 transition-colors\",\n                                                                            children: \"购买NFT\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 626,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleReadArticle(article),\n                                                                            className: \"px-3 py-1 bg-blue-500 rounded-lg text-xs font-medium hover:bg-blue-600 transition-colors\",\n                                                                            children: \"阅读\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 633,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 624,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 21\n                                                }, this),\n                                                article.images && article.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 m-4 rounded-lg overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: article.images[0],\n                                                        alt: article.title,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 554,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, article.id, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 551,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'community' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: (filteredContent.community || discoveryContent.community).map((community)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center text-xl\",\n                                                                children: community.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: community.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 669,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: [\n                                                                            community.creator,\n                                                                            \" • \",\n                                                                            community.publishTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 670,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 668,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full \".concat(community.activity === 'high' ? 'bg-green-400' : community.activity === 'medium' ? 'bg-yellow-400' : 'bg-gray-400')\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 674,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: community.activity === 'high' ? '活跃' : community.activity === 'medium' ? '一般' : '较少'\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 678,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 663,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 text-sm mb-3\",\n                                                children: community.description\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 685,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2 mb-3\",\n                                                children: community.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-green-500/20 rounded text-xs text-green-300\",\n                                                        children: [\n                                                            \"#\",\n                                                            tag\n                                                        ]\n                                                    }, idx, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"\\uD83D\\uDC65 \",\n                                                                    community.members.toLocaleString(),\n                                                                    \" 成员\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"\\uD83D\\uDCAC \",\n                                                                    community.posts,\n                                                                    \" 讨论\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 698,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-blue-500/20 rounded text-xs text-blue-300\",\n                                                                children: community.groupType\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 699,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 696,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleShare(community),\n                                                                className: \"px-3 py-1 bg-white/10 rounded-lg text-xs font-medium hover:bg-white/20 transition-colors\",\n                                                                children: \"分享\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 704,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleJoinGroup(community.id),\n                                                                className: \"px-4 py-2 rounded-lg text-sm font-medium transition-colors \".concat(joinedGroups.has(community.id) ? 'bg-gray-600 text-gray-300 hover:bg-gray-500' : 'bg-gradient-to-r from-green-500 to-teal-500 text-white hover:from-green-600 hover:to-teal-600'),\n                                                                children: joinedGroups.has(community.id) ? '已加入' : '加入讨论'\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 710,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, community.id, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'ecosystem' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: (filteredContent.ecosystem || discoveryContent.ecosystem).map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center text-xl\",\n                                                                children: project.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: project.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 738,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: [\n                                                                            project.creator,\n                                                                            \" • \",\n                                                                            project.publishTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 739,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(project.status === 'open' ? 'bg-green-500' : 'bg-gray-500'),\n                                                            children: project.status === 'open' ? '招募中' : '已结束'\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 743,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 text-sm mb-3\",\n                                                children: project.description\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"预算范围\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 755,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-green-400 font-bold\",\n                                                                children: project.budget\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"项目周期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-blue-400 font-bold\",\n                                                                children: project.duration\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 758,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"项目地点\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 763,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-purple-400 font-bold\",\n                                                                children: project.location\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"匹配类型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 767,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-orange-400 font-bold\",\n                                                                children: project.matchType\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 768,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mb-2\",\n                                                        children: \"技能要求\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2\",\n                                                        children: project.requirements.map((req, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-orange-500/20 rounded text-xs text-orange-300\",\n                                                                children: req\n                                                            }, idx, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 776,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2 mb-3\",\n                                                children: project.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-red-500/20 rounded text-xs text-red-300\",\n                                                        children: [\n                                                            \"#\",\n                                                            tag\n                                                        ]\n                                                    }, idx, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 785,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 783,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleShare(project),\n                                                        className: \"px-3 py-2 bg-white/10 rounded-lg text-xs font-medium hover:bg-white/20 transition-colors\",\n                                                        children: \"分享\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 792,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSave(project.id),\n                                                        className: \"px-3 py-2 rounded-lg text-xs font-medium transition-colors \".concat(savedItems.has(project.id) ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500' : 'bg-white/10 text-gray-300 hover:bg-white/20'),\n                                                        children: savedItems.has(project.id) ? '已收藏' : '收藏'\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 798,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleViewProject(project),\n                                                        className: \"flex-1 py-2 bg-white/10 border border-white/20 rounded-lg text-sm font-medium hover:bg-white/20 transition-colors\",\n                                                        children: \"查看详情\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 808,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleApplyProject(project),\n                                                        className: \"flex-1 py-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg text-sm font-medium hover:from-orange-600 hover:to-red-600 transition-colors\",\n                                                        children: \"立即申请\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 814,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 791,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, project.id, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 731,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 729,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 350,\n                columnNumber: 7\n            }, this),\n            showSuccessToast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 832,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: toastMessage\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 833,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 831,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 830,\n                columnNumber: 9\n            }, this),\n            showContentDetail && selectedContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20 max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"内容详情\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 843,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowContentDetail(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 844,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 842,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-xl\",\n                                            children: selectedContent.avatar\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 854,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: selectedContent.title\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 858,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: selectedContent.creator\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 859,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 857,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 853,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-sm\",\n                                    children: selectedContent.description\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 863,\n                                    columnNumber: 15\n                                }, this),\n                                selectedContent.duration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: [\n                                        \"时长: \",\n                                        selectedContent.duration\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 866,\n                                    columnNumber: 17\n                                }, this),\n                                selectedContent.readTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: [\n                                        \"阅读时间: \",\n                                        selectedContent.readTime\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 872,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: (_selectedContent_tags = selectedContent.tags) === null || _selectedContent_tags === void 0 ? void 0 : _selectedContent_tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 bg-purple-500/20 rounded text-xs text-purple-300\",\n                                            children: [\n                                                \"#\",\n                                                tag\n                                            ]\n                                        }, idx, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 879,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 877,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowContentDetail(false);\n                                                handleTipCreator(selectedContent);\n                                            },\n                                            className: \"flex-1 py-3 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl text-white font-medium hover:from-green-600 hover:to-teal-600 transition-colors\",\n                                            children: \"\\uD83D\\uDCB0 打赏创作者\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 886,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowContentDetail(false),\n                                            className: \"flex-1 py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors\",\n                                            children: \"关闭\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 895,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 885,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 852,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 841,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 840,\n                columnNumber: 9\n            }, this),\n            showShareModal && selectedContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-white/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"分享内容\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 912,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowShareModal(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 913,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 911,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-4 mb-6\",\n                            children: [\n                                {\n                                    name: '微信',\n                                    icon: '💬',\n                                    color: 'bg-green-500'\n                                },\n                                {\n                                    name: '微博',\n                                    icon: '📱',\n                                    color: 'bg-red-500'\n                                },\n                                {\n                                    name: 'QQ',\n                                    icon: '🐧',\n                                    color: 'bg-blue-500'\n                                },\n                                {\n                                    name: '钉钉',\n                                    icon: '💼',\n                                    color: 'bg-blue-600'\n                                },\n                                {\n                                    name: '复制链接',\n                                    icon: '🔗',\n                                    color: 'bg-gray-500'\n                                },\n                                {\n                                    name: '更多',\n                                    icon: '⋯',\n                                    color: 'bg-purple-500'\n                                }\n                            ].map((platform)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        showToast(\"已分享到\".concat(platform.name));\n                                        setShowShareModal(false);\n                                    },\n                                    className: \"\".concat(platform.color, \" rounded-xl p-4 text-white text-center hover:opacity-80 transition-opacity\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl mb-1\",\n                                            children: platform.icon\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 938,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs\",\n                                            children: platform.name\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 939,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, platform.name, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 930,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 921,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowShareModal(false),\n                            className: \"w-full py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors\",\n                            children: \"取消\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 944,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 910,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 909,\n                columnNumber: 9\n            }, this),\n            showApplyProject && selectedProject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20 max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"申请项目\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 959,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowApplyProject(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 960,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 958,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold text-white mb-2\",\n                                            children: selectedProject.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 970,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: selectedProject.description\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 971,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 969,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"预算范围\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 976,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 font-bold\",\n                                                    children: selectedProject.budget\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 977,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 975,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"项目周期\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 980,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-400 font-bold\",\n                                                    children: selectedProject.duration\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 981,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 979,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 974,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-white mb-2\",\n                                            children: \"个人简介\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 986,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            className: \"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500\",\n                                            rows: 3,\n                                            placeholder: \"请简要介绍您的相关经验和技能...\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 987,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 985,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-white mb-2\",\n                                            children: \"联系方式\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 995,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            className: \"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500\",\n                                            placeholder: \"请输入您的联系方式...\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 996,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 994,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-white mb-2\",\n                                            children: \"期望报酬\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1004,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            className: \"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500\",\n                                            placeholder: \"请输入您的期望报酬...\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1005,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1003,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowApplyProject(false),\n                                            className: \"flex-1 py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors\",\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1013,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowApplyProject(false);\n                                                showToast('申请已提交，请等待回复');\n                                            },\n                                            className: \"flex-1 py-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl text-white font-medium hover:from-orange-600 hover:to-red-600 transition-colors\",\n                                            children: \"提交申请\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1019,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1012,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 968,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 957,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 956,\n                columnNumber: 9\n            }, this),\n            showNFTModal && selectedContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"购买NFT\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1039,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowNFTModal(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1040,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 1038,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center text-3xl mx-auto mb-3\",\n                                            children: \"\\uD83C\\uDFA8\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1050,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold text-white\",\n                                            children: selectedContent.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1053,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: [\n                                                \"by \",\n                                                selectedContent.creator\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1054,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1049,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"NFT价格\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 1059,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400 font-bold\",\n                                                    children: \"0.1 ETH\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 1060,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1058,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Gas费用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 1063,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-400\",\n                                                    children: \"0.005 ETH\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 1064,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1062,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t border-white/10 pt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold\",\n                                                        children: \"总计\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 1068,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400 font-bold\",\n                                                        children: \"0.105 ETH\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 1069,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 1067,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1066,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1057,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"购买后您将获得该内容的NFT所有权证明，可在二级市场交易。\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1074,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowNFTModal(false),\n                                            className: \"flex-1 py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors\",\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1079,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowNFTModal(false);\n                                                showToast('NFT购买成功！');\n                                            },\n                                            className: \"flex-1 py-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl text-white font-medium hover:from-purple-600 hover:to-pink-600 transition-colors\",\n                                            children: \"确认购买\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1085,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1078,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 1048,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 1037,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 1036,\n                columnNumber: 9\n            }, this),\n            showTipModal && selectedContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-white/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"打赏创作者\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1105,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowTipModal(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1106,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 1104,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-2xl mx-auto mb-2\",\n                                            children: selectedContent.avatar\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1116,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: selectedContent.creator\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1119,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"感谢优质内容创作\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1120,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1115,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-3\",\n                                    children: [\n                                        10,\n                                        50,\n                                        100,\n                                        200,\n                                        500,\n                                        1000\n                                    ].map((amount)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowTipModal(false);\n                                                showToast(\"成功打赏 \".concat(amount, \" NGT\"));\n                                            },\n                                            className: \"py-3 bg-white/10 border border-white/20 rounded-lg text-white font-medium hover:bg-white/20 transition-colors\",\n                                            children: [\n                                                amount,\n                                                \" NGT\"\n                                            ]\n                                        }, amount, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1125,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1123,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        placeholder: \"自定义金额\",\n                                        className: \"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 1139,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1138,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowTipModal(false),\n                                    className: \"w-full py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors\",\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1146,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 1114,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 1103,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 1102,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n        lineNumber: 349,\n        columnNumber: 5\n    }, this);\n}\n_s(DiscoveryPage, \"p5BLHYhD7bS/EXdk2xWA/9sGK9M=\");\n_c = DiscoveryPage;\nvar _c;\n$RefreshReg$(_c, \"DiscoveryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/engineering/page.tsx\n"));

/***/ })

});