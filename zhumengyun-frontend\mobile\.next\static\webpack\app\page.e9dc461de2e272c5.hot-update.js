"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/GitHubBottomNavigation.tsx":
/*!***************************************************!*\
  !*** ./src/components/GitHubBottomNavigation.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ 中文底部导航),\n/* harmony export */   \"中文卡片\": () => (/* binding */ 中文卡片),\n/* harmony export */   \"中文容器\": () => (/* binding */ 中文容器),\n/* harmony export */   \"中文按钮\": () => (/* binding */ 中文按钮),\n/* harmony export */   \"中文页面布局\": () => (/* binding */ 中文页面布局)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Code,Compass,Home,MessageSquare,PlusCircle,Search,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Code,Compass,Home,MessageSquare,PlusCircle,Search,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/compass.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Code,Compass,Home,MessageSquare,PlusCircle,Search,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Code,Compass,Home,MessageSquare,PlusCircle,Search,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Code,Compass,Home,MessageSquare,PlusCircle,Search,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Code,Compass,Home,MessageSquare,PlusCircle,Search,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Code,Compass,Home,MessageSquare,PlusCircle,Search,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Code,Compass,Home,MessageSquare,PlusCircle,Search,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Code,Compass,Home,MessageSquare,PlusCircle,Search,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* __next_internal_client_entry_do_not_use__ default,中文页面布局,中文容器,中文卡片,中文按钮 auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nfunction 中文底部导航() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const navItems = [\n        {\n            id: 'home',\n            label: 'Home',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                lineNumber: 34,\n                columnNumber: 13\n            }, this),\n            path: '/'\n        },\n        {\n            id: 'engineering',\n            label: 'Discover',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                lineNumber: 40,\n                columnNumber: 13\n            }, this),\n            path: '/engineering'\n        },\n        {\n            id: 'creator',\n            label: 'Create',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                lineNumber: 46,\n                columnNumber: 13\n            }, this),\n            path: '/creator'\n        },\n        {\n            id: 'social',\n            label: 'Messages',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                lineNumber: 52,\n                columnNumber: 13\n            }, this),\n            path: '/social',\n            badge: 3\n        },\n        {\n            id: 'profile',\n            label: 'Profile',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                lineNumber: 59,\n                columnNumber: 13\n            }, this),\n            path: '/profile'\n        }\n    ];\n    const handleNavigation = (path)=>{\n        router.push(path);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"github-desktop-only fixed top-0 left-0 right-0 z-50 github-nav\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"github-container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between py-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-6 h-6 text-[#24292f]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-bold text-[#24292f]\",\n                                                children: \"NextGen 2025\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleNavigation(item.path),\n                                                className: \"github-nav-item \".concat(pathname === item.path ? 'active' : ''),\n                                                children: item.label\n                                            }, item.id, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"relative p-2 text-[#656d76] hover:text-[#24292f] transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -top-1 -right-1 w-3 h-3 bg-[#cf222e] rounded-full text-xs text-white flex items-center justify-center\",\n                                                children: \"2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-2 text-[#656d76] hover:text-[#24292f] transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"github-avatar-md\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-medium text-sm\",\n                                            children: \"U\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"github-mobile-only fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-[#d0d7de]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-around py-2\",\n                    children: navItems.map((item)=>{\n                        const isActive = pathname === item.path;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleNavigation(item.path),\n                            className: \"\\n                  flex flex-col items-center justify-center p-2 rounded-lg transition-all duration-150 ease-in-out\\n                  \".concat(isActive ? 'text-[#0969da] bg-[#ddf4ff]' : 'text-[#656d76] hover:text-[#24292f] hover:bg-[#f6f8fa]', \"\\n                \"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        item.icon,\n                                        item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-2 -right-2 w-4 h-4 bg-[#cf222e] rounded-full text-xs text-white flex items-center justify-center\",\n                                            children: item.badge\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium mt-1\",\n                                    children: item.label\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, item.id, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"github-mobile-only h-16\"\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(中文底部导航, \"0h+B63IiVHeDT9bDhB3JTwv8ebY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n// GitHub桌面版风格的页面布局组件\nfunction 中文页面布局(param) {\n    let { children, title, showNavigation = false } = param;\n    _s1();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const getNavClass = (path)=>{\n        return pathname === path ? 'github-nav-tab active' : 'github-nav-tab';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"github-desktop-layout\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"github-header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"github-header-nav\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-6 h-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"NextGen 2025\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/\",\n                                            className: getNavClass('/'),\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/engineering\",\n                                            className: getNavClass('/engineering'),\n                                            children: \"Discover\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/creator\",\n                                            className: getNavClass('/creator'),\n                                            children: \"Create\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/social\",\n                                            className: getNavClass('/social'),\n                                            children: \"Messages\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/profile\",\n                                            className: getNavClass('/profile'),\n                                            children: \"Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"github-search-desktop\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Code_Compass_Home_MessageSquare_PlusCircle_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"github-search-icon w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"搜索或跳转到...\",\n                                        className: \"github-search-input-desktop\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"github-main-content\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, this);\n}\n_s1(中文页面布局, \"xbyQPtUVMO7MNj7WjJlpdWqRcTo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n// 中文风格的容器组件\nfunction 中文容器(param) {\n    let { children, className = '' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"中文-容器 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n        lineNumber: 216,\n        columnNumber: 5\n    }, this);\n}\n// 中文风格的卡片组件\nfunction 中文卡片(param) {\n    let { children, hover = false, className = '' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"中文-卡片 \".concat(hover ? '中文-卡片-悬停' : '', \" \").concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n        lineNumber: 233,\n        columnNumber: 5\n    }, this);\n}\n// 中文风格的按钮组件\nfunction 中文按钮(param) {\n    let { children, variant = 'secondary', size = 'md', onClick, disabled = false, className = '' } = param;\n    const baseClass = '中文-按钮';\n    const variantClass = \"中文-按钮-\".concat(variant === 'primary' ? '主要' : variant === 'secondary' ? '次要' : variant === 'outline' ? '轮廓' : '危险');\n    const sizeClass = size !== 'md' ? \"中文-按钮-\".concat(size === 'sm' ? '小' : '大') : '';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        disabled: disabled,\n        className: \"\".concat(baseClass, \" \").concat(variantClass, \" \").concat(sizeClass, \" \").concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\GitHubBottomNavigation.tsx\",\n        lineNumber: 260,\n        columnNumber: 5\n    }, this);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/GitHubBottomNavigation.tsx\n"));

/***/ })

});