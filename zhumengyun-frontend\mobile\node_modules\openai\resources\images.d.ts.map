{"version": 3, "file": "images.d.ts", "sourceRoot": "", "sources": ["../src/resources/images.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAC1C,OAAO,KAAK,IAAI,MAAM,SAAS,CAAC;AAEhC,qBAAa,MAAO,SAAQ,WAAW;IACrC;;;;;;;;;OASG;IACH,eAAe,CACb,IAAI,EAAE,0BAA0B,EAChC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;IAIlC;;;;;;;;;;;OAWG;IACH,IAAI,CAAC,IAAI,EAAE,eAAe,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;IAI3F;;;;;;;;;;OAUG;IACH,QAAQ,CAAC,IAAI,EAAE,mBAAmB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;CAGpG;AAED;;GAEG;AACH,MAAM,WAAW,KAAK;IACpB;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;;;OAIG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;CACd;AAED,MAAM,MAAM,UAAU,GAAG,UAAU,GAAG,UAAU,GAAG,aAAa,CAAC;AAEjE;;GAEG;AACH,MAAM,WAAW,cAAc;IAC7B;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,IAAI,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;IAEpB;;OAEG;IACH,KAAK,CAAC,EAAE,cAAc,CAAC,KAAK,CAAC;CAC9B;AAED,yBAAiB,cAAc,CAAC;IAC9B;;OAEG;IACH,UAAiB,KAAK;QACpB;;WAEG;QACH,YAAY,EAAE,MAAM,CAAC;QAErB;;WAEG;QACH,oBAAoB,EAAE,KAAK,CAAC,kBAAkB,CAAC;QAE/C;;WAEG;QACH,aAAa,EAAE,MAAM,CAAC;QAEtB;;WAEG;QACH,YAAY,EAAE,MAAM,CAAC;KACtB;IAED,UAAiB,KAAK,CAAC;QACrB;;WAEG;QACH,UAAiB,kBAAkB;YACjC;;eAEG;YACH,YAAY,EAAE,MAAM,CAAC;YAErB;;eAEG;YACH,WAAW,EAAE,MAAM,CAAC;SACrB;KACF;CACF;AAED,MAAM,WAAW,0BAA0B;IACzC;;;OAGG;IACH,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC;IAEvB;;;OAGG;IACH,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,UAAU,GAAG,IAAI,CAAC;IAE1C;;OAEG;IACH,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAElB;;;;OAIG;IACH,eAAe,CAAC,EAAE,KAAK,GAAG,UAAU,GAAG,IAAI,CAAC;IAE5C;;;OAGG;IACH,IAAI,CAAC,EAAE,SAAS,GAAG,SAAS,GAAG,WAAW,GAAG,IAAI,CAAC;IAElD;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,MAAM,WAAW,eAAe;IAC9B;;;;;;;;OAQG;IACH,KAAK,EAAE,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAEhD;;;OAGG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;;;;;;;OAQG;IACH,UAAU,CAAC,EAAE,aAAa,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;IAEtD;;;;;OAKG;IACH,IAAI,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC;IAEvB;;;;OAIG;IACH,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,UAAU,GAAG,IAAI,CAAC;IAE1C;;OAEG;IACH,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAElB;;;;OAIG;IACH,OAAO,CAAC,EAAE,UAAU,GAAG,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;IAEjE;;;;;OAKG;IACH,eAAe,CAAC,EAAE,KAAK,GAAG,UAAU,GAAG,IAAI,CAAC;IAE5C;;;;OAIG;IACH,IAAI,CAAC,EAAE,SAAS,GAAG,SAAS,GAAG,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,MAAM,GAAG,IAAI,CAAC;IAEvF;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,MAAM,WAAW,mBAAmB;IAClC;;;;OAIG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;;;;;;;OAQG;IACH,UAAU,CAAC,EAAE,aAAa,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;IAEtD;;;;OAIG;IACH,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,UAAU,GAAG,IAAI,CAAC;IAE1C;;;OAGG;IACH,UAAU,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,IAAI,CAAC;IAEnC;;;OAGG;IACH,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAElB;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEnC;;;OAGG;IACH,aAAa,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;IAE/C;;;;;;;;OAQG;IACH,OAAO,CAAC,EAAE,UAAU,GAAG,IAAI,GAAG,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;IAExE;;;;;OAKG;IACH,eAAe,CAAC,EAAE,KAAK,GAAG,UAAU,GAAG,IAAI,CAAC;IAE5C;;;;;OAKG;IACH,IAAI,CAAC,EACD,MAAM,GACN,WAAW,GACX,WAAW,GACX,WAAW,GACX,SAAS,GACT,SAAS,GACT,WAAW,GACX,WAAW,GACX,IAAI,CAAC;IAET;;;;;OAKG;IACH,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,GAAG,IAAI,CAAC;IAEnC;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC;IAC9B,OAAO,EACL,KAAK,KAAK,IAAI,KAAK,EACnB,KAAK,UAAU,IAAI,UAAU,EAC7B,KAAK,cAAc,IAAI,cAAc,EACrC,KAAK,0BAA0B,IAAI,0BAA0B,EAC7D,KAAK,eAAe,IAAI,eAAe,EACvC,KAAK,mBAAmB,IAAI,mBAAmB,GAChD,CAAC;CACH"}