/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { XRSession } from '../session/XRSession.js';
interface XRSessionEventInit extends EventInit {
    session: XRSession;
}
export declare class XRSessionEvent extends Event {
    readonly session: XRSession;
    constructor(type: string, eventInitDict: XRSessionEventInit);
}
export interface XRSessionEventHandler {
    (evt: XRSessionEvent): any;
}
export {};
//# sourceMappingURL=XRSessionEvent.d.ts.map