'use client'

import { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useRouter } from 'next/navigation'

// 生态合作伙伴战略展示页组件
export default function EngineeringDiscovery() {
  const router = useRouter()

  // 状态管理
  const [activeCategory, setActiveCategory] = useState('technology')
  const [showPartnerModal, setShowPartnerModal] = useState(false)
  const [selectedPartner, setSelectedPartner] = useState<any>(null)
  const [showContactModal, setShowContactModal] = useState(false)

  // 生态合作伙伴数据
  const ecosystemPartners = {
    technology: [
      {
        id: 'openai',
        name: 'OpenAI',
        type: 'AI技术联盟',
        description: 'GPT系列模型深度集成，提供先进的自然语言处理能力',
        icon: '🤖',
        status: '深度集成',
        cooperation: ['GPT-4集成', 'API服务', '模型定制', '技术支持'],
        fields: ['自然语言处理', 'AI对话', '内容生成'],
        level: 'strategic',
        color: 'from-green-500 to-emerald-500'
      },
      {
        id: 'anthropic',
        name: 'Anthropic',
        type: 'AI技术联盟',
        description: 'Claude模型安全AI合作，确保AI系统的安全性和可靠性',
        icon: '🛡️',
        status: '战略合作',
        cooperation: ['Claude集成', '安全AI', '伦理审查', '技术咨询'],
        fields: ['AI安全', '伦理AI', '对话系统'],
        level: 'strategic',
        color: 'from-blue-500 to-cyan-500'
      },
      {
        id: 'google',
        name: 'Google',
        type: 'AI技术联盟',
        description: 'Gemini模型和云服务合作，提供多模态AI能力',
        icon: '🔍',
        status: '技术授权',
        cooperation: ['Gemini集成', '云计算', '搜索技术', 'AI工具'],
        fields: ['多模态AI', '云服务', '搜索引擎'],
        level: 'strategic',
        color: 'from-red-500 to-orange-500'
      },
      {
        id: 'baidu',
        name: '百度',
        type: 'AI技术联盟',
        description: '文心一言中文AI能力合作，优化中文语言处理',
        icon: '🇨🇳',
        status: '深度集成',
        cooperation: ['文心一言', '中文NLP', '本土化', '技术支持'],
        fields: ['中文AI', '本土化服务', '语言模型'],
        level: 'strategic',
        color: 'from-purple-500 to-indigo-500'
      },
      {
        id: 'apple',
        name: 'Apple',
        type: '硬件生态合作',
        description: 'Vision Pro深度适配和优化，提供沉浸式AR体验',
        icon: '🍎',
        status: '深度适配',
        cooperation: ['Vision Pro', 'AR应用', '硬件优化', '生态集成'],
        fields: ['AR/VR', '硬件适配', '移动端'],
        level: 'hardware',
        color: 'from-gray-500 to-slate-500'
      },
      {
        id: 'nvidia',
        name: 'NVIDIA',
        type: '硬件生态合作',
        description: 'AI芯片和云计算合作，提供强大的计算能力支持',
        icon: '💚',
        status: '技术合作',
        cooperation: ['GPU计算', 'AI芯片', '云计算', '性能优化'],
        fields: ['AI计算', 'GPU加速', '高性能计算'],
        level: 'hardware',
        color: 'from-green-600 to-emerald-600'
      }
    ],
    content: [
      {
        id: 'mit',
        name: 'MIT',
        type: '工程教育机构',
        description: '计算机科学和工程课程合作，提供世界顶级的技术教育内容',
        icon: '🎓',
        status: '内容授权',
        cooperation: ['课程内容', '技术培训', '认证体系', '学术合作'],
        fields: ['计算机科学', '工程技术', '在线教育'],
        level: 'education',
        color: 'from-blue-600 to-indigo-600'
      },
      {
        id: 'stanford',
        name: '斯坦福大学',
        type: '工程教育机构',
        description: 'AI和机器学习课程授权，分享前沿的人工智能研究成果',
        icon: '🌲',
        status: '学术合作',
        cooperation: ['AI课程', '机器学习', '研究合作', '人才培养'],
        fields: ['人工智能', '机器学习', '学术研究'],
        level: 'education',
        color: 'from-red-600 to-pink-600'
      },
      {
        id: 'tsinghua',
        name: '清华大学',
        type: '工程教育机构',
        description: '工程技术课程中文化，为中国用户提供本土化的优质教育内容',
        icon: '🏛️',
        status: '战略合作',
        cooperation: ['中文课程', '本土化', '工程教育', '产学研'],
        fields: ['工程技术', '中文教育', '产学合作'],
        level: 'education',
        color: 'from-purple-600 to-violet-600'
      },
      {
        id: 'coursera',
        name: 'Coursera',
        type: '在线教育平台',
        description: '在线教育平台内容合作，扩大优质教育资源的覆盖范围',
        icon: '💻',
        status: '平台集成',
        cooperation: ['在线课程', '平台集成', '内容分发', '用户认证'],
        fields: ['在线教育', '课程平台', '技能认证'],
        level: 'platform',
        color: 'from-blue-500 to-cyan-500'
      },
      {
        id: 'ieee',
        name: 'IEEE',
        type: '专业认证机构',
        description: '工程师专业认证标准，确保技术人员的专业水平和能力',
        icon: '⚡',
        status: '认证合作',
        cooperation: ['专业认证', '标准制定', '技术规范', '行业标准'],
        fields: ['工程认证', '技术标准', '专业规范'],
        level: 'certification',
        color: 'from-yellow-500 to-orange-500'
      },
      {
        id: 'aws',
        name: 'AWS',
        type: '云计算认证',
        description: '云计算技能认证，提供专业的云服务技术培训和认证',
        icon: '☁️',
        status: '技术合作',
        cooperation: ['云计算认证', '技术培训', '平台集成', '服务支持'],
        fields: ['云计算', '技术认证', '平台服务'],
        level: 'certification',
        color: 'from-orange-500 to-red-500'
      },
      {
        id: 'top-engineers',
        name: '顶级工程师',
        type: '内容创作者生态',
        description: '邀请知名工程师入驻，分享前沿技术经验和工程实践',
        icon: '👨‍💻',
        status: '创作者入驻',
        cooperation: ['技术分享', '项目展示', '经验传授', '社区建设'],
        fields: ['工程实践', '技术分享', '项目经验'],
        level: 'creator',
        color: 'from-indigo-500 to-purple-500'
      },
      {
        id: 'tech-bloggers',
        name: '技术博主',
        type: '内容创作者生态',
        description: '科技内容创作者合作，打造优质的技术内容生态',
        icon: '✍️',
        status: '内容合作',
        cooperation: ['内容创作', '技术科普', '趋势分析', '社区互动'],
        fields: ['技术科普', '内容创作', '知识分享'],
        level: 'creator',
        color: 'from-pink-500 to-rose-500'
      },
      {
        id: 'open-source',
        name: '开源社区',
        type: '内容创作者生态',
        description: '开源项目深度合作，推动开源技术发展和社区建设',
        icon: '🔓',
        status: '社区合作',
        cooperation: ['开源项目', '代码贡献', '社区维护', '技术推广'],
        fields: ['开源开发', '社区建设', '技术推广'],
        level: 'community',
        color: 'from-emerald-500 to-teal-500'
      },
      {
        id: 'tech-conferences',
        name: '技术会议',
        type: '内容创作者生态',
        description: '全球技术大会内容授权，提供最新的技术趋势和行业洞察',
        icon: '🎤',
        status: '内容授权',
        cooperation: ['会议内容', '演讲录制', '技术报告', '行业洞察'],
        fields: ['技术会议', '行业趋势', '专业演讲'],
        level: 'conference',
        color: 'from-violet-500 to-purple-500'
      }
    ],
    industry: [
      {
        id: 'siemens',
        name: '西门子',
        type: '智能制造',
        description: '工业4.0解决方案合作，推动制造业数字化转型',
        icon: '🏭',
        status: '战略合作',
        cooperation: ['工业4.0', '数字化工厂', 'IoT集成', '智能制造'],
        fields: ['智能制造', '工业自动化', '数字化转型'],
        level: 'manufacturing',
        color: 'from-blue-600 to-cyan-600'
      },
      {
        id: 'autodesk',
        name: 'Autodesk',
        type: '建筑工程生态',
        description: 'BIM软件深度集成，AutoCAD、Revit、Civil 3D插件开发',
        icon: '🏗️',
        status: '深度集成',
        cooperation: ['BIM集成', '插件开发', 'API对接', '技术支持'],
        fields: ['BIM技术', '建筑设计', '工程软件'],
        level: 'construction',
        color: 'from-orange-600 to-red-600'
      },
      {
        id: 'bentley',
        name: 'Bentley',
        type: '建筑工程生态',
        description: '基础设施工程软件，MicroStation、STAAD.Pro集成',
        icon: '🌉',
        status: '技术集成',
        cooperation: ['基础设施软件', '工程分析', '项目协作', '数据互通'],
        fields: ['基础设施', '工程分析', '项目管理'],
        level: 'construction',
        color: 'from-green-600 to-emerald-600'
      },
      {
        id: 'cscec',
        name: '中建集团',
        type: '建筑工程生态',
        description: '大型工程项目合作，数字化施工管理实践应用',
        icon: '🏢',
        status: '项目合作',
        cooperation: ['大型项目', '数字化施工', 'BIM应用', '管理创新'],
        fields: ['大型工程', '施工管理', '项目实施'],
        level: 'construction',
        color: 'from-red-600 to-pink-600'
      },
      {
        id: 'vanke',
        name: '万科集团',
        type: '建筑工程生态',
        description: '智慧建筑解决方案，住宅产业化标准制定',
        icon: '🏠',
        status: '标准制定',
        cooperation: ['智慧建筑', '产业化标准', '住宅创新', '绿色建筑'],
        fields: ['智慧建筑', '住宅产业', '标准制定'],
        level: 'construction',
        color: 'from-purple-600 to-indigo-600'
      },
      {
        id: 'github',
        name: 'GitHub',
        type: '软件开发',
        description: '代码托管和协作平台，开源生态深度合作',
        icon: '🐙',
        status: '平台集成',
        cooperation: ['代码托管', '开源协作', '版本控制', '社区建设'],
        fields: ['代码管理', '开源生态', '开发协作'],
        level: 'development',
        color: 'from-gray-600 to-slate-600'
      }
    ]
  }

  // 处理合作伙伴详情
  const handlePartnerDetail = (partner: any) => {
    setSelectedPartner(partner)
    setShowPartnerModal(true)
  }

  // 处理合作洽谈
  const handleContact = () => {
    setShowContactModal(true)
  }

  // 获取当前分类的合作伙伴
  const getCurrentPartners = () => {
    return ecosystemPartners[activeCategory as keyof typeof ecosystemPartners] || []
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white">
      {/* 头部导航 */}
      <div className="sticky top-0 z-40 bg-black/20 backdrop-blur-lg border-b border-white/10">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold">生态合作伙伴</h1>
              <p className="text-sm text-gray-400">构建全球技术生态联盟</p>
            </div>
            <button
              onClick={handleContact}
              className="px-4 py-2 bg-blue-500 rounded-full text-sm font-medium hover:bg-blue-600 transition-colors"
            >
              合作洽谈
            </button>
          </div>
        </div>
      </div>

      {/* 分类导航 */}
      <div className="px-4 py-6">
        <div className="flex space-x-2 overflow-x-auto">
          {[
            { id: 'technology', name: '技术合作伙伴', icon: '⚡' },
            { id: 'content', name: '内容生态合作', icon: '📚' },
            { id: 'industry', name: '行业应用合作', icon: '🏭' }
          ].map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-full whitespace-nowrap transition-colors ${
                activeCategory === category.id
                  ? 'bg-blue-500 text-white'
                  : 'bg-white/10 text-gray-300 hover:bg-white/20'
              }`}
            >
              <span className="text-lg">{category.icon}</span>
              <span className="font-medium">{category.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* 合作伙伴列表 */}
      <div className="px-4 pb-20">
        <div className="space-y-4">
          {getCurrentPartners().map((partner, index) => (
            <motion.div
              key={partner.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => handlePartnerDetail(partner)}
              className="bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10 hover:bg-white/10 transition-colors cursor-pointer"
            >
              {/* 合作伙伴头部 */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${partner.color} flex items-center justify-center text-2xl`}>
                    {partner.icon}
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-white">{partner.name}</h3>
                    <p className="text-sm text-gray-400">{partner.description}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-bold text-blue-400">{partner.type}</div>
                  <div className="text-xs text-gray-400">{partner.status}</div>
                </div>
              </div>

              {/* 合作领域 */}
              <div className="mb-4">
                <div className="flex flex-wrap gap-2">
                  {partner.fields.map((field, idx) => (
                    <span
                      key={idx}
                      className="px-3 py-1 bg-white/10 rounded-full text-xs text-gray-300"
                    >
                      {field}
                    </span>
                  ))}
                </div>
              </div>

              {/* 底部信息 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-1">
                    <span className="text-xs text-gray-400">合作状态</span>
                    <span className="text-sm font-medium text-green-400">{partner.status}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="text-xs text-gray-400">合作项目</span>
                    <span className="text-sm font-medium text-blue-400">{partner.cooperation.length}+</span>
                  </div>
                </div>
                <button className="px-4 py-2 bg-blue-500/20 border border-blue-500/30 rounded-full text-sm text-blue-400 hover:bg-blue-500/30 transition-colors">
                  查看详情
                </button>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* 合作伙伴详情弹窗 */}
      <AnimatePresence>
        {showPartnerModal && selectedPartner && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4"
            onClick={() => setShowPartnerModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20 max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              {/* 合作伙伴头部 */}
              <div className="text-center mb-6">
                <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${selectedPartner.color} flex items-center justify-center text-3xl mx-auto mb-4`}>
                  {selectedPartner.icon}
                </div>
                <h3 className="text-xl font-bold text-white mb-2">{selectedPartner.name}</h3>
                <p className="text-gray-400 text-sm">{selectedPartner.description}</p>
              </div>

              {/* 合作类型和状态 */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <div className="text-lg font-bold text-blue-400">{selectedPartner.type}</div>
                  <div className="text-xs text-gray-400">合作类型</div>
                </div>
                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <div className="text-lg font-bold text-green-400">{selectedPartner.status}</div>
                  <div className="text-xs text-gray-400">合作状态</div>
                </div>
              </div>

              {/* 合作项目 */}
              <div className="mb-6">
                <h4 className="text-white font-bold mb-3">合作项目</h4>
                <div className="space-y-2">
                  {selectedPartner.cooperation.map((project: string, index: number) => (
                    <div key={index} className="flex items-center space-x-2">
                      <span className="text-green-400">✓</span>
                      <span className="text-gray-300 text-sm">{project}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* 合作领域 */}
              <div className="mb-6">
                <h4 className="text-white font-bold mb-3">合作领域</h4>
                <div className="flex flex-wrap gap-2">
                  {selectedPartner.fields.map((field: string, index: number) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-blue-500/20 border border-blue-500/30 rounded-full text-xs text-blue-400"
                    >
                      {field}
                    </span>
                  ))}
                </div>
                <div className="mt-3 text-center">
                  <span className="text-sm text-gray-400">合作等级: </span>
                  <span className="text-lg font-bold text-purple-400">{selectedPartner.level}</span>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    setShowPartnerModal(false)
                    setShowContactModal(true)
                  }}
                  className="flex-1 py-3 bg-blue-500 rounded-xl text-white font-medium hover:bg-blue-600 transition-colors"
                >
                  合作洽谈
                </button>
                <button
                  onClick={() => setShowPartnerModal(false)}
                  className="flex-1 py-3 bg-gray-700 rounded-xl text-white font-medium hover:bg-gray-600 transition-colors"
                >
                  关闭
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 合作洽谈弹窗 */}
      <AnimatePresence>
        {showContactModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4"
            onClick={() => setShowContactModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center mb-6">
                <div className="text-4xl mb-4">🤝</div>
                <h3 className="text-xl font-bold text-white mb-2">合作洽谈</h3>
                <p className="text-gray-400 text-sm">携手共建全球技术生态联盟</p>
              </div>

              <div className="space-y-4 mb-6">
                <div className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">📧</span>
                    <div>
                      <div className="text-white font-medium">合作邮箱</div>
                      <div className="text-gray-400 text-sm"><EMAIL></div>
                    </div>
                  </div>
                </div>

                <div className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">📱</span>
                    <div>
                      <div className="text-white font-medium">商务热线</div>
                      <div className="text-gray-400 text-sm">400-888-2025</div>
                    </div>
                  </div>
                </div>

                <div className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">🌐</span>
                    <div>
                      <div className="text-white font-medium">合作门户</div>
                      <div className="text-gray-400 text-sm">partner.nextgen2025.com</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 mb-6">
                <div className="text-blue-200 text-sm">
                  <div className="font-medium mb-2">🤝 合作承诺</div>
                  <div className="space-y-1 text-xs">
                    <p>• 24小时内响应合作咨询</p>
                    <p>• 提供详细的合作方案</p>
                    <p>• 专业商务团队对接</p>
                    <p>• 长期战略合作伙伴关系</p>
                  </div>
                </div>
              </div>

              <button
                onClick={() => setShowContactModal(false)}
                className="w-full py-3 bg-gray-700 rounded-xl text-white font-medium hover:bg-gray-600 transition-colors"
              >
                关闭
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
