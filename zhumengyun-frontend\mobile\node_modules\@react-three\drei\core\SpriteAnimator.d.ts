import * as React from 'react';
import { Vector3 } from '@react-three/fiber';
import * as THREE from 'three';
import { Instances, Instance } from './Instances';
import { SpriteData } from './useSpriteLoader';
type AnimationEventData = {
    currentFrameName: string;
    currentFrame: number;
};
type CommonProps<T, U, V> = Pick<T & U & V, keyof T & keyof U & keyof V>;
type CommonMeshProps = CommonProps<React.ComponentProps<'mesh'>, React.ComponentProps<typeof Instance>, React.ComponentProps<typeof Instances>>;
export type SpriteAnimatorProps = {
    startFrame?: number;
    endFrame?: number;
    fps?: number;
    frameName?: string;
    textureDataURL?: string;
    textureImageURL?: string;
    loop?: boolean;
    numberOfFrames?: number;
    autoPlay?: boolean;
    animationNames?: Array<string>;
    onStart?: (data: AnimationEventData) => void;
    onEnd?: (data: AnimationEventData) => void;
    onLoopEnd?: (data: AnimationEventData) => void;
    onFrame?: (data: AnimationEventData) => void;
    play?: boolean;
    pause?: boolean;
    flipX?: boolean;
    alphaTest?: number;
    asSprite?: boolean;
    offset?: number;
    playBackwards?: boolean;
    resetOnEnd?: boolean;
    instanceItems?: Vector3[];
    maxItems?: number;
    spriteDataset?: {
        spriteTexture: THREE.Texture;
        spriteData: SpriteData | null;
        aspect: Vector3;
    } | null;
    canvasRenderingContext2DSettings?: CanvasRenderingContext2DSettings;
    roundFramePosition?: boolean;
    meshProps?: CommonMeshProps;
} & JSX.IntrinsicElements['group'];
type SpriteAnimatorState = {
    current?: number;
    offset?: number;
    imageUrl?: string;
    hasEnded: boolean;
    ref: React.Ref<THREE.Group>;
};
export declare function useSpriteAnimator(): SpriteAnimatorState | null;
export declare const SpriteAnimator: React.ForwardRefExoticComponent<Omit<SpriteAnimatorProps, "ref"> & React.RefAttributes<THREE.Group>>;
export {};
