{"version": 3, "file": "MorphAnimMesh.js", "sources": ["../../src/misc/MorphAnimMesh.js"], "sourcesContent": ["import { AnimationClip, AnimationMixer, <PERSON><PERSON> } from 'three'\n\nclass MorphAnimMesh extends Mesh {\n  constructor(geometry, material) {\n    super(geometry, material)\n\n    this.type = 'MorphAnimMesh'\n\n    this.mixer = new AnimationMixer(this)\n    this.activeAction = null\n  }\n\n  setDirectionForward() {\n    this.mixer.timeScale = 1.0\n  }\n\n  setDirectionBackward() {\n    this.mixer.timeScale = -1.0\n  }\n\n  playAnimation(label, fps) {\n    if (this.activeAction) {\n      this.activeAction.stop()\n      this.activeAction = null\n    }\n\n    const clip = AnimationClip.findByName(this, label)\n\n    if (clip) {\n      const action = this.mixer.clipAction(clip)\n      action.timeScale = (clip.tracks.length * fps) / clip.duration\n      this.activeAction = action.play()\n    } else {\n      throw new Error('THREE.MorphAnimMesh: animations[' + label + '] undefined in .playAnimation()')\n    }\n  }\n\n  updateAnimation(delta) {\n    this.mixer.update(delta)\n  }\n\n  copy(source, recursive) {\n    super.copy(source, recursive)\n\n    this.mixer = new AnimationMixer(this)\n\n    return this\n  }\n}\n\nexport { MorphAnimMesh }\n"], "names": [], "mappings": ";AAEA,MAAM,sBAAsB,KAAK;AAAA,EAC/B,YAAY,UAAU,UAAU;AAC9B,UAAM,UAAU,QAAQ;AAExB,SAAK,OAAO;AAEZ,SAAK,QAAQ,IAAI,eAAe,IAAI;AACpC,SAAK,eAAe;AAAA,EACrB;AAAA,EAED,sBAAsB;AACpB,SAAK,MAAM,YAAY;AAAA,EACxB;AAAA,EAED,uBAAuB;AACrB,SAAK,MAAM,YAAY;AAAA,EACxB;AAAA,EAED,cAAc,OAAO,KAAK;AACxB,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,KAAM;AACxB,WAAK,eAAe;AAAA,IACrB;AAED,UAAM,OAAO,cAAc,WAAW,MAAM,KAAK;AAEjD,QAAI,MAAM;AACR,YAAM,SAAS,KAAK,MAAM,WAAW,IAAI;AACzC,aAAO,YAAa,KAAK,OAAO,SAAS,MAAO,KAAK;AACrD,WAAK,eAAe,OAAO,KAAM;AAAA,IACvC,OAAW;AACL,YAAM,IAAI,MAAM,qCAAqC,QAAQ,iCAAiC;AAAA,IAC/F;AAAA,EACF;AAAA,EAED,gBAAgB,OAAO;AACrB,SAAK,MAAM,OAAO,KAAK;AAAA,EACxB;AAAA,EAED,KAAK,QAAQ,WAAW;AACtB,UAAM,KAAK,QAAQ,SAAS;AAE5B,SAAK,QAAQ,IAAI,eAAe,IAAI;AAEpC,WAAO;AAAA,EACR;AACH;"}