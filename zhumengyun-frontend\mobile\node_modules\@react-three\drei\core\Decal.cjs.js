"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),o=require("@react-three/fiber"),n=require("three-stdlib");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var c=a(e),s=i(t),l=i(r);function u(e=[0,0,0]){return function(e){return Array.isArray(e)}(e)?e:e instanceof l.Vector3||e instanceof l.Euler?[e.x,e.y,e.z]:[e,e,e]}const p=s.forwardRef((function({debug:e,depthTest:t=!1,polygonOffsetFactor:r=-10,map:a,mesh:i,children:p,position:f,rotation:m,scale:y,...d},b){const h=s.useRef(null);s.useImperativeHandle(b,(()=>h.current));const g=s.useRef(null),w=s.useRef({position:new l.Vector3,rotation:new l.Euler,scale:new l.Vector3(1,1,1)});return s.useLayoutEffect((()=>{const e=(null==i?void 0:i.current)||h.current.parent,t=h.current;if(!(e instanceof l.Mesh))throw new Error('Decal must have a Mesh as parent or specify its "mesh" prop');if(e){o.applyProps(w.current,{position:f,scale:y});const r=e.matrixWorld.clone();if(e.matrixWorld.identity(),m&&"number"!=typeof m)o.applyProps(w.current,{rotation:m});else{const t=new l.Object3D;t.position.copy(w.current.position);const r=e.geometry.attributes.position.array;void 0===e.geometry.attributes.normal&&e.geometry.computeVertexNormals();const n=e.geometry.attributes.normal.array;let a=1/0;new l.Vector3;let i=new l.Vector3;const c=t.position.x,s=t.position.y,u=t.position.z,p=r.length;let f=-1;for(let e=0;e<p;e+=3){const t=r[e]-c,o=r[e+1]-s,n=r[e+2]-u,i=t*t+o*o+n*n;i<a&&(a=i,f=e)}i.fromArray(n,f),t.lookAt(t.position.clone().add(i)),t.rotateZ(Math.PI),t.rotateY(Math.PI),"number"==typeof m&&t.rotateZ(m),o.applyProps(w.current,{rotation:t.rotation})}return t.geometry=new n.DecalGeometry(e,w.current.position,w.current.rotation,w.current.scale),e.matrixWorld=r,()=>{t.geometry.dispose()}}}),[i,...u(f),...u(y),...u(m)]),s.useLayoutEffect((()=>{g.current&&(o.applyProps(g.current,w.current),g.current.traverse((e=>e.raycast=()=>null)))}),[e]),s.createElement("mesh",c.default({ref:h,"material-transparent":!0,"material-polygonOffset":!0,"material-polygonOffsetFactor":r,"material-depthTest":t,"material-map":a},d),p,e&&s.createElement("mesh",{ref:g},s.createElement("boxGeometry",null),s.createElement("meshNormalMaterial",{wireframe:!0}),s.createElement("axesHelper",null)))}));exports.Decal=p;
