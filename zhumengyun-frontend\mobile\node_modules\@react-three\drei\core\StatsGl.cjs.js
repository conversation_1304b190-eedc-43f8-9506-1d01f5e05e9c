"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@react-three/fiber"),t=require("react"),r=require("stats-gl");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function l(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var o=l(t),u=n(r);const s=o.forwardRef((function({className:t,parent:r,id:n,clearStatsGlStyle:l,...s},c){const a=e.useThree((e=>e.gl)),d=o.useMemo((()=>{const e=new u.default({...s});return e.init(a),e}),[a]);return o.useImperativeHandle(c,(()=>d.domElement),[d]),o.useEffect((()=>{if(d){const o=r&&r.current||document.body;null==o||o.appendChild(d.domElement),d.domElement.querySelectorAll("canvas").forEach((e=>{e.style.removeProperty("position")})),n&&(d.domElement.id=n),l&&d.domElement.removeAttribute("style"),d.domElement.removeAttribute("style");const u=(null!=t?t:"").split(" ").filter((e=>e));u.length&&d.domElement.classList.add(...u);const s=e.addAfterEffect((()=>d.update()));return()=>{u.length&&d.domElement.classList.remove(...u),null==o||o.removeChild(d.domElement),s()}}}),[r,d,t,n,l]),null}));exports.StatsGl=s;
