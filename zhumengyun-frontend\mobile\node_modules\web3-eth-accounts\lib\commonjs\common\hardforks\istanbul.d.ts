declare const _default: {
    name: string;
    comment: string;
    url: string;
    status: string;
    gasConfig: {};
    gasPrices: {
        blake2Round: {
            v: number;
            d: string;
        };
        ecAdd: {
            v: number;
            d: string;
        };
        ecMul: {
            v: number;
            d: string;
        };
        ecPairing: {
            v: number;
            d: string;
        };
        ecPairingWord: {
            v: number;
            d: string;
        };
        txDataNonZero: {
            v: number;
            d: string;
        };
        sstoreSentryGasEIP2200: {
            v: number;
            d: string;
        };
        sstoreNoopGasEIP2200: {
            v: number;
            d: string;
        };
        sstoreDirtyGasEIP2200: {
            v: number;
            d: string;
        };
        sstoreInitGasEIP2200: {
            v: number;
            d: string;
        };
        sstoreInitRefundEIP2200: {
            v: number;
            d: string;
        };
        sstoreCleanGasEIP2200: {
            v: number;
            d: string;
        };
        sstoreCleanRefundEIP2200: {
            v: number;
            d: string;
        };
        sstoreClearRefundEIP2200: {
            v: number;
            d: string;
        };
        balance: {
            v: number;
            d: string;
        };
        extcodehash: {
            v: number;
            d: string;
        };
        chainid: {
            v: number;
            d: string;
        };
        selfbalance: {
            v: number;
            d: string;
        };
        sload: {
            v: number;
            d: string;
        };
    };
    vm: {};
    pow: {};
};
export default _default;
