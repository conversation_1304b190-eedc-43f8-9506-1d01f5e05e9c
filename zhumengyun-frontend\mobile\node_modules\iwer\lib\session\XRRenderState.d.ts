/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_RENDER_STATE } from '../private.js';
import { XRWebGLLayer } from '../layers/XRWebGLLayer.js';
export declare class XRRenderState {
    [P_RENDER_STATE]: {
        depthNear: number;
        depthFar: number;
        inlineVerticalFieldOfView: number | null;
        baseLayer: XRWebGLLayer | null;
    };
    constructor(init?: Partial<XRRenderStateInit>, oldState?: XRRenderState);
    get depthNear(): number;
    get depthFar(): number;
    get inlineVerticalFieldOfView(): number | null;
    get baseLayer(): XRWebGLLayer | null;
}
export interface XRRenderStateInit {
    depthNear?: number;
    depthFar?: number;
    inlineVerticalFieldOfView?: number;
    baseLayer?: XRWebGLLayer;
}
//# sourceMappingURL=XRRenderState.d.ts.map