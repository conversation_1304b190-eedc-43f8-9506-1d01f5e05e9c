/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
declare class PolyfillDOMPointReadOnly {
    readonly x: number;
    readonly y: number;
    readonly z: number;
    readonly w: number;
    constructor(x?: number, y?: number, z?: number, w?: number);
    static fromPoint(other: DOMPointInit): PolyfillDOMPointReadOnly;
    matrixTransform(_matrix: DOMMatrixReadOnly): DOMPointReadOnly;
    toJSON(): any;
}
export declare const DOMPointReadOnly: typeof PolyfillDOMPointReadOnly | {
    new (x?: number | undefined, y?: number | undefined, z?: number | undefined, w?: number | undefined): DOMPointReadOnly;
    prototype: DOMPointReadOnly;
    fromPoint(other?: DOMPointInit | undefined): DOMPointReadOnly;
};
export {};
//# sourceMappingURL=DOMPointReadOnly.d.ts.map