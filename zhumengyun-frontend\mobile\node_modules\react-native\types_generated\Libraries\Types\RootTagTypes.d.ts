/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<64e53c3a0aa4c92759f7d75091ab7325>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Types/RootTagTypes.js
 */

export type { RootTag } from "../ReactNative/RootTag";
