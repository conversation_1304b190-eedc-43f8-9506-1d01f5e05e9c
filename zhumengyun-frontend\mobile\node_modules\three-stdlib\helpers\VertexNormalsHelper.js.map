{"version": 3, "file": "VertexNormalsHelper.js", "sources": ["../../src/helpers/VertexNormalsHelper.js"], "sourcesContent": ["import { BufferGeometry, Float32BufferAttribute, LineSegments, LineBasicMaterial, Matrix3, Vector3 } from 'three'\n\nconst _v1 = /* @__PURE__ */ new Vector3()\nconst _v2 = /* @__PURE__ */ new Vector3()\nconst _normalMatrix = /* @__PURE__ */ new Matrix3()\n\nclass VertexNormalsHelper extends LineSegments {\n  constructor(object, size = 1, color = 0xff0000) {\n    const geometry = new BufferGeometry()\n\n    const nNormals = object.geometry.attributes.normal.count\n    const positions = new Float32BufferAttribute(nNormals * 2 * 3, 3)\n\n    geometry.setAttribute('position', positions)\n\n    super(geometry, new LineBasicMaterial({ color, toneMapped: false }))\n\n    this.object = object\n    this.size = size\n    this.type = 'VertexNormalsHelper'\n\n    //\n\n    this.matrixAutoUpdate = false\n\n    this.update()\n  }\n\n  update() {\n    this.object.updateMatrixWorld(true)\n\n    _normalMatrix.getNormalMatrix(this.object.matrixWorld)\n\n    const matrixWorld = this.object.matrixWorld\n\n    const position = this.geometry.attributes.position\n\n    //\n\n    const objGeometry = this.object.geometry\n\n    if (objGeometry) {\n      const objPos = objGeometry.attributes.position\n\n      const objNorm = objGeometry.attributes.normal\n\n      let idx = 0\n\n      // for simplicity, ignore index and drawcalls, and render every normal\n\n      for (let j = 0, jl = objPos.count; j < jl; j++) {\n        _v1.fromBufferAttribute(objPos, j).applyMatrix4(matrixWorld)\n\n        _v2.fromBufferAttribute(objNorm, j)\n\n        _v2.applyMatrix3(_normalMatrix).normalize().multiplyScalar(this.size).add(_v1)\n\n        position.setXYZ(idx, _v1.x, _v1.y, _v1.z)\n\n        idx = idx + 1\n\n        position.setXYZ(idx, _v2.x, _v2.y, _v2.z)\n\n        idx = idx + 1\n      }\n    }\n\n    position.needsUpdate = true\n  }\n\n  dispose() {\n    this.geometry.dispose()\n    this.material.dispose()\n  }\n}\n\nexport { VertexNormalsHelper }\n"], "names": [], "mappings": ";AAEA,MAAM,MAAsB,oBAAI,QAAS;AACzC,MAAM,MAAsB,oBAAI,QAAS;AACzC,MAAM,gBAAgC,oBAAI,QAAS;AAEnD,MAAM,4BAA4B,aAAa;AAAA,EAC7C,YAAY,QAAQ,OAAO,GAAG,QAAQ,UAAU;AAC9C,UAAM,WAAW,IAAI,eAAgB;AAErC,UAAM,WAAW,OAAO,SAAS,WAAW,OAAO;AACnD,UAAM,YAAY,IAAI,uBAAuB,WAAW,IAAI,GAAG,CAAC;AAEhE,aAAS,aAAa,YAAY,SAAS;AAE3C,UAAM,UAAU,IAAI,kBAAkB,EAAE,OAAO,YAAY,MAAK,CAAE,CAAC;AAEnE,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,OAAO;AAIZ,SAAK,mBAAmB;AAExB,SAAK,OAAQ;AAAA,EACd;AAAA,EAED,SAAS;AACP,SAAK,OAAO,kBAAkB,IAAI;AAElC,kBAAc,gBAAgB,KAAK,OAAO,WAAW;AAErD,UAAM,cAAc,KAAK,OAAO;AAEhC,UAAM,WAAW,KAAK,SAAS,WAAW;AAI1C,UAAM,cAAc,KAAK,OAAO;AAEhC,QAAI,aAAa;AACf,YAAM,SAAS,YAAY,WAAW;AAEtC,YAAM,UAAU,YAAY,WAAW;AAEvC,UAAI,MAAM;AAIV,eAAS,IAAI,GAAG,KAAK,OAAO,OAAO,IAAI,IAAI,KAAK;AAC9C,YAAI,oBAAoB,QAAQ,CAAC,EAAE,aAAa,WAAW;AAE3D,YAAI,oBAAoB,SAAS,CAAC;AAElC,YAAI,aAAa,aAAa,EAAE,UAAW,EAAC,eAAe,KAAK,IAAI,EAAE,IAAI,GAAG;AAE7E,iBAAS,OAAO,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAExC,cAAM,MAAM;AAEZ,iBAAS,OAAO,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAExC,cAAM,MAAM;AAAA,MACb;AAAA,IACF;AAED,aAAS,cAAc;AAAA,EACxB;AAAA,EAED,UAAU;AACR,SAAK,SAAS,QAAS;AACvB,SAAK,SAAS,QAAS;AAAA,EACxB;AACH;"}