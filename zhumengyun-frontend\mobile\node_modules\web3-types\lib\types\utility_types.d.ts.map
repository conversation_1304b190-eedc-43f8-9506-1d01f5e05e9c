{"version": 3, "file": "utility_types.d.ts", "sourceRoot": "", "sources": ["../../src/utility_types.ts"], "names": [], "mappings": "AAiBA,OAAO,EAAE,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAGhD,MAAM,MAAM,OAAO,CAAC,CAAC,IAAI;IACxB,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAC9B,CAAC;AAEF,MAAM,MAAM,eAAe,GAAG;IAC7B,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,CAAC,EAAE,OAAO,CAAC;CACnB,CAAC;AAEF,MAAM,MAAM,QAAQ,CAAC,CAAC,EAAE,CAAC,SAAS,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9E,MAAM,MAAM,aAAa,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC;AAEtE,MAAM,MAAM,WAAW,GAAG;IACzB,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,aAAa,CAAC;CACrB,CAAC;AAEF,MAAM,MAAM,sBAAsB,GAAG;IACpC,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,aAAa,CAAC;CACjB,CAAC;AAEF,MAAM,MAAM,SAAS,GAAG,WAAW,GAAG,sBAAsB,GAAG,OAAO,GAAG,OAAO,GAAG,MAAM,CAAC;AAE1F,MAAM,MAAM,iBAAiB,CAAC,CAAC,SAAS,SAAS,OAAO,EAAE,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;AAEzF,MAAM,MAAM,kBAAkB,CAAC,CAAC,SAAS,aAAa,CAAC,OAAO,CAAC,IAAI;KACjE,CAAC,IAAI,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACjC,CAAC;AAEF,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,GAC3F,CAAC,GACD,KAAK,CAAC;AAET,MAAM,MAAM,UAAU,CAAC,CAAC,EAAE,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,MAAM,IAAI;IACjE,CAAC,EAAE,CAAC,CAAC;IACL,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;CACjC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAEjC,MAAM,MAAM,cAAc,CAAC,CAAC,EAAE,CAAC,SAAS,MAAM,IAAI,UAAU,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC"}