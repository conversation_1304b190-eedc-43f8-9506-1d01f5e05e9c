'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import PointsManager from '@/components/PointsManager'
import DigitalAssetMarket from '@/components/DigitalAssetMarket'
import { UserPoints } from '@/services/creatorEconomyService'

// 创作者经济引擎首页组件
export default function CreatorEconomyHomepage() {
  const router = useRouter()
  
  // 状态管理
  const [currentIndex, setCurrentIndex] = useState(0)
  const [userVerified, setUserVerified] = useState(false)
  const [userId, setUserId] = useState('user-001')

  const [showPointsModal, setShowPointsModal] = useState(false)
  const [showAssetModal, setShowAssetModal] = useState(false)
  const [showTipModal, setShowTipModal] = useState(false)
  const [showMenuModal, setShowMenuModal] = useState(false)
  const [showSearchModal, setShowSearchModal] = useState(false)
  const [activeTab, setActiveTab] = useState<'follow' | 'recommend' | 'nearby'>('recommend')
  const [searchQuery, setSearchQuery] = useState('')
  const [showFollowModal, setShowFollowModal] = useState(false)
  const [showCommentModal, setShowCommentModal] = useState(false)
  const [showShareModal, setShowShareModal] = useState(false)
  const [isLiked, setIsLiked] = useState(false)
  const [isSaved, setIsSaved] = useState(false)
  const [isFollowing, setIsFollowing] = useState(false)
  const [newComment, setNewComment] = useState('')
  const commentInputRef = useRef<HTMLInputElement>(null)
  const [showCreatorModal, setShowCreatorModal] = useState(false)
  const [showEconomyModal, setShowEconomyModal] = useState(false)
  const [showLevelModal, setShowLevelModal] = useState(false)
  const [userPoints, setUserPoints] = useState<UserPoints>({
    NGT: 1000,
    CRT: 500,
    SKL: 300,
    FAN: 800,
    DID: 95
  })
  const [barrageMessages, setBarrageMessages] = useState([
    { id: '1', text: '这个设计太棒了！', user: '建筑师小王', time: Date.now() },
    { id: '2', text: 'AI技术真的改变了建筑行业', user: '设计爱好者', time: Date.now() - 1000 },
    { id: '3', text: '想学习这个技术', user: '学生小李', time: Date.now() - 2000 }
  ])

  // 创作者经济内容数据
  const contents = [
    {
      id: 'content-1',
      title: 'AI驱动的智慧城市建设',
      description: '🏗️ 使用AI驱动的建筑设计，创造未来智慧城市综合体。集成VR漫游、智能管理和IoT控制系统。',
      creator: {
        name: 'AI建筑大师',
        avatar: '🏗️',
        verified: true,
        level: 'Legend',
        followers: 1250000,
        revenue: 156800
      },
      media: {
        url: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=800&fit=crop',
        type: 'video'
      },
      engagement: {
        views: 2340000,
        likes: 189500,
        comments: 24800,
        shares: 18900,
        saves: 45600
      },
      digitalAsset: {
        isDigitalAsset: true,
        price: 5000,
        currency: 'CNY',
        category: '设计方案',
        copyrightProtected: true
      },
      economy: {
        totalRevenue: 156800,
        monthlyIncome: 52000,
        fanCount: 1250000,
        tier: 'Platinum'
      },
      ai: {
        score: 9.2,
        reason: '基于您对建筑设计的兴趣推荐',
        trending: true
      },
      tags: ['AI建筑', '智慧城市', '未来设计']
    },
    {
      id: 'content-2', 
      title: '元宇宙社交空间设计',
      description: '🚀 基于WebXR的沉浸式社交元宇宙，支持虚拟身份、空间音频、AI社交助手。',
      creator: {
        name: 'VR创作者',
        avatar: '🚀',
        verified: true,
        level: 'Elite',
        followers: 890000,
        revenue: 89600
      },
      media: {
        url: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=800&fit=crop',
        type: 'video'
      },
      engagement: {
        views: 1560000,
        likes: 125000,
        comments: 18900,
        shares: 12400,
        saves: 28900
      },
      digitalAsset: {
        isDigitalAsset: true,
        price: 3000,
        currency: 'CNY',
        category: '3D模型',
        copyrightProtected: true
      },
      economy: {
        totalRevenue: 89600,
        monthlyIncome: 28000,
        fanCount: 890000,
        tier: 'Gold'
      },
      ai: {
        score: 8.8,
        reason: '基于您对元宇宙技术的关注推荐',
        trending: true
      },
      tags: ['元宇宙', 'WebXR', 'AI社交']
    }
  ]

  // 关注内容数据
  const followContents = [
    {
      ...contents[0],
      creator: { ...contents[0].creator, name: '关注的建筑师', followers: 50000 }
    }
  ]

  // 附近内容数据
  const nearbyContents = [
    {
      ...contents[1],
      creator: { ...contents[1].creator, name: '附近的设计师', followers: 25000 },
      location: '距离您 2.5km'
    }
  ]

  // 根据当前标签页获取内容
  const getCurrentContents = () => {
    switch (activeTab) {
      case 'follow': return followContents
      case 'nearby': return nearbyContents
      default: return contents
    }
  }

  const currentContents = getCurrentContents()
  const currentContent = currentContents[currentIndex] || contents[0]

  // 实名认证
  const handleVerification = useCallback(async () => {
    try {
      // 模拟实名认证流程
      const confirmed = confirm('是否进行实名认证？\n实名认证后可享受更多平台服务')
      if (confirmed) {
        // 这里应该跳转到实名认证页面
        alert('正在跳转到实名认证页面...')
        setUserVerified(true)
      }
    } catch (error) {
      console.error('实名认证失败:', error)
      alert('实名认证失败，请稍后重试')
    }
  }, [])

  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M'
    if (num >= 1000) return (num / 1000).toFixed(1) + 'k'
    return num.toString()
  }

  // 格式化价格
  const formatPrice = (price: number) => {
    return `¥${price.toLocaleString()}`
  }

  // 处理积分更新
  const handlePointsUpdate = useCallback((newPoints: UserPoints) => {
    setUserPoints(newPoints)
  }, [])

  // 处理数字资产购买
  const handleAssetPurchase = useCallback((asset: any) => {
    console.log('购买数字资产:', asset)
    // 这里可以添加购买成功后的处理逻辑
  }, [])

  // 处理交互
  const handleLike = () => {
    setIsLiked(!isLiked)
    // 模拟点赞数变化
    console.log(isLiked ? '取消点赞' : '点赞成功')
  }

  const handleComment = () => {
    setShowCommentModal(true)
    // 延迟聚焦，等待弹窗动画完成
    setTimeout(() => {
      commentInputRef.current?.focus()
    }, 300)
  }

  const handleShare = () => setShowShareModal(true)

  const handleSave = () => {
    setIsSaved(!isSaved)
    console.log(isSaved ? '取消收藏' : '收藏成功')
  }

  const handleFollow = () => {
    setIsFollowing(!isFollowing)
    console.log(isFollowing ? '取消关注' : '关注成功')
  }

  const handleNFTPurchase = () => setShowAssetModal(true)
  const handleTip = () => setShowTipModal(true)

  // 滑动切换
  const handleSwipe = (direction: 'up' | 'down') => {
    const maxIndex = currentContents.length - 1
    if (direction === 'up' && currentIndex < maxIndex) {
      setCurrentIndex(currentIndex + 1)
    } else if (direction === 'down' && currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }

  // 标签页切换时重置索引
  const handleTabChange = (tab: 'follow' | 'recommend' | 'nearby') => {
    setActiveTab(tab)
    setCurrentIndex(0)
  }



  return (
    <div className="min-h-screen bg-black text-white overflow-hidden relative">
      {/* 背景视频/图片 */}
      <div className="absolute inset-0">
        <Image
          src={currentContent.media.url}
          alt={currentContent.title}
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/40"></div>
      </div>

      {/* 主要内容区域 */}
      <div className="relative z-10 h-screen flex flex-col">
        {/* 顶部导航栏 - 创作者经济增强版 */}
        <div className="absolute top-0 left-0 right-0 z-20 pt-12 pb-4 px-4">
          <div className="flex items-center justify-between">
            {/* 左侧：直播状态 + 认证状态 */}
            <div className="flex items-center space-x-2">
              <div className="px-2 py-1 bg-red-500/80 rounded-full flex items-center space-x-1">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                <span className="text-white text-xs font-medium">LIVE</span>
              </div>

              {userVerified && (
                <div className="px-2 py-1 bg-green-500/80 rounded-full">
                  <span className="text-white text-xs">✓ 已认证</span>
                </div>
              )}
            </div>

            {/* 中间：Tab切换 + AI推荐指示器 */}
            <div className="flex items-center space-x-4">
              <button
                onClick={() => handleTabChange('follow')}
                className={`text-sm transition-colors ${
                  activeTab === 'follow'
                    ? 'text-white font-bold border-b-2 border-white pb-1'
                    : 'text-white/60 hover:text-white/80'
                }`}
              >
                关注
              </button>
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => handleTabChange('recommend')}
                  className={`text-sm transition-colors ${
                    activeTab === 'recommend'
                      ? 'text-white font-bold border-b-2 border-white pb-1'
                      : 'text-white/60 hover:text-white/80'
                  }`}
                >
                  推荐
                </button>
                {activeTab === 'recommend' && (
                  <div className="w-2 h-2 bg-gradient-to-r from-cyan-400 to-purple-500 rounded-full animate-pulse"></div>
                )}
              </div>
              <button
                onClick={() => handleTabChange('nearby')}
                className={`text-sm transition-colors ${
                  activeTab === 'nearby'
                    ? 'text-white font-bold border-b-2 border-white pb-1'
                    : 'text-white/60 hover:text-white/80'
                }`}
              >
                附近
              </button>
            </div>

            {/* 右侧：搜索 + 菜单 */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowSearchModal(true)}
                className="w-8 h-8 flex items-center justify-center hover:bg-white/10 rounded-full transition-colors"
              >
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>

              <button
                onClick={() => setShowMenuModal(true)}
                className="w-8 h-8 flex items-center justify-center"
              >
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                </svg>
              </button>
            </div>
          </div>
        </div>





        {/* 实时弹幕 */}
        <div className="absolute top-1/3 left-4 right-20 z-15">
          <AnimatePresence>
            {barrageMessages.slice(-3).map((msg, index) => (
              <motion.div
                key={msg.id}
                initial={{ opacity: 0, x: 100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -100 }}
                transition={{ delay: index * 0.5 }}
                className="mb-2 bg-black/40 backdrop-blur-sm rounded-full px-3 py-1 max-w-64"
              >
                <span className="text-xs text-cyan-400">{msg.user}: </span>
                <span className="text-xs text-white">{msg.text}</span>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* 底部内容信息区域 */}
        <div className="absolute bottom-0 left-0 right-0 z-20">
          <div className="flex items-end justify-between p-4 pb-24">
            {/* 左侧：内容信息 + 创作者经济 */}
            <div className="flex-1 pr-4 max-w-[70%]">
              {/* 创作者信息 + 经济数据 */}
              <div className="flex items-center space-x-3 mb-3">
                <button
                  onClick={() => setShowCreatorModal(true)}
                  className="flex items-center space-x-2 hover:bg-white/10 rounded-lg px-2 py-1 transition-colors"
                >
                  <span className="text-white font-semibold text-base">@{currentContent.creator.name}</span>
                  {currentContent.creator.verified && (
                    <svg className="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  )}
                </button>
                <button
                  onClick={() => setShowLevelModal(true)}
                  className="px-2 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded text-xs font-bold text-black hover:scale-105 transition-transform"
                >
                  {currentContent.creator.level}
                </button>
              </div>

              {/* 标签页指示器 */}
              <div className="flex items-center space-x-2 mb-2">
                <div className={`px-2 py-1 rounded-full text-xs ${
                  activeTab === 'follow' ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30' :
                  activeTab === 'nearby' ? 'bg-green-500/20 text-green-400 border border-green-500/30' :
                  'bg-purple-500/20 text-purple-400 border border-purple-500/30'
                }`}>
                  {activeTab === 'follow' ? '👥 关注' :
                   activeTab === 'nearby' ? '📍 附近' :
                   '🔥 推荐'}
                </div>
                {(currentContent as any).location && (
                  <div className="px-2 py-1 bg-green-500/20 text-green-400 border border-green-500/30 rounded-full text-xs">
                    📍 {(currentContent as any).location}
                  </div>
                )}
              </div>

              {/* 创作者收益展示 */}
              <div className="flex items-center space-x-4 mb-3">
                <button
                  onClick={() => setShowEconomyModal(true)}
                  className="flex items-center space-x-1 hover:bg-green-500/10 rounded-lg px-2 py-1 transition-colors"
                >
                  <span className="text-xs text-green-400">💰</span>
                  <span className="text-xs text-white">{formatNumber(currentContent.economy.monthlyIncome)}/月</span>
                </button>
                <button
                  onClick={() => setShowCreatorModal(true)}
                  className="flex items-center space-x-1 hover:bg-purple-500/10 rounded-lg px-2 py-1 transition-colors"
                >
                  <span className="text-xs text-purple-400">👥</span>
                  <span className="text-xs text-white">{formatNumber(currentContent.creator.followers)}</span>
                </button>
                <button
                  onClick={() => setShowEconomyModal(true)}
                  className="px-2 py-1 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded border border-purple-500/30 hover:bg-purple-500/30 transition-colors"
                >
                  <span className="text-xs text-purple-300">{currentContent.economy.tier}</span>
                </button>
              </div>

              {/* 内容描述 */}
              <div className="mb-3">
                <p className="text-white text-sm leading-relaxed line-clamp-3">
                  {currentContent.description}
                </p>
              </div>

              {/* 话题标签 */}
              <div className="flex flex-wrap gap-2 mb-3">
                {currentContent.tags.map((tag, index) => (
                  <span key={index} className="text-white text-sm font-medium">
                    #{tag}
                  </span>
                ))}
              </div>

              {/* 创作者经济操作 */}

            </div>

            {/* 右侧：交互按钮 + Web3功能 */}
            <div className="flex flex-col items-center space-y-4">
              {/* 创作者头像 + 关注按钮 */}
              <div className="flex flex-col items-center">
                <div className="relative">
                  <button
                    onClick={handleFollow}
                    className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-lg border-2 border-white hover:scale-105 transition-transform"
                  >
                    {currentContent.creator.avatar}
                  </button>
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">
                      {currentContent.creator.level === 'Legend' ? '👑' :
                       currentContent.creator.level === 'Elite' ? '⭐' :
                       currentContent.creator.level === 'Popular' ? '🔥' : '🌟'}
                    </span>
                  </div>
                  {/* 关注状态指示器 */}
                  <motion.button
                    onClick={handleFollow}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className={`absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full flex items-center justify-center border-2 border-white transition-colors ${
                      isFollowing
                        ? 'bg-green-500 text-white'
                        : 'bg-red-500 text-white'
                    }`}
                  >
                    <span className="text-sm font-bold">
                      {isFollowing ? '✓' : '+'}
                    </span>
                  </motion.button>
                </div>
              </div>

              {/* 统一交互按钮设计 */}
              {/* 点赞按钮 */}
              <motion.button
                onClick={handleLike}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="flex flex-col items-center"
              >
                <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                  👍
                </div>
                <span className={`text-xs mt-1 transition-colors ${
                  isLiked ? 'text-red-400' : 'text-white'
                }`}>
                  {formatNumber(currentContent.engagement.likes + (isLiked ? 1 : 0))}
                </span>
              </motion.button>

              {/* 分享按钮 */}
              <button onClick={handleShare} className="flex flex-col items-center">
                <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                  🔄
                </div>
                <span className="text-xs mt-1 text-white">{formatNumber(currentContent.engagement.shares)}</span>
              </button>

              {/* 收藏按钮 */}
              <motion.button
                onClick={handleSave}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="flex flex-col items-center"
              >
                <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                  ❤️
                </div>
                <span className={`text-xs mt-1 transition-colors ${
                  isSaved ? 'text-yellow-400' : 'text-white'
                }`}>
                  {formatNumber(currentContent.engagement.saves + (isSaved ? 1 : 0))}
                </span>
              </motion.button>

              {/* 评论按钮 */}
              <button onClick={handleComment} className="flex flex-col items-center">
                <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                  💬
                </div>
                <span className="text-xs mt-1 text-white">{formatNumber(currentContent.engagement.comments)}</span>
              </button>

              {/* 数字资产购买按钮 */}
              {currentContent.digitalAsset.isDigitalAsset && (
                <button onClick={() => setShowAssetModal(true)} className="flex flex-col items-center">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center">
                    <span className="text-lg">💎</span>
                  </div>
                  <span className="text-xs mt-1">资产</span>
                </button>
              )}

              {/* 积分按钮 */}
              <button onClick={() => setShowPointsModal(true)} className="flex flex-col items-center">
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                  <span className="text-lg">🌟</span>
                </div>
                <span className="text-xs mt-1">积分</span>
              </button>


            </div>
          </div>
        </div>

        {/* 触摸手势处理 */}
        <div
          className="absolute inset-0 z-10"
          onTouchStart={(e) => {
            const touch = e.touches[0]
            const startY = touch.clientY

            const handleTouchMove = (e: TouchEvent) => {
              const touch = e.touches[0]
              const deltaY = touch.clientY - startY

              if (Math.abs(deltaY) > 50) {
                if (deltaY > 0) {
                  handleSwipe('down')
                } else {
                  handleSwipe('up')
                }
                document.removeEventListener('touchmove', handleTouchMove)
              }
            }

            document.addEventListener('touchmove', handleTouchMove)
            document.addEventListener('touchend', () => {
              document.removeEventListener('touchmove', handleTouchMove)
            }, { once: true })
          }}
        />
      </div>

      {/* 积分管理弹窗 */}
      <AnimatePresence>
        {showPointsModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4"
            onClick={() => setShowPointsModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-900 to-black rounded-3xl max-w-md w-full border border-blue-500/30 max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <div className="text-center mb-6">
                  <div className="text-4xl mb-4">🌟</div>
                  <h3 className="text-xl font-bold text-white mb-2">积分管理</h3>
                  <p className="text-gray-400 text-sm">管理您的平台积分</p>
                </div>

                <PointsManager
                  userId={userId}
                  onPointsUpdate={handlePointsUpdate}
                />

                <div className="mt-6">
                  <button
                    onClick={() => setShowPointsModal(false)}
                    className="w-full py-3 bg-gray-700 rounded-xl text-white font-medium"
                  >
                    关闭
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 数字资产市场弹窗 */}
      <AnimatePresence>
        {showAssetModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4"
            onClick={() => setShowAssetModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-900 to-black rounded-3xl max-w-md w-full border border-green-500/30 max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <div className="text-center mb-6">
                  <div className="text-4xl mb-4">💎</div>
                  <h3 className="text-xl font-bold text-white mb-2">数字资产市场</h3>
                  <p className="text-gray-400 text-sm">浏览和购买数字资产</p>
                </div>

                <DigitalAssetMarket onPurchase={handleAssetPurchase} />

                <div className="mt-6">
                  <button
                    onClick={() => setShowAssetModal(false)}
                    className="w-full py-3 bg-gray-700 rounded-xl text-white font-medium"
                  >
                    关闭
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 打赏弹窗 */}
      <AnimatePresence>
        {showTipModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4"
            onClick={() => setShowTipModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-yellow-500/30"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center">
                <div className="text-4xl mb-4">💰</div>
                <h3 className="text-xl font-bold text-white mb-2">积分打赏</h3>
                <p className="text-gray-400 text-sm mb-4">支持 @{currentContent.creator.name}</p>

                <div className="grid grid-cols-3 gap-3 mb-4">
                  {[100, 500, 1000].map((amount) => (
                    <button
                      key={amount}
                      className="py-3 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-xl text-white font-medium hover:bg-yellow-500/30 transition-colors"
                    >
                      {amount} FAN
                    </button>
                  ))}
                </div>

                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-4">
                  <div className="text-blue-200 text-xs">
                    <p className="font-medium mb-1">打赏说明：</p>
                    <p>• 使用FAN积分进行打赏</p>
                    <p>• 积分为平台内虚拟积分，不具有货币属性</p>
                    <p>• 打赏将增加创作者的粉丝积分</p>
                  </div>
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowTipModal(false)}
                    className="flex-1 py-3 bg-gray-700 rounded-xl text-white font-medium"
                  >
                    取消
                  </button>
                  <button
                    onClick={() => {
                      console.log('发送积分打赏')
                      alert('积分打赏成功！感谢您对创作者的支持！')
                      setShowTipModal(false)
                    }}
                    className="flex-1 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl text-black font-medium"
                  >
                    确认打赏
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 三点菜单弹窗 */}
      <AnimatePresence>
        {showMenuModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4"
            onClick={() => setShowMenuModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-white/20"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center mb-6">
                <div className="text-4xl mb-4">⚙️</div>
                <h3 className="text-xl font-bold text-white mb-2">菜单</h3>
                <p className="text-gray-400 text-sm">更多功能和设置</p>
              </div>

              <div className="space-y-3">
                {/* 创作功能 */}
                <button
                  onClick={() => {
                    setShowMenuModal(false)
                    router.push('/creator')
                  }}
                  className="w-full flex items-center space-x-3 p-4 bg-gradient-to-r from-pink-500/20 to-purple-500/20 border border-pink-500/30 rounded-xl hover:bg-pink-500/30 transition-colors"
                >
                  <div className="text-2xl">✨</div>
                  <div className="text-left">
                    <div className="text-white font-medium">创作中心</div>
                    <div className="text-gray-400 text-xs">发布内容和管理作品</div>
                  </div>
                </button>

                {/* 积分打赏 */}
                <button
                  onClick={() => {
                    setShowMenuModal(false)
                    setShowTipModal(true)
                  }}
                  className="w-full flex items-center space-x-3 p-4 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-xl hover:bg-yellow-500/30 transition-colors"
                >
                  <div className="text-2xl">💰</div>
                  <div className="text-left">
                    <div className="text-white font-medium">积分打赏</div>
                    <div className="text-gray-400 text-xs">支持喜欢的创作者</div>
                  </div>
                </button>

                {/* 数字资产 */}
                <button
                  onClick={() => {
                    setShowMenuModal(false)
                    setShowAssetModal(true)
                  }}
                  className="w-full flex items-center space-x-3 p-4 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-xl hover:bg-blue-500/30 transition-colors"
                >
                  <div className="text-2xl">💎</div>
                  <div className="text-left">
                    <div className="text-white font-medium">数字资产</div>
                    <div className="text-gray-400 text-xs">浏览和购买数字作品</div>
                  </div>
                </button>

                {/* 实名认证 */}
                {!userVerified && (
                  <button
                    onClick={() => {
                      setShowMenuModal(false)
                      router.push('/verification')
                    }}
                    className="w-full flex items-center space-x-3 p-4 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-xl hover:bg-green-500/30 transition-colors"
                  >
                    <div className="text-2xl">✓</div>
                    <div className="text-left">
                      <div className="text-white font-medium">实名认证</div>
                      <div className="text-gray-400 text-xs">完成身份验证</div>
                    </div>
                  </button>
                )}

                {/* 合规声明 */}
                <button
                  onClick={() => {
                    setShowMenuModal(false)
                    router.push('/legal')
                  }}
                  className="w-full flex items-center space-x-3 p-4 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-xl hover:bg-yellow-500/30 transition-colors"
                >
                  <div className="text-2xl">⚖️</div>
                  <div className="text-left">
                    <div className="text-white font-medium">合规声明</div>
                    <div className="text-gray-400 text-xs">法律条款和隐私政策</div>
                  </div>
                </button>

                {/* 设置 */}
                <button
                  onClick={() => {
                    setShowMenuModal(false)
                    router.push('/profile')
                  }}
                  className="w-full flex items-center space-x-3 p-4 bg-white/10 border border-white/20 rounded-xl hover:bg-white/20 transition-colors"
                >
                  <div className="text-2xl">⚙️</div>
                  <div className="text-left">
                    <div className="text-white font-medium">个人中心</div>
                    <div className="text-gray-400 text-xs">账户设置和管理</div>
                  </div>
                </button>
              </div>

              <div className="mt-6">
                <button
                  onClick={() => setShowMenuModal(false)}
                  className="w-full py-3 bg-gray-700 rounded-xl text-white font-medium"
                >
                  关闭
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 搜索弹窗 */}
      <AnimatePresence>
        {showSearchModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/90 backdrop-blur-lg"
          >
            <div className="flex flex-col h-full">
              {/* 搜索头部 */}
              <div className="flex items-center p-4 border-b border-white/10">
                <button
                  onClick={() => setShowSearchModal(false)}
                  className="w-8 h-8 flex items-center justify-center mr-3"
                >
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <div className="flex-1 relative">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="搜索创作者、内容、技能..."
                    className="w-full bg-white/10 border border-white/20 rounded-full px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                    autoFocus
                  />
                  <svg className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>

              {/* 搜索内容 */}
              <div className="flex-1 overflow-y-auto">
                {searchQuery ? (
                  /* 搜索结果 */
                  <div className="p-4">
                    <h3 className="text-white font-bold mb-4">搜索结果</h3>
                    <div className="space-y-4">
                      {/* 创作者结果 */}
                      <div>
                        <h4 className="text-gray-400 text-sm mb-2">创作者</h4>
                        <div className="space-y-3">
                          {contents.filter(c => c.creator.name.includes(searchQuery) || c.title.includes(searchQuery)).map((content, index) => (
                            <div key={index} className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg">
                              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                                <span className="text-lg">{content.creator.avatar}</span>
                              </div>
                              <div className="flex-1">
                                <div className="text-white font-medium">{content.creator.name}</div>
                                <div className="text-gray-400 text-sm">{formatNumber(content.creator.followers)} 关注者</div>
                              </div>
                              <button className="px-3 py-1 bg-blue-500 rounded-full text-white text-sm">
                                关注
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* 内容结果 */}
                      <div>
                        <h4 className="text-gray-400 text-sm mb-2">相关内容</h4>
                        <div className="grid grid-cols-2 gap-3">
                          {contents.filter(c => c.tags.some(tag => tag.includes(searchQuery)) || c.description.includes(searchQuery)).map((content, index) => (
                            <div key={index} className="bg-white/5 rounded-lg overflow-hidden">
                              <div className="aspect-video bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center">
                                <span className="text-2xl">{content.creator.avatar}</span>
                              </div>
                              <div className="p-3">
                                <div className="text-white text-sm font-medium line-clamp-2">{content.title}</div>
                                <div className="text-gray-400 text-xs mt-1">{formatNumber(content.engagement.views)} 观看</div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  /* 搜索建议 */
                  <div className="p-4">
                    <h3 className="text-white font-bold mb-4">热门搜索</h3>
                    <div className="flex flex-wrap gap-2 mb-6">
                      {['AI建筑', '智慧城市', 'BIM设计', '元宇宙', '绿色建筑', 'VR漫游'].map((tag, index) => (
                        <button
                          key={index}
                          onClick={() => setSearchQuery(tag)}
                          className="px-3 py-1 bg-white/10 rounded-full text-white text-sm hover:bg-white/20 transition-colors"
                        >
                          {tag}
                        </button>
                      ))}
                    </div>

                    <h3 className="text-white font-bold mb-4">推荐创作者</h3>
                    <div className="space-y-3">
                      {contents.map((content, index) => (
                        <div key={index} className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg">
                          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                            <span className="text-lg">{content.creator.avatar}</span>
                          </div>
                          <div className="flex-1">
                            <div className="text-white font-medium">{content.creator.name}</div>
                            <div className="text-gray-400 text-sm">{formatNumber(content.creator.followers)} 关注者</div>
                          </div>
                          <button className="px-3 py-1 bg-blue-500 rounded-full text-white text-sm">
                            关注
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 评论弹窗 */}
      <AnimatePresence>
        {showCommentModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-[9999] bg-black/80 backdrop-blur-lg"
            onClick={() => setShowCommentModal(false)}
          >
            {/* 弹窗容器 - 确保不被底部导航遮挡 */}
            <div className="absolute inset-0 flex items-end pb-20">
              <motion.div
                initial={{ y: '100%' }}
                animate={{ y: 0 }}
                exit={{ y: '100%' }}
                className="w-full bg-gradient-to-t from-gray-900 to-gray-800 rounded-t-3xl flex flex-col max-h-[75vh]"
                onClick={(e) => e.stopPropagation()}
              >
              {/* 头部 - 固定 */}
              <div className="flex items-center justify-between p-6 pb-4 border-b border-white/10">
                <h3 className="text-xl font-bold text-white">评论 {formatNumber(currentContent.engagement.comments)}</h3>
                <button
                  onClick={() => setShowCommentModal(false)}
                  className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center"
                >
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* 评论列表 - 可滚动 */}
              <div className="flex-1 overflow-y-auto px-6 py-4">
                <div className="space-y-4">
                  {[
                    { user: '建筑师小王', avatar: '🏗️', comment: '这个设计理念太前卫了！AI技术在建筑领域的应用真的让人惊叹', time: '2分钟前', likes: 12 },
                    { user: '设计爱好者', avatar: '🎨', comment: '请问用的是什么软件建模的？效果太棒了', time: '5分钟前', likes: 8 },
                    { user: '学生小李', avatar: '📚', comment: '作为建筑专业的学生，这给了我很多启发', time: '10分钟前', likes: 15 },
                    { user: 'VR专家', avatar: '🥽', comment: 'VR漫游的体验一定很震撼，期待实际应用', time: '15分钟前', likes: 6 }
                  ].map((comment, index) => (
                    <div key={index} className="flex space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                        <span className="text-sm">{comment.avatar}</span>
                      </div>
                      <div className="flex-1">
                        <div className="bg-white/10 rounded-lg p-3">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="text-white font-medium text-sm">{comment.user}</span>
                            <span className="text-gray-400 text-xs">{comment.time}</span>
                          </div>
                          <p className="text-gray-300 text-sm leading-relaxed">{comment.comment}</p>
                          <div className="flex items-center space-x-4 mt-2">
                            <button className="flex items-center space-x-1 text-gray-400 hover:text-red-400 transition-colors">
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                              </svg>
                              <span className="text-xs">{comment.likes}</span>
                            </button>
                            <button className="text-gray-400 hover:text-blue-400 transition-colors text-xs">
                              回复
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 评论输入 - 固定在底部，确保可见 */}
              <div className="mt-auto border-t border-white/10 bg-gray-900 p-4">
                <div className="flex space-x-3 items-end">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-sm">👤</span>
                  </div>
                  <div className="flex-1">
                    <div className="flex space-x-2">
                      <input
                        ref={commentInputRef}
                        type="text"
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        placeholder="写下你的评论..."
                        className="flex-1 bg-white/10 border border-white/20 rounded-full px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:bg-white/15 transition-colors text-sm"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && newComment.trim()) {
                            console.log('发送评论:', newComment)
                            setNewComment('')
                            alert('评论发送成功！')
                          }
                        }}
                      />
                      <button
                        onClick={() => {
                          if (newComment.trim()) {
                            console.log('发送评论:', newComment)
                            setNewComment('')
                            alert('评论发送成功！')
                          }
                        }}
                        disabled={!newComment.trim()}
                        className={`px-4 py-3 rounded-full font-medium transition-colors flex-shrink-0 text-sm ${
                          newComment.trim()
                            ? 'bg-blue-500 text-white hover:bg-blue-600'
                            : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        }`}
                      >
                        发送
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 分享弹窗 */}
      <AnimatePresence>
        {showShareModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4"
            onClick={() => setShowShareModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-white/20"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center mb-6">
                <div className="text-4xl mb-4">📤</div>
                <h3 className="text-xl font-bold text-white mb-2">分享内容</h3>
                <p className="text-gray-400 text-sm">分享这个精彩内容</p>
              </div>

              <div className="grid grid-cols-3 gap-4 mb-6">
                {[
                  { name: '微信', icon: '💚', color: 'from-green-500 to-emerald-500' },
                  { name: '朋友圈', icon: '🌟', color: 'from-blue-500 to-cyan-500' },
                  { name: 'QQ', icon: '🐧', color: 'from-blue-600 to-blue-700' },
                  { name: '微博', icon: '📱', color: 'from-red-500 to-pink-500' },
                  { name: '复制链接', icon: '🔗', color: 'from-gray-500 to-gray-600' },
                  { name: '更多', icon: '⋯', color: 'from-purple-500 to-indigo-500' }
                ].map((platform, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      console.log(`分享到${platform.name}`)
                      alert(`已分享到${platform.name}`)
                      setShowShareModal(false)
                    }}
                    className={`flex flex-col items-center p-4 bg-gradient-to-r ${platform.color} rounded-xl hover:scale-105 transition-transform`}
                  >
                    <div className="text-2xl mb-2">{platform.icon}</div>
                    <span className="text-white text-xs font-medium">{platform.name}</span>
                  </button>
                ))}
              </div>

              <button
                onClick={() => setShowShareModal(false)}
                className="w-full py-3 bg-gray-700 rounded-xl text-white font-medium"
              >
                取消
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 创作者详情弹窗 */}
      <AnimatePresence>
        {showCreatorModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4"
            onClick={() => setShowCreatorModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20 max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              {/* 创作者头部信息 */}
              <div className="text-center mb-6">
                <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-3xl mx-auto mb-4 border-4 border-white/20">
                  {currentContent.creator.avatar}
                </div>
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <h3 className="text-xl font-bold text-white">@{currentContent.creator.name}</h3>
                  {currentContent.creator.verified && (
                    <svg className="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  )}
                </div>
                <div className="flex items-center justify-center space-x-2 mb-4">
                  <div className="px-3 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full text-sm font-bold text-black">
                    {currentContent.creator.level}
                  </div>
                  <div className="px-3 py-1 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full border border-purple-500/30">
                    <span className="text-sm text-purple-300">{currentContent.economy.tier}</span>
                  </div>
                </div>
              </div>

              {/* 统计数据 */}
              <div className="grid grid-cols-3 gap-4 mb-6">
                <div className="text-center p-3 bg-white/5 rounded-lg">
                  <div className="text-2xl font-bold text-white">{formatNumber(currentContent.creator.followers)}</div>
                  <div className="text-xs text-gray-400">关注者</div>
                </div>
                <div className="text-center p-3 bg-white/5 rounded-lg">
                  <div className="text-2xl font-bold text-green-400">{formatNumber(currentContent.economy.monthlyIncome)}</div>
                  <div className="text-xs text-gray-400">月收入</div>
                </div>
                <div className="text-center p-3 bg-white/5 rounded-lg">
                  <div className="text-2xl font-bold text-purple-400">{formatNumber(currentContent.engagement.views)}</div>
                  <div className="text-xs text-gray-400">总观看</div>
                </div>
              </div>

              {/* 创作者介绍 */}
              <div className="mb-6">
                <h4 className="text-white font-bold mb-3">创作者介绍</h4>
                <div className="bg-white/5 rounded-lg p-4">
                  <p className="text-gray-300 text-sm leading-relaxed mb-3">
                    专注于AI驱动的建筑设计和智慧城市规划，拥有10年建筑行业经验。
                    擅长将前沿科技与传统建筑美学相结合，创造出既实用又美观的建筑作品。
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {['AI建筑', '智慧城市', 'BIM设计', 'VR漫游', '绿色建筑'].map((skill, index) => (
                      <span key={index} className="px-2 py-1 bg-blue-500/20 text-blue-400 rounded-full text-xs">
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {/* 成就徽章 */}
              <div className="mb-6">
                <h4 className="text-white font-bold mb-3">成就徽章</h4>
                <div className="grid grid-cols-4 gap-3">
                  {[
                    { icon: '🏆', name: '金牌创作者', color: 'from-yellow-500 to-orange-500' },
                    { icon: '🔥', name: '热门内容', color: 'from-red-500 to-pink-500' },
                    { icon: '💎', name: 'NFT先锋', color: 'from-blue-500 to-purple-500' },
                    { icon: '🌟', name: '粉丝之星', color: 'from-purple-500 to-indigo-500' }
                  ].map((badge, index) => (
                    <div key={index} className={`text-center p-3 bg-gradient-to-r ${badge.color} rounded-lg`}>
                      <div className="text-lg mb-1">{badge.icon}</div>
                      <div className="text-xs text-white font-medium">{badge.name}</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex space-x-3 mb-4">
                <button
                  onClick={() => {
                    handleFollow()
                    setShowCreatorModal(false)
                  }}
                  className={`flex-1 py-3 rounded-xl font-medium transition-colors ${
                    isFollowing
                      ? 'bg-gray-600 text-white'
                      : 'bg-blue-500 text-white hover:bg-blue-600'
                  }`}
                >
                  {isFollowing ? '已关注' : '关注'}
                </button>
                <button
                  onClick={() => {
                    setShowCreatorModal(false)
                    setShowTipModal(true)
                  }}
                  className="flex-1 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl text-black font-medium hover:scale-105 transition-transform"
                >
                  打赏
                </button>
              </div>

              <button
                onClick={() => setShowCreatorModal(false)}
                className="w-full py-3 bg-gray-700 rounded-xl text-white font-medium"
              >
                关闭
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 经济数据弹窗 */}
      <AnimatePresence>
        {showEconomyModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4"
            onClick={() => setShowEconomyModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-green-500/30 max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center mb-6">
                <div className="text-4xl mb-4">💰</div>
                <h3 className="text-xl font-bold text-white mb-2">创作者经济数据</h3>
                <p className="text-gray-400 text-sm">@{currentContent.creator.name} 的收益统计</p>
              </div>

              {/* 收益概览 */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-green-400 mb-1">
                    ¥{formatNumber(currentContent.economy.monthlyIncome)}
                  </div>
                  <div className="text-xs text-gray-400">月收入</div>
                </div>
                <div className="bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border border-blue-500/30 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-blue-400 mb-1">
                    ¥{formatNumber(currentContent.economy.totalRevenue)}
                  </div>
                  <div className="text-xs text-gray-400">总收入</div>
                </div>
              </div>

              {/* 收入来源分析 */}
              <div className="mb-6">
                <h4 className="text-white font-bold mb-3">收入来源</h4>
                <div className="space-y-3">
                  {[
                    { source: '数字资产销售', amount: 28000, percentage: 54, color: 'bg-blue-500' },
                    { source: '粉丝打赏', amount: 15000, percentage: 29, color: 'bg-yellow-500' },
                    { source: '广告分成', amount: 6000, percentage: 12, color: 'bg-green-500' },
                    { source: '直播收益', amount: 3000, percentage: 5, color: 'bg-purple-500' }
                  ].map((item, index) => (
                    <div key={index} className="bg-white/5 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-white text-sm">{item.source}</span>
                        <span className="text-green-400 text-sm font-medium">¥{formatNumber(item.amount)}</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className={`${item.color} h-2 rounded-full transition-all duration-500`}
                          style={{ width: `${item.percentage}%` }}
                        ></div>
                      </div>
                      <div className="text-xs text-gray-400 mt-1">{item.percentage}%</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 等级权益 */}
              <div className="mb-6">
                <h4 className="text-white font-bold mb-3">等级权益</h4>
                <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <span className="px-2 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded text-xs font-bold text-black">
                      {currentContent.economy.tier}
                    </span>
                    <span className="text-purple-300 text-sm">等级权益</span>
                  </div>
                  <div className="space-y-2 text-sm text-gray-300">
                    <div className="flex items-center space-x-2">
                      <span className="text-green-400">✓</span>
                      <span>优先推荐位置</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-green-400">✓</span>
                      <span>专属客服支持</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-green-400">✓</span>
                      <span>高级数据分析</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-green-400">✓</span>
                      <span>NFT铸造优惠</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 趋势图表 */}
              <div className="mb-6">
                <h4 className="text-white font-bold mb-3">收益趋势</h4>
                <div className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-end space-x-2 h-20">
                    {[40, 65, 45, 80, 60, 90, 75].map((height, index) => (
                      <div key={index} className="flex-1 flex flex-col items-center">
                        <div
                          className="w-full bg-gradient-to-t from-green-500 to-emerald-400 rounded-t"
                          style={{ height: `${height}%` }}
                        ></div>
                        <span className="text-xs text-gray-400 mt-1">{index + 1}月</span>
                      </div>
                    ))}
                  </div>
                  <div className="text-center mt-3">
                    <span className="text-xs text-gray-400">近7个月收益趋势</span>
                  </div>
                </div>
              </div>

              <button
                onClick={() => setShowEconomyModal(false)}
                className="w-full py-3 bg-gray-700 rounded-xl text-white font-medium"
              >
                关闭
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 等级说明弹窗 */}
      <AnimatePresence>
        {showLevelModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4"
            onClick={() => setShowLevelModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-yellow-500/30 max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center mb-6">
                <div className="text-4xl mb-4">👑</div>
                <h3 className="text-xl font-bold text-white mb-2">创作者等级系统</h3>
                <p className="text-gray-400 text-sm">了解不同等级的权益和要求</p>
              </div>

              {/* 当前等级 */}
              <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-lg p-4 mb-6">
                <div className="text-center">
                  <div className="text-2xl mb-2">👑</div>
                  <div className="px-3 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full text-sm font-bold text-black inline-block mb-2">
                    {currentContent.creator.level}
                  </div>
                  <div className="text-white font-medium">当前等级</div>
                  <div className="text-gray-400 text-xs mt-1">顶级创作者，平台最高荣誉</div>
                </div>
              </div>

              {/* 等级列表 */}
              <div className="space-y-4 mb-6">
                {[
                  {
                    level: 'Legend',
                    icon: '👑',
                    name: '传奇',
                    requirement: '月收入 ≥ 50k',
                    benefits: ['专属推荐位', '优先客服', '高级分析', 'NFT优惠'],
                    color: 'from-yellow-400 to-orange-500',
                    current: currentContent.creator.level === 'Legend'
                  },
                  {
                    level: 'Elite',
                    icon: '⭐',
                    name: '精英',
                    requirement: '月收入 ≥ 20k',
                    benefits: ['推荐加权', '数据分析', '活动优先', '认证加速'],
                    color: 'from-purple-500 to-indigo-500',
                    current: currentContent.creator.level === 'Elite'
                  },
                  {
                    level: 'Popular',
                    icon: '🔥',
                    name: '热门',
                    requirement: '月收入 ≥ 5k',
                    benefits: ['流量扶持', '基础分析', '社区权限', '活动参与'],
                    color: 'from-red-500 to-pink-500',
                    current: currentContent.creator.level === 'Popular'
                  },
                  {
                    level: 'Rising',
                    icon: '🌟',
                    name: '新星',
                    requirement: '月收入 ≥ 1k',
                    benefits: ['新人扶持', '基础工具', '学习资源', '社区支持'],
                    color: 'from-blue-500 to-cyan-500',
                    current: currentContent.creator.level === 'Rising'
                  }
                ].map((levelInfo, index) => (
                  <div key={index} className={`rounded-lg p-4 border ${
                    levelInfo.current
                      ? 'bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border-yellow-500/50'
                      : 'bg-white/5 border-white/10'
                  }`}>
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="text-2xl">{levelInfo.icon}</div>
                      <div>
                        <div className={`px-2 py-1 bg-gradient-to-r ${levelInfo.color} rounded text-xs font-bold text-black inline-block`}>
                          {levelInfo.name}
                        </div>
                        <div className="text-gray-400 text-xs mt-1">{levelInfo.requirement}</div>
                      </div>
                      {levelInfo.current && (
                        <div className="ml-auto">
                          <span className="px-2 py-1 bg-green-500/20 text-green-400 rounded-full text-xs border border-green-500/30">
                            当前
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      {levelInfo.benefits.map((benefit, benefitIndex) => (
                        <div key={benefitIndex} className="flex items-center space-x-1">
                          <span className="text-green-400 text-xs">✓</span>
                          <span className="text-gray-300 text-xs">{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              {/* 升级提示 */}
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 mb-6">
                <div className="text-blue-200 text-sm">
                  <div className="font-medium mb-2">💡 升级提示</div>
                  <div className="space-y-1 text-xs">
                    <p>• 等级基于月收入和创作质量综合评定</p>
                    <p>• 每月1号更新等级，连续3个月达标可升级</p>
                    <p>• 高等级享受更多平台资源和收益分成</p>
                    <p>• 违规行为可能导致等级降低</p>
                  </div>
                </div>
              </div>

              <button
                onClick={() => setShowLevelModal(false)}
                className="w-full py-3 bg-gray-700 rounded-xl text-white font-medium"
              >
                关闭
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
