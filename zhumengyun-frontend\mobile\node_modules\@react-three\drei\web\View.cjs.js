"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),n=require("@react-three/fiber"),c=require("tunnel-rat");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var o=a(e),s=i(t),l=i(r),u=a(c);const f=new l.Color,d=u.default();function m(e){return"top"in e}function h(e,t){const{right:r,top:n,left:c,bottom:a,width:i,height:o}=t,s=t.bottom<0||n>e.height||r<0||t.left>e.width;if(m(e)){const t=e.top+e.height-a;return{position:{width:i,height:o,left:c-e.left,top:n,bottom:t,right:r},isOffscreen:s}}return{position:{width:i,height:o,top:n,left:c,bottom:e.height-a,right:r},isOffscreen:s}}function g(e,{left:t,bottom:r,width:n,height:c}){let a;const i=n/c;var o;return(o=e.camera)&&o.isOrthographicCamera?e.camera.manual?e.camera.updateProjectionMatrix():e.camera.left===n/-2&&e.camera.right===n/2&&e.camera.top===c/2&&e.camera.bottom===c/-2||(Object.assign(e.camera,{left:n/-2,right:n/2,top:c/2,bottom:c/-2}),e.camera.updateProjectionMatrix()):e.camera.aspect!==i&&(e.camera.aspect=i,e.camera.updateProjectionMatrix()),a=e.gl.autoClear,e.gl.autoClear=!1,e.gl.setViewport(t,r,n,c),e.gl.setScissor(t,r,n,c),e.gl.setScissorTest(!0),a}function p(e,t){e.gl.setScissorTest(!1),e.gl.autoClear=t}function v(e){e.gl.getClearColor(f),e.gl.setClearColor(f,e.gl.getClearAlpha()),e.gl.clear(!0,!0)}function b({visible:e=!0,canvasSize:t,scene:r,index:c,children:a,frames:i,rect:o,track:l}){const u=n.useThree(),[f,d]=s.useState(!1);let b=0;return n.useFrame((n=>{var c;(i===1/0||b<=i)&&(l&&(o.current=null==(c=l.current)?void 0:c.getBoundingClientRect()),b++);if(o.current){const{position:c,isOffscreen:i}=h(t,o.current);if(f!==i&&d(i),e&&!f&&o.current){const e=g(n,c);n.gl.render(a?n.scene:r,n.camera),p(n,e)}}}),c),s.useLayoutEffect((()=>{const r=o.current;if(r&&(!e||!f)){const{position:e}=h(t,r),n=g(u,e);v(u),p(u,n)}}),[e,f]),s.useEffect((()=>{if(!l)return;const e=o.current,r=u.get().events.connected;return u.setEvents({connected:l.current}),()=>{if(e){const{position:r}=h(t,e),n=g(u,r);v(u),p(u,n)}u.setEvents({connected:r})}}),[l]),s.useEffect((()=>{m(t)||console.warn("Detected @react-three/fiber canvas size does not include position information. <View /> may not work as expected. Upgrade to @react-three/fiber ^8.1.0 for support.\n See https://github.com/pmndrs/drei/issues/944")}),[]),s.createElement(s.Fragment,null,a,s.createElement("group",{onPointerOver:()=>null}))}const w=s.forwardRef((({track:e,visible:t=!0,index:r=1,id:c,style:a,className:i,frames:u=1/0,children:f,...d},m)=>{var h,g,p,v;const w=s.useRef(null),{size:E,scene:C}=n.useThree(),[x]=s.useState((()=>new l.Scene)),[O,y]=s.useReducer((()=>!0),!1),j=s.useCallback(((t,r)=>{if(w.current&&e&&e.current&&t.target===e.current){const{width:e,height:n,left:c,top:a}=w.current,i=t.clientX-c,o=t.clientY-a;r.pointer.set(i/e*2-1,-o/n*2+1),r.raycaster.setFromCamera(r.pointer,r.camera)}}),[w,e]);return s.useEffect((()=>{var t;e&&(w.current=null==(t=e.current)?void 0:t.getBoundingClientRect()),y()}),[e]),s.createElement("group",o.default({ref:m},d),O&&n.createPortal(s.createElement(b,{visible:t,canvasSize:E,frames:u,scene:C,track:e,rect:w,index:r},f),x,{events:{compute:j,priority:r},size:{width:null==(h=w.current)?void 0:h.width,height:null==(g=w.current)?void 0:g.height,top:null==(p=w.current)?void 0:p.top,left:null==(v=w.current)?void 0:v.left}}))})),E=s.forwardRef((({as:e="div",id:t,visible:r,className:n,style:c,index:a=1,track:i,frames:l=1/0,children:u,...f},m)=>{const h=s.useId(),g=s.useRef(null);return s.useImperativeHandle(m,(()=>g.current)),s.createElement(s.Fragment,null,s.createElement(e,o.default({ref:g,id:t,className:n,style:c},f)),s.createElement(d.In,null,s.createElement(w,{visible:r,key:h,track:g,frames:l,index:a},u)))})),C=(()=>{const e=s.forwardRef(((e,t)=>s.useContext(n.context)?s.createElement(w,o.default({ref:t},e)):s.createElement(E,o.default({ref:t},e))));return e.Port=()=>s.createElement(d.Out,null),e})();exports.View=C;
