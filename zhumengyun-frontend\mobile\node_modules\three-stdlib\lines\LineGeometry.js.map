{"version": 3, "file": "LineGeometry.js", "sources": ["../../src/lines/LineGeometry.js"], "sourcesContent": ["import { LineSegmentsGeometry } from '../lines/LineSegmentsGeometry'\n\nclass LineGeometry extends LineSegmentsGeometry {\n  constructor() {\n    super()\n\n    this.isLineGeometry = true\n\n    this.type = 'LineGeometry'\n  }\n\n  setPositions(array) {\n    // converts [ x1, y1, z1,  x2, y2, z2, ... ] to pairs format\n\n    const length = array.length - 3\n    const points = new Float32Array(2 * length)\n\n    for (let i = 0; i < length; i += 3) {\n      points[2 * i] = array[i]\n      points[2 * i + 1] = array[i + 1]\n      points[2 * i + 2] = array[i + 2]\n\n      points[2 * i + 3] = array[i + 3]\n      points[2 * i + 4] = array[i + 4]\n      points[2 * i + 5] = array[i + 5]\n    }\n\n    super.setPositions(points)\n\n    return this\n  }\n\n  setColors(array, itemSize = 3) {\n    // converts [ r1, g1, b1, (a1),  r2, g2, b2, (a2), ... ] to pairs format\n\n    const length = array.length - itemSize\n    const colors = new Float32Array(2 * length)\n\n    if (itemSize === 3) {\n      for (let i = 0; i < length; i += itemSize) {\n        colors[2 * i] = array[i]\n        colors[2 * i + 1] = array[i + 1]\n        colors[2 * i + 2] = array[i + 2]\n\n        colors[2 * i + 3] = array[i + 3]\n        colors[2 * i + 4] = array[i + 4]\n        colors[2 * i + 5] = array[i + 5]\n      }\n    } else {\n      for (let i = 0; i < length; i += itemSize) {\n        colors[2 * i] = array[i]\n        colors[2 * i + 1] = array[i + 1]\n        colors[2 * i + 2] = array[i + 2]\n        colors[2 * i + 3] = array[i + 3]\n\n        colors[2 * i + 4] = array[i + 4]\n        colors[2 * i + 5] = array[i + 5]\n        colors[2 * i + 6] = array[i + 6]\n        colors[2 * i + 7] = array[i + 7]\n      }\n    }\n\n    super.setColors(colors, itemSize)\n\n    return this\n  }\n\n  fromLine(line) {\n    const geometry = line.geometry\n\n    this.setPositions(geometry.attributes.position.array) // assumes non-indexed\n\n    // set colors, maybe\n\n    return this\n  }\n}\n\nexport { LineGeometry }\n"], "names": [], "mappings": ";AAEA,MAAM,qBAAqB,qBAAqB;AAAA,EAC9C,cAAc;AACZ,UAAO;AAEP,SAAK,iBAAiB;AAEtB,SAAK,OAAO;AAAA,EACb;AAAA,EAED,aAAa,OAAO;AAGlB,UAAM,SAAS,MAAM,SAAS;AAC9B,UAAM,SAAS,IAAI,aAAa,IAAI,MAAM;AAE1C,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,aAAO,IAAI,CAAC,IAAI,MAAM,CAAC;AACvB,aAAO,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAC/B,aAAO,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAE/B,aAAO,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAC/B,aAAO,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAC/B,aAAO,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAAA,IAChC;AAED,UAAM,aAAa,MAAM;AAEzB,WAAO;AAAA,EACR;AAAA,EAED,UAAU,OAAO,WAAW,GAAG;AAG7B,UAAM,SAAS,MAAM,SAAS;AAC9B,UAAM,SAAS,IAAI,aAAa,IAAI,MAAM;AAE1C,QAAI,aAAa,GAAG;AAClB,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,UAAU;AACzC,eAAO,IAAI,CAAC,IAAI,MAAM,CAAC;AACvB,eAAO,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAC/B,eAAO,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAE/B,eAAO,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAC/B,eAAO,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAC/B,eAAO,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAAA,MAChC;AAAA,IACP,OAAW;AACL,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,UAAU;AACzC,eAAO,IAAI,CAAC,IAAI,MAAM,CAAC;AACvB,eAAO,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAC/B,eAAO,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAC/B,eAAO,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAE/B,eAAO,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAC/B,eAAO,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAC/B,eAAO,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAC/B,eAAO,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAAA,MAChC;AAAA,IACF;AAED,UAAM,UAAU,QAAQ,QAAQ;AAEhC,WAAO;AAAA,EACR;AAAA,EAED,SAAS,MAAM;AACb,UAAM,WAAW,KAAK;AAEtB,SAAK,aAAa,SAAS,WAAW,SAAS,KAAK;AAIpD,WAAO;AAAA,EACR;AACH;"}