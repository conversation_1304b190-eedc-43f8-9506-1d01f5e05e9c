'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'

export default function WithdrawPage() {
  const router = useRouter()
  const [balance, setBalance] = useState({
    available: 15680.50,
    pending: 2340.00,
    total: 18020.50
  })
  const [withdrawAmount, setWithdrawAmount] = useState('')
  const [withdrawMethod, setWithdrawMethod] = useState<'bank' | 'alipay' | 'wechat'>('bank')
  const [bankInfo, setBankInfo] = useState({
    bankName: '',
    accountNumber: '',
    accountName: '',
    branchName: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showTaxInfo, setShowTaxInfo] = useState(false)

  // 计算税费
  const calculateTax = (amount: number) => {
    // 简化的税费计算（实际应根据税法计算）
    if (amount <= 800) return 0
    if (amount <= 4000) return (amount - 800) * 0.2
    return amount * 0.2 * 0.8 // 减除20%费用后的80%按20%税率
  }

  const handleWithdraw = async () => {
    const amount = parseFloat(withdrawAmount)
    if (!amount || amount <= 0) {
      alert('请输入有效的提现金额')
      return
    }

    if (amount > balance.available) {
      alert('提现金额不能超过可用余额')
      return
    }

    if (amount < 100) {
      alert('最低提现金额为100元')
      return
    }

    setIsSubmitting(true)
    try {
      // 模拟提现申请
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      alert('提现申请提交成功！预计1-3个工作日到账')
      router.push('/profile')
    } catch (error) {
      console.error('提现申请失败:', error)
      alert('提现申请失败，请稍后重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  const tax = withdrawAmount ? calculateTax(parseFloat(withdrawAmount)) : 0
  const actualAmount = withdrawAmount ? parseFloat(withdrawAmount) - tax : 0

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 text-white">
      {/* 头部 */}
      <div className="sticky top-0 z-20 bg-black/20 backdrop-blur-lg border-b border-white/10">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <button onClick={() => router.back()} className="text-white">
              ← 返回
            </button>
            <h1 className="text-xl font-bold">收益提现</h1>
            <div className="w-8"></div>
          </div>
        </div>
      </div>

      <div className="px-4 py-6 space-y-6">
        {/* 余额概览 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-sm rounded-2xl p-6 border border-green-500/30"
        >
          <h2 className="text-lg font-bold mb-4 text-center">账户余额</h2>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-400">¥{balance.available.toLocaleString()}</div>
              <div className="text-xs text-gray-400">可提现</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-yellow-400">¥{balance.pending.toLocaleString()}</div>
              <div className="text-xs text-gray-400">待结算</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-400">¥{balance.total.toLocaleString()}</div>
              <div className="text-xs text-gray-400">总收益</div>
            </div>
          </div>
        </motion.div>

        {/* 提现表单 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20"
        >
          <h3 className="text-lg font-bold mb-4">提现申请</h3>
          
          {/* 提现金额 */}
          <div className="mb-6">
            <label className="block text-white text-sm font-medium mb-2">提现金额 *</label>
            <div className="relative">
              <input
                type="number"
                value={withdrawAmount}
                onChange={(e) => setWithdrawAmount(e.target.value)}
                placeholder="请输入提现金额"
                className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 pr-16 text-white placeholder-gray-400"
              />
              <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">元</span>
            </div>
            <div className="flex justify-between mt-2 text-xs text-gray-400">
              <span>最低提现：100元</span>
              <span>可用余额：¥{balance.available.toLocaleString()}</span>
            </div>
            
            {/* 快捷金额 */}
            <div className="flex space-x-2 mt-3">
              {[500, 1000, 5000, balance.available].map((amount) => (
                <button
                  key={amount}
                  onClick={() => setWithdrawAmount(amount.toString())}
                  className="px-3 py-1 bg-white/10 rounded-lg text-xs hover:bg-white/20 transition-colors"
                >
                  {amount === balance.available ? '全部' : `¥${amount}`}
                </button>
              ))}
            </div>
          </div>

          {/* 提现方式 */}
          <div className="mb-6">
            <label className="block text-white text-sm font-medium mb-3">提现方式 *</label>
            <div className="space-y-3">
              {[
                { key: 'bank', label: '银行卡', icon: '🏦', desc: '1-3个工作日到账' },
                { key: 'alipay', label: '支付宝', icon: '💙', desc: '实时到账' },
                { key: 'wechat', label: '微信', icon: '💚', desc: '实时到账' }
              ].map((method) => (
                <button
                  key={method.key}
                  onClick={() => setWithdrawMethod(method.key as any)}
                  className={`w-full p-4 rounded-lg border-2 transition-all text-left ${
                    withdrawMethod === method.key
                      ? 'border-blue-500 bg-blue-500/20'
                      : 'border-white/20 bg-white/5 hover:bg-white/10'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{method.icon}</span>
                      <div>
                        <div className="font-medium">{method.label}</div>
                        <div className="text-xs text-gray-400">{method.desc}</div>
                      </div>
                    </div>
                    {withdrawMethod === method.key && (
                      <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-xs">✓</span>
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* 银行卡信息 */}
          {withdrawMethod === 'bank' && (
            <div className="mb-6 space-y-4">
              <div>
                <label className="block text-white text-sm font-medium mb-2">银行名称 *</label>
                <input
                  type="text"
                  value={bankInfo.bankName}
                  onChange={(e) => setBankInfo(prev => ({ ...prev, bankName: e.target.value }))}
                  placeholder="请输入银行名称"
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400"
                />
              </div>
              <div>
                <label className="block text-white text-sm font-medium mb-2">银行卡号 *</label>
                <input
                  type="text"
                  value={bankInfo.accountNumber}
                  onChange={(e) => setBankInfo(prev => ({ ...prev, accountNumber: e.target.value }))}
                  placeholder="请输入银行卡号"
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400"
                />
              </div>
              <div>
                <label className="block text-white text-sm font-medium mb-2">开户姓名 *</label>
                <input
                  type="text"
                  value={bankInfo.accountName}
                  onChange={(e) => setBankInfo(prev => ({ ...prev, accountName: e.target.value }))}
                  placeholder="请输入开户姓名"
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400"
                />
              </div>
            </div>
          )}

          {/* 税费计算 */}
          {withdrawAmount && parseFloat(withdrawAmount) > 0 && (
            <div className="mb-6">
              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-yellow-200 font-medium">税费计算</span>
                  <button
                    onClick={() => setShowTaxInfo(!showTaxInfo)}
                    className="text-yellow-400 text-xs underline"
                  >
                    {showTaxInfo ? '收起' : '详情'}
                  </button>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">提现金额：</span>
                    <span className="text-white">¥{parseFloat(withdrawAmount).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">个人所得税：</span>
                    <span className="text-red-400">-¥{tax.toFixed(2)}</span>
                  </div>
                  <div className="border-t border-yellow-500/20 pt-2">
                    <div className="flex justify-between font-medium">
                      <span className="text-yellow-200">实际到账：</span>
                      <span className="text-green-400">¥{actualAmount.toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                {showTaxInfo && (
                  <div className="mt-4 pt-4 border-t border-yellow-500/20">
                    <div className="text-yellow-100 text-xs space-y-1">
                      <p className="font-medium">税费说明：</p>
                      <p>• 根据《个人所得税法》，劳务报酬所得需缴纳个人所得税</p>
                      <p>• 800元以下免税，800-4000元减除800元后按20%税率</p>
                      <p>• 4000元以上减除20%费用后按20%税率</p>
                      <p>• 平台将代扣代缴个人所得税</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 合规提示 */}
          <div className="mb-6">
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
              <div className="text-blue-200 text-xs space-y-1">
                <p className="font-medium mb-2">提现须知：</p>
                <p>• 提现需完成实名认证和银行卡绑定</p>
                <p>• 平台将依法代扣代缴个人所得税</p>
                <p>• 提现记录将作为纳税凭证保存</p>
                <p>• 如有疑问请联系客服：400-123-4567</p>
              </div>
            </div>
          </div>

          {/* 提交按钮 */}
          <button
            onClick={handleWithdraw}
            disabled={isSubmitting || !withdrawAmount || parseFloat(withdrawAmount) <= 0}
            className="w-full py-4 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl text-white font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? '提交中...' : '确认提现'}
          </button>
        </motion.div>

        {/* 提现记录 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20"
        >
          <h3 className="text-lg font-bold mb-4">最近提现记录</h3>
          
          <div className="space-y-3">
            {[
              { date: '2024-12-15', amount: 5000, status: '已到账', method: '银行卡' },
              { date: '2024-12-10', amount: 2000, status: '处理中', method: '支付宝' },
              { date: '2024-12-05', amount: 1500, status: '已到账', method: '微信' }
            ].map((record, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                <div>
                  <div className="font-medium">¥{record.amount.toLocaleString()}</div>
                  <div className="text-xs text-gray-400">{record.date} · {record.method}</div>
                </div>
                <div className={`px-2 py-1 rounded-full text-xs ${
                  record.status === '已到账' 
                    ? 'bg-green-500/20 text-green-400' 
                    : 'bg-yellow-500/20 text-yellow-400'
                }`}>
                  {record.status}
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  )
}
