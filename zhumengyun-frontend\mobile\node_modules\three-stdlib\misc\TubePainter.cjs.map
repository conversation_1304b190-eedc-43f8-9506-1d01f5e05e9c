{"version": 3, "file": "TubePainter.cjs", "sources": ["../../src/misc/TubePainter.js"], "sourcesContent": ["import {\n  BufferAttribute,\n  BufferGeometry,\n  Color,\n  DynamicDrawUsage,\n  Matrix4,\n  Mesh,\n  MeshStandardMaterial,\n  Vector3,\n} from 'three'\n\nfunction TubePainter() {\n  const BUFFER_SIZE = 1000000 * 3\n\n  const positions = new BufferAttribute(new Float32Array(BUFFER_SIZE), 3)\n  positions.usage = DynamicDrawUsage\n\n  const normals = new BufferAttribute(new Float32Array(BUFFER_SIZE), 3)\n  normals.usage = DynamicDrawUsage\n\n  const colors = new BufferAttribute(new Float32Array(BUFFER_SIZE), 3)\n  colors.usage = DynamicDrawUsage\n\n  const geometry = new BufferGeometry()\n  geometry.setAttribute('position', positions)\n  geometry.setAttribute('normal', normals)\n  geometry.setAttribute('color', colors)\n  geometry.drawRange.count = 0\n\n  const material = new MeshStandardMaterial({\n    vertexColors: true,\n  })\n\n  const mesh = new Mesh(geometry, material)\n  mesh.frustumCulled = false\n\n  //\n\n  function getPoints(size) {\n    const PI2 = Math.PI * 2\n\n    const sides = 10\n    const array = []\n    const radius = 0.01 * size\n\n    for (let i = 0; i < sides; i++) {\n      const angle = (i / sides) * PI2\n      array.push(new Vector3(Math.sin(angle) * radius, Math.cos(angle) * radius, 0))\n    }\n\n    return array\n  }\n\n  //\n\n  const vector1 = new Vector3()\n  const vector2 = new Vector3()\n  const vector3 = new Vector3()\n  const vector4 = new Vector3()\n\n  const color = new Color(0xffffff)\n  let size = 1\n\n  function stroke(position1, position2, matrix1, matrix2) {\n    if (position1.distanceToSquared(position2) === 0) return\n\n    let count = geometry.drawRange.count\n\n    const points = getPoints(size)\n\n    for (let i = 0, il = points.length; i < il; i++) {\n      const vertex1 = points[i]\n      const vertex2 = points[(i + 1) % il]\n\n      // positions\n\n      vector1.copy(vertex1).applyMatrix4(matrix2).add(position2)\n      vector2.copy(vertex2).applyMatrix4(matrix2).add(position2)\n      vector3.copy(vertex2).applyMatrix4(matrix1).add(position1)\n      vector4.copy(vertex1).applyMatrix4(matrix1).add(position1)\n\n      vector1.toArray(positions.array, (count + 0) * 3)\n      vector2.toArray(positions.array, (count + 1) * 3)\n      vector4.toArray(positions.array, (count + 2) * 3)\n\n      vector2.toArray(positions.array, (count + 3) * 3)\n      vector3.toArray(positions.array, (count + 4) * 3)\n      vector4.toArray(positions.array, (count + 5) * 3)\n\n      // normals\n\n      vector1.copy(vertex1).applyMatrix4(matrix2).normalize()\n      vector2.copy(vertex2).applyMatrix4(matrix2).normalize()\n      vector3.copy(vertex2).applyMatrix4(matrix1).normalize()\n      vector4.copy(vertex1).applyMatrix4(matrix1).normalize()\n\n      vector1.toArray(normals.array, (count + 0) * 3)\n      vector2.toArray(normals.array, (count + 1) * 3)\n      vector4.toArray(normals.array, (count + 2) * 3)\n\n      vector2.toArray(normals.array, (count + 3) * 3)\n      vector3.toArray(normals.array, (count + 4) * 3)\n      vector4.toArray(normals.array, (count + 5) * 3)\n\n      // colors\n\n      color.toArray(colors.array, (count + 0) * 3)\n      color.toArray(colors.array, (count + 1) * 3)\n      color.toArray(colors.array, (count + 2) * 3)\n\n      color.toArray(colors.array, (count + 3) * 3)\n      color.toArray(colors.array, (count + 4) * 3)\n      color.toArray(colors.array, (count + 5) * 3)\n\n      count += 6\n    }\n\n    geometry.drawRange.count = count\n  }\n\n  //\n\n  const up = new Vector3(0, 1, 0)\n\n  const point1 = new Vector3()\n  const point2 = new Vector3()\n\n  const matrix1 = new Matrix4()\n  const matrix2 = new Matrix4()\n\n  function moveTo(position) {\n    point1.copy(position)\n    matrix1.lookAt(point2, point1, up)\n\n    point2.copy(position)\n    matrix2.copy(matrix1)\n  }\n\n  function lineTo(position) {\n    point1.copy(position)\n    matrix1.lookAt(point2, point1, up)\n\n    stroke(point1, point2, matrix1, matrix2)\n\n    point2.copy(point1)\n    matrix2.copy(matrix1)\n  }\n\n  function setSize(value) {\n    size = value\n  }\n\n  //\n\n  let count = 0\n\n  function update() {\n    const start = count\n    const end = geometry.drawRange.count\n\n    if (start === end) return\n\n    positions.updateRange.offset = start * 3\n    positions.updateRange.count = (end - start) * 3\n    positions.needsUpdate = true\n\n    normals.updateRange.offset = start * 3\n    normals.updateRange.count = (end - start) * 3\n    normals.needsUpdate = true\n\n    colors.updateRange.offset = start * 3\n    colors.updateRange.count = (end - start) * 3\n    colors.needsUpdate = true\n\n    count = geometry.drawRange.count\n  }\n\n  return {\n    mesh: mesh,\n    moveTo: moveTo,\n    lineTo: lineTo,\n    setSize: setSize,\n    update: update,\n  }\n}\n\nexport { TubePainter }\n"], "names": ["BufferAttribute", "DynamicDrawUsage", "BufferGeometry", "MeshStandardMaterial", "<PERSON><PERSON>", "size", "Vector3", "Color", "matrix1", "matrix2", "count", "Matrix4"], "mappings": ";;;AAWA,SAAS,cAAc;AACrB,QAAM,cAAc,MAAU;AAE9B,QAAM,YAAY,IAAIA,MAAe,gBAAC,IAAI,aAAa,WAAW,GAAG,CAAC;AACtE,YAAU,QAAQC,MAAgB;AAElC,QAAM,UAAU,IAAID,MAAe,gBAAC,IAAI,aAAa,WAAW,GAAG,CAAC;AACpE,UAAQ,QAAQC,MAAgB;AAEhC,QAAM,SAAS,IAAID,MAAe,gBAAC,IAAI,aAAa,WAAW,GAAG,CAAC;AACnE,SAAO,QAAQC,MAAgB;AAE/B,QAAM,WAAW,IAAIC,qBAAgB;AACrC,WAAS,aAAa,YAAY,SAAS;AAC3C,WAAS,aAAa,UAAU,OAAO;AACvC,WAAS,aAAa,SAAS,MAAM;AACrC,WAAS,UAAU,QAAQ;AAE3B,QAAM,WAAW,IAAIC,2BAAqB;AAAA,IACxC,cAAc;AAAA,EAClB,CAAG;AAED,QAAM,OAAO,IAAIC,WAAK,UAAU,QAAQ;AACxC,OAAK,gBAAgB;AAIrB,WAAS,UAAUC,OAAM;AACvB,UAAM,MAAM,KAAK,KAAK;AAEtB,UAAM,QAAQ;AACd,UAAM,QAAQ,CAAE;AAChB,UAAM,SAAS,OAAOA;AAEtB,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,YAAM,QAAS,IAAI,QAAS;AAC5B,YAAM,KAAK,IAAIC,MAAO,QAAC,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,IAAI,KAAK,IAAI,QAAQ,CAAC,CAAC;AAAA,IAC9E;AAED,WAAO;AAAA,EACR;AAID,QAAM,UAAU,IAAIA,cAAS;AAC7B,QAAM,UAAU,IAAIA,cAAS;AAC7B,QAAM,UAAU,IAAIA,cAAS;AAC7B,QAAM,UAAU,IAAIA,cAAS;AAE7B,QAAM,QAAQ,IAAIC,MAAK,MAAC,QAAQ;AAChC,MAAI,OAAO;AAEX,WAAS,OAAO,WAAW,WAAWC,UAASC,UAAS;AACtD,QAAI,UAAU,kBAAkB,SAAS,MAAM;AAAG;AAElD,QAAIC,SAAQ,SAAS,UAAU;AAE/B,UAAM,SAAS,UAAU,IAAI;AAE7B,aAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AAC/C,YAAM,UAAU,OAAO,CAAC;AACxB,YAAM,UAAU,QAAQ,IAAI,KAAK,EAAE;AAInC,cAAQ,KAAK,OAAO,EAAE,aAAaD,QAAO,EAAE,IAAI,SAAS;AACzD,cAAQ,KAAK,OAAO,EAAE,aAAaA,QAAO,EAAE,IAAI,SAAS;AACzD,cAAQ,KAAK,OAAO,EAAE,aAAaD,QAAO,EAAE,IAAI,SAAS;AACzD,cAAQ,KAAK,OAAO,EAAE,aAAaA,QAAO,EAAE,IAAI,SAAS;AAEzD,cAAQ,QAAQ,UAAU,QAAQE,SAAQ,KAAK,CAAC;AAChD,cAAQ,QAAQ,UAAU,QAAQA,SAAQ,KAAK,CAAC;AAChD,cAAQ,QAAQ,UAAU,QAAQA,SAAQ,KAAK,CAAC;AAEhD,cAAQ,QAAQ,UAAU,QAAQA,SAAQ,KAAK,CAAC;AAChD,cAAQ,QAAQ,UAAU,QAAQA,SAAQ,KAAK,CAAC;AAChD,cAAQ,QAAQ,UAAU,QAAQA,SAAQ,KAAK,CAAC;AAIhD,cAAQ,KAAK,OAAO,EAAE,aAAaD,QAAO,EAAE,UAAW;AACvD,cAAQ,KAAK,OAAO,EAAE,aAAaA,QAAO,EAAE,UAAW;AACvD,cAAQ,KAAK,OAAO,EAAE,aAAaD,QAAO,EAAE,UAAW;AACvD,cAAQ,KAAK,OAAO,EAAE,aAAaA,QAAO,EAAE,UAAW;AAEvD,cAAQ,QAAQ,QAAQ,QAAQE,SAAQ,KAAK,CAAC;AAC9C,cAAQ,QAAQ,QAAQ,QAAQA,SAAQ,KAAK,CAAC;AAC9C,cAAQ,QAAQ,QAAQ,QAAQA,SAAQ,KAAK,CAAC;AAE9C,cAAQ,QAAQ,QAAQ,QAAQA,SAAQ,KAAK,CAAC;AAC9C,cAAQ,QAAQ,QAAQ,QAAQA,SAAQ,KAAK,CAAC;AAC9C,cAAQ,QAAQ,QAAQ,QAAQA,SAAQ,KAAK,CAAC;AAI9C,YAAM,QAAQ,OAAO,QAAQA,SAAQ,KAAK,CAAC;AAC3C,YAAM,QAAQ,OAAO,QAAQA,SAAQ,KAAK,CAAC;AAC3C,YAAM,QAAQ,OAAO,QAAQA,SAAQ,KAAK,CAAC;AAE3C,YAAM,QAAQ,OAAO,QAAQA,SAAQ,KAAK,CAAC;AAC3C,YAAM,QAAQ,OAAO,QAAQA,SAAQ,KAAK,CAAC;AAC3C,YAAM,QAAQ,OAAO,QAAQA,SAAQ,KAAK,CAAC;AAE3C,MAAAA,UAAS;AAAA,IACV;AAED,aAAS,UAAU,QAAQA;AAAA,EAC5B;AAID,QAAM,KAAK,IAAIJ,MAAAA,QAAQ,GAAG,GAAG,CAAC;AAE9B,QAAM,SAAS,IAAIA,cAAS;AAC5B,QAAM,SAAS,IAAIA,cAAS;AAE5B,QAAM,UAAU,IAAIK,cAAS;AAC7B,QAAM,UAAU,IAAIA,cAAS;AAE7B,WAAS,OAAO,UAAU;AACxB,WAAO,KAAK,QAAQ;AACpB,YAAQ,OAAO,QAAQ,QAAQ,EAAE;AAEjC,WAAO,KAAK,QAAQ;AACpB,YAAQ,KAAK,OAAO;AAAA,EACrB;AAED,WAAS,OAAO,UAAU;AACxB,WAAO,KAAK,QAAQ;AACpB,YAAQ,OAAO,QAAQ,QAAQ,EAAE;AAEjC,WAAO,QAAQ,QAAQ,SAAS,OAAO;AAEvC,WAAO,KAAK,MAAM;AAClB,YAAQ,KAAK,OAAO;AAAA,EACrB;AAED,WAAS,QAAQ,OAAO;AACtB,WAAO;AAAA,EACR;AAID,MAAI,QAAQ;AAEZ,WAAS,SAAS;AAChB,UAAM,QAAQ;AACd,UAAM,MAAM,SAAS,UAAU;AAE/B,QAAI,UAAU;AAAK;AAEnB,cAAU,YAAY,SAAS,QAAQ;AACvC,cAAU,YAAY,SAAS,MAAM,SAAS;AAC9C,cAAU,cAAc;AAExB,YAAQ,YAAY,SAAS,QAAQ;AACrC,YAAQ,YAAY,SAAS,MAAM,SAAS;AAC5C,YAAQ,cAAc;AAEtB,WAAO,YAAY,SAAS,QAAQ;AACpC,WAAO,YAAY,SAAS,MAAM,SAAS;AAC3C,WAAO,cAAc;AAErB,YAAQ,SAAS,UAAU;AAAA,EAC5B;AAED,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACH;;"}