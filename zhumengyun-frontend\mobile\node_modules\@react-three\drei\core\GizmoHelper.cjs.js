"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("@react-three/fiber"),t=require("three"),n=require("./OrthographicCamera.cjs.js"),o=require("./Hud.cjs.js");function i(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}require("@babel/runtime/helpers/extends"),require("./Fbo.cjs.js");var u=i(e);const a=u.createContext({}),c=2*Math.PI,s=new t.Object3D,l=new t.Matrix4,[p,d]=[new t.Quaternion,new t.Quaternion],f=new t.Vector3,h=new t.Vector3,m=e=>"getTarget"in e;exports.GizmoHelper=({alignment:e="bottom-right",margin:i=[80,80],renderPriority:g=1,onUpdate:y,onTarget:b,children:j})=>{const w=r.useThree((e=>e.size)),v=r.useThree((e=>e.camera)),q=r.useThree((e=>e.controls)),x=r.useThree((e=>e.invalidate)),O=u.useRef(null),T=u.useRef(null),P=u.useRef(!1),C=u.useRef(0),R=u.useRef(new t.Vector3(0,0,0)),z=u.useRef(new t.Vector3(0,0,0));u.useEffect((()=>{z.current.copy(v.up),s.up.copy(v.up)}),[v]);const E=u.useCallback((e=>{P.current=!0,(q||b)&&(R.current=(null==b?void 0:b())||(m(q)?q.getTarget(R.current):null==q?void 0:q.target)),C.current=v.position.distanceTo(f),p.copy(v.quaternion),h.copy(e).multiplyScalar(C.current).add(f),s.lookAt(h),d.copy(s.quaternion),x()}),[q,v,b,x]);r.useFrame(((e,r)=>{if(T.current&&O.current){var t;if(P.current)if(p.angleTo(d)<.01)P.current=!1,"minPolarAngle"in q&&v.up.copy(z.current);else{const e=r*c;p.rotateTowards(d,e),v.position.set(0,0,1).applyQuaternion(p).multiplyScalar(C.current).add(R.current),v.up.set(0,1,0).applyQuaternion(p).normalize(),v.quaternion.copy(p),m(q)&&q.setPosition(v.position.x,v.position.y,v.position.z),y?y():q&&q.update(r),x()}l.copy(v.matrix).invert(),null==(t=O.current)||t.quaternion.setFromRotationMatrix(l)}}));const M=u.useMemo((()=>({tweenCamera:E})),[E]),[k,Q]=i,V=e.endsWith("-center")?0:e.endsWith("-left")?-w.width/2+k:w.width/2-k,W=e.startsWith("center-")?0:e.startsWith("top-")?w.height/2-Q:-w.height/2+Q;return u.createElement(o.Hud,{renderPriority:g},u.createElement(a.Provider,{value:M},u.createElement(n.OrthographicCamera,{makeDefault:!0,ref:T,position:[0,0,200]}),u.createElement("group",{ref:O,position:[V,W,0]},j)))},exports.useGizmoContext=()=>u.useContext(a);
