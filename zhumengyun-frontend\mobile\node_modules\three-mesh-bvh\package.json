{"name": "three-mesh-bvh", "version": "0.7.8", "description": "A BVH implementation to speed up raycasting against three.js meshes.", "module": "src/index.js", "main": "build/index.umd.cjs", "type": "module", "types": "src/index.d.ts", "sideEffects": false, "scripts": {"start": "concurrently \"rollup -w -c rollup-templating.config.js\" \"vite --config ./vite.config.js\"", "build": "rollup -c rollup-templating.config.js && rollup -c", "build-silent": "rollup -c rollup-templating.config.js --silent && rollup -c --silent", "build-examples": "npm run build && vite build --config ./vite.config.js && cp ./example/coi-serviceworker.js ./example/bundle/", "test": "npm run build-silent && cd test && jest", "lint": "eslint \"./src/**/*.{js,ts}\" \"./test/**/*.{js,ts}\" \"./example/*.js\" && tsc --noEmit", "benchmark": "npm run build-silent && node benchmark/run-benchmark.js", "prepublishOnly": "npm run build"}, "files": ["src/*", "build/*"], "keywords": ["graphics", "raycast", "tree", "bounds", "threejs", "three-js", "bounds-hierarchy", "performance", "raytracing", "pathtracing", "geometry", "mesh", "distance", "intersection", "acceleration", "bvh", "webvr", "webxr"], "repository": {"type": "git", "url": "git+https://github.com/gkjo<PERSON>son/three-mesh-bvh.git"}, "author": "<PERSON> <garrett.k<PERSON><PERSON><PERSON>@gmail.com>", "license": "MIT", "bugs": {"url": "https://github.com/gk<PERSON><PERSON>son/three-mesh-bvh/issues"}, "homepage": "https://github.com/gk<PERSON><PERSON>son/three-mesh-bvh#readme", "peerDependencies": {"three": ">= 0.151.0"}, "devDependencies": {"@babel/core": "^7.15.5", "@babel/preset-env": "^7.15.4", "@types/eslint": "^7.28.1", "@types/jest": "^27.0.2", "@types/three": "^0.166.0", "@typescript-eslint/eslint-plugin": "^7.14.1", "@typescript-eslint/parser": "^7.14.1", "babel-jest": "^27.2.4", "concurrently": "^8.2.1", "eslint": "^8.56.0", "eslint-config-mdcs": "^5.0.0", "eslint-plugin-jest": "^28.6.0", "glob": "^10.3.3", "jest": "^27.2.4", "preprocess": "^3.2.0", "rollup": "^3.28.1", "script-loader": "^0.7.2", "simple-git": "^3.19.1", "simplex-noise": "^2.4.0", "static-server": "^2.2.1", "stats.js": "^0.17.0", "three": "^0.166.1", "typescript": "^5.1.3", "vite": "^5.2.13"}}