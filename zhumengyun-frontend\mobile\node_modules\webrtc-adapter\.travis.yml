sudo: false
language: node_js
dist: trusty
node_js:
- "7"

env:
  - CXX=g++-4.8
matrix:
  include:
    - os: linux
      sudo: false
      env: BROWSER=chrome  BVER=stable
    - os: linux
      sudo: false
      env: BROWSER=chrome  BVER=beta
    - os: linux
      sudo: false
      env: BROWSER=chrome  BVER=unstable
    - os: linux
      sudo: false
      env: BROWSER=firefox BVER=stable
    - os: linux
      sudo: false
      env: BROWSER=firefox BVER=beta
    - os: linux
      sudo: false
      env: BROWSER=firefox BVER=unstable
    - os: osx
      sudo: required
      osx_image: xcode9.4
      env: BROWSER=safari BVER=stable
    - os: osx
      sudo: required
      osx_image: xcode11.2
      env: BROWSER=safari BVER=unstable

  fast_finish: true

  allow_failures:
    - os: linux
      sudo: false
      env: BROWSER=chrome  BVER=unstable
    - os: linux
      sudo: false
      env: BROWSER=firefox BVER=unstable

before_script:
  - ./node_modules/travis-multirunner/setup.sh
  - export DISPLAY=:99.0
  - if [ -f /etc/init.d/xvfb ]; then sh -e /etc/init.d/xvfb start; fi

after_failure:
  - for file in *.log; do echo $file; echo "======================"; cat $file; done || true

notifications:
  email:
  - 

addons:
  apt:
    sources:
      - ubuntu-toolchain-r-test
    packages:
      - g++-4.8
