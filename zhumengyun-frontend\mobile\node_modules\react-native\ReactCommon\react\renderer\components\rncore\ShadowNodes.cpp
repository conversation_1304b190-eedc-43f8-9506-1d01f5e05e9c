
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateShadowNodeCpp.js
 */

#include <react/renderer/components/rncore/ShadowNodes.h>

namespace facebook::react {

extern const char ActivityIndicatorViewComponentName[] = "ActivityIndicatorView";
extern const char AndroidDrawerLayoutComponentName[] = "AndroidDrawerLayout";
extern const char AndroidSwipeRefreshLayoutComponentName[] = "AndroidSwipeRefreshLayout";
extern const char DebuggingOverlayComponentName[] = "DebuggingOverlay";
extern const char PullToRefreshViewComponentName[] = "PullToRefreshView";
extern const char SwitchComponentName[] = "Switch";
extern const char UnimplementedNativeViewComponentName[] = "UnimplementedNativeView";

} // namespace facebook::react
