/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { quat, vec3 } from 'gl-matrix';
/**
 * Wrapper class for gl-matrix vec3
 * Minimal interoperable interface to Vector3 in Three.js and Babylon.js
 */
export declare class Vector3 {
    vec3: vec3;
    private tempVec3;
    constructor(x?: number, y?: number, z?: number);
    get x(): number;
    set x(value: number);
    get y(): number;
    set y(value: number);
    get z(): number;
    set z(value: number);
    set(x: number, y: number, z: number): this;
    clone(): Vector3;
    copy(v: Vector3): this;
    round(): this;
    normalize(): this;
    add(v: Vector3): this;
    applyQuaternion(q: Quaternion): this;
}
/**
 * Wrapper class for gl-matrix quat4
 * Minimal interoperable interface to Vector3 in Three.js and Babylon.js
 */
export declare class Quaternion {
    quat: quat;
    private tempQuat;
    constructor(x?: number, y?: number, z?: number, w?: number);
    get x(): number;
    set x(value: number);
    get y(): number;
    set y(value: number);
    get z(): number;
    set z(value: number);
    get w(): number;
    set w(value: number);
    set(x: number, y: number, z: number, w: number): this;
    clone(): Quaternion;
    copy(q: Quaternion): this;
    normalize(): this;
    invert(): this;
    multiply(q: Quaternion): this;
    setFromAxisAngle(axis: Vector3, angle: number): this;
}
//# sourceMappingURL=Math.d.ts.map