"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),n=require("three"),r=require("react"),t=require("./shaderMaterial.cjs.js"),i=require("@react-three/fiber"),o=require("three-stdlib"),c=require("../helpers/constants.cjs.js");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function s(e){if(e&&e.__esModule)return e;var n=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var t=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,t.get?t:{enumerable:!0,get:function(){return e[r]}})}})),n.default=e,Object.freeze(n)}var l=a(e),p=s(n),u=s(r);const f=t.shaderMaterial({screenspace:!1,color:new p.Color("black"),opacity:1,thickness:.05,size:new p.Vector2},"#include <common>\n   #include <morphtarget_pars_vertex>\n   #include <skinning_pars_vertex>\n   #include <clipping_planes_pars_vertex>\n   uniform float thickness;\n   uniform bool screenspace;\n   uniform vec2 size;\n   void main() {\n     #if defined (USE_SKINNING)\n\t     #include <beginnormal_vertex>\n       #include <morphnormal_vertex>\n       #include <skinbase_vertex>\n       #include <skinnormal_vertex>\n       #include <defaultnormal_vertex>\n     #endif\n     #include <begin_vertex>\n\t   #include <morphtarget_vertex>\n\t   #include <skinning_vertex>\n     #include <project_vertex>\n     #include <clipping_planes_vertex>\n     vec4 tNormal = vec4(normal, 0.0);\n     vec4 tPosition = vec4(transformed, 1.0);\n     #ifdef USE_INSTANCING\n       tNormal = instanceMatrix * tNormal;\n       tPosition = instanceMatrix * tPosition;\n     #endif\n     if (screenspace) {\n       vec3 newPosition = tPosition.xyz + tNormal.xyz * thickness;\n       gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0); \n     } else {\n       vec4 clipPosition = projectionMatrix * modelViewMatrix * tPosition;\n       vec4 clipNormal = projectionMatrix * modelViewMatrix * tNormal;\n       vec2 offset = normalize(clipNormal.xy) * thickness / size * clipPosition.w * 2.0;\n       clipPosition.xy += offset;\n       gl_Position = clipPosition;\n     }\n   }",`uniform vec3 color;\n   uniform float opacity;\n   #include <clipping_planes_pars_fragment>\n   void main(){\n     #include <clipping_planes_fragment>\n     gl_FragColor = vec4(color, opacity);\n     #include <tonemapping_fragment>\n     #include <${c.version>=154?"colorspace_fragment":"encodings_fragment"}>\n   }`);exports.Outlines=function({color:e="black",opacity:n=1,transparent:r=!1,screenspace:t=!1,toneMapped:c=!0,polygonOffset:a=!1,polygonOffsetFactor:s=0,renderOrder:d=0,thickness:m=.05,angle:g=Math.PI,clippingPlanes:v,...h}){const y=u.useRef(),[_]=u.useState((()=>new f({side:p.BackSide}))),{gl:x}=i.useThree(),M=x.getDrawingBufferSize(new p.Vector2);u.useMemo((()=>i.extend({OutlinesMaterial:f})),[]);const P=u.useRef(0),b=u.useRef();return u.useLayoutEffect((()=>{const e=y.current;if(!e)return;const n=e.parent;if(n&&n.geometry&&(P.current!==g||b.current!==n.geometry)){var r;P.current=g,b.current=n.geometry;let t=null==(r=e.children)?void 0:r[0];t&&(g&&t.geometry.dispose(),e.remove(t)),n.skeleton?(t=new p.SkinnedMesh,t.material=_,t.bind(n.skeleton,n.bindMatrix),e.add(t)):n.isInstancedMesh?(t=new p.InstancedMesh(n.geometry,_,n.count),t.instanceMatrix=n.instanceMatrix,e.add(t)):(t=new p.Mesh,t.material=_,e.add(t)),t.geometry=g?o.toCreasedNormals(n.geometry,g):n.geometry,t.morphTargetInfluences=n.morphTargetInfluences,t.morphTargetDictionary=n.morphTargetDictionary}})),u.useLayoutEffect((()=>{const o=y.current;if(!o)return;const l=o.children[0];if(l){l.renderOrder=d;const p=o.parent;i.applyProps(l,{morphTargetInfluences:p.morphTargetInfluences,morphTargetDictionary:p.morphTargetDictionary}),i.applyProps(l.material,{transparent:r,thickness:m,color:e,opacity:n,size:M,screenspace:t,toneMapped:c,polygonOffset:a,polygonOffsetFactor:s,clippingPlanes:v,clipping:v&&v.length>0})}})),u.useEffect((()=>()=>{const e=y.current;if(!e)return;const n=e.children[0];n&&(g&&n.geometry.dispose(),e.remove(n))}),[]),u.createElement("group",l.default({ref:y},h))};
