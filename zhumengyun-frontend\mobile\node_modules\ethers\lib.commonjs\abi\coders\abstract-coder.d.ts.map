{"version": 3, "file": "abstract-coder.d.ts", "sourceRoot": "", "sources": ["../../../src.ts/abi/coders/abstract-coder.ts"], "names": [], "mappings": "AAQA,OAAO,KAAK,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAEpE;;GAEG;AACH,eAAO,MAAM,QAAQ,EAAE,MAAW,CAAC;AA+CnC;;;;;;GAMG;AACH,qBAAa,MAAO,SAAQ,KAAK,CAAC,GAAG,CAAC;;IAMlC,CAAE,CAAC,EAAE,MAAM,GAAG,MAAM,GAAI,GAAG,CAAA;IAE3B;;OAEG;gBACS,GAAG,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC;IA+F/B;;;;;;OAMG;IACH,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC;IAYnC;;;;;;;OAOG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAY7C;;OAEG;IACH,KAAK,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,MAAM;IAyBnE;;OAEG;IACH,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,KAAK,OAAO,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,MAAM;IAmB3F;;OAEG;IACH,GAAG,CAAC,CAAC,SAAS,GAAG,GAAG,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC;IAezG;;;;;;;OAOG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG;IAa3B;;;OAGG;IACH,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,GAAG,MAAM;CAG3E;AAED;;;;;;;;;;;;GAYG;AACH,wBAAgB,iBAAiB,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,CAAC;IAAE,IAAI,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;IAAC,KAAK,EAAE,KAAK,CAAA;CAAE,CAAC,CAqBvG;AAeD;;GAEG;AACH,8BAAsB,KAAK;IAIvB,QAAQ,CAAC,IAAI,EAAG,MAAM,CAAC;IAIvB,QAAQ,CAAC,IAAI,EAAG,MAAM,CAAC;IAIvB,QAAQ,CAAC,SAAS,EAAG,MAAM,CAAC;IAK5B,QAAQ,CAAC,OAAO,EAAG,OAAO,CAAC;gBAEf,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO;IAM3E,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,KAAK;IAI/C,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,MAAM;IACnD,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,GAAG;IAEpC,QAAQ,CAAC,YAAY,IAAI,GAAG;CAC/B;AAED;;GAEG;AACH,qBAAa,MAAM;;;IAUf,IAAI,IAAI,IAAI,MAAM,CAEjB;IACD,IAAI,MAAM,IAAI,MAAM,CAA6B;IAQjD,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM;IAKpC,UAAU,CAAC,KAAK,EAAE,SAAS,GAAG,MAAM;IAUpC,UAAU,CAAC,KAAK,EAAE,YAAY,GAAG,MAAM;IAMvC,mBAAmB,IAAI,CAAC,KAAK,EAAE,YAAY,KAAK,IAAI;CAQvD;AAED;;GAEG;AACH,qBAAa,MAAM;;IAKf,QAAQ,CAAC,UAAU,EAAG,OAAO,CAAC;gBASlB,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,EAAE,OAAO,EAAE,YAAY,CAAC,EAAE,MAAM;IAWxE,IAAI,IAAI,IAAI,MAAM,CAAgC;IAClD,IAAI,UAAU,IAAI,MAAM,CAA8B;IACtD,IAAI,QAAQ,IAAI,MAAM,CAAyB;IAC/C,IAAI,KAAK,IAAI,UAAU,CAAuC;IAkC9D,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM;IAOjC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,OAAO,GAAG,UAAU;IAStD,SAAS,IAAI,MAAM;IAInB,SAAS,IAAI,MAAM;CAGtB"}