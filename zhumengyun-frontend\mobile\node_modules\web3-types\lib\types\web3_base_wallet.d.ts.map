{"version": 3, "file": "web3_base_wallet.d.ts", "sourceRoot": "", "sources": ["../../src/web3_base_wallet.ts"], "names": [], "mappings": "AAgBA,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAElD,MAAM,MAAM,MAAM,GAAG,aAAa,GAAG,aAAa,GAAG,aAAa,CAAC;AAEnE,MAAM,MAAM,aAAa,GAAG;IAC3B,IAAI,CAAC,EAAE,UAAU,GAAG,MAAM,CAAC;IAC3B,EAAE,CAAC,EAAE,UAAU,GAAG,MAAM,CAAC;IACzB,GAAG,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC;IAC1B,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,CAAC,CAAC,EAAE,MAAM,CAAC;IACX,CAAC,CAAC,EAAE,MAAM,CAAC;IACX,CAAC,CAAC,EAAE,MAAM,CAAC;IACX,CAAC,CAAC,EAAE,MAAM,CAAC;CACX,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG;IAC1B,KAAK,EAAE,MAAM,CAAC;IACd,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,IAAI,EAAE,UAAU,GAAG,MAAM,CAAC;CAC1B,CAAC;AACF,MAAM,MAAM,kBAAkB,GAAG;IAChC,CAAC,EAAE,MAAM,CAAC;IACV,KAAK,EAAE,MAAM,CAAC;IACd,GAAG,EAAE,aAAa,CAAC;IACnB,IAAI,EAAE,UAAU,GAAG,MAAM,CAAC;CAC1B,CAAC;AAEF,MAAM,MAAM,QAAQ,GAAG;IACtB,MAAM,EAAE;QACP,MAAM,EAAE,MAAM,CAAC;QACf,UAAU,EAAE,MAAM,CAAC;QACnB,YAAY,EAAE;YACb,EAAE,EAAE,MAAM,CAAC;SACX,CAAC;QACF,GAAG,EAAE,QAAQ,GAAG,QAAQ,CAAC;QACzB,SAAS,EAAE,YAAY,GAAG,kBAAkB,CAAC;QAC7C,GAAG,EAAE,SAAS,CAAC;KACf,CAAC;IACF,EAAE,EAAE,MAAM,CAAC;IACX,OAAO,EAAE,CAAC,CAAC;IACX,OAAO,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF,MAAM,MAAM,eAAe,GAAG;IAC7B,WAAW,EAAE,MAAM,CAAC;IACpB,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;CACV,CAAC;AAEF,MAAM,MAAM,qBAAqB,GAAG,eAAe,GAAG;IACrD,cAAc,EAAE,MAAM,CAAC;IACvB,eAAe,EAAE,MAAM,CAAC;CACxB,CAAC;AAEF,MAAM,MAAM,UAAU,GAAG,eAAe,GAAG;IAC1C,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,MAAM,CAAC;CAClB,CAAC;AAEF,MAAM,WAAW,qBAAqB;IACrC,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC;IACvB,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;IACzB,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC;IAC5B,QAAQ,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,WAAW,KAAK,OAAO,CAAC,qBAAqB,CAAC,CAAC;IAC9E,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,MAAM,KAAK,UAAU,CAAC;IACtE,QAAQ,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAC;CAC7F;AAED,MAAM,WAAW,mBAAmB,CAAC,CAAC;IACrC,mBAAmB,EAAE,CAAC,UAAU,EAAE,MAAM,KAAK,CAAC,CAAC;IAC/C,MAAM,EAAE,MAAM,CAAC,CAAC;IAChB,OAAO,EAAE,CACR,QAAQ,EAAE,QAAQ,GAAG,MAAM,EAC3B,QAAQ,EAAE,MAAM,EAChB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,KAC7B,OAAO,CAAC,CAAC,CAAC,CAAC;CAChB;AAED,8BAAsB,cAAc,CAAC,CAAC,SAAS,qBAAqB,CAAE,SAAQ,KAAK,CAAC,CAAC,CAAC;IACrF,SAAS,CAAC,QAAQ,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC;gBAEzC,eAAe,EAAE,mBAAmB,CAAC,CAAC,CAAC;aAK1C,MAAM,CAAC,gBAAgB,EAAE,MAAM,GAAG,IAAI;aACtC,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG,MAAM,GAAG,IAAI;aAC9B,GAAG,CAAC,cAAc,EAAE,MAAM,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS;aACnD,MAAM,CAAC,cAAc,EAAE,MAAM,GAAG,MAAM,GAAG,OAAO;aAChD,KAAK,IAAI,IAAI;aACb,OAAO,CACtB,QAAQ,EAAE,MAAM,EAChB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAC/B,OAAO,CAAC,QAAQ,EAAE,CAAC;aACN,OAAO,CACtB,eAAe,EAAE,QAAQ,EAAE,EAC3B,QAAQ,EAAE,MAAM,EAChB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAC/B,OAAO,CAAC,IAAI,CAAC;aACA,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;aAClE,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC;CAC/E"}