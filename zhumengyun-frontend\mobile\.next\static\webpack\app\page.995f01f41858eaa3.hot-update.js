"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/wechat-video-homepage.tsx":
/*!*******************************************!*\
  !*** ./src/app/wechat-video-homepage.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WeChatVideoHomepage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction WeChatVideoHomepage() {\n    _s();\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFollowing, setIsFollowing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showComments, setShowComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showShare, setShowShare] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('推荐');\n    const [showControls, setShowControls] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 视频内容数据\n    const videoContent = [\n        {\n            id: 1,\n            title: \"震撼！全球最高摩天大楼建设纪录片\",\n            author: \"建筑奇迹\",\n            authorAvatar: \"🏢\",\n            verified: true,\n            description: \"🏗️ 见证828米迪拜塔的建设全过程，工程技术的巅峰之作！\",\n            videoUrl: \"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4\",\n            likes: 1200000,\n            comments: 89000,\n            shares: 156000,\n            videoTime: \"12:30\",\n            friendCount: \"1个朋友关注\"\n        },\n        {\n            id: 2,\n            title: \"AI机器人自动化建房，24小时完工！\",\n            author: \"未来建筑师\",\n            authorAvatar: \"🤖\",\n            verified: true,\n            description: \"🚀 革命性的3D打印建筑技术，机器人24小时自动建房全过程！\",\n            videoUrl: \"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4\",\n            likes: 890000,\n            comments: 67000,\n            shares: 123000,\n            videoTime: \"08:45\",\n            friendCount: \"5个朋友关注\"\n        },\n        {\n            id: 3,\n            title: \"我家装修日记30天，终于看到希望了！\",\n            author: \"装修小白\",\n            authorAvatar: \"🏠\",\n            verified: false,\n            description: \"💪 历时一个月的装修终于有起色了，分享一下踩过的坑！\",\n            videoUrl: \"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4\",\n            likes: 456000,\n            comments: 23000,\n            shares: 78000,\n            videoTime: \"06:30\",\n            friendCount: \"5个朋友关注\"\n        }\n    ];\n    const currentContent = videoContent[currentIndex];\n    // 格式化数字\n    const formatNumber = (num)=>{\n        if (num >= 10000) {\n            return (num / 10000).toFixed(1) + 'w';\n        }\n        return num.toString();\n    };\n    // 交互处理函数\n    const handleLike = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleLike]\": ()=>setIsLiked(!isLiked)\n    }[\"WeChatVideoHomepage.useCallback[handleLike]\"], [\n        isLiked\n    ]);\n    const handleComment = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleComment]\": ()=>setShowComments(true)\n    }[\"WeChatVideoHomepage.useCallback[handleComment]\"], []);\n    const handleShare = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleShare]\": ()=>setShowShare(true)\n    }[\"WeChatVideoHomepage.useCallback[handleShare]\"], []);\n    const togglePlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[togglePlay]\": ()=>{\n            if (videoRef.current) {\n                if (isPlaying) {\n                    videoRef.current.pause();\n                } else {\n                    videoRef.current.play();\n                }\n                setIsPlaying(!isPlaying);\n            }\n        }\n    }[\"WeChatVideoHomepage.useCallback[togglePlay]\"], [\n        isPlaying\n    ]);\n    // 点击视频显示控制界面\n    const handleVideoClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleVideoClick]\": ()=>{\n            setShowControls(!showControls);\n            // 3秒后自动隐藏控制界面\n            if (!showControls) {\n                setTimeout({\n                    \"WeChatVideoHomepage.useCallback[handleVideoClick]\": ()=>{\n                        setShowControls(false);\n                    }\n                }[\"WeChatVideoHomepage.useCallback[handleVideoClick]\"], 3000);\n            }\n        }\n    }[\"WeChatVideoHomepage.useCallback[handleVideoClick]\"], [\n        showControls\n    ]);\n    // 格式化时间\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = Math.floor(seconds % 60);\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    // 视频播放控制\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeChatVideoHomepage.useEffect\": ()=>{\n            if (videoRef.current) {\n                if (isPlaying) {\n                    videoRef.current.play();\n                } else {\n                    videoRef.current.pause();\n                }\n            }\n        }\n    }[\"WeChatVideoHomepage.useEffect\"], [\n        currentIndex,\n        isPlaying\n    ]);\n    // 视频时间更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeChatVideoHomepage.useEffect\": ()=>{\n            const video = videoRef.current;\n            if (!video) return;\n            const updateTime = {\n                \"WeChatVideoHomepage.useEffect.updateTime\": ()=>{\n                    setCurrentTime(video.currentTime);\n                    setProgress(video.currentTime / video.duration * 100);\n                }\n            }[\"WeChatVideoHomepage.useEffect.updateTime\"];\n            const updateDuration = {\n                \"WeChatVideoHomepage.useEffect.updateDuration\": ()=>{\n                    setDuration(video.duration);\n                }\n            }[\"WeChatVideoHomepage.useEffect.updateDuration\"];\n            video.addEventListener('timeupdate', updateTime);\n            video.addEventListener('loadedmetadata', updateDuration);\n            return ({\n                \"WeChatVideoHomepage.useEffect\": ()=>{\n                    video.removeEventListener('timeupdate', updateTime);\n                    video.removeEventListener('loadedmetadata', updateDuration);\n                }\n            })[\"WeChatVideoHomepage.useEffect\"];\n        }\n    }[\"WeChatVideoHomepage.useEffect\"], [\n        currentIndex\n    ]);\n    // 标签切换处理函数\n    const handleTabChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleTabChange]\": (tab)=>{\n            setActiveTab(tab);\n            setCurrentIndex(0);\n            setIsLiked(false);\n            setIsFollowing(false);\n            setIsPlaying(true);\n        }\n    }[\"WeChatVideoHomepage.useCallback[handleTabChange]\"], []);\n    // 视频切换函数\n    const nextVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[nextVideo]\": ()=>{\n            setCurrentIndex({\n                \"WeChatVideoHomepage.useCallback[nextVideo]\": (prev)=>(prev + 1) % videoContent.length\n            }[\"WeChatVideoHomepage.useCallback[nextVideo]\"]);\n            setIsLiked(false);\n            setIsFollowing(false);\n            setIsPlaying(true);\n        }\n    }[\"WeChatVideoHomepage.useCallback[nextVideo]\"], [\n        videoContent.length\n    ]);\n    const prevVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[prevVideo]\": ()=>{\n            setCurrentIndex({\n                \"WeChatVideoHomepage.useCallback[prevVideo]\": (prev)=>(prev - 1 + videoContent.length) % videoContent.length\n            }[\"WeChatVideoHomepage.useCallback[prevVideo]\"]);\n            setIsLiked(false);\n            setIsFollowing(false);\n            setIsPlaying(true);\n        }\n    }[\"WeChatVideoHomepage.useCallback[prevVideo]\"], [\n        videoContent.length\n    ]);\n    // 触摸手势处理\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientY);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientY);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isUpSwipe = distance > 50;\n        const isDownSwipe = distance < -50;\n        if (isUpSwipe) {\n            nextVideo();\n        }\n        if (isDownSwipe) {\n            prevVideo();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                mode: \"wait\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        y: 100,\n                        opacity: 0\n                    },\n                    animate: {\n                        y: 0,\n                        opacity: 1\n                    },\n                    exit: {\n                        y: -100,\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.3,\n                        ease: \"easeInOut\"\n                    },\n                    className: \"absolute inset-0\",\n                    onTouchStart: handleTouchStart,\n                    onTouchMove: handleTouchMove,\n                    onTouchEnd: handleTouchEnd,\n                    onClick: handleVideoClick,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                ref: videoRef,\n                                src: currentContent.videoUrl,\n                                className: \"w-full h-full object-cover\",\n                                loop: true,\n                                muted: true,\n                                playsInline: true,\n                                autoPlay: true,\n                                onLoadedData: ()=>{\n                                    if (videoRef.current && isPlaying) {\n                                        videoRef.current.play();\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this),\n                        !isPlaying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center z-20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 bg-white/30 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-0 h-0 border-l-[20px] border-l-white border-y-[12px] border-y-transparent ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, currentIndex, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 left-0 right-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center px-4 pt-12 pb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange('推荐'),\n                                    className: \"text-lg font-medium transition-all duration-200 relative \".concat(activeTab === '推荐' ? 'text-white' : 'text-white/50'),\n                                    children: [\n                                        \"推荐\",\n                                        activeTab === '推荐' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange('关注'),\n                                    className: \"text-lg font-medium transition-all duration-200 relative \".concat(activeTab === '关注' ? 'text-white' : 'text-white/50'),\n                                    children: [\n                                        \"关注\",\n                                        activeTab === '关注' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange('朋友'),\n                                    className: \"text-lg font-medium transition-all duration-200 flex items-center space-x-1 relative \".concat(activeTab === '朋友' ? 'text-white' : 'text-white/50'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"朋友\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500/50 text-sm\",\n                                            children: \"♥\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeTab === '朋友' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute right-4 top-12 flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-8 h-8 flex items-center justify-center transition-colors duration-200 hover:bg-white/20 rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-white/50 hover:text-white transition-colors duration-200\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-8 h-8 flex items-center justify-center transition-colors duration-200 hover:bg-white/20 rounded-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-white/50 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-white/50 rounded-full mx-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-white/50 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-16 right-4 bg-black/60 backdrop-blur-sm rounded-lg px-2 py-1 z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-white text-sm font-medium\",\n                    children: currentContent.videoTime\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 295,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-20 left-0 right-0 z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 rounded-full overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-lg\",\n                                                children: currentContent.authorAvatar\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white/50 font-medium text-base\",\n                                                        children: currentContent.author\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    currentContent.verified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 bg-blue-500/50 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-xs\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/30 text-sm\",\n                                                children: currentContent.friendCount\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShare,\n                                        className: \"flex flex-col items-center transition-all duration-200 hover:scale-110\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-white/50 hover:text-white transition-colors duration-200\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/50 text-xs\",\n                                                children: formatNumber(currentContent.shares)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                        onClick: handleLike,\n                                        whileTap: {\n                                            scale: 0.8\n                                        },\n                                        className: \"flex flex-col items-center transition-all duration-200 hover:scale-110\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                    animate: isLiked ? {\n                                                        scale: [\n                                                            1,\n                                                            1.2,\n                                                            1\n                                                        ]\n                                                    } : {},\n                                                    transition: {\n                                                        duration: 0.3\n                                                    },\n                                                    children: isLiked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-red-500\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-white/50 hover:text-white transition-colors duration-200\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/50 text-xs\",\n                                                children: formatNumber(currentContent.likes + (isLiked ? 1 : 0))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleComment,\n                                        className: \"flex flex-col items-center transition-all duration-200 hover:scale-110\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-white/50 hover:text-white transition-colors duration-200\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/50 text-xs\",\n                                                children: formatNumber(currentContent.comments)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, this);\n}\n_s(WeChatVideoHomepage, \"1laIHd8pwMh+KMUxsZVA0G/K+Jc=\");\n_c = WeChatVideoHomepage;\nvar _c;\n$RefreshReg$(_c, \"WeChatVideoHomepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/wechat-video-homepage.tsx\n"));

/***/ })

});