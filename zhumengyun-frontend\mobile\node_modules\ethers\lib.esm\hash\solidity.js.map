{"version": 3, "file": "solidity.js", "sourceRoot": "", "sources": ["../../src.ts/hash/solidity.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EACH,SAAS,IAAI,UAAU,EAAE,MAAM,IAAI,OAAO,EAC7C,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EACH,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,EACjG,cAAc,EACjB,MAAM,mBAAmB,CAAC;AAG3B,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,iBAAiB,CAAC,CAAC;AACjD,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAAC,CAAC;AACpD,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAGtD,SAAS,KAAK,CAAC,IAAY,EAAE,KAAU,EAAE,OAAiB;IACtD,QAAO,IAAI,EAAE;QACT,KAAK,SAAS;YACV,IAAI,OAAO,EAAE;gBAAE,OAAO,QAAQ,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;aAAE;YAC1D,OAAO,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;QACvC,KAAK,QAAQ;YACT,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;QAC9B,KAAK,OAAO;YACR,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC3B,KAAK,MAAM;YACP,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAA,CAAC,CAAC,MAAM,CAAC,CAAC;YACnC,IAAI,OAAO,EAAE;gBAAE,OAAO,QAAQ,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;aAAE;YAC1D,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;KAC9B;IAED,IAAI,KAAK,GAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IACrC,IAAI,KAAK,EAAE;QACP,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC;QAClC,IAAI,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAA;QAEtC,cAAc,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,GAAG,EAAE,qBAAqB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAE/I,IAAI,OAAO,EAAE;YAAE,IAAI,GAAG,GAAG,CAAC;SAAE;QAE5B,IAAI,MAAM,EAAE;YAAE,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;SAAE;QAE5C,OAAO,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;KAC7D;IAED,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC/B,IAAI,KAAK,EAAE;QACP,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE,EAAE,oBAAoB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAC1G,cAAc,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,qBAAsB,IAAK,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAE1F,IAAI,OAAO,EAAE;YAAE,OAAO,QAAQ,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;SAAE;QAC1D,OAAO,KAAK,CAAC;KAChB;IAED,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC/B,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QAC/B,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QACzD,cAAc,CAAC,KAAK,KAAK,KAAK,CAAC,MAAM,EAAE,4BAA6B,IAAK,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAE7F,MAAM,MAAM,GAAsB,EAAE,CAAC;QACrC,KAAK,CAAC,OAAO,CAAC,UAAS,KAAK;YACxB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;KACnC;IAED,cAAc,CAAC,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;AACvD,CAAC;AAED,oBAAoB;AAEpB;;;;;;;;GAQG;AACH,MAAM,UAAU,cAAc,CAAC,KAA4B,EAAE,MAA0B;IACnF,cAAc,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,oDAAoD,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IAEvH,MAAM,KAAK,GAAsB,EAAE,CAAC;IACpC,KAAK,CAAC,OAAO,CAAC,UAAS,IAAI,EAAE,KAAK;QAC9B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IACH,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAClC,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,uBAAuB,CAAC,KAA4B,EAAE,MAA0B;IAC5F,OAAO,UAAU,CAAC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AACrD,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,oBAAoB,CAAC,KAA4B,EAAE,MAA0B;IACzF,OAAO,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAClD,CAAC"}