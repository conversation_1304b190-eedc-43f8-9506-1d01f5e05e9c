/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_JOINT_SPACE } from '../private.js';
import { XRSpace } from './XRSpace.js';
export class XRJointSpace extends XRSpace {
    constructor(jointName, parentSpace, offsetMatrix) {
        super(parentSpace, offsetMatrix);
        this[P_JOINT_SPACE] = { jointName, radius: 0 };
    }
    get jointName() {
        return this[P_JOINT_SPACE].jointName;
    }
}
//# sourceMappingURL=XRJointSpace.js.map