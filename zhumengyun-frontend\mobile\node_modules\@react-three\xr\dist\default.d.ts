import { DefaultXRControllerOptions, DefaultXRGazeOptions, DefaultXRHandOptions, DefaultXRHandTouchPointerOptions, DefaultXRInputSourceGrabPointerOptions, DefaultXRInputSourceRayPointerOptions, DefaultXRInputSourceTeleportPointerOptions, DefaultXRScreenInputOptions, DefaultXRTransientPointerOptions } from '@pmndrs/xr/internals';
import { ReactNode } from 'react';
export { defaultGrabPointerOpacity, defaultRayPointerOpacity, defaultTouchPointerOpacity, type DefaultXRControllerOptions, type DefaultXRGazeOptions, type DefaultXRHandOptions, type DefaultXRHandTouchPointerOptions, type DefaultXRInputSourceGrabPointerOptions, type DefaultXRInputSourceRayPointerOptions, type DefaultXRScreenInputOptions, type DefaultXRTransientPointerOptions, } from '@pmndrs/xr/internals';
/**
 * Grab pointer for the XRHand
 *
 * @param {DefaultXRInputSourceGrabPointerOptions} props
 * #### `clickThresholdMs` - Time in milliseconds between `pointerdown` and `pointerup` to trigger a click event
 * #### `dblClickThresholdMs` - Time in milliseconds between the first click and the second click to trigger a `dblclick` event
 * #### `contextMenuButton` - The button that triggers context menu events
 * #### `makeDefault` - Used the set the default pointer inside a combined pointer
 * #### `cursorModel` - Properties for configuring how the cursor should look
 * #### `radius` - The size of the intersection sphere
 * #### `customSort` - Overrides the default sort function to use for sorting the intersection results
 */
export declare const DefaultXRHandGrabPointer: (props: DefaultXRInputSourceGrabPointerOptions) => ReactNode;
/**
 * Grab pointer for the XRController
 *
 * @param {DefaultXRInputSourceGrabPointerOptions} props
 * #### `clickThresholdMs` - Time in milliseconds between `pointerdown` and `pointerup` to trigger a click event
 * #### `dblClickThresholdMs` - Time in milliseconds between the first click and the second click to trigger a `dblclick` event
 * #### `contextMenuButton` - The button that triggers context menu events
 * #### `makeDefault` - Used the set the default pointer inside a combined pointer
 * #### `cursorModel` - Properties for configuring how the cursor should look
 * #### `radius` - The size of the intersection sphere
 * #### `customSort` - Overrides the default sort function to use for sorting the intersection results
 */
export declare const DefaultXRControllerGrabPointer: (props: DefaultXRInputSourceGrabPointerOptions) => ReactNode;
/**
 * Ray pointer for the XRInputSource
 *
 * @param {DefaultXRInputSourceRayPointerOptions} props
 * #### `clickThresholdMs` - Time in milliseconds between pointerdown and pointerup to trigger a click event
 * #### `dblClickThresholdMs` - Time in milliseconds between the first click and the second click to trigger a dblclick event
 * #### `contextMenuButton` - The button that triggers contextmenu events
 * #### `makeDefault` - Used the set the default pointer inside a combined pointer
 * #### `radius` - The size of the intersection sphere
 * #### `minDistance` - Minimal distance to trigger interactions
 * #### `linePoints` - The points thay make up the shape of the ray if undefined the ray goes in a straight line
 * #### `direction` - The direction of the ray
 * #### `rayModel` - Properties for configuring how the ray should look
 * #### `cursorModel` - Properties for configuring how the cursor should look
 */
export declare function DefaultXRInputSourceRayPointer(props: DefaultXRInputSourceRayPointerOptions): import("react/jsx-runtime").JSX.Element;
/**
 * Touch pointer for the XRHand
 *
 * @param {DefaultXRHandTouchPointerOptions} props
 * #### `clickThresholdMs` - Time in milliseconds between `pointerdown` and `pointerup` to trigger a click event
 * #### `dblClickThresholdMs` - Time in milliseconds between the first click and the second click to trigger a `dblclick` event
 * #### `contextMenuButton` - The button that triggers context menu events
 * #### `makeDefault` - Used the set the default pointer inside a combined pointer
 * #### `cursorModel` - Properties for configuring how the cursor should look
 * #### `hoverRadius` - The size of the intersection sphere
 * #### `downRadius` - The distance to the touch center to trigger a `pointerdown` event
 * #### `button` - The id of the button that is triggered when touching
 */
export declare function DefaultXRHandTouchPointer(props: DefaultXRHandTouchPointerOptions): import("react/jsx-runtime").JSX.Element;
/**
 * Default controller implementation with grab and ray pointers
 *
 * @param {DefaultXRControllerOptions} props
 * #### `model` - Options for configuring the controller apperance
 * #### `grabPointer` - Options for configuring the grab pointer
 * #### `rayPointer` - Options for configuring the ray pointer
 */
export declare function DefaultXRController(props: DefaultXRControllerOptions): import("react/jsx-runtime").JSX.Element;
/**
 * Default hand implementation with touch, grab and ray pointers
 *
 * @param {DefaultXRHandOptions} props
 * #### `model` - Options for configuring the hand appearance
 * #### `grabPointer` - Options for configuring the grab pointer
 * #### `rayPointer` - Options for configuring the ray pointer
 * #### `touchPointer` - Options for configuring the touch pointer
 */
export declare function DefaultXRHand(props: DefaultXRHandOptions): import("react/jsx-runtime").JSX.Element;
/**
 * Default transient-pointer implementation with ray pointer
 *
 * @param {DefaultXRTransientPointerOptions} props
 * #### `clickThresholdMs` - Time in milliseconds between `pointerdown` and `pointerup` to trigger a click event
 * #### `dblClickThresholdMs` - Time in milliseconds between the first click and the second click to trigger a `dblclick` event
 * #### `contextMenuButton` - The button that triggers context menu events
 * #### `minDistance` - Minimal distance to trigger interactions
 * #### `linePoints` - The points thay make up the shape of the ray if undefined the ray goes in a straight line
 * #### `direction` - The direction of the ray
 * #### `cursorModel` - Properties for configuring how the cursor should look
 */
export declare function DefaultXRTransientPointer(props: DefaultXRTransientPointerOptions): import("react/jsx-runtime").JSX.Element;
/**
 * Default gaze implementation with ray pointer
 *
 * @param {DefaultXRGazeOptions} props
 * #### `clickThresholdMs` - Time in milliseconds between `pointerdown` and `pointerup` to trigger a click event
 * #### `dblClickThresholdMs` - Time in milliseconds between the first click and the second click to trigger a `dblclick` event
 * #### `contextMenuButton` - The button that triggers context menu events
 * #### `minDistance` - Minimal distance to trigger interactions
 * #### `linePoints` - The points thay make up the shape of the ray if undefined the ray goes in a straight line
 * #### `direction` - The direction of the ray
 * #### `cursorModel` - Properties for configuring how the cursor should look
 */
export declare function DefaultXRGaze(props: DefaultXRGazeOptions): import("react/jsx-runtime").JSX.Element;
/**
 * Default screen-input implementation with ray pointer
 *
 * @param {DefaultXRScreenInputOptions} props
 * #### `clickThresholdMs` - Time in milliseconds between `pointerdown` and `pointerup` to trigger a click event
 * #### `dblClickThresholdMs` - Time in milliseconds between the first click and the second click to trigger a `dblclick` event
 * #### `contextMenuButton` - The button that triggers context menu events
 * #### `minDistance` - Minimal distance to trigger interactions
 * #### `linePoints` - The points thay make up the shape of the ray if undefined the ray goes in a straight line
 * #### `direction` - The direction of the ray
 */
export declare function DefaultXRScreenInput(props: DefaultXRScreenInputOptions): import("react/jsx-runtime").JSX.Element;
/**
 * Telport pointer for the XRInputSource.
 * Emits a downward bend ray that only interesects with meshes marked as teleportable
 *
 * @param {DefaultXRInputSourceTeleportPointerOptions} props
 * #### `clickThresholdMs` - Time in milliseconds between `pointerdown` and `pointerup` to trigger a click event
 * #### `dblClickThresholdMs` - Time in milliseconds between the first click and the second click to trigger a `dblclick` event
 * #### `contextMenuButton` - The button that triggers context menu events
 * #### `makeDefault` - Used the set the default pointer inside a combined pointer
 * #### `radius` - The size of the intersection sphere
 * #### `minDistance` - Minimal distance to trigger interactions
 * #### `direction` - The direction of the ray
 * #### `rayModel` - Properties for configuring how the ray should look
 * #### `cursorModel` - Properties for configuring how the cursor should look
 */
export declare function DefaultXRInputSourceTeleportPointer(props: DefaultXRInputSourceTeleportPointerOptions): import("react/jsx-runtime").JSX.Element;
