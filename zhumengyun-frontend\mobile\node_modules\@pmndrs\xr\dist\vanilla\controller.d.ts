import { Object3D } from 'three';
import { XRControllerModelOptions } from '../controller/model.js';
import { XRControllerState } from '../input.js';
/**
 * Component for rendering a 3D model for the XRController
 * @param state: XRControllerState
 * @param options: XRControllerModelOptions
 */
export declare class XRControllerModel extends Object3D {
    constructor(state: XRControllerState, options?: XRControllerModelOptions);
}
