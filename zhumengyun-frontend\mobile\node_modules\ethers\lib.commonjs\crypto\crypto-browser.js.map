{"version": 3, "file": "crypto-browser.js", "sourceRoot": "", "sources": ["../../src.ts/crypto/crypto-browser.ts"], "names": [], "mappings": ";AAAA,0BAA0B;;;AAE1B,6CAA0C;AAC1C,iDAA8C;AAC9C,iDAA8C;AAC9C,iDAA8C;AAE9C,gDAA2D;AAW3D,SAAS,SAAS;IAChB,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;QAAE,OAAO,IAAI,CAAC;KAAE;IACjD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QAAE,OAAO,MAAM,CAAC;KAAE;IACrD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QAAE,OAAO,MAAM,CAAC;KAAE;IACrD,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;AACpD,CAAC;AAAA,CAAC;AAEF,MAAM,SAAS,GAAG,SAAS,EAAE,CAAC;AAC9B,MAAM,MAAM,GAAQ,SAAS,CAAC,MAAM,IAAI,SAAS,CAAC,QAAQ,CAAC;AAQ3D,SAAgB,UAAU,CAAC,IAAY;IACnC,QAAQ,IAAI,EAAE;QACV,KAAK,QAAQ,CAAC,CAAC,OAAO,eAAM,CAAC,MAAM,EAAE,CAAC;QACtC,KAAK,QAAQ,CAAC,CAAC,OAAO,eAAM,CAAC,MAAM,EAAE,CAAC;KACzC;IACD,IAAA,yBAAc,EAAC,KAAK,EAAE,gCAAgC,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AAC/E,CAAC;AAND,gCAMC;AAED,SAAgB,UAAU,CAAC,KAAa,EAAE,GAAe;IACrD,MAAM,IAAI,GAAG,CAAC,EAAE,MAAM,EAAN,eAAM,EAAE,MAAM,EAAN,eAAM,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACzC,IAAA,yBAAc,EAAC,IAAI,IAAI,IAAI,EAAE,wBAAwB,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;IAC3E,OAAO,WAAI,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAClC,CAAC;AAJD,gCAIC;AAED,SAAgB,UAAU,CAAC,QAAoB,EAAE,IAAgB,EAAE,UAAkB,EAAE,MAAc,EAAE,KAA0B;IAC7H,MAAM,IAAI,GAAG,CAAC,EAAE,MAAM,EAAN,eAAM,EAAE,MAAM,EAAN,eAAM,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACzC,IAAA,yBAAc,EAAC,IAAI,IAAI,IAAI,EAAE,0BAA0B,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;IAC7E,OAAO,IAAA,eAAM,EAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;AAC1E,CAAC;AAJD,gCAIC;AAED,SAAgB,WAAW,CAAC,MAAc;IACtC,IAAA,iBAAM,EAAC,MAAM,IAAI,IAAI,EAAE,iDAAiD,EAAE,uBAAuB,EAAE;QAC/F,SAAS,EAAE,aAAa;KAAE,CAAC,CAAC;IAEhC,IAAA,yBAAc,EAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,IAAI,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IAE7G,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IACtC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;AAClB,CAAC;AATD,kCASC"}