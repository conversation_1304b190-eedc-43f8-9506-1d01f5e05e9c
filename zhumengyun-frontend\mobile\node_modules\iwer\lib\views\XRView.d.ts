/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_VIEW } from '../private.js';
import { XRRigidTransform } from '../primitives/XRRigidTransform.js';
import { XRSession } from '../session/XRSession.js';
export declare enum XREye {
    None = "none",
    Left = "left",
    Right = "right"
}
export declare class XRView {
    [P_VIEW]: {
        eye: XREye;
        projectionMatrix: Float32Array;
        transform: XRRigidTransform;
        recommendedViewportScale: number | null;
        requestedViewportScale: number;
        session: XRSession;
    };
    constructor(eye: XREye, projectionMatrix: Float32Array, transform: XRRigidTransform, session: XRSession);
    get eye(): XREye;
    get projectionMatrix(): Float32Array;
    get transform(): XRRigidTransform;
    get recommendedViewportScale(): number | null;
    requestViewportScale(scale: number | null): void;
}
//# sourceMappingURL=XRView.d.ts.map