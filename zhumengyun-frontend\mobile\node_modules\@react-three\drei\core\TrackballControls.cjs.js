"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("@react-three/fiber"),r=require("react"),n=require("three-stdlib");function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function a(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var s=o(e),c=a(r);const u=c.forwardRef((({makeDefault:e,camera:r,domElement:o,regress:a,onChange:u,onStart:i,onEnd:f,...l},d)=>{const{invalidate:v,camera:m,gl:b,events:E,set:p,get:g,performance:h,viewport:j}=t.useThree(),O=r||m,L=o||E.connected||b.domElement,y=c.useMemo((()=>new n.TrackballControls(O)),[O]);return t.useFrame((()=>{y.enabled&&y.update()}),-1),c.useEffect((()=>(y.connect(L),()=>{y.dispose()})),[L,a,y,v]),c.useEffect((()=>{const e=e=>{v(),a&&h.regress(),u&&u(e)};return y.addEventListener("change",e),i&&y.addEventListener("start",i),f&&y.addEventListener("end",f),()=>{i&&y.removeEventListener("start",i),f&&y.removeEventListener("end",f),y.removeEventListener("change",e)}}),[u,i,f,y,v]),c.useEffect((()=>{y.handleResize()}),[j]),c.useEffect((()=>{if(e){const e=g().controls;return p({controls:y}),()=>p({controls:e})}}),[e,y]),c.createElement("primitive",s.default({ref:d,object:y},l))}));exports.TrackballControls=u;
