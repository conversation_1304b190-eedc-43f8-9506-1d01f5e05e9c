{"name": "react-devtools-core", "version": "6.1.5", "description": "Use react-devtools outside of the browser", "license": "MIT", "main": "./dist/backend.js", "repository": {"type": "git", "url": "https://github.com/facebook/react.git", "directory": "packages/react-devtools-core"}, "files": ["dist", "backend.js", "standalone.js"], "scripts": {"build": "yarn build:backend && yarn build:standalone", "build:backend": "cross-env NODE_ENV=production webpack --config webpack.backend.js", "build:backend:fb": "cross-env NODE_ENV=production FEATURE_FLAG_TARGET=core/backend-fb webpack --config webpack.backend.js", "build:standalone": "cross-env NODE_ENV=production webpack --config webpack.standalone.js", "build:standalone:fb": "cross-env NODE_ENV=production FEATURE_FLAG_TARGET=core/standalone-fb webpack --config webpack.standalone.js", "prepublish": "yarn run build", "start:backend": "cross-env NODE_ENV=development webpack --config webpack.backend.js --watch", "start:standalone": "cross-env NODE_ENV=development webpack --config webpack.standalone.js --watch"}, "dependencies": {"shell-quote": "^1.6.1", "ws": "^7"}, "devDependencies": {"cross-env": "^3.1.4", "process": "0.11.10", "webpack": "^5.82.1", "webpack-cli": "^5.1.1", "workerize-loader": "^2.0.2"}}