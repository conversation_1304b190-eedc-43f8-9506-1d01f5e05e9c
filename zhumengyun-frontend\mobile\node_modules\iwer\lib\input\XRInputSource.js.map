{"version": 3, "file": "XRInputSource.js", "sourceRoot": "", "sources": ["../../src/input/XRInputSource.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAGH,OAAO,EAAE,cAAc,EAAE,MAAM,eAAe,CAAC;AAI/C,MAAM,CAAN,IAAY,YAIX;AAJD,WAAY,YAAY;IACvB,6BAAa,CAAA;IACb,6BAAa,CAAA;IACb,+BAAe,CAAA;AAChB,CAAC,EAJW,YAAY,KAAZ,YAAY,QAIvB;AAED,MAAM,CAAN,IAAY,eAKX;AALD,WAAY,eAAe;IAC1B,gCAAa,CAAA;IACb,qDAAkC,CAAA;IAClC,oCAAiB,CAAA;IACjB,yDAAsC,CAAA;AACvC,CAAC,EALW,eAAe,KAAf,eAAe,QAK1B;AAED,MAAM,OAAO,kBAAmB,SAAQ,KAAoB;CAAG;AAE/D,MAAM,OAAO,aAAa;IAWzB,YACC,UAAwB,EACxB,aAA8B,EAC9B,QAAkB,EAClB,cAAuB,EACvB,OAAiB,EACjB,SAAmB,EACnB,IAAa;QAEb,IAAI,CAAC,cAAc,CAAC,GAAG;YACtB,UAAU;YACV,aAAa;YACb,cAAc;YACd,SAAS;YACT,QAAQ;YACR,OAAO;YACP,IAAI;SACJ,CAAC;IACH,CAAC;IAED,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC;IACxC,CAAC;IAED,IAAI,aAAa;QAChB,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,aAAa,CAAC;IAC3C,CAAC;IAED,IAAI,cAAc;QACjB,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,cAAc,CAAC;IAC5C,CAAC;IAED,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC;IACvC,CAAC;IAED,IAAI,QAAQ;QACX,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC;IACtC,CAAC;IAED,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC;IACrC,CAAC;IAED,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC;IAClC,CAAC;CACD"}