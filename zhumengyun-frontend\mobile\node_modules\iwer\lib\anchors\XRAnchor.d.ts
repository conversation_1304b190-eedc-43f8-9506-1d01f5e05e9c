/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_ANCHOR } from '../private.js';
import { XRSession } from '../session/XRSession.js';
import { XRSpace } from '../spaces/XRSpace.js';
export declare class XRAnchor {
    [P_ANCHOR]: {
        anchorSpace: XRSpace | null;
        session: XRSession;
        deleted: boolean;
    };
    constructor(anchorSpace: XRSpace, session: XRSession);
    get anchorSpace(): XRSpace;
    requestPersistentHandle(): Promise<string>;
    delete(): void;
}
export declare class XRAnchorSet extends Set<XRAnchor> {
}
export declare class XRAnchorUtils {
    static recoverPersistentAnchorsFromStorage(session: XRSession): void;
    static createPersistentAnchor(session: XRSession, anchor: XRAnchor, uuid: string): void;
}
//# sourceMappingURL=XRAnchor.d.ts.map