import { MOUSE, Vector3, PerspectiveCamera, OrthographicCamera } from 'three';
import { EventDispatcher } from './EventDispatcher';
import { StandardControlsEventMap } from './StandardControlsEventMap';
declare class TrackballControls extends EventDispatcher<StandardControlsEventMap> {
    enabled: boolean;
    screen: {
        left: number;
        top: number;
        width: number;
        height: number;
    };
    rotateSpeed: number;
    zoomSpeed: number;
    panSpeed: number;
    noRotate: boolean;
    noZoom: boolean;
    noPan: boolean;
    staticMoving: boolean;
    dynamicDampingFactor: number;
    minDistance: number;
    maxDistance: number;
    keys: [string, string, string];
    mouseButtons: {
        LEFT: MOUSE;
        MIDDLE: MOUSE;
        RIGHT: MOUSE;
    };
    object: PerspectiveCamera | OrthographicCamera;
    domElement: HTMLElement | undefined;
    cursorZoom: boolean;
    readonly target: Vector3;
    private mousePosition;
    private STATE;
    private EPS;
    private lastZoom;
    private lastPosition;
    private cursorVector;
    private targetVector;
    private _state;
    private _keyState;
    private _eye;
    private _movePrev;
    private _moveCurr;
    private _lastAxis;
    private _lastAngle;
    private _zoomStart;
    private _zoomEnd;
    private _touchZoomDistanceStart;
    private _touchZoomDistanceEnd;
    private _panStart;
    private _panEnd;
    private target0;
    private position0;
    private up0;
    private zoom0;
    private changeEvent;
    private startEvent;
    private endEvent;
    constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement);
    private onScreenVector;
    private getMouseOnScreen;
    private onCircleVector;
    private getMouseOnCircle;
    private axis;
    private quaternion;
    private eyeDirection;
    private objectUpDirection;
    private objectSidewaysDirection;
    private moveDirection;
    private angle;
    private rotateCamera;
    private zoomCamera;
    private mouseChange;
    private objectUp;
    private pan;
    private panCamera;
    private checkDistances;
    handleResize: () => void;
    update: () => void;
    reset: () => void;
    private keydown;
    private onPointerDown;
    private onPointerMove;
    private onPointerUp;
    private keyup;
    private onMouseDown;
    private onMouseMove;
    private onMouseUp;
    private mousewheel;
    private touchstart;
    private touchmove;
    private touchend;
    private contextmenu;
    connect: (domElement: HTMLElement) => void;
    dispose: () => void;
}
export { TrackballControls };
