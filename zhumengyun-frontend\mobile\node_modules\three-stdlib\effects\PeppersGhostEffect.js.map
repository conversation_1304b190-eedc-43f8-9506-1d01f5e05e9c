{"version": 3, "file": "PeppersGhostEffect.js", "sources": ["../../src/effects/PeppersGhostEffect.js"], "sourcesContent": ["import { Perspective<PERSON>amera, Quaternion, Vector3 } from 'three'\n\n/**\n * peppers ghost effect based on http://www.instructables.com/id/Reflective-Prism/?ALLSTEPS\n */\n\nclass PeppersGhostEffect {\n  constructor(renderer) {\n    const scope = this\n\n    scope.cameraDistance = 15\n    scope.reflectFromAbove = false\n\n    // Internals\n    let _halfWidth, _width, _height\n\n    const _cameraF = new PerspectiveCamera() //front\n    const _cameraB = new PerspectiveCamera() //back\n    const _cameraL = new PerspectiveCamera() //left\n    const _cameraR = new PerspectiveCamera() //right\n\n    const _position = new Vector3()\n    const _quaternion = new Quaternion()\n    const _scale = new Vector3()\n\n    // Initialization\n    renderer.autoClear = false\n\n    this.setSize = function (width, height) {\n      _halfWidth = width / 2\n      if (width < height) {\n        _width = width / 3\n        _height = width / 3\n      } else {\n        _width = height / 3\n        _height = height / 3\n      }\n\n      renderer.setSize(width, height)\n    }\n\n    this.render = function (scene, camera) {\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld()\n\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld()\n\n      camera.matrixWorld.decompose(_position, _quaternion, _scale)\n\n      // front\n      _cameraF.position.copy(_position)\n      _cameraF.quaternion.copy(_quaternion)\n      _cameraF.translateZ(scope.cameraDistance)\n      _cameraF.lookAt(scene.position)\n\n      // back\n      _cameraB.position.copy(_position)\n      _cameraB.quaternion.copy(_quaternion)\n      _cameraB.translateZ(-scope.cameraDistance)\n      _cameraB.lookAt(scene.position)\n      _cameraB.rotation.z += 180 * (Math.PI / 180)\n\n      // left\n      _cameraL.position.copy(_position)\n      _cameraL.quaternion.copy(_quaternion)\n      _cameraL.translateX(-scope.cameraDistance)\n      _cameraL.lookAt(scene.position)\n      _cameraL.rotation.x += 90 * (Math.PI / 180)\n\n      // right\n      _cameraR.position.copy(_position)\n      _cameraR.quaternion.copy(_quaternion)\n      _cameraR.translateX(scope.cameraDistance)\n      _cameraR.lookAt(scene.position)\n      _cameraR.rotation.x += 90 * (Math.PI / 180)\n\n      renderer.clear()\n      renderer.setScissorTest(true)\n\n      renderer.setScissor(_halfWidth - _width / 2, _height * 2, _width, _height)\n      renderer.setViewport(_halfWidth - _width / 2, _height * 2, _width, _height)\n\n      if (scope.reflectFromAbove) {\n        renderer.render(scene, _cameraB)\n      } else {\n        renderer.render(scene, _cameraF)\n      }\n\n      renderer.setScissor(_halfWidth - _width / 2, 0, _width, _height)\n      renderer.setViewport(_halfWidth - _width / 2, 0, _width, _height)\n\n      if (scope.reflectFromAbove) {\n        renderer.render(scene, _cameraF)\n      } else {\n        renderer.render(scene, _cameraB)\n      }\n\n      renderer.setScissor(_halfWidth - _width / 2 - _width, _height, _width, _height)\n      renderer.setViewport(_halfWidth - _width / 2 - _width, _height, _width, _height)\n\n      if (scope.reflectFromAbove) {\n        renderer.render(scene, _cameraR)\n      } else {\n        renderer.render(scene, _cameraL)\n      }\n\n      renderer.setScissor(_halfWidth + _width / 2, _height, _width, _height)\n      renderer.setViewport(_halfWidth + _width / 2, _height, _width, _height)\n\n      if (scope.reflectFromAbove) {\n        renderer.render(scene, _cameraL)\n      } else {\n        renderer.render(scene, _cameraR)\n      }\n\n      renderer.setScissorTest(false)\n    }\n  }\n}\n\nexport { PeppersGhostEffect }\n"], "names": [], "mappings": ";AAMA,MAAM,mBAAmB;AAAA,EACvB,YAAY,UAAU;AACpB,UAAM,QAAQ;AAEd,UAAM,iBAAiB;AACvB,UAAM,mBAAmB;AAGzB,QAAI,YAAY,QAAQ;AAExB,UAAM,WAAW,IAAI,kBAAmB;AACxC,UAAM,WAAW,IAAI,kBAAmB;AACxC,UAAM,WAAW,IAAI,kBAAmB;AACxC,UAAM,WAAW,IAAI,kBAAmB;AAExC,UAAM,YAAY,IAAI,QAAS;AAC/B,UAAM,cAAc,IAAI,WAAY;AACpC,UAAM,SAAS,IAAI,QAAS;AAG5B,aAAS,YAAY;AAErB,SAAK,UAAU,SAAU,OAAO,QAAQ;AACtC,mBAAa,QAAQ;AACrB,UAAI,QAAQ,QAAQ;AAClB,iBAAS,QAAQ;AACjB,kBAAU,QAAQ;AAAA,MAC1B,OAAa;AACL,iBAAS,SAAS;AAClB,kBAAU,SAAS;AAAA,MACpB;AAED,eAAS,QAAQ,OAAO,MAAM;AAAA,IAC/B;AAED,SAAK,SAAS,SAAU,OAAO,QAAQ;AACrC,UAAI,MAAM,0BAA0B;AAAM,cAAM,kBAAmB;AAEnE,UAAI,OAAO,WAAW,QAAQ,OAAO,0BAA0B;AAAM,eAAO,kBAAmB;AAE/F,aAAO,YAAY,UAAU,WAAW,aAAa,MAAM;AAG3D,eAAS,SAAS,KAAK,SAAS;AAChC,eAAS,WAAW,KAAK,WAAW;AACpC,eAAS,WAAW,MAAM,cAAc;AACxC,eAAS,OAAO,MAAM,QAAQ;AAG9B,eAAS,SAAS,KAAK,SAAS;AAChC,eAAS,WAAW,KAAK,WAAW;AACpC,eAAS,WAAW,CAAC,MAAM,cAAc;AACzC,eAAS,OAAO,MAAM,QAAQ;AAC9B,eAAS,SAAS,KAAK,OAAO,KAAK,KAAK;AAGxC,eAAS,SAAS,KAAK,SAAS;AAChC,eAAS,WAAW,KAAK,WAAW;AACpC,eAAS,WAAW,CAAC,MAAM,cAAc;AACzC,eAAS,OAAO,MAAM,QAAQ;AAC9B,eAAS,SAAS,KAAK,MAAM,KAAK,KAAK;AAGvC,eAAS,SAAS,KAAK,SAAS;AAChC,eAAS,WAAW,KAAK,WAAW;AACpC,eAAS,WAAW,MAAM,cAAc;AACxC,eAAS,OAAO,MAAM,QAAQ;AAC9B,eAAS,SAAS,KAAK,MAAM,KAAK,KAAK;AAEvC,eAAS,MAAO;AAChB,eAAS,eAAe,IAAI;AAE5B,eAAS,WAAW,aAAa,SAAS,GAAG,UAAU,GAAG,QAAQ,OAAO;AACzE,eAAS,YAAY,aAAa,SAAS,GAAG,UAAU,GAAG,QAAQ,OAAO;AAE1E,UAAI,MAAM,kBAAkB;AAC1B,iBAAS,OAAO,OAAO,QAAQ;AAAA,MACvC,OAAa;AACL,iBAAS,OAAO,OAAO,QAAQ;AAAA,MAChC;AAED,eAAS,WAAW,aAAa,SAAS,GAAG,GAAG,QAAQ,OAAO;AAC/D,eAAS,YAAY,aAAa,SAAS,GAAG,GAAG,QAAQ,OAAO;AAEhE,UAAI,MAAM,kBAAkB;AAC1B,iBAAS,OAAO,OAAO,QAAQ;AAAA,MACvC,OAAa;AACL,iBAAS,OAAO,OAAO,QAAQ;AAAA,MAChC;AAED,eAAS,WAAW,aAAa,SAAS,IAAI,QAAQ,SAAS,QAAQ,OAAO;AAC9E,eAAS,YAAY,aAAa,SAAS,IAAI,QAAQ,SAAS,QAAQ,OAAO;AAE/E,UAAI,MAAM,kBAAkB;AAC1B,iBAAS,OAAO,OAAO,QAAQ;AAAA,MACvC,OAAa;AACL,iBAAS,OAAO,OAAO,QAAQ;AAAA,MAChC;AAED,eAAS,WAAW,aAAa,SAAS,GAAG,SAAS,QAAQ,OAAO;AACrE,eAAS,YAAY,aAAa,SAAS,GAAG,SAAS,QAAQ,OAAO;AAEtE,UAAI,MAAM,kBAAkB;AAC1B,iBAAS,OAAO,OAAO,QAAQ;AAAA,MACvC,OAAa;AACL,iBAAS,OAAO,OAAO,QAAQ;AAAA,MAChC;AAED,eAAS,eAAe,KAAK;AAAA,IAC9B;AAAA,EACF;AACH;"}