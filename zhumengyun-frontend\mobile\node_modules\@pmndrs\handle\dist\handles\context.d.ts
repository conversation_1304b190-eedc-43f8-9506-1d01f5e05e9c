import { PointerEventsMap } from '@pmndrs/pointer-events';
import { Object3D, Object3DEventMap } from 'three';
import { HandleState } from '../state.js';
import { HandleOptions, HandleStore } from '../store.js';
import { TransformHandlesSpace } from './index.js';
export declare class HandlesContext {
    readonly target: Object3D | {
        current?: Object3D | null;
    };
    private readonly getOptions?;
    private handles;
    private hoveredTagMap;
    private hoverSubscriptions;
    private applySubscriptions;
    space?: TransformHandlesSpace;
    constructor(target: Object3D | {
        current?: Object3D | null;
    }, getOptions?: (() => HandleOptions<unknown>) | undefined);
    getSpace(): TransformHandlesSpace;
    getTarget(): Object3D<Object3DEventMap> | null | undefined;
    getHandleOptions<T>(tag: string, getOverrideOptions?: () => HandleOptions<unknown>): HandleOptions<T>;
    registerHandle(store: HandleStore<unknown>, object: Object3D<PointerEventsMap & Object3DEventMap>, tag: string): () => void;
    subscribeHover(fn: (tags: Array<string>) => void): () => void;
    subscribeApply(fn: (tag: string, state: HandleState<unknown>, target: Object3D) => void): () => void;
    update(time: number): void;
    private onPointerEnter;
    private onPointerLeave;
    private updateHover;
    private onApply;
}
