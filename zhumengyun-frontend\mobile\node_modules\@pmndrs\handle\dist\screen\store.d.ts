import { Scene } from 'three';
export declare class ScreenHandleStore<T = unknown> {
    private apply;
    private getInitial;
    private map;
    private initial;
    constructor(apply: (initial: T, map: ScreenHandleStore['map']) => void, getInitial: () => T);
    bind(scene: Scene): () => void;
    private onPointerDown;
    private onPointerUp;
    private onPointerMove;
    private save;
    update(): void;
}
