/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<4a42f9a48ac886ab00bb7483296a1c07>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Utilities/useWindowDimensions.js
 */

import { type DisplayMetrics, type DisplayMetricsAndroid } from "./NativeDeviceInfo";
declare function useWindowDimensions(): DisplayMetrics | DisplayMetricsAndroid;
export default useWindowDimensions;
