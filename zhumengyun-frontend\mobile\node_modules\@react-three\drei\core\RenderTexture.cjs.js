"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("three"),t=require("react"),n=require("@react-three/fiber"),a=require("./Fbo.cjs.js");function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function l(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var u=i(e),s=l(r),c=l(t);const o=c.forwardRef((({children:e,compute:r,width:t,height:i,samples:l=8,renderPriority:o=0,eventPriority:d=0,frames:g=1/0,stencilBuffer:p=!1,depthBuffer:m=!0,generateMipmaps:b=!1,...v},h)=>{const{size:x,viewport:y}=n.useThree(),j=a.useFBO((t||x.width)*y.dpr,(i||x.height)*y.dpr,{samples:l,stencilBuffer:p,depthBuffer:m,generateMipmaps:b}),[P]=c.useState((()=>new s.Scene)),O=c.useCallback(((e,r,t)=>{var n,a;let i=null==(n=j.texture)?void 0:n.__r3f.parent;for(;i&&!(i instanceof s.Object3D);)i=i.__r3f.parent;if(!i)return!1;t.raycaster.camera||t.events.compute(e,t,null==(a=t.previousRoot)?void 0:a.getState());const[l]=t.raycaster.intersectObject(i);if(!l)return!1;const u=l.uv;if(!u)return!1;r.raycaster.setFromCamera(r.pointer.set(2*u.x-1,2*u.y-1),r.camera)}),[]);return c.useImperativeHandle(h,(()=>j.texture),[j]),c.createElement(c.Fragment,null,n.createPortal(c.createElement(f,{renderPriority:o,frames:g,fbo:j},e,c.createElement("group",{onPointerOver:()=>null})),P,{events:{compute:r||O,priority:d}}),c.createElement("primitive",u.default({object:j.texture},v)))}));function f({frames:e,renderPriority:r,children:t,fbo:a}){let i,l,u,s,o=0;return n.useFrame((r=>{(e===1/0||o<e)&&(i=r.gl.autoClear,l=r.gl.xr.enabled,u=r.gl.getRenderTarget(),s=r.gl.xr.isPresenting,r.gl.autoClear=!0,r.gl.xr.enabled=!1,r.gl.xr.isPresenting=!1,r.gl.setRenderTarget(a),r.gl.render(r.scene,r.camera),r.gl.setRenderTarget(u),r.gl.autoClear=i,r.gl.xr.enabled=l,r.gl.xr.isPresenting=s,o++)}),r),c.createElement(c.Fragment,null,t)}exports.RenderTexture=o;
