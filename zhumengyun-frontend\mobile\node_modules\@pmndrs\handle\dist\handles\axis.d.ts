import { <PERSON><PERSON>erG<PERSON><PERSON>, Euler, LineBasicMaterial, LineSegments } from 'three';
import { HandlesContext } from './context.js';
export declare class HandlesAxisHighlight extends LineSegments<BufferGeometry, LineBasicMaterial> {
    private readonly context;
    private readonly rotationOffset;
    constructor(context: HandlesContext, rotationOffset: Euler);
    update(): void;
    bind(tag: string): () => void;
}
