/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<ad5431d38b19532302ed06825df456ad>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Animated/components/AnimatedScrollView.js
 */

import type { AnimatedComponentType } from "../createAnimatedComponent";
import ScrollView from "../../Components/ScrollView/ScrollView";
import * as React from "react";
type AnimatedScrollViewProps = React.JSX.LibraryManagedAttributes<typeof ScrollView, React.ComponentProps<typeof ScrollView>>;
type AnimatedScrollViewInstance = React.ComponentRef<typeof ScrollView>;
declare const AnimatedScrollView: AnimatedComponentType<AnimatedScrollViewProps, AnimatedScrollViewInstance>;
/**
 * @see https://github.com/facebook/react-native/commit/b8c8562
 */
declare const $$AnimatedScrollView: typeof AnimatedScrollView;
declare type $$AnimatedScrollView = typeof $$AnimatedScrollView;
export default $$AnimatedScrollView;
