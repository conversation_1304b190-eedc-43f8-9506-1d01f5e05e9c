{"version": 3, "file": "OBJExporter.js", "sources": ["../../src/exporters/OBJExporter.ts"], "sourcesContent": ["import { BufferAttribute, Color, Line, Matrix3, Mesh, Object3D, Points, Vector2, Vector3 } from 'three'\n\nclass OBJExporter {\n  private output\n\n  private indexVertex\n  private indexVertexUvs\n  private indexNormals\n\n  private vertex\n  private color\n  private normal\n  private uv\n\n  private face: string[]\n\n  constructor() {\n    this.output = ''\n\n    this.indexVertex = 0\n    this.indexVertexUvs = 0\n    this.indexNormals = 0\n\n    this.vertex = new Vector3()\n    this.color = new Color()\n    this.normal = new Vector3()\n    this.uv = new Vector2()\n\n    this.face = []\n  }\n\n  public parse(object: Object3D): string {\n    object.traverse((child) => {\n      if (child instanceof Mesh && child.isMesh) {\n        this.parseMesh(child)\n      }\n\n      if (child instanceof Line && child.isLine) {\n        this.parseLine(child)\n      }\n\n      if (child instanceof Points && child.isPoints) {\n        this.parsePoints(child)\n      }\n    })\n\n    return this.output\n  }\n\n  private parseMesh(mesh: Mesh): void {\n    let nbVertex = 0\n    let nbNormals = 0\n    let nbVertexUvs = 0\n\n    const geometry = mesh.geometry\n\n    const normalMatrixWorld = new Matrix3()\n\n    if (!geometry.isBufferGeometry) {\n      throw new Error('THREE.OBJExporter: Geometry is not of type THREE.BufferGeometry.')\n    }\n\n    // shortcuts\n    const vertices = geometry.getAttribute('position')\n    const normals = geometry.getAttribute('normal')\n    const uvs = geometry.getAttribute('uv')\n    const indices = geometry.getIndex()\n\n    // name of the mesh object\n    this.output += `o ${mesh.name}\\n`\n\n    // name of the mesh material\n    if (mesh.material && !Array.isArray(mesh.material) && mesh.material.name) {\n      this.output += `usemtl ${mesh.material.name}\\n`\n    }\n\n    // vertices\n\n    if (vertices !== undefined) {\n      for (let i = 0, l = vertices.count; i < l; i++, nbVertex++) {\n        this.vertex.x = vertices.getX(i)\n        this.vertex.y = vertices.getY(i)\n        this.vertex.z = vertices.getZ(i)\n\n        // transform the vertex to world space\n        this.vertex.applyMatrix4(mesh.matrixWorld)\n\n        // transform the vertex to export format\n        this.output += `v ${this.vertex.x} ${this.vertex.y} ${this.vertex.z}\\n`\n      }\n    }\n\n    // uvs\n\n    if (uvs !== undefined) {\n      for (let i = 0, l = uvs.count; i < l; i++, nbVertexUvs++) {\n        this.uv.x = uvs.getX(i)\n        this.uv.y = uvs.getY(i)\n\n        // transform the uv to export format\n        this.output += `vt ${this.uv.x} ${this.uv.y}\\n`\n      }\n    }\n\n    // normals\n\n    if (normals !== undefined) {\n      normalMatrixWorld.getNormalMatrix(mesh.matrixWorld)\n\n      for (let i = 0, l = normals.count; i < l; i++, nbNormals++) {\n        this.normal.x = normals.getX(i)\n        this.normal.y = normals.getY(i)\n        this.normal.z = normals.getZ(i)\n\n        // transform the normal to world space\n        this.normal.applyMatrix3(normalMatrixWorld).normalize()\n\n        // transform the normal to export format\n        this.output += `vn ${this.normal.x} ${this.normal.y} ${this.normal.z}\\n`\n      }\n    }\n\n    // faces\n\n    if (indices !== null) {\n      for (let i = 0, l = indices.count; i < l; i += 3) {\n        for (let m = 0; m < 3; m++) {\n          const j = indices.getX(i + m) + 1\n\n          this.face[m] =\n            this.indexVertex +\n            j +\n            (normals || uvs\n              ? `/${uvs ? this.indexVertexUvs + j : ''}${normals ? `/${this.indexNormals + j}` : ''}`\n              : '')\n        }\n\n        // transform the face to export format\n        this.output += `f ${this.face.join(' ')}\\n`\n      }\n    } else {\n      for (let i = 0, l = vertices.count; i < l; i += 3) {\n        for (let m = 0; m < 3; m++) {\n          const j = i + m + 1\n\n          this.face[m] =\n            this.indexVertex +\n            j +\n            (normals || uvs\n              ? `/${uvs ? this.indexVertexUvs + j : ''}${normals ? `/${this.indexNormals + j}` : ''}`\n              : '')\n        }\n\n        // transform the face to export format\n        this.output += `f ${this.face.join(' ')}\\n`\n      }\n    }\n\n    // update index\n    this.indexVertex += nbVertex\n    this.indexVertexUvs += nbVertexUvs\n    this.indexNormals += nbNormals\n  }\n\n  private parseLine(line: Line): void {\n    let nbVertex = 0\n\n    const geometry = line.geometry\n    const type = line.type\n\n    if (geometry.isBufferGeometry) {\n      throw new Error('THREE.OBJExporter: Geometry is not of type THREE.BufferGeometry.')\n    }\n\n    // shortcuts\n    const vertices = geometry.getAttribute('position')\n\n    // name of the line object\n    this.output += `o ${line.name}\\n`\n\n    if (vertices !== undefined) {\n      for (let i = 0, l = vertices.count; i < l; i++, nbVertex++) {\n        this.vertex.x = vertices.getX(i)\n        this.vertex.y = vertices.getY(i)\n        this.vertex.z = vertices.getZ(i)\n\n        // transform the vertex to world space\n        this.vertex.applyMatrix4(line.matrixWorld)\n\n        // transform the vertex to export format\n        this.output += `v ${this.vertex.x} ${this.vertex.y} ${this.vertex.z}\\n`\n      }\n    }\n\n    if (type === 'Line') {\n      this.output += 'l '\n\n      for (let j = 1, l = vertices.count; j <= l; j++) {\n        this.output += `${this.indexVertex + j} `\n      }\n\n      this.output += '\\n'\n    }\n\n    if (type === 'LineSegments') {\n      for (let j = 1, k = j + 1, l = vertices.count; j < l; j += 2, k = j + 1) {\n        this.output += `l ${this.indexVertex + j} ${this.indexVertex + k}\\n`\n      }\n    }\n\n    // update index\n    this.indexVertex += nbVertex\n  }\n\n  private parsePoints(points: Points): void {\n    let nbVertex = 0\n\n    const geometry = points.geometry\n\n    if (!geometry.isBufferGeometry) {\n      throw new Error('THREE.OBJExporter: Geometry is not of type THREE.BufferGeometry.')\n    }\n\n    const vertices = geometry.getAttribute('position')\n    const colors = geometry.getAttribute('color')\n\n    this.output += `o ${points.name}\\n`\n\n    if (vertices !== undefined) {\n      for (let i = 0, l = vertices.count; i < l; i++, nbVertex++) {\n        this.vertex.fromBufferAttribute(vertices, i)\n        this.vertex.applyMatrix4(points.matrixWorld)\n\n        this.output += `v ${this.vertex.x} ${this.vertex.y} ${this.vertex.z}`\n\n        if (colors !== undefined && colors instanceof BufferAttribute) {\n          this.color.fromBufferAttribute(colors, i)\n\n          this.output += ` ${this.color.r} ${this.color.g} ${this.color.b}`\n        }\n\n        this.output += '\\n'\n      }\n    }\n\n    this.output += 'p '\n\n    for (let j = 1, l = vertices.count; j <= l; j++) {\n      this.output += `${this.indexVertex + j} `\n    }\n\n    this.output += '\\n'\n\n    // update index\n    this.indexVertex += nbVertex\n  }\n}\n\nexport { OBJExporter }\n"], "names": [], "mappings": ";;;;;;;AAEA,MAAM,YAAY;AAAA,EAchB,cAAc;AAbN;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AAGN,SAAK,SAAS;AAEd,SAAK,cAAc;AACnB,SAAK,iBAAiB;AACtB,SAAK,eAAe;AAEf,SAAA,SAAS,IAAI;AACb,SAAA,QAAQ,IAAI;AACZ,SAAA,SAAS,IAAI;AACb,SAAA,KAAK,IAAI;AAEd,SAAK,OAAO;EACd;AAAA,EAEO,MAAM,QAA0B;AAC9B,WAAA,SAAS,CAAC,UAAU;AACrB,UAAA,iBAAiB,QAAQ,MAAM,QAAQ;AACzC,aAAK,UAAU,KAAK;AAAA,MACtB;AAEI,UAAA,iBAAiB,QAAQ,MAAM,QAAQ;AACzC,aAAK,UAAU,KAAK;AAAA,MACtB;AAEI,UAAA,iBAAiB,UAAU,MAAM,UAAU;AAC7C,aAAK,YAAY,KAAK;AAAA,MACxB;AAAA,IAAA,CACD;AAED,WAAO,KAAK;AAAA,EACd;AAAA,EAEQ,UAAU,MAAkB;AAClC,QAAI,WAAW;AACf,QAAI,YAAY;AAChB,QAAI,cAAc;AAElB,UAAM,WAAW,KAAK;AAEhB,UAAA,oBAAoB,IAAI;AAE1B,QAAA,CAAC,SAAS,kBAAkB;AACxB,YAAA,IAAI,MAAM,kEAAkE;AAAA,IACpF;AAGM,UAAA,WAAW,SAAS,aAAa,UAAU;AAC3C,UAAA,UAAU,SAAS,aAAa,QAAQ;AACxC,UAAA,MAAM,SAAS,aAAa,IAAI;AAChC,UAAA,UAAU,SAAS;AAGpB,SAAA,UAAU,KAAK,KAAK;AAAA;AAGrB,QAAA,KAAK,YAAY,CAAC,MAAM,QAAQ,KAAK,QAAQ,KAAK,KAAK,SAAS,MAAM;AACnE,WAAA,UAAU,UAAU,KAAK,SAAS;AAAA;AAAA,IACzC;AAIA,QAAI,aAAa,QAAW;AACjB,eAAA,IAAI,GAAG,IAAI,SAAS,OAAO,IAAI,GAAG,KAAK,YAAY;AAC1D,aAAK,OAAO,IAAI,SAAS,KAAK,CAAC;AAC/B,aAAK,OAAO,IAAI,SAAS,KAAK,CAAC;AAC/B,aAAK,OAAO,IAAI,SAAS,KAAK,CAAC;AAG1B,aAAA,OAAO,aAAa,KAAK,WAAW;AAGpC,aAAA,UAAU,KAAK,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK,KAAK,OAAO;AAAA;AAAA,MACpE;AAAA,IACF;AAIA,QAAI,QAAQ,QAAW;AACZ,eAAA,IAAI,GAAG,IAAI,IAAI,OAAO,IAAI,GAAG,KAAK,eAAe;AACxD,aAAK,GAAG,IAAI,IAAI,KAAK,CAAC;AACtB,aAAK,GAAG,IAAI,IAAI,KAAK,CAAC;AAGtB,aAAK,UAAU,MAAM,KAAK,GAAG,KAAK,KAAK,GAAG;AAAA;AAAA,MAC5C;AAAA,IACF;AAIA,QAAI,YAAY,QAAW;AACP,wBAAA,gBAAgB,KAAK,WAAW;AAEzC,eAAA,IAAI,GAAG,IAAI,QAAQ,OAAO,IAAI,GAAG,KAAK,aAAa;AAC1D,aAAK,OAAO,IAAI,QAAQ,KAAK,CAAC;AAC9B,aAAK,OAAO,IAAI,QAAQ,KAAK,CAAC;AAC9B,aAAK,OAAO,IAAI,QAAQ,KAAK,CAAC;AAG9B,aAAK,OAAO,aAAa,iBAAiB,EAAE,UAAU;AAGjD,aAAA,UAAU,MAAM,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK,KAAK,OAAO;AAAA;AAAA,MACrE;AAAA,IACF;AAIA,QAAI,YAAY,MAAM;AACX,eAAA,IAAI,GAAG,IAAI,QAAQ,OAAO,IAAI,GAAG,KAAK,GAAG;AAChD,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAM,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI;AAEhC,eAAK,KAAK,CAAC,IACT,KAAK,cACL,KACC,WAAW,MACR,IAAI,MAAM,KAAK,iBAAiB,IAAI,KAAK,UAAU,IAAI,KAAK,eAAe,MAAM,OACjF;AAAA,QACR;AAGA,aAAK,UAAU,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,MACxC;AAAA,IAAA,OACK;AACI,eAAA,IAAI,GAAG,IAAI,SAAS,OAAO,IAAI,GAAG,KAAK,GAAG;AACjD,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACpB,gBAAA,IAAI,IAAI,IAAI;AAElB,eAAK,KAAK,CAAC,IACT,KAAK,cACL,KACC,WAAW,MACR,IAAI,MAAM,KAAK,iBAAiB,IAAI,KAAK,UAAU,IAAI,KAAK,eAAe,MAAM,OACjF;AAAA,QACR;AAGA,aAAK,UAAU,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,MACxC;AAAA,IACF;AAGA,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EAEQ,UAAU,MAAkB;AAClC,QAAI,WAAW;AAEf,UAAM,WAAW,KAAK;AACtB,UAAM,OAAO,KAAK;AAElB,QAAI,SAAS,kBAAkB;AACvB,YAAA,IAAI,MAAM,kEAAkE;AAAA,IACpF;AAGM,UAAA,WAAW,SAAS,aAAa,UAAU;AAG5C,SAAA,UAAU,KAAK,KAAK;AAAA;AAEzB,QAAI,aAAa,QAAW;AACjB,eAAA,IAAI,GAAG,IAAI,SAAS,OAAO,IAAI,GAAG,KAAK,YAAY;AAC1D,aAAK,OAAO,IAAI,SAAS,KAAK,CAAC;AAC/B,aAAK,OAAO,IAAI,SAAS,KAAK,CAAC;AAC/B,aAAK,OAAO,IAAI,SAAS,KAAK,CAAC;AAG1B,aAAA,OAAO,aAAa,KAAK,WAAW;AAGpC,aAAA,UAAU,KAAK,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK,KAAK,OAAO;AAAA;AAAA,MACpE;AAAA,IACF;AAEA,QAAI,SAAS,QAAQ;AACnB,WAAK,UAAU;AAEf,eAAS,IAAI,GAAG,IAAI,SAAS,OAAO,KAAK,GAAG,KAAK;AAC1C,aAAA,UAAU,GAAG,KAAK,cAAc;AAAA,MACvC;AAEA,WAAK,UAAU;AAAA,IACjB;AAEA,QAAI,SAAS,gBAAgB;AAC3B,eAAS,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,SAAS,OAAO,IAAI,GAAG,KAAK,GAAG,IAAI,IAAI,GAAG;AACvE,aAAK,UAAU,KAAK,KAAK,cAAc,KAAK,KAAK,cAAc;AAAA;AAAA,MACjE;AAAA,IACF;AAGA,SAAK,eAAe;AAAA,EACtB;AAAA,EAEQ,YAAY,QAAsB;AACxC,QAAI,WAAW;AAEf,UAAM,WAAW,OAAO;AAEpB,QAAA,CAAC,SAAS,kBAAkB;AACxB,YAAA,IAAI,MAAM,kEAAkE;AAAA,IACpF;AAEM,UAAA,WAAW,SAAS,aAAa,UAAU;AAC3C,UAAA,SAAS,SAAS,aAAa,OAAO;AAEvC,SAAA,UAAU,KAAK,OAAO;AAAA;AAE3B,QAAI,aAAa,QAAW;AACjB,eAAA,IAAI,GAAG,IAAI,SAAS,OAAO,IAAI,GAAG,KAAK,YAAY;AACrD,aAAA,OAAO,oBAAoB,UAAU,CAAC;AACtC,aAAA,OAAO,aAAa,OAAO,WAAW;AAEtC,aAAA,UAAU,KAAK,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK,KAAK,OAAO;AAE9D,YAAA,WAAW,UAAa,kBAAkB,iBAAiB;AACxD,eAAA,MAAM,oBAAoB,QAAQ,CAAC;AAEnC,eAAA,UAAU,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM;AAAA,QAChE;AAEA,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AAEA,SAAK,UAAU;AAEf,aAAS,IAAI,GAAG,IAAI,SAAS,OAAO,KAAK,GAAG,KAAK;AAC1C,WAAA,UAAU,GAAG,KAAK,cAAc;AAAA,IACvC;AAEA,SAAK,UAAU;AAGf,SAAK,eAAe;AAAA,EACtB;AACF;"}