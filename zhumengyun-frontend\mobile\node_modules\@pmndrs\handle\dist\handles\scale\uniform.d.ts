import { ColorRepresentation } from 'three';
import { Axis } from '../../state.js';
import { HandlesContext } from '../context.js';
import { HandlesProperties } from '../index.js';
import { RegisteredHandle } from '../registered.js';
export declare class UniformAxisScaleHandle extends RegisteredHandle {
    private readonly actualAxis;
    private readonly invert;
    constructor(context: HandlesContext, tagPrefix: string | undefined, actualAxis: Axis, invert?: boolean);
    bind(defaultColor: ColorRepresentation, defaultHoverColor: ColorRepresentation, config?: HandlesProperties): (() => void) | undefined;
}
