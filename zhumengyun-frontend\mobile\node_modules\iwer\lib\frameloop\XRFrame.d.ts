/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_FRAME } from '../private.js';
import { XRAnchor, XRAnchorSet } from '../anchors/XRAnchor.js';
import { XRHitTestResult, XRHitTestSource } from '../hittest/XRHitTest.js';
import { XRSpace } from '../spaces/XRSpace.js';
import { mat4 } from 'gl-matrix';
import { XRJointPose } from '../pose/XRJointPose.js';
import { XRJointSpace } from '../spaces/XRJointSpace.js';
import { XRMeshSet } from '../meshes/XRMesh.js';
import { XRPlaneSet } from '../planes/XRPlane.js';
import { XRPose } from '../pose/XRPose.js';
import { XRReferenceSpace } from '../spaces/XRReferenceSpace.js';
import { XRRigidTransform } from '../primitives/XRRigidTransform.js';
import { XRSession } from '../session/XRSession.js';
import { XRViewerPose } from '../pose/XRViewerPose.js';
export declare class XRFrame {
    [P_FRAME]: {
        session: XRSession;
        id: number;
        active: boolean;
        animationFrame: boolean;
        predictedDisplayTime: number;
        tempMat4: mat4;
        detectedPlanes: XRPlaneSet;
        detectedMeshes: XRMeshSet;
        trackedAnchors: XRAnchorSet;
        hitTestResultsMap: Map<XRHitTestSource, XRHitTestResult[]>;
    };
    constructor(session: XRSession, id: number, active: boolean, animationFrame: boolean, predictedDisplayTime: number);
    get session(): XRSession;
    get predictedDisplayTime(): number;
    getPose(space: XRSpace, baseSpace: XRSpace): XRPose;
    getViewerPose(referenceSpace: XRReferenceSpace): XRViewerPose;
    getJointPose(joint: XRJointSpace, baseSpace: XRSpace): XRJointPose;
    fillJointRadii(jointSpaces: XRJointSpace[], radii: Float32Array): boolean;
    fillPoses(spaces: XRSpace[], baseSpace: XRSpace, transforms: Float32Array): boolean;
    get detectedPlanes(): XRPlaneSet;
    get detectedMeshes(): XRMeshSet;
    get trackedAnchors(): XRAnchorSet;
    createAnchor(pose: XRRigidTransform, space: XRSpace): Promise<XRAnchor>;
    getHitTestResults(hitTestSource: XRHitTestSource): XRHitTestResult[];
}
//# sourceMappingURL=XRFrame.d.ts.map