import { bindXRInputSourceEvent, XRGazeState, XRInputSourceState, XRInputSourceStateMap, XRScreenInputState, XRTransientPointerState } from '@pmndrs/xr/internals';
export type { XRTransientPointerState, XRScreenInputState, XRGazeState };
export declare function useXRInputSourceStates(): readonly XRInputSourceState[];
export declare function useXRInputSourceState<T extends keyof XRInputSourceStateMap>(type: T, handedness?: XRHandedness): XRInputSourceStateMap[T] | undefined;
export declare function useXRInputSourceStateContext<T extends keyof XRInputSourceStateMap>(type: T): XRInputSourceStateMap[T];
export declare function useXRInputSourceStateContext(): XRInputSourceState;
/**
 * Hook for listening to xr input source events
 * @param inputSource The input source to listen to, or 'all' to listen to all input sources
 * @param event The event to listen to. ([List of events](https://developer.mozilla.org/en-US/docs/Web/API/XRInputSourceEvent))
 * @param fn Callback function called when the event is triggered.
 * @param deps Retriggers the binding of the event when the dependencies change.
 */
export declare function useXRInputSourceEvent(inputSource: XRInputSource | 'all' | undefined, event: Parameters<typeof bindXRInputSourceEvent>[2], fn: (event: XRInputSourceEvent) => void, deps: Array<any>): void;
