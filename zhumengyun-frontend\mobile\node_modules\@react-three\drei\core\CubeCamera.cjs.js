"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("three"),r=require("react"),t=require("@react-three/fiber");function u(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var u=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,u.get?u:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var n=u(e),a=u(r);function o({resolution:u=256,near:o=.1,far:c=1e3,envMap:s,fog:f}={}){const i=t.useThree((({gl:e})=>e)),l=t.useThree((({scene:e})=>e)),b=r.useMemo((()=>{const r=new e.WebGLCubeRenderTarget(u);return r.texture.type=e.HalfFloatType,r}),[u]);r.useEffect((()=>()=>{b.dispose()}),[b]);const p=r.useMemo((()=>new n.CubeCamera(o,c,b)),[o,c,b]);let d,g;const m=a.useCallback((()=>{d=l.fog,g=l.background,l.background=s||g,l.fog=f||d,p.update(i,l),l.fog=d,l.background=g}),[i,l,p]);return{fbo:b,camera:p,update:m}}exports.CubeCamera=function({children:e,frames:r=1/0,resolution:u,near:n,far:c,envMap:s,fog:f,...i}){const l=a.useRef(null),{fbo:b,camera:p,update:d}=o({resolution:u,near:n,far:c,envMap:s,fog:f});let g=0;return t.useFrame((()=>{l.current&&(r===1/0||g<r)&&(l.current.visible=!1,d(),l.current.visible=!0,g++)})),a.createElement("group",i,a.createElement("primitive",{object:p}),a.createElement("group",{ref:l},null==e?void 0:e(b.texture)))},exports.useCubeCamera=o;
