import { PointerEvent } from '@pmndrs/pointer-events';
import { RefObject } from 'react';
import { Object3D } from 'three';
/**
 * Used to track the hover state of a 3D object.
 *
 * @param ref The reference to the 3D object.
 * @param onChange `(hover: boolean, event: PointerEvent) => void` Callback for hover state changes.
 */
export declare function useHover(ref: RefObject<Object3D | null>): boolean;
export declare function useHover(ref: RefObject<Object3D | null>, onChange: (hover: boolean, event: PointerEvent) => void): void;
/**
 * Gets the visibility state of the XR session.
 *
 * @returns The visibility state of the XR session.
 */
export declare function useXRSessionVisibilityState(): XRVisibilityState | undefined;
/**
 * Initilizes the room capture process.
 *
 * @returns A function to initiate room capture, or undefined if unavailable.
 */
export declare function useInitRoomCapture(): (() => Promise<undefined>) | undefined;
/**
 * Checks whether a specific XRSessionMode is supported or not
 *
 * @param {XRSessionMode} mode - The `XRSessionMode` to check against.
 * @param {(error: any) => void} [onError] - Callback executed when an error occurs.
 */
export declare function useXRSessionModeSupported(mode: XRSessionMode, onError?: (error: any) => void): boolean | undefined;
/**
 * @deprecated use `useXRSessionModeSupported` instead
 */
export declare const useSessionModeSupported: typeof useXRSessionModeSupported;
/**
 * Checks if a specific XR session feature is enabled.
 *
 * @param {string} feature - The XR session feature to check against.
 * @returns {boolean} Whether the feature is enabled.
 */
export declare function useXRSessionFeatureEnabled(feature: string): boolean;
/**
 * @deprecated use `useXRSessionFeatureEnabled` instead
 */
export declare const useSessionFeatureEnabled: typeof useXRSessionFeatureEnabled;
