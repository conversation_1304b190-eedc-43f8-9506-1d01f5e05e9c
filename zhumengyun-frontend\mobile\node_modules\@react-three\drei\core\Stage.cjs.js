"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),i=require("./Environment.cjs.js"),r=require("./ContactShadows.cjs.js"),n=require("./Center.cjs.js"),a=require("./AccumulativeShadows.cjs.js"),s=require("./Bounds.cjs.js");function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(i){if("default"!==i){var r=Object.getOwnPropertyDescriptor(e,i);Object.defineProperty(t,i,r.get?r:{enumerable:!0,get:function(){return e[i]}})}})),t.default=e,Object.freeze(t)}require("@react-three/fiber"),require("three"),require("three-stdlib"),require("./useEnvironment.cjs.js"),require("@monogrid/gainmap-js"),require("../helpers/environment-assets.cjs.js"),require("../helpers/deprecated.cjs.js"),require("./shaderMaterial.cjs.js"),require("../materials/DiscardMaterial.cjs.js"),require("../helpers/constants.cjs.js");var o=l(e),c=u(t);const d={rembrandt:{main:[1,2,1],fill:[-2,-.5,-2]},portrait:{main:[-1,2,.5],fill:[-1,.5,-1.5]},upfront:{main:[0,2,1],fill:[-1,.5,-1.5]},soft:{main:[-2,4,4],fill:[-1,.5,-1.5]}};function m({radius:e,adjustCamera:t}){const i=s.useBounds();return c.useEffect((()=>{t&&i.refresh().clip().fit()}),[e,t]),null}exports.Stage=function({children:e,center:t,adjustCamera:l=!0,intensity:u=.5,shadows:f="contact",environment:p="city",preset:h="rembrandt",...j}){var v,b,g,q,y,E,w,C;const S="string"==typeof h?d[h]:h,[{radius:O,height:z},B]=c.useState({radius:0,width:0,height:0,depth:0}),M=null!==(v=null==f?void 0:f.bias)&&void 0!==v?v:-1e-4,L=null!==(b=null==f?void 0:f.normalBias)&&void 0!==b?b:0,_=null!==(g=null==f?void 0:f.size)&&void 0!==g?g:1024,x=null!==(q=null==f?void 0:f.offset)&&void 0!==q?q:0,P="contact"===f||"contact"===(null==f?void 0:f.type),k="accumulative"===f||"accumulative"===(null==f?void 0:f.type),A={..."object"==typeof f?f:{}},D=p?"string"==typeof p?{preset:p}:p:null,F=c.useCallback((e=>{const{width:i,height:r,depth:n,boundingSphere:a}=e;B({radius:a.radius,width:i,height:r,depth:n}),null!=t&&t.onCentered&&t.onCentered(e)}),[]);return c.createElement(c.Fragment,null,c.createElement("ambientLight",{intensity:u/3}),c.createElement("spotLight",{penumbra:1,position:[S.main[0]*O,S.main[1]*O,S.main[2]*O],intensity:2*u,castShadow:!!f,"shadow-bias":M,"shadow-normalBias":L,"shadow-mapSize":_}),c.createElement("pointLight",{position:[S.fill[0]*O,S.fill[1]*O,S.fill[2]*O],intensity:u}),c.createElement(s.Bounds,o.default({fit:!!l,clip:!!l,margin:Number(l),observe:!0},j),c.createElement(m,{radius:O,adjustCamera:l}),c.createElement(n.Center,o.default({},t,{position:[0,x/2,0],onCentered:F}),e)),c.createElement("group",{position:[0,-z/2-x/2,0]},P&&c.createElement(r.ContactShadows,o.default({scale:4*O,far:O,blur:2},A)),k&&c.createElement(a.AccumulativeShadows,o.default({temporal:!0,frames:100,alphaTest:.9,toneMapped:!0,scale:4*O},A),c.createElement(a.RandomizedLight,{amount:null!==(y=A.amount)&&void 0!==y?y:8,radius:null!==(E=A.radius)&&void 0!==E?E:O,ambient:null!==(w=A.ambient)&&void 0!==w?w:.5,intensity:null!==(C=A.intensity)&&void 0!==C?C:1,position:[S.main[0]*O,S.main[1]*O,S.main[2]*O],size:4*O,bias:-M,mapSize:_}))),p&&c.createElement(i.Environment,D))};
