/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

// @generated by enums.py

package com.facebook.yoga;

public enum YogaBoxSizing {
  BORDER_BOX(0),
  CONTENT_BOX(1);

  private final int mIntValue;

  YogaBoxSizing(int intValue) {
    mIntValue = intValue;
  }

  public int intValue() {
    return mIntValue;
  }

  public static YogaBoxSizing fromInt(int value) {
    switch (value) {
      case 0: return BORDER_BOX;
      case 1: return CONTENT_BOX;
      default: throw new IllegalArgumentException("Unknown enum value: " + value);
    }
  }
}
