{"version": 3, "file": "AmmoPhysics.js", "sources": ["../../src/physics/AmmoPhysics.js"], "sourcesContent": ["async function AmmoPhysics() {\n  if ('Ammo' in window === false) {\n    console.error(\"AmmoPhysics: Couldn't find Ammo.js\")\n    return\n  }\n\n  const AmmoLib = await Ammo()\n\n  const frameRate = 60\n\n  const collisionConfiguration = new AmmoLib.btDefaultCollisionConfiguration()\n  const dispatcher = new AmmoLib.btCollisionDispatcher(collisionConfiguration)\n  const broadphase = new AmmoLib.btDbvtBroadphase()\n  const solver = new AmmoLib.btSequentialImpulseConstraintSolver()\n  const world = new AmmoLib.btDiscreteDynamicsWorld(dispatcher, broadphase, solver, collisionConfiguration)\n  world.setGravity(new AmmoLib.btVector3(0, -9.8, 0))\n\n  const worldTransform = new AmmoLib.btTransform()\n\n  //\n\n  function getShape(geometry) {\n    const parameters = geometry.parameters\n\n    // TODO change type to is*\n\n    if (geometry.type === 'BoxGeometry') {\n      const sx = parameters.width !== undefined ? parameters.width / 2 : 0.5\n      const sy = parameters.height !== undefined ? parameters.height / 2 : 0.5\n      const sz = parameters.depth !== undefined ? parameters.depth / 2 : 0.5\n\n      const shape = new AmmoLib.btBoxShape(new AmmoLib.btVector3(sx, sy, sz))\n      shape.setMargin(0.05)\n\n      return shape\n    } else if (geometry.type === 'SphereGeometry' || geometry.type === 'IcosahedronGeometry') {\n      const radius = parameters.radius !== undefined ? parameters.radius : 1\n\n      const shape = new AmmoLib.btSphereShape(radius)\n      shape.setMargin(0.05)\n\n      return shape\n    }\n\n    return null\n  }\n\n  const meshes = []\n  const meshMap = new WeakMap()\n\n  function addMesh(mesh, mass = 0) {\n    const shape = getShape(mesh.geometry)\n\n    if (shape !== null) {\n      if (mesh.isInstancedMesh) {\n        handleInstancedMesh(mesh, mass, shape)\n      } else if (mesh.isMesh) {\n        handleMesh(mesh, mass, shape)\n      }\n    }\n  }\n\n  function handleMesh(mesh, mass, shape) {\n    const position = mesh.position\n    const quaternion = mesh.quaternion\n\n    const transform = new AmmoLib.btTransform()\n    transform.setIdentity()\n    transform.setOrigin(new AmmoLib.btVector3(position.x, position.y, position.z))\n    transform.setRotation(new AmmoLib.btQuaternion(quaternion.x, quaternion.y, quaternion.z, quaternion.w))\n\n    const motionState = new AmmoLib.btDefaultMotionState(transform)\n\n    const localInertia = new AmmoLib.btVector3(0, 0, 0)\n    shape.calculateLocalInertia(mass, localInertia)\n\n    const rbInfo = new AmmoLib.btRigidBodyConstructionInfo(mass, motionState, shape, localInertia)\n\n    const body = new AmmoLib.btRigidBody(rbInfo)\n    // body.setFriction( 4 );\n    world.addRigidBody(body)\n\n    if (mass > 0) {\n      meshes.push(mesh)\n      meshMap.set(mesh, body)\n    }\n  }\n\n  function handleInstancedMesh(mesh, mass, shape) {\n    const array = mesh.instanceMatrix.array\n\n    const bodies = []\n\n    for (let i = 0; i < mesh.count; i++) {\n      const index = i * 16\n\n      const transform = new AmmoLib.btTransform()\n      transform.setFromOpenGLMatrix(array.slice(index, index + 16))\n\n      const motionState = new AmmoLib.btDefaultMotionState(transform)\n\n      const localInertia = new AmmoLib.btVector3(0, 0, 0)\n      shape.calculateLocalInertia(mass, localInertia)\n\n      const rbInfo = new AmmoLib.btRigidBodyConstructionInfo(mass, motionState, shape, localInertia)\n\n      const body = new AmmoLib.btRigidBody(rbInfo)\n      world.addRigidBody(body)\n\n      bodies.push(body)\n    }\n\n    if (mass > 0) {\n      mesh.instanceMatrix.setUsage(35048) // THREE.DynamicDrawUsage = 35048\n      meshes.push(mesh)\n\n      meshMap.set(mesh, bodies)\n    }\n  }\n\n  //\n\n  function setMeshPosition(mesh, position, index = 0) {\n    if (mesh.isInstancedMesh) {\n      const bodies = meshMap.get(mesh)\n      const body = bodies[index]\n\n      body.setAngularVelocity(new AmmoLib.btVector3(0, 0, 0))\n      body.setLinearVelocity(new AmmoLib.btVector3(0, 0, 0))\n\n      worldTransform.setIdentity()\n      worldTransform.setOrigin(new AmmoLib.btVector3(position.x, position.y, position.z))\n      body.setWorldTransform(worldTransform)\n    } else if (mesh.isMesh) {\n      const body = meshMap.get(mesh)\n\n      body.setAngularVelocity(new AmmoLib.btVector3(0, 0, 0))\n      body.setLinearVelocity(new AmmoLib.btVector3(0, 0, 0))\n\n      worldTransform.setIdentity()\n      worldTransform.setOrigin(new AmmoLib.btVector3(position.x, position.y, position.z))\n      body.setWorldTransform(worldTransform)\n    }\n  }\n\n  //\n\n  let lastTime = 0\n\n  function step() {\n    const time = performance.now()\n\n    if (lastTime > 0) {\n      const delta = (time - lastTime) / 1000\n\n      // console.time( 'world.step' );\n      world.stepSimulation(delta, 10)\n      // console.timeEnd( 'world.step' );\n    }\n\n    lastTime = time\n\n    //\n\n    for (let i = 0, l = meshes.length; i < l; i++) {\n      const mesh = meshes[i]\n\n      if (mesh.isInstancedMesh) {\n        const array = mesh.instanceMatrix.array\n        const bodies = meshMap.get(mesh)\n\n        for (let j = 0; j < bodies.length; j++) {\n          const body = bodies[j]\n\n          const motionState = body.getMotionState()\n          motionState.getWorldTransform(worldTransform)\n\n          const position = worldTransform.getOrigin()\n          const quaternion = worldTransform.getRotation()\n\n          compose(position, quaternion, array, j * 16)\n        }\n\n        mesh.instanceMatrix.needsUpdate = true\n      } else if (mesh.isMesh) {\n        const body = meshMap.get(mesh)\n\n        const motionState = body.getMotionState()\n        motionState.getWorldTransform(worldTransform)\n\n        const position = worldTransform.getOrigin()\n        const quaternion = worldTransform.getRotation()\n        mesh.position.set(position.x(), position.y(), position.z())\n        mesh.quaternion.set(quaternion.x(), quaternion.y(), quaternion.z(), quaternion.w())\n      }\n    }\n  }\n\n  // animate\n\n  setInterval(step, 1000 / frameRate)\n\n  return {\n    addMesh: addMesh,\n    setMeshPosition: setMeshPosition,\n    // addCompoundMesh\n  }\n}\n\nfunction compose(position, quaternion, array, index) {\n  const x = quaternion.x(),\n    y = quaternion.y(),\n    z = quaternion.z(),\n    w = quaternion.w()\n  const x2 = x + x,\n    y2 = y + y,\n    z2 = z + z\n  const xx = x * x2,\n    xy = x * y2,\n    xz = x * z2\n  const yy = y * y2,\n    yz = y * z2,\n    zz = z * z2\n  const wx = w * x2,\n    wy = w * y2,\n    wz = w * z2\n\n  array[index + 0] = 1 - (yy + zz)\n  array[index + 1] = xy + wz\n  array[index + 2] = xz - wy\n  array[index + 3] = 0\n\n  array[index + 4] = xy - wz\n  array[index + 5] = 1 - (xx + zz)\n  array[index + 6] = yz + wx\n  array[index + 7] = 0\n\n  array[index + 8] = xz + wy\n  array[index + 9] = yz - wx\n  array[index + 10] = 1 - (xx + yy)\n  array[index + 11] = 0\n\n  array[index + 12] = position.x()\n  array[index + 13] = position.y()\n  array[index + 14] = position.z()\n  array[index + 15] = 1\n}\n\nexport { AmmoPhysics }\n"], "names": [], "mappings": "AAAA,eAAe,cAAc;AAC3B,MAAI,UAAU,WAAW,OAAO;AAC9B,YAAQ,MAAM,oCAAoC;AAClD;AAAA,EACD;AAED,QAAM,UAAU,MAAM,KAAM;AAE5B,QAAM,YAAY;AAElB,QAAM,yBAAyB,IAAI,QAAQ,gCAAiC;AAC5E,QAAM,aAAa,IAAI,QAAQ,sBAAsB,sBAAsB;AAC3E,QAAM,aAAa,IAAI,QAAQ,iBAAkB;AACjD,QAAM,SAAS,IAAI,QAAQ,oCAAqC;AAChE,QAAM,QAAQ,IAAI,QAAQ,wBAAwB,YAAY,YAAY,QAAQ,sBAAsB;AACxG,QAAM,WAAW,IAAI,QAAQ,UAAU,GAAG,MAAM,CAAC,CAAC;AAElD,QAAM,iBAAiB,IAAI,QAAQ,YAAa;AAIhD,WAAS,SAAS,UAAU;AAC1B,UAAM,aAAa,SAAS;AAI5B,QAAI,SAAS,SAAS,eAAe;AACnC,YAAM,KAAK,WAAW,UAAU,SAAY,WAAW,QAAQ,IAAI;AACnE,YAAM,KAAK,WAAW,WAAW,SAAY,WAAW,SAAS,IAAI;AACrE,YAAM,KAAK,WAAW,UAAU,SAAY,WAAW,QAAQ,IAAI;AAEnE,YAAM,QAAQ,IAAI,QAAQ,WAAW,IAAI,QAAQ,UAAU,IAAI,IAAI,EAAE,CAAC;AACtE,YAAM,UAAU,IAAI;AAEpB,aAAO;AAAA,IACb,WAAe,SAAS,SAAS,oBAAoB,SAAS,SAAS,uBAAuB;AACxF,YAAM,SAAS,WAAW,WAAW,SAAY,WAAW,SAAS;AAErE,YAAM,QAAQ,IAAI,QAAQ,cAAc,MAAM;AAC9C,YAAM,UAAU,IAAI;AAEpB,aAAO;AAAA,IACR;AAED,WAAO;AAAA,EACR;AAED,QAAM,SAAS,CAAE;AACjB,QAAM,UAAU,oBAAI,QAAS;AAE7B,WAAS,QAAQ,MAAM,OAAO,GAAG;AAC/B,UAAM,QAAQ,SAAS,KAAK,QAAQ;AAEpC,QAAI,UAAU,MAAM;AAClB,UAAI,KAAK,iBAAiB;AACxB,4BAAoB,MAAM,MAAM,KAAK;AAAA,MAC7C,WAAiB,KAAK,QAAQ;AACtB,mBAAW,MAAM,MAAM,KAAK;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAED,WAAS,WAAW,MAAM,MAAM,OAAO;AACrC,UAAM,WAAW,KAAK;AACtB,UAAM,aAAa,KAAK;AAExB,UAAM,YAAY,IAAI,QAAQ,YAAa;AAC3C,cAAU,YAAa;AACvB,cAAU,UAAU,IAAI,QAAQ,UAAU,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC;AAC7E,cAAU,YAAY,IAAI,QAAQ,aAAa,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,WAAW,CAAC,CAAC;AAEtG,UAAM,cAAc,IAAI,QAAQ,qBAAqB,SAAS;AAE9D,UAAM,eAAe,IAAI,QAAQ,UAAU,GAAG,GAAG,CAAC;AAClD,UAAM,sBAAsB,MAAM,YAAY;AAE9C,UAAM,SAAS,IAAI,QAAQ,4BAA4B,MAAM,aAAa,OAAO,YAAY;AAE7F,UAAM,OAAO,IAAI,QAAQ,YAAY,MAAM;AAE3C,UAAM,aAAa,IAAI;AAEvB,QAAI,OAAO,GAAG;AACZ,aAAO,KAAK,IAAI;AAChB,cAAQ,IAAI,MAAM,IAAI;AAAA,IACvB;AAAA,EACF;AAED,WAAS,oBAAoB,MAAM,MAAM,OAAO;AAC9C,UAAM,QAAQ,KAAK,eAAe;AAElC,UAAM,SAAS,CAAE;AAEjB,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,KAAK;AACnC,YAAM,QAAQ,IAAI;AAElB,YAAM,YAAY,IAAI,QAAQ,YAAa;AAC3C,gBAAU,oBAAoB,MAAM,MAAM,OAAO,QAAQ,EAAE,CAAC;AAE5D,YAAM,cAAc,IAAI,QAAQ,qBAAqB,SAAS;AAE9D,YAAM,eAAe,IAAI,QAAQ,UAAU,GAAG,GAAG,CAAC;AAClD,YAAM,sBAAsB,MAAM,YAAY;AAE9C,YAAM,SAAS,IAAI,QAAQ,4BAA4B,MAAM,aAAa,OAAO,YAAY;AAE7F,YAAM,OAAO,IAAI,QAAQ,YAAY,MAAM;AAC3C,YAAM,aAAa,IAAI;AAEvB,aAAO,KAAK,IAAI;AAAA,IACjB;AAED,QAAI,OAAO,GAAG;AACZ,WAAK,eAAe,SAAS,KAAK;AAClC,aAAO,KAAK,IAAI;AAEhB,cAAQ,IAAI,MAAM,MAAM;AAAA,IACzB;AAAA,EACF;AAID,WAAS,gBAAgB,MAAM,UAAU,QAAQ,GAAG;AAClD,QAAI,KAAK,iBAAiB;AACxB,YAAM,SAAS,QAAQ,IAAI,IAAI;AAC/B,YAAM,OAAO,OAAO,KAAK;AAEzB,WAAK,mBAAmB,IAAI,QAAQ,UAAU,GAAG,GAAG,CAAC,CAAC;AACtD,WAAK,kBAAkB,IAAI,QAAQ,UAAU,GAAG,GAAG,CAAC,CAAC;AAErD,qBAAe,YAAa;AAC5B,qBAAe,UAAU,IAAI,QAAQ,UAAU,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC;AAClF,WAAK,kBAAkB,cAAc;AAAA,IAC3C,WAAe,KAAK,QAAQ;AACtB,YAAM,OAAO,QAAQ,IAAI,IAAI;AAE7B,WAAK,mBAAmB,IAAI,QAAQ,UAAU,GAAG,GAAG,CAAC,CAAC;AACtD,WAAK,kBAAkB,IAAI,QAAQ,UAAU,GAAG,GAAG,CAAC,CAAC;AAErD,qBAAe,YAAa;AAC5B,qBAAe,UAAU,IAAI,QAAQ,UAAU,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC;AAClF,WAAK,kBAAkB,cAAc;AAAA,IACtC;AAAA,EACF;AAID,MAAI,WAAW;AAEf,WAAS,OAAO;AACd,UAAM,OAAO,YAAY,IAAK;AAE9B,QAAI,WAAW,GAAG;AAChB,YAAM,SAAS,OAAO,YAAY;AAGlC,YAAM,eAAe,OAAO,EAAE;AAAA,IAE/B;AAED,eAAW;AAIX,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,YAAM,OAAO,OAAO,CAAC;AAErB,UAAI,KAAK,iBAAiB;AACxB,cAAM,QAAQ,KAAK,eAAe;AAClC,cAAM,SAAS,QAAQ,IAAI,IAAI;AAE/B,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,gBAAM,OAAO,OAAO,CAAC;AAErB,gBAAM,cAAc,KAAK,eAAgB;AACzC,sBAAY,kBAAkB,cAAc;AAE5C,gBAAM,WAAW,eAAe,UAAW;AAC3C,gBAAM,aAAa,eAAe,YAAa;AAE/C,kBAAQ,UAAU,YAAY,OAAO,IAAI,EAAE;AAAA,QAC5C;AAED,aAAK,eAAe,cAAc;AAAA,MAC1C,WAAiB,KAAK,QAAQ;AACtB,cAAM,OAAO,QAAQ,IAAI,IAAI;AAE7B,cAAM,cAAc,KAAK,eAAgB;AACzC,oBAAY,kBAAkB,cAAc;AAE5C,cAAM,WAAW,eAAe,UAAW;AAC3C,cAAM,aAAa,eAAe,YAAa;AAC/C,aAAK,SAAS,IAAI,SAAS,EAAC,GAAI,SAAS,EAAG,GAAE,SAAS,GAAG;AAC1D,aAAK,WAAW,IAAI,WAAW,EAAC,GAAI,WAAW,EAAC,GAAI,WAAW,EAAG,GAAE,WAAW,EAAC,CAAE;AAAA,MACnF;AAAA,IACF;AAAA,EACF;AAID,cAAY,MAAM,MAAO,SAAS;AAElC,SAAO;AAAA,IACL;AAAA,IACA;AAAA;AAAA,EAED;AACH;AAEA,SAAS,QAAQ,UAAU,YAAY,OAAO,OAAO;AACnD,QAAM,IAAI,WAAW,EAAG,GACtB,IAAI,WAAW,EAAG,GAClB,IAAI,WAAW,EAAG,GAClB,IAAI,WAAW,EAAG;AACpB,QAAM,KAAK,IAAI,GACb,KAAK,IAAI,GACT,KAAK,IAAI;AACX,QAAM,KAAK,IAAI,IACb,KAAK,IAAI,IACT,KAAK,IAAI;AACX,QAAM,KAAK,IAAI,IACb,KAAK,IAAI,IACT,KAAK,IAAI;AACX,QAAM,KAAK,IAAI,IACb,KAAK,IAAI,IACT,KAAK,IAAI;AAEX,QAAM,QAAQ,CAAC,IAAI,KAAK,KAAK;AAC7B,QAAM,QAAQ,CAAC,IAAI,KAAK;AACxB,QAAM,QAAQ,CAAC,IAAI,KAAK;AACxB,QAAM,QAAQ,CAAC,IAAI;AAEnB,QAAM,QAAQ,CAAC,IAAI,KAAK;AACxB,QAAM,QAAQ,CAAC,IAAI,KAAK,KAAK;AAC7B,QAAM,QAAQ,CAAC,IAAI,KAAK;AACxB,QAAM,QAAQ,CAAC,IAAI;AAEnB,QAAM,QAAQ,CAAC,IAAI,KAAK;AACxB,QAAM,QAAQ,CAAC,IAAI,KAAK;AACxB,QAAM,QAAQ,EAAE,IAAI,KAAK,KAAK;AAC9B,QAAM,QAAQ,EAAE,IAAI;AAEpB,QAAM,QAAQ,EAAE,IAAI,SAAS,EAAG;AAChC,QAAM,QAAQ,EAAE,IAAI,SAAS,EAAG;AAChC,QAAM,QAAQ,EAAE,IAAI,SAAS,EAAG;AAChC,QAAM,QAAQ,EAAE,IAAI;AACtB;"}