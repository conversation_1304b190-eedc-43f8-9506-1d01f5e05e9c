import { CombinedPointer, GetCamera } from '@pmndrs/pointer-events';
import { Object3D } from 'three';
import { DefaultXRInputSourceTeleportPointerOptions, XRControllerState, XRHandState, XRInputSourceState } from '../internals.js';
import { DefaultXRControllerOptions, DefaultXRGazeOptions, DefaultXRHandOptions, DefaultXRHandTouchPointerOptions, DefaultXRInputSourceGrabPointerOptions, DefaultXRInputSourceRayPointerOptions, DefaultXRScreenInputOptions, DefaultXRTransientPointerOptions } from '../default.js';
import { XRSpaceType } from './types.js';
export declare function createDefaultXRInputSourceRayPointer(scene: Object3D, getCamera: GetCamera, space: Object3D, state: XRInputSourceState, session: XRSession, options: DefaultXRInputSourceRayPointerOptions | undefined, combined: CombinedPointer, makeDefault?: boolean): () => void;
export declare function createDefaultXRInputSourceTeleportPointer(scene: Object3D, getCamera: GetCamera, space: Object3D, state: XRInputSourceState, session: XRSession, options: DefaultXRInputSourceTeleportPointerOptions | undefined, combined: CombinedPointer, makeDefault?: boolean): () => void;
export declare function createDefaultXRInputSourceGrabPointer(scene: Object3D, getCamera: GetCamera, space: Object3D, state: XRInputSourceState, gripSpace: XRSpaceType, session: XRSession, event: 'select' | 'squeeze', options: DefaultXRInputSourceGrabPointerOptions | undefined, combined: CombinedPointer, makeDefault?: boolean): () => void;
export declare function createDefaultXRHandTouchPointer(scene: Object3D, getCamera: GetCamera, space: Object3D, state: XRHandState, options: DefaultXRHandTouchPointerOptions | undefined, combined: CombinedPointer, makeDefault?: boolean): () => void;
export declare function createDefaultXRHand(scene: Object3D, getCamera: GetCamera, space: Object3D, state: XRHandState, session: XRSession, { grabPointer: grabPointerOptions, rayPointer: rayPointerOptions, teleportPointer: teleportPointerOptions, model: modelOptions, touchPointer: touchPointerOptions, }: DefaultXRHandOptions | undefined, combined: CombinedPointer): () => void;
export declare function createDefaultXRController(scene: Object3D, getCamera: GetCamera, space: Object3D, state: XRControllerState, session: XRSession, { rayPointer: rayPointerOptions, grabPointer: grabPointerOptions, teleportPointer: teleportPointerOptions, model: modelOptions, }: DefaultXRControllerOptions | undefined, combined: CombinedPointer): () => void;
export declare function createDefaultXRTransientPointer(scene: Object3D, getCamera: GetCamera, space: Object3D, state: XRInputSourceState, session: XRSession, options: DefaultXRTransientPointerOptions | undefined, combined: CombinedPointer): () => void;
export declare function createDefaultXRGaze(scene: Object3D, getCamera: GetCamera, space: Object3D, state: XRInputSourceState, session: XRSession, options: DefaultXRGazeOptions | undefined, combined: CombinedPointer): () => void;
export declare function createDefaultXRScreenInput(scene: Object3D, getCamera: GetCamera, space: Object3D, state: XRInputSourceState, session: XRSession, options: DefaultXRScreenInputOptions | undefined, combined: CombinedPointer): () => void;
