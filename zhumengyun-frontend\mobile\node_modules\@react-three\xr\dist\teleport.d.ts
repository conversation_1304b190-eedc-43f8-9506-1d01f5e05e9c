import { Pointer } from '@pmndrs/pointer-events';
import { TeleportPointerRayModel as TeleportPointerRayModelImpl, TeleportPointerRayModelOptions } from '@pmndrs/xr';
import { ThreeEvent } from '@react-three/fiber';
import { ReactNode } from 'react';
import { Vector3 } from 'three';
/**
 * Component that allows to declare its children as teleport targets.
 * @param props
 * @param props.onTeleport Function that is called when the teleport target is hit
 */
export declare function TeleportTarget({ children, onTeleport, }: {
    children?: ReactNode;
    onTeleport?: (point: Vector3, event: ThreeEvent<MouseEvent>) => void;
}): import("react/jsx-runtime").JSX.Element;
export declare const TeleportPointerRayModel: import("react").ForwardRefExoticComponent<TeleportPointerRayModelOptions & {
    pointer: Pointer;
    linePoints: Array<Vector3>;
} & import("react").RefAttributes<TeleportPointerRayModelImpl>>;
