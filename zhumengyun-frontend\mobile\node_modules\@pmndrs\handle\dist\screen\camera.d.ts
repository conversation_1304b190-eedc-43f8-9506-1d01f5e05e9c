import { Object3D, Quaternion, Vector3, Vector3Tuple } from 'three';
import { StoreApi } from 'zustand/vanilla';
export declare function defaultScreenCameraApply(update: Partial<ScreenCameraState>, store: StoreApi<ScreenCameraState>): void;
export type ScreenCameraState = {
    distance: number;
    origin: Readonly<Vector3Tuple>;
    yaw: number;
    pitch: number;
};
export type ScreenCameraStateAndFunctions = ScreenCameraState & {
    setCameraPosition(x: number, y: number, z: number, keepOffsetToOrigin?: boolean): void;
    getCameraTransformation(position?: Vector3, rotation?: Quaternion): void;
    setOriginPosition(x: number, y: number, z: number, keepOffsetToCamera?: boolean): void;
};
export declare function computeScreenCameraStoreTransformation(pitch: number, yaw: number, cameraDistanceToOrigin: number, origin: Readonly<Vector3Tuple>, position?: Vector3, rotation?: Quaternion, up?: Vector3): void;
export declare function createScreenCameraStore({ distance, origin, pitch: rotationX, yaw: rotationY }?: Partial<ScreenCameraState>, up?: Vector3): StoreApi<ScreenCameraStateAndFunctions>;
export declare function applyScreenCameraState(store: StoreApi<ScreenCameraStateAndFunctions>, getTarget: () => Object3D | undefined | null): () => void;
export declare function applyDampedScreenCameraState(store: StoreApi<ScreenCameraStateAndFunctions>, getTarget: () => Object3D | undefined | null, getDamping: () => number | boolean, up?: Vector3): (deltaTime: number) => void;
