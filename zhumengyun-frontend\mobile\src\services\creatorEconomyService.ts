// 创作者经济服务 - 国内合规版
// 严格遵守中华人民共和国相关法律法规

export interface UserPoints {
  NGT: number;  // NextGen积分 - 平台功能积分
  CRT: number;  // Creator积分 - 创作价值积分
  SKL: number;  // Skill积分 - 技能服务积分
  FAN: number;  // Fan积分 - 社区参与积分
  DID: number;  // 数字身份积分 - 信誉积分
}

export interface CreatorProfile {
  id: string;
  name: string;
  avatar: string;
  verified: boolean;
  level: 'Bronze' | 'Silver' | 'Gold' | 'Platinum' | 'Diamond';
  specialties: string[];
  points: UserPoints;
  earnings: {
    totalRevenue: number;        // 总收益(人民币)
    monthlyIncome: number;       // 月收入(人民币)
    contentSales: number;        // 内容销售收入
    skillServices: number;       // 技能服务收入
    platformRewards: number;     // 平台奖励
  };
  reputation: {
    score: number;               // 信誉评分
    reviews: number;             // 评价数量
    rating: number;              // 平均评分
    completedProjects: number;   // 完成项目数
  };
  certifications: string[];     // 认证列表
  realNameVerified: boolean;    // 实名认证状态
}

export interface DigitalAsset {
  id: string;
  title: string;
  description: string;
  creator: CreatorProfile;
  category: 'design' | 'model' | 'document' | 'media' | 'course';
  price: number;                // 价格(人民币)
  currency: 'CNY';              // 仅支持人民币
  tags: string[];
  createdAt: Date;
  downloads: number;
  rating: number;
  reviews: number;
  isExclusive: boolean;         // 是否独家
  copyrightProtected: boolean;  // 版权保护
  thumbnailUrl: string;
  fileSize: string;
  format: string;
}

export interface SkillService {
  id: string;
  provider: CreatorProfile;
  title: string;
  description: string;
  category: string;
  hourlyRate: number;           // 小时费率(人民币)
  currency: 'CNY';
  availability: 'available' | 'busy' | 'offline';
  responseTime: string;         // 响应时间
  completionRate: number;       // 完成率
  rating: number;
  totalOrders: number;
  skills: string[];
  certifications: string[];
  portfolio: string[];          // 作品集
}

export interface CommunityActivity {
  id: string;
  type: 'post' | 'comment' | 'like' | 'share' | 'follow';
  user: CreatorProfile;
  content?: string;
  targetId?: string;
  pointsEarned: number;         // 获得积分
  timestamp: Date;
}

export interface PointsTransaction {
  id: string;
  userId: string;
  type: 'earn' | 'spend' | 'exchange';
  pointType: keyof UserPoints;
  amount: number;
  description: string;
  relatedId?: string;           // 相关内容/服务ID
  timestamp: Date;
  status: 'pending' | 'completed' | 'failed';
}

export interface ComplianceInfo {
  realNameVerified: boolean;
  idCardVerified: boolean;
  phoneVerified: boolean;
  emailVerified: boolean;
  bankCardVerified: boolean;
  taxId?: string;               // 纳税人识别号
  businessLicense?: string;     // 营业执照(企业用户)
  verificationLevel: 'basic' | 'intermediate' | 'advanced' | 'enterprise';
  dailyLimit: number;           // 日交易限额
  monthlyLimit: number;         // 月交易限额
}

class CreatorEconomyService {
  private baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

  // 获取用户积分
  async getUserPoints(userId: string): Promise<UserPoints> {
    try {
      const response = await fetch(`${this.baseUrl}/api/users/${userId}/points`, {
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) throw new Error('获取积分失败');
      return await response.json();
    } catch (error) {
      console.error('获取用户积分失败:', error);
      // 返回模拟数据
      return {
        NGT: 1000,
        CRT: 500,
        SKL: 300,
        FAN: 800,
        DID: 95
      };
    }
  }

  // 获取创作者资料
  async getCreatorProfile(userId: string): Promise<CreatorProfile> {
    try {
      const response = await fetch(`${this.baseUrl}/api/creators/${userId}`, {
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) throw new Error('获取创作者资料失败');
      return await response.json();
    } catch (error) {
      console.error('获取创作者资料失败:', error);
      // 返回模拟数据
      return this.getMockCreatorProfile(userId);
    }
  }

  // 获取数字资产列表
  async getDigitalAssets(params?: {
    category?: string;
    minPrice?: number;
    maxPrice?: number;
    tags?: string[];
    page?: number;
    limit?: number;
  }): Promise<DigitalAsset[]> {
    try {
      const queryParams = new URLSearchParams();
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined) {
            queryParams.append(key, String(value));
          }
        });
      }

      const response = await fetch(`${this.baseUrl}/api/digital-assets?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) throw new Error('获取数字资产失败');
      return await response.json();
    } catch (error) {
      console.error('获取数字资产失败:', error);
      return this.getMockDigitalAssets();
    }
  }

  // 获取技能服务列表
  async getSkillServices(params?: {
    category?: string;
    minRate?: number;
    maxRate?: number;
    skills?: string[];
    availability?: string;
    page?: number;
    limit?: number;
  }): Promise<SkillService[]> {
    try {
      const queryParams = new URLSearchParams();
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined) {
            queryParams.append(key, String(value));
          }
        });
      }

      const response = await fetch(`${this.baseUrl}/api/skill-services?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) throw new Error('获取技能服务失败');
      return await response.json();
    } catch (error) {
      console.error('获取技能服务失败:', error);
      return this.getMockSkillServices();
    }
  }

  // 购买数字资产
  async purchaseDigitalAsset(assetId: string, paymentMethod: 'points' | 'cny'): Promise<{
    success: boolean;
    orderId?: string;
    message: string;
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/digital-assets/${assetId}/purchase`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ paymentMethod })
      });
      
      if (!response.ok) throw new Error('购买失败');
      return await response.json();
    } catch (error) {
      console.error('购买数字资产失败:', error);
      return {
        success: false,
        message: '购买失败，请稍后重试'
      };
    }
  }

  // 预约技能服务
  async bookSkillService(serviceId: string, details: {
    duration: number;
    scheduledTime: Date;
    requirements: string;
    paymentMethod: 'points' | 'cny';
  }): Promise<{
    success: boolean;
    bookingId?: string;
    message: string;
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/skill-services/${serviceId}/book`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(details)
      });
      
      if (!response.ok) throw new Error('预约失败');
      return await response.json();
    } catch (error) {
      console.error('预约技能服务失败:', error);
      return {
        success: false,
        message: '预约失败，请稍后重试'
      };
    }
  }

  // 积分兑换
  async exchangePoints(from: keyof UserPoints, to: keyof UserPoints, amount: number): Promise<{
    success: boolean;
    newBalance?: UserPoints;
    message: string;
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/points/exchange`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ from, to, amount })
      });
      
      if (!response.ok) throw new Error('兑换失败');
      return await response.json();
    } catch (error) {
      console.error('积分兑换失败:', error);
      return {
        success: false,
        message: '兑换失败，请稍后重试'
      };
    }
  }

  // 获取积分交易记录
  async getPointsTransactions(userId: string, page = 1, limit = 20): Promise<PointsTransaction[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/users/${userId}/points/transactions?page=${page}&limit=${limit}`, {
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) throw new Error('获取交易记录失败');
      return await response.json();
    } catch (error) {
      console.error('获取积分交易记录失败:', error);
      return [];
    }
  }

  // 获取合规信息
  async getComplianceInfo(userId: string): Promise<ComplianceInfo> {
    try {
      const response = await fetch(`${this.baseUrl}/api/users/${userId}/compliance`, {
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) throw new Error('获取合规信息失败');
      return await response.json();
    } catch (error) {
      console.error('获取合规信息失败:', error);
      return {
        realNameVerified: false,
        idCardVerified: false,
        phoneVerified: true,
        emailVerified: true,
        bankCardVerified: false,
        verificationLevel: 'basic',
        dailyLimit: 1000,
        monthlyLimit: 10000
      };
    }
  }

  // 私有方法
  private getToken(): string {
    return localStorage.getItem('authToken') || '';
  }

  private getMockCreatorProfile(userId: string): CreatorProfile {
    return {
      id: userId,
      name: '张建筑师',
      avatar: '🏗️',
      verified: true,
      level: 'Gold',
      specialties: ['建筑设计', 'BIM建模', '智慧城市'],
      points: {
        NGT: 1000,
        CRT: 500,
        SKL: 300,
        FAN: 800,
        DID: 95
      },
      earnings: {
        totalRevenue: 150000,
        monthlyIncome: 25000,
        contentSales: 80000,
        skillServices: 60000,
        platformRewards: 10000
      },
      reputation: {
        score: 4.8,
        reviews: 156,
        rating: 4.8,
        completedProjects: 89
      },
      certifications: ['高级建筑师', 'BIM专家', '智慧城市规划师'],
      realNameVerified: true
    };
  }

  private getMockDigitalAssets(): DigitalAsset[] {
    return [
      {
        id: '1',
        title: 'AI驱动的智慧城市设计方案',
        description: '采用最新AI技术的智慧城市综合体设计方案，包含完整的BIM模型和技术文档。',
        creator: this.getMockCreatorProfile('creator1'),
        category: 'design',
        price: 5000,
        currency: 'CNY',
        tags: ['智慧城市', 'BIM', 'AI设计'],
        createdAt: new Date('2024-12-01'),
        downloads: 89,
        rating: 4.9,
        reviews: 23,
        isExclusive: true,
        copyrightProtected: true,
        thumbnailUrl: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop',
        fileSize: '2.5GB',
        format: 'DWG, RVT, PDF'
      }
    ];
  }

  private getMockSkillServices(): SkillService[] {
    return [
      {
        id: '1',
        provider: this.getMockCreatorProfile('provider1'),
        title: '建筑结构设计咨询',
        description: '提供专业的建筑结构设计咨询服务，包括结构分析、优化建议等。',
        category: '建筑工程',
        hourlyRate: 500,
        currency: 'CNY',
        availability: 'available',
        responseTime: '2小时内',
        completionRate: 98,
        rating: 4.9,
        totalOrders: 156,
        skills: ['结构设计', '抗震分析', '建筑力学'],
        certifications: ['注册结构工程师', '高级工程师'],
        portfolio: ['项目1', '项目2', '项目3']
      }
    ];
  }
}

export const creatorEconomyService = new CreatorEconomyService();
