{"version": 3, "file": "ParametricGeometries.cjs", "sources": ["../../src/geometries/ParametricGeometries.js"], "sourcesContent": ["import { <PERSON>ur<PERSON>, Vector3 } from 'three'\nimport { ParametricGeometry } from './ParametricGeometry'\n\nclass TubeGeometry extends ParametricGeometry {\n  constructor(path, segments = 64, radius = 1, segmentsRadius = 8, closed = false) {\n    const numpoints = segments + 1\n\n    const frames = path.computeFrenetFrames(segments, closed),\n      tangents = frames.tangents,\n      normals = frames.normals,\n      binormals = frames.binormals\n\n    const position = new Vector3()\n\n    function ParametricTube(u, v, target) {\n      v *= 2 * Math.PI\n\n      const i = Math.floor(u * (numpoints - 1))\n\n      path.getPointAt(u, position)\n\n      const normal = normals[i]\n      const binormal = binormals[i]\n\n      const cx = -radius * Math.cos(v) // TODO: Hack: Negating it so it faces outside.\n      const cy = radius * Math.sin(v)\n\n      position.x += cx * normal.x + cy * binormal.x\n      position.y += cx * normal.y + cy * binormal.y\n      position.z += cx * normal.z + cy * binormal.z\n\n      target.copy(position)\n    }\n\n    super(ParametricTube, segments, segmentsRadius)\n\n    // proxy internals\n\n    this.tangents = tangents\n    this.normals = normals\n    this.binormals = binormals\n\n    this.path = path\n    this.segments = segments\n    this.radius = radius\n    this.segmentsRadius = segmentsRadius\n    this.closed = closed\n  }\n}\n\n/**\n * Experimental primitive geometry creation using Surface Parametric equations\n */\nconst ParametricGeometries = {\n  klein: function (v, u, target) {\n    u *= Math.PI\n    v *= 2 * Math.PI\n\n    u = u * 2\n    let x, z\n    if (u < Math.PI) {\n      x = 3 * Math.cos(u) * (1 + Math.sin(u)) + 2 * (1 - Math.cos(u) / 2) * Math.cos(u) * Math.cos(v)\n      z = -8 * Math.sin(u) - 2 * (1 - Math.cos(u) / 2) * Math.sin(u) * Math.cos(v)\n    } else {\n      x = 3 * Math.cos(u) * (1 + Math.sin(u)) + 2 * (1 - Math.cos(u) / 2) * Math.cos(v + Math.PI)\n      z = -8 * Math.sin(u)\n    }\n\n    const y = -2 * (1 - Math.cos(u) / 2) * Math.sin(v)\n\n    target.set(x, y, z)\n  },\n\n  plane: function (width, height) {\n    return function (u, v, target) {\n      const x = u * width\n      const y = 0\n      const z = v * height\n\n      target.set(x, y, z)\n    }\n  },\n\n  mobius: function (u, t, target) {\n    // flat mobius strip\n    // http://www.wolframalpha.com/input/?i=M%C3%B6bius+strip+parametric+equations&lk=1&a=ClashPrefs_*Surface.MoebiusStrip.SurfaceProperty.ParametricEquations-\n    u = u - 0.5\n    const v = 2 * Math.PI * t\n\n    const a = 2\n\n    const x = Math.cos(v) * (a + u * Math.cos(v / 2))\n    const y = Math.sin(v) * (a + u * Math.cos(v / 2))\n    const z = u * Math.sin(v / 2)\n\n    target.set(x, y, z)\n  },\n\n  mobius3d: function (u, t, target) {\n    // volumetric mobius strip\n\n    u *= Math.PI\n    t *= 2 * Math.PI\n\n    u = u * 2\n    const phi = u / 2\n    const major = 2.25,\n      a = 0.125,\n      b = 0.65\n\n    let x = a * Math.cos(t) * Math.cos(phi) - b * Math.sin(t) * Math.sin(phi)\n    const z = a * Math.cos(t) * Math.sin(phi) + b * Math.sin(t) * Math.cos(phi)\n    const y = (major + x) * Math.sin(u)\n    x = (major + x) * Math.cos(u)\n\n    target.set(x, y, z)\n  },\n  TubeGeometry,\n  TorusKnotGeometry: class TorusKnotGeometry extends TubeGeometry {\n    constructor(radius = 200, tube = 40, segmentsT = 64, segmentsR = 8, p = 2, q = 3) {\n      class TorusKnotCurve extends Curve {\n        getPoint(t, optionalTarget = new Vector3()) {\n          const point = optionalTarget\n\n          t *= Math.PI * 2\n\n          const r = 0.5\n\n          const x = (1 + r * Math.cos(q * t)) * Math.cos(p * t)\n          const y = (1 + r * Math.cos(q * t)) * Math.sin(p * t)\n          const z = r * Math.sin(q * t)\n\n          return point.set(x, y, z).multiplyScalar(radius)\n        }\n      }\n\n      const segments = segmentsT\n      const radiusSegments = segmentsR\n      const extrudePath = new TorusKnotCurve()\n\n      super(extrudePath, segments, tube, radiusSegments, true, false)\n\n      this.radius = radius\n      this.tube = tube\n      this.segmentsT = segmentsT\n      this.segmentsR = segmentsR\n      this.p = p\n      this.q = q\n    }\n  },\n  SphereGeometry: class SphereGeometry extends ParametricGeometry {\n    constructor(size, u, v) {\n      function sphere(u, v, target) {\n        u *= Math.PI\n        v *= 2 * Math.PI\n\n        const x = size * Math.sin(u) * Math.cos(v)\n        const y = size * Math.sin(u) * Math.sin(v)\n        const z = size * Math.cos(u)\n\n        target.set(x, y, z)\n      }\n\n      super(sphere, u, v)\n    }\n  },\n  PlaneGeometry: class PlaneGeometry extends ParametricGeometry {\n    constructor(width, depth, segmentsWidth, segmentsDepth) {\n      function plane(u, v, target) {\n        const x = u * width\n        const y = 0\n        const z = v * depth\n\n        target.set(x, y, z)\n      }\n\n      super(plane, segmentsWidth, segmentsDepth)\n    }\n  },\n}\n\nexport { ParametricGeometries }\n"], "names": ["ParametricGeometry", "Vector3", "Curve", "u", "v"], "mappings": ";;;;AAGA,MAAM,qBAAqBA,mBAAAA,mBAAmB;AAAA,EAC5C,YAAY,MAAM,WAAW,IAAI,SAAS,GAAG,iBAAiB,GAAG,SAAS,OAAO;AAC/E,UAAM,YAAY,WAAW;AAE7B,UAAM,SAAS,KAAK,oBAAoB,UAAU,MAAM,GACtD,WAAW,OAAO,UAClB,UAAU,OAAO,SACjB,YAAY,OAAO;AAErB,UAAM,WAAW,IAAIC,cAAS;AAE9B,aAAS,eAAe,GAAG,GAAG,QAAQ;AACpC,WAAK,IAAI,KAAK;AAEd,YAAM,IAAI,KAAK,MAAM,KAAK,YAAY,EAAE;AAExC,WAAK,WAAW,GAAG,QAAQ;AAE3B,YAAM,SAAS,QAAQ,CAAC;AACxB,YAAM,WAAW,UAAU,CAAC;AAE5B,YAAM,KAAK,CAAC,SAAS,KAAK,IAAI,CAAC;AAC/B,YAAM,KAAK,SAAS,KAAK,IAAI,CAAC;AAE9B,eAAS,KAAK,KAAK,OAAO,IAAI,KAAK,SAAS;AAC5C,eAAS,KAAK,KAAK,OAAO,IAAI,KAAK,SAAS;AAC5C,eAAS,KAAK,KAAK,OAAO,IAAI,KAAK,SAAS;AAE5C,aAAO,KAAK,QAAQ;AAAA,IACrB;AAED,UAAM,gBAAgB,UAAU,cAAc;AAI9C,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,YAAY;AAEjB,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,SAAK,SAAS;AAAA,EACf;AACH;AAKK,MAAC,uBAAuB;AAAA,EAC3B,OAAO,SAAU,GAAG,GAAG,QAAQ;AAC7B,SAAK,KAAK;AACV,SAAK,IAAI,KAAK;AAEd,QAAI,IAAI;AACR,QAAI,GAAG;AACP,QAAI,IAAI,KAAK,IAAI;AACf,UAAI,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AAC9F,UAAI,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,IACjF,OAAW;AACL,UAAI,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,EAAE;AAC1F,UAAI,KAAK,KAAK,IAAI,CAAC;AAAA,IACpB;AAED,UAAM,IAAI,MAAM,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,CAAC;AAEjD,WAAO,IAAI,GAAG,GAAG,CAAC;AAAA,EACnB;AAAA,EAED,OAAO,SAAU,OAAO,QAAQ;AAC9B,WAAO,SAAU,GAAG,GAAG,QAAQ;AAC7B,YAAM,IAAI,IAAI;AACd,YAAM,IAAI;AACV,YAAM,IAAI,IAAI;AAEd,aAAO,IAAI,GAAG,GAAG,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EAED,QAAQ,SAAU,GAAG,GAAG,QAAQ;AAG9B,QAAI,IAAI;AACR,UAAM,IAAI,IAAI,KAAK,KAAK;AAExB,UAAM,IAAI;AAEV,UAAM,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC;AAC/C,UAAM,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC;AAC/C,UAAM,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC;AAE5B,WAAO,IAAI,GAAG,GAAG,CAAC;AAAA,EACnB;AAAA,EAED,UAAU,SAAU,GAAG,GAAG,QAAQ;AAGhC,SAAK,KAAK;AACV,SAAK,IAAI,KAAK;AAEd,QAAI,IAAI;AACR,UAAM,MAAM,IAAI;AAChB,UAAM,QAAQ,MACZ,IAAI,OACJ,IAAI;AAEN,QAAI,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,GAAG;AACxE,UAAM,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,GAAG;AAC1E,UAAM,KAAK,QAAQ,KAAK,KAAK,IAAI,CAAC;AAClC,SAAK,QAAQ,KAAK,KAAK,IAAI,CAAC;AAE5B,WAAO,IAAI,GAAG,GAAG,CAAC;AAAA,EACnB;AAAA,EACD;AAAA,EACA,mBAAmB,MAAM,0BAA0B,aAAa;AAAA,IAC9D,YAAY,SAAS,KAAK,OAAO,IAAI,YAAY,IAAI,YAAY,GAAG,IAAI,GAAG,IAAI,GAAG;AAChF,YAAM,uBAAuBC,MAAAA,MAAM;AAAA,QACjC,SAAS,GAAG,iBAAiB,IAAID,MAAO,QAAA,GAAI;AAC1C,gBAAM,QAAQ;AAEd,eAAK,KAAK,KAAK;AAEf,gBAAM,IAAI;AAEV,gBAAM,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC;AACpD,gBAAM,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC;AACpD,gBAAM,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC;AAE5B,iBAAO,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,eAAe,MAAM;AAAA,QAChD;AAAA,MACF;AAED,YAAM,WAAW;AACjB,YAAM,iBAAiB;AACvB,YAAM,cAAc,IAAI,eAAgB;AAExC,YAAM,aAAa,UAAU,MAAM,gBAAgB,MAAM,KAAK;AAE9D,WAAK,SAAS;AACd,WAAK,OAAO;AACZ,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,WAAK,IAAI;AACT,WAAK,IAAI;AAAA,IACV;AAAA,EACF;AAAA,EACD,gBAAgB,MAAM,uBAAuBD,sCAAmB;AAAA,IAC9D,YAAY,MAAM,GAAG,GAAG;AACtB,eAAS,OAAOG,IAAGC,IAAG,QAAQ;AAC5B,QAAAD,MAAK,KAAK;AACV,QAAAC,MAAK,IAAI,KAAK;AAEd,cAAM,IAAI,OAAO,KAAK,IAAID,EAAC,IAAI,KAAK,IAAIC,EAAC;AACzC,cAAM,IAAI,OAAO,KAAK,IAAID,EAAC,IAAI,KAAK,IAAIC,EAAC;AACzC,cAAM,IAAI,OAAO,KAAK,IAAID,EAAC;AAE3B,eAAO,IAAI,GAAG,GAAG,CAAC;AAAA,MACnB;AAED,YAAM,QAAQ,GAAG,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EACD,eAAe,MAAM,sBAAsBH,sCAAmB;AAAA,IAC5D,YAAY,OAAO,OAAO,eAAe,eAAe;AACtD,eAAS,MAAM,GAAG,GAAG,QAAQ;AAC3B,cAAM,IAAI,IAAI;AACd,cAAM,IAAI;AACV,cAAM,IAAI,IAAI;AAEd,eAAO,IAAI,GAAG,GAAG,CAAC;AAAA,MACnB;AAED,YAAM,OAAO,eAAe,aAAa;AAAA,IAC1C;AAAA,EACF;AACH;;"}