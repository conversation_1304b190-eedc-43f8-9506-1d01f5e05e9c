import { updateXRControllerGamepadState } from './gamepad.js';
import { syncAsync } from './utils.js';
export function createXRControllerState(id, inputSource, layoutLoader, events, isPrimary) {
    return syncAsync(() => layoutLoader.load(inputSource.profiles, inputSource.handedness), (layout) => {
        const gamepad = {};
        updateXRControllerGamepadState(gamepad, inputSource, layout);
        return {
            id,
            isPrimary,
            events,
            type: 'controller',
            inputSource,
            gamepad,
            layout,
        };
    });
}
export function updateXRControllerState({ gamepad, inputSource, layout }) {
    updateXRControllerGamepadState(gamepad, inputSource, layout);
}
