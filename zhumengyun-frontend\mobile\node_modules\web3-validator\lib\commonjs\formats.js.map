{"version": 3, "file": "formats.js", "sourceRoot": "", "sources": ["../../src/formats.ts"], "names": [], "mappings": ";;AAkBA,wDAAoD;AACpD,oDAAsF;AACtF,oDAAgD;AAChD,wDAAoD;AACpD,oDAAgD;AAChD,sDAAwD;AACxD,sDAA+D;AAC/D,wDAAkE;AAElE,MAAM,OAAO,GAAkD;IAC9D,OAAO,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,IAAA,sBAAS,EAAC,IAAuB,CAAC;IAC9D,KAAK,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,IAAA,kBAAO,EAAC,IAAuB,CAAC;IAC1D,WAAW,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,IAAA,wBAAa,EAAC,IAAgC,CAAC;IAC/E,QAAQ,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,IAAA,qBAAU,EAAC,IAAc,CAAC;IACvD,gBAAgB,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,IAAA,6BAAkB,EAAC,IAAgC,CAAC;IACzF,IAAI,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,IAAA,sBAAS,EAAC,IAAuB,CAAC;IAC3D,KAAK,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,IAAA,kBAAO,EAAC,IAA+C,CAAC;IAClF,MAAM,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,IAAA,0BAAc,EAAC,IAAc,CAAC;IACzD,GAAG,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,IAAA,uBAAW,EAAC,IAAuB,CAAC;IAC5D,IAAI,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,IAAA,mBAAM,EAAC,IAAuB,CAAC;IACxD,GAAG,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,IAAA,kBAAK,EAAC,IAAuB,CAAC;IACtD,MAAM,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,IAAA,qBAAQ,EAAC,IAAuB,CAAC;IAC5D,MAAM,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,IAAA,oBAAQ,EAAC,IAAuB,CAAC;CAC5D,CAAC;AACF,yCAAyC;AACzC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,GAAG,EAAE,OAAO,IAAI,CAAC,EAAE;IACnD,OAAO,CAAC,MAAM,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,IAAA,kBAAK,EAAC,IAAuB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAC/E,OAAO,CAAC,OAAO,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,IAAA,mBAAM,EAAC,IAAuB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;CACjF;AACD,iBAAiB;AACjB,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,IAAI,EAAE,EAAE,IAAI,IAAI,CAAC,EAAE;IACzC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAChC,IAAA,kBAAO,EAAC,IAA+C,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;CACpE;AACD,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC;AAEjC,kBAAe,OAAO,CAAC"}