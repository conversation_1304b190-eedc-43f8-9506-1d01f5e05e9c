import { Group } from 'three';
import { HandleStore, HandleOptions } from '../store.js';
import { HandlesContext } from './context.js';
import { extractHandleTransformOptions } from './utils.js';
export declare class RegisteredHandle extends Group {
    protected readonly context: HandlesContext;
    protected readonly axis: 'x' | 'y' | 'z' | 'xy' | 'yz' | 'xz' | 'xyz' | 'e';
    readonly store: HandleStore<unknown>;
    protected options: Exclude<ReturnType<typeof extractHandleTransformOptions>['options'], false> | undefined;
    protected readonly tag: string;
    constructor(context: HandlesContext, axis: 'x' | 'y' | 'z' | 'xy' | 'yz' | 'xz' | 'xyz' | 'e', tagPrefix: string, getOptions: () => HandleOptions<unknown>);
}
