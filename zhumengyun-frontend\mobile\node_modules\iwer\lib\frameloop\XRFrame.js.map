{"version": 3, "file": "XRFrame.js", "sourceRoot": "", "sources": ["../../src/frameloop/XRFrame.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EACN,QAAQ,EACR,OAAO,EACP,aAAa,EACb,SAAS,EACT,OAAO,GACP,MAAM,eAAe,CAAC;AACvB,OAAO,EAAE,QAAQ,EAAe,MAAM,wBAAwB,CAAC;AAC/D,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAEnD,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAC7D,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAE7C,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAErD,OAAO,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAChD,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAClD,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AAErE,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AAEvD,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACxC,MAAM,qBAAqB,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAC5C,MAAM,4BAA4B,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAEnD,MAAM,eAAe,GAAG,CACvB,YAAkB,EAClB,KAAc,EACd,SAAkB,EACjB,EAAE;IACH,YAAY,CAAC,2BAA2B,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC;IACnE,YAAY,CAAC,2BAA2B,CAAC,SAAS,EAAE,qBAAqB,CAAC,CAAC;IAC3E,IAAI,CAAC,MAAM,CAAC,4BAA4B,EAAE,qBAAqB,CAAC,CAAC;IACjE,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,4BAA4B,EAAE,iBAAiB,CAAC,CAAC;AAC9E,CAAC,CAAC;AAEF,MAAM,OAAO,OAAO;IAcnB,YACC,OAAkB,EAClB,EAAU,EACV,MAAe,EACf,cAAuB,EACvB,oBAA4B;QAE5B,IAAI,CAAC,OAAO,CAAC,GAAG;YACf,OAAO;YACP,EAAE;YACF,MAAM;YACN,cAAc;YACd,oBAAoB;YACpB,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE;YACvB,cAAc,EAAE,IAAI,UAAU,EAAE;YAChC,cAAc,EAAE,IAAI,SAAS,EAAE;YAC/B,cAAc,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,mBAAmB;YACtD,iBAAiB,EAAE,IAAI,GAAG,EAAE;SAC5B,CAAC;IACH,CAAC;IAED,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;IAC9B,CAAC;IAED,IAAI,oBAAoB;QACvB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;IAC3C,CAAC;IAED,OAAO,CAAC,KAAc,EAAE,SAAkB;QACzC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE;YAC1B,MAAM,IAAI,YAAY,CACrB,kEAAkE,EAClE,mBAAmB,CACnB,CAAC;SACF;QACD,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;QAC1D,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC/B,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtD,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAClC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtD,OAAO,IAAI,MAAM,CAChB,IAAI,gBAAgB,CACnB,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,EAC1D;YACC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;YACjB,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;YACjB,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;YACjB,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;SACjB,CACD,EACD,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CACvB,CAAC;IACH,CAAC;IAED,aAAa,CAAC,cAAgC;QAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE;YAClC,MAAM,IAAI,YAAY,CACrB,0GAA0G,EAC1G,mBAAmB,CACnB,CAAC;SACF;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;QACtC,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;QACzC,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QAC9D,MAAM,IAAI,GACT,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,QAAQ;YACnC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;YACd,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QAE9B,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YACpB,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YACzD,MAAM,gBAAgB,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YACrE,MAAM,IAAI,GAAG,IAAI,MAAM,CACtB,GAAG,EACH,IAAI,YAAY,CAAC,gBAAgB,CAAC,EAClC,QAAQ,CAAC,SAAS,EAClB,OAAO,CACP,CAAC;YACF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACvD,CAAC;IAED,YAAY,CAAC,KAAmB,EAAE,SAAkB;QACnD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAC9C,MAAM,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC;QAC3C,OAAO,IAAI,WAAW,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACzD,CAAC;IAED,cAAc,CAAC,WAA2B,EAAE,KAAmB;QAC9D,yCAAyC;QACzC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACtC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE;YAC1B,MAAM,IAAI,YAAY,CACrB,kEAAkE,EAClE,mBAAmB,CACnB,CAAC;SACF;QACD,IAAI,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE;YACtC,MAAM,IAAI,YAAY,CACrB,0EAA0E,EAC1E,WAAW,CACX,CAAC;SACF;QACD,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;YAC3D,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC,CAAC,MAAM,EAAE;gBAC/C,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;gBACpB,QAAQ,GAAG,KAAK,CAAC;aACjB;iBAAM;gBACN,KAAK,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC;aAC1D;SACD;QACD,OAAO,QAAQ,CAAC;IACjB,CAAC;IAED,SAAS,CAAC,MAAiB,EAAE,SAAkB,EAAE,UAAwB;QACxE,yCAAyC;QACzC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE;YAC1B,MAAM,IAAI,YAAY,CACrB,kEAAkE,EAClE,mBAAmB,CACnB,CAAC;SACF;QACD,IAAI,MAAM,CAAC,MAAM,GAAG,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE;YAC3C,MAAM,IAAI,YAAY,CACrB,2FAA2F,EAC3F,WAAW,CACX,CAAC;SACF;QACD,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;YAC3B,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;YAC1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;gBAC5B,UAAU,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;aACnD;QACF,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACb,CAAC;IAED,IAAI,cAAc;QACjB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE;YAC1B,MAAM,IAAI,YAAY,CACrB,kEAAkE,EAClE,mBAAmB,CACnB,CAAC;SACF;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC;IACrC,CAAC;IAED,IAAI,cAAc;QACjB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE;YAC1B,MAAM,IAAI,YAAY,CACrB,kEAAkE,EAClE,mBAAmB,CACnB,CAAC;SACF;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC;IACrC,CAAC;IAED,IAAI,cAAc;QACjB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE;YAC1B,MAAM,IAAI,YAAY,CACrB,kEAAkE,EAClE,mBAAmB,CACnB,CAAC;SACF;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC;IACrC,CAAC;IAED,YAAY,CAAC,IAAsB,EAAE,KAAc;QAClD,OAAO,IAAI,OAAO,CAAW,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAChD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE;gBAC1B,MAAM,CACL,IAAI,YAAY,CACf,kEAAkE,EAClE,mBAAmB,CACnB,CACD,CAAC;aACF;iBAAM;gBACN,MAAM,WAAW,GAChB,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC;gBAC/D,MAAM,SAAS,GAAG,IAAI,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBAClD,MAAM,kBAAkB,GACvB,YAAY,CAAC,2BAA2B,CAAC,SAAS,CAAC,CAAC;gBACrD,MAAM,WAAW,GAAG,IAAI,OAAO,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;gBACjE,MAAM,MAAM,GAAG,IAAI,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC;gBAChE,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAC5D,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE;oBACvD,OAAO;oBACP,MAAM;iBACN,CAAC,CAAC;aACH;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,iBAAiB,CAAC,aAA8B;QAC/C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE;YAC1B,MAAM,IAAI,YAAY,CACrB,kEAAkE,EAClE,mBAAmB,CACnB,CAAC;SACF;aAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;YAC/D,MAAM,IAAI,YAAY,CACrB,iEAAiE,EACjE,mBAAmB,CACnB,CAAC;SACF;aAAM;YACN,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,aAAa,CAAE,CAAC,CAAC;SAChE;IACF,CAAC;CACD"}