/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_TRACKED_INPUT } from '../private.js';
import { Quaternion, Vector3 } from '../utils/Math.js';
import { XRInputSource } from '../input/XRInputSource.js';
import { XRFrame } from '../frameloop/XRFrame.js';
export declare class XRTrackedInput {
    [P_TRACKED_INPUT]: {
        inputSource: XRInputSource;
        position: Vector3;
        quaternion: Quaternion;
        connected: boolean;
        lastFrameConnected: boolean;
        inputSourceChanged: boolean;
    };
    constructor(inputSource: XRInputSource);
    get position(): Vector3;
    get quaternion(): Quaternion;
    get inputSource(): XRInputSource;
    get connected(): boolean;
    set connected(value: boolean);
    onFrameStart(frame: XRFrame): void;
}
//# sourceMappingURL=XRTrackedInput.d.ts.map