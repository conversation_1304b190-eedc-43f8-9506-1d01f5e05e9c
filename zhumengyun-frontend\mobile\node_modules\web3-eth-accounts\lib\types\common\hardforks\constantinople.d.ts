declare const _default: {
    name: string;
    comment: string;
    url: string;
    status: string;
    gasConfig: {};
    gasPrices: {
        netSstoreNoopGas: {
            v: number;
            d: string;
        };
        netSstoreInitGas: {
            v: number;
            d: string;
        };
        netSstoreCleanGas: {
            v: number;
            d: string;
        };
        netSstoreDirtyGas: {
            v: number;
            d: string;
        };
        netSstoreClearRefund: {
            v: number;
            d: string;
        };
        netSstoreResetRefund: {
            v: number;
            d: string;
        };
        netSstoreResetClearRefund: {
            v: number;
            d: string;
        };
        shl: {
            v: number;
            d: string;
        };
        shr: {
            v: number;
            d: string;
        };
        sar: {
            v: number;
            d: string;
        };
        extcodehash: {
            v: number;
            d: string;
        };
        create2: {
            v: number;
            d: string;
        };
    };
    vm: {};
    pow: {
        minerReward: {
            v: string;
            d: string;
        };
        difficultyBombDelay: {
            v: number;
            d: string;
        };
    };
};
export default _default;
//# sourceMappingURL=constantinople.d.ts.map