{"version": 3, "file": "encoding.js", "sourceRoot": "", "sources": ["../../src/encoding.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AAEF,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AAE1D,OAAO,EAON,UAAU,EACV,SAAS,GAET,MAAM,YAAY,CAAC;AAEpB,OAAO,EACN,kBAAkB,EAClB,oBAAoB,EACpB,oBAAoB,EACpB,uBAAuB,EACvB,eAAe,EACf,gBAAgB,EAChB,6BAA6B,EAC7B,wBAAwB,EACxB,2BAA2B,GAC3B,MAAM,cAAc,CAAC;AAEtB,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AACnD,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAEhD,OAAO,EAAE,cAAc,EAAE,MAAM,UAAU,CAAC;AAG1C,MAAM,CAAC,MAAM,cAAc,GAAG,CAC7B,EAAE,OAAO,EAAmB,EAC5B,KAA+C,EAC/C,OAAgB,EACf,EAAE;;IACH,MAAM,MAAM,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,CAAC;IAC/B,MAAM,MAAM,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,mCAAI,EAAE,CAAC;IACrC,MAAM,IAAI,GAAsB,EAAE,CAAC;IAEnC,IAAI,CAAC,SAAS,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,CAAC,EAAE,CAAC;QACpC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,EAAE;YAC1E,MAAM,EAAE,UAAU,CAAC,GAAG;YACtB,KAAK,EAAE,SAAS,CAAC,GAAG;SACpB,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,CAAC,SAAS,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,CAAC,EAAE,CAAC;QAClC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,EAAE;YACtE,MAAM,EAAE,UAAU,CAAC,GAAG;YACtB,KAAK,EAAE,SAAS,CAAC,GAAG;SACpB,CAAC,CAAC;IACJ,CAAC;IAED,IAAI,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QACrC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAY,CAAC;IACtC,CAAC;SAAM,CAAC;QACP,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,sBAAsB;QACtB,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAClF,IAAI,CAAC,MAAM,CAAC,IAAI,CACf,MAAA,KAAK,CAAC,SAAS,mCAAI,oBAAoB,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC,CAC3E,CAAC;QACH,CAAC;QAED,uCAAuC;QACvC,IAAI,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YACrE,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBAClC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;oBACpB,SAAS;gBACV,CAAC;gBAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACjC,IAAI,CAAC,KAAK,EAAE,CAAC;oBACZ,2CAA2C;oBAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACvB,SAAS;gBACV,CAAC;gBAED,uDAAuD;gBACvD,sCAAsC;gBACtC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClE,CAAC;qBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAe,CAAC,CAAC,CAAC;gBAC9C,CAAC;qBAAM,CAAC;oBACP,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;gBACtD,CAAC;YACF,CAAC;QACF,CAAC;IACF,CAAC;IAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;QAAE,OAAO,IAAI,CAAC,MAAM,CAAC;IAE5C,IAAI,OAAO,EAAE,CAAC;QACb,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IACtC,CAAC;IAED,OAAO,IAAI,CAAC;AACb,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAG,CAC9B,GAAiD,EACjD,IAAe,EACf,UAAsB,EACrB,EAAE;IACH,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACtE,IAAI,GAAG,CAAC,MAAM,IAAI,WAAW,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;QAC/C,MAAM,IAAI,iBAAiB,CAC1B,yFAAyF,WAAW,aAAa,CACjH,CAAC;IACH,CAAC;IAED,IAAI,MAAc,CAAC;IACnB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;QAChB,MAAM,GAAG,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,OAAO,CACnF,IAAI,EACJ,EAAE,CACF,CAAC;IACH,CAAC;SAAM,CAAC;QACP,MAAM,GAAG,6BAA6B,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAChE,CAAC;IAED,IAAI,wBAAwB,CAAC,GAAG,CAAC,EAAE,CAAC;QACnC,IAAI,CAAC,UAAU;YACd,MAAM,IAAI,iBAAiB,CAC1B,uGAAuG,CACvG,CAAC;QAEH,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,OAAO,KAAK,UAAU,GAAG,MAAM,EAAE,CAAC;QACnC,CAAC;QAED,OAAO,GAAG,UAAU,GAAG,MAAM,EAAE,CAAC;IACjC,CAAC;IAED,OAAO,GAAG,uBAAuB,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE,CAAC;AACnD,CAAC,CAAC;AAEF,4EAA4E;AAC5E,MAAM,CAAC,MAAM,kBAAkB,GAAG,kBAAkB,CAAC;AACrD,8EAA8E;AAC9E,MAAM,CAAC,MAAM,kBAAkB,GAAG,oBAAoB,CAAC"}