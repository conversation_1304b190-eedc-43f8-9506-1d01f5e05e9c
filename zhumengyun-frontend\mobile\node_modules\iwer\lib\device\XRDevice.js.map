{"version": 3, "file": "XRDevice.js", "sourceRoot": "", "sources": ["../../src/device/XRDevice.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EACN,QAAQ,EACR,WAAW,EACX,SAAS,EACT,OAAO,EACP,QAAQ,GACR,MAAM,eAAe,CAAC;AACvB,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,kBAAkB,CAAC;AACvD,OAAO,EAAE,YAAY,EAAsB,MAAM,mBAAmB,CAAC;AACrE,OAAO,EAGN,SAAS,GAGT,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EAAE,WAAW,EAAE,gBAAgB,EAAE,MAAM,kBAAkB,CAAC;AACjE,OAAO,EACN,YAAY,EACZ,aAAa,EACb,kBAAkB,GAClB,MAAM,2BAA2B,CAAC;AACnC,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AAClE,OAAO,EACN,gBAAgB,EAChB,oBAAoB,GACpB,MAAM,+BAA+B,CAAC;AACvC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAEvC,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AAEzD,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AACxC,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,kBAAkB,EAAE,MAAM,iCAAiC,CAAC;AACrE,OAAO,EAAE,yBAAyB,EAAE,MAAM,wCAAwC,CAAC;AACnF,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AACzD,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,qBAAqB,EAAE,MAAM,oCAAoC,CAAC;AAC3E,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAC5D,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AACrE,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAC7D,OAAO,EAAE,QAAQ,EAAE,MAAM,+BAA+B,CAAC;AAEzD,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AACvD,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AA0CpD,MAAM,QAAQ,GAAG;IAChB,GAAG,EAAE,KAAK;IACV,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC;IACjB,eAAe,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IACvC,iBAAiB,EAAE,IAAI,UAAU,EAAE;IACnC,aAAa,EAAE,KAAK;CACpB,CAAC;AA6BF,MAAM,kBAAkB,GAAG,CAAC,CAAC;AAC7B,MAAM,kBAAkB,GAAG,CAAC,CAAC;AAC7B,MAAM,oBAAoB,GAAG,CAAC,CAAC;AAC/B,MAAM,uBAAuB,GAAG,CAAC,CAAC;AAElC;;;GAGG;AACH,MAAM,OAAO,QAAQ;IA2DpB,YACC,YAA4B,EAC5B,gBAA0C,EAAE;;QA5D7B,YAAO,GAAG,OAAO,CAAC;QA8DjC,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;QACtC,MAAM,WAAW,GAAG,IAAI,gBAAgB,CACvC,oBAAoB,CAAC,MAAM,EAC3B,WAAW,CACX,CAAC;QACF,MAAM,UAAU,GAAgC;YAC/C,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,OAAO,CAAC,WAAW,CAAC;YACtC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,OAAO,CAAC,WAAW,CAAC;YACvC,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,OAAO,CAAC,WAAW,CAAC;SACtC,CAAC;QACF,MAAM,gBAAgB,GAAG,YAAY,CAAC,gBAAgB,CAAC;QACvD,MAAM,WAAW,GAA6C,EAAE,CAAC;QACjE,IAAI,gBAAgB,EAAE;YACrB,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAClD,IAAI,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE;oBACxC,WAAW,CAAC,UAAU,CAAC,GAAG,IAAI,YAAY,CACzC,gBAAgB,EAChB,UAAU,EACV,WAAW,CACX,CAAC;iBACF;YACF,CAAC,CAAC,CAAC;SACH;QACD,MAAM,KAAK,GAAG;YACb,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,WAAW,CACnC,gBAAgB,EAChB,YAAY,CAAC,IAAI,EACjB,WAAW,CACX;YACD,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,WAAW,CACpC,gBAAgB,EAChB,YAAY,CAAC,KAAK,EAClB,WAAW,CACX;SACD,CAAC;QACF,MAAM,eAAe,GACpB,MAAA,aAAa,CAAC,eAAe,mCAAI,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAChE,eAAe,CAAC,OAAO,CAAC,aAAa,GAAG,oCAAoC,OAAO,EAAE,CAAC;QACtF,eAAe,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;QACzC,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC;QACrC,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QACtC,eAAe,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;QAChC,eAAe,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC;QACjC,eAAe,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;QACvC,eAAe,CAAC,KAAK,CAAC,cAAc,GAAG,QAAQ,CAAC;QAChD,eAAe,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;QAC5C,eAAe,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC1C,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC;QAErC,IAAI,CAAC,QAAQ,CAAC,GAAG;YAChB,IAAI,EAAE,YAAY,CAAC,IAAI;YACvB,qBAAqB,EAAE,YAAY,CAAC,qBAAqB;YACzD,iBAAiB,EAAE,YAAY,CAAC,iBAAiB;YACjD,mBAAmB,EAAE,YAAY,CAAC,mBAAmB;YACrD,yBAAyB,EAAE,YAAY,CAAC,yBAAyB;YACjE,wBAAwB,EAAE,YAAY,CAAC,wBAAwB;YAC/D,qBAAqB,EAAE,YAAY,CAAC,qBAAqB;YACzD,eAAe,EAAE,YAAY,CAAC,eAAe;YAC7C,SAAS,EAAE,YAAY,CAAC,SAAS;YAEjC,QAAQ,EACP,MAAA,aAAa,CAAC,eAAe,mCAAI,QAAQ,CAAC,eAAe,CAAC,KAAK,EAAE;YAClE,UAAU,EACT,MAAA,aAAa,CAAC,iBAAiB,mCAAI,QAAQ,CAAC,iBAAiB,CAAC,KAAK,EAAE;YACtE,aAAa,EAAE,MAAA,aAAa,CAAC,aAAa,mCAAI,QAAQ,CAAC,aAAa;YACpE,GAAG,EAAE,MAAA,aAAa,CAAC,GAAG,mCAAI,QAAQ,CAAC,GAAG;YACtC,IAAI,EAAE,MAAA,aAAa,CAAC,IAAI,mCAAI,QAAQ,CAAC,IAAI;YACzC,WAAW;YACX,KAAK;YACL,gBAAgB,EAAE,YAAY;YAC9B,0BAA0B,EAAE,KAAK;YACjC,eAAe,EAAE,SAAS;YAC1B,sBAAsB,EAAE,IAAI;YAC5B,QAAQ,EAAE,IAAI;YAEd,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE;YACrB,WAAW;YACX,WAAW;YACX,UAAU;YACV,eAAe;YAEf,WAAW,EAAE,CAAC,KAAmB,EAAE,IAAY,EAAE,EAAE;gBAClD,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBACpC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;gBACjC,QAAQ,IAAI,CAAC,GAAG,EAAE;oBACjB,KAAK,KAAK,CAAC,IAAI;wBACd,OAAO,IAAI,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;oBAC5C,KAAK,KAAK,CAAC,IAAI;wBACd,OAAO,IAAI,UAAU,CACpB,CAAC,EACD,CAAC,EACD,IAAI,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAChD,MAAM,CACN,CAAC;oBACH,KAAK,KAAK,CAAC,KAAK;wBACf,OAAO,IAAI,UAAU,CACpB,KAAK,GAAG,CAAC,EACT,CAAC,EACD,IAAI,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAC5C,MAAM,CACN,CAAC;iBACH;YACF,CAAC;YACD,WAAW,EAAE,GAAG,EAAE;gBACjB,qBAAqB;gBACrB,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC;gBAC/C,IAAI,CAAC,uBAAuB,CAC3B,WAAW,CAAC,OAAO,CAAC,CAAC,YAAY,EACjC,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,IAAI,EAC9B,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAC5B,CAAC;gBAEF,oBAAoB;gBACpB,IAAI,CAAC,eAAe,CACnB,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY,EAC3D,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAC9C,CAAC;gBACF,IAAI,CAAC,eAAe,CACnB,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY,EAC5D,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAC7C,CAAC;YACH,CAAC;YACD,cAAc,EAAE,CAAC,SAA8B,EAAE,EAAE;gBAClD,IAAI,CAAC,SAAS;oBAAE,OAAO;gBAEvB,qBAAqB;gBACrB,MAAM,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC,MAA2B,CAAC;gBAC7D,IAAI,MAAM,CAAC,aAAa,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,eAAe,EAAE;oBAC5D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC;oBACnC,IAAI,KAAK,EAAE;wBACV,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,KAAK,CAAC;wBAC9C,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,oBAAoB,CAAC,QAAQ,EAAE,CAAC;wBAC3D,cAAc,CAAC,KAAK,CAAC,MAAM,GAAG,uBAAuB,CAAC,QAAQ,EAAE,CAAC;wBACjE,IAAI,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;wBAC9D,IAAI,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;qBACjE;oBACD,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC;oBAC/B,IAAI,GAAG,EAAE;wBACR,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,GAAG,kBAAkB,CAAC,QAAQ,EAAE,CAAC;wBACnE,IAAI,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;qBAClE;oBACD,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,GAAG;wBAC3B,MAAM;wBACN,MAAM,EAAE,MAAM,CAAC,aAAa;wBAC5B,KAAK,EAAE,MAAM,CAAC,KAAK;wBACnB,MAAM,EAAE,MAAM,CAAC,MAAM;wBACrB,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM;qBAC3B,CAAC;oBACF,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,kBAAkB,CAAC,QAAQ,EAAE,CAAC;oBACpD,IAAI,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;oBACnD,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;iBAC1D;gBAED,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC;gBACjC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC;YACpC,CAAC;YACD,YAAY,EAAE,GAAG,EAAE;gBAClB,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,EAAE;oBAC9B,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,GAC9C,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC;oBAC3B,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;oBACrB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;oBACvB,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;oBAC7B,IAAI,MAAM,EAAE;wBACX,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;qBAC3B;yBAAM;wBACN,IAAI,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;qBACnD;oBACD,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC;oBACnC,IAAI,KAAK,EAAE;wBACV,IAAI,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;wBAC9D,IAAI,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;qBACjE;oBACD,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC;oBAC/B,IAAI,GAAG,EAAE;wBACR,IAAI,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;qBAClE;oBACD,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;oBAC1D,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,GAAG,SAAS,CAAC;oBACtC,MAAM,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;iBAC1C;YACF,CAAC;YACD,YAAY,EAAE,CAAC,KAAc,EAAE,EAAE;;gBAChC,IAAI,MAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,YAAY,0CAAE,OAAO,EAAE;oBACzC,IAAI,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;iBACxC;qBAAM;oBACN,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;oBAC9B,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;oBAE7B,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,sBAAsB,EAAE;wBAC1C,IAAI,CAAC,QAAQ,CAAC,CAAC,eAAe;4BAC7B,IAAI,CAAC,QAAQ,CAAC,CAAC,sBAAsB,CAAC;wBACvC,IAAI,CAAC,QAAQ,CAAC,CAAC,sBAAsB,GAAG,IAAI,CAAC;wBAC7C,OAAO,CAAC,aAAa,CACpB,IAAI,cAAc,CAAC,kBAAkB,EAAE,EAAE,OAAO,EAAE,CAAC,CACnD,CAAC;qBACF;oBACD,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,eAAe,KAAK,SAAS,EAAE;wBACjD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE;4BACzC,WAAW,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;wBACjC,CAAC,CAAC,CAAC;qBACH;oBAED,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,0BAA0B,EAAE;wBAC9C,OAAO,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;4BAC7D,QAAQ,cAAc,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE;gCACzC,KAAK,oBAAoB,CAAC,KAAK,CAAC;gCAChC,KAAK,oBAAoB,CAAC,UAAU,CAAC;gCACrC,KAAK,oBAAoB,CAAC,YAAY,CAAC;gCACvC,KAAK,oBAAoB,CAAC,SAAS;oCAClC,cAAc,CAAC,aAAa,CAC3B,IAAI,qBAAqB,CAAC,OAAO,EAAE,EAAE,cAAc,EAAE,CAAC,CACtD,CAAC;oCACF,MAAM;6BACP;wBACF,CAAC,CAAC,CAAC;wBACH,IAAI,CAAC,QAAQ,CAAC,CAAC,0BAA0B,GAAG,KAAK,CAAC;qBAClD;iBACD;gBAED,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9B,CAAC;SACD,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAC7B,UAAU,CAAC;IACZ,CAAC;IAED,cAAc,CAAC,eAAoB,UAAU;QAC5C,MAAM,CAAC,cAAc,CACpB,sBAAsB,CAAC,SAAS,EAChC,kBAAkB,EAClB;YACC,KAAK,EAAE;gBACN,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;oBACvC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC;YACJ,CAAC;YACD,YAAY,EAAE,IAAI;SAClB,CACD,CAAC;QACF,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC7C,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,EAAE,IAAI,EAAE;YACjD,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ;YAC9B,YAAY,EAAE,IAAI;SAClB,CAAC,CAAC;QACH,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,EAAE;YAC7C,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,SAAS;YAC/B,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,KAAK;YACnB,UAAU,EAAE,IAAI;SAChB,CAAC,CAAC;QACH,YAAY,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;QACpC,YAAY,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC;QACtC,YAAY,CAAC,eAAe,CAAC,GAAG,aAAa,CAAC;QAC9C,YAAY,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;QAClC,YAAY,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;QAClC,YAAY,CAAC,kBAAkB,CAAC,GAAG,gBAAgB,CAAC;QACpD,YAAY,CAAC,cAAc,CAAC,GAAG,YAAY,CAAC;QAC5C,YAAY,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;QAChC,YAAY,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC;QACxC,YAAY,CAAC,kBAAkB,CAAC,GAAG,gBAAgB,CAAC;QACpD,YAAY,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;QAChC,YAAY,CAAC,cAAc,CAAC,GAAG,YAAY,CAAC;QAC5C,YAAY,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC;QAC1C,YAAY,CAAC,eAAe,CAAC,GAAG,aAAa,CAAC;QAC9C,YAAY,CAAC,oBAAoB,CAAC,GAAG,kBAAkB,CAAC;QACxD,YAAY,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;QAChC,YAAY,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;QAClC,YAAY,CAAC,cAAc,CAAC,GAAG,YAAY,CAAC;QAC5C,YAAY,CAAC,gBAAgB,CAAC,GAAG,cAAc,CAAC;QAChD,YAAY,CAAC,oBAAoB,CAAC,GAAG,kBAAkB,CAAC;QACxD,YAAY,CAAC,2BAA2B,CAAC,GAAG,yBAAyB,CAAC;QACtE,YAAY,CAAC,uBAAuB,CAAC,GAAG,qBAAqB,CAAC;IAC/D,CAAC;IAED,YAAY,CAAC,gBAAkC;QAC9C,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACnD,CAAC;IAED,UAAU,CAAC,cAA8B;QACxC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED,IAAI,qBAAqB;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,qBAAqB,CAAC;IAC7C,CAAC;IAED,IAAI,iBAAiB;QACpB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,iBAAiB,CAAC;IACzC,CAAC;IAED,IAAI,mBAAmB;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,mBAAmB,CAAC;IAC3C,CAAC;IAED,IAAI,yBAAyB;QAC5B,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,yBAAyB,CAAC;IACjD,CAAC;IAED,IAAI,wBAAwB;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,wBAAwB,CAAC;IAChD,CAAC;IAED,IAAI,aAAa;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC;IACrC,CAAC;IAED,IAAI,aAAa,CAAC,KAAc;QAC/B,IAAI,CAAC,QAAQ,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC;IACtC,CAAC;IAED,IAAI,GAAG;QACN,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC;IAC3B,CAAC;IAED,IAAI,GAAG,CAAC,KAAa;QACpB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED,IAAI,IAAI,CAAC,KAAa;QACrB,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC;IAC7B,CAAC;IAED,IAAI,QAAQ;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC;IAChC,CAAC;IAED,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC;IAClC,CAAC;IAED,IAAI,WAAW;;QACd,IAAI,MAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,YAAY,0CAAE,OAAO,EAAE;YACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC;SAC/C;aAAM;YACN,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC;SAClC;IACF,CAAC;IAED,IAAI,UAAU;;QACb,IAAI,MAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,YAAY,0CAAE,OAAO,EAAE;YACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC;SAC9C;aAAM;YACN,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC;SACjC;IACF,CAAC;IAED,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC;IACnC,CAAC;IAED,IAAI,KAAK;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED,IAAI,gBAAgB;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,gBAAgB,CAAC;IACxC,CAAC;IAED,IAAI,gBAAgB,CAAC,IAA2B;QAC/C,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,MAAM,EAAE;YAC7C,OAAO,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;YACtE,OAAO;SACP;QACD,IAAI,CAAC,QAAQ,CAAC,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACxC,CAAC;IAED,IAAI,YAAY;QACf,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,eAAe,KAAK,SAAS,EAAE;YACjD,OAAO,EAAE,CAAC;SACV;QACD,MAAM,YAAY,GACjB,IAAI,CAAC,QAAQ,CAAC,CAAC,gBAAgB,KAAK,YAAY;YAC/C,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC;YAC3C,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC;QACxC,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IACxD,CAAC;IAED,IAAI,YAAY;;QACf,IAAI,MAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,YAAY,0CAAE,OAAO,EAAE;YACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC;SAChD;aAAM;YACN,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;SAC3D;IACF,CAAC;IAED,IAAI,eAAe;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC;IACvC,CAAC;IAED,IAAI,gBAAgB;QACnB,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,EAAE;YAC9B,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC;YAC3D,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;SACzB;QACD,OAAO;IACR,CAAC;IAED,IAAI,aAAa;;QAChB,OAAO,MAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,0CAAG,QAAQ,EAAE,aAAa,CAAC;IAC1D,CAAC;IAED,IAAI,cAAc;;QACjB,OAAO,OAAO,CAAC,MAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,0CAAG,QAAQ,EAAE,oBAAoB,CAAC,CAAC;IAC1E,CAAC;IAED,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED,mBAAmB;;QAClB,MAAM,OAAO,GAAG,MAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,0CAAG,QAAQ,CAAC,CAAC;QACpD,IAAI,OAAO,IAAI,OAAO,CAAC,oBAAoB,EAAE;YAC5C,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;YACnD,OAAO,CAAC,oBAAoB,GAAG,SAAS,CAAC;SACzC;IACF,CAAC;IAED,QAAQ;QACP,MAAM,QAAQ,GAAG,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACpE,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,OAAO,CAAC,SAAS,EAAE,CAAC;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChD,MAAM,SAAS,GAAG,IAAI,UAAU,EAAE,CAAC,gBAAgB,CAClD,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACpB,KAAK,CACL,CAAC;QACF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC5B,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAEpC;YACC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC;YAC5C,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC;SACtC,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE;YACzB,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACnC,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAC3C,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,CAAC,0BAA0B,GAAG,IAAI,CAAC;IAClD,CAAC;IAED,IAAI,eAAe;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC;IACvC,CAAC;IAED,0EAA0E;IAC1E,qBAAqB,CAAC,KAAwB;QAC7C,IACC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,iBAAiB,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EACvE;YACD,MAAM,IAAI,YAAY,CACrB,iCAAiC,EACjC,mBAAmB,CACnB,CAAC;SACF;QACD,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,eAAe,EAAE;YAC7C,IAAI,CAAC,QAAQ,CAAC,CAAC,sBAAsB,GAAG,KAAK,CAAC;SAC9C;IACF,CAAC;IAED,kBAAkB,CACjB,QAA0B,EAC1B,SAMC;QAED,IAAI,CAAC,QAAQ,CAAC,CAAC,YAAY,GAAG,IAAI,YAAY,CAC7C,QAAQ,EACR,SAAS,EACT,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAClB,CAAC;QACF,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC;IACpC,CAAC;IAED,IAAI,KAAK;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED,IAAI,GAAG;QACN,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC;IAC3B,CAAC;CACD"}