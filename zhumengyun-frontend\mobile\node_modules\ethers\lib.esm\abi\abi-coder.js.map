{"version": 3, "file": "abi-coder.js", "sourceRoot": "", "sources": ["../../src.ts/abi/abi-coder.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;GAWG;AAEH,mEAAmE;AAEnE,OAAO,EAAE,mBAAmB,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAExE,OAAO,EAAS,MAAM,EAAU,MAAM,EAAE,MAAM,4BAA4B,CAAC;AAC3E,OAAO,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AACnD,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAC/C,OAAO,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AACnD,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAC/C,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAC;AAC1D,OAAO,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AACjD,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AACjD,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAC/C,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAE3C,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAOjE,mEAAmE;AACnE,MAAM,YAAY,GAAwB,IAAI,GAAG,EAAE,CAAC;AACpD,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;AACxC,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;AACvC,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AACnC,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;AACzC,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;AAC3C,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;AAC3C,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;AAC1C,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;AAC5C,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;AACxC,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,6BAA6B,CAAC,CAAC;AAEtD,MAAM,cAAc,GAAG,IAAI,MAAM,CAAC,iBAAiB,CAAC,CAAC;AACrD,MAAM,eAAe,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAGxD,IAAI,YAAY,GAAoB,IAAI,CAAC;AACzC,IAAI,mBAAmB,GAAG,IAAI,CAAC;AAE/B,SAAS,uBAAuB,CAAC,MAA2B,EAAE,EAA+D,EAAE,IAAsB,EAAE,QAAkB;IACrK,IAAI,OAAO,GAAG,qBAAqB,CAAC;IAEpC,IAAI,MAAM,GAAkB,IAAI,CAAC;IACjC,MAAM,UAAU,GAAG,IAAI,CAAC;IACxB,IAAI,MAAM,GAAiE,IAAI,CAAC;IAEhF,IAAI,IAAI,EAAE;QACN,OAAO,GAAG,oBAAoB,CAAC;QAE/B,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAErB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,OAAO,IAAI,mDAAmD,CAAC;YAC/D,MAAM,GAAG,gBAAgB,CAAC;SAE7B;aAAM,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE;YAChC,OAAO,IAAI,iDAAiD,CAAC;SAEhE;aAAM,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,YAAY,EAAE;YACpD,gBAAgB;YAChB,IAAI;gBACA,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAE,QAAQ,CAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;gBACzD,MAAM,GAAG;oBACL,SAAS,EAAE,eAAe;oBAC1B,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,CAAE,MAAM,CAAE;iBACnB,CAAC;gBACF,OAAO,IAAI,KAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAE,EAAE,CAAC;aAE9C;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,IAAI,iDAAiD,CAAC;aAChE;SAEJ;aAAM,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,YAAY,EAAE;YACpD,iBAAiB;YACjB,IAAI;gBACA,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAE,SAAS,CAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvE,MAAM,GAAG;oBACL,SAAS,EAAE,gBAAgB;oBAC3B,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,CAAE,IAAI,CAAE;iBACjB,CAAC;gBACF,MAAM,GAAG,gBAAiB,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,SAAU,IAAK,IAAK,GAAG,CAAC;gBAC5E,OAAO,IAAI,KAAM,MAAO,EAAE,CAAC;aAC9B;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,IAAI,gCAAgC,CAAC;aAC/C;SACJ;aAAM;YACH,OAAO,IAAI,yBAAyB,CAAC;SACxC;KACJ;IAED,MAAM,WAAW,GAA6B;QAC1C,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA,CAAC,CAAC,IAAI,CAAC;QACrC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,IAAI,IAAI,CAAC;KAC1B,CAAC;IACF,IAAI,EAAE,CAAC,IAAI,EAAE;QAAE,WAAW,CAAC,IAAI,GAAG,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;KAAE;IAExD,OAAO,SAAS,CAAC,OAAO,EAAE,gBAAgB,EAAE;QACxC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM;KACxD,CAAC,CAAC;AACP,CAAC;AAED;;;GAGG;AACH,MAAM,OAAO,QAAQ;IAEjB,SAAS,CAAC,KAAgB;QACtB,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE;YACjB,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;SAC7F;QAED,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE;YACjB,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;SACrF;QAED,QAAQ,KAAK,CAAC,QAAQ,EAAE;YACpB,KAAK,SAAS;gBACV,OAAO,IAAI,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACxC,KAAK,MAAM;gBACP,OAAO,IAAI,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACxC,KAAK,QAAQ;gBACT,OAAO,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACvC,KAAK,OAAO;gBACR,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACtC,KAAK,EAAE;gBACH,OAAO,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACxC;QAED,cAAc;QACd,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAC9C,IAAI,KAAK,EAAE;YACP,IAAI,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC;YACvC,cAAc,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,EACxD,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO,IAAI,WAAW,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;SACtE;QAED,cAAc;QACd,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QACzC,IAAI,KAAK,EAAE;YACP,IAAI,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,cAAc,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE,EAAE,sBAAsB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YACjF,OAAO,IAAI,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;SAChD;QAED,cAAc,CAAC,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACH,eAAe,CAAC,KAAwC;QACpD,MAAM,MAAM,GAAiB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvF,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAC1C,OAAO,KAAK,CAAC,YAAY,EAAE,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAwC,EAAE,MAA0B;QACvE,mBAAmB,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,8BAA8B,CAAC,CAAC;QAEjF,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzE,MAAM,KAAK,GAAG,CAAC,IAAI,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;QAE5C,MAAM,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;QAC5B,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7B,OAAO,MAAM,CAAC,IAAI,CAAC;IACvB,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,KAAwC,EAAE,IAAe,EAAE,KAAe;QAC7E,MAAM,MAAM,GAAiB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvF,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAC1C,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,mBAAmB,CAAC,CAAC,CAAC;IACtE,CAAC;IAED,MAAM,CAAC,uBAAuB,CAAC,KAAa;QACxC,cAAc,CAAC,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,oCAAoC,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAC5H,mBAAmB,GAAG,KAAK,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,eAAe;QAClB,IAAI,YAAY,IAAI,IAAI,EAAE;YACtB,YAAY,GAAG,IAAI,QAAQ,EAAE,CAAC;SACjC;QACD,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,uBAAuB,CAAC,MAA2B,EAAE,EAA+D,EAAE,IAAsB;QAC/I,OAAO,uBAAuB,CAAC,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,eAAe,EAAE,CAAC,CAAC;IACjF,CAAC;CACJ"}