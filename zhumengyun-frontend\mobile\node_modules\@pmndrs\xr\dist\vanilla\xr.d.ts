import { ForwardEventsOptions, GetCamera } from '@pmndrs/pointer-events';
import { Object3D, WebXRManager } from 'three';
import { XRStore, XRStoreOptions } from '../store.js';
import { DefaultXRControllerOptions, DefaultXRGazeOptions, DefaultXRHandOptions, DefaultXRScreenInputOptions, DefaultXRTransientPointerOptions } from '../default.js';
import type { XRControllerState, XRGazeState, XRHandState, XRScreenInputState, XRTransientPointerState } from '../input.js';
export type XRElementImplementationCleanup = (() => void) | void;
export type XRUpdatesList = Array<(frame: XRFrame, delta: number) => void>;
export type XRElementImplementations = {
    hand: ((store: XRStore<XRElementImplementations>, handSpace: Object3D, state: XRHandState, session: XRSession) => XRElementImplementationCleanup) | boolean | DefaultXRHandOptions;
    gaze: ((store: XRStore<XRElementImplementations>, handSpace: Object3D, state: XRGazeState, session: XRSession) => XRElementImplementationCleanup) | boolean | DefaultXRGazeOptions;
    screenInput: ((store: XRStore<XRElementImplementations>, handSpace: Object3D, state: XRScreenInputState, session: XRSession) => XRElementImplementationCleanup) | boolean | DefaultXRScreenInputOptions;
    transientPointer: ((store: XRStore<XRElementImplementations>, handSpace: Object3D, state: XRTransientPointerState, session: XRSession) => XRElementImplementationCleanup) | boolean | DefaultXRTransientPointerOptions;
    controller: ((store: XRStore<XRElementImplementations>, controllerSpace: Object3D, state: XRControllerState, session: XRSession) => XRElementImplementationCleanup) | boolean | DefaultXRControllerOptions;
    detectedPlane: ((store: XRStore<XRElementImplementations>, detectedPlaneSpace: Object3D, plane: XRPlane, session: XRSession) => XRElementImplementationCleanup) | false;
    detectedMesh: ((store: XRStore<XRElementImplementations>, detecedMeshSpace: Object3D, mesh: XRMesh, session: XRSession) => XRElementImplementationCleanup) | false;
};
export declare function createXRStore(canvas: HTMLCanvasElement, scene: Object3D, getCamera: GetCamera, xr: WebXRManager, options?: XRStoreOptions<XRElementImplementations> & {
    htmlInput?: ForwardEventsOptions | false;
}): Omit<import("zustand/vanilla").StoreApi<Readonly<{
    body?: XRBody;
    session?: XRSession;
    mediaBinding?: XRMediaBinding;
    originReferenceSpace?: XRReferenceSpace;
    origin?: Object3D;
    domOverlayRoot?: Element;
    visibilityState?: XRVisibilityState;
    frameRate?: number;
    mode: XRSessionMode | null;
    inputSourceStates: ReadonlyArray<import("../input.js").XRInputSourceState>;
    detectedPlanes: ReadonlyArray<XRPlane>;
    detectedMeshes: ReadonlyArray<XRMesh>;
    layerEntries: ReadonlyArray<import("../layer.js").XRLayerEntry>;
    emulator?: import("iwer").XRDevice;
} & import("../store.js").WithRecord<XRElementImplementations>>>, "destroy"> & {
    addLayerEntry(entry: import("../layer.js").XRLayerEntry): void;
    removeLayerEntry(entry: import("../layer.js").XRLayerEntry): void;
    setWebXRManager(xr: WebXRManager): void;
    onBeforeFrame(scene: Object3D, camera: import("three").Camera, frame: XRFrame | undefined): void;
    onBeforeRender(): void;
    destroy(): void;
    enterXR(mode: XRSessionMode): Promise<XRSession | undefined>;
    enterAR(): Promise<XRSession | undefined>;
    enterVR(): Promise<XRSession | undefined>;
    setHand(implementation: boolean | DefaultXRHandOptions | ((store: XRStore<XRElementImplementations>, handSpace: Object3D, state: XRHandState, session: XRSession) => XRElementImplementationCleanup), handedness?: XRHandedness): void;
    setController(implementation: boolean | DefaultXRControllerOptions | ((store: XRStore<XRElementImplementations>, controllerSpace: Object3D, state: XRControllerState, session: XRSession) => XRElementImplementationCleanup), handedness?: XRHandedness): void;
    setGaze(implementation: boolean | DefaultXRGazeOptions | ((store: XRStore<XRElementImplementations>, handSpace: Object3D, state: XRGazeState, session: XRSession) => XRElementImplementationCleanup)): void;
    setScreenInput(implementation: boolean | import("@pmndrs/pointer-events").RayPointerOptions | ((store: XRStore<XRElementImplementations>, handSpace: Object3D, state: XRScreenInputState, session: XRSession) => XRElementImplementationCleanup)): void;
    setTransientPointer(implementation: boolean | DefaultXRTransientPointerOptions | ((store: XRStore<XRElementImplementations>, handSpace: Object3D, state: XRTransientPointerState, session: XRSession) => XRElementImplementationCleanup), handedness?: XRHandedness): void;
    setFrameRate(value: import("../store.js").FrameRateOption): void;
    requestFrame(): Promise<XRFrame>;
} & {
    destroy(): void;
    update(frame: XRFrame | undefined, delta: number): void;
};
