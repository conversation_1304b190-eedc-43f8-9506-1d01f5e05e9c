'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import { creatorEconomyService, DigitalAsset } from '@/services/creatorEconomyService'

interface DigitalAssetMarketProps {
  onPurchase?: (asset: DigitalAsset) => void
}

export default function DigitalAssetMarket({ onPurchase }: DigitalAssetMarketProps) {
  const [assets, setAssets] = useState<DigitalAsset[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)
  const [selectedAsset, setSelectedAsset] = useState<DigitalAsset | null>(null)
  const [paymentMethod, setPaymentMethod] = useState<'points' | 'cny'>('cny')

  const categories = [
    { key: 'all', label: '全部', icon: '📁' },
    { key: 'design', label: '设计方案', icon: '🎨' },
    { key: 'model', label: '3D模型', icon: '🏗️' },
    { key: 'document', label: '技术文档', icon: '📄' },
    { key: 'media', label: '多媒体', icon: '🎬' },
    { key: 'course', label: '课程教程', icon: '📚' }
  ]

  useEffect(() => {
    loadDigitalAssets()
  }, [selectedCategory])

  const loadDigitalAssets = async () => {
    try {
      setLoading(true)
      const params = selectedCategory !== 'all' ? { category: selectedCategory } : undefined
      const digitalAssets = await creatorEconomyService.getDigitalAssets(params)
      setAssets(digitalAssets)
    } catch (error) {
      console.error('加载数字资产失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handlePurchase = async () => {
    if (!selectedAsset) return

    try {
      const result = await creatorEconomyService.purchaseDigitalAsset(selectedAsset.id, paymentMethod)
      if (result.success) {
        alert('购买成功！')
        setShowPurchaseModal(false)
        onPurchase?.(selectedAsset)
      } else {
        alert(result.message || '购买失败')
      }
    } catch (error) {
      console.error('购买失败:', error)
      alert('购买失败，请稍后重试')
    }
  }

  const formatPrice = (price: number): string => {
    return `¥${price.toLocaleString()}`
  }

  const getCategoryColor = (category: string): string => {
    const colors = {
      design: 'from-purple-500 to-pink-500',
      model: 'from-blue-500 to-cyan-500',
      document: 'from-green-500 to-emerald-500',
      media: 'from-red-500 to-orange-500',
      course: 'from-yellow-500 to-amber-500'
    }
    return colors[category as keyof typeof colors] || 'from-gray-500 to-gray-600'
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-8 bg-white/20 rounded mb-4"></div>
          <div className="grid grid-cols-2 gap-3">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-64 bg-white/10 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 分类筛选 */}
      <div className="flex space-x-2 overflow-x-auto pb-2">
        {categories.map((category) => (
          <button
            key={category.key}
            onClick={() => setSelectedCategory(category.key)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-all ${
              selectedCategory === category.key
                ? 'bg-blue-500 text-white'
                : 'bg-white/10 text-white/70 hover:bg-white/20'
            }`}
          >
            <span>{category.icon}</span>
            <span>{category.label}</span>
          </button>
        ))}
      </div>

      {/* 数字资产网格 */}
      <div className="grid grid-cols-2 gap-4">
        {assets.map((asset, index) => (
          <motion.div
            key={asset.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white/10 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/20 hover:border-white/40 transition-all duration-300"
            onClick={() => {
              setSelectedAsset(asset)
              setShowPurchaseModal(true)
            }}
          >
            {/* 资产图片 */}
            <div className="relative h-32">
              <Image
                src={asset.thumbnailUrl}
                alt={asset.title}
                fill
                className="object-cover"
              />
              
              {/* 分类标签 */}
              <div className="absolute top-2 left-2">
                <div className={`px-2 py-1 bg-gradient-to-r ${getCategoryColor(asset.category)} rounded-lg`}>
                  <span className="text-xs font-bold text-white">
                    {categories.find(c => c.key === asset.category)?.label}
                  </span>
                </div>
              </div>

              {/* 独家标识 */}
              {asset.isExclusive && (
                <div className="absolute top-2 right-2">
                  <div className="px-2 py-1 bg-yellow-500/80 rounded-lg">
                    <span className="text-xs font-bold text-white">独家</span>
                  </div>
                </div>
              )}

              {/* 版权保护标识 */}
              {asset.copyrightProtected && (
                <div className="absolute bottom-2 right-2">
                  <div className="w-6 h-6 bg-green-500/80 rounded-full flex items-center justify-center">
                    <span className="text-xs">🛡️</span>
                  </div>
                </div>
              )}
            </div>

            {/* 资产信息 */}
            <div className="p-3">
              {/* 创作者信息 */}
              <div className="flex items-center space-x-2 mb-2">
                <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-sm">
                  {asset.creator.avatar}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-1">
                    <span className="text-xs font-medium text-white truncate">{asset.creator.name}</span>
                    {asset.creator.verified && (
                      <svg className="w-3 h-3 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                    )}
                  </div>
                </div>
              </div>

              {/* 资产标题 */}
              <h3 className="text-sm font-bold text-white mb-2 line-clamp-2 leading-tight">
                {asset.title}
              </h3>

              {/* 价格和统计 */}
              <div className="flex items-center justify-between mb-2">
                <div className="text-green-400 font-bold text-lg">
                  {formatPrice(asset.price)}
                </div>
                <div className="flex items-center space-x-2 text-xs text-gray-400">
                  <span>⭐ {asset.rating}</span>
                  <span>📥 {asset.downloads}</span>
                </div>
              </div>

              {/* 文件信息 */}
              <div className="text-xs text-gray-400 space-y-1">
                <div>格式: {asset.format}</div>
                <div>大小: {asset.fileSize}</div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* 购买弹窗 */}
      <AnimatePresence>
        {showPurchaseModal && selectedAsset && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4"
            onClick={() => setShowPurchaseModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-green-500/30"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center mb-6">
                <div className="text-4xl mb-4">💎</div>
                <h3 className="text-xl font-bold text-white mb-2">购买数字资产</h3>
                <p className="text-gray-400 text-sm">{selectedAsset.title}</p>
              </div>

              {/* 资产信息 */}
              <div className="bg-gray-800/50 rounded-xl p-4 mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-400">价格</span>
                  <span className="text-green-400 font-bold">{formatPrice(selectedAsset.price)}</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-400">创作者</span>
                  <span className="text-white font-bold">{selectedAsset.creator.name}</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-400">评分</span>
                  <span className="text-yellow-400 font-bold">⭐ {selectedAsset.rating}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">下载量</span>
                  <span className="text-blue-400 font-bold">{selectedAsset.downloads}</span>
                </div>
              </div>

              {/* 支付方式选择 */}
              <div className="mb-6">
                <h4 className="text-white font-medium mb-3">支付方式</h4>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg cursor-pointer">
                    <input
                      type="radio"
                      name="payment"
                      value="cny"
                      checked={paymentMethod === 'cny'}
                      onChange={(e) => setPaymentMethod(e.target.value as 'points' | 'cny')}
                      className="text-green-500"
                    />
                    <span className="text-white">人民币支付</span>
                    <span className="text-green-400 font-bold ml-auto">{formatPrice(selectedAsset.price)}</span>
                  </label>
                  <label className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg cursor-pointer">
                    <input
                      type="radio"
                      name="payment"
                      value="points"
                      checked={paymentMethod === 'points'}
                      onChange={(e) => setPaymentMethod(e.target.value as 'points' | 'cny')}
                      className="text-blue-500"
                    />
                    <span className="text-white">积分支付</span>
                    <span className="text-blue-400 font-bold ml-auto">{selectedAsset.price * 10} NGT</span>
                  </label>
                </div>
              </div>

              {/* 合规提示 */}
              <div className="mb-6 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                <div className="text-yellow-200 text-xs">
                  <p className="font-medium mb-1">购买须知：</p>
                  <p>• 数字资产仅供个人学习和工作使用</p>
                  <p>• 禁止用于商业用途或二次销售</p>
                  <p>• 购买后享有使用权，版权归原创作者所有</p>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowPurchaseModal(false)}
                  className="flex-1 py-3 bg-gray-700 rounded-xl text-white font-medium"
                >
                  取消
                </button>
                <button
                  onClick={handlePurchase}
                  className="flex-1 py-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl text-white font-medium"
                >
                  确认购买
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
