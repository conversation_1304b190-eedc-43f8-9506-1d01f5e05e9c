/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_VIEWER_POSE } from '../private.js';
import { XRPose } from './XRPose.js';
export class XRViewerPose extends XRPose {
    constructor(transform, views, emulatedPosition = false, linearVelocity = undefined, angularVelocity = undefined) {
        super(transform, emulatedPosition, linearVelocity, angularVelocity);
        this[P_VIEWER_POSE] = {
            views: Object.freeze(views),
        };
    }
    get views() {
        return this[P_VIEWER_POSE].views;
    }
}
//# sourceMappingURL=XRViewerPose.js.map