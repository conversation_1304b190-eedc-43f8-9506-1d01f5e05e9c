/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
export class XRSessionEvent extends Event {
    constructor(type, eventInitDict) {
        super(type, eventInitDict);
        if (!eventInitDict.session) {
            throw new Error('XRSessionEventInit.session is required');
        }
        this.session = eventInitDict.session;
    }
}
//# sourceMappingURL=XRSessionEvent.js.map