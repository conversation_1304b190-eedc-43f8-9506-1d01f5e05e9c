{"name": "@pmndrs/xr", "author": "<PERSON><PERSON>", "type": "module", "description": "VR/AR for threejs", "homepage": "https://github.com/pmndrs/xr", "version": "6.6.19", "keywords": ["r3f", "xr", "ar", "vr", "three.js", "react", "typescript"], "repository": {"type": "git", "url": "**************:pmndrs/xr.git"}, "license": "SEE LICENSE IN LICENSE", "peerDependencies": {"three": "*"}, "dependencies": {"@iwer/devui": "^1.1.1", "@iwer/sem": "~0.2.5", "iwer": "^2.0.1", "meshline": "^3.3.1", "zustand": "^4.5.2", "@pmndrs/pointer-events": "~6.6.19"}, "files": ["dist"], "main": "dist/index.js", "exports": {".": "./dist/index.js", "./internals": "./dist/internals.js"}, "scripts": {"build": "tsc", "check:prettier": "prettier --check src", "check:eslint": "eslint \"src/**/*.{ts,tsx}\"", "fix:prettier": "prettier --write src", "fix:eslint": "eslint \"src/**/*.{ts,tsx}\" --fix"}}