{"version": 3, "file": "record.mjs", "sourceRoot": "", "sources": ["../../../src/_vendor/zod-to-json-schema/parsers/record.ts"], "names": [], "mappings": "OAAO,EAAE,qBAAqB,EAAuC,MAAM,KAAK;OACzE,EAAmB,QAAQ,EAAE;OAI7B,EAAyB,cAAc,EAAE;AAYhD,MAAM,UAAU,cAAc,CAC5B,GAAqD,EACrD,IAAU;IAEV,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,KAAK,qBAAqB,CAAC,OAAO,EAAE;QAC9F,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM;YACjC,UAAU,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CACxC,CAAC,GAAoC,EAAE,GAAW,EAAE,EAAE,CAAC,CAAC;gBACtD,GAAG,GAAG;gBACN,CAAC,GAAG,CAAC,EACH,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;oBAC3B,GAAG,IAAI;oBACP,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,YAAY,EAAE,GAAG,CAAC;iBACtD,CAAC,IAAI,EAAE;aACX,CAAC,EACF,EAAE,CACH;YACD,oBAAoB,EAAE,KAAK;SACW,CAAC;KAC1C;IAED,MAAM,MAAM,GAA0B;QACpC,IAAI,EAAE,QAAQ;QACd,oBAAoB,EAClB,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;YAC3B,GAAG,IAAI;YACP,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,sBAAsB,CAAC;SAC3D,CAAC,IAAI,EAAE;KACX,CAAC;IAEF,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,EAAE;QAC9B,OAAO,MAAM,CAAC;KACf;IAED,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,KAAK,qBAAqB,CAAC,SAAS,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE;QACrG,MAAM,OAAO,GAAuC,MAAM,CAAC,OAAO,CAChE,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CACvC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QAEvF,OAAO;YACL,GAAG,MAAM;YACT,aAAa,EAAE,OAAO;SACvB,CAAC;KACH;SAAM,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,KAAK,qBAAqB,CAAC,OAAO,EAAE;QACvE,OAAO;YACL,GAAG,MAAM;YACT,aAAa,EAAE;gBACb,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM;aAC9B;SACF,CAAC;KACH;IAED,OAAO,MAAM,CAAC;AAChB,CAAC"}