"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("three-stdlib"),u=require("@react-three/fiber"),n=require("./Clone.cjs.js");function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function a(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var u=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,u.get?u:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}require("three");var c=o(e),i=a(r);function l(e){return u.useLoader(t.FBXLoader,e)}l.preload=e=>u.useLoader.preload(t.FBXLoader,e),l.clear=e=>u.useLoader.clear(t.FBXLoader,e),exports.Fbx=function({path:e,...r}){const t=l(e).children[0];return i.createElement(n.Clone,c.default({},r,{object:t}))},exports.useFBX=l;
