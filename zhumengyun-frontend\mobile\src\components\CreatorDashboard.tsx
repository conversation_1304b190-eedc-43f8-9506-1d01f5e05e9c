'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { creatorEconomyService, CreatorProfile } from '@/services/creatorEconomyService'
import EarningsAnalytics from './EarningsAnalytics'
import SkillCertification from './SkillCertification'
import CommunityInteraction from './CommunityInteraction'

interface CreatorDashboardProps {
  userId: string
}

export default function CreatorDashboard({ userId }: CreatorDashboardProps) {
  const [profile, setProfile] = useState<CreatorProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'earnings' | 'reputation' | 'analytics' | 'certification' | 'community'>('overview')

  useEffect(() => {
    loadCreatorProfile()
  }, [userId])

  const loadCreatorProfile = async () => {
    try {
      setLoading(true)
      const creatorProfile = await creatorEconomyService.getCreatorProfile(userId)
      setProfile(creatorProfile)
    } catch (error) {
      console.error('加载创作者资料失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number): string => {
    return `¥${amount.toLocaleString()}`
  }

  const getLevelColor = (level: string): string => {
    const colors = {
      Bronze: 'from-amber-600 to-amber-700',
      Silver: 'from-gray-400 to-gray-500',
      Gold: 'from-yellow-400 to-yellow-500',
      Platinum: 'from-blue-400 to-blue-500',
      Diamond: 'from-purple-400 to-purple-500'
    }
    return colors[level as keyof typeof colors] || 'from-gray-400 to-gray-500'
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-32 bg-white/10 rounded-2xl mb-4"></div>
          <div className="h-64 bg-white/10 rounded-2xl"></div>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">😔</div>
        <p className="text-white text-lg">无法加载创作者资料</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 创作者头部信息 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-2xl p-6 border border-white/20"
      >
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center text-2xl">
            {profile.avatar}
          </div>
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <h2 className="text-xl font-bold text-white">{profile.name}</h2>
              {profile.verified && (
                <svg className="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              )}
              {profile.realNameVerified && (
                <div className="px-2 py-1 bg-green-500/20 border border-green-500/30 rounded-full">
                  <span className="text-green-400 text-xs font-medium">实名认证</span>
                </div>
              )}
            </div>
            <div className="flex items-center space-x-3">
              <div className={`px-3 py-1 bg-gradient-to-r ${getLevelColor(profile.level)} rounded-full`}>
                <span className="text-white text-sm font-bold">{profile.level}</span>
              </div>
              <div className="text-gray-300 text-sm">
                信誉积分: {profile.points.DID}
              </div>
            </div>
          </div>
        </div>

        {/* 专业领域 */}
        <div className="mt-4">
          <div className="flex flex-wrap gap-2">
            {profile.specialties.map((specialty, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-white/10 rounded-full text-white text-sm"
              >
                {specialty}
              </span>
            ))}
          </div>
        </div>
      </motion.div>

      {/* 标签页导航 */}
      <div className="overflow-x-auto">
        <div className="flex space-x-1 bg-white/10 rounded-lg p-1 min-w-max">
          {[
            { key: 'overview', label: '概览', icon: '📊' },
            { key: 'earnings', label: '收益', icon: '💰' },
            { key: 'reputation', label: '信誉', icon: '🏆' },
            { key: 'analytics', label: '分析', icon: '📈' },
            { key: 'certification', label: '认证', icon: '📜' },
            { key: 'community', label: '社区', icon: '👥' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`flex items-center justify-center space-x-2 py-3 px-4 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
                activeTab === tab.key
                  ? 'bg-blue-500 text-white'
                  : 'text-white/70 hover:text-white'
              }`}
            >
              <span>{tab.icon}</span>
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* 标签页内容 */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        {activeTab === 'overview' && (
          <div className="space-y-4">
            {/* 积分概览 */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <h3 className="text-lg font-bold text-white mb-4">积分概览</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-white/5 rounded-xl p-4">
                  <div className="text-blue-400 text-2xl font-bold">{profile.points.NGT}</div>
                  <div className="text-gray-400 text-sm">NextGen积分</div>
                </div>
                <div className="bg-white/5 rounded-xl p-4">
                  <div className="text-purple-400 text-2xl font-bold">{profile.points.CRT}</div>
                  <div className="text-gray-400 text-sm">Creator积分</div>
                </div>
                <div className="bg-white/5 rounded-xl p-4">
                  <div className="text-green-400 text-2xl font-bold">{profile.points.SKL}</div>
                  <div className="text-gray-400 text-sm">Skill积分</div>
                </div>
                <div className="bg-white/5 rounded-xl p-4">
                  <div className="text-red-400 text-2xl font-bold">{profile.points.FAN}</div>
                  <div className="text-gray-400 text-sm">Fan积分</div>
                </div>
              </div>
            </div>

            {/* 认证状态 */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <h3 className="text-lg font-bold text-white mb-4">专业认证</h3>
              <div className="space-y-3">
                {profile.certifications.map((cert, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg">
                    <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                      <span className="text-green-400 text-sm">✓</span>
                    </div>
                    <span className="text-white font-medium">{cert}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'earnings' && (
          <div className="space-y-4">
            {/* 收益概览 */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <h3 className="text-lg font-bold text-white mb-4">收益概览</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-xl p-4 border border-green-500/20">
                  <div className="text-green-400 text-2xl font-bold">{formatCurrency(profile.earnings.totalRevenue)}</div>
                  <div className="text-gray-400 text-sm">总收益</div>
                </div>
                <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-xl p-4 border border-blue-500/20">
                  <div className="text-blue-400 text-2xl font-bold">{formatCurrency(profile.earnings.monthlyIncome)}</div>
                  <div className="text-gray-400 text-sm">月收入</div>
                </div>
              </div>
            </div>

            {/* 收益来源分析 */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <h3 className="text-lg font-bold text-white mb-4">收益来源</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-purple-500/20 rounded-full flex items-center justify-center">
                      <span className="text-purple-400 text-sm">🎨</span>
                    </div>
                    <span className="text-white">内容销售</span>
                  </div>
                  <span className="text-purple-400 font-bold">{formatCurrency(profile.earnings.contentSales)}</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                      <span className="text-green-400 text-sm">🛠️</span>
                    </div>
                    <span className="text-white">技能服务</span>
                  </div>
                  <span className="text-green-400 font-bold">{formatCurrency(profile.earnings.skillServices)}</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-yellow-500/20 rounded-full flex items-center justify-center">
                      <span className="text-yellow-400 text-sm">🎁</span>
                    </div>
                    <span className="text-white">平台奖励</span>
                  </div>
                  <span className="text-yellow-400 font-bold">{formatCurrency(profile.earnings.platformRewards)}</span>
                </div>
              </div>
            </div>

            {/* 税务提示 */}
            <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-xl p-4">
              <div className="flex items-start space-x-2">
                <span className="text-yellow-400 text-sm">💡</span>
                <div className="text-yellow-200 text-xs">
                  <p className="font-medium mb-1">税务提醒：</p>
                  <p>• 所有收益均需依法纳税</p>
                  <p>• 平台将协助提供相关税务凭证</p>
                  <p>• 建议咨询专业税务顾问</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'reputation' && (
          <div className="space-y-4">
            {/* 信誉概览 */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <h3 className="text-lg font-bold text-white mb-4">信誉概览</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-xl p-4 border border-yellow-500/20">
                  <div className="text-yellow-400 text-2xl font-bold">{profile.reputation.score}</div>
                  <div className="text-gray-400 text-sm">信誉评分</div>
                </div>
                <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-xl p-4 border border-blue-500/20">
                  <div className="text-blue-400 text-2xl font-bold">{profile.reputation.rating}</div>
                  <div className="text-gray-400 text-sm">平均评分</div>
                </div>
                <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-xl p-4 border border-green-500/20">
                  <div className="text-green-400 text-2xl font-bold">{profile.reputation.completedProjects}</div>
                  <div className="text-gray-400 text-sm">完成项目</div>
                </div>
                <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-xl p-4 border border-purple-500/20">
                  <div className="text-purple-400 text-2xl font-bold">{profile.reputation.reviews}</div>
                  <div className="text-gray-400 text-sm">用户评价</div>
                </div>
              </div>
            </div>

            {/* 信誉等级说明 */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <h3 className="text-lg font-bold text-white mb-4">等级说明</h3>
              <div className="space-y-3">
                {[
                  { level: 'Bronze', range: '0-20', color: 'from-amber-600 to-amber-700' },
                  { level: 'Silver', range: '21-40', color: 'from-gray-400 to-gray-500' },
                  { level: 'Gold', range: '41-60', color: 'from-yellow-400 to-yellow-500' },
                  { level: 'Platinum', range: '61-80', color: 'from-blue-400 to-blue-500' },
                  { level: 'Diamond', range: '81-100', color: 'from-purple-400 to-purple-500' }
                ].map((item) => (
                  <div key={item.level} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-8 h-8 bg-gradient-to-r ${item.color} rounded-full flex items-center justify-center`}>
                        <span className="text-white text-xs font-bold">{item.level[0]}</span>
                      </div>
                      <span className="text-white font-medium">{item.level}</span>
                    </div>
                    <span className="text-gray-400 text-sm">{item.range}分</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <EarningsAnalytics userId={userId} />
        )}

        {activeTab === 'certification' && (
          <SkillCertification userId={userId} />
        )}

        {activeTab === 'community' && (
          <CommunityInteraction userId={userId} />
        )}
      </motion.div>
    </div>
  )
}
