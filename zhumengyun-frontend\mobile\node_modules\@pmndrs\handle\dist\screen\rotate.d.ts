import { OrthographicCamera, PerspectiveCamera, Vector2Tuple } from 'three';
import { StoreApi } from 'zustand/vanilla';
import { defaultScreenCameraApply, ScreenCameraStateAndFunctions } from './camera.js';
import { ScreenHandleStore } from './store.js';
export declare class RotateScreenHandleStore extends ScreenHandleStore<Readonly<Vector2Tuple>> {
    filter: (map: ScreenHandleStore['map']) => boolean;
    customApply?: typeof defaultScreenCameraApply | undefined;
    speed?: number | undefined;
    constructor(store: StoreApi<ScreenCameraStateAndFunctions>, getCamera: () => PerspectiveCamera | OrthographicCamera, filter: (map: ScreenHandleStore['map']) => boolean, customApply?: typeof defaultScreenCameraApply | undefined, speed?: number | undefined);
}
