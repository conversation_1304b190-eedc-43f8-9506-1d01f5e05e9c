/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { XRFrame } from '../frameloop/XRFrame.js';
import { XRInputSource } from '../input/XRInputSource.js';
interface XRInputSourceEventInit extends EventInit {
    frame: XRFrame;
    inputSource: XRInputSource;
}
export declare class XRInputSourceEvent extends Event {
    readonly frame: XRFrame;
    readonly inputSource: XRInputSource;
    constructor(type: string, eventInitDict: XRInputSourceEventInit);
}
export interface XRInputSourceEventHandler {
    (evt: XRInputSourceEvent): any;
}
export {};
//# sourceMappingURL=XRInputSourceEvent.d.ts.map