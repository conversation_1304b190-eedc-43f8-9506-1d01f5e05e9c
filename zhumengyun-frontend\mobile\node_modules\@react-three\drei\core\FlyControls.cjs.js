"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("@react-three/fiber"),r=require("react"),n=require("three-stdlib");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function o(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var s=u(e),c=o(r);const a=c.forwardRef((({domElement:e,...r},u)=>{const{onChange:o,makeDefault:a,...l}=r,i=t.useThree((e=>e.invalidate)),f=t.useThree((e=>e.camera)),d=t.useThree((e=>e.gl)),v=t.useThree((e=>e.events)),m=t.useThree((e=>e.get)),b=t.useThree((e=>e.set)),h=e||v.connected||d.domElement,p=c.useMemo((()=>new n.FlyControls(f)),[f]);return c.useEffect((()=>(p.connect(h),()=>{p.dispose()})),[h,p,i]),c.useEffect((()=>{const e=e=>{i(),o&&o(e)};return null==p.addEventListener||p.addEventListener("change",e),()=>null==p.removeEventListener?void 0:p.removeEventListener("change",e)}),[o,i]),c.useEffect((()=>{if(a){const e=m().controls;return b({controls:p}),()=>b({controls:e})}}),[a,p]),t.useFrame(((e,t)=>p.update(t))),c.createElement("primitive",s.default({ref:u,object:p,args:[f,h]},l))}));exports.FlyControls=a;
