import { Vector2Tuple, PerspectiveCamera, OrthographicCamera, Object3D } from 'three';
import type { HandlesProperties } from './index.js';
export declare function extractHandleTransformOptions(key: 'x' | 'y' | 'z' | 'xy' | 'yz' | 'xz' | 'xyz' | 'e', properties?: HandlesProperties): {
    options: {
        x: boolean | Vector2Tuple;
        y: boolean | Vector2Tuple;
        z: boolean | Vector2Tuple;
        e: boolean | Vector2Tuple;
    } | false;
    disabled: boolean;
};
export declare function computeHandlesScale(handlesCenter: Object3D, camera: PerspectiveCamera | OrthographicCamera, fixed: boolean, size: number): number;
