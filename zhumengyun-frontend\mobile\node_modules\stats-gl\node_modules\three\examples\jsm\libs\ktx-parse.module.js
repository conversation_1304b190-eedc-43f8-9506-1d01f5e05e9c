const t=0,e=1,n=2,i=3,s=0,a=0,r=2,o=0,l=1,f=160,h=161,U=162,c=163,_=166,p=0,g=1,y=0,x=1,u=2,b=3,d=4,w=5,m=6,D=7,B=8,L=9,v=10,A=11,k=12,V=13,I=14,S=15,F=16,O=17,E=18,T=0,C=1,M=2,P=3,z=4,W=5,H=6,N=7,K=8,X=9,R=10,Y=11,j=0,q=1,G=2,J=13,Q=14,Z=15,$=128,tt=64,et=32,nt=16,it=0,st=1,at=2,rt=3,ot=4,lt=5,ft=6,ht=7,Ut=8,ct=9,_t=10,pt=13,gt=14,yt=15,xt=16,ut=17,bt=20,dt=21,wt=22,mt=23,Dt=24,Bt=27,Lt=28,vt=29,At=30,kt=31,Vt=34,It=35,St=36,Ft=37,Ot=38,Et=41,Tt=42,Ct=43,Mt=44,Pt=45,zt=48,Wt=49,Ht=50,Nt=58,Kt=59,Xt=62,Rt=63,Yt=64,jt=65,qt=68,Gt=69,Jt=70,Qt=71,Zt=74,$t=75,te=76,ee=77,ne=78,ie=81,se=82,ae=83,re=84,oe=85,le=88,fe=89,he=90,Ue=91,ce=92,_e=95,pe=96,ge=97,ye=98,xe=99,ue=100,be=101,de=102,we=103,me=104,De=105,Be=106,Le=107,ve=108,Ae=109,ke=110,Ve=111,Ie=112,Se=113,Fe=114,Oe=115,Ee=116,Te=117,Ce=118,Me=119,Pe=120,ze=121,We=122,He=123,Ne=124,Ke=125,Xe=126,Re=127,Ye=128,je=129,qe=130,Ge=131,Je=132,Qe=133,Ze=134,$e=135,tn=136,en=137,nn=138,sn=139,an=140,rn=141,on=142,ln=143,fn=144,hn=145,Un=146,cn=147,_n=148,pn=149,gn=150,yn=151,xn=152,un=153,bn=154,dn=155,wn=156,mn=157,Dn=158,Bn=159,Ln=160,vn=161,An=162,kn=163,Vn=164,In=165,Sn=166,Fn=167,On=168,En=169,Tn=170,Cn=171,Mn=172,Pn=173,zn=174,Wn=175,Hn=176,Nn=177,Kn=178,Xn=179,Rn=180,Yn=181,jn=182,qn=183,Gn=184,Jn=1000156007,Qn=1000156008,Zn=1000156009,$n=1000156010,ti=1000156011,ei=1000156017,ni=1000156018,ii=1000156019,si=1000156020,ai=1000156021,ri=1000054e3,oi=1000054001,li=1000054002,fi=1000054003,hi=1000054004,Ui=1000054005,ci=1000054006,_i=1000054007,pi=1000066e3,gi=1000066001,yi=1000066002,xi=1000066003,ui=1000066004,bi=1000066005,di=1000066006,wi=1000066007,mi=1000066008,Di=1000066009,Bi=1000066010,Li=1000066011,vi=1000066012,Ai=1000066013,ki=100034e4,Vi=1000340001;class Ii{constructor(){this.vkFormat=0,this.typeSize=1,this.pixelWidth=0,this.pixelHeight=0,this.pixelDepth=0,this.layerCount=0,this.faceCount=1,this.supercompressionScheme=0,this.levels=[],this.dataFormatDescriptor=[{vendorId:0,descriptorType:0,descriptorBlockSize:0,versionNumber:2,colorModel:0,colorPrimaries:1,transferFunction:2,flags:0,texelBlockDimension:[0,0,0,0],bytesPlane:[0,0,0,0,0,0,0,0],samples:[]}],this.keyValue={},this.globalData=null}}class Si{constructor(t,e,n,i){this._dataView=void 0,this._littleEndian=void 0,this._offset=void 0,this._dataView=new DataView(t.buffer,t.byteOffset+e,n),this._littleEndian=i,this._offset=0}_nextUint8(){const t=this._dataView.getUint8(this._offset);return this._offset+=1,t}_nextUint16(){const t=this._dataView.getUint16(this._offset,this._littleEndian);return this._offset+=2,t}_nextUint32(){const t=this._dataView.getUint32(this._offset,this._littleEndian);return this._offset+=4,t}_nextUint64(){const t=this._dataView.getUint32(this._offset,this._littleEndian)+2**32*this._dataView.getUint32(this._offset+4,this._littleEndian);return this._offset+=8,t}_nextInt32(){const t=this._dataView.getInt32(this._offset,this._littleEndian);return this._offset+=4,t}_nextUint8Array(t){const e=new Uint8Array(this._dataView.buffer,this._dataView.byteOffset+this._offset,t);return this._offset+=t,e}_skip(t){return this._offset+=t,this}_scan(t,e){void 0===e&&(e=0);const n=this._offset;let i=0;for(;this._dataView.getUint8(this._offset)!==e&&i<t;)i++,this._offset++;return i<t&&this._offset++,new Uint8Array(this._dataView.buffer,this._dataView.byteOffset+n,i)}}const Fi=new Uint8Array([0]),Oi=[171,75,84,88,32,50,48,187,13,10,26,10];function Ei(t){return(new TextEncoder).encode(t)}function Ti(t){return(new TextDecoder).decode(t)}function Ci(t){let e=0;for(const n of t)e+=n.byteLength;const n=new Uint8Array(e);let i=0;for(const e of t)n.set(new Uint8Array(e),i),i+=e.byteLength;return n}function Mi(t,e){return void 0===e&&(e=4),Math.ceil(t/e)*e-t}function Pi(t){const e=new Uint8Array(t.buffer,t.byteOffset,Oi.length);if(e[0]!==Oi[0]||e[1]!==Oi[1]||e[2]!==Oi[2]||e[3]!==Oi[3]||e[4]!==Oi[4]||e[5]!==Oi[5]||e[6]!==Oi[6]||e[7]!==Oi[7]||e[8]!==Oi[8]||e[9]!==Oi[9]||e[10]!==Oi[10]||e[11]!==Oi[11])throw new Error("Missing KTX 2.0 identifier.");const n=new Ii,i=17*Uint32Array.BYTES_PER_ELEMENT,s=new Si(t,Oi.length,i,!0);n.vkFormat=s._nextUint32(),n.typeSize=s._nextUint32(),n.pixelWidth=s._nextUint32(),n.pixelHeight=s._nextUint32(),n.pixelDepth=s._nextUint32(),n.layerCount=s._nextUint32(),n.faceCount=s._nextUint32();const a=s._nextUint32();n.supercompressionScheme=s._nextUint32();const r=s._nextUint32(),o=s._nextUint32(),l=s._nextUint32(),f=s._nextUint32(),h=s._nextUint64(),U=s._nextUint64(),c=new Si(t,Oi.length+i,3*a*8,!0);for(let e=0;e<a;e++)n.levels.push({levelData:new Uint8Array(t.buffer,t.byteOffset+c._nextUint64(),c._nextUint64()),uncompressedByteLength:c._nextUint64()});const _=new Si(t,r,o,!0),p={vendorId:_._skip(4)._nextUint16(),descriptorType:_._nextUint16(),versionNumber:_._nextUint16(),descriptorBlockSize:_._nextUint16(),colorModel:_._nextUint8(),colorPrimaries:_._nextUint8(),transferFunction:_._nextUint8(),flags:_._nextUint8(),texelBlockDimension:[_._nextUint8(),_._nextUint8(),_._nextUint8(),_._nextUint8()],bytesPlane:[_._nextUint8(),_._nextUint8(),_._nextUint8(),_._nextUint8(),_._nextUint8(),_._nextUint8(),_._nextUint8(),_._nextUint8()],samples:[]},g=(p.descriptorBlockSize/4-6)/4;for(let t=0;t<g;t++){const e={bitOffset:_._nextUint16(),bitLength:_._nextUint8(),channelType:_._nextUint8(),samplePosition:[_._nextUint8(),_._nextUint8(),_._nextUint8(),_._nextUint8()],sampleLower:-Infinity,sampleUpper:Infinity};64&e.channelType?(e.sampleLower=_._nextInt32(),e.sampleUpper=_._nextInt32()):(e.sampleLower=_._nextUint32(),e.sampleUpper=_._nextUint32()),p.samples[t]=e}n.dataFormatDescriptor.length=0,n.dataFormatDescriptor.push(p);const y=new Si(t,l,f,!0);for(;y._offset<f;){const t=y._nextUint32(),e=y._scan(t),i=Ti(e);if(n.keyValue[i]=y._nextUint8Array(t-e.byteLength-1),i.match(/^ktx/i)){const t=Ti(n.keyValue[i]);n.keyValue[i]=t.substring(0,t.lastIndexOf("\0"))}y._skip(t%4?4-t%4:0)}if(U<=0)return n;const x=new Si(t,h,U,!0),u=x._nextUint16(),b=x._nextUint16(),d=x._nextUint32(),w=x._nextUint32(),m=x._nextUint32(),D=x._nextUint32(),B=[];for(let t=0;t<a;t++)B.push({imageFlags:x._nextUint32(),rgbSliceByteOffset:x._nextUint32(),rgbSliceByteLength:x._nextUint32(),alphaSliceByteOffset:x._nextUint32(),alphaSliceByteLength:x._nextUint32()});const L=h+x._offset,v=L+d,A=v+w,k=A+m,V=new Uint8Array(t.buffer,t.byteOffset+L,d),I=new Uint8Array(t.buffer,t.byteOffset+v,w),S=new Uint8Array(t.buffer,t.byteOffset+A,m),F=new Uint8Array(t.buffer,t.byteOffset+k,D);return n.globalData={endpointCount:u,selectorCount:b,imageDescs:B,endpointsData:V,selectorsData:I,tablesData:S,extendedData:F},n}const zi={keepWriter:!1};function Wi(t,e){void 0===e&&(e={}),e={...zi,...e};let n=new ArrayBuffer(0);if(t.globalData){const e=new ArrayBuffer(20+5*t.globalData.imageDescs.length*4),i=new DataView(e);i.setUint16(0,t.globalData.endpointCount,!0),i.setUint16(2,t.globalData.selectorCount,!0),i.setUint32(4,t.globalData.endpointsData.byteLength,!0),i.setUint32(8,t.globalData.selectorsData.byteLength,!0),i.setUint32(12,t.globalData.tablesData.byteLength,!0),i.setUint32(16,t.globalData.extendedData.byteLength,!0);for(let e=0;e<t.globalData.imageDescs.length;e++){const n=t.globalData.imageDescs[e];i.setUint32(20+5*e*4+0,n.imageFlags,!0),i.setUint32(20+5*e*4+4,n.rgbSliceByteOffset,!0),i.setUint32(20+5*e*4+8,n.rgbSliceByteLength,!0),i.setUint32(20+5*e*4+12,n.alphaSliceByteOffset,!0),i.setUint32(20+5*e*4+16,n.alphaSliceByteLength,!0)}n=Ci([e,t.globalData.endpointsData,t.globalData.selectorsData,t.globalData.tablesData,t.globalData.extendedData])}const i=[];let s=t.keyValue;e.keepWriter||(s={...t.keyValue,KTXwriter:"KTX-Parse v0.7.1"});for(const t in s){const e=s[t],n=Ei(t),a="string"==typeof e?Ci([Ei(e),Fi]):e,r=n.byteLength+1+a.byteLength,o=Mi(r,4);i.push(Ci([new Uint32Array([r]),n,Fi,a,new Uint8Array(o).fill(0)]))}const a=Ci(i);if(1!==t.dataFormatDescriptor.length||0!==t.dataFormatDescriptor[0].descriptorType)throw new Error("Only BASICFORMAT Data Format Descriptor output supported.");const r=t.dataFormatDescriptor[0],o=new ArrayBuffer(28+16*r.samples.length),l=new DataView(o),f=24+16*r.samples.length;if(l.setUint32(0,o.byteLength,!0),l.setUint16(4,r.vendorId,!0),l.setUint16(6,r.descriptorType,!0),l.setUint16(8,r.versionNumber,!0),l.setUint16(10,f,!0),l.setUint8(12,r.colorModel),l.setUint8(13,r.colorPrimaries),l.setUint8(14,r.transferFunction),l.setUint8(15,r.flags),!Array.isArray(r.texelBlockDimension))throw new Error("texelBlockDimension is now an array. For dimensionality `d`, set `d - 1`.");l.setUint8(16,r.texelBlockDimension[0]),l.setUint8(17,r.texelBlockDimension[1]),l.setUint8(18,r.texelBlockDimension[2]),l.setUint8(19,r.texelBlockDimension[3]);for(let t=0;t<8;t++)l.setUint8(20+t,r.bytesPlane[t]);for(let t=0;t<r.samples.length;t++){const e=r.samples[t],n=28+16*t;if(e.channelID)throw new Error("channelID has been renamed to channelType.");l.setUint16(n+0,e.bitOffset,!0),l.setUint8(n+2,e.bitLength),l.setUint8(n+3,e.channelType),l.setUint8(n+4,e.samplePosition[0]),l.setUint8(n+5,e.samplePosition[1]),l.setUint8(n+6,e.samplePosition[2]),l.setUint8(n+7,e.samplePosition[3]),64&e.channelType?(l.setInt32(n+8,e.sampleLower,!0),l.setInt32(n+12,e.sampleUpper,!0)):(l.setUint32(n+8,e.sampleLower,!0),l.setUint32(n+12,e.sampleUpper,!0))}const h=Oi.length+68+3*t.levels.length*8,U=h+o.byteLength;let c=n.byteLength>0?U+a.byteLength:0;c%8&&(c+=8-c%8);const _=[],p=new DataView(new ArrayBuffer(3*t.levels.length*8)),g=new Uint32Array(t.levels.length);let y=0;0===t.supercompressionScheme&&(y=function(t,e){const n=Math.max(t,4),i=Math.min(t,4);let s=n;for(;s%i!=0;)s+=n;return s}(function(t){return t.levels[0].levelData.byteLength/function(t,e){let n=1;const i=[t.pixelWidth,t.pixelHeight,t.pixelDepth],s=function(t){const[e,n,i]=t.dataFormatDescriptor[0].texelBlockDimension;return[e+1,n+1,i+1]}(t);for(let t=0;t<3;t++)if(i[t]>0){const e=Math.ceil(Math.floor(i[t]*Math.pow(2,-0))/s[t]);n*=Math.max(1,e)}return t.layerCount>0&&(n*=t.layerCount),t.faceCount>0&&(n*=t.faceCount),n}(t)}(t)));let x=(c||U+a.byteLength)+n.byteLength;for(let e=t.levels.length-1;e>=0;e--){if(x%y){const t=Mi(x,y);_.push(new Uint8Array(t)),x+=t}const n=t.levels[e];_.push(n.levelData),g[e]=x,x+=n.levelData.byteLength}for(let e=0;e<t.levels.length;e++){const n=t.levels[e];p.setBigUint64(24*e+0,BigInt(g[e]),!0),p.setBigUint64(24*e+8,BigInt(n.levelData.byteLength),!0),p.setBigUint64(24*e+16,BigInt(n.uncompressedByteLength),!0)}const u=new ArrayBuffer(68),b=new DataView(u);return b.setUint32(0,t.vkFormat,!0),b.setUint32(4,t.typeSize,!0),b.setUint32(8,t.pixelWidth,!0),b.setUint32(12,t.pixelHeight,!0),b.setUint32(16,t.pixelDepth,!0),b.setUint32(20,t.layerCount,!0),b.setUint32(24,t.faceCount,!0),b.setUint32(28,t.levels.length,!0),b.setUint32(32,t.supercompressionScheme,!0),b.setUint32(36,h,!0),b.setUint32(40,o.byteLength,!0),b.setUint32(44,U,!0),b.setUint32(48,a.byteLength,!0),b.setBigUint64(52,BigInt(n.byteLength>0?c:0),!0),b.setBigUint64(60,BigInt(n.byteLength),!0),new Uint8Array(Ci([new Uint8Array(Oi).buffer,u,p.buffer,o,a,c>0?new ArrayBuffer(c-(U+a.byteLength)):new ArrayBuffer(0),n,..._]))}export{Z as KHR_DF_CHANNEL_RGBSDA_ALPHA,G as KHR_DF_CHANNEL_RGBSDA_BLUE,Q as KHR_DF_CHANNEL_RGBSDA_DEPTH,q as KHR_DF_CHANNEL_RGBSDA_GREEN,j as KHR_DF_CHANNEL_RGBSDA_RED,J as KHR_DF_CHANNEL_RGBSDA_STENCIL,g as KHR_DF_FLAG_ALPHA_PREMULTIPLIED,p as KHR_DF_FLAG_ALPHA_STRAIGHT,s as KHR_DF_KHR_DESCRIPTORTYPE_BASICFORMAT,U as KHR_DF_MODEL_ASTC,f as KHR_DF_MODEL_ETC1,c as KHR_DF_MODEL_ETC1S,h as KHR_DF_MODEL_ETC2,l as KHR_DF_MODEL_RGBSDA,_ as KHR_DF_MODEL_UASTC,o as KHR_DF_MODEL_UNSPECIFIED,H as KHR_DF_PRIMARIES_ACES,N as KHR_DF_PRIMARIES_ACESCC,Y as KHR_DF_PRIMARIES_ADOBERGB,z as KHR_DF_PRIMARIES_BT2020,M as KHR_DF_PRIMARIES_BT601_EBU,P as KHR_DF_PRIMARIES_BT601_SMPTE,C as KHR_DF_PRIMARIES_BT709,W as KHR_DF_PRIMARIES_CIEXYZ,R as KHR_DF_PRIMARIES_DISPLAYP3,K as KHR_DF_PRIMARIES_NTSC1953,X as KHR_DF_PRIMARIES_PAL525,T as KHR_DF_PRIMARIES_UNSPECIFIED,et as KHR_DF_SAMPLE_DATATYPE_EXPONENT,$ as KHR_DF_SAMPLE_DATATYPE_FLOAT,nt as KHR_DF_SAMPLE_DATATYPE_LINEAR,tt as KHR_DF_SAMPLE_DATATYPE_SIGNED,F as KHR_DF_TRANSFER_ACESCC,O as KHR_DF_TRANSFER_ACESCCT,E as KHR_DF_TRANSFER_ADOBERGB,D as KHR_DF_TRANSFER_BT1886,k as KHR_DF_TRANSFER_DCIP3,L as KHR_DF_TRANSFER_HLG_EOTF,B as KHR_DF_TRANSFER_HLG_OETF,b as KHR_DF_TRANSFER_ITU,x as KHR_DF_TRANSFER_LINEAR,d as KHR_DF_TRANSFER_NTSC,I as KHR_DF_TRANSFER_PAL625_EOTF,V as KHR_DF_TRANSFER_PAL_OETF,v as KHR_DF_TRANSFER_PQ_EOTF,A as KHR_DF_TRANSFER_PQ_OETF,w as KHR_DF_TRANSFER_SLOG,m as KHR_DF_TRANSFER_SLOG2,u as KHR_DF_TRANSFER_SRGB,S as KHR_DF_TRANSFER_ST240,y as KHR_DF_TRANSFER_UNSPECIFIED,a as KHR_DF_VENDORID_KHRONOS,r as KHR_DF_VERSION,e as KHR_SUPERCOMPRESSION_BASISLZ,t as KHR_SUPERCOMPRESSION_NONE,i as KHR_SUPERCOMPRESSION_ZLIB,n as KHR_SUPERCOMPRESSION_ZSTD,Ii as KTX2Container,Ut as VK_FORMAT_A1R5G5B5_UNORM_PACK16,Gt as VK_FORMAT_A2B10G10R10_SINT_PACK32,jt as VK_FORMAT_A2B10G10R10_SNORM_PACK32,qt as VK_FORMAT_A2B10G10R10_UINT_PACK32,Yt as VK_FORMAT_A2B10G10R10_UNORM_PACK32,Rt as VK_FORMAT_A2R10G10B10_SINT_PACK32,Kt as VK_FORMAT_A2R10G10B10_SNORM_PACK32,Xt as VK_FORMAT_A2R10G10B10_UINT_PACK32,Nt as VK_FORMAT_A2R10G10B10_UNORM_PACK32,Vi as VK_FORMAT_A4B4G4R4_UNORM_PACK16_EXT,ki as VK_FORMAT_A4R4G4B4_UNORM_PACK16_EXT,Li as VK_FORMAT_ASTC_10x10_SFLOAT_BLOCK_EXT,Rn as VK_FORMAT_ASTC_10x10_SRGB_BLOCK,Xn as VK_FORMAT_ASTC_10x10_UNORM_BLOCK,mi as VK_FORMAT_ASTC_10x5_SFLOAT_BLOCK_EXT,zn as VK_FORMAT_ASTC_10x5_SRGB_BLOCK,Pn as VK_FORMAT_ASTC_10x5_UNORM_BLOCK,Di as VK_FORMAT_ASTC_10x6_SFLOAT_BLOCK_EXT,Hn as VK_FORMAT_ASTC_10x6_SRGB_BLOCK,Wn as VK_FORMAT_ASTC_10x6_UNORM_BLOCK,Bi as VK_FORMAT_ASTC_10x8_SFLOAT_BLOCK_EXT,Kn as VK_FORMAT_ASTC_10x8_SRGB_BLOCK,Nn as VK_FORMAT_ASTC_10x8_UNORM_BLOCK,vi as VK_FORMAT_ASTC_12x10_SFLOAT_BLOCK_EXT,jn as VK_FORMAT_ASTC_12x10_SRGB_BLOCK,Yn as VK_FORMAT_ASTC_12x10_UNORM_BLOCK,Ai as VK_FORMAT_ASTC_12x12_SFLOAT_BLOCK_EXT,Gn as VK_FORMAT_ASTC_12x12_SRGB_BLOCK,qn as VK_FORMAT_ASTC_12x12_UNORM_BLOCK,pi as VK_FORMAT_ASTC_4x4_SFLOAT_BLOCK_EXT,Dn as VK_FORMAT_ASTC_4x4_SRGB_BLOCK,mn as VK_FORMAT_ASTC_4x4_UNORM_BLOCK,gi as VK_FORMAT_ASTC_5x4_SFLOAT_BLOCK_EXT,Ln as VK_FORMAT_ASTC_5x4_SRGB_BLOCK,Bn as VK_FORMAT_ASTC_5x4_UNORM_BLOCK,yi as VK_FORMAT_ASTC_5x5_SFLOAT_BLOCK_EXT,An as VK_FORMAT_ASTC_5x5_SRGB_BLOCK,vn as VK_FORMAT_ASTC_5x5_UNORM_BLOCK,xi as VK_FORMAT_ASTC_6x5_SFLOAT_BLOCK_EXT,Vn as VK_FORMAT_ASTC_6x5_SRGB_BLOCK,kn as VK_FORMAT_ASTC_6x5_UNORM_BLOCK,ui as VK_FORMAT_ASTC_6x6_SFLOAT_BLOCK_EXT,Sn as VK_FORMAT_ASTC_6x6_SRGB_BLOCK,In as VK_FORMAT_ASTC_6x6_UNORM_BLOCK,bi as VK_FORMAT_ASTC_8x5_SFLOAT_BLOCK_EXT,On as VK_FORMAT_ASTC_8x5_SRGB_BLOCK,Fn as VK_FORMAT_ASTC_8x5_UNORM_BLOCK,di as VK_FORMAT_ASTC_8x6_SFLOAT_BLOCK_EXT,Tn as VK_FORMAT_ASTC_8x6_SRGB_BLOCK,En as VK_FORMAT_ASTC_8x6_UNORM_BLOCK,wi as VK_FORMAT_ASTC_8x8_SFLOAT_BLOCK_EXT,Mn as VK_FORMAT_ASTC_8x8_SRGB_BLOCK,Cn as VK_FORMAT_ASTC_8x8_UNORM_BLOCK,We as VK_FORMAT_B10G11R11_UFLOAT_PACK32,ti as VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16,ai as VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16,rt as VK_FORMAT_B4G4R4A4_UNORM_PACK16,ht as VK_FORMAT_B5G5R5A1_UNORM_PACK16,lt as VK_FORMAT_B5G6R5_UNORM_PACK16,Wt as VK_FORMAT_B8G8R8A8_SINT,Pt as VK_FORMAT_B8G8R8A8_SNORM,Ht as VK_FORMAT_B8G8R8A8_SRGB,zt as VK_FORMAT_B8G8R8A8_UINT,Mt as VK_FORMAT_B8G8R8A8_UNORM,It as VK_FORMAT_B8G8R8_SINT,kt as VK_FORMAT_B8G8R8_SNORM,St as VK_FORMAT_B8G8R8_SRGB,Vt as VK_FORMAT_B8G8R8_UINT,At as VK_FORMAT_B8G8R8_UNORM,Ze as VK_FORMAT_BC1_RGBA_SRGB_BLOCK,Qe as VK_FORMAT_BC1_RGBA_UNORM_BLOCK,Je as VK_FORMAT_BC1_RGB_SRGB_BLOCK,Ge as VK_FORMAT_BC1_RGB_UNORM_BLOCK,tn as VK_FORMAT_BC2_SRGB_BLOCK,$e as VK_FORMAT_BC2_UNORM_BLOCK,nn as VK_FORMAT_BC3_SRGB_BLOCK,en as VK_FORMAT_BC3_UNORM_BLOCK,an as VK_FORMAT_BC4_SNORM_BLOCK,sn as VK_FORMAT_BC4_UNORM_BLOCK,on as VK_FORMAT_BC5_SNORM_BLOCK,rn as VK_FORMAT_BC5_UNORM_BLOCK,fn as VK_FORMAT_BC6H_SFLOAT_BLOCK,ln as VK_FORMAT_BC6H_UFLOAT_BLOCK,Un as VK_FORMAT_BC7_SRGB_BLOCK,hn as VK_FORMAT_BC7_UNORM_BLOCK,Ne as VK_FORMAT_D16_UNORM,Ye as VK_FORMAT_D16_UNORM_S8_UINT,je as VK_FORMAT_D24_UNORM_S8_UINT,Xe as VK_FORMAT_D32_SFLOAT,qe as VK_FORMAT_D32_SFLOAT_S8_UINT,He as VK_FORMAT_E5B9G9R9_UFLOAT_PACK32,wn as VK_FORMAT_EAC_R11G11_SNORM_BLOCK,dn as VK_FORMAT_EAC_R11G11_UNORM_BLOCK,bn as VK_FORMAT_EAC_R11_SNORM_BLOCK,un as VK_FORMAT_EAC_R11_UNORM_BLOCK,gn as VK_FORMAT_ETC2_R8G8B8A1_SRGB_BLOCK,pn as VK_FORMAT_ETC2_R8G8B8A1_UNORM_BLOCK,xn as VK_FORMAT_ETC2_R8G8B8A8_SRGB_BLOCK,yn as VK_FORMAT_ETC2_R8G8B8A8_UNORM_BLOCK,_n as VK_FORMAT_ETC2_R8G8B8_SRGB_BLOCK,cn as VK_FORMAT_ETC2_R8G8B8_UNORM_BLOCK,$n as VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16,si as VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16,hi as VK_FORMAT_PVRTC1_2BPP_SRGB_BLOCK_IMG,ri as VK_FORMAT_PVRTC1_2BPP_UNORM_BLOCK_IMG,Ui as VK_FORMAT_PVRTC1_4BPP_SRGB_BLOCK_IMG,oi as VK_FORMAT_PVRTC1_4BPP_UNORM_BLOCK_IMG,ci as VK_FORMAT_PVRTC2_2BPP_SRGB_BLOCK_IMG,li as VK_FORMAT_PVRTC2_2BPP_UNORM_BLOCK_IMG,_i as VK_FORMAT_PVRTC2_4BPP_SRGB_BLOCK_IMG,fi as VK_FORMAT_PVRTC2_4BPP_UNORM_BLOCK_IMG,Zn as VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16,Qn as VK_FORMAT_R10X6G10X6_UNORM_2PACK16,Jn as VK_FORMAT_R10X6_UNORM_PACK16,ii as VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16,ni as VK_FORMAT_R12X4G12X4_UNORM_2PACK16,ei as VK_FORMAT_R12X4_UNORM_PACK16,ge as VK_FORMAT_R16G16B16A16_SFLOAT,pe as VK_FORMAT_R16G16B16A16_SINT,ce as VK_FORMAT_R16G16B16A16_SNORM,_e as VK_FORMAT_R16G16B16A16_UINT,Ue as VK_FORMAT_R16G16B16A16_UNORM,he as VK_FORMAT_R16G16B16_SFLOAT,fe as VK_FORMAT_R16G16B16_SINT,oe as VK_FORMAT_R16G16B16_SNORM,le as VK_FORMAT_R16G16B16_UINT,re as VK_FORMAT_R16G16B16_UNORM,ae as VK_FORMAT_R16G16_SFLOAT,se as VK_FORMAT_R16G16_SINT,ne as VK_FORMAT_R16G16_SNORM,ie as VK_FORMAT_R16G16_UINT,ee as VK_FORMAT_R16G16_UNORM,te as VK_FORMAT_R16_SFLOAT,$t as VK_FORMAT_R16_SINT,Qt as VK_FORMAT_R16_SNORM,Zt as VK_FORMAT_R16_UINT,Jt as VK_FORMAT_R16_UNORM,Ae as VK_FORMAT_R32G32B32A32_SFLOAT,ve as VK_FORMAT_R32G32B32A32_SINT,Le as VK_FORMAT_R32G32B32A32_UINT,Be as VK_FORMAT_R32G32B32_SFLOAT,De as VK_FORMAT_R32G32B32_SINT,me as VK_FORMAT_R32G32B32_UINT,we as VK_FORMAT_R32G32_SFLOAT,de as VK_FORMAT_R32G32_SINT,be as VK_FORMAT_R32G32_UINT,ue as VK_FORMAT_R32_SFLOAT,xe as VK_FORMAT_R32_SINT,ye as VK_FORMAT_R32_UINT,at as VK_FORMAT_R4G4B4A4_UNORM_PACK16,st as VK_FORMAT_R4G4_UNORM_PACK8,ft as VK_FORMAT_R5G5B5A1_UNORM_PACK16,ot as VK_FORMAT_R5G6B5_UNORM_PACK16,ze as VK_FORMAT_R64G64B64A64_SFLOAT,Pe as VK_FORMAT_R64G64B64A64_SINT,Me as VK_FORMAT_R64G64B64A64_UINT,Ce as VK_FORMAT_R64G64B64_SFLOAT,Te as VK_FORMAT_R64G64B64_SINT,Ee as VK_FORMAT_R64G64B64_UINT,Oe as VK_FORMAT_R64G64_SFLOAT,Fe as VK_FORMAT_R64G64_SINT,Se as VK_FORMAT_R64G64_UINT,Ie as VK_FORMAT_R64_SFLOAT,Ve as VK_FORMAT_R64_SINT,ke as VK_FORMAT_R64_UINT,Tt as VK_FORMAT_R8G8B8A8_SINT,Ot as VK_FORMAT_R8G8B8A8_SNORM,Ct as VK_FORMAT_R8G8B8A8_SRGB,Et as VK_FORMAT_R8G8B8A8_UINT,Ft as VK_FORMAT_R8G8B8A8_UNORM,Lt as VK_FORMAT_R8G8B8_SINT,Dt as VK_FORMAT_R8G8B8_SNORM,vt as VK_FORMAT_R8G8B8_SRGB,Bt as VK_FORMAT_R8G8B8_UINT,mt as VK_FORMAT_R8G8B8_UNORM,dt as VK_FORMAT_R8G8_SINT,ut as VK_FORMAT_R8G8_SNORM,wt as VK_FORMAT_R8G8_SRGB,bt as VK_FORMAT_R8G8_UINT,xt as VK_FORMAT_R8G8_UNORM,gt as VK_FORMAT_R8_SINT,_t as VK_FORMAT_R8_SNORM,yt as VK_FORMAT_R8_SRGB,pt as VK_FORMAT_R8_UINT,ct as VK_FORMAT_R8_UNORM,Re as VK_FORMAT_S8_UINT,it as VK_FORMAT_UNDEFINED,Ke as VK_FORMAT_X8_D24_UNORM_PACK32,Pi as read,Wi as write};
