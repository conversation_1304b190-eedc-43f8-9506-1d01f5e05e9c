'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { creatorEconomyService, UserPoints } from '@/services/creatorEconomyService'

interface PointsManagerProps {
  userId: string
  onPointsUpdate?: (points: UserPoints) => void
}

export default function PointsManager({ userId, onPointsUpdate }: PointsManagerProps) {
  const [points, setPoints] = useState<UserPoints>({
    NGT: 0,
    CRT: 0,
    SKL: 0,
    FAN: 0,
    DID: 0
  })
  const [loading, setLoading] = useState(true)
  const [showExchange, setShowExchange] = useState(false)
  const [exchangeFrom, setExchangeFrom] = useState<keyof UserPoints>('NGT')
  const [exchangeTo, setExchangeTo] = useState<keyof UserPoints>('CRT')
  const [exchangeAmount, setExchangeAmount] = useState('')

  // 积分类型配置
  const pointTypes = {
    NGT: {
      name: 'NextGen积分',
      icon: '🌟',
      color: 'from-blue-500 to-indigo-600',
      description: '平台功能积分，用于解锁高级功能'
    },
    CRT: {
      name: 'Creator积分',
      icon: '🎨',
      color: 'from-purple-500 to-pink-600',
      description: '创作价值积分，体现创作贡献'
    },
    SKL: {
      name: 'Skill积分',
      icon: '🛠️',
      color: 'from-green-500 to-emerald-600',
      description: '技能服务积分，用于技能认证'
    },
    FAN: {
      name: 'Fan积分',
      icon: '❤️',
      color: 'from-red-500 to-rose-600',
      description: '社区参与积分，粉丝互动奖励'
    },
    DID: {
      name: '信誉积分',
      icon: '🏆',
      color: 'from-yellow-500 to-orange-600',
      description: '数字身份积分，信誉评价记录'
    }
  }

  // 兑换比例配置
  const exchangeRates = {
    'NGT-CRT': 10,
    'NGT-SKL': 5,
    'NGT-FAN': 20,
    'CRT-DID': 100,
    'SKL-DID': 200,
    'FAN-DID': 50
  }

  useEffect(() => {
    loadUserPoints()
  }, [userId])

  const loadUserPoints = async () => {
    try {
      setLoading(true)
      const userPoints = await creatorEconomyService.getUserPoints(userId)
      setPoints(userPoints)
      onPointsUpdate?.(userPoints)
    } catch (error) {
      console.error('加载积分失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleExchange = async () => {
    if (!exchangeAmount || isNaN(Number(exchangeAmount))) {
      alert('请输入有效的兑换数量')
      return
    }

    const amount = Number(exchangeAmount)
    if (amount <= 0) {
      alert('兑换数量必须大于0')
      return
    }

    if (points[exchangeFrom] < amount) {
      alert('积分余额不足')
      return
    }

    try {
      const result = await creatorEconomyService.exchangePoints(exchangeFrom, exchangeTo, amount)
      if (result.success && result.newBalance) {
        setPoints(result.newBalance)
        onPointsUpdate?.(result.newBalance)
        setExchangeAmount('')
        setShowExchange(false)
        alert('兑换成功！')
      } else {
        alert(result.message || '兑换失败')
      }
    } catch (error) {
      console.error('兑换失败:', error)
      alert('兑换失败，请稍后重试')
    }
  }

  const getExchangeRate = (from: keyof UserPoints, to: keyof UserPoints): number => {
    const key = `${from}-${to}` as keyof typeof exchangeRates
    return exchangeRates[key] || 1
  }

  const calculateExchangeResult = (): number => {
    if (!exchangeAmount || isNaN(Number(exchangeAmount))) return 0
    const amount = Number(exchangeAmount)
    const rate = getExchangeRate(exchangeFrom, exchangeTo)
    return amount * rate
  }

  if (loading) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
        <div className="animate-pulse">
          <div className="h-6 bg-white/20 rounded mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="h-16 bg-white/10 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
      {/* 标题 */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-white">我的积分</h3>
        <button
          onClick={() => setShowExchange(true)}
          className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-white text-sm font-medium hover:from-blue-600 hover:to-purple-600 transition-all"
        >
          积分兑换
        </button>
      </div>

      {/* 积分列表 */}
      <div className="space-y-4">
        {Object.entries(pointTypes).map(([key, config]) => {
          const pointKey = key as keyof UserPoints
          const value = points[pointKey]
          
          return (
            <motion.div
              key={key}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-white/5 rounded-xl p-4 border border-white/10"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-12 h-12 bg-gradient-to-r ${config.color} rounded-xl flex items-center justify-center text-xl`}>
                    {config.icon}
                  </div>
                  <div>
                    <h4 className="text-white font-medium">{config.name}</h4>
                    <p className="text-gray-400 text-sm">{config.description}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-white">{value.toLocaleString()}</div>
                  <div className="text-xs text-gray-400">{key}</div>
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* 合规提示 */}
      <div className="mt-6 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-xl">
        <div className="flex items-start space-x-2">
          <span className="text-yellow-400 text-sm">⚠️</span>
          <div className="text-yellow-200 text-xs">
            <p className="font-medium mb-1">重要提示：</p>
            <p>• 所有积分均为平台内虚拟积分，不具有货币属性</p>
            <p>• 积分仅可用于平台内服务消费，不可提现或转换为法定货币</p>
            <p>• 平台严格遵守国家相关法律法规</p>
          </div>
        </div>
      </div>

      {/* 积分兑换弹窗 */}
      <AnimatePresence>
        {showExchange && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4"
            onClick={() => setShowExchange(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-blue-500/30"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center mb-6">
                <div className="text-4xl mb-4">🔄</div>
                <h3 className="text-xl font-bold text-white mb-2">积分兑换</h3>
                <p className="text-gray-400 text-sm">将一种积分兑换为另一种积分</p>
              </div>

              {/* 兑换选择 */}
              <div className="space-y-4 mb-6">
                <div>
                  <label className="block text-white text-sm font-medium mb-2">从</label>
                  <select
                    value={exchangeFrom}
                    onChange={(e) => setExchangeFrom(e.target.value as keyof UserPoints)}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white"
                  >
                    {Object.entries(pointTypes).map(([key, config]) => (
                      <option key={key} value={key} className="bg-gray-800">
                        {config.name} ({points[key as keyof UserPoints]})
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-white text-sm font-medium mb-2">到</label>
                  <select
                    value={exchangeTo}
                    onChange={(e) => setExchangeTo(e.target.value as keyof UserPoints)}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white"
                  >
                    {Object.entries(pointTypes).map(([key, config]) => (
                      <option key={key} value={key} className="bg-gray-800">
                        {config.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-white text-sm font-medium mb-2">兑换数量</label>
                  <input
                    type="number"
                    value={exchangeAmount}
                    onChange={(e) => setExchangeAmount(e.target.value)}
                    placeholder="请输入兑换数量"
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400"
                  />
                </div>

                {exchangeAmount && (
                  <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
                    <div className="text-blue-400 text-sm">
                      兑换结果: {calculateExchangeResult().toLocaleString()} {pointTypes[exchangeTo].name}
                    </div>
                    <div className="text-gray-400 text-xs mt-1">
                      兑换比例: 1 {pointTypes[exchangeFrom].name} = {getExchangeRate(exchangeFrom, exchangeTo)} {pointTypes[exchangeTo].name}
                    </div>
                  </div>
                )}
              </div>

              {/* 操作按钮 */}
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowExchange(false)}
                  className="flex-1 py-3 bg-gray-700 rounded-xl text-white font-medium"
                >
                  取消
                </button>
                <button
                  onClick={handleExchange}
                  className="flex-1 py-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl text-white font-medium"
                >
                  确认兑换
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
