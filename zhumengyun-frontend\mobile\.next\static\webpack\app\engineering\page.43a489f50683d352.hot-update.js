"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/engineering/page",{

/***/ "(app-pages-browser)/./src/app/engineering/page.tsx":
/*!**************************************!*\
  !*** ./src/app/engineering/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ 工程页面)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_GitHubBottomNavigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/GitHubBottomNavigation */ \"(app-pages-browser)/./src/components/GitHubBottomNavigation.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_ExternalLink_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Building,ExternalLink,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Building_ExternalLink_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Building,ExternalLink,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_ExternalLink_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Building,ExternalLink,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction 工程页面() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const projects = [\n        {\n            id: 1,\n            title: '杭州市智慧城市基础设施建设项目',\n            description: '建设包括5G基站、物联网传感器、智能交通系统等在内的智慧城市基础设施。',\n            location: '浙江省杭州市',\n            budget: '2.5亿',\n            deadline: '2025-12-31',\n            status: 'open',\n            bidCount: 12,\n            viewCount: 256,\n            publishDate: '2025-01-10',\n            organization: '杭州市发展和改革委员会',\n            tags: [\n                '智慧城市',\n                '5G',\n                '物联网',\n                'AI'\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GitHubBottomNavigation__WEBPACK_IMPORTED_MODULE_2__[\"中文页面布局\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GitHubBottomNavigation__WEBPACK_IMPORTED_MODULE_2__[\"中文容器\"], {\n            className: \"py-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GitHubBottomNavigation__WEBPACK_IMPORTED_MODULE_2__[\"中文卡片\"], {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"中文-搜索\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_ExternalLink_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"中文-搜索-图标 w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"搜索工程项目...\",\n                                        className: \"中文-搜索-输入框 w-full\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GitHubBottomNavigation__WEBPACK_IMPORTED_MODULE_2__[\"中文卡片\"], {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_ExternalLink_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-8 h-8 text-[#0969da] mx-auto mb-2\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-[#24292f]\",\n                                children: \"156\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-[#656d76]\",\n                                children: \"活跃项目\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-[#24292f]\",\n                            children: \"项目列表\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        projects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GitHubBottomNavigation__WEBPACK_IMPORTED_MODULE_2__[\"中文卡片\"], {\n                                hover: true,\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-[#24292f] mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-[#0969da] hover:underline\",\n                                                        children: project.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-[#656d76] mb-3\",\n                                                    children: project.description\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 mb-3\",\n                                                    children: project.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"中文-标签-主要 text-xs\",\n                                                            children: tag\n                                                        }, index, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 80,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GitHubBottomNavigation__WEBPACK_IMPORTED_MODULE_2__[\"中文按钮\"], {\n                                            variant: \"primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_ExternalLink_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"查看详情\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            }, project.id, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_s(工程页面, \"I2pdv8LIzBhmmuHoDkZqltArfyE=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/engineering/page.tsx\n"));

/***/ })

});