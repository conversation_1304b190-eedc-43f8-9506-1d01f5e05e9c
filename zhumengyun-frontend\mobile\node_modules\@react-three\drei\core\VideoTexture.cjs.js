"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("three"),r=require("@react-three/fiber"),n=require("suspend-react"),o=require("hls.js");function u(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var s=u(e),c=u(t);const a=((e,t)=>"undefined"!=typeof window&&"function"==typeof(null==(e=window.document)?void 0:e.createElement)&&"string"==typeof(null==(t=window.navigator)?void 0:t.userAgent))();let i=null;function l(t,{unsuspend:s="loadedmetadata",start:l=!0,hls:d={},crossOrigin:p="anonymous",muted:m=!0,loop:v=!0,playsInline:g=!0,onVideoFrame:y,...b}={}){const w=r.useThree((e=>e.gl)),E=e.useRef(null),h=n.suspend((()=>new Promise((async e=>{let r,n;"string"==typeof t?r=t:n=t;const l=Object.assign(document.createElement("video"),{src:r,srcObject:n,crossOrigin:p,loop:v,muted:m,playsInline:g,...b});if(r&&a&&r.endsWith(".m3u8")){const e=E.current=await async function(...e){var t;null!==(t=i)&&void 0!==t||(i=await Promise.resolve().then((function(){return u(require("hls.js"))})));const r=i.default;return r.isSupported()?new r(...e):null}(d);e&&(e.on(o.Events.MEDIA_ATTACHED,(()=>{e.loadSource(r)})),e.attachMedia(l))}const f=new c.VideoTexture(l);"colorSpace"in f?f.colorSpace=w.outputColorSpace:f.encoding=w.outputEncoding,l.addEventListener(s,(()=>e(f)))}))),[t]),O=h.source.data;return f(O,y),e.useEffect((()=>(l&&h.image.play(),()=>{E.current&&(E.current.destroy(),E.current=null)})),[h,l]),h}const d=e.forwardRef((({children:t,src:r,...n},o)=>{const u=l(r,n);return e.useEffect((()=>()=>{u.dispose()}),[u]),e.useImperativeHandle(o,(()=>u),[u]),s.createElement(s.Fragment,null,null==t?void 0:t(u))})),f=(t,r)=>{e.useEffect((()=>{if(!r)return;if(!t.requestVideoFrameCallback)return;let e;const n=(...o)=>{r(...o),e=t.requestVideoFrameCallback(n)};return t.requestVideoFrameCallback(n),()=>t.cancelVideoFrameCallback(e)}),[t,r])};exports.VideoTexture=d,exports.useVideoTexture=l;
