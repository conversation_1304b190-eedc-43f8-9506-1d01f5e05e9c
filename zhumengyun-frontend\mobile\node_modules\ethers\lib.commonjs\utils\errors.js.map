{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src.ts/utils/errors.ts"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;;;AAEH,gDAAyC;AAEzC,mDAAmD;AAenD,SAAS,SAAS,CAAC,KAAU,EAAE,IAAe;IAC1C,IAAI,KAAK,IAAI,IAAI,EAAE;QAAE,OAAO,MAAM,CAAC;KAAE;IAErC,IAAI,IAAI,IAAI,IAAI,EAAE;QAAE,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;KAAE;IACvC,IAAI,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE;QAC5B,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAAE,OAAO,YAAY,CAAC;SAAE;QAC7C,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;KACnB;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACtB,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;KAC1E;IAED,IAAI,KAAK,YAAY,UAAU,EAAE;QAC7B,MAAM,GAAG,GAAG,kBAAkB,CAAC;QAC/B,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YAC7B,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;SACjC;QACD,OAAO,MAAM,CAAC;KACjB;IAED,IAAI,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,IAAI,OAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,UAAU,EAAE;QACnE,OAAO,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;KAC1C;IAED,QAAQ,OAAM,CAAC,KAAK,CAAC,EAAE;QACnB,KAAK,SAAS,CAAC;QAAC,KAAK,QAAQ,CAAC;QAAC,KAAK,QAAQ;YACxC,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC5B,KAAK,QAAQ;YACT,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;QACpC,KAAK,QAAQ;YACT,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACjC,KAAK,QAAQ,CAAC,CAAC;YACX,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChC,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,OAAO,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAI,SAAS,CAAC,CAAC,EAAE,IAAI,CAAE,KAAM,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAE,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;SAC9G;KACJ;IAED,OAAO,yBAAyB,CAAC;AACrC,CAAC;AAsiBD;;;;;;;;;;;;;;;;;;GAkBG;AACH,SAAgB,OAAO,CAAqD,KAAU,EAAE,IAAO;IAC3F,OAAO,CAAC,KAAK,IAAkB,KAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;AACzD,CAAC;AAFD,0BAEC;AAED;;GAEG;AACH,SAAgB,eAAe,CAAC,KAAU;IACtC,OAAO,OAAO,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;AAC5C,CAAC;AAFD,0CAEC;AAED;;;;;;;;;GASG;AACH,SAAgB,SAAS,CAAqD,OAAe,EAAE,IAAO,EAAE,IAAmB;IACvH,IAAI,YAAY,GAAG,OAAO,CAAC;IAE3B;QACI,MAAM,OAAO,GAAkB,EAAE,CAAC;QAClC,IAAI,IAAI,EAAE;YACN,IAAI,SAAS,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,EAAE;gBACvD,MAAM,IAAI,KAAK,CAAC,0CAA2C,SAAS,CAAC,IAAI,CAAE,EAAE,CAAC,CAAC;aAClF;YACD,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;gBACpB,IAAI,GAAG,KAAK,cAAc,EAAE;oBAAE,SAAS;iBAAE;gBACzC,MAAM,KAAK,GAAQ,CAAC,IAAI,CAAqB,GAAG,CAAC,CAAC,CAAC;gBACnE,uBAAuB;gBACH,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC/D,wCAAwC;gBACxC,oDAAoD;gBACpD,0EAA0E;gBAC1E,mBAAmB;aACN;SACJ;QACD,OAAO,CAAC,IAAI,CAAC,QAAS,IAAK,EAAE,CAAC,CAAC;QAC/B,OAAO,CAAC,IAAI,CAAC,WAAY,qBAAQ,EAAE,CAAC,CAAC;QAErC,IAAI,OAAO,CAAC,MAAM,EAAE;YAChB,OAAO,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;SAC9C;KACJ;IAED,IAAI,KAAK,CAAC;IACV,QAAQ,IAAI,EAAE;QACV,KAAK,kBAAkB;YACnB,KAAK,GAAG,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC;YAC/B,MAAM;QACV,KAAK,eAAe,CAAC;QACrB,KAAK,gBAAgB;YACjB,KAAK,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;YAChC,MAAM;QACV;YACI,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;KAClC;IAED,IAAA,gCAAgB,EAA2B,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;IAE5D,IAAI,IAAI,EAAE;QAAE,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;KAAE;IAEzC,IAAU,KAAM,CAAC,YAAY,IAAI,IAAI,EAAE;QACnC,IAAA,gCAAgB,EAA2B,KAAK,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC;KACvE;IAED,OAAU,KAAK,CAAC;AACpB,CAAC;AAlDD,8BAkDC;AAED;;;;;GAKG;AACH,SAAgB,MAAM,CAAqD,KAAc,EAAE,OAAe,EAAE,IAAO,EAAE,IAAmB;IACpI,IAAI,CAAC,KAAK,EAAE;QAAE,MAAM,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;KAAE;AACzD,CAAC;AAFD,wBAEC;AAGD;;;;;;GAMG;AACH,SAAgB,cAAc,CAAC,KAAc,EAAE,OAAe,EAAE,IAAY,EAAE,KAAc;IACxF,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,kBAAkB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AACjF,CAAC;AAFD,wCAEC;AAED,SAAgB,mBAAmB,CAAC,KAAa,EAAE,aAAqB,EAAE,OAAgB;IACtF,IAAI,OAAO,IAAI,IAAI,EAAE;QAAE,OAAO,GAAG,EAAE,CAAC;KAAE;IACtC,IAAI,OAAO,EAAE;QAAE,OAAO,GAAG,IAAI,GAAG,OAAO,CAAC;KAAE;IAE1C,MAAM,CAAC,KAAK,IAAI,aAAa,EAAE,kBAAkB,GAAG,OAAO,EAAE,kBAAkB,EAAE;QAC7E,KAAK,EAAE,KAAK;QACZ,aAAa,EAAE,aAAa;KAC/B,CAAC,CAAC;IAEH,MAAM,CAAC,KAAK,IAAI,aAAa,EAAE,oBAAoB,GAAG,OAAO,EAAE,qBAAqB,EAAE;QAClF,KAAK,EAAE,KAAK;QACZ,aAAa,EAAE,aAAa;KAC/B,CAAC,CAAC;AACP,CAAC;AAbD,kDAaC;AAED,MAAM,eAAe,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;IAC1E,IAAI;QACA,6BAA6B;QAC7B,qBAAqB;QACrB,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,MAAM,EAAE;YAAE,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;SAAE;QAAA,CAAC;QACnE,oBAAoB;QAEpB,IAAI,IAAI,KAAK,KAAK,EAAE;YAChB,MAAM,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;YAClD,qBAAqB;YACrB,IAAI,KAAK,KAAK,QAAQ,EAAE;gBAAE,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAA;aAAE;YACrD,oBAAoB;SACvB;QAED,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACpB;IAAC,OAAM,KAAK,EAAE,GAAG;IAElB,OAAO,KAAK,CAAC;AACjB,CAAC,EAAiB,EAAE,CAAC,CAAC;AAEtB;;GAEG;AACH,SAAgB,eAAe,CAAC,IAAY;IACxC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,6CAA6C,EAAE,uBAAuB,EAAE;QAC/G,SAAS,EAAE,4BAA4B,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE;KAC1D,CAAC,CAAC;AACP,CAAC;AAJD,0CAIC;AAED;;;;;GAKG;AACH,SAAgB,aAAa,CAAC,UAAe,EAAE,KAAU,EAAE,SAAkB;IACzE,IAAI,SAAS,IAAI,IAAI,EAAE;QAAE,SAAS,GAAG,EAAE,CAAC;KAAE;IAC1C,IAAI,UAAU,KAAK,KAAK,EAAE;QACtB,IAAI,MAAM,GAAG,SAAS,EAAE,SAAS,GAAG,KAAK,CAAC;QAC1C,IAAI,SAAS,EAAE;YACX,MAAM,IAAI,GAAG,CAAC;YACd,SAAS,IAAI,GAAG,GAAG,SAAS,CAAC;SAChC;QACD,MAAM,CAAC,KAAK,EAAE,4BAA6B,MAAO,eAAe,EAAE,uBAAuB,EAAE;YACxF,SAAS;SACZ,CAAC,CAAC;KACN;AACL,CAAC;AAZD,sCAYC"}