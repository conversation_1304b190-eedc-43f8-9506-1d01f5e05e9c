/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
export { XRDevice, XRDeviceConfig } from './device/XRDevice.js';
export { metaQuest2, metaQuest3, metaQuestPro, oculusQuest1, } from './device/configs/headset/meta.js';
export { XRSystem } from './initialization/XRSystem.js';
export { XRRenderState } from './session/XRRenderState.js';
export { XRSession } from './session/XRSession.js';
export { XRFrame } from './frameloop/XRFrame.js';
export { XRSpace } from './spaces/XRSpace.js';
export { XRReferenceSpace } from './spaces/XRReferenceSpace.js';
export { XRJointSpace } from './spaces/XRJointSpace.js';
export { XRView } from './views/XRView.js';
export { XRViewport } from './views/XRViewport.js';
export { XRRigidTransform } from './primitives/XRRigidTransform.js';
export { XRPose } from './pose/XRPose.js';
export { XRViewerPose } from './pose/XRViewerPose.js';
export { XRJointPose } from './pose/XRJointPose.js';
export { XRInputSource, XRInputSourceArray } from './input/XRInputSource.js';
export { XRHand } from './input/XRHand.js';
export { XRWebGLLayer, XRLayer } from './layers/XRWebGLLayer.js';
export { XRPlane, XRPlaneSet, NativePlane } from './planes/XRPlane.js';
export { XRMesh, XRMeshSet, NativeMesh } from './meshes/XRMesh.js';
export { XRSemanticLabels } from './labels/labels.js';
export { XRAnchor, XRAnchorSet } from './anchors/XRAnchor.js';
export { XRRay } from './hittest/XRRay.js';
export { XRSessionEvent } from './events/XRSessionEvent.js';
export { XRInputSourceEvent } from './events/XRInputSourceEvent.js';
export { XRInputSourcesChangeEvent } from './events/XRInputSourcesChangeEvent.js';
export { XRReferenceSpaceEvent } from './events/XRReferenceSpaceEvent.js';
export { ActionRecorder } from './action/ActionRecorder.js';
export * from './private.js';
//# sourceMappingURL=index.d.ts.map