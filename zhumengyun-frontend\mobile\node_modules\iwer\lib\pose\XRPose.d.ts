/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_POSE } from '../private.js';
import { XRRigidTransform } from '../primitives/XRRigidTransform.js';
export declare class XRPose {
    [P_POSE]: {
        transform: XRRigidTransform;
        emulatedPosition: boolean;
        linearVelocity?: DOMPointReadOnly;
        angularVelocity?: DOMPointReadOnly;
    };
    constructor(transform: XRRigidTransform, emulatedPosition?: boolean, linearVelocity?: DOMPointReadOnly | undefined, angularVelocity?: DOMPointReadOnly | undefined);
    get transform(): XRRigidTransform;
    get emulatedPosition(): boolean;
    get linearVelocity(): DOMPointReadOnly | undefined;
    get angularVelocity(): DOMPointReadOnly | undefined;
}
//# sourceMappingURL=XRPose.d.ts.map