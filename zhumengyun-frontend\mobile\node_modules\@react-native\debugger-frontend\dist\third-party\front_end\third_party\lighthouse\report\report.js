function e({median:e,p10:t},n){if(e<=0)throw new Error("median must be greater than zero");if(t<=0)throw new Error("p10 must be greater than zero");if(t>=e)throw new Error("p10 must be less than the median");if(n<=0)return 1;let r,i=Math.max(Number.MIN_VALUE,n/e),a=Math.log(i),o=Math.max(Number.MIN_VALUE,t/e),l=(1-function(e){let t=Math.sign(e),n=1/(1+.3275911*(e=Math.abs(e)));return t*(1-n*(.254829592+n*(n*(1.421413741+n*(1.061405429*n-1.453152027))-.284496736))*Math.exp(-e*e))}(.9061938024368232*a/-Math.log(o)))/2;return r=n<=t?Math.max(.9,Math.min(1,l)):n<=e?Math.max(.5,Math.min(.8999999999999999,l)):Math.max(0,Math.min(.49999999999999994,l)),r}var t="…",n={PASS:{label:"pass",minScore:.9},AVERAGE:{label:"average",minScore:.5},FAIL:{label:"fail"},ERROR:{label:"error"}},r=["com","co","gov","edu","ac","org","go","gob","or","net","in","ne","nic","gouv","web","spb","blog","jus","kiev","mil","wi","qc","ca","bel","on"],i=class i{static get RATINGS(){return n}static get PASS_THRESHOLD(){return.9}static get MS_DISPLAY_VALUE(){return"%10d ms"}static getFinalDisplayedUrl(e){if(e.finalDisplayedUrl)return e.finalDisplayedUrl;if(e.finalUrl)return e.finalUrl;throw new Error("Could not determine final displayed URL")}static getMainDocumentUrl(e){return e.mainDocumentUrl||e.finalUrl}static getFullPageScreenshot(e){return e.fullPageScreenshot?e.fullPageScreenshot:e.audits["full-page-screenshot"]?.details}static getEntityFromUrl(e,t){return t&&t.find((t=>t.origins.find((t=>e.startsWith(t)))))||i.getPseudoRootDomain(e)}static splitMarkdownCodeSpans(e){let t=[],n=e.split(/`(.*?)`/g);for(let e=0;e<n.length;e++){let r=n[e];if(!r)continue;let i=e%2!=0;t.push({isCode:i,text:r})}return t}static splitMarkdownLink(e){let t=[],n=e.split(/\[([^\]]+?)\]\((https?:\/\/.*?)\)/g);for(;n.length;){let[e,r,i]=n.splice(0,3);e&&t.push({isLink:!1,text:e}),r&&i&&t.push({isLink:!0,text:r,linkHref:i})}return t}static truncate(e,t,n="…"){if(e.length<=t)return e;let r=new Intl.Segmenter(void 0,{granularity:"grapheme"}).segment(e)[Symbol.iterator](),i=0;for(let a=0;a<=t-n.length;a++){let t=r.next();if(t.done)return e;i=t.value.index}for(let t=0;t<n.length;t++)if(r.next().done)return e;return e.slice(0,i)+n}static getURLDisplayName(e,n){let r,i=void 0!==(n=n||{numPathParts:void 0,preserveQuery:void 0,preserveHost:void 0}).numPathParts?n.numPathParts:2,a=void 0===n.preserveQuery||n.preserveQuery,o=n.preserveHost||!1;if("about:"===e.protocol||"data:"===e.protocol)r=e.href;else{r=e.pathname;let n=r.split("/").filter((e=>e.length));i&&n.length>i&&(r=t+n.slice(-1*i).join("/")),o&&(r=`${e.host}/${r.replace(/^\//,"")}`),a&&(r=`${r}${e.search}`)}if("data:"!==e.protocol&&(r=r.slice(0,200),r=r.replace(/([a-f0-9]{7})[a-f0-9]{13}[a-f0-9]*/g,`$1${t}`),r=r.replace(/([a-zA-Z0-9-_]{9})(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[a-zA-Z0-9-_]{10,}/g,`$1${t}`),r=r.replace(/(\d{3})\d{6,}/g,`$1${t}`),r=r.replace(/\u2026+/g,t),r.length>64&&r.includes("?")&&(r=r.replace(/\?([^=]*)(=)?.*/,`?$1$2${t}`),r.length>64&&(r=r.replace(/\?.*/,`?${t}`)))),r.length>64){let e=r.lastIndexOf(".");r=e>=0?r.slice(0,63-(r.length-e))+`${t}${r.slice(e)}`:r.slice(0,63)+t}return r}static getChromeExtensionOrigin(e){let t=new URL(e);return t.protocol+"//"+t.host}static parseURL(e){let t=new URL(e);return{file:i.getURLDisplayName(t),hostname:t.hostname,origin:"chrome-extension:"===t.protocol?i.getChromeExtensionOrigin(e):t.origin}}static createOrReturnURL(e){return e instanceof URL?e:new URL(e)}static getPseudoTld(e){let t=e.split(".").slice(-2);return r.includes(t[0])?`.${t.join(".")}`:`.${t[t.length-1]}`}static getPseudoRootDomain(e){let t=i.createOrReturnURL(e).hostname,n=i.getPseudoTld(t).split(".");return t.split(".").slice(-n.length).join(".")}static filterRelevantLines(e,t,n){if(0===t.length)return e.slice(0,2*n+1);let r=new Set;return(t=t.sort(((e,t)=>(e.lineNumber||0)-(t.lineNumber||0)))).forEach((({lineNumber:e})=>{let t=e-n,i=e+n;for(;t<1;)t++,i++;r.has(t-3-1)&&(t-=3);for(let e=t;e<=i;e++){let t=e;r.add(t)}})),e.filter((e=>r.has(e.lineNumber)))}static computeLogNormalScore(t,n){let r=e(t,n);return r>.9&&(r+=.05*(r-.9)),Math.floor(100*r)/100}};var a=class{constructor(e,t){this._document=e,this._lighthouseChannel="unknown",this._componentCache=new Map,this.rootEl=t}createElement(e,t){let n=this._document.createElement(e);if(t)for(let e of t.split(/\s+/))e&&n.classList.add(e);return n}createElementNS(e,t,n){let r=this._document.createElementNS(e,t);if(n)for(let e of n.split(/\s+/))e&&r.classList.add(e);return r}createSVGElement(e,t){return this._document.createElementNS("http://www.w3.org/2000/svg",e,t)}createFragment(){return this._document.createDocumentFragment()}createTextNode(e){return this._document.createTextNode(e)}createChildOf(e,t,n){let r=this.createElement(t,n);return e.append(r),r}createComponent(e){let t=this._componentCache.get(e);if(t){let e=t.cloneNode(!0);return this.findAll("style",e).forEach((e=>e.remove())),e}return t=function(e,t){switch(t){case"3pFilter":return function(e){let t=e.createFragment(),n=e.createElement("style");n.append("\n    .lh-3p-filter {\n      color: var(--color-gray-600);\n      float: right;\n      padding: 6px var(--stackpack-padding-horizontal);\n    }\n    .lh-3p-filter-label, .lh-3p-filter-input {\n      vertical-align: middle;\n      user-select: none;\n    }\n    .lh-3p-filter-input:disabled + .lh-3p-ui-string {\n      text-decoration: line-through;\n    }\n  "),t.append(n);let r=e.createElement("div","lh-3p-filter"),i=e.createElement("label","lh-3p-filter-label"),a=e.createElement("input","lh-3p-filter-input");a.setAttribute("type","checkbox"),a.setAttribute("checked","");let o=e.createElement("span","lh-3p-ui-string");o.append("Show 3rd party resources");let l=e.createElement("span","lh-3p-filter-count");return i.append(" ",a," ",o," (",l,") "),r.append(" ",i," "),t.append(r),t}(e);case"audit":return function(e){let t=e.createFragment(),n=e.createElement("div","lh-audit"),r=e.createElement("details","lh-expandable-details"),i=e.createElement("summary"),a=e.createElement("div","lh-audit__header lh-expandable-details__summary"),o=e.createElement("span","lh-audit__score-icon"),l=e.createElement("span","lh-audit__title-and-text"),s=e.createElement("span","lh-audit__title"),c=e.createElement("span","lh-audit__display-text");l.append(" ",s," ",c," ");let d=e.createElement("div","lh-chevron-container");a.append(" ",o," ",l," ",d," "),i.append(" ",a," ");let h=e.createElement("div","lh-audit__description"),p=e.createElement("div","lh-audit__stackpacks");return r.append(" ",i," ",h," ",p," "),n.append(" ",r," "),t.append(n),t}(e);case"categoryHeader":return function(e){let t=e.createFragment(),n=e.createElement("div","lh-category-header"),r=e.createElement("div","lh-score__gauge");r.setAttribute("role","heading"),r.setAttribute("aria-level","2");let i=e.createElement("div","lh-category-header__description");return n.append(" ",r," ",i," "),t.append(n),t}(e);case"chevron":return function(e){let t=e.createFragment(),n=e.createElementNS("http://www.w3.org/2000/svg","svg","lh-chevron");n.setAttribute("viewBox","0 0 100 100");let r=e.createElementNS("http://www.w3.org/2000/svg","g","lh-chevron__lines"),i=e.createElementNS("http://www.w3.org/2000/svg","path","lh-chevron__line lh-chevron__line-left");i.setAttribute("d","M10 50h40");let a=e.createElementNS("http://www.w3.org/2000/svg","path","lh-chevron__line lh-chevron__line-right");return a.setAttribute("d","M90 50H50"),r.append(" ",i," ",a," "),n.append(" ",r," "),t.append(n),t}(e);case"clump":return function(e){let t=e.createFragment(),n=e.createElement("div","lh-audit-group"),r=e.createElement("details","lh-clump"),i=e.createElement("summary"),a=e.createElement("div","lh-audit-group__summary"),o=e.createElement("div","lh-audit-group__header"),l=e.createElement("span","lh-audit-group__title"),s=e.createElement("span","lh-audit-group__itemcount");o.append(" ",l," ",s," "," "," ");let c=e.createElement("div","lh-clump-toggle"),d=e.createElement("span","lh-clump-toggletext--show"),h=e.createElement("span","lh-clump-toggletext--hide");return c.append(" ",d," ",h," "),a.append(" ",o," ",c," "),i.append(" ",a," "),r.append(" ",i," "),n.append(" "," ",r," "),t.append(n),t}(e);case"crc":return function(e){let t=e.createFragment(),n=e.createElement("div","lh-crc-container"),r=e.createElement("style");r.append('\n      .lh-crc .lh-tree-marker {\n        width: 12px;\n        height: 26px;\n        display: block;\n        float: left;\n        background-position: top left;\n      }\n      .lh-crc .lh-horiz-down {\n        background: url(\'data:image/svg+xml;utf8,<svg width="16" height="26" viewBox="0 0 16 26" xmlns="http://www.w3.org/2000/svg"><g fill="%23D8D8D8" fill-rule="evenodd"><path d="M16 12v2H-2v-2z"/><path d="M9 12v14H7V12z"/></g></svg>\');\n      }\n      .lh-crc .lh-right {\n        background: url(\'data:image/svg+xml;utf8,<svg width="16" height="26" viewBox="0 0 16 26" xmlns="http://www.w3.org/2000/svg"><path d="M16 12v2H0v-2z" fill="%23D8D8D8" fill-rule="evenodd"/></svg>\');\n      }\n      .lh-crc .lh-up-right {\n        background: url(\'data:image/svg+xml;utf8,<svg width="16" height="26" viewBox="0 0 16 26" xmlns="http://www.w3.org/2000/svg"><path d="M7 0h2v14H7zm2 12h7v2H9z" fill="%23D8D8D8" fill-rule="evenodd"/></svg>\');\n      }\n      .lh-crc .lh-vert-right {\n        background: url(\'data:image/svg+xml;utf8,<svg width="16" height="26" viewBox="0 0 16 26" xmlns="http://www.w3.org/2000/svg"><path d="M7 0h2v27H7zm2 12h7v2H9z" fill="%23D8D8D8" fill-rule="evenodd"/></svg>\');\n      }\n      .lh-crc .lh-vert {\n        background: url(\'data:image/svg+xml;utf8,<svg width="16" height="26" viewBox="0 0 16 26" xmlns="http://www.w3.org/2000/svg"><path d="M7 0h2v26H7z" fill="%23D8D8D8" fill-rule="evenodd"/></svg>\');\n      }\n      .lh-crc .lh-crc-tree {\n        font-size: 14px;\n        width: 100%;\n        overflow-x: auto;\n      }\n      .lh-crc .lh-crc-node {\n        height: 26px;\n        line-height: 26px;\n        white-space: nowrap;\n      }\n      .lh-crc .lh-crc-node__tree-value {\n        margin-left: 10px;\n      }\n      .lh-crc .lh-crc-node__tree-value div {\n        display: inline;\n      }\n      .lh-crc .lh-crc-node__chain-duration {\n        font-weight: 700;\n      }\n      .lh-crc .lh-crc-initial-nav {\n        color: #595959;\n        font-style: italic;\n      }\n      .lh-crc__summary-value {\n        margin-bottom: 10px;\n      }\n    ');let i=e.createElement("div"),a=e.createElement("div","lh-crc__summary-value"),o=e.createElement("span","lh-crc__longest_duration_label"),l=e.createElement("b","lh-crc__longest_duration");a.append(" ",o," ",l," "),i.append(" ",a," ");let s=e.createElement("div","lh-crc"),c=e.createElement("div","lh-crc-initial-nav");return s.append(" ",c," "," "),n.append(" ",r," ",i," ",s," "),t.append(n),t}(e);case"crcChain":return function(e){let t=e.createFragment(),n=e.createElement("div","lh-crc-node"),r=e.createElement("span","lh-crc-node__tree-marker"),i=e.createElement("span","lh-crc-node__tree-value");return n.append(" ",r," ",i," "),t.append(n),t}(e);case"elementScreenshot":return function(e){let t=e.createFragment(),n=e.createElement("div","lh-element-screenshot"),r=e.createElement("div","lh-element-screenshot__content"),i=e.createElement("div","lh-element-screenshot__image"),a=e.createElement("div","lh-element-screenshot__mask"),o=e.createElementNS("http://www.w3.org/2000/svg","svg");o.setAttribute("height","0"),o.setAttribute("width","0");let l=e.createElementNS("http://www.w3.org/2000/svg","defs"),s=e.createElementNS("http://www.w3.org/2000/svg","clipPath");s.setAttribute("clipPathUnits","objectBoundingBox"),l.append(" ",s," "," "),o.append(" ",l," "),a.append(" ",o," ");let c=e.createElement("div","lh-element-screenshot__element-marker");return i.append(" ",a," ",c," "),r.append(" ",i," "),n.append(" ",r," "),t.append(n),t}(e);case"explodeyGauge":return function(e){let t=e.createFragment(),n=e.createElement("div","lh-exp-gauge-component"),r=e.createElement("div","lh-exp-gauge__wrapper");r.setAttribute("target","_blank");let i=e.createElement("div","lh-exp-gauge__svg-wrapper"),a=e.createElementNS("http://www.w3.org/2000/svg","svg","lh-exp-gauge"),o=e.createElementNS("http://www.w3.org/2000/svg","g","lh-exp-gauge__inner"),l=e.createElementNS("http://www.w3.org/2000/svg","circle","lh-exp-gauge__bg"),s=e.createElementNS("http://www.w3.org/2000/svg","circle","lh-exp-gauge__base lh-exp-gauge--faded"),c=e.createElementNS("http://www.w3.org/2000/svg","circle","lh-exp-gauge__arc"),d=e.createElementNS("http://www.w3.org/2000/svg","text","lh-exp-gauge__percentage");o.append(" ",l," ",s," ",c," ",d," ");let h=e.createElementNS("http://www.w3.org/2000/svg","g","lh-exp-gauge__outer"),p=e.createElementNS("http://www.w3.org/2000/svg","circle","lh-cover");h.append(" ",p," ");let g=e.createElementNS("http://www.w3.org/2000/svg","text","lh-exp-gauge__label");return g.setAttribute("text-anchor","middle"),g.setAttribute("x","0"),g.setAttribute("y","60"),a.append(" ",o," ",h," ",g," "),i.append(" ",a," "),r.append(" ",i," "),n.append(" ",r," "),t.append(n),t}(e);case"footer":return function(e){let t=e.createFragment(),n=e.createElement("style");n.append("\n    .lh-footer {\n      padding: var(--footer-padding-vertical) calc(var(--default-padding) * 2);\n      max-width: var(--report-content-max-width);\n      margin: 0 auto;\n    }\n    .lh-footer .lh-generated {\n      text-align: center;\n    }\n  "),t.append(n);let r=e.createElement("footer","lh-footer"),i=e.createElement("ul","lh-meta__items");i.append(" ");let a=e.createElement("div","lh-generated"),o=e.createElement("b");o.append("Lighthouse");let l=e.createElement("span","lh-footer__version"),s=e.createElement("a","lh-footer__version_issue");return s.setAttribute("href","https://github.com/GoogleChrome/Lighthouse/issues"),s.setAttribute("target","_blank"),s.setAttribute("rel","noopener"),s.append("File an issue"),a.append(" "," Generated by ",o," ",l," | ",s," "),r.append(" ",i," ",a," "),t.append(r),t}(e);case"fraction":return function(e){let t=e.createFragment(),n=e.createElement("a","lh-fraction__wrapper"),r=e.createElement("div","lh-fraction__content-wrapper"),i=e.createElement("div","lh-fraction__content"),a=e.createElement("div","lh-fraction__background");i.append(" ",a," "),r.append(" ",i," ");let o=e.createElement("div","lh-fraction__label");return n.append(" ",r," ",o," "),t.append(n),t}(e);case"gauge":return function(e){let t=e.createFragment(),n=e.createElement("a","lh-gauge__wrapper"),r=e.createElement("div","lh-gauge__svg-wrapper"),i=e.createElementNS("http://www.w3.org/2000/svg","svg","lh-gauge");i.setAttribute("viewBox","0 0 120 120");let a=e.createElementNS("http://www.w3.org/2000/svg","circle","lh-gauge-base");a.setAttribute("r","56"),a.setAttribute("cx","60"),a.setAttribute("cy","60"),a.setAttribute("stroke-width","8");let o=e.createElementNS("http://www.w3.org/2000/svg","circle","lh-gauge-arc");o.setAttribute("r","56"),o.setAttribute("cx","60"),o.setAttribute("cy","60"),o.setAttribute("stroke-width","8"),i.append(" ",a," ",o," "),r.append(" ",i," ");let l=e.createElement("div","lh-gauge__percentage"),s=e.createElement("div","lh-gauge__label");return n.append(" "," ",r," ",l," "," ",s," "),t.append(n),t}(e);case"heading":return function(e){let t=e.createFragment(),n=e.createElement("style");n.append("\n    /* CSS Fireworks. Originally by Eddie Lin\n       https://codepen.io/paulirish/pen/yEVMbP\n    */\n    .lh-pyro {\n      display: none;\n      z-index: 1;\n      pointer-events: none;\n    }\n    .lh-score100 .lh-pyro {\n      display: block;\n    }\n    .lh-score100 .lh-lighthouse stop:first-child {\n      stop-color: hsla(200, 12%, 95%, 0);\n    }\n    .lh-score100 .lh-lighthouse stop:last-child {\n      stop-color: hsla(65, 81%, 76%, 1);\n    }\n\n    .lh-pyro > .lh-pyro-before, .lh-pyro > .lh-pyro-after {\n      position: absolute;\n      width: 5px;\n      height: 5px;\n      border-radius: 2.5px;\n      box-shadow: 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff;\n      animation: 1s bang ease-out infinite backwards,  1s gravity ease-in infinite backwards,  5s position linear infinite backwards;\n      animation-delay: 1s, 1s, 1s;\n    }\n\n    .lh-pyro > .lh-pyro-after {\n      animation-delay: 2.25s, 2.25s, 2.25s;\n      animation-duration: 1.25s, 1.25s, 6.25s;\n    }\n\n    @keyframes bang {\n      to {\n        opacity: 1;\n        box-shadow: -70px -115.67px #47ebbc, -28px -99.67px #eb47a4, 58px -31.67px #7eeb47, 13px -141.67px #eb47c5, -19px 6.33px #7347eb, -2px -74.67px #ebd247, 24px -151.67px #eb47e0, 57px -138.67px #b4eb47, -51px -104.67px #479eeb, 62px 8.33px #ebcf47, -93px 0.33px #d547eb, -16px -118.67px #47bfeb, 53px -84.67px #47eb83, 66px -57.67px #eb47bf, -93px -65.67px #91eb47, 30px -13.67px #86eb47, -2px -59.67px #83eb47, -44px 1.33px #eb47eb, 61px -58.67px #47eb73, 5px -22.67px #47e8eb, -66px -28.67px #ebe247, 42px -123.67px #eb5547, -75px 26.33px #7beb47, 15px -52.67px #a147eb, 36px -51.67px #eb8347, -38px -12.67px #eb5547, -46px -59.67px #47eb81, 78px -114.67px #eb47ba, 15px -156.67px #eb47bf, -36px 1.33px #eb4783, -72px -86.67px #eba147, 31px -46.67px #ebe247, -68px 29.33px #47e2eb, -55px 19.33px #ebe047, -56px 27.33px #4776eb, -13px -91.67px #eb5547, -47px -138.67px #47ebc7, -18px -96.67px #eb47ac, 11px -88.67px #4783eb, -67px -28.67px #47baeb, 53px 10.33px #ba47eb, 11px 19.33px #5247eb, -5px -11.67px #eb4791, -68px -4.67px #47eba7, 95px -37.67px #eb478b, -67px -162.67px #eb5d47, -54px -120.67px #eb6847, 49px -12.67px #ebe047, 88px 8.33px #47ebda, 97px 33.33px #eb8147, 6px -71.67px #ebbc47;\n      }\n    }\n    @keyframes gravity {\n      from {\n        opacity: 1;\n      }\n      to {\n        transform: translateY(80px);\n        opacity: 0;\n      }\n    }\n    @keyframes position {\n      0%, 19.9% {\n        margin-top: 4%;\n        margin-left: 47%;\n      }\n      20%, 39.9% {\n        margin-top: 7%;\n        margin-left: 30%;\n      }\n      40%, 59.9% {\n        margin-top: 6%;\n        margin-left: 70%;\n      }\n      60%, 79.9% {\n        margin-top: 3%;\n        margin-left: 20%;\n      }\n      80%, 99.9% {\n        margin-top: 3%;\n        margin-left: 80%;\n      }\n    }\n  "),t.append(n);let r=e.createElement("div","lh-header-container"),i=e.createElement("div","lh-scores-wrapper-placeholder");return r.append(" ",i," "),t.append(r),t}(e);case"metric":return function(e){let t=e.createFragment(),n=e.createElement("div","lh-metric"),r=e.createElement("div","lh-metric__innerwrap"),i=e.createElement("div","lh-metric__icon"),a=e.createElement("span","lh-metric__title"),o=e.createElement("div","lh-metric__value"),l=e.createElement("div","lh-metric__description");return r.append(" ",i," ",a," ",o," ",l," "),n.append(" ",r," "),t.append(n),t}(e);case"scorescale":return function(e){let t=e.createFragment(),n=e.createElement("div","lh-scorescale"),r=e.createElement("span","lh-scorescale-range lh-scorescale-range--fail");r.append("0–49");let i=e.createElement("span","lh-scorescale-range lh-scorescale-range--average");i.append("50–89");let a=e.createElement("span","lh-scorescale-range lh-scorescale-range--pass");return a.append("90–100"),n.append(" ",r," ",i," ",a," "),t.append(n),t}(e);case"scoresWrapper":return function(e){let t=e.createFragment(),n=e.createElement("style");n.append("\n    .lh-scores-container {\n      display: flex;\n      flex-direction: column;\n      padding: var(--default-padding) 0;\n      position: relative;\n      width: 100%;\n    }\n\n    .lh-sticky-header {\n      --gauge-circle-size: var(--gauge-circle-size-sm);\n      --plugin-badge-size: 16px;\n      --plugin-icon-size: 75%;\n      --gauge-wrapper-width: 60px;\n      --gauge-percentage-font-size: 13px;\n      position: fixed;\n      left: 0;\n      right: 0;\n      top: var(--topbar-height);\n      font-weight: 500;\n      display: none;\n      justify-content: center;\n      background-color: var(--sticky-header-background-color);\n      border-bottom: 1px solid var(--color-gray-200);\n      padding-top: var(--score-container-padding);\n      padding-bottom: 4px;\n      z-index: 2;\n      pointer-events: none;\n    }\n\n    .lh-devtools .lh-sticky-header {\n      /* The report within DevTools is placed in a container with overflow, which changes the placement of this header unless we change `position` to `sticky.` */\n      position: sticky;\n    }\n\n    .lh-sticky-header--visible {\n      display: grid;\n      grid-auto-flow: column;\n      pointer-events: auto;\n    }\n\n    /* Disable the gauge arc animation for the sticky header, so toggling display: none\n       does not play the animation. */\n    .lh-sticky-header .lh-gauge-arc {\n      animation: none;\n    }\n\n    .lh-sticky-header .lh-gauge__label,\n    .lh-sticky-header .lh-fraction__label {\n      display: none;\n    }\n\n    .lh-highlighter {\n      width: var(--gauge-wrapper-width);\n      height: 1px;\n      background-color: var(--highlighter-background-color);\n      /* Position at bottom of first gauge in sticky header. */\n      position: absolute;\n      grid-column: 1;\n      bottom: -1px;\n      left: 0px;\n      right: 0px;\n    }\n  "),t.append(n);let r=e.createElement("div","lh-scores-wrapper"),i=e.createElement("div","lh-scores-container"),a=e.createElement("div","lh-pyro"),o=e.createElement("div","lh-pyro-before"),l=e.createElement("div","lh-pyro-after");return a.append(" ",o," ",l," "),i.append(" ",a," "),r.append(" ",i," "),t.append(r),t}(e);case"snippet":return function(e){let t=e.createFragment(),n=e.createElement("div","lh-snippet"),r=e.createElement("style");return r.append('\n          :root {\n            --snippet-highlight-light: #fbf1f2;\n            --snippet-highlight-dark: #ffd6d8;\n          }\n\n         .lh-snippet__header {\n          position: relative;\n          overflow: hidden;\n          padding: 10px;\n          border-bottom: none;\n          color: var(--snippet-color);\n          background-color: var(--snippet-background-color);\n          border: 1px solid var(--report-border-color-secondary);\n        }\n        .lh-snippet__title {\n          font-weight: bold;\n          float: left;\n        }\n        .lh-snippet__node {\n          float: left;\n          margin-left: 4px;\n        }\n        .lh-snippet__toggle-expand {\n          padding: 1px 7px;\n          margin-top: -1px;\n          margin-right: -7px;\n          float: right;\n          background: transparent;\n          border: none;\n          cursor: pointer;\n          font-size: 14px;\n          color: #0c50c7;\n        }\n\n        .lh-snippet__snippet {\n          overflow: auto;\n          border: 1px solid var(--report-border-color-secondary);\n        }\n        /* Container needed so that all children grow to the width of the scroll container */\n        .lh-snippet__snippet-inner {\n          display: inline-block;\n          min-width: 100%;\n        }\n\n        .lh-snippet:not(.lh-snippet--expanded) .lh-snippet__show-if-expanded {\n          display: none;\n        }\n        .lh-snippet.lh-snippet--expanded .lh-snippet__show-if-collapsed {\n          display: none;\n        }\n\n        .lh-snippet__line {\n          background: white;\n          white-space: pre;\n          display: flex;\n        }\n        .lh-snippet__line:not(.lh-snippet__line--message):first-child {\n          padding-top: 4px;\n        }\n        .lh-snippet__line:not(.lh-snippet__line--message):last-child {\n          padding-bottom: 4px;\n        }\n        .lh-snippet__line--content-highlighted {\n          background: var(--snippet-highlight-dark);\n        }\n        .lh-snippet__line--message {\n          background: var(--snippet-highlight-light);\n        }\n        .lh-snippet__line--message .lh-snippet__line-number {\n          padding-top: 10px;\n          padding-bottom: 10px;\n        }\n        .lh-snippet__line--message code {\n          padding: 10px;\n          padding-left: 5px;\n          color: var(--color-fail);\n          font-family: var(--report-font-family);\n        }\n        .lh-snippet__line--message code {\n          white-space: normal;\n        }\n        .lh-snippet__line-icon {\n          padding-top: 10px;\n          display: none;\n        }\n        .lh-snippet__line--message .lh-snippet__line-icon {\n          display: block;\n        }\n        .lh-snippet__line-icon:before {\n          content: "";\n          display: inline-block;\n          vertical-align: middle;\n          margin-right: 4px;\n          width: var(--score-icon-size);\n          height: var(--score-icon-size);\n          background-image: var(--fail-icon-url);\n        }\n        .lh-snippet__line-number {\n          flex-shrink: 0;\n          width: 40px;\n          text-align: right;\n          font-family: monospace;\n          padding-right: 5px;\n          margin-right: 5px;\n          color: var(--color-gray-600);\n          user-select: none;\n        }\n    '),n.append(" ",r," "),t.append(n),t}(e);case"snippetContent":return function(e){let t=e.createFragment(),n=e.createElement("div","lh-snippet__snippet"),r=e.createElement("div","lh-snippet__snippet-inner");return n.append(" ",r," "),t.append(n),t}(e);case"snippetHeader":return function(e){let t=e.createFragment(),n=e.createElement("div","lh-snippet__header"),r=e.createElement("div","lh-snippet__title"),i=e.createElement("div","lh-snippet__node"),a=e.createElement("button","lh-snippet__toggle-expand"),o=e.createElement("span","lh-snippet__btn-label-collapse lh-snippet__show-if-expanded"),l=e.createElement("span","lh-snippet__btn-label-expand lh-snippet__show-if-collapsed");return a.append(" ",o," ",l," "),n.append(" ",r," ",i," ",a," "),t.append(n),t}(e);case"snippetLine":return function(e){let t=e.createFragment(),n=e.createElement("div","lh-snippet__line"),r=e.createElement("div","lh-snippet__line-number"),i=e.createElement("div","lh-snippet__line-icon"),a=e.createElement("code");return n.append(" ",r," ",i," ",a," "),t.append(n),t}(e);case"styles":return function(e){let t=e.createFragment(),n=e.createElement("style");return n.append('/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/*\n  Naming convention:\n\n  If a variable is used for a specific component: --{component}-{property name}-{modifier}\n\n  Both {component} and {property name} should be kebab-case. If the target is the entire page,\n  use \'report\' for the component. The property name should not be abbreviated. Use the\n  property name the variable is intended for - if it\'s used for multiple, a common descriptor\n  is fine (ex: \'size\' for a variable applied to \'width\' and \'height\'). If a variable is shared\n  across multiple components, either create more variables or just drop the "{component}-"\n  part of the name. Append any modifiers at the end (ex: \'big\', \'dark\').\n\n  For colors: --color-{hue}-{intensity}\n\n  {intensity} is the Material Design tag - 700, A700, etc.\n*/\n.lh-vars {\n  /* Palette using Material Design Colors\n   * https://www.materialui.co/colors */\n  --color-amber-50: #FFF8E1;\n  --color-blue-200: #90CAF9;\n  --color-blue-900: #0D47A1;\n  --color-blue-A700: #2962FF;\n  --color-blue-primary: #06f;\n  --color-cyan-500: #00BCD4;\n  --color-gray-100: #F5F5F5;\n  --color-gray-300: #CFCFCF;\n  --color-gray-200: #E0E0E0;\n  --color-gray-400: #BDBDBD;\n  --color-gray-50: #FAFAFA;\n  --color-gray-500: #9E9E9E;\n  --color-gray-600: #757575;\n  --color-gray-700: #616161;\n  --color-gray-800: #424242;\n  --color-gray-900: #212121;\n  --color-gray: #000000;\n  --color-green-700: #080;\n  --color-green: #0c6;\n  --color-lime-400: #D3E156;\n  --color-orange-50: #FFF3E0;\n  --color-orange-700: #C33300;\n  --color-orange: #fa3;\n  --color-red-700: #c00;\n  --color-red: #f33;\n  --color-teal-600: #00897B;\n  --color-white: #FFFFFF;\n\n  /* Context-specific colors */\n  --color-average-secondary: var(--color-orange-700);\n  --color-average: var(--color-orange);\n  --color-fail-secondary: var(--color-red-700);\n  --color-fail: var(--color-red);\n  --color-hover: var(--color-gray-50);\n  --color-informative: var(--color-blue-900);\n  --color-pass-secondary: var(--color-green-700);\n  --color-pass: var(--color-green);\n  --color-not-applicable: var(--color-gray-600);\n\n  /* Component variables */\n  --audit-description-padding-left: calc(var(--score-icon-size) + var(--score-icon-margin-left) + var(--score-icon-margin-right));\n  --audit-explanation-line-height: 16px;\n  --audit-group-margin-bottom: calc(var(--default-padding) * 6);\n  --audit-group-padding-vertical: 8px;\n  --audit-margin-horizontal: 5px;\n  --audit-padding-vertical: 8px;\n  --category-padding: calc(var(--default-padding) * 6) var(--edge-gap-padding) calc(var(--default-padding) * 4);\n  --chevron-line-stroke: var(--color-gray-600);\n  --chevron-size: 12px;\n  --default-padding: 8px;\n  --edge-gap-padding: calc(var(--default-padding) * 4);\n  --env-item-background-color: var(--color-gray-100);\n  --env-item-font-size: 28px;\n  --env-item-line-height: 36px;\n  --env-item-padding: 10px 0px;\n  --env-name-min-width: 220px;\n  --footer-padding-vertical: 16px;\n  --gauge-circle-size-big: 96px;\n  --gauge-circle-size: 48px;\n  --gauge-circle-size-sm: 32px;\n  --gauge-label-font-size-big: 18px;\n  --gauge-label-font-size: var(--report-font-size-secondary);\n  --gauge-label-line-height-big: 24px;\n  --gauge-label-line-height: var(--report-line-height-secondary);\n  --gauge-percentage-font-size-big: 38px;\n  --gauge-percentage-font-size: var(--report-font-size-secondary);\n  --gauge-wrapper-width: 120px;\n  --header-line-height: 24px;\n  --highlighter-background-color: var(--report-text-color);\n  --icon-square-size: calc(var(--score-icon-size) * 0.88);\n  --image-preview-size: 48px;\n  --link-color: var(--color-blue-primary);\n  --locale-selector-background-color: var(--color-white);\n  --metric-toggle-lines-fill: #7F7F7F;\n  --metric-value-font-size: calc(var(--report-font-size) * 1.8);\n  --metrics-toggle-background-color: var(--color-gray-200);\n  --plugin-badge-background-color: var(--color-white);\n  --plugin-badge-size-big: calc(var(--gauge-circle-size-big) / 2.7);\n  --plugin-badge-size: calc(var(--gauge-circle-size) / 2.7);\n  --plugin-icon-size: 65%;\n  --report-background-color: #fff;\n  --report-border-color-secondary: #ebebeb;\n  --report-font-family-monospace: \'Roboto Mono\', \'Menlo\', \'dejavu sans mono\', \'Consolas\', \'Lucida Console\', monospace;\n  --report-font-family: Roboto, Helvetica, Arial, sans-serif;\n  --report-font-size: 14px;\n  --report-font-size-secondary: 12px;\n  --report-icon-size: var(--score-icon-background-size);\n  --report-line-height: 24px;\n  --report-line-height-secondary: 20px;\n  --report-monospace-font-size: calc(var(--report-font-size) * 0.85);\n  --report-text-color-secondary: var(--color-gray-800);\n  --report-text-color: var(--color-gray-900);\n  --report-content-max-width: calc(60 * var(--report-font-size)); /* defaults to 840px */\n  --report-content-min-width: 360px;\n  --report-content-max-width-minus-edge-gap: calc(var(--report-content-max-width) - var(--edge-gap-padding) * 2);\n  --score-container-padding: 8px;\n  --score-icon-background-size: 24px;\n  --score-icon-margin-left: 6px;\n  --score-icon-margin-right: 14px;\n  --score-icon-margin: 0 var(--score-icon-margin-right) 0 var(--score-icon-margin-left);\n  --score-icon-size: 12px;\n  --score-icon-size-big: 16px;\n  --screenshot-overlay-background: rgba(0, 0, 0, 0.3);\n  --section-padding-vertical: calc(var(--default-padding) * 6);\n  --snippet-background-color: var(--color-gray-50);\n  --snippet-color: #0938C2;\n  --stackpack-padding-horizontal: 10px;\n  --sticky-header-background-color: var(--report-background-color);\n  --sticky-header-buffer: var(--topbar-height);\n  --sticky-header-height: calc(var(--gauge-circle-size-sm) + var(--score-container-padding) * 2 + 1em);\n  --table-group-header-background-color: #EEF1F4;\n  --table-group-header-text-color: var(--color-gray-700);\n  --table-higlight-background-color: #F5F7FA;\n  --tools-icon-color: var(--color-gray-600);\n  --topbar-background-color: var(--color-white);\n  --topbar-height: 32px;\n  --topbar-logo-size: 24px;\n  --topbar-padding: 0 8px;\n  --toplevel-warning-background-color: hsla(30, 100%, 75%, 10%);\n  --toplevel-warning-message-text-color: var(--color-average-secondary);\n  --toplevel-warning-padding: 18px;\n  --toplevel-warning-text-color: var(--report-text-color);\n\n  /* SVGs */\n  --plugin-icon-url-dark: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" fill="%23FFFFFF"><path d="M0 0h24v24H0z" fill="none"/><path d="M20.5 11H19V7c0-1.1-.9-2-2-2h-4V3.5C13 2.12 11.88 1 10.5 1S8 2.12 8 3.5V5H4c-1.1 0-1.99.9-1.99 2v3.8H3.5c1.49 0 2.7 1.21 2.7 2.7s-1.21 2.7-2.7 2.7H2V20c0 1.1.9 2 2 2h3.8v-1.5c0-1.49 1.21-2.7 2.7-2.7 1.49 0 2.7 1.21 2.7 2.7V22H17c1.1 0 2-.9 2-2v-4h1.5c1.38 0 2.5-1.12 2.5-2.5S21.88 11 20.5 11z"/></svg>\');\n  --plugin-icon-url: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" fill="%23757575"><path d="M0 0h24v24H0z" fill="none"/><path d="M20.5 11H19V7c0-1.1-.9-2-2-2h-4V3.5C13 2.12 11.88 1 10.5 1S8 2.12 8 3.5V5H4c-1.1 0-1.99.9-1.99 2v3.8H3.5c1.49 0 2.7 1.21 2.7 2.7s-1.21 2.7-2.7 2.7H2V20c0 1.1.9 2 2 2h3.8v-1.5c0-1.49 1.21-2.7 2.7-2.7 1.49 0 2.7 1.21 2.7 2.7V22H17c1.1 0 2-.9 2-2v-4h1.5c1.38 0 2.5-1.12 2.5-2.5S21.88 11 20.5 11z"/></svg>\');\n\n  --pass-icon-url: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48"><title>check</title><path fill="%23178239" d="M24 4C12.95 4 4 12.95 4 24c0 11.04 8.95 20 20 20 11.04 0 20-8.96 20-20 0-11.05-8.96-20-20-20zm-4 30L10 24l2.83-2.83L20 28.34l15.17-15.17L38 16 20 34z"/></svg>\');\n  --average-icon-url: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48"><title>info</title><path fill="%23E67700" d="M24 4C12.95 4 4 12.95 4 24s8.95 20 20 20 20-8.95 20-20S35.05 4 24 4zm2 30h-4V22h4v12zm0-16h-4v-4h4v4z"/></svg>\');\n  --fail-icon-url: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48"><title>warn</title><path fill="%23C7221F" d="M2 42h44L24 4 2 42zm24-6h-4v-4h4v4zm0-8h-4v-8h4v8z"/></svg>\');\n  --error-icon-url: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 3 15"><title>error</title><path d="M0 15H 3V 12H 0V" fill="%23FF4E42"/><path d="M0 9H 3V 0H 0V" fill="%23FF4E42"/></svg>\');\n\n  --swap-locale-icon-url: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#000000"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/></svg>\');\n}\n\n@media not print {\n  .lh-dark {\n    /* Pallete */\n    --color-gray-200: var(--color-gray-800);\n    --color-gray-300: #616161;\n    --color-gray-400: var(--color-gray-600);\n    --color-gray-700: var(--color-gray-400);\n    --color-gray-50: #757575;\n    --color-gray-600: var(--color-gray-500);\n    --color-green-700: var(--color-green);\n    --color-orange-700: var(--color-orange);\n    --color-red-700: var(--color-red);\n    --color-teal-600: var(--color-cyan-500);\n\n    /* Context-specific colors */\n    --color-hover: rgba(0, 0, 0, 0.2);\n    --color-informative: var(--color-blue-200);\n\n    /* Component variables */\n    --env-item-background-color: #393535;\n    --link-color: var(--color-blue-200);\n    --locale-selector-background-color: var(--color-gray-200);\n    --plugin-badge-background-color: var(--color-gray-800);\n    --report-background-color: var(--color-gray-900);\n    --report-border-color-secondary: var(--color-gray-200);\n    --report-text-color-secondary: var(--color-gray-400);\n    --report-text-color: var(--color-gray-100);\n    --snippet-color: var(--color-cyan-500);\n    --topbar-background-color: var(--color-gray);\n    --toplevel-warning-background-color: hsl(33deg 14% 18%);\n    --toplevel-warning-message-text-color: var(--color-orange-700);\n    --toplevel-warning-text-color: var(--color-gray-100);\n    --table-group-header-background-color: rgba(186, 196, 206, 0.15);\n    --table-group-header-text-color: var(--color-gray-100);\n    --table-higlight-background-color: rgba(186, 196, 206, 0.09);\n\n    /* SVGs */\n    --plugin-icon-url: var(--plugin-icon-url-dark);\n  }\n}\n\n@media only screen and (max-width: 480px) {\n  .lh-vars {\n    --audit-group-margin-bottom: 20px;\n    --edge-gap-padding: var(--default-padding);\n    --env-name-min-width: 120px;\n    --gauge-circle-size-big: 96px;\n    --gauge-circle-size: 72px;\n    --gauge-label-font-size-big: 22px;\n    --gauge-label-font-size: 14px;\n    --gauge-label-line-height-big: 26px;\n    --gauge-label-line-height: 20px;\n    --gauge-percentage-font-size-big: 34px;\n    --gauge-percentage-font-size: 26px;\n    --gauge-wrapper-width: 112px;\n    --header-padding: 16px 0 16px 0;\n    --image-preview-size: 24px;\n    --plugin-icon-size: 75%;\n    --report-font-size: 14px;\n    --report-line-height: 20px;\n    --score-icon-margin-left: 2px;\n    --score-icon-size: 10px;\n    --topbar-height: 28px;\n    --topbar-logo-size: 20px;\n  }\n}\n\n.lh-vars.lh-devtools {\n  --audit-explanation-line-height: 14px;\n  --audit-group-margin-bottom: 20px;\n  --audit-group-padding-vertical: 12px;\n  --audit-padding-vertical: 4px;\n  --category-padding: 12px;\n  --default-padding: 12px;\n  --env-name-min-width: 120px;\n  --footer-padding-vertical: 8px;\n  --gauge-circle-size-big: 72px;\n  --gauge-circle-size: 64px;\n  --gauge-label-font-size-big: 22px;\n  --gauge-label-font-size: 14px;\n  --gauge-label-line-height-big: 26px;\n  --gauge-label-line-height: 20px;\n  --gauge-percentage-font-size-big: 34px;\n  --gauge-percentage-font-size: 26px;\n  --gauge-wrapper-width: 97px;\n  --header-line-height: 20px;\n  --header-padding: 16px 0 16px 0;\n  --screenshot-overlay-background: transparent;\n  --plugin-icon-size: 75%;\n  --report-font-family-monospace: \'Menlo\', \'dejavu sans mono\', \'Consolas\', \'Lucida Console\', monospace;\n  --report-font-family: \'.SFNSDisplay-Regular\', \'Helvetica Neue\', \'Lucida Grande\', sans-serif;\n  --report-font-size: 12px;\n  --report-line-height: 20px;\n  --score-icon-margin-left: 2px;\n  --score-icon-size: 10px;\n  --section-padding-vertical: 8px;\n}\n\n.lh-container:has(.lh-sticky-header) {\n  --sticky-header-buffer: calc(var(--topbar-height) + var(--sticky-header-height));\n}\n\n.lh-container:not(.lh-topbar + .lh-container) {\n  --topbar-height: 0;\n  --sticky-header-height: 0;\n  --sticky-header-buffer: 0;\n}\n\n.lh-max-viewport {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  width: 100%;\n}\n\n.lh-devtools.lh-root {\n  height: 100%;\n}\n.lh-devtools.lh-root img {\n  /* Override devtools default \'min-width: 0\' so svg without size in a flexbox isn\'t collapsed. */\n  min-width: auto;\n}\n.lh-devtools .lh-container {\n  overflow-y: scroll;\n  height: calc(100% - var(--topbar-height));\n  /** The .lh-container is the scroll parent in DevTools so we exclude the topbar from the sticky header buffer. */\n  --sticky-header-buffer: 0;\n}\n.lh-devtools .lh-container:has(.lh-sticky-header) {\n  /** The .lh-container is the scroll parent in DevTools so we exclude the topbar from the sticky header buffer. */\n  --sticky-header-buffer: var(--sticky-header-height);\n}\n@media print {\n  .lh-devtools .lh-container {\n    overflow: unset;\n  }\n}\n.lh-devtools .lh-sticky-header {\n  /* This is normally the height of the topbar, but we want it to stick to the top of our scroll container .lh-container` */\n  top: 0;\n}\n.lh-devtools .lh-element-screenshot__overlay {\n  position: absolute;\n}\n\n@keyframes fadeIn {\n  0% { opacity: 0;}\n  100% { opacity: 0.6;}\n}\n\n.lh-root *, .lh-root *::before, .lh-root *::after {\n  box-sizing: border-box;\n}\n\n.lh-root {\n  font-family: var(--report-font-family);\n  font-size: var(--report-font-size);\n  margin: 0;\n  line-height: var(--report-line-height);\n  background: var(--report-background-color);\n  color: var(--report-text-color);\n}\n\n.lh-root :focus-visible {\n    outline: -webkit-focus-ring-color auto 3px;\n}\n.lh-root summary:focus {\n    outline: none;\n    box-shadow: 0 0 0 1px hsl(217, 89%, 61%);\n}\n\n.lh-root [hidden] {\n  display: none !important;\n}\n\n.lh-root pre {\n  margin: 0;\n}\n\n.lh-root pre,\n.lh-root code {\n  font-family: var(--report-font-family-monospace);\n}\n\n.lh-root details > summary {\n  cursor: pointer;\n}\n\n.lh-hidden {\n  display: none !important;\n}\n\n.lh-container {\n  /*\n  Text wrapping in the report is so much FUN!\n  We have a `word-break: break-word;` globally here to prevent a few common scenarios, namely\n  long non-breakable text (usually URLs) found in:\n    1. The footer\n    2. .lh-node (outerHTML)\n    3. .lh-code\n\n  With that sorted, the next challenge is appropriate column sizing and text wrapping inside our\n  .lh-details tables. Even more fun.\n    * We don\'t want table headers ("Potential Savings (ms)") to wrap or their column values, but\n    we\'d be happy for the URL column to wrap if the URLs are particularly long.\n    * We want the narrow columns to remain narrow, providing the most column width for URL\n    * We don\'t want the table to extend past 100% width.\n    * Long URLs in the URL column can wrap. Util.getURLDisplayName maxes them out at 64 characters,\n      but they do not get any overflow:ellipsis treatment.\n  */\n  word-break: break-word;\n}\n\n.lh-audit-group a,\n.lh-category-header__description a,\n.lh-audit__description a,\n.lh-warnings a,\n.lh-footer a,\n.lh-table-column--link a {\n  color: var(--link-color);\n}\n\n.lh-audit__description, .lh-audit__stackpack {\n  --inner-audit-padding-right: var(--stackpack-padding-horizontal);\n  padding-left: var(--audit-description-padding-left);\n  padding-right: var(--inner-audit-padding-right);\n  padding-top: 8px;\n  padding-bottom: 8px;\n}\n\n.lh-details {\n  margin-top: var(--default-padding);\n  margin-bottom: var(--default-padding);\n  margin-left: var(--audit-description-padding-left);\n  /* whatever the .lh-details side margins are */\n  width: 100%;\n}\n\n.lh-audit__stackpack {\n  display: flex;\n  align-items: center;\n}\n\n.lh-audit__stackpack__img {\n  max-width: 30px;\n  margin-right: var(--default-padding)\n}\n\n/* Report header */\n\n.lh-report-icon {\n  display: flex;\n  align-items: center;\n  padding: 10px 12px;\n  cursor: pointer;\n}\n.lh-report-icon[disabled] {\n  opacity: 0.3;\n  pointer-events: none;\n}\n\n.lh-report-icon::before {\n  content: "";\n  margin: 4px;\n  background-repeat: no-repeat;\n  width: var(--report-icon-size);\n  height: var(--report-icon-size);\n  opacity: 0.7;\n  display: inline-block;\n  vertical-align: middle;\n}\n.lh-report-icon:hover::before {\n  opacity: 1;\n}\n.lh-dark .lh-report-icon::before {\n  filter: invert(1);\n}\n.lh-report-icon--print::before {\n  background-image: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M19 8H5c-1.66 0-3 1.34-3 3v6h4v4h12v-4h4v-6c0-1.66-1.34-3-3-3zm-3 11H8v-5h8v5zm3-7c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm-1-9H6v4h12V3z"/><path fill="none" d="M0 0h24v24H0z"/></svg>\');\n}\n.lh-report-icon--copy::before {\n  background-image: url(\'data:image/svg+xml;utf8,<svg height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h24v24H0z" fill="none"/><path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/></svg>\');\n}\n.lh-report-icon--open::before {\n  background-image: url(\'data:image/svg+xml;utf8,<svg height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h24v24H0z" fill="none"/><path d="M19 4H5c-1.11 0-2 .9-2 2v12c0 1.1.89 2 2 2h4v-2H5V8h14v10h-4v2h4c1.1 0 2-.9 2-2V6c0-1.1-.89-2-2-2zm-7 6l-4 4h3v6h2v-6h3l-4-4z"/></svg>\');\n}\n.lh-report-icon--download::before {\n  background-image: url(\'data:image/svg+xml;utf8,<svg height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/><path d="M0 0h24v24H0z" fill="none"/></svg>\');\n}\n.lh-report-icon--dark::before {\n  background-image:url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 100 125"><path d="M50 23.587c-16.27 0-22.799 12.574-22.799 21.417 0 12.917 10.117 22.451 12.436 32.471h20.726c2.32-10.02 12.436-19.554 12.436-32.471 0-8.843-6.528-21.417-22.799-21.417zM39.637 87.161c0 3.001 1.18 4.181 4.181 4.181h.426l.41 1.231C45.278 94.449 46.042 95 48.019 95h3.963c1.978 0 2.74-.551 3.365-2.427l.409-1.231h.427c3.002 0 4.18-1.18 4.18-4.181V80.91H39.637v6.251zM50 18.265c1.26 0 2.072-.814 2.072-2.073v-9.12C52.072 5.813 51.26 5 50 5c-1.259 0-2.072.813-2.072 2.073v9.12c0 1.259.813 2.072 2.072 2.072zM68.313 23.727c.994.774 2.135.634 2.91-.357l5.614-7.187c.776-.992.636-2.135-.356-2.909-.992-.776-2.135-.636-2.91.357l-5.613 7.186c-.778.993-.636 2.135.355 2.91zM91.157 36.373c-.306-1.222-1.291-1.815-2.513-1.51l-8.85 2.207c-1.222.305-1.814 1.29-1.51 2.512.305 1.223 1.291 1.814 2.513 1.51l8.849-2.206c1.223-.305 1.816-1.291 1.511-2.513zM86.757 60.48l-8.331-3.709c-1.15-.512-2.225-.099-2.736 1.052-.512 1.151-.1 2.224 1.051 2.737l8.33 3.707c1.15.514 2.225.101 2.736-1.05.513-1.149.1-2.223-1.05-2.737zM28.779 23.37c.775.992 1.917 1.131 2.909.357.992-.776 1.132-1.917.357-2.91l-5.615-7.186c-.775-.992-1.917-1.132-2.909-.357s-1.131 1.917-.356 2.909l5.614 7.187zM21.715 39.583c.305-1.223-.288-2.208-1.51-2.513l-8.849-2.207c-1.222-.303-2.208.289-2.513 1.511-.303 1.222.288 2.207 1.511 2.512l8.848 2.206c1.222.304 2.208-.287 2.513-1.509zM21.575 56.771l-8.331 3.711c-1.151.511-1.563 1.586-1.05 2.735.511 1.151 1.586 1.563 2.736 1.052l8.331-3.711c1.151-.511 1.563-1.586 1.05-2.735-.512-1.15-1.585-1.562-2.736-1.052z"/></svg>\');\n}\n.lh-report-icon--treemap::before {\n  background-image: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="black"><path d="M3 5v14h19V5H3zm2 2h15v4H5V7zm0 10v-4h4v4H5zm6 0v-4h9v4h-9z"/></svg>\');\n}\n.lh-report-icon--date::before {\n  background-image: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M7 11h2v2H7v-2zm14-5v14a2 2 0 01-2 2H5a2 2 0 01-2-2V6c0-1.1.9-2 2-2h1V2h2v2h8V2h2v2h1a2 2 0 012 2zM5 8h14V6H5v2zm14 12V10H5v10h14zm-4-7h2v-2h-2v2zm-4 0h2v-2h-2v2z"/></svg>\');\n}\n.lh-report-icon--devices::before {\n  background-image: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M4 6h18V4H4a2 2 0 00-2 2v11H0v3h14v-3H4V6zm19 2h-6a1 1 0 00-1 1v10c0 .6.5 1 1 1h6c.6 0 1-.5 1-1V9c0-.6-.5-1-1-1zm-1 9h-4v-7h4v7z"/></svg>\');\n}\n.lh-report-icon--world::before {\n  background-image: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20zm7 6h-3c-.3-1.3-.8-2.5-1.4-3.6A8 8 0 0 1 18.9 8zm-7-4a14 14 0 0 1 2 4h-4a14 14 0 0 1 2-4zM4.3 14a8.2 8.2 0 0 1 0-4h3.3a16.5 16.5 0 0 0 0 4H4.3zm.8 2h3a14 14 0 0 0 1.3 3.6A8 8 0 0 1 5.1 16zm3-8H5a8 8 0 0 1 4.3-3.6L8 8zM12 20a14 14 0 0 1-2-4h4a14 14 0 0 1-2 4zm2.3-6H9.7a14.7 14.7 0 0 1 0-4h4.6a14.6 14.6 0 0 1 0 4zm.3 5.6c.6-1.2 1-2.4 1.4-3.6h3a8 8 0 0 1-4.4 3.6zm1.8-5.6a16.5 16.5 0 0 0 0-4h3.3a8.2 8.2 0 0 1 0 4h-3.3z"/></svg>\');\n}\n.lh-report-icon--stopwatch::before {\n  background-image: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M15 1H9v2h6V1zm-4 13h2V8h-2v6zm8.1-6.6L20.5 6l-1.4-1.4L17.7 6A9 9 0 0 0 3 13a9 9 0 1 0 16-5.6zm-7 12.6a7 7 0 1 1 0-14 7 7 0 0 1 0 14z"/></svg>\');\n}\n.lh-report-icon--networkspeed::before {\n  background-image: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M15.9 5c-.2 0-.3 0-.4.2v.2L10.1 17a2 2 0 0 0-.2 1 2 2 0 0 0 4 .4l2.4-12.9c0-.3-.2-.5-.5-.5zM1 9l2 2c2.9-2.9 6.8-4 10.5-3.6l1.2-2.7C10 3.8 4.7 5.3 1 9zm20 2 2-2a15.4 15.4 0 0 0-5.6-3.6L17 8.2c1.5.7 2.9 1.6 4.1 2.8zm-4 4 2-2a9.9 9.9 0 0 0-2.7-1.9l-.5 3 1.2.9zM5 13l2 2a7.1 7.1 0 0 1 4-2l1.3-2.9C9.7 10.1 7 11 5 13z"/></svg>\');\n}\n.lh-report-icon--samples-one::before {\n  background-image: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><circle cx="7" cy="14" r="3"/><path d="M7 18a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm4-2a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm5.6 17.6a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/></svg>\');\n}\n.lh-report-icon--samples-many::before {\n  background-image: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M7 18a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm4-2a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm5.6 17.6a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/><circle cx="7" cy="14" r="3"/><circle cx="11" cy="6" r="3"/></svg>\');\n}\n.lh-report-icon--chrome::before {\n  background-image: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="-50 -50 562 562"><path d="M256 25.6v25.6a204 204 0 0 1 144.8 60 204 204 0 0 1 60 144.8 204 204 0 0 1-60 144.8 204 204 0 0 1-144.8 60 204 204 0 0 1-144.8-60 204 204 0 0 1-60-144.8 204 204 0 0 1 60-144.8 204 204 0 0 1 144.8-60V0a256 256 0 1 0 0 512 256 256 0 0 0 0-512v25.6z"/><path d="M256 179.2v25.6a51.3 51.3 0 0 1 0 102.4 51.3 51.3 0 0 1 0-102.4v-51.2a102.3 102.3 0 1 0-.1 204.7 102.3 102.3 0 0 0 .1-204.7v25.6z"/><path d="M256 204.8h217.6a25.6 25.6 0 0 0 0-51.2H256a25.6 25.6 0 0 0 0 51.2m44.3 76.8L191.5 470.1a25.6 25.6 0 1 0 44.4 25.6l108.8-188.5a25.6 25.6 0 1 0-44.4-25.6m-88.6 0L102.9 93.2a25.7 25.7 0 0 0-35-9.4 25.7 25.7 0 0 0-9.4 35l108.8 188.5a25.7 25.7 0 0 0 35 9.4 25.9 25.9 0 0 0 9.4-35.1"/></svg>\');\n}\n.lh-report-icon--external::before {\n  background-image: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg"><path d="M3.15 11.9a1.01 1.01 0 0 1-.743-.307 1.01 1.01 0 0 1-.306-.743v-7.7c0-.292.102-.54.306-.744a1.01 1.01 0 0 1 .744-.306H7v1.05H3.15v7.7h7.7V7h1.05v3.85c0 .291-.103.54-.307.743a1.01 1.01 0 0 1-.743.307h-7.7Zm2.494-2.8-.743-.744 5.206-5.206H8.401V2.1h3.5v3.5h-1.05V3.893L5.644 9.1Z"/></svg>\');\n}\n\n.lh-buttons {\n  display: flex;\n  flex-wrap: wrap;\n  margin: var(--default-padding) 0;\n}\n.lh-button {\n  height: 32px;\n  border: 1px solid var(--report-border-color-secondary);\n  border-radius: 3px;\n  color: var(--link-color);\n  background-color: var(--report-background-color);\n  margin: 5px;\n}\n\n.lh-button:first-of-type {\n  margin-left: 0;\n}\n\n/* Node */\n.lh-node__snippet {\n  font-family: var(--report-font-family-monospace);\n  color: var(--snippet-color);\n  font-size: var(--report-monospace-font-size);\n  line-height: 20px;\n}\n\n/* Score */\n\n.lh-audit__score-icon {\n  width: var(--score-icon-size);\n  height: var(--score-icon-size);\n  margin: var(--score-icon-margin);\n}\n\n.lh-audit--pass .lh-audit__display-text {\n  color: var(--color-pass-secondary);\n}\n.lh-audit--pass .lh-audit__score-icon,\n.lh-scorescale-range--pass::before {\n  border-radius: 100%;\n  background: var(--color-pass);\n}\n\n.lh-audit--average .lh-audit__display-text {\n  color: var(--color-average-secondary);\n}\n.lh-audit--average .lh-audit__score-icon,\n.lh-scorescale-range--average::before {\n  background: var(--color-average);\n  width: var(--icon-square-size);\n  height: var(--icon-square-size);\n}\n\n.lh-audit--fail .lh-audit__display-text {\n  color: var(--color-fail-secondary);\n}\n.lh-audit--fail .lh-audit__score-icon,\n.lh-audit--error .lh-audit__score-icon,\n.lh-scorescale-range--fail::before {\n  border-left: calc(var(--score-icon-size) / 2) solid transparent;\n  border-right: calc(var(--score-icon-size) / 2) solid transparent;\n  border-bottom: var(--score-icon-size) solid var(--color-fail);\n}\n\n.lh-audit--error .lh-audit__score-icon,\n.lh-metric--error .lh-metric__icon {\n  background-image: var(--error-icon-url);\n  background-repeat: no-repeat;\n  background-position: center;\n  border: none;\n}\n\n.lh-gauge__wrapper--fail .lh-gauge--error {\n  background-image: var(--error-icon-url);\n  background-repeat: no-repeat;\n  background-position: center;\n  transform: scale(0.5);\n  top: var(--score-container-padding);\n}\n\n.lh-audit--manual .lh-audit__display-text,\n.lh-audit--notapplicable .lh-audit__display-text {\n  color: var(--color-gray-600);\n}\n.lh-audit--manual .lh-audit__score-icon,\n.lh-audit--notapplicable .lh-audit__score-icon {\n  border: calc(0.2 * var(--score-icon-size)) solid var(--color-gray-400);\n  border-radius: 100%;\n  background: none;\n}\n\n.lh-audit--informative .lh-audit__display-text {\n  color: var(--color-gray-600);\n}\n\n.lh-audit--informative .lh-audit__score-icon {\n  border: calc(0.2 * var(--score-icon-size)) solid var(--color-gray-400);\n  border-radius: 100%;\n}\n\n.lh-audit__description,\n.lh-audit__stackpack {\n  color: var(--report-text-color-secondary);\n}\n.lh-audit__adorn {\n  border: 1px solid var(--color-gray-500);\n  border-radius: 3px;\n  margin: 0 3px;\n  padding: 0 2px;\n  line-height: 1.1;\n  display: inline-block;\n  font-size: 90%;\n  color: var(--report-text-color-secondary);\n}\n\n.lh-category-header__description  {\n  text-align: center;\n  color: var(--color-gray-700);\n  margin: 0px auto;\n  max-width: 400px;\n}\n\n\n.lh-audit__display-text,\n.lh-chevron-container {\n  margin: 0 var(--audit-margin-horizontal);\n}\n.lh-chevron-container {\n  margin-right: 0;\n}\n\n.lh-audit__title-and-text {\n  flex: 1;\n}\n\n.lh-audit__title-and-text code {\n  color: var(--snippet-color);\n  font-size: var(--report-monospace-font-size);\n}\n\n/* Prepend display text with em dash separator. */\n.lh-audit__display-text:not(:empty):before {\n  content: \'—\';\n  margin-right: var(--audit-margin-horizontal);\n}\n\n/* Expandable Details (Audit Groups, Audits) */\n.lh-audit__header {\n  display: flex;\n  align-items: center;\n  padding: var(--default-padding);\n}\n\n\n.lh-metricfilter {\n  display: grid;\n  justify-content: end;\n  align-items: center;\n  grid-auto-flow: column;\n  gap: 4px;\n  color: var(--color-gray-700);\n}\n\n.lh-metricfilter__radio {\n  /*\n   * Instead of hiding, position offscreen so it\'s still accessible to screen readers\n   * https://bugs.chromium.org/p/chromium/issues/detail?id=1439785\n   */\n  position: fixed;\n  left: -9999px;\n}\n.lh-metricfilter input[type=\'radio\']:focus-visible + label {\n  outline: -webkit-focus-ring-color auto 1px;\n}\n\n.lh-metricfilter__label {\n  display: inline-flex;\n  padding: 0 4px;\n  height: 16px;\n  text-decoration: underline;\n  align-items: center;\n  cursor: pointer;\n  font-size: 90%;\n}\n\n.lh-metricfilter__label--active {\n  background: var(--color-blue-primary);\n  color: var(--color-white);\n  border-radius: 3px;\n  text-decoration: none;\n}\n/* Give the \'All\' choice a more muted display */\n.lh-metricfilter__label--active[for="metric-All"] {\n  background-color: var(--color-blue-200) !important;\n  color: black !important;\n}\n\n.lh-metricfilter__text {\n  margin-right: 8px;\n}\n\n/* If audits are filtered, hide the itemcount for Passed Audits… */\n.lh-category--filtered .lh-audit-group .lh-audit-group__itemcount {\n  display: none;\n}\n\n\n.lh-audit__header:hover {\n  background-color: var(--color-hover);\n}\n\n/* We want to hide the browser\'s default arrow marker on summary elements. Admittedly, it\'s complicated. */\n.lh-root details > summary {\n  /* Blink 89+ and Firefox will hide the arrow when display is changed from (new) default of `list-item` to block.  https://chromestatus.com/feature/6730096436051968*/\n  display: block;\n}\n/* Safari and Blink <=88 require using the -webkit-details-marker selector */\n.lh-root details > summary::-webkit-details-marker {\n  display: none;\n}\n\n/* Perf Metric */\n\n.lh-metrics-container {\n  display: grid;\n  grid-auto-rows: 1fr;\n  grid-template-columns: 1fr 1fr;\n  grid-column-gap: var(--report-line-height);\n  margin-bottom: var(--default-padding);\n}\n\n.lh-metric {\n  border-top: 1px solid var(--report-border-color-secondary);\n}\n\n.lh-category:not(.lh--hoisted-meta) .lh-metric:nth-last-child(-n+2) {\n  border-bottom: 1px solid var(--report-border-color-secondary);\n}\n\n.lh-metric__innerwrap {\n  display: grid;\n  /**\n   * Icon -- Metric Name\n   *      -- Metric Value\n   */\n  grid-template-columns: calc(var(--score-icon-size) + var(--score-icon-margin-left) + var(--score-icon-margin-right)) 1fr;\n  align-items: center;\n  padding: var(--default-padding);\n}\n\n.lh-metric__details {\n  order: -1;\n}\n\n.lh-metric__title {\n  flex: 1;\n}\n\n.lh-calclink {\n  padding-left: calc(1ex / 3);\n}\n\n.lh-metric__description {\n  display: none;\n  grid-column-start: 2;\n  grid-column-end: 4;\n  color: var(--report-text-color-secondary);\n}\n\n.lh-metric__value {\n  font-size: var(--metric-value-font-size);\n  margin: calc(var(--default-padding) / 2) 0;\n  white-space: nowrap; /* No wrapping between metric value and the icon */\n  grid-column-start: 2;\n}\n\n\n@media screen and (max-width: 535px) {\n  .lh-metrics-container {\n    display: block;\n  }\n\n  .lh-metric {\n    border-bottom: none !important;\n  }\n  .lh-category:not(.lh--hoisted-meta) .lh-metric:nth-last-child(1) {\n    border-bottom: 1px solid var(--report-border-color-secondary) !important;\n  }\n\n  /* Change the grid to 3 columns for narrow viewport. */\n  .lh-metric__innerwrap {\n  /**\n   * Icon -- Metric Name -- Metric Value\n   */\n    grid-template-columns: calc(var(--score-icon-size) + var(--score-icon-margin-left) + var(--score-icon-margin-right)) 2fr 1fr;\n  }\n  .lh-metric__value {\n    justify-self: end;\n    grid-column-start: unset;\n  }\n}\n\n/* No-JS toggle switch */\n/* Keep this selector sync\'d w/ `magicSelector` in report-ui-features-test.js */\n .lh-metrics-toggle__input:checked ~ .lh-metrics-container .lh-metric__description {\n  display: block;\n}\n\n/* TODO get rid of the SVGS and clean up these some more */\n.lh-metrics-toggle__input {\n  opacity: 0;\n  position: absolute;\n  right: 0;\n  top: 0px;\n}\n\n.lh-metrics-toggle__input + div > label > .lh-metrics-toggle__labeltext--hide,\n.lh-metrics-toggle__input:checked + div > label > .lh-metrics-toggle__labeltext--show {\n  display: none;\n}\n.lh-metrics-toggle__input:checked + div > label > .lh-metrics-toggle__labeltext--hide {\n  display: inline;\n}\n.lh-metrics-toggle__input:focus + div > label {\n  outline: -webkit-focus-ring-color auto 3px;\n}\n\n.lh-metrics-toggle__label {\n  cursor: pointer;\n  font-size: var(--report-font-size-secondary);\n  line-height: var(--report-line-height-secondary);\n  color: var(--color-gray-700);\n}\n\n/* Pushes the metric description toggle button to the right. */\n.lh-audit-group--metrics .lh-audit-group__header {\n  display: flex;\n  justify-content: space-between;\n}\n\n.lh-metric__icon,\n.lh-scorescale-range::before {\n  content: \'\';\n  width: var(--score-icon-size);\n  height: var(--score-icon-size);\n  display: inline-block;\n  margin: var(--score-icon-margin);\n}\n\n.lh-metric--pass .lh-metric__value {\n  color: var(--color-pass-secondary);\n}\n.lh-metric--pass .lh-metric__icon {\n  border-radius: 100%;\n  background: var(--color-pass);\n}\n\n.lh-metric--average .lh-metric__value {\n  color: var(--color-average-secondary);\n}\n.lh-metric--average .lh-metric__icon {\n  background: var(--color-average);\n  width: var(--icon-square-size);\n  height: var(--icon-square-size);\n}\n\n.lh-metric--fail .lh-metric__value {\n  color: var(--color-fail-secondary);\n}\n.lh-metric--fail .lh-metric__icon {\n  border-left: calc(var(--score-icon-size) / 2) solid transparent;\n  border-right: calc(var(--score-icon-size) / 2) solid transparent;\n  border-bottom: var(--score-icon-size) solid var(--color-fail);\n}\n\n.lh-metric--error .lh-metric__value,\n.lh-metric--error .lh-metric__description {\n  color: var(--color-fail-secondary);\n}\n\n/* Filmstrip */\n\n.lh-filmstrip-container {\n  /* smaller gap between metrics and filmstrip */\n  margin: -8px auto 0 auto;\n}\n\n.lh-filmstrip {\n  display: flex;\n  justify-content: space-between;\n  justify-items: center;\n  margin-bottom: var(--default-padding);\n  width: 100%;\n}\n\n.lh-filmstrip__frame {\n  overflow: hidden;\n  line-height: 0;\n}\n\n.lh-filmstrip__thumbnail {\n  border: 1px solid var(--report-border-color-secondary);\n  max-height: 150px;\n  max-width: 120px;\n}\n\n/* Audit */\n\n.lh-audit {\n  border-bottom: 1px solid var(--report-border-color-secondary);\n}\n\n/* Apply border-top to just the first audit. */\n.lh-audit {\n  border-top: 1px solid var(--report-border-color-secondary);\n}\n.lh-audit ~ .lh-audit {\n  border-top: none;\n}\n\n\n.lh-audit--error .lh-audit__display-text {\n  color: var(--color-fail-secondary);\n}\n\n/* Audit Group */\n\n.lh-audit-group {\n  margin-bottom: var(--audit-group-margin-bottom);\n  position: relative;\n}\n.lh-audit-group--metrics {\n  margin-bottom: calc(var(--audit-group-margin-bottom) / 2);\n}\n\n.lh-audit-group--metrics .lh-audit-group__summary {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n\n.lh-audit-group__summary {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.lh-audit-group__header .lh-chevron {\n  margin-top: calc((var(--report-line-height) - 5px) / 2);\n}\n\n.lh-audit-group__header {\n  letter-spacing: 0.8px;\n  padding: var(--default-padding);\n  padding-left: 0;\n}\n\n.lh-audit-group__header, .lh-audit-group__summary {\n  font-size: var(--report-font-size-secondary);\n  line-height: var(--report-line-height-secondary);\n  color: var(--color-gray-700);\n}\n\n.lh-audit-group__title {\n  text-transform: uppercase;\n  font-weight: 500;\n}\n\n.lh-audit-group__itemcount {\n  color: var(--color-gray-600);\n}\n\n.lh-audit-group__footer {\n  color: var(--color-gray-600);\n  display: block;\n  margin-top: var(--default-padding);\n}\n\n.lh-details,\n.lh-category-header__description,\n.lh-audit-group__footer {\n  font-size: var(--report-font-size-secondary);\n  line-height: var(--report-line-height-secondary);\n}\n\n.lh-audit-explanation {\n  margin: var(--audit-padding-vertical) 0 calc(var(--audit-padding-vertical) / 2) var(--audit-margin-horizontal);\n  line-height: var(--audit-explanation-line-height);\n  display: inline-block;\n}\n\n.lh-audit--fail .lh-audit-explanation {\n  color: var(--color-fail-secondary);\n}\n\n/* Report */\n.lh-list > :not(:last-child) {\n  margin-bottom: calc(var(--default-padding) * 2);\n}\n\n.lh-header-container {\n  display: block;\n  margin: 0 auto;\n  position: relative;\n  word-wrap: break-word;\n}\n\n.lh-header-container .lh-scores-wrapper {\n  border-bottom: 1px solid var(--color-gray-200);\n}\n\n\n.lh-report {\n  min-width: var(--report-content-min-width);\n}\n\n.lh-exception {\n  font-size: large;\n}\n\n.lh-code {\n  white-space: normal;\n  margin-top: 0;\n  font-size: var(--report-monospace-font-size);\n}\n\n.lh-warnings {\n  --item-margin: calc(var(--report-line-height) / 6);\n  color: var(--color-average-secondary);\n  margin: var(--audit-padding-vertical) 0;\n  padding: var(--default-padding)\n    var(--default-padding)\n    var(--default-padding)\n    calc(var(--audit-description-padding-left));\n  background-color: var(--toplevel-warning-background-color);\n}\n.lh-warnings span {\n  font-weight: bold;\n}\n\n.lh-warnings--toplevel {\n  --item-margin: calc(var(--header-line-height) / 4);\n  color: var(--toplevel-warning-text-color);\n  margin-left: auto;\n  margin-right: auto;\n  max-width: var(--report-content-max-width-minus-edge-gap);\n  padding: var(--toplevel-warning-padding);\n  border-radius: 8px;\n}\n\n.lh-warnings__msg {\n  color: var(--toplevel-warning-message-text-color);\n  margin: 0;\n}\n\n.lh-warnings ul {\n  margin: 0;\n}\n.lh-warnings li {\n  margin: var(--item-margin) 0;\n}\n.lh-warnings li:last-of-type {\n  margin-bottom: 0;\n}\n\n.lh-scores-header {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: center;\n}\n.lh-scores-header__solo {\n  padding: 0;\n  border: 0;\n}\n\n/* Gauge */\n\n.lh-gauge__wrapper--pass {\n  color: var(--color-pass-secondary);\n  fill: var(--color-pass);\n  stroke: var(--color-pass);\n}\n\n.lh-gauge__wrapper--average {\n  color: var(--color-average-secondary);\n  fill: var(--color-average);\n  stroke: var(--color-average);\n}\n\n.lh-gauge__wrapper--fail {\n  color: var(--color-fail-secondary);\n  fill: var(--color-fail);\n  stroke: var(--color-fail);\n}\n\n.lh-gauge__wrapper--not-applicable {\n  color: var(--color-not-applicable);\n  fill: var(--color-not-applicable);\n  stroke: var(--color-not-applicable);\n}\n\n.lh-fraction__wrapper .lh-fraction__content::before {\n  content: \'\';\n  height: var(--score-icon-size);\n  width: var(--score-icon-size);\n  margin: var(--score-icon-margin);\n  display: inline-block;\n}\n.lh-fraction__wrapper--pass .lh-fraction__content {\n  color: var(--color-pass-secondary);\n}\n.lh-fraction__wrapper--pass .lh-fraction__background {\n  background-color: var(--color-pass);\n}\n.lh-fraction__wrapper--pass .lh-fraction__content::before {\n  background-color: var(--color-pass);\n  border-radius: 50%;\n}\n.lh-fraction__wrapper--average .lh-fraction__content {\n  color: var(--color-average-secondary);\n}\n.lh-fraction__wrapper--average .lh-fraction__background,\n.lh-fraction__wrapper--average .lh-fraction__content::before {\n  background-color: var(--color-average);\n}\n.lh-fraction__wrapper--fail .lh-fraction__content {\n  color: var(--color-fail);\n}\n.lh-fraction__wrapper--fail .lh-fraction__background {\n  background-color: var(--color-fail);\n}\n.lh-fraction__wrapper--fail .lh-fraction__content::before {\n  border-left: calc(var(--score-icon-size) / 2) solid transparent;\n  border-right: calc(var(--score-icon-size) / 2) solid transparent;\n  border-bottom: var(--score-icon-size) solid var(--color-fail);\n}\n.lh-fraction__wrapper--null .lh-fraction__content {\n  color: var(--color-gray-700);\n}\n.lh-fraction__wrapper--null .lh-fraction__background {\n  background-color: var(--color-gray-700);\n}\n.lh-fraction__wrapper--null .lh-fraction__content::before {\n  border-radius: 50%;\n  border: calc(0.2 * var(--score-icon-size)) solid var(--color-gray-700);\n}\n\n.lh-fraction__background {\n  position: absolute;\n  height: 100%;\n  width: 100%;\n  border-radius: calc(var(--gauge-circle-size) / 2);\n  opacity: 0.1;\n  z-index: -1;\n}\n\n.lh-fraction__content-wrapper {\n  height: var(--gauge-circle-size);\n  display: flex;\n  align-items: center;\n}\n\n.lh-fraction__content {\n  display: flex;\n  position: relative;\n  align-items: center;\n  justify-content: center;\n  font-size: calc(0.3 * var(--gauge-circle-size));\n  line-height: calc(0.4 * var(--gauge-circle-size));\n  width: max-content;\n  min-width: calc(1.5 * var(--gauge-circle-size));\n  padding: calc(0.1 * var(--gauge-circle-size)) calc(0.2 * var(--gauge-circle-size));\n  --score-icon-size: calc(0.21 * var(--gauge-circle-size));\n  --score-icon-margin: 0 calc(0.15 * var(--gauge-circle-size)) 0 0;\n}\n\n.lh-gauge {\n  stroke-linecap: round;\n  width: var(--gauge-circle-size);\n  height: var(--gauge-circle-size);\n}\n\n.lh-category .lh-gauge {\n  --gauge-circle-size: var(--gauge-circle-size-big);\n}\n\n.lh-gauge-base {\n  opacity: 0.1;\n}\n\n.lh-gauge-arc {\n  fill: none;\n  transform-origin: 50% 50%;\n  animation: load-gauge var(--transition-length) ease both;\n  animation-delay: 250ms;\n}\n\n.lh-gauge__svg-wrapper {\n  position: relative;\n  height: var(--gauge-circle-size);\n}\n.lh-category .lh-gauge__svg-wrapper,\n.lh-category .lh-fraction__wrapper {\n  --gauge-circle-size: var(--gauge-circle-size-big);\n}\n\n/* The plugin badge overlay */\n.lh-gauge__wrapper--plugin .lh-gauge__svg-wrapper::before {\n  width: var(--plugin-badge-size);\n  height: var(--plugin-badge-size);\n  background-color: var(--plugin-badge-background-color);\n  background-image: var(--plugin-icon-url);\n  background-repeat: no-repeat;\n  background-size: var(--plugin-icon-size);\n  background-position: 58% 50%;\n  content: "";\n  position: absolute;\n  right: -6px;\n  bottom: 0px;\n  display: block;\n  z-index: 100;\n  box-shadow: 0 0 4px rgba(0,0,0,.2);\n  border-radius: 25%;\n}\n.lh-category .lh-gauge__wrapper--plugin .lh-gauge__svg-wrapper::before {\n  width: var(--plugin-badge-size-big);\n  height: var(--plugin-badge-size-big);\n}\n\n@keyframes load-gauge {\n  from { stroke-dasharray: 0 352; }\n}\n\n.lh-gauge__percentage {\n  width: 100%;\n  height: var(--gauge-circle-size);\n  line-height: var(--gauge-circle-size);\n  position: absolute;\n  font-family: var(--report-font-family-monospace);\n  font-size: calc(var(--gauge-circle-size) * 0.34 + 1.3px);\n  text-align: center;\n  top: var(--score-container-padding);\n}\n\n.lh-category .lh-gauge__percentage {\n  --gauge-circle-size: var(--gauge-circle-size-big);\n  --gauge-percentage-font-size: var(--gauge-percentage-font-size-big);\n}\n\n.lh-gauge__wrapper,\n.lh-fraction__wrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n  flex-direction: column;\n  text-decoration: none;\n  padding: var(--score-container-padding);\n\n  --transition-length: 1s;\n\n  /* Contain the layout style paint & layers during animation*/\n  contain: content;\n  will-change: opacity; /* Only using for layer promotion */\n}\n\n.lh-gauge__label,\n.lh-fraction__label {\n  font-size: var(--gauge-label-font-size);\n  font-weight: 500;\n  line-height: var(--gauge-label-line-height);\n  margin-top: 10px;\n  text-align: center;\n  color: var(--report-text-color);\n  word-break: keep-all;\n}\n\n/* TODO(#8185) use more BEM (.lh-gauge__label--big) instead of relying on descendant selector */\n.lh-category .lh-gauge__label,\n.lh-category .lh-fraction__label {\n  --gauge-label-font-size: var(--gauge-label-font-size-big);\n  --gauge-label-line-height: var(--gauge-label-line-height-big);\n  margin-top: 14px;\n}\n\n.lh-scores-header .lh-gauge__wrapper,\n.lh-scores-header .lh-fraction__wrapper,\n.lh-sticky-header .lh-gauge__wrapper,\n.lh-sticky-header .lh-fraction__wrapper {\n  width: var(--gauge-wrapper-width);\n}\n\n.lh-scorescale {\n  display: inline-flex;\n\n  gap: calc(var(--default-padding) * 4);\n  margin: 16px auto 0 auto;\n  font-size: var(--report-font-size-secondary);\n  color: var(--color-gray-700);\n\n}\n\n.lh-scorescale-range {\n  display: flex;\n  align-items: center;\n  font-family: var(--report-font-family-monospace);\n  white-space: nowrap;\n}\n\n.lh-category-header__finalscreenshot .lh-scorescale {\n  border: 0;\n  display: flex;\n  justify-content: center;\n}\n\n.lh-category-header__finalscreenshot .lh-scorescale-range {\n  font-family: unset;\n  font-size: 12px;\n}\n\n.lh-scorescale-wrap {\n  display: contents;\n}\n\n/* Hide category score gauages if it\'s a single category report */\n.lh-header--solo-category .lh-scores-wrapper {\n  display: none;\n}\n\n\n.lh-categories {\n  width: 100%;\n}\n\n.lh-category {\n  padding: var(--category-padding);\n  max-width: var(--report-content-max-width);\n  margin: 0 auto;\n\n  scroll-margin-top: calc(var(--sticky-header-buffer) - 1em);\n}\n\n.lh-category-wrapper {\n  border-bottom: 1px solid var(--color-gray-200);\n}\n.lh-category-wrapper:last-of-type {\n  border-bottom: 0;\n}\n\n.lh-category-header {\n  margin-bottom: var(--section-padding-vertical);\n}\n\n.lh-category-header .lh-score__gauge {\n  max-width: 400px;\n  width: auto;\n  margin: 0px auto;\n}\n\n.lh-category-header__finalscreenshot {\n  display: grid;\n  grid-template: none / 1fr 1px 1fr;\n  justify-items: center;\n  align-items: center;\n  gap: var(--report-line-height);\n  min-height: 288px;\n  margin-bottom: var(--default-padding);\n}\n\n.lh-final-ss-image {\n  /* constrain the size of the image to not be too large */\n  max-height: calc(var(--gauge-circle-size-big) * 2.8);\n  max-width: calc(var(--gauge-circle-size-big) * 3.5);\n  border: 1px solid var(--color-gray-200);\n  padding: 4px;\n  border-radius: 3px;\n  display: block;\n}\n\n.lh-category-headercol--separator {\n  background: var(--color-gray-200);\n  width: 1px;\n  height: var(--gauge-circle-size-big);\n}\n\n@media screen and (max-width: 780px) {\n  .lh-category-header__finalscreenshot {\n    grid-template: 1fr 1fr / none\n  }\n  .lh-category-headercol--separator {\n    display: none;\n  }\n}\n\n\n/* 964 fits the min-width of the filmstrip */\n@media screen and (max-width: 964px) {\n  .lh-report {\n    margin-left: 0;\n    width: 100%;\n  }\n}\n\n@media print {\n  body {\n    -webkit-print-color-adjust: exact; /* print background colors */\n  }\n  .lh-container {\n    display: block;\n  }\n  .lh-report {\n    margin-left: 0;\n    padding-top: 0;\n  }\n  .lh-categories {\n    margin-top: 0;\n  }\n}\n\n.lh-table {\n  position: relative;\n  border-collapse: separate;\n  border-spacing: 0;\n  /* Can\'t assign padding to table, so shorten the width instead. */\n  width: calc(100% - var(--audit-description-padding-left) - var(--stackpack-padding-horizontal));\n  border: 1px solid var(--report-border-color-secondary);\n}\n\n.lh-table thead th {\n  position: sticky;\n  top: var(--sticky-header-buffer);\n  z-index: 1;\n  background-color: var(--report-background-color);\n  border-bottom: 1px solid var(--report-border-color-secondary);\n  font-weight: normal;\n  color: var(--color-gray-600);\n  /* See text-wrapping comment on .lh-container. */\n  word-break: normal;\n}\n\n.lh-row--group {\n  background-color: var(--table-group-header-background-color);\n}\n\n.lh-row--group td {\n  font-weight: bold;\n  font-size: 1.05em;\n  color: var(--table-group-header-text-color);\n}\n\n.lh-row--group td:first-child {\n  display: block;\n  min-width: max-content;\n  font-weight: normal;\n}\n\n.lh-row--group .lh-text {\n  color: inherit;\n  text-decoration: none;\n  display: inline-block;\n}\n\n.lh-row--group a.lh-link:hover {\n  text-decoration: underline;\n}\n\n.lh-row--group .lh-audit__adorn {\n  text-transform: capitalize;\n  font-weight: normal;\n  padding: 2px 3px 1px 3px;\n}\n\n.lh-row--group .lh-audit__adorn1p {\n  color: var(--link-color);\n  border-color: var(--link-color);\n}\n\n.lh-row--group .lh-report-icon--external::before {\n  content: "";\n  background-repeat: no-repeat;\n  width: 14px;\n  height: 16px;\n  opacity: 0.7;\n  display: inline-block;\n  vertical-align: middle;\n}\n\n.lh-row--group .lh-report-icon--external {\n  visibility: hidden;\n}\n\n.lh-row--group:hover .lh-report-icon--external {\n  visibility: visible;\n}\n\n.lh-dark .lh-report-icon--external::before {\n  filter: invert(1);\n}\n\n/** Manages indentation of two-level and three-level nested adjacent rows */\n\n.lh-row--group ~ [data-entity]:not(.lh-row--group) td:first-child {\n  padding-left: 20px;\n}\n\n.lh-row--group ~ [data-entity]:not(.lh-row--group) ~ .lh-sub-item-row td:first-child {\n  padding-left: 40px;\n}\n\n.lh-row--even {\n  background-color: var(--table-group-header-background-color);\n}\n.lh-row--hidden {\n  display: none;\n}\n\n.lh-table th,\n.lh-table td {\n  padding: var(--default-padding);\n}\n\n.lh-table tr {\n  vertical-align: middle;\n}\n\n.lh-table tr:hover {\n  background-color: var(--table-higlight-background-color);\n}\n\n/* Looks unnecessary, but mostly for keeping the <th>s left-aligned */\n.lh-table-column--text,\n.lh-table-column--source-location,\n.lh-table-column--url,\n/* .lh-table-column--thumbnail, */\n/* .lh-table-column--empty,*/\n.lh-table-column--code,\n.lh-table-column--node {\n  text-align: left;\n}\n\n.lh-table-column--code {\n  min-width: 100px;\n}\n\n.lh-table-column--bytes,\n.lh-table-column--timespanMs,\n.lh-table-column--ms,\n.lh-table-column--numeric {\n  text-align: right;\n  word-break: normal;\n}\n\n\n\n.lh-table .lh-table-column--thumbnail {\n  width: var(--image-preview-size);\n}\n\n.lh-table-column--url {\n  min-width: 250px;\n}\n\n.lh-table-column--text {\n  min-width: 80px;\n}\n\n/* Keep columns narrow if they follow the URL column */\n/* 12% was determined to be a decent narrow width, but wide enough for column headings */\n.lh-table-column--url + th.lh-table-column--bytes,\n.lh-table-column--url + .lh-table-column--bytes + th.lh-table-column--bytes,\n.lh-table-column--url + .lh-table-column--ms,\n.lh-table-column--url + .lh-table-column--ms + th.lh-table-column--bytes,\n.lh-table-column--url + .lh-table-column--bytes + th.lh-table-column--timespanMs {\n  width: 12%;\n}\n\n.lh-text__url-host {\n  display: inline;\n}\n\n.lh-text__url-host {\n  margin-left: calc(var(--report-font-size) / 2);\n  opacity: 0.6;\n  font-size: 90%\n}\n\n.lh-thumbnail {\n  object-fit: cover;\n  width: var(--image-preview-size);\n  height: var(--image-preview-size);\n  display: block;\n}\n\n.lh-unknown pre {\n  overflow: scroll;\n  border: solid 1px var(--color-gray-200);\n}\n\n.lh-text__url > a {\n  color: inherit;\n  text-decoration: none;\n}\n\n.lh-text__url > a:hover {\n  text-decoration: underline dotted #999;\n}\n\n.lh-sub-item-row {\n  margin-left: 20px;\n  margin-bottom: 0;\n  color: var(--color-gray-700);\n}\n\n.lh-sub-item-row td {\n  padding-top: 4px;\n  padding-bottom: 4px;\n  padding-left: 20px;\n}\n\n.lh-sub-item-row .lh-element-screenshot {\n  zoom: 0.6;\n}\n\n/* Chevron\n   https://codepen.io/paulirish/pen/LmzEmK\n */\n.lh-chevron {\n  --chevron-angle: 42deg;\n  /* Edge doesn\'t support transform: rotate(calc(...)), so we define it here */\n  --chevron-angle-right: -42deg;\n  width: var(--chevron-size);\n  height: var(--chevron-size);\n  margin-top: calc((var(--report-line-height) - 12px) / 2);\n}\n\n.lh-chevron__lines {\n  transition: transform 0.4s;\n  transform: translateY(var(--report-line-height));\n}\n.lh-chevron__line {\n stroke: var(--chevron-line-stroke);\n stroke-width: var(--chevron-size);\n stroke-linecap: square;\n transform-origin: 50%;\n transform: rotate(var(--chevron-angle));\n transition: transform 300ms, stroke 300ms;\n}\n\n.lh-expandable-details .lh-chevron__line-right,\n.lh-expandable-details[open] .lh-chevron__line-left {\n transform: rotate(var(--chevron-angle-right));\n}\n\n.lh-expandable-details[open] .lh-chevron__line-right {\n  transform: rotate(var(--chevron-angle));\n}\n\n\n.lh-expandable-details[open]  .lh-chevron__lines {\n transform: translateY(calc(var(--chevron-size) * -1));\n}\n\n.lh-expandable-details[open] {\n  animation: 300ms openDetails forwards;\n  padding-bottom: var(--default-padding);\n}\n\n@keyframes openDetails {\n  from {\n    outline: 1px solid var(--report-background-color);\n  }\n  to {\n   outline: 1px solid;\n   box-shadow: 0 2px 4px rgba(0, 0, 0, .24);\n  }\n}\n\n@media screen and (max-width: 780px) {\n  /* no black outline if we\'re not confident the entire table can be displayed within bounds */\n  .lh-expandable-details[open] {\n    animation: none;\n  }\n}\n\n.lh-expandable-details[open] summary, details.lh-clump > summary {\n  border-bottom: 1px solid var(--report-border-color-secondary);\n}\ndetails.lh-clump[open] > summary {\n  border-bottom-width: 0;\n}\n\n\n\ndetails .lh-clump-toggletext--hide,\ndetails[open] .lh-clump-toggletext--show { display: none; }\ndetails[open] .lh-clump-toggletext--hide { display: block;}\n\n\n/* Tooltip */\n.lh-tooltip-boundary {\n  position: relative;\n}\n\n.lh-tooltip {\n  position: absolute;\n  display: none; /* Don\'t retain these layers when not needed */\n  opacity: 0;\n  background: #ffffff;\n  white-space: pre-line; /* Render newlines in the text */\n  min-width: 246px;\n  max-width: 275px;\n  padding: 15px;\n  border-radius: 5px;\n  text-align: initial;\n  line-height: 1.4;\n}\n/* shrink tooltips to not be cutoff on left edge of narrow viewports\n   45vw is chosen to be ~= width of the left column of metrics\n*/\n@media screen and (max-width: 535px) {\n  .lh-tooltip {\n    min-width: 45vw;\n    padding: 3vw;\n  }\n}\n\n.lh-tooltip-boundary:hover .lh-tooltip {\n  display: block;\n  animation: fadeInTooltip 250ms;\n  animation-fill-mode: forwards;\n  animation-delay: 850ms;\n  bottom: 100%;\n  z-index: 1;\n  will-change: opacity;\n  right: 0;\n  pointer-events: none;\n}\n\n.lh-tooltip::before {\n  content: "";\n  border: solid transparent;\n  border-bottom-color: #fff;\n  border-width: 10px;\n  position: absolute;\n  bottom: -20px;\n  right: 6px;\n  transform: rotate(180deg);\n  pointer-events: none;\n}\n\n@keyframes fadeInTooltip {\n  0% { opacity: 0; }\n  75% { opacity: 1; }\n  100% { opacity: 1;  filter: drop-shadow(1px 0px 1px #aaa) drop-shadow(0px 2px 4px hsla(206, 6%, 25%, 0.15)); pointer-events: auto; }\n}\n\n/* Element screenshot */\n.lh-element-screenshot {\n  float: left;\n  margin-right: 20px;\n}\n.lh-element-screenshot__content {\n  overflow: hidden;\n  min-width: 110px;\n  display: flex;\n  justify-content: center;\n  background-color: var(--report-background-color);\n}\n.lh-element-screenshot__image {\n  position: relative;\n  /* Set by ElementScreenshotRenderer.installFullPageScreenshotCssVariable */\n  background-image: var(--element-screenshot-url);\n  outline: 2px solid #777;\n  background-color: white;\n  background-repeat: no-repeat;\n}\n.lh-element-screenshot__mask {\n  position: absolute;\n  background: #555;\n  opacity: 0.8;\n}\n.lh-element-screenshot__element-marker {\n  position: absolute;\n  outline: 2px solid var(--color-lime-400);\n}\n.lh-element-screenshot__overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 2000; /* .lh-topbar is 1000 */\n  background: var(--screenshot-overlay-background);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: zoom-out;\n}\n\n.lh-element-screenshot__overlay .lh-element-screenshot {\n  margin-right: 0; /* clearing margin used in thumbnail case */\n  outline: 1px solid var(--color-gray-700);\n}\n\n.lh-screenshot-overlay--enabled .lh-element-screenshot {\n  cursor: zoom-out;\n}\n.lh-screenshot-overlay--enabled .lh-node .lh-element-screenshot {\n  cursor: zoom-in;\n}\n\n\n.lh-meta__items {\n  --meta-icon-size: calc(var(--report-icon-size) * 0.667);\n  padding: var(--default-padding);\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr;\n  background-color: var(--env-item-background-color);\n  border-radius: 3px;\n  margin: 0 0 var(--default-padding) 0;\n  font-size: 12px;\n  column-gap: var(--default-padding);\n  color: var(--color-gray-700);\n}\n\n.lh-meta__item {\n  display: block;\n  list-style-type: none;\n  position: relative;\n  padding: 0 0 0 calc(var(--meta-icon-size) + var(--default-padding) * 2);\n  cursor: unset; /* disable pointer cursor from report-icon */\n}\n\n.lh-meta__item.lh-tooltip-boundary {\n  text-decoration: dotted underline var(--color-gray-500);\n  cursor: help;\n}\n\n.lh-meta__item.lh-report-icon::before {\n  position: absolute;\n  left: var(--default-padding);\n  width: var(--meta-icon-size);\n  height: var(--meta-icon-size);\n}\n\n.lh-meta__item.lh-report-icon:hover::before {\n  opacity: 0.7;\n}\n\n.lh-meta__item .lh-tooltip {\n  color: var(--color-gray-800);\n}\n\n.lh-meta__item .lh-tooltip::before {\n  right: auto; /* Set the tooltip arrow to the leftside */\n  left: 6px;\n}\n\n/* Change the grid for narrow viewport. */\n@media screen and (max-width: 640px) {\n  .lh-meta__items {\n    grid-template-columns: 1fr 1fr;\n  }\n}\n@media screen and (max-width: 535px) {\n  .lh-meta__items {\n    display: block;\n  }\n}\n\n/* Explodey gauge */\n\n.lh-exp-gauge-component {\n  margin-bottom: 10px;\n}\n\n.lh-exp-gauge-component circle {\n  stroke: currentcolor;\n  r: var(--radius);\n}\n\n.lh-exp-gauge-component text {\n  font-size: calc(var(--radius) * 0.2);\n}\n\n.lh-exp-gauge-component .lh-exp-gauge {\n  margin: 0 auto;\n  width: 225px;\n  stroke-width: var(--stroke-width);\n  stroke-linecap: round;\n\n  /* for better rendering perf */\n  contain: strict;\n  height: 225px;\n  will-change: transform;\n}\n.lh-exp-gauge-component .lh-exp-gauge--faded {\n  opacity: 0.1;\n}\n.lh-exp-gauge-component .lh-exp-gauge__wrapper {\n  font-family: var(--report-font-family-monospace);\n  text-align: center;\n  text-decoration: none;\n  transition: .3s;\n}\n.lh-exp-gauge-component .lh-exp-gauge__wrapper--pass {\n  color: var(--color-pass);\n}\n.lh-exp-gauge-component .lh-exp-gauge__wrapper--average {\n  color: var(--color-average);\n}\n.lh-exp-gauge-component .lh-exp-gauge__wrapper--fail {\n  color: var(--color-fail);\n}\n.lh-exp-gauge-component .state--expanded {\n  transition: color .3s;\n}\n.lh-exp-gauge-component .state--highlight {\n  color: var(--color-highlight);\n}\n.lh-exp-gauge-component .lh-exp-gauge__svg-wrapper {\n  display: flex;\n  flex-direction: column-reverse;\n}\n\n.lh-exp-gauge-component .lh-exp-gauge__label {\n  fill: var(--report-text-color);\n  font-family: var(--report-font-family);\n  font-size: 12px;\n}\n\n.lh-exp-gauge-component .lh-exp-gauge__cutout {\n  opacity: .999;\n  transition: opacity .3s;\n}\n.lh-exp-gauge-component .state--highlight .lh-exp-gauge__cutout {\n  opacity: 0;\n}\n\n.lh-exp-gauge-component .lh-exp-gauge__inner {\n  color: inherit;\n}\n.lh-exp-gauge-component .lh-exp-gauge__base {\n  fill: currentcolor;\n}\n\n\n.lh-exp-gauge-component .lh-exp-gauge__arc {\n  fill: none;\n  transition: opacity .3s;\n}\n.lh-exp-gauge-component .lh-exp-gauge__arc--metric {\n  color: var(--metric-color);\n  stroke-dashoffset: var(--metric-offset);\n  opacity: 0.3;\n}\n.lh-exp-gauge-component .lh-exp-gauge-hovertarget {\n  color: currentcolor;\n  opacity: 0.001;\n  stroke-linecap: butt;\n  stroke-width: 24;\n  /* hack. move the hover target out of the center. ideally i tweak the r instead but that rquires considerably more math. */\n  transform: scale(1.15);\n}\n.lh-exp-gauge-component .lh-exp-gauge__arc--metric.lh-exp-gauge--miniarc {\n  opacity: 0;\n  stroke-dasharray: 0 calc(var(--circle-meas) * var(--radius));\n  transition: 0s .005s;\n}\n.lh-exp-gauge-component .state--expanded .lh-exp-gauge__arc--metric.lh-exp-gauge--miniarc {\n  opacity: .999;\n  stroke-dasharray: var(--metric-array);\n  transition: 0.3s; /*  calc(.005s + var(--i)*.05s); entrace animation */\n}\n.lh-exp-gauge-component .state--expanded .lh-exp-gauge__inner .lh-exp-gauge__arc {\n  opacity: 0;\n}\n\n\n.lh-exp-gauge-component .lh-exp-gauge__percentage {\n  text-anchor: middle;\n  dominant-baseline: middle;\n  opacity: .999;\n  font-size: calc(var(--radius) * 0.625);\n  transition: opacity .3s ease-in;\n}\n.lh-exp-gauge-component .state--highlight .lh-exp-gauge__percentage {\n  opacity: 0;\n}\n\n.lh-exp-gauge-component .lh-exp-gauge__wrapper--fail .lh-exp-gauge__percentage {\n  fill: var(--color-fail);\n}\n.lh-exp-gauge-component .lh-exp-gauge__wrapper--average .lh-exp-gauge__percentage {\n  fill: var(--color-average);\n}\n.lh-exp-gauge-component .lh-exp-gauge__wrapper--pass .lh-exp-gauge__percentage {\n  fill: var(--color-pass);\n}\n\n.lh-exp-gauge-component .lh-cover {\n  fill: none;\n  opacity: .001;\n  pointer-events: none;\n}\n.lh-exp-gauge-component .state--expanded .lh-cover {\n  pointer-events: auto;\n}\n\n.lh-exp-gauge-component .metric {\n  transform: scale(var(--scale-initial));\n  opacity: 0;\n  transition: transform .1s .2s ease-out,  opacity .3s ease-out;\n  pointer-events: none;\n}\n.lh-exp-gauge-component .metric text {\n  pointer-events: none;\n}\n.lh-exp-gauge-component .metric__value {\n  fill: currentcolor;\n  opacity: 0;\n  transition: opacity 0.2s;\n}\n.lh-exp-gauge-component .state--expanded .metric {\n  transform: scale(1);\n  opacity: .999;\n  transition: transform .3s ease-out,  opacity .3s ease-in,  stroke-width .1s ease-out;\n  transition-delay: calc(var(--i)*.05s);\n  pointer-events: auto;\n}\n.lh-exp-gauge-component .state--highlight .metric {\n  opacity: .3;\n}\n.lh-exp-gauge-component .state--highlight .metric--highlight {\n  opacity: .999;\n  stroke-width: calc(1.5*var(--stroke-width));\n}\n.lh-exp-gauge-component .state--highlight .metric--highlight .metric__value {\n  opacity: 0.999;\n}\n\n\n/*\n the initial first load peek\n*/\n.lh-exp-gauge-component .lh-exp-gauge__bg {  /* needed for the use zindex stacking w/ transparency */\n  fill: var(--report-background-color);\n  stroke: var(--report-background-color);\n}\n.lh-exp-gauge-component .state--peek .metric {\n  transition-delay: 0ms;\n  animation: peek var(--peek-dur) cubic-bezier(0.46, 0.03, 0.52, 0.96);\n  animation-fill-mode: forwards;\n}\n.lh-exp-gauge-component .state--peek .lh-exp-gauge__inner .lh-exp-gauge__arc {\n  opacity: 1;\n}\n.lh-exp-gauge-component .state--peek .lh-exp-gauge__arc.lh-exp-gauge--faded {\n  opacity: 0.3; /* just a tad stronger cuz its fighting with a big solid arg */\n}\n/* do i need to set expanded and override this? */\n.lh-exp-gauge-component .state--peek .lh-exp-gauge__arc--metric.lh-exp-gauge--miniarc {\n  transition: opacity 0.3s;\n}\n.lh-exp-gauge-component .state--peek {\n  color: unset;\n}\n.lh-exp-gauge-component .state--peek .metric__label {\n  display: none;\n}\n\n.lh-exp-gauge-component .metric__label {\n  fill: var(--report-text-color);\n}\n\n@keyframes peek {\n  /* biggest it should go is 0.92. smallest is 0.8 */\n  0% {\n    transform: scale(0.8);\n    opacity: 0.8;\n  }\n\n  50% {\n    transform: scale(0.92);\n    opacity: 1;\n  }\n\n  100% {\n    transform: scale(0.8);\n    opacity: 0.8;\n  }\n}\n\n.lh-exp-gauge-component .wrapper {\n  width: 620px;\n}\n\n/*# sourceURL=report-styles.css */\n'),t.append(n),t}(e);case"topbar":return function(e){let t=e.createFragment(),n=e.createElement("style");n.append("\n    .lh-topbar {\n      position: sticky;\n      top: 0;\n      left: 0;\n      right: 0;\n      z-index: 1000;\n      display: flex;\n      align-items: center;\n      height: var(--topbar-height);\n      padding: var(--topbar-padding);\n      font-size: var(--report-font-size-secondary);\n      background-color: var(--topbar-background-color);\n      border-bottom: 1px solid var(--color-gray-200);\n    }\n\n    .lh-topbar__logo {\n      width: var(--topbar-logo-size);\n      height: var(--topbar-logo-size);\n      user-select: none;\n      flex: none;\n    }\n\n    .lh-topbar__url {\n      margin: var(--topbar-padding);\n      text-decoration: none;\n      color: var(--report-text-color);\n      text-overflow: ellipsis;\n      overflow: hidden;\n      white-space: nowrap;\n    }\n\n    .lh-tools {\n      display: flex;\n      align-items: center;\n      margin-left: auto;\n      will-change: transform;\n      min-width: var(--report-icon-size);\n    }\n    .lh-tools__button {\n      width: var(--report-icon-size);\n      min-width: 24px;\n      height: var(--report-icon-size);\n      cursor: pointer;\n      margin-right: 5px;\n      /* This is actually a button element, but we want to style it like a transparent div. */\n      display: flex;\n      background: none;\n      color: inherit;\n      border: none;\n      padding: 0;\n      font: inherit;\n      outline: inherit;\n    }\n    .lh-tools__button svg {\n      fill: var(--tools-icon-color);\n    }\n    .lh-dark .lh-tools__button svg {\n      filter: invert(1);\n    }\n    .lh-tools__button.lh-active + .lh-tools__dropdown {\n      opacity: 1;\n      clip: rect(-1px, 194px, 270px, -3px);\n      visibility: visible;\n    }\n    .lh-tools__dropdown {\n      position: absolute;\n      background-color: var(--report-background-color);\n      border: 1px solid var(--report-border-color);\n      border-radius: 3px;\n      padding: calc(var(--default-padding) / 2) 0;\n      cursor: pointer;\n      top: 36px;\n      right: 0;\n      box-shadow: 1px 1px 3px #ccc;\n      min-width: 125px;\n      clip: rect(0, 164px, 0, 0);\n      visibility: hidden;\n      opacity: 0;\n      transition: all 200ms cubic-bezier(0,0,0.2,1);\n    }\n    .lh-tools__dropdown a {\n      color: currentColor;\n      text-decoration: none;\n      white-space: nowrap;\n      padding: 0 6px;\n      line-height: 2;\n    }\n    .lh-tools__dropdown a:hover,\n    .lh-tools__dropdown a:focus {\n      background-color: var(--color-gray-200);\n      outline: none;\n    }\n    /* save-gist option hidden in report. */\n    .lh-tools__dropdown a[data-action='save-gist'] {\n      display: none;\n    }\n\n    .lh-locale-selector {\n      width: 100%;\n      color: var(--report-text-color);\n      background-color: var(--locale-selector-background-color);\n      padding: 2px;\n    }\n    .lh-tools-locale {\n      display: flex;\n      align-items: center;\n      flex-direction: row-reverse;\n    }\n    .lh-tools-locale__selector-wrapper {\n      transition: opacity 0.15s;\n      opacity: 0;\n      max-width: 200px;\n    }\n    .lh-button.lh-tool-locale__button {\n      height: var(--topbar-height);\n      color: var(--tools-icon-color);\n      padding: calc(var(--default-padding) / 2);\n    }\n    .lh-tool-locale__button.lh-active + .lh-tools-locale__selector-wrapper {\n      opacity: 1;\n      clip: rect(-1px, 194px, 242px, -3px);\n      visibility: visible;\n      margin: 0 4px;\n    }\n\n    @media screen and (max-width: 964px) {\n      .lh-tools__dropdown {\n        right: 0;\n        left: initial;\n      }\n    }\n    @media print {\n      .lh-topbar {\n        position: static;\n        margin-left: 0;\n      }\n\n      .lh-tools__dropdown {\n        display: none;\n      }\n    }\n  "),t.append(n);let r=e.createElement("div","lh-topbar"),i=e.createElementNS("http://www.w3.org/2000/svg","svg","lh-topbar__logo");i.setAttribute("role","img"),i.setAttribute("title","Lighthouse logo"),i.setAttribute("fill","none"),i.setAttribute("xmlns","http://www.w3.org/2000/svg"),i.setAttribute("viewBox","0 0 48 48");let a=e.createElementNS("http://www.w3.org/2000/svg","path");a.setAttribute("d","m14 7 10-7 10 7v10h5v7h-5l5 24H9l5-24H9v-7h5V7Z"),a.setAttribute("fill","#F63");let o=e.createElementNS("http://www.w3.org/2000/svg","path");o.setAttribute("d","M31.561 24H14l-1.689 8.105L31.561 24ZM18.983 48H9l1.022-4.907L35.723 32.27l1.663 7.98L18.983 48Z"),o.setAttribute("fill","#FFA385");let l=e.createElementNS("http://www.w3.org/2000/svg","path");l.setAttribute("fill","#FF3"),l.setAttribute("d","M20.5 10h7v7h-7z"),i.append(" ",a," ",o," ",l," ");let s=e.createElement("a","lh-topbar__url");s.setAttribute("href",""),s.setAttribute("target","_blank"),s.setAttribute("rel","noopener");let c=e.createElement("div","lh-tools"),d=e.createElement("div","lh-tools-locale lh-hidden"),h=e.createElement("button","lh-button lh-tool-locale__button");h.setAttribute("id","lh-button__swap-locales"),h.setAttribute("title","Show Language Picker"),h.setAttribute("aria-label","Toggle language picker"),h.setAttribute("aria-haspopup","menu"),h.setAttribute("aria-expanded","false"),h.setAttribute("aria-controls","lh-tools-locale__selector-wrapper");let p=e.createElementNS("http://www.w3.org/2000/svg","svg");p.setAttribute("width","20px"),p.setAttribute("height","20px"),p.setAttribute("viewBox","0 0 24 24"),p.setAttribute("fill","currentColor");let g=e.createElementNS("http://www.w3.org/2000/svg","path");g.setAttribute("d","M0 0h24v24H0V0z"),g.setAttribute("fill","none");let u=e.createElementNS("http://www.w3.org/2000/svg","path");u.setAttribute("d","M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"),p.append(g,u),h.append(" ",p," ");let m=e.createElement("div","lh-tools-locale__selector-wrapper");m.setAttribute("id","lh-tools-locale__selector-wrapper"),m.setAttribute("role","menu"),m.setAttribute("aria-labelledby","lh-button__swap-locales"),m.setAttribute("aria-hidden","true"),m.append(" "," "),d.append(" ",h," ",m," ");let f=e.createElement("button","lh-tools__button");f.setAttribute("id","lh-tools-button"),f.setAttribute("title","Tools menu"),f.setAttribute("aria-label","Toggle report tools menu"),f.setAttribute("aria-haspopup","menu"),f.setAttribute("aria-expanded","false"),f.setAttribute("aria-controls","lh-tools-dropdown");let v=e.createElementNS("http://www.w3.org/2000/svg","svg");v.setAttribute("width","100%"),v.setAttribute("height","100%"),v.setAttribute("viewBox","0 0 24 24");let b=e.createElementNS("http://www.w3.org/2000/svg","path");b.setAttribute("d","M0 0h24v24H0z"),b.setAttribute("fill","none");let _=e.createElementNS("http://www.w3.org/2000/svg","path");_.setAttribute("d","M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"),v.append(" ",b," ",_," "),f.append(" ",v," ");let w=e.createElement("div","lh-tools__dropdown");w.setAttribute("id","lh-tools-dropdown"),w.setAttribute("role","menu"),w.setAttribute("aria-labelledby","lh-tools-button");let y=e.createElement("a","lh-report-icon lh-report-icon--print");y.setAttribute("role","menuitem"),y.setAttribute("tabindex","-1"),y.setAttribute("href","#"),y.setAttribute("data-i18n","dropdownPrintSummary"),y.setAttribute("data-action","print-summary");let x=e.createElement("a","lh-report-icon lh-report-icon--print");x.setAttribute("role","menuitem"),x.setAttribute("tabindex","-1"),x.setAttribute("href","#"),x.setAttribute("data-i18n","dropdownPrintExpanded"),x.setAttribute("data-action","print-expanded");let k=e.createElement("a","lh-report-icon lh-report-icon--copy");k.setAttribute("role","menuitem"),k.setAttribute("tabindex","-1"),k.setAttribute("href","#"),k.setAttribute("data-i18n","dropdownCopyJSON"),k.setAttribute("data-action","copy");let E=e.createElement("a","lh-report-icon lh-report-icon--download lh-hidden");E.setAttribute("role","menuitem"),E.setAttribute("tabindex","-1"),E.setAttribute("href","#"),E.setAttribute("data-i18n","dropdownSaveHTML"),E.setAttribute("data-action","save-html");let A=e.createElement("a","lh-report-icon lh-report-icon--download");A.setAttribute("role","menuitem"),A.setAttribute("tabindex","-1"),A.setAttribute("href","#"),A.setAttribute("data-i18n","dropdownSaveJSON"),A.setAttribute("data-action","save-json");let S=e.createElement("a","lh-report-icon lh-report-icon--open");S.setAttribute("role","menuitem"),S.setAttribute("tabindex","-1"),S.setAttribute("href","#"),S.setAttribute("data-i18n","dropdownViewer"),S.setAttribute("data-action","open-viewer");let z=e.createElement("a","lh-report-icon lh-report-icon--open");z.setAttribute("role","menuitem"),z.setAttribute("tabindex","-1"),z.setAttribute("href","#"),z.setAttribute("data-i18n","dropdownSaveGist"),z.setAttribute("data-action","save-gist");let L=e.createElement("a","lh-report-icon lh-report-icon--open lh-hidden");L.setAttribute("role","menuitem"),L.setAttribute("tabindex","-1"),L.setAttribute("href","#"),L.setAttribute("data-i18n","dropdownViewUnthrottledTrace"),L.setAttribute("data-action","view-unthrottled-trace");let C=e.createElement("a","lh-report-icon lh-report-icon--dark");return C.setAttribute("role","menuitem"),C.setAttribute("tabindex","-1"),C.setAttribute("href","#"),C.setAttribute("data-i18n","dropdownDarkTheme"),C.setAttribute("data-action","toggle-dark"),w.append(" ",y," ",x," ",k," "," ",E," ",A," ",S," ",z," "," ",L," ",C," "),c.append(" ",d," ",f," ",w," "),r.append(" "," ",i," ",s," ",c," "),t.append(r),t}(e);case"warningsToplevel":return function(e){let t=e.createFragment(),n=e.createElement("div","lh-warnings lh-warnings--toplevel"),r=e.createElement("p","lh-warnings__msg"),i=e.createElement("ul");return n.append(" ",r," ",i," "),t.append(n),t}(e)}throw new Error("unexpected component: "+t)}(this,e),this._componentCache.set(e,t),t.cloneNode(!0)}clearComponentCache(){this._componentCache.clear()}convertMarkdownLinkSnippets(e,t={}){let n=this.createElement("span");for(let r of i.splitMarkdownLink(e)){let e=r.text.includes("`")?this.convertMarkdownCodeSnippets(r.text):r.text;if(!r.isLink){n.append(e);continue}let i=new URL(r.linkHref);(["https://developers.google.com","https://web.dev","https://developer.chrome.com"].includes(i.origin)||t.alwaysAppendUtmSource)&&(i.searchParams.set("utm_source","lighthouse"),i.searchParams.set("utm_medium",this._lighthouseChannel));let a=this.createElement("a");a.rel="noopener",a.target="_blank",a.append(e),this.safelySetHref(a,i.href),n.append(a)}return n}safelySetHref(e,t){if((t=t||"").startsWith("#"))return void(e.href=t);let n;try{n=new URL(t)}catch{}n&&["https:","http:"].includes(n.protocol)&&(e.href=n.href)}safelySetBlobHref(e,t){if("text/html"!==t.type&&"application/json"!==t.type)throw new Error("Unsupported blob type");let n=URL.createObjectURL(t);e.href=n}convertMarkdownCodeSnippets(e){let t=this.createElement("span");for(let n of i.splitMarkdownCodeSpans(e))if(n.isCode){let e=this.createElement("code");e.textContent=n.text,t.append(e)}else t.append(this._document.createTextNode(n.text));return t}setLighthouseChannel(e){this._lighthouseChannel=e}document(){return this._document}isDevTools(){return!!this._document.querySelector(".lh-devtools")}find(e,t=this.rootEl??this._document){let n=this.maybeFind(e,t);if(null===n)throw new Error(`query ${e} not found`);return n}maybeFind(e,t){return t.querySelector(e)}findAll(e,t){return Array.from(t.querySelectorAll(e))}fireEventOn(e,t=this._document,n){let r=new CustomEvent(e,n?{detail:n}:void 0);t.dispatchEvent(r)}saveFile(e,t){let n=this.createElement("a");n.download=t,this.safelySetBlobHref(n,e),this._document.body.append(n),n.click(),this._document.body.removeChild(n),setTimeout((()=>URL.revokeObjectURL(n.href)),500)}},o=0,l=class e{static i18n=null;static strings={};static reportJson=null;static apply(t){e.strings={...h,...t.providedStrings},e.i18n=t.i18n,e.reportJson=t.reportJson}static getUniqueSuffix(){return o++}static resetUniqueSuffix(){o=0}},s="data:image/jpeg;base64,";var c=i.RATINGS,d=class e{static prepareReportResult(t){let n=JSON.parse(JSON.stringify(t));!function(e){e.configSettings.locale||(e.configSettings.locale="en"),e.configSettings.formFactor||(e.configSettings.formFactor=e.configSettings.emulatedFormFactor),e.finalDisplayedUrl=i.getFinalDisplayedUrl(e),e.mainDocumentUrl=i.getMainDocumentUrl(e);for(let t of Object.values(e.audits))if(("not_applicable"===t.scoreDisplayMode||"not-applicable"===t.scoreDisplayMode)&&(t.scoreDisplayMode="notApplicable"),"informative"===t.scoreDisplayMode&&(t.score=1),t.details){if((void 0===t.details.type||"diagnostic"===t.details.type)&&(t.details.type="debugdata"),"filmstrip"===t.details.type)for(let e of t.details.items)e.data.startsWith(s)||(e.data=s+e.data);if("table"===t.details.type)for(let e of t.details.headings){let{itemType:t,text:n}=e;void 0!==t&&(e.valueType=t,delete e.itemType),void 0!==n&&(e.label=n,delete e.text);let r=e.subItemsHeading?.itemType;e.subItemsHeading&&void 0!==r&&(e.subItemsHeading.valueType=r,delete e.subItemsHeading.itemType)}if("third-party-summary"===t.id&&("opportunity"===t.details.type||"table"===t.details.type)){let{headings:e,items:n}=t.details;if("link"===e[0].valueType){e[0].valueType="text";for(let e of n)"object"==typeof e.entity&&"link"===e.entity.type&&(e.entity=e.entity.text);t.details.isEntityGrouped=!0}}}let[t]=e.lighthouseVersion.split(".").map(Number),n=e.categories.performance;if(n)if(t<9){e.categoryGroups||(e.categoryGroups={}),e.categoryGroups.hidden={title:""};for(let e of n.auditRefs)e.group?"load-opportunities"===e.group&&(e.group="diagnostics"):e.group="hidden"}else if(t<12)for(let e of n.auditRefs)e.group||(e.group="diagnostics");if(t<12&&n){let t=new Map;for(let e of n.auditRefs){let n=e.relevantAudits;if(n&&e.acronym)for(let r of n){let n=t.get(r)||[];n.push(e.acronym),t.set(r,n)}}for(let[n,r]of t){if(!r.length)continue;let t=e.audits[n];if(t&&!t.metricSavings){t.metricSavings={};for(let e of r)t.metricSavings[e]=0}}}if(e.environment||(e.environment={benchmarkIndex:0,networkUserAgent:e.userAgent,hostUserAgent:e.userAgent}),e.configSettings.screenEmulation||(e.configSettings.screenEmulation={width:-1,height:-1,deviceScaleFactor:-1,mobile:/mobile/i.test(e.environment.hostUserAgent),disabled:!1}),e.i18n||(e.i18n={}),e.audits["full-page-screenshot"]){let t=e.audits["full-page-screenshot"].details;e.fullPageScreenshot=t?{screenshot:t.screenshot,nodes:t.nodes}:null,delete e.audits["full-page-screenshot"]}}(n);for(let t of Object.values(n.audits))t.details&&("opportunity"===t.details.type||"table"===t.details.type)&&!t.details.isEntityGrouped&&n.entities&&e.classifyEntities(n.entities,t.details);if("object"!=typeof n.categories)throw new Error("No categories provided.");let r=new Map;for(let e of Object.values(n.categories))e.auditRefs.forEach((e=>{e.acronym&&r.set(e.acronym,e)})),e.auditRefs.forEach((e=>{let t=n.audits[e.id];e.result=t;let i=Object.keys(e.result.metricSavings||{});if(i.length){e.relevantMetrics=[];for(let t of i){let n=r.get(t);n&&e.relevantMetrics.push(n)}}n.stackPacks&&n.stackPacks.forEach((t=>{t.descriptions[e.id]&&(e.stackPacks=e.stackPacks||[],e.stackPacks.push({title:t.title,iconDataURL:t.iconDataURL,description:t.descriptions[e.id]}))}))}));return n}static getUrlLocatorFn(e){let t=e.find((e=>"url"===e.valueType))?.key;if(t&&"string"==typeof t)return e=>{let n=e[t];if("string"==typeof n)return n};let n=e.find((e=>"source-location"===e.valueType))?.key;return n?e=>{let t=e[n];if("object"==typeof t&&"source-location"===t.type)return t.url}:void 0}static classifyEntities(t,n){let{items:r,headings:a}=n;if(!r.length||r.some((e=>e.entity)))return;let o=e.getUrlLocatorFn(a);if(o)for(let e of r){let n=o(e);if(!n)continue;let r="";try{r=i.parseURL(n).origin}catch{}if(!r)continue;let a=t.find((e=>e.origins.includes(r)));a&&(e.entity=a.name)}}static getTableItemSortComparator(e){return(t,n)=>{for(let r of e){let e=t[r],i=n[r];if((typeof e!=typeof i||!["number","string"].includes(typeof e))&&console.warn(`Warning: Attempting to sort unsupported value type: ${r}.`),"number"==typeof e&&"number"==typeof i&&e!==i)return i-e;if("string"==typeof e&&"string"==typeof i&&e!==i)return e.localeCompare(i)}return 0}}static getEmulationDescriptions(e){let t,n,r,i=e.throttling,a=l.i18n,o=l.strings;switch(e.throttlingMethod){case"provided":r=n=t=o.throttlingProvided;break;case"devtools":{let{cpuSlowdownMultiplier:e,requestLatencyMs:l}=i;t=`${a.formatNumber(e)}x slowdown (DevTools)`,n=`${a.formatMilliseconds(l)} HTTP RTT, ${a.formatKbps(i.downloadThroughputKbps)} down, ${a.formatKbps(i.uploadThroughputKbps)} up (DevTools)`,r=562.5===l&&i.downloadThroughputKbps===1638.4*.9&&675===i.uploadThroughputKbps?o.runtimeSlow4g:o.runtimeCustom;break}case"simulate":{let{cpuSlowdownMultiplier:e,rttMs:l,throughputKbps:s}=i;t=`${a.formatNumber(e)}x slowdown (Simulated)`,n=`${a.formatMilliseconds(l)} TCP RTT, ${a.formatKbps(s)} throughput (Simulated)`,r=150===l&&1638.4===s?o.runtimeSlow4g:o.runtimeCustom;break}default:r=t=n=o.runtimeUnknown}let s="devtools"!==e.channel&&e.screenEmulation.disabled,c="devtools"===e.channel?"mobile"===e.formFactor:e.screenEmulation.mobile,d=o.runtimeMobileEmulation;return s?d=o.runtimeNoEmulation:c||(d=o.runtimeDesktopEmulation),{deviceEmulation:d,screenEmulation:s?void 0:`${e.screenEmulation.width}x${e.screenEmulation.height}, DPR ${e.screenEmulation.deviceScaleFactor}`,cpuThrottling:t,networkThrottling:n,summary:r}}static showAsPassed(e){switch(e.scoreDisplayMode){case"manual":case"notApplicable":return!0;case"error":case"informative":return!1;default:return Number(e.score)>=c.PASS.minScore}}static calculateRating(e,t){if("manual"===t||"notApplicable"===t)return c.PASS.label;if("error"===t)return c.ERROR.label;if(null===e)return c.FAIL.label;let n=c.FAIL.label;return e>=c.PASS.minScore?n=c.PASS.label:e>=c.AVERAGE.minScore&&(n=c.AVERAGE.label),n}static calculateCategoryFraction(t){let n=0,r=0,i=0,a=0;for(let o of t.auditRefs){let t=e.showAsPassed(o.result);if("hidden"!==o.group&&"manual"!==o.result.scoreDisplayMode&&"notApplicable"!==o.result.scoreDisplayMode){if("informative"===o.result.scoreDisplayMode){t||++i;continue}++n,a+=o.weight,t&&r++}}return{numPassed:r,numPassableAudits:n,numInformative:i,totalWeight:a}}static isPluginCategory(e){return e.startsWith("lighthouse-plugin-")}static shouldDisplayAsFraction(e){return"timespan"===e||"snapshot"===e}},h={varianceDisclaimer:"Values are estimated and may vary. The [performance score is calculated](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directly from these metrics.",calculatorLink:"See calculator.",showRelevantAudits:"Show audits relevant to:",opportunityResourceColumnLabel:"Opportunity",opportunitySavingsColumnLabel:"Estimated Savings",errorMissingAuditInfo:"Report error: no audit information",errorLabel:"Error!",warningHeader:"Warnings: ",warningAuditsGroupTitle:"Passed audits but with warnings",passedAuditsGroupTitle:"Passed audits",notApplicableAuditsGroupTitle:"Not applicable",manualAuditsGroupTitle:"Additional items to manually check",toplevelWarningsMessage:"There were issues affecting this run of Lighthouse:",crcInitialNavigation:"Initial Navigation",crcLongestDurationLabel:"Maximum critical path latency:",snippetExpandButtonLabel:"Expand snippet",snippetCollapseButtonLabel:"Collapse snippet",lsPerformanceCategoryDescription:"[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analysis of the current page on an emulated mobile network. Values are estimated and may vary.",labDataTitle:"Lab Data",thirdPartyResourcesLabel:"Show 3rd-party resources",viewTreemapLabel:"View Treemap",viewTraceLabel:"View Trace",dropdownPrintSummary:"Print Summary",dropdownPrintExpanded:"Print Expanded",dropdownCopyJSON:"Copy JSON",dropdownSaveHTML:"Save as HTML",dropdownSaveJSON:"Save as JSON",dropdownViewer:"Open in Viewer",dropdownSaveGist:"Save as Gist",dropdownDarkTheme:"Toggle Dark Theme",dropdownViewUnthrottledTrace:"View Unthrottled Trace",runtimeSettingsDevice:"Device",runtimeSettingsNetworkThrottling:"Network throttling",runtimeSettingsCPUThrottling:"CPU throttling",runtimeSettingsUANetwork:"User agent (network)",runtimeSettingsBenchmark:"Unthrottled CPU/Memory Power",runtimeSettingsAxeVersion:"Axe version",runtimeSettingsScreenEmulation:"Screen emulation",footerIssue:"File an issue",runtimeNoEmulation:"No emulation",runtimeMobileEmulation:"Emulated Moto G Power",runtimeDesktopEmulation:"Emulated Desktop",runtimeUnknown:"Unknown",runtimeSingleLoad:"Single page session",runtimeAnalysisWindow:"Initial page load",runtimeAnalysisWindowTimespan:"User interactions timespan",runtimeAnalysisWindowSnapshot:"Point-in-time snapshot",runtimeSingleLoadTooltip:"This data is taken from a single page session, as opposed to field data summarizing many sessions.",throttlingProvided:"Provided by environment",show:"Show",hide:"Hide",expandView:"Expand view",collapseView:"Collapse view",runtimeSlow4g:"Slow 4G throttling",runtimeCustom:"Custom throttling",firstPartyChipLabel:"1st party",openInANewTabTooltip:"Open in a new tab",unattributable:"Unattributable"},p=class{constructor(e,t){this.dom=e,this.detailsRenderer=t}get _clumpTitles(){return{warning:l.strings.warningAuditsGroupTitle,manual:l.strings.manualAuditsGroupTitle,passed:l.strings.passedAuditsGroupTitle,notApplicable:l.strings.notApplicableAuditsGroupTitle}}renderAudit(e){let t=l.strings,n=this.dom.createComponent("audit"),r=this.dom.find("div.lh-audit",n);r.id=e.result.id;let i=e.result.scoreDisplayMode;e.result.displayValue&&(this.dom.find(".lh-audit__display-text",r).textContent=e.result.displayValue);let a=this.dom.find(".lh-audit__title",r);a.append(this.dom.convertMarkdownCodeSnippets(e.result.title));let o=this.dom.find(".lh-audit__description",r);o.append(this.dom.convertMarkdownLinkSnippets(e.result.description));for(let t of e.relevantMetrics||[]){let e=this.dom.createChildOf(o,"span","lh-audit__adorn");e.title=`Relevant to ${t.result.title}`,e.textContent=t.acronym||t.id}e.stackPacks&&e.stackPacks.forEach((e=>{let t=this.dom.createElement("img","lh-audit__stackpack__img");t.src=e.iconDataURL,t.alt=e.title;let n=this.dom.convertMarkdownLinkSnippets(e.description,{alwaysAppendUtmSource:!0}),i=this.dom.createElement("div","lh-audit__stackpack");i.append(t,n),this.dom.find(".lh-audit__stackpacks",r).append(i)}));let s=this.dom.find("details",r);if(e.result.details){let t=this.detailsRenderer.render(e.result.details);t&&(t.classList.add("lh-details"),s.append(t))}if(this.dom.find(".lh-chevron-container",r).append(this._createChevron()),this._setRatingClass(r,e.result.score,i),"error"===e.result.scoreDisplayMode){r.classList.add("lh-audit--error");let n=this.dom.find(".lh-audit__display-text",r);n.textContent=t.errorLabel,n.classList.add("lh-tooltip-boundary"),this.dom.createChildOf(n,"div","lh-tooltip lh-tooltip--error").textContent=e.result.errorMessage||t.errorMissingAuditInfo}else if(e.result.explanation){this.dom.createChildOf(a,"div","lh-audit-explanation").textContent=e.result.explanation}let c=e.result.warnings;if(!c||0===c.length)return r;let d=this.dom.find("summary",s),h=this.dom.createChildOf(d,"div","lh-warnings");if(this.dom.createChildOf(h,"span").textContent=t.warningHeader,1===c.length)h.append(this.dom.createTextNode(c.join("")));else{let e=this.dom.createChildOf(h,"ul");for(let t of c){this.dom.createChildOf(e,"li").textContent=t}}return r}injectFinalScreenshot(e,t,n){let r=t["final-screenshot"];if(!r||"error"===r.scoreDisplayMode||!r.details||"screenshot"!==r.details.type)return null;let i=this.dom.createElement("img","lh-final-ss-image"),a=r.details.data;i.src=a,i.alt=r.title;let o=this.dom.find(".lh-category .lh-category-header",e),l=this.dom.createElement("div","lh-category-headercol"),s=this.dom.createElement("div","lh-category-headercol lh-category-headercol--separator"),c=this.dom.createElement("div","lh-category-headercol");l.append(...o.childNodes),l.append(n),c.append(i),o.append(l,s,c),o.classList.add("lh-category-header__finalscreenshot")}_createChevron(){let e=this.dom.createComponent("chevron");return this.dom.find("svg.lh-chevron",e)}_setRatingClass(e,t,n){let r=d.calculateRating(t,n);return e.classList.add(`lh-audit--${n.toLowerCase()}`),"informative"!==n&&e.classList.add(`lh-audit--${r}`),e}renderCategoryHeader(e,t,n){let r=this.dom.createComponent("categoryHeader"),i=this.dom.find(".lh-score__gauge",r),a=this.renderCategoryScore(e,t,n);if(i.append(a),e.description){let t=this.dom.convertMarkdownLinkSnippets(e.description);this.dom.find(".lh-category-header__description",r).append(t)}return r}renderAuditGroup(e){let t=this.dom.createElement("div","lh-audit-group"),n=this.dom.createElement("div","lh-audit-group__header");this.dom.createChildOf(n,"span","lh-audit-group__title").textContent=e.title,t.append(n);let r=null;return e.description&&(r=this.dom.convertMarkdownLinkSnippets(e.description),r.classList.add("lh-audit-group__description","lh-audit-group__footer"),t.append(r)),[t,r]}_renderGroupedAudits(e,t){let n=new Map,r="NotAGroup";n.set(r,[]);for(let t of e){let e=t.group||r,i=n.get(e)||[];i.push(t),n.set(e,i)}let i=[];for(let[e,a]of n){if(e===r){for(let e of a)i.push(this.renderAudit(e));continue}let n=t[e],[o,l]=this.renderAuditGroup(n);for(let e of a)o.insertBefore(this.renderAudit(e),l);o.classList.add(`lh-audit-group--${e}`),i.push(o)}return i}renderUnexpandableClump(e,t){let n=this.dom.createElement("div");return this._renderGroupedAudits(e,t).forEach((e=>n.append(e))),n}renderClump(e,{auditRefsOrEls:t,description:n,openByDefault:r}){let i=this.dom.createComponent("clump"),a=this.dom.find(".lh-clump",i);r&&a.setAttribute("open","");let o=this.dom.find(".lh-audit-group__header",a),s=this._clumpTitles[e];this.dom.find(".lh-audit-group__title",o).textContent=s,this.dom.find(".lh-audit-group__itemcount",a).textContent=`(${t.length})`;let c=t.map((e=>e instanceof HTMLElement?e:this.renderAudit(e)));a.append(...c);let d=this.dom.find(".lh-audit-group",i);if(n){let e=this.dom.convertMarkdownLinkSnippets(n);e.classList.add("lh-audit-group__description","lh-audit-group__footer"),d.append(e)}return this.dom.find(".lh-clump-toggletext--show",d).textContent=l.strings.show,this.dom.find(".lh-clump-toggletext--hide",d).textContent=l.strings.hide,a.classList.add(`lh-clump--${e.toLowerCase()}`),d}renderCategoryScore(e,t,n){let r;if(r=n&&d.shouldDisplayAsFraction(n.gatherMode)?this.renderCategoryFraction(e):this.renderScoreGauge(e,t),n?.omitLabel&&this.dom.find(".lh-gauge__label,.lh-fraction__label",r).remove(),n?.onPageAnchorRendered){let e=this.dom.find("a",r);n.onPageAnchorRendered(e)}return r}renderScoreGauge(e,t){let n=this.dom.createComponent("gauge"),r=this.dom.find("a.lh-gauge__wrapper",n);d.isPluginCategory(e.id)&&r.classList.add("lh-gauge__wrapper--plugin");let i=Number(e.score),a=this.dom.find(".lh-gauge",n),o=this.dom.find("circle.lh-gauge-arc",a);o&&this._setGaugeArc(o,i);let s=Math.round(100*i),c=this.dom.find("div.lh-gauge__percentage",n);return c.textContent=s.toString(),null===e.score&&(c.classList.add("lh-gauge--error"),c.textContent="",c.title=l.strings.errorLabel),0===e.auditRefs.length||this.hasApplicableAudits(e)?r.classList.add(`lh-gauge__wrapper--${d.calculateRating(e.score)}`):(r.classList.add("lh-gauge__wrapper--not-applicable"),c.textContent="-",c.title=l.strings.notApplicableAuditsGroupTitle),this.dom.find(".lh-gauge__label",n).textContent=e.title,n}renderCategoryFraction(e){let t=this.dom.createComponent("fraction"),n=this.dom.find("a.lh-fraction__wrapper",t),{numPassed:r,numPassableAudits:i,totalWeight:a}=d.calculateCategoryFraction(e),o=r/i,l=this.dom.find(".lh-fraction__content",t),s=this.dom.createElement("span");s.textContent=`${r}/${i}`,l.append(s);let c=d.calculateRating(o);return 0===a&&(c="null"),n.classList.add(`lh-fraction__wrapper--${c}`),this.dom.find(".lh-fraction__label",t).textContent=e.title,t}hasApplicableAudits(e){return e.auditRefs.some((e=>"notApplicable"!==e.result.scoreDisplayMode))}_setGaugeArc(e,t){let n=2*Math.PI*Number(e.getAttribute("r")),r=Number(e.getAttribute("stroke-width")),i=.25*r/n;e.style.transform=`rotate(${360*i-90}deg)`;let a=t*n-r/2;0===t&&(e.style.opacity="0"),1===t&&(a=n),e.style.strokeDasharray=`${Math.max(a,0)} ${n}`}_auditHasWarning(e){return!!e.result.warnings?.length}_getClumpIdForAuditRef(e){let t=e.result.scoreDisplayMode;return"manual"===t||"notApplicable"===t?t:d.showAsPassed(e.result)?this._auditHasWarning(e)?"warning":"passed":"failed"}render(e,t={},n){let r=this.dom.createElement("div","lh-category");r.id=e.id,r.append(this.renderCategoryHeader(e,t,n));let i=new Map;i.set("failed",[]),i.set("warning",[]),i.set("manual",[]),i.set("passed",[]),i.set("notApplicable",[]);for(let t of e.auditRefs){if("hidden"===t.group)continue;let e=this._getClumpIdForAuditRef(t),n=i.get(e);n.push(t),i.set(e,n)}for(let e of i.values())e.sort(((e,t)=>t.weight-e.weight));let a=i.get("failed")?.length;for(let[n,o]of i){if(0===o.length)continue;if("failed"===n){let e=this.renderUnexpandableClump(o,t);e.classList.add("lh-clump--failed"),r.append(e);continue}let i="manual"===n?e.manualDescription:void 0,l="warning"===n||"manual"===n&&0===a,s=this.renderClump(n,{auditRefsOrEls:o,description:i,openByDefault:l});r.append(s)}return r}},g=class{static initTree(e){let t=0,n=Object.keys(e);return n.length>0&&(t=e[n[0]].request.startTime),{tree:e,startTime:t,transferSize:0}}static createSegment(e,t,n,r,i,a){let o=e[t],l=Object.keys(e),s=l.indexOf(t)===l.length-1,c=!!o.children&&Object.keys(o.children).length>0,d=Array.isArray(i)?i.slice(0):[];return typeof a<"u"&&d.push(!a),{node:o,isLastChild:s,hasChildren:c,startTime:n,transferSize:r+o.request.transferSize,treeMarkers:d}}static createChainNode(e,t,n){let r=e.createComponent("crcChain");e.find(".lh-crc-node",r).setAttribute("title",t.node.request.url);let i=e.find(".lh-crc-node__tree-marker",r);t.treeMarkers.forEach((t=>{let n=t?"lh-tree-marker lh-vert":"lh-tree-marker";i.append(e.createElement("span",n),e.createElement("span","lh-tree-marker"))}));let a=t.isLastChild?"lh-tree-marker lh-up-right":"lh-tree-marker lh-vert-right",o=t.hasChildren?"lh-tree-marker lh-horiz-down":"lh-tree-marker lh-right";i.append(e.createElement("span",a),e.createElement("span","lh-tree-marker lh-right"),e.createElement("span",o));let s=t.node.request.url,c=n.renderTextURL(s),d=e.find(".lh-crc-node__tree-value",r);if(d.append(c),!t.hasChildren){let{startTime:n,endTime:r,transferSize:i}=t.node.request,a=e.createElement("span","lh-crc-node__chain-duration");a.textContent=" - "+l.i18n.formatMilliseconds(1e3*(r-n))+", ";let o=e.createElement("span","lh-crc-node__chain-duration");o.textContent=l.i18n.formatBytesToKiB(i,.01),d.append(a,o)}return r}static buildTree(e,t,n,r,i,a){if(r.append(u.createChainNode(e,n,a)),n.node.children)for(let o of Object.keys(n.node.children)){let l=u.createSegment(n.node.children,o,n.startTime,n.transferSize,n.treeMarkers,n.isLastChild);u.buildTree(e,t,l,r,i,a)}}static render(e,t,n){let r=e.createComponent("crc"),i=e.find(".lh-crc",r);e.find(".lh-crc-initial-nav",r).textContent=l.strings.crcInitialNavigation,e.find(".lh-crc__longest_duration_label",r).textContent=l.strings.crcLongestDurationLabel,e.find(".lh-crc__longest_duration",r).textContent=l.i18n.formatMilliseconds(t.longestChain.duration);let a=u.initTree(t.chains);for(let o of Object.keys(a.tree)){let l=u.createSegment(a.tree,o,a.startTime,a.transferSize);u.buildTree(e,r,l,i,t,n)}return e.find(".lh-crc-container",r)}},u=g;function m(e,t,n){return e<t?t:e>n?n:e}var f=class e{static getScreenshotPositions(e,t,n){let r=function(e){return{x:e.left+e.width/2,y:e.top+e.height/2}}(e),i=m(r.x-t.width/2,0,n.width-t.width),a=m(r.y-t.height/2,0,n.height-t.height);return{screenshot:{left:i,top:a},clip:{left:e.left-i,top:e.top-a}}}static renderClipPathInScreenshot(e,t,n,r,i){let a=e.find("clipPath",t),o=`clip-${l.getUniqueSuffix()}`;a.id=o,t.style.clipPath=`url(#${o})`;let s=n.top/i.height,c=s+r.height/i.height,d=n.left/i.width,h=d+r.width/i.width,p=[`0,0             1,0            1,${s}          0,${s}`,`0,${c}     1,${c}    1,1               0,1`,`0,${s}        ${d},${s} ${d},${c} 0,${c}`,`${h},${s} 1,${s}       1,${c}       ${h},${c}`];for(let t of p){let n=e.createElementNS("http://www.w3.org/2000/svg","polygon");n.setAttribute("points",t),a.append(n)}}static installFullPageScreenshot(e,t){e.style.setProperty("--element-screenshot-url",`url('${t.data}')`)}static installOverlayFeature(t){let{dom:n,rootEl:r,overlayContainerEl:i,fullPageScreenshot:a}=t,o="lh-screenshot-overlay--enabled";r.classList.contains(o)||(r.classList.add(o),r.addEventListener("click",(t=>{let r=t.target;if(!r)return;let o=r.closest(".lh-node > .lh-element-screenshot");if(!o)return;let l=n.createElement("div","lh-element-screenshot__overlay");i.append(l);let s={width:.95*l.clientWidth,height:.8*l.clientHeight},c={width:Number(o.dataset.rectWidth),height:Number(o.dataset.rectHeight),left:Number(o.dataset.rectLeft),right:Number(o.dataset.rectLeft)+Number(o.dataset.rectWidth),top:Number(o.dataset.rectTop),bottom:Number(o.dataset.rectTop)+Number(o.dataset.rectHeight)},d=e.render(n,a.screenshot,c,s);d?(l.append(d),l.addEventListener("click",(()=>l.remove()))):l.remove()})))}static _computeZoomFactor(e,t){let n={x:t.width/e.width,y:t.height/e.height},r=.75*Math.min(n.x,n.y);return Math.min(1,r)}static render(t,n,r,i){if(!function(e,t){return t.left<=e.width&&0<=t.right&&t.top<=e.height&&0<=t.bottom}(n,r))return null;let a=t.createComponent("elementScreenshot"),o=t.find("div.lh-element-screenshot",a);o.dataset.rectWidth=r.width.toString(),o.dataset.rectHeight=r.height.toString(),o.dataset.rectLeft=r.left.toString(),o.dataset.rectTop=r.top.toString();let l=this._computeZoomFactor(r,i),s={width:i.width/l,height:i.height/l};s.width=Math.min(n.width,s.width),s.height=Math.min(n.height,s.height);let c=s.width*l,d=s.height*l,h=e.getScreenshotPositions(r,s,{width:n.width,height:n.height}),p=t.find("div.lh-element-screenshot__image",o);p.style.width=c+"px",p.style.height=d+"px",p.style.backgroundPositionY=-h.screenshot.top*l+"px",p.style.backgroundPositionX=-h.screenshot.left*l+"px",p.style.backgroundSize=`${n.width*l}px ${n.height*l}px`;let g=t.find("div.lh-element-screenshot__element-marker",o);g.style.width=r.width*l+"px",g.style.height=r.height*l+"px",g.style.left=h.clip.left*l+"px",g.style.top=h.clip.top*l+"px";let u=t.find("div.lh-element-screenshot__mask",o);return u.style.width=c+"px",u.style.height=d+"px",e.renderClipPathInScreenshot(t,u,h.clip,r,s),o}},v=["http://","https://","data:"],b=["bytes","numeric","ms","timespanMs"],_=class{constructor(e){"en-XA"===e&&(e="de"),this._locale=e,this._cachedNumberFormatters=new Map}_formatNumberWithGranularity(e,t,n={}){if(void 0!==t){let r=-Math.log10(t);Number.isInteger(r)||(console.warn(`granularity of ${t} is invalid. Using 1 instead`),t=1),t<1&&((n={...n}).minimumFractionDigits=n.maximumFractionDigits=Math.ceil(r)),e=Math.round(e/t)*t,Object.is(e,-0)&&(e=0)}else Math.abs(e)<5e-4&&(e=0);let r,i=[n.minimumFractionDigits,n.maximumFractionDigits,n.style,n.unit,n.unitDisplay,this._locale].join("");return r=this._cachedNumberFormatters.get(i),r||(r=new Intl.NumberFormat(this._locale,n),this._cachedNumberFormatters.set(i,r)),r.format(e).replace(" "," ")}formatNumber(e,t){return this._formatNumberWithGranularity(e,t)}formatInteger(e){return this._formatNumberWithGranularity(e,1)}formatPercent(e){return new Intl.NumberFormat(this._locale,{style:"percent"}).format(e)}formatBytesToKiB(e,t=void 0){return this._formatNumberWithGranularity(e/1024,t)+" KiB"}formatBytesToMiB(e,t=void 0){return this._formatNumberWithGranularity(e/1048576,t)+" MiB"}formatBytes(e,t=1){return this._formatNumberWithGranularity(e,t,{style:"unit",unit:"byte",unitDisplay:"long"})}formatBytesWithBestUnit(e,t=.1){return e>=1048576?this.formatBytesToMiB(e,t):e>=1024?this.formatBytesToKiB(e,t):this._formatNumberWithGranularity(e,t,{style:"unit",unit:"byte",unitDisplay:"narrow"})}formatKbps(e,t=void 0){return this._formatNumberWithGranularity(e,t,{style:"unit",unit:"kilobit-per-second",unitDisplay:"short"})}formatMilliseconds(e,t=void 0){return this._formatNumberWithGranularity(e,t,{style:"unit",unit:"millisecond",unitDisplay:"short"})}formatSeconds(e,t=void 0){return this._formatNumberWithGranularity(e/1e3,t,{style:"unit",unit:"second",unitDisplay:"narrow"})}formatDateTime(e){let t,n={month:"short",day:"numeric",year:"numeric",hour:"numeric",minute:"numeric",timeZoneName:"short"};try{t=new Intl.DateTimeFormat(this._locale,n)}catch{n.timeZone="UTC",t=new Intl.DateTimeFormat(this._locale,n)}return t.format(new Date(e))}formatDuration(e){let t=e/1e3;if(0===Math.round(t))return"None";let n=[],r={day:86400,hour:3600,minute:60,second:1};return Object.keys(r).forEach((e=>{let i=r[e],a=Math.floor(t/i);if(a>0){t-=a*i;let r=this._formatNumberWithGranularity(a,1,{style:"unit",unit:e,unitDisplay:"narrow"});n.push(r)}})),n.join(" ")}};function w(e,t,n){let r=e.find("div.lh-exp-gauge__wrapper",t);r.className="",r.classList.add("lh-exp-gauge__wrapper",`lh-exp-gauge__wrapper--${d.calculateRating(n.score)}`),function(e,t,n){let r=Number(n.score),{radiusInner:i,radiusOuter:a,circumferenceInner:o,circumferenceOuter:l,getArcLength:s,getMetricArcLength:c,endDiffInner:h,endDiffOuter:p,strokeWidth:g,strokeGap:u}=function(e,t,n){n=n||e/32;let r=e/n,i=.5*n,a=r+i+n,o=2*Math.PI*r,l=Math.acos(1-.5*Math.pow(.5*n/r,2))*r,s=2*Math.PI*a,c=Math.acos(1-.5*Math.pow(.5*n/a,2))*a;return{radiusInner:r,radiusOuter:a,circumferenceInner:o,circumferenceOuter:s,getArcLength:()=>Math.max(0,Number(t*o)),getMetricArcLength:(e,t=!1)=>{let n=t?0:2*c;return Math.max(0,Number(e*s-i-n))},endDiffInner:l,endDiffOuter:c,strokeWidth:n,strokeGap:i}}(128,r),m=e.find("svg.lh-exp-gauge",t);e.find(".lh-exp-gauge__label",m).textContent=n.title,m.setAttribute("viewBox",[-64,-32,128,64].join(" ")),m.style.setProperty("--stroke-width",`${g}px`),m.style.setProperty("--circle-meas",(2*Math.PI).toFixed(4));let f=e.find("g.lh-exp-gauge__outer",t),v=e.find("g.lh-exp-gauge__inner",t),b=e.find("circle.lh-cover",f),_=e.find("circle.lh-exp-gauge__arc",v),w=e.find("text.lh-exp-gauge__percentage",v);f.style.setProperty("--scale-initial",String(i/a)),f.style.setProperty("--radius",`${a}px`),b.style.setProperty("--radius",.5*(i+a)+"px"),b.setAttribute("stroke-width",String(u)),m.style.setProperty("--radius",`${i}px`),_.setAttribute("stroke-dasharray",`${s()} ${(o-s()).toFixed(4)}`),_.setAttribute("stroke-dashoffset",String(.25*o-h)),w.textContent=Math.round(100*r).toString();let y=a+g,x=a-g,k=n.auditRefs.filter((e=>"metrics"===e.group&&e.weight)),E=k.reduce(((e,t)=>e+t.weight),0),A=.25*l-p-.5*u,S=-.5*Math.PI;f.querySelectorAll(".metric").forEach((e=>{k.map((e=>`metric--${e.id}`)).find((t=>e.classList.contains(t)))||e.remove()})),k.forEach(((t,n)=>{let r=t.acronym??t.id,i=!f.querySelector(`.metric--${r}`),a=e.maybeFind(`g.metric--${r}`,f)||e.createSVGElement("g"),o=e.maybeFind(`.metric--${r} circle.lh-exp-gauge--faded`,f)||e.createSVGElement("circle"),s=e.maybeFind(`.metric--${r} circle.lh-exp-gauge--miniarc`,f)||e.createSVGElement("circle"),h=e.maybeFind(`.metric--${r} circle.lh-exp-gauge-hovertarget`,f)||e.createSVGElement("circle"),g=e.maybeFind(`.metric--${r} text.metric__label`,f)||e.createSVGElement("text"),u=e.maybeFind(`.metric--${r} text.metric__value`,f)||e.createSVGElement("text");a.classList.add("metric",`metric--${r}`),o.classList.add("lh-exp-gauge__arc","lh-exp-gauge__arc--metric","lh-exp-gauge--faded"),s.classList.add("lh-exp-gauge__arc","lh-exp-gauge__arc--metric","lh-exp-gauge--miniarc"),h.classList.add("lh-exp-gauge__arc","lh-exp-gauge__arc--metric","lh-exp-gauge-hovertarget");let m=t.weight/E,v=c(m),b=t.result.score?t.result.score*m:0,_=c(b),w=m*l,k=c(m,!0),z=d.calculateRating(t.result.score,t.result.scoreDisplayMode);a.style.setProperty("--metric-rating",z),a.style.setProperty("--metric-color",`var(--color-${z})`),a.style.setProperty("--metric-offset",`${A}`),a.style.setProperty("--i",n.toString()),o.setAttribute("stroke-dasharray",`${v} ${l-v}`),s.style.setProperty("--metric-array",`${_} ${l-_}`),h.setAttribute("stroke-dasharray",`${k} ${l-k-p}`),g.classList.add("metric__label"),u.classList.add("metric__value"),g.textContent=r,u.textContent=`+${Math.round(100*b)}`;let L=S+m*Math.PI,C=Math.cos(L),M=Math.sin(L);switch(!0){case C>0:u.setAttribute("text-anchor","end");break;case C<0:g.setAttribute("text-anchor","end");break;case 0===C:g.setAttribute("text-anchor","middle"),u.setAttribute("text-anchor","middle")}switch(!0){case M>0:g.setAttribute("dominant-baseline","hanging");break;case M<0:u.setAttribute("dominant-baseline","hanging");break;case 0===M:g.setAttribute("dominant-baseline","middle"),u.setAttribute("dominant-baseline","middle")}g.setAttribute("x",(y*C).toFixed(2)),g.setAttribute("y",(y*M).toFixed(2)),u.setAttribute("x",(x*C).toFixed(2)),u.setAttribute("y",(x*M).toFixed(2)),i&&(a.appendChild(o),a.appendChild(s),a.appendChild(h),a.appendChild(g),a.appendChild(u),f.appendChild(a)),A-=w,S+=2*m*Math.PI}));let z=f.querySelector(".lh-exp-gauge-underhovertarget")||e.createSVGElement("circle");z.classList.add("lh-exp-gauge__arc","lh-exp-gauge__arc--metric","lh-exp-gauge-hovertarget","lh-exp-gauge-underhovertarget");let L=c(1,!0);if(z.setAttribute("stroke-dasharray",`${L} ${l-L-p}`),z.isConnected||f.prepend(z),m.dataset.listenersSetup)return;async function C(t){if(await new Promise((e=>setTimeout(e,1e3))),t.classList.contains("state--expanded"))return;let n=e.find(".lh-exp-gauge__inner",t),r=`uniq-${Math.random()}`;n.setAttribute("id",r);let i=e.createSVGElement("use");i.setAttribute("href",`#${r}`),t.appendChild(i);let a=2.5;t.style.setProperty("--peek-dur",`${a}s`),t.classList.add("state--peek","state--expanded");let o=()=>{t.classList.remove("state--peek","state--expanded"),i.remove()},l=setTimeout((()=>{t.removeEventListener("mouseenter",s),o()}),1e3*a*1.5);function s(){clearTimeout(l),o()}t.addEventListener("mouseenter",s,{once:!0})}m.dataset.listenersSetup=!0,C(m),m.addEventListener("pointerover",(n=>{if(n.target===m&&m.classList.contains("state--expanded"))return m.classList.remove("state--expanded"),void(m.classList.contains("state--highlight")&&(m.classList.remove("state--highlight"),e.find(".metric--highlight",m).classList.remove("metric--highlight")));if(!(n.target instanceof Element))return;let r=n.target.parentNode;if(r instanceof SVGElement){if(r&&r===v)return void(m.classList.contains("state--expanded")?m.classList.contains("state--highlight")&&(m.classList.remove("state--highlight"),e.find(".metric--highlight",m).classList.remove("metric--highlight")):m.classList.add("state--expanded"));if(r&&r.classList&&r.classList.contains("metric")){let n=r.style.getPropertyValue("--metric-rating");if(t.style.setProperty("--color-highlight",`var(--color-${n}-secondary)`),m.classList.contains("state--highlight")){let t=e.find(".metric--highlight",m);r!==t&&(t.classList.remove("metric--highlight"),r.classList.add("metric--highlight"))}else m.classList.add("state--highlight"),r.classList.add("metric--highlight")}}})),m.addEventListener("mouseleave",(()=>{m.classList.remove("state--highlight"),m.querySelector(".metric--highlight")?.classList.remove("metric--highlight")}))}(e,r,n)}var y=class extends p{_renderMetric(e){let t=this.dom.createComponent("metric"),n=this.dom.find(".lh-metric",t);n.id=e.result.id;let r=d.calculateRating(e.result.score,e.result.scoreDisplayMode);n.classList.add(`lh-metric--${r}`),this.dom.find(".lh-metric__title",t).textContent=e.result.title;let i=this.dom.find(".lh-metric__value",t);i.textContent=e.result.displayValue||"";let a=this.dom.find(".lh-metric__description",t);if(a.append(this.dom.convertMarkdownLinkSnippets(e.result.description)),"error"===e.result.scoreDisplayMode){a.textContent="",i.textContent="Error!",this.dom.createChildOf(a,"span").textContent=e.result.errorMessage||"Report error: no metric information"}else"notApplicable"===e.result.scoreDisplayMode&&(i.textContent="--");return n}_getScoringCalculatorHref(e){let t=e.filter((e=>"metrics"===e.group)),n=e.find((e=>"interactive"===e.id)),r=e.find((e=>"first-cpu-idle"===e.id)),i=e.find((e=>"first-meaningful-paint"===e.id));n&&t.push(n),r&&t.push(r),i&&"number"==typeof i.result.score&&t.push(i);let a=[...t.map((e=>{let t;return"number"==typeof e.result.numericValue?(t="cumulative-layout-shift"===e.id?(e=>Math.round(100*e)/100)(e.result.numericValue):Math.round(e.result.numericValue),t=t.toString()):t="null",[e.acronym||e.id,t]}))];l.reportJson&&(a.push(["device",l.reportJson.configSettings.formFactor]),a.push(["version",l.reportJson.lighthouseVersion]));let o=new URLSearchParams(a),s=new URL("https://googlechrome.github.io/lighthouse/scorecalc/");return s.hash=o.toString(),s.href}overallImpact(e,t){if(!e.result.metricSavings)return{overallImpact:0,overallLinearImpact:0};let n=0,r=0;for(let[a,o]of Object.entries(e.result.metricSavings)){if(void 0===o)continue;let e=t.find((e=>e.acronym===a));if(!e||null===e.result.score)continue;let l=e.result.numericValue;if(!l)continue;r+=o/l*e.weight;let s=e.result.scoringOptions;s&&(n+=(i.computeLogNormalScore(s,l-o)-e.result.score)*e.weight)}return{overallImpact:n,overallLinearImpact:r}}render(e,t,n){let r=l.strings,i=this.dom.createElement("div","lh-category");i.id=e.id,i.append(this.renderCategoryHeader(e,t,n));let a=e.auditRefs.filter((e=>"metrics"===e.group));if(a.length){let[n,o]=this.renderAuditGroup(t.metrics),s=this.dom.createElement("input","lh-metrics-toggle__input"),c=`lh-metrics-toggle${l.getUniqueSuffix()}`;s.setAttribute("aria-label","Toggle the display of metric descriptions"),s.type="checkbox",s.id=c,n.prepend(s);let d=this.dom.find(".lh-audit-group__header",n),h=this.dom.createChildOf(d,"label","lh-metrics-toggle__label");h.htmlFor=c;let p=this.dom.createChildOf(h,"span","lh-metrics-toggle__labeltext--show"),g=this.dom.createChildOf(h,"span","lh-metrics-toggle__labeltext--hide");p.textContent=l.strings.expandView,g.textContent=l.strings.collapseView;let u=this.dom.createElement("div","lh-metrics-container");if(n.insertBefore(u,o),a.forEach((e=>{u.append(this._renderMetric(e))})),i.querySelector(".lh-gauge__wrapper")){let t=this.dom.find(".lh-category-header__description",i),n=this.dom.createChildOf(t,"div","lh-metrics__disclaimer"),a=this.dom.convertMarkdownLinkSnippets(r.varianceDisclaimer);n.append(a);let o=this.dom.createChildOf(n,"a","lh-calclink");o.target="_blank",o.textContent=r.calculatorLink,this.dom.safelySetHref(o,this._getScoringCalculatorHref(e.auditRefs))}n.classList.add("lh-audit-group--metrics"),i.append(n)}let o=this.dom.createChildOf(i,"div","lh-filmstrip-container"),s=e.auditRefs.find((e=>"screenshot-thumbnails"===e.id))?.result;if(s?.details){o.id=s.id;let e=this.detailsRenderer.render(s.details);e&&o.append(e)}let c=e.auditRefs.filter((e=>"diagnostics"===e.group)).map((e=>{let{overallImpact:t,overallLinearImpact:n}=this.overallImpact(e,a),r=e.result.guidanceLevel||1;return{auditRef:e,auditEl:this.renderAudit(e),overallImpact:t,overallLinearImpact:n,guidanceLevel:r}})),h=c.filter((e=>!d.showAsPassed(e.auditRef.result))),p=c.filter((e=>d.showAsPassed(e.auditRef.result))),[g,u]=this.renderAuditGroup(t.diagnostics);function m(e){for(let t of c)if("All"===e)t.auditEl.hidden=!1;else{let n=void 0===t.auditRef.result.metricSavings?.[e];t.auditEl.hidden=n}h.sort(((t,n)=>{let r=t.auditRef.result.score||0,i=n.auditRef.result.score||0;if(r!==i)return r-i;if("All"!==e){let r=t.auditRef.result.metricSavings?.[e]??-1,i=n.auditRef.result.metricSavings?.[e]??-1;if(r!==i)return i-r}return t.overallImpact!==n.overallImpact?n.overallImpact*n.guidanceLevel-t.overallImpact*t.guidanceLevel:0===t.overallImpact&&0===n.overallImpact&&t.overallLinearImpact!==n.overallLinearImpact?n.overallLinearImpact*n.guidanceLevel-t.overallLinearImpact*t.guidanceLevel:n.guidanceLevel-t.guidanceLevel}));for(let e of h)g.insertBefore(e.auditEl,u)}g.classList.add("lh-audit-group--diagnostics");let f=new Set;for(let e of h){let t=e.auditRef.result.metricSavings||{};for(let[e,n]of Object.entries(t))"number"==typeof n&&f.add(e)}let v=a.filter((e=>e.acronym&&f.has(e.acronym)));if(v.length&&this.renderMetricAuditFilter(v,i,m),m("All"),h.length&&i.append(g),!p.length)return i;let b={auditRefsOrEls:p.map((e=>e.auditEl)),groupDefinitions:t},_=this.renderClump("passed",b);if(i.append(_),(!n||"navigation"===n?.gatherMode)&&null!==e.score){let t=function(e){let t=e.createComponent("explodeyGauge");return e.find(".lh-exp-gauge-component",t)}(this.dom);w(this.dom,t,e),this.dom.find(".lh-score__gauge",i).replaceWith(t)}return i}renderMetricAuditFilter(e,t,n){let r=this.dom.createElement("div","lh-metricfilter");this.dom.createChildOf(r,"span","lh-metricfilter__text").textContent=l.strings.showRelevantAudits;let i=[{acronym:"All",id:"All"},...e],a=l.getUniqueSuffix();for(let e of i){let i=`metric-${e.acronym}-${a}`,o=this.dom.createChildOf(r,"input","lh-metricfilter__radio");o.type="radio",o.name=`metricsfilter-${a}`,o.id=i;let l=this.dom.createChildOf(r,"label","lh-metricfilter__label");l.htmlFor=i,l.title="result"in e?e.result.title:"",l.textContent=e.acronym||e.id,"All"===e.acronym&&(o.checked=!0,l.classList.add("lh-metricfilter__label--active")),t.append(r),o.addEventListener("input",(r=>{for(let e of t.querySelectorAll("label.lh-metricfilter__label"))e.classList.toggle("lh-metricfilter__label--active",e.htmlFor===i);t.classList.toggle("lh-category--filtered","All"!==e.acronym),n(e.acronym||"All");let a=t.querySelectorAll("div.lh-audit-group, details.lh-audit-group");for(let e of a){e.hidden=!1;let t=Array.from(e.querySelectorAll("div.lh-audit")),n=!!t.length&&t.every((e=>e.hidden));e.hidden=n}}))}}},x=class{constructor(e){this._dom=e,this._opts={}}renderReport(e,t,n){if(!this._dom.rootEl&&t){console.warn("Please adopt the new report API in renderer/api.js.");let e=t.closest(".lh-root");e?this._dom.rootEl=e:(t.classList.add("lh-root","lh-vars"),this._dom.rootEl=t)}else this._dom.rootEl&&t&&(this._dom.rootEl=t);n&&(this._opts=n),this._dom.setLighthouseChannel(e.configSettings.channel||"unknown");let r=d.prepareReportResult(e);return this._dom.rootEl.textContent="",this._dom.rootEl.append(this._renderReport(r)),this._opts.occupyEntireViewport&&this._dom.rootEl.classList.add("lh-max-viewport"),this._dom.rootEl}_renderReportTopbar(e){let t=this._dom.createComponent("topbar"),n=this._dom.find("a.lh-topbar__url",t);return n.textContent=e.finalDisplayedUrl,n.title=e.finalDisplayedUrl,this._dom.safelySetHref(n,e.finalDisplayedUrl),t}_renderReportHeader(){let e=this._dom.createComponent("heading"),t=this._dom.createComponent("scoresWrapper");return this._dom.find(".lh-scores-wrapper-placeholder",e).replaceWith(t),e}_renderReportFooter(e){let t=this._dom.createComponent("footer");return this._renderMetaBlock(e,t),this._dom.find(".lh-footer__version_issue",t).textContent=l.strings.footerIssue,this._dom.find(".lh-footer__version",t).textContent=e.lighthouseVersion,t}_renderMetaBlock(e,t){let n=d.getEmulationDescriptions(e.configSettings||{}),r=e.userAgent.match(/(\w*Chrome\/[\d.]+)/),i=Array.isArray(r)?r[1].replace("/"," ").replace("Chrome","Chromium"):"Chromium",a=e.configSettings.channel,o=e.environment.benchmarkIndex.toFixed(0),s=e.environment.credits?.["axe-core"],c=[`${l.strings.runtimeSettingsBenchmark}: ${o}`,`${l.strings.runtimeSettingsCPUThrottling}: ${n.cpuThrottling}`];n.screenEmulation&&c.push(`${l.strings.runtimeSettingsScreenEmulation}: ${n.screenEmulation}`),s&&c.push(`${l.strings.runtimeSettingsAxeVersion}: ${s}`);let h=l.strings.runtimeAnalysisWindow;"timespan"===e.gatherMode?h=l.strings.runtimeAnalysisWindowTimespan:"snapshot"===e.gatherMode&&(h=l.strings.runtimeAnalysisWindowSnapshot);let p=[["date",`Captured at ${l.i18n.formatDateTime(e.fetchTime)}`],["devices",`${n.deviceEmulation} with Lighthouse ${e.lighthouseVersion}`,c.join("\n")],["samples-one",l.strings.runtimeSingleLoad,l.strings.runtimeSingleLoadTooltip],["stopwatch",h],["networkspeed",`${n.summary}`,`${l.strings.runtimeSettingsNetworkThrottling}: ${n.networkThrottling}`],["chrome",`Using ${i}`+(a?` with ${a}`:""),`${l.strings.runtimeSettingsUANetwork}: "${e.environment.networkUserAgent}"`]],g=this._dom.find(".lh-meta__items",t);for(let[e,t,n]of p){let r=this._dom.createChildOf(g,"li","lh-meta__item");if(r.textContent=t,n){r.classList.add("lh-tooltip-boundary"),this._dom.createChildOf(r,"div","lh-tooltip").textContent=n}r.classList.add("lh-report-icon",`lh-report-icon--${e}`)}}_renderReportWarnings(e){if(!e.runWarnings||0===e.runWarnings.length)return this._dom.createElement("div");let t=this._dom.createComponent("warningsToplevel");this._dom.find(".lh-warnings__msg",t).textContent=l.strings.toplevelWarningsMessage;let n=[];for(let t of e.runWarnings){let e=this._dom.createElement("li");e.append(this._dom.convertMarkdownLinkSnippets(t)),n.push(e)}return this._dom.find("ul",t).append(...n),t}_renderScoreGauges(e,t,n){let r=[],i=[];for(let a of Object.values(e.categories)){let o=(n[a.id]||t).renderCategoryScore(a,e.categoryGroups||{},{gatherMode:e.gatherMode}),l=this._dom.find("a.lh-gauge__wrapper, a.lh-fraction__wrapper",o);l&&(this._dom.safelySetHref(l,`#${a.id}`),l.addEventListener("click",(e=>{if(!l.matches('[href^="#"]'))return;let t=l.getAttribute("href"),n=this._dom.rootEl;if(!t||!n)return;let r=this._dom.find(t,n);e.preventDefault(),r.scrollIntoView()})),this._opts.onPageAnchorRendered?.(l)),d.isPluginCategory(a.id)?i.push(o):r.push(o)}return[...r,...i]}_renderReport(e){l.apply({providedStrings:e.i18n.rendererFormattedStrings,i18n:new _(e.configSettings.locale),reportJson:e});let t=new class{constructor(e,t={}){this._dom=e,this._fullPageScreenshot=t.fullPageScreenshot,this._entities=t.entities}render(e){switch(e.type){case"filmstrip":return this._renderFilmstrip(e);case"list":return this._renderList(e);case"table":case"opportunity":return this._renderTable(e);case"criticalrequestchain":return g.render(this._dom,e,this);case"screenshot":case"debugdata":case"treemap-data":return null;default:return this._renderUnknown(e.type,e)}}_renderBytes(e){let t=l.i18n.formatBytesToKiB(e.value,e.granularity||.1),n=this._renderText(t);return n.title=l.i18n.formatBytes(e.value),n}_renderMilliseconds(e){let t;return t="duration"===e.displayUnit?l.i18n.formatDuration(e.value):l.i18n.formatMilliseconds(e.value,e.granularity||10),this._renderText(t)}renderTextURL(e){let t,n,r,a=e;try{let e=i.parseURL(a);t="/"===e.file?e.origin:e.file,n="/"===e.file||""===e.hostname?"":`(${e.hostname})`,r=a}catch{t=a}let o=this._dom.createElement("div","lh-text__url");if(o.append(this._renderLink({text:t,url:a})),n){let e=this._renderText(n);e.classList.add("lh-text__url-host"),o.append(e)}return r&&(o.title=a,o.dataset.url=a),o}_renderLink(e){let t=this._dom.createElement("a");if(this._dom.safelySetHref(t,e.url),!t.href){let t=this._renderText(e.text);return t.classList.add("lh-link"),t}return t.rel="noopener",t.target="_blank",t.textContent=e.text,t.classList.add("lh-link"),t}_renderText(e){let t=this._dom.createElement("div","lh-text");return t.textContent=e,t}_renderNumeric(e){let t=l.i18n.formatNumber(e.value,e.granularity||.1),n=this._dom.createElement("div","lh-numeric");return n.textContent=t,n}_renderThumbnail(e){let t=this._dom.createElement("img","lh-thumbnail"),n=e;return t.src=n,t.title=n,t.alt="",t}_renderUnknown(e,t){console.error(`Unknown details type: ${e}`,t);let n=this._dom.createElement("details","lh-unknown");return this._dom.createChildOf(n,"summary").textContent=`We don't know how to render audit details of type \`${e}\`. The Lighthouse version that collected this data is likely newer than the Lighthouse version of the report renderer. Expand for the raw JSON.`,this._dom.createChildOf(n,"pre").textContent=JSON.stringify(t,null,2),n}_renderTableValue(e,t){if(null==e)return null;if("object"==typeof e)switch(e.type){case"code":return this._renderCode(e.value);case"link":return this._renderLink(e);case"node":return this.renderNode(e);case"numeric":return this._renderNumeric(e);case"source-location":return this.renderSourceLocation(e);case"url":return this.renderTextURL(e.value);default:return this._renderUnknown(e.type,e)}switch(t.valueType){case"bytes":{let n=Number(e);return this._renderBytes({value:n,granularity:t.granularity})}case"code":{let t=String(e);return this._renderCode(t)}case"ms":{let n={value:Number(e),granularity:t.granularity,displayUnit:t.displayUnit};return this._renderMilliseconds(n)}case"numeric":{let n=Number(e);return this._renderNumeric({value:n,granularity:t.granularity})}case"text":{let t=String(e);return this._renderText(t)}case"thumbnail":{let t=String(e);return this._renderThumbnail(t)}case"timespanMs":{let t=Number(e);return this._renderMilliseconds({value:t})}case"url":{let t=String(e);return v.some((e=>t.startsWith(e)))?this.renderTextURL(t):this._renderCode(t)}default:return this._renderUnknown(t.valueType,e)}}_getDerivedSubItemsHeading(e){return e.subItemsHeading?{key:e.subItemsHeading.key||"",valueType:e.subItemsHeading.valueType||e.valueType,granularity:e.subItemsHeading.granularity||e.granularity,displayUnit:e.subItemsHeading.displayUnit||e.displayUnit,label:""}:null}_renderTableRow(e,t){let n=this._dom.createElement("tr");for(let r of t){if(!r||!r.key){this._dom.createChildOf(n,"td","lh-table-column--empty");continue}let t,i=e[r.key];if(null!=i&&(t=this._renderTableValue(i,r)),t){let e=`lh-table-column--${r.valueType}`;this._dom.createChildOf(n,"td",e).append(t)}else this._dom.createChildOf(n,"td","lh-table-column--empty")}return n}_renderTableRowsFromItem(e,t){let n=this._dom.createFragment();if(n.append(this._renderTableRow(e,t)),!e.subItems)return n;let r=t.map(this._getDerivedSubItemsHeading);if(!r.some(Boolean))return n;for(let t of e.subItems.items){let e=this._renderTableRow(t,r);e.classList.add("lh-sub-item-row"),n.append(e)}return n}_adornEntityGroupRow(e){let t=e.dataset.entity;if(!t)return;let n=this._entities?.find((e=>e.name===t));if(!n)return;let r=this._dom.find("td",e);if(n.category){let e=this._dom.createElement("span");e.classList.add("lh-audit__adorn"),e.textContent=n.category,r.append(" ",e)}if(n.isFirstParty){let e=this._dom.createElement("span");e.classList.add("lh-audit__adorn","lh-audit__adorn1p"),e.textContent=l.strings.firstPartyChipLabel,r.append(" ",e)}if(n.homepage){let e=this._dom.createElement("a");e.href=n.homepage,e.target="_blank",e.title=l.strings.openInANewTabTooltip,e.classList.add("lh-report-icon--external"),r.append(" ",e)}}_renderEntityGroupRow(e,t){let n={...t[0]};n.valueType="text";let r=[n,...t.slice(1)],i=this._dom.createFragment();return i.append(this._renderTableRow(e,r)),this._dom.find("tr",i).classList.add("lh-row--group"),i}_getEntityGroupItems(e){let{items:t,headings:n,sortedBy:r}=e;if(!t.length||e.isEntityGrouped||!t.some((e=>e.entity)))return[];let i=new Set(e.skipSumming||[]),a=[];for(let e of n)!e.key||i.has(e.key)||b.includes(e.valueType)&&a.push(e.key);let o=n[0].key;if(!o)return[];let s=new Map;for(let e of t){let t="string"==typeof e.entity?e.entity:void 0,n=s.get(t)||{[o]:t||l.strings.unattributable,entity:t};for(let t of a)n[t]=Number(n[t]||0)+Number(e[t]||0);s.set(t,n)}let c=[...s.values()];return r&&c.sort(d.getTableItemSortComparator(r)),c}_renderTable(e){if(!e.items.length)return this._dom.createElement("span");let t=this._dom.createElement("table","lh-table"),n=this._dom.createChildOf(t,"thead"),r=this._dom.createChildOf(n,"tr");for(let t of e.headings){let e=`lh-table-column--${t.valueType||"text"}`,n=this._dom.createElement("div","lh-text");n.textContent=t.label,this._dom.createChildOf(r,"th",e).append(n)}let i=this._getEntityGroupItems(e),a=this._dom.createChildOf(t,"tbody");if(i.length)for(let t of i){let n="string"==typeof t.entity?t.entity:void 0,r=this._renderEntityGroupRow(t,e.headings);for(let t of e.items.filter((e=>e.entity===n)))r.append(this._renderTableRowsFromItem(t,e.headings));let i=this._dom.findAll("tr",r);n&&i.length&&(i.forEach((e=>e.dataset.entity=n)),this._adornEntityGroupRow(i[0])),a.append(r)}else{let t=!0;for(let n of e.items){let r=this._renderTableRowsFromItem(n,e.headings),i=this._dom.findAll("tr",r),o=i[0];if("string"==typeof n.entity&&(o.dataset.entity=n.entity),e.isEntityGrouped&&n.entity)o.classList.add("lh-row--group"),this._adornEntityGroupRow(o);else for(let e of i)e.classList.add(t?"lh-row--even":"lh-row--odd");t=!t,a.append(r)}}return t}_renderList(e){let t=this._dom.createElement("div","lh-list");return e.items.forEach((e=>{let n=this.render(e);n&&t.append(n)})),t}renderNode(e){let t=this._dom.createElement("span","lh-node");if(e.nodeLabel){let n=this._dom.createElement("div");n.textContent=e.nodeLabel,t.append(n)}if(e.snippet){let n=this._dom.createElement("div");n.classList.add("lh-node__snippet"),n.textContent=e.snippet,t.append(n)}if(e.selector&&(t.title=e.selector),e.path&&t.setAttribute("data-path",e.path),e.selector&&t.setAttribute("data-selector",e.selector),e.snippet&&t.setAttribute("data-snippet",e.snippet),!this._fullPageScreenshot)return t;let n=e.lhId&&this._fullPageScreenshot.nodes[e.lhId];if(!n||0===n.width||0===n.height)return t;let r=f.render(this._dom,this._fullPageScreenshot.screenshot,n,{width:147,height:100});return r&&t.prepend(r),t}renderSourceLocation(e){if(!e.url)return null;let t,n,r=`${e.url}:${e.line+1}:${e.column}`;if(e.original&&(t=`${e.original.file||"<unmapped>"}:${e.original.line+1}:${e.original.column}`),"network"===e.urlProvider&&t)n=this._renderLink({url:e.url,text:t}),n.title=`maps to generated location ${r}`;else if("network"!==e.urlProvider||t)if("comment"===e.urlProvider&&t)n=this._renderText(`${t} (from source map)`),n.title=`${r} (from sourceURL)`;else{if("comment"!==e.urlProvider||t)return null;n=this._renderText(`${r} (from sourceURL)`)}else n=this.renderTextURL(e.url),this._dom.find(".lh-link",n).textContent+=`:${e.line+1}:${e.column}`;return n.classList.add("lh-source-location"),n.setAttribute("data-source-url",e.url),n.setAttribute("data-source-line",String(e.line)),n.setAttribute("data-source-column",String(e.column)),n}_renderFilmstrip(e){let t=this._dom.createElement("div","lh-filmstrip");for(let n of e.items){let e=this._dom.createChildOf(t,"div","lh-filmstrip__frame"),r=this._dom.createChildOf(e,"img","lh-filmstrip__thumbnail");r.src=n.data,r.alt="Screenshot"}return t}_renderCode(e){let t=this._dom.createElement("pre","lh-code");return t.textContent=e,t}}(this._dom,{fullPageScreenshot:e.fullPageScreenshot??void 0,entities:e.entities}),n=new p(this._dom,t),r={performance:new y(this._dom,t)},a=this._dom.createElement("div");a.append(this._renderReportHeader());let o,s=this._dom.createElement("div","lh-container"),c=this._dom.createElement("div","lh-report");c.append(this._renderReportWarnings(e)),1===Object.keys(e.categories).length?a.classList.add("lh-header--solo-category"):o=this._dom.createElement("div","lh-scores-header");let h=this._dom.createElement("div");if(h.classList.add("lh-scorescale-wrap"),h.append(this._dom.createComponent("scorescale")),o){let t=this._dom.find(".lh-scores-container",a);o.append(...this._renderScoreGauges(e,n,r)),t.append(o,h);let i=this._dom.createElement("div","lh-sticky-header");i.append(...this._renderScoreGauges(e,n,r)),s.append(i)}let u=this._dom.createElement("div","lh-categories");c.append(u);let m={gatherMode:e.gatherMode};for(let t of Object.values(e.categories)){let i=r[t.id]||n;i.dom.createChildOf(u,"div","lh-category-wrapper").append(i.render(t,e.categoryGroups,m))}n.injectFinalScreenshot(u,e.audits,h);let w=this._dom.createFragment();return this._opts.omitGlobalStyles||w.append(this._dom.createComponent("styles")),this._opts.omitTopbar||w.append(this._renderReportTopbar(e)),w.append(s),c.append(this._renderReportFooter(e)),s.append(a,c),e.fullPageScreenshot&&f.installFullPageScreenshot(this._dom.rootEl,e.fullPageScreenshot.screenshot),w}};function k(e,t){let n=e.rootEl;typeof t>"u"?n.classList.toggle("lh-dark"):n.classList.toggle("lh-dark",t)}var E=typeof btoa<"u"?btoa:e=>Buffer.from(e).toString("base64"),A=typeof atob<"u"?atob:e=>Buffer.from(e,"base64").toString();var S={toBase64:async function(e,t){let n=(new TextEncoder).encode(e);if(t.gzip)if(typeof CompressionStream<"u"){let e=new CompressionStream("gzip"),t=e.writable.getWriter();t.write(n),t.close();let r=await new Response(e.readable).arrayBuffer();n=new Uint8Array(r)}else n=window.pako.gzip(e);let r="";for(let e=0;e<n.length;e+=5e3)r+=String.fromCharCode(...n.subarray(e,e+5e3));return E(r)},fromBase64:function(e,t){let n=A(e),r=Uint8Array.from(n,(e=>e.charCodeAt(0)));return t.gzip?window.pako.ungzip(r,{to:"string"}):(new TextDecoder).decode(r)}};function z(){let e=window.location.host.endsWith(".vercel.app"),t=new URLSearchParams(window.location.search).has("dev");return e?`https://${window.location.host}/gh-pages`:t?"http://localhost:7333":"https://googlechrome.github.io/lighthouse"}function L(e){let t=e.generatedTime,n=e.fetchTime||t;return`${e.lighthouseVersion}-${e.finalDisplayedUrl}-${n}`}async function C(e,t,n){let r=new URL(t),i=!!window.CompressionStream;r.hash=await S.toBase64(JSON.stringify(e),{gzip:i}),i&&r.searchParams.set("gzip","1"),window.open(r.toString(),n)}async function M(e){let t="viewer-"+L(e);!function(e,t,n){let r=new URL(t).origin;window.addEventListener("message",(function t(n){n.origin===r&&i&&n.data.opened&&(i.postMessage(e,r),window.removeEventListener("message",t))}));let i=window.open(t,n)}({lhr:e},z()+"/viewer/",t)}function T(e){return function(e,t){let n=t?new Date(t):new Date,r=n.toLocaleTimeString("en-US",{hour12:!1}),i=n.toLocaleDateString("en-US",{year:"numeric",month:"2-digit",day:"2-digit"}).split("/");return i.unshift(i.pop()),`${e}_${i.join("-")}_${r}`.replace(/[/?<>\\:*|"]/g,"-")}(new URL(e.finalDisplayedUrl).hostname,e.fetchTime)}var D=class{constructor(e,t={}){this.json,this._dom=e,this._opts=t,this._topbar=t.omitTopbar?null:new class{constructor(e,t){this.lhr,this._reportUIFeatures=e,this._dom=t,this._dropDownMenu=new class{constructor(e){this._dom=e,this._toggleEl,this._menuEl,this.onDocumentKeyDown=this.onDocumentKeyDown.bind(this),this.onToggleClick=this.onToggleClick.bind(this),this.onToggleKeydown=this.onToggleKeydown.bind(this),this.onMenuFocusOut=this.onMenuFocusOut.bind(this),this.onMenuKeydown=this.onMenuKeydown.bind(this),this._getNextMenuItem=this._getNextMenuItem.bind(this),this._getNextSelectableNode=this._getNextSelectableNode.bind(this),this._getPreviousMenuItem=this._getPreviousMenuItem.bind(this)}setup(e){this._toggleEl=this._dom.find(".lh-topbar button.lh-tools__button",this._dom.rootEl),this._toggleEl.addEventListener("click",this.onToggleClick),this._toggleEl.addEventListener("keydown",this.onToggleKeydown),this._menuEl=this._dom.find(".lh-topbar div.lh-tools__dropdown",this._dom.rootEl),this._menuEl.addEventListener("keydown",this.onMenuKeydown),this._menuEl.addEventListener("click",e)}close(){this._toggleEl.classList.remove("lh-active"),this._toggleEl.setAttribute("aria-expanded","false"),this._menuEl.contains(this._dom.document().activeElement)&&this._toggleEl.focus(),this._menuEl.removeEventListener("focusout",this.onMenuFocusOut),this._dom.document().removeEventListener("keydown",this.onDocumentKeyDown)}open(e){this._toggleEl.classList.contains("lh-active")?e.focus():this._menuEl.addEventListener("transitionend",(()=>{e.focus()}),{once:!0}),this._toggleEl.classList.add("lh-active"),this._toggleEl.setAttribute("aria-expanded","true"),this._menuEl.addEventListener("focusout",this.onMenuFocusOut),this._dom.document().addEventListener("keydown",this.onDocumentKeyDown)}onToggleClick(e){e.preventDefault(),e.stopImmediatePropagation(),this._toggleEl.classList.contains("lh-active")?this.close():this.open(this._getNextMenuItem())}onToggleKeydown(e){switch(e.code){case"ArrowUp":e.preventDefault(),this.open(this._getPreviousMenuItem());break;case"ArrowDown":case"Enter":case" ":e.preventDefault(),this.open(this._getNextMenuItem())}}onMenuKeydown(e){let t=e.target;switch(e.code){case"ArrowUp":e.preventDefault(),this._getPreviousMenuItem(t).focus();break;case"ArrowDown":e.preventDefault(),this._getNextMenuItem(t).focus();break;case"Home":e.preventDefault(),this._getNextMenuItem().focus();break;case"End":e.preventDefault(),this._getPreviousMenuItem().focus()}}onDocumentKeyDown(e){27===e.keyCode&&this.close()}onMenuFocusOut(e){let t=e.relatedTarget;this._menuEl.contains(t)||this.close()}_getNextSelectableNode(e,t){let n=e.filter((e=>!(!(e instanceof HTMLElement)||e.hasAttribute("disabled")||"none"===window.getComputedStyle(e).display))),r=t?n.indexOf(t)+1:0;return r>=n.length&&(r=0),n[r]}_getNextMenuItem(e){let t=Array.from(this._menuEl.childNodes);return this._getNextSelectableNode(t,e)}_getPreviousMenuItem(e){let t=Array.from(this._menuEl.childNodes).reverse();return this._getNextSelectableNode(t,e)}}(this._dom),this._copyAttempt=!1,this.topbarEl,this.categoriesEl,this.stickyHeaderEl,this.highlightEl,this.onDropDownMenuClick=this.onDropDownMenuClick.bind(this),this.onKeyUp=this.onKeyUp.bind(this),this.onCopy=this.onCopy.bind(this),this.collapseAllDetails=this.collapseAllDetails.bind(this)}enable(e){this.lhr=e,this._dom.rootEl.addEventListener("keyup",this.onKeyUp),this._dom.document().addEventListener("copy",this.onCopy),this._dropDownMenu.setup(this.onDropDownMenuClick),this._setUpCollapseDetailsAfterPrinting(),this._dom.find(".lh-topbar__logo",this._dom.rootEl).addEventListener("click",(()=>k(this._dom))),this._setupStickyHeader()}onDropDownMenuClick(e){e.preventDefault();let t=e.target;if(t&&t.hasAttribute("data-action")){switch(t.getAttribute("data-action")){case"copy":this.onCopyButtonClick();break;case"print-summary":this.collapseAllDetails(),this._print();break;case"print-expanded":this.expandAllDetails(),this._print();break;case"save-json":{let e=JSON.stringify(this.lhr,null,2);this._reportUIFeatures._saveFile(new Blob([e],{type:"application/json"}));break}case"save-html":{let e=this._reportUIFeatures.getReportHtml();try{this._reportUIFeatures._saveFile(new Blob([e],{type:"text/html"}))}catch(e){this._dom.fireEventOn("lh-log",this._dom.document(),{cmd:"error",msg:"Could not export as HTML. "+e.message})}break}case"open-viewer":this._dom.isDevTools()?async function(e){let t="viewer-"+L(e),n=z()+"/viewer/";await C({lhr:e},n,t)}(this.lhr):M(this.lhr);break;case"save-gist":this._reportUIFeatures.saveAsGist();break;case"toggle-dark":k(this._dom);break;case"view-unthrottled-trace":this._reportUIFeatures._opts.onViewTrace?.()}this._dropDownMenu.close()}}onCopy(e){this._copyAttempt&&e.clipboardData&&(e.preventDefault(),e.clipboardData.setData("text/plain",JSON.stringify(this.lhr,null,2)),this._dom.fireEventOn("lh-log",this._dom.document(),{cmd:"log",msg:"Report JSON copied to clipboard"})),this._copyAttempt=!1}onCopyButtonClick(){this._dom.fireEventOn("lh-analytics",this._dom.document(),{cmd:"send",fields:{hitType:"event",eventCategory:"report",eventAction:"copy"}});try{this._dom.document().queryCommandSupported("copy")&&(this._copyAttempt=!0,this._dom.document().execCommand("copy")||(this._copyAttempt=!1,this._dom.fireEventOn("lh-log",this._dom.document(),{cmd:"warn",msg:"Your browser does not support copy to clipboard."})))}catch(e){this._copyAttempt=!1,this._dom.fireEventOn("lh-log",this._dom.document(),{cmd:"log",msg:e.message})}}onKeyUp(e){(e.ctrlKey||e.metaKey)&&80===e.keyCode&&this._dropDownMenu.close()}expandAllDetails(){this._dom.findAll(".lh-categories details",this._dom.rootEl).map((e=>e.open=!0))}collapseAllDetails(){this._dom.findAll(".lh-categories details",this._dom.rootEl).map((e=>e.open=!1))}_print(){this._reportUIFeatures._opts.onPrintOverride?this._reportUIFeatures._opts.onPrintOverride(this._dom.rootEl):self.print()}resetUIState(){this._dropDownMenu.close()}_getScrollParent(e){let{overflowY:t}=window.getComputedStyle(e);return"visible"!==t&&"hidden"!==t?e:e.parentElement?this._getScrollParent(e.parentElement):document}_setUpCollapseDetailsAfterPrinting(){"onbeforeprint"in self?self.addEventListener("afterprint",this.collapseAllDetails):self.matchMedia("print").addListener((e=>{e.matches?this.expandAllDetails():this.collapseAllDetails()}))}_setupStickyHeader(){this.topbarEl=this._dom.find("div.lh-topbar",this._dom.rootEl),this.categoriesEl=this._dom.find("div.lh-categories",this._dom.rootEl),window.requestAnimationFrame((()=>window.requestAnimationFrame((()=>{try{this.stickyHeaderEl=this._dom.find("div.lh-sticky-header",this._dom.rootEl)}catch{return}this.highlightEl=this._dom.createChildOf(this.stickyHeaderEl,"div","lh-highlighter");let e=this._getScrollParent(this._dom.find(".lh-container",this._dom.rootEl));e.addEventListener("scroll",(()=>this._updateStickyHeader()));let t=e instanceof window.Document?document.documentElement:e;new window.ResizeObserver((()=>this._updateStickyHeader())).observe(t)}))))}_updateStickyHeader(){if(!this.stickyHeaderEl)return;let e=this.topbarEl.getBoundingClientRect().bottom>=this.categoriesEl.getBoundingClientRect().top,t=Array.from(this._dom.rootEl.querySelectorAll(".lh-category")).filter((e=>e.getBoundingClientRect().top-window.innerHeight/2<0)),n=t.length>0?t.length-1:0,r=this.stickyHeaderEl.querySelectorAll(".lh-gauge__wrapper, .lh-fraction__wrapper"),i=r[n],a=r[0].getBoundingClientRect().left,o=i.getBoundingClientRect().left-a;this.highlightEl.style.transform=`translate(${o}px)`,this.stickyHeaderEl.classList.toggle("lh-sticky-header--visible",e)}}(this,e),this.onMediaQueryChange=this.onMediaQueryChange.bind(this)}initFeatures(e){this.json=e,this._fullPageScreenshot=i.getFullPageScreenshot(e),this._topbar&&(this._topbar.enable(e),this._topbar.resetUIState()),this._setupMediaQueryListeners(),this._setupThirdPartyFilter(),this._setupElementScreenshotOverlay(this._dom.rootEl);let t=this._dom.isDevTools()||this._opts.disableDarkMode||this._opts.disableAutoDarkModeAndFireworks;!t&&window.matchMedia("(prefers-color-scheme: dark)").matches&&k(this._dom,!0);let n=["performance","accessibility","best-practices","seo"].every((t=>{let n=e.categories[t];return n&&1===n.score})),r=this._opts.disableFireworks||this._opts.disableAutoDarkModeAndFireworks;if(n&&!r&&(this._enableFireworks(),t||k(this._dom,!0)),e.categories.performance&&e.categories.performance.auditRefs.some((t=>!("metrics"!==t.group||!e.audits[t.id].errorMessage)))){this._dom.find("input.lh-metrics-toggle__input",this._dom.rootEl).checked=!0}this.json.audits["script-treemap-data"]&&this.json.audits["script-treemap-data"].details&&this.addButton({text:l.strings.viewTreemapLabel,icon:"treemap",onClick:()=>function(e){if(!e.audits["script-treemap-data"].details)throw new Error("no script treemap data found");C({lhr:{mainDocumentUrl:e.mainDocumentUrl,finalUrl:e.finalUrl,finalDisplayedUrl:e.finalDisplayedUrl,audits:{"script-treemap-data":e.audits["script-treemap-data"]},configSettings:{locale:e.configSettings.locale}}},z()+"/treemap/","treemap-"+L(e))}(this.json)}),this._opts.onViewTrace&&("simulate"===e.configSettings.throttlingMethod?this._dom.find('a[data-action="view-unthrottled-trace"]',this._dom.rootEl).classList.remove("lh-hidden"):this.addButton({text:l.strings.viewTraceLabel,onClick:()=>this._opts.onViewTrace?.()})),this._opts.getStandaloneReportHTML&&this._dom.find('a[data-action="save-html"]',this._dom.rootEl).classList.remove("lh-hidden");for(let e of this._dom.findAll("[data-i18n]",this._dom.rootEl)){let t=e.getAttribute("data-i18n");e.textContent=l.strings[t]}}addButton(e){let t=this._dom.rootEl.querySelector(".lh-audit-group--metrics");if(!t)return;let n=t.querySelector(".lh-buttons");n||(n=this._dom.createChildOf(t,"div","lh-buttons"));let r=["lh-button"];e.icon&&(r.push("lh-report-icon"),r.push(`lh-report-icon--${e.icon}`));let i=this._dom.createChildOf(n,"button",r.join(" "));return i.textContent=e.text,i.addEventListener("click",e.onClick),i}resetUIState(){this._topbar&&this._topbar.resetUIState()}getReportHtml(){if(!this._opts.getStandaloneReportHTML)throw new Error("`getStandaloneReportHTML` is not set");return this.resetUIState(),this._opts.getStandaloneReportHTML()}saveAsGist(){throw new Error("Cannot save as gist from base report")}_enableFireworks(){this._dom.find(".lh-scores-container",this._dom.rootEl).classList.add("lh-score100")}_setupMediaQueryListeners(){let e=self.matchMedia("(max-width: 500px)");e.addListener(this.onMediaQueryChange),this.onMediaQueryChange(e)}_resetUIState(){this._topbar&&this._topbar.resetUIState()}onMediaQueryChange(e){this._dom.rootEl.classList.toggle("lh-narrow",e.matches)}_setupThirdPartyFilter(){let e=["uses-rel-preconnect","third-party-facades"],t=["legacy-javascript"];Array.from(this._dom.rootEl.querySelectorAll("table.lh-table")).filter((e=>e.querySelector("td.lh-table-column--url, td.lh-table-column--source-location"))).filter((t=>{let n=t.closest(".lh-audit");if(!n)throw new Error(".lh-table not within audit");return!e.includes(n.id)})).forEach((e=>{let n=(d=e,Array.from(d.tBodies[0].rows)),r=n.filter((e=>!e.classList.contains("lh-sub-item-row"))),a=this._getThirdPartyRows(r,i.getFinalDisplayedUrl(this.json)),o=n.some((e=>e.classList.contains("lh-row--even"))),s=this._dom.createComponent("3pFilter"),c=this._dom.find("input",s);var d;c.addEventListener("change",(e=>{let t=e.target instanceof HTMLInputElement&&!e.target.checked,n=!0,i=r[0];for(;i;){let e=t&&a.includes(i);do{i.classList.toggle("lh-row--hidden",e),o&&(i.classList.toggle("lh-row--even",!e&&n),i.classList.toggle("lh-row--odd",!e&&!n)),i=i.nextElementSibling}while(i&&i.classList.contains("lh-sub-item-row"));e||(n=!n)}}));let h=a.filter((e=>!e.classList.contains("lh-row--group"))).length;this._dom.find(".lh-3p-filter-count",s).textContent=`${h}`,this._dom.find(".lh-3p-ui-string",s).textContent=l.strings.thirdPartyResourcesLabel;let p=a.length===r.length,g=!a.length;if((p||g)&&(this._dom.find("div.lh-3p-filter",s).hidden=!0),!e.parentNode)return;e.parentNode.insertBefore(s,e);let u=e.closest(".lh-audit");if(!u)throw new Error(".lh-table not within audit");t.includes(u.id)&&!p&&c.click()}))}_setupElementScreenshotOverlay(e){this._fullPageScreenshot&&f.installOverlayFeature({dom:this._dom,rootEl:e,overlayContainerEl:e,fullPageScreenshot:this._fullPageScreenshot})}_getThirdPartyRows(e,t){let n=i.getEntityFromUrl(t,this.json.entities),r=this.json.entities?.find((e=>!0===e.isFirstParty))?.name,a=[];for(let t of e){if(r){if(!t.dataset.entity||t.dataset.entity===r)continue}else{let e=t.querySelector("div.lh-text__url");if(!e)continue;let r=e.dataset.url;if(!r||i.getEntityFromUrl(r,this.json.entities)===n)continue}a.push(t)}return a}_saveFile(e){let t=e.type.match("json")?".json":".html",n=T({finalDisplayedUrl:i.getFinalDisplayedUrl(this.json),fetchTime:this.json.fetchTime})+t;this._opts.onSaveFileOverride?this._opts.onSaveFileOverride(e,n):this._dom.saveFile(e,n)}};function F(e,t={}){let n=document.createElement("article");n.classList.add("lh-root","lh-vars");let r=new a(n.ownerDocument,n);return new x(r).renderReport(e,n,t),new D(r,t).initFeatures(e),n}function R(e,t){return{lhr:e,missingIcuMessageIds:[]}}var N={registerLocaleData:function(e,t){},hasLocale:function(e){return!1}};
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @license
 * Copyright 2023 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @license
 * Copyright 2020 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @license Copyright 2023 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/
/**
 * @license
 * Copyright 2018 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 *
 * Dummy text for ensuring report robustness: <\/script> pre$`post %%LIGHTHOUSE_JSON%%
 * (this is handled by terser)
 */
/**
 * @license
 * Copyright 2021 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */export{a as DOM,x as ReportRenderer,D as ReportUIFeatures,N as format,F as renderReport,R as swapLocale};
