"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f60584df7867\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJFOlxcMjAyNVxcMjAyNTA3MTBcXHpodW1lbmd5dW4tZnJvbnRlbmRcXG1vYmlsZVxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZjYwNTg0ZGY3ODY3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/BottomNavigation.tsx":
/*!*********************************************!*\
  !*** ./src/components/BottomNavigation.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BottomNavigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction BottomNavigation() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const navItems = [\n        {\n            href: '/',\n            icon: 'home',\n            label: '首页',\n            active: pathname === '/'\n        },\n        {\n            href: '/engineering',\n            icon: 'discover',\n            label: '发现',\n            active: pathname === '/engineering'\n        },\n        {\n            href: '/creator',\n            icon: 'create',\n            label: '',\n            active: pathname === '/creator',\n            isCreate: true\n        },\n        {\n            href: '/social',\n            icon: 'messages',\n            label: '消息',\n            active: pathname === '/social'\n        },\n        {\n            href: '/profile',\n            icon: 'profile',\n            label: '我的',\n            active: pathname === '/profile'\n        }\n    ];\n    // TikTok风格的SVG图标\n    const renderIcon = (iconType, isActive)=>{\n        const iconClass = \"w-7 h-7 \".concat(isActive ? 'text-white' : 'text-gray-500');\n        switch(iconType){\n            case 'home':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: iconClass,\n                    fill: isActive ? 'currentColor' : 'none',\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: isActive ? 0 : 2,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\BottomNavigation.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\BottomNavigation.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, this);\n            case 'discover':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: iconClass,\n                    fill: isActive ? 'currentColor' : 'none',\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: isActive ? 0 : 2,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\BottomNavigation.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\BottomNavigation.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, this);\n            case 'create':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-8 bg-gradient-to-r from-red-500 via-pink-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5 text-white\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            strokeWidth: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\BottomNavigation.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\BottomNavigation.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\BottomNavigation.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\BottomNavigation.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 11\n                }, this);\n            case 'messages':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: iconClass,\n                    fill: isActive ? 'currentColor' : 'none',\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: isActive ? 0 : 2,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\BottomNavigation.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\BottomNavigation.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, this);\n            case 'profile':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: iconClass,\n                    fill: isActive ? 'currentColor' : 'none',\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: isActive ? 0 : 2,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\BottomNavigation.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\BottomNavigation.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-0 left-0 right-0 z-50 bg-gray-800 border-t border-gray-700\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-around py-1 pb-safe\",\n            children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: item.href,\n                    className: \"flex flex-col items-center py-2 px-2 transition-all duration-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"transition-all duration-200 \".concat(item.active && !item.isCreate ? 'scale-110' : ''),\n                            children: renderIcon(item.icon, item.active)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\BottomNavigation.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this),\n                        item.label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs mt-1 transition-colors duration-200 \".concat(item.active ? 'text-white font-medium' : 'text-gray-500'),\n                            children: item.label\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\BottomNavigation.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, item.href, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\BottomNavigation.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\BottomNavigation.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\components\\\\BottomNavigation.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(BottomNavigation, \"xbyQPtUVMO7MNj7WjJlpdWqRcTo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = BottomNavigation;\nvar _c;\n$RefreshReg$(_c, \"BottomNavigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/BottomNavigation.tsx\n"));

/***/ })

});