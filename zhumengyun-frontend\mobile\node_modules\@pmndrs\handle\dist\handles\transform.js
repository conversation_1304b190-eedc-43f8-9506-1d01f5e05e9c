import { Euler, Group } from 'three';
import { HandlesAxisHighlight } from './axis.js';
import { HandlesContext } from './context.js';
import { ScaleHandles } from './index.js';
import { RotateHandles } from './rotate/index.js';
import { TranslateHandles } from './translate/index.js';
const xRotationOffset = new Euler();
const yRotationOffset = new Euler(0, 0, Math.PI / 2);
const zRotationOffset = new Euler(0, -Math.PI / 2, 0);
export class TransformHandles extends Group {
    xAxisHighlight;
    yAxisHighlight;
    zAxisHighlight;
    handles;
    context;
    constructor(getOptions) {
        super();
        this.context = new HandlesContext(this, getOptions);
        this.xAxisHighlight = new HandlesAxisHighlight(this.context, xRotationOffset);
        this.add(this.xAxisHighlight);
        this.yAxisHighlight = new HandlesAxisHighlight(this.context, yRotationOffset);
        this.add(this.yAxisHighlight);
        this.zAxisHighlight = new HandlesAxisHighlight(this.context, zRotationOffset);
        this.add(this.zAxisHighlight);
    }
    set space(space) {
        this.context.space = space;
    }
    get space() {
        return this.context.space;
    }
    update(time, camera) {
        this.context.update(time);
        this.xAxisHighlight.update();
        this.yAxisHighlight.update();
        this.zAxisHighlight.update();
        this.handles?.update(camera);
    }
    bind(mode, options) {
        const unbindXAxisHighlight = this.xAxisHighlight.bind('x');
        const unbindYAxisHighlight = this.yAxisHighlight.bind('y');
        const unbindZAxisHighlight = this.zAxisHighlight.bind('z');
        switch (mode) {
            case 'rotate':
                this.handles = new RotateHandles(this.context);
                break;
            case 'scale':
                this.handles = new ScaleHandles(this.context);
                break;
            case 'translate':
                this.handles = new TranslateHandles(this.context);
                break;
        }
        this.add(this.handles);
        const unbind = this.handles.bind(options);
        return () => {
            if (this.handles != null) {
                this.remove(this.handles);
            }
            this.handles = undefined;
            unbind();
            unbindXAxisHighlight();
            unbindYAxisHighlight();
            unbindZAxisHighlight();
        };
    }
}
