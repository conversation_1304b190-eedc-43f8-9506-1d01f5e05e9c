"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("three"),n=require("@react-three/fiber"),a=require("three-stdlib");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function c(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var u=s(e),l=c(r);const o=l.forwardRef((({children:e,multisamping:r=8,renderIndex:s=1,disableRender:c,disableGamma:o,disableRenderPass:i,depthBuffer:d=!0,stencilBuffer:f=!1,anisotropy:p=1,encoding:h,type:m,...g},b)=>{l.useMemo((()=>n.extend({EffectComposer:a.EffectComposer,RenderPass:a.RenderPass,ShaderPass:a.ShaderPass})),[]);const y=l.useRef(null);l.useImperativeHandle(b,(()=>y.current),[]);const{scene:v,camera:x,gl:E,size:P,viewport:R}=n.useThree(),[w]=l.useState((()=>{const e=new t.WebGLRenderTarget(P.width,P.height,{type:m||t.HalfFloatType,format:t.RGBAFormat,depthBuffer:d,stencilBuffer:f,anisotropy:p});return m===t.UnsignedByteType&&null!=h&&("colorSpace"in e?e.texture.colorSpace=h:e.texture.encoding=h),e.samples=r,e}));l.useEffect((()=>{var e,r;null==(e=y.current)||e.setSize(P.width,P.height),null==(r=y.current)||r.setPixelRatio(R.dpr)}),[E,P,R.dpr]),n.useFrame((()=>{var e;c||null==(e=y.current)||e.render()}),s);const j=[];return i||j.push(l.createElement("renderPass",{key:"renderpass",attach:`passes-${j.length}`,args:[v,x]})),o||j.push(l.createElement("shaderPass",{attach:`passes-${j.length}`,key:"gammapass",args:[a.GammaCorrectionShader]})),l.Children.forEach(e,(e=>{e&&j.push(l.cloneElement(e,{key:j.length,attach:`passes-${j.length}`}))})),l.createElement("effectComposer",u.default({ref:y,args:[E,w]},g),j)}));exports.Effects=o,exports.isWebGL2Available=()=>{try{var e=document.createElement("canvas");return!(!window.WebGL2RenderingContext||!e.getContext("webgl2"))}catch(e){return!1}};
