import { ReactNode } from 'react';
import { Vector3 } from 'three';
interface FacingCameraProps {
    children?: ReactNode;
    direction: Vector3;
    angle?: number;
}
/**
 * Guard that only **shows** its children by toggling their visibility if the camera is facing the object.
 * Calculation is based on the provided angle and direction.
 *
 * @param props
 * #### `children` - `ReactNode` The ReactNode elements to conditionally show.
 * #### `direction` - [Vector3](https://threejs.org/docs/#api/en/math/Vector3) Direction vector to check against the camera's facing direction.
 * #### `angle` - `number` The angle in radians to determine visibility. Defaults to `Math.PI / 2` (90 degrees).
 */
export declare function ShowIfFacingCamera({ children, direction, angle }: FacingCameraProps): import("react/jsx-runtime").JSX.Element;
/**
 * Guard that only **renders** its children into the scene if the camera is facing the object.
 * Calculation is based on the provided angle and direction.
 *
 * @param props
 * #### `children` - `ReactNode` The ReactNode elements to conditionally render.
 * #### `direction` - [Vector3](https://threejs.org/docs/#api/en/math/Vector3) Direction vector to check against the camera's facing direction.
 * #### `angle` - `number` The angle in radians to determine visibility. Defaults to `Math.PI / 2` (90 degrees).
 */
export declare function IfFacingCamera({ children, direction, angle }: FacingCameraProps): import("react/jsx-runtime").JSX.Element | null;
export {};
