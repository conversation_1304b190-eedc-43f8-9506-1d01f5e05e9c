import*as e from"../../core/common/common.js";import"../../core/root/root.js";import*as i from"../../core/i18n/i18n.js";import*as t from"../../ui/legacy/legacy.js";const n={performance:"Performance",showPerformance:"Show Performance",record:"Record",stop:"Stop",recordAndReload:"Record and reload",saveProfile:"Save profile…",loadProfile:"Load profile…",previousFrame:"Previous frame",nextFrame:"Next frame",showRecentTimelineSessions:"Show recent timeline sessions",previousRecording:"Previous recording",nextRecording:"Next recording",hideChromeFrameInLayersView:"Hide `chrome` frame in Layers view"},o=i.i18n.registerUIStrings("panels/timeline/timeline-meta.ts",n),a=i.i18n.getLazilyComputedLocalizedString.bind(void 0,o);let r;async function l(){return r||(r=await import("./timeline.js")),r}function c(e){return void 0===r?[]:e(r)}t.ViewManager.registerViewExtension({location:"panel",id:"timeline",title:a(n.performance),commandPrompt:a(n.showPerformance),order:50,experiment:!0===globalThis.FB_ONLY__enablePerformance?void 0:"enable-performance-panel",loadView:async()=>(await l()).TimelinePanel.TimelinePanel.instance()}),t.ActionRegistration.registerActionExtension({actionId:"timeline.toggle-recording",category:"PERFORMANCE",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>c((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await l()).TimelinePanel.ActionDelegate),options:[{value:!0,title:a(n.record)},{value:!1,title:a(n.stop)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.record-reload",iconClass:"refresh",contextTypes:()=>c((e=>[e.TimelinePanel.TimelinePanel])),category:"PERFORMANCE",title:a(n.recordAndReload),loadActionDelegate:async()=>new((await l()).TimelinePanel.ActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+E"},{platform:"mac",shortcut:"Meta+Shift+E"}],experiment:"!react-native-specific-ui"}),t.ActionRegistration.registerActionExtension({category:"PERFORMANCE",actionId:"timeline.save-to-file",contextTypes:()=>c((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await l()).TimelinePanel.ActionDelegate),title:a(n.saveProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+S"},{platform:"mac",shortcut:"Meta+S"}]}),t.ActionRegistration.registerActionExtension({category:"PERFORMANCE",actionId:"timeline.load-from-file",contextTypes:()=>c((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await l()).TimelinePanel.ActionDelegate),title:a(n.loadProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+O"},{platform:"mac",shortcut:"Meta+O"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.jump-to-previous-frame",category:"PERFORMANCE",title:a(n.previousFrame),contextTypes:()=>c((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await l()).TimelinePanel.ActionDelegate),bindings:[{shortcut:"["}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.jump-to-next-frame",category:"PERFORMANCE",title:a(n.nextFrame),contextTypes:()=>c((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await l()).TimelinePanel.ActionDelegate),bindings:[{shortcut:"]"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.show-history",loadActionDelegate:async()=>new((await l()).TimelinePanel.ActionDelegate),category:"PERFORMANCE",title:a(n.showRecentTimelineSessions),contextTypes:()=>c((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+H"},{platform:"mac",shortcut:"Meta+Y"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.previous-recording",category:"PERFORMANCE",loadActionDelegate:async()=>new((await l()).TimelinePanel.ActionDelegate),title:a(n.previousRecording),contextTypes:()=>c((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Alt+Left"},{platform:"mac",shortcut:"Meta+Left"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.next-recording",category:"PERFORMANCE",loadActionDelegate:async()=>new((await l()).TimelinePanel.ActionDelegate),title:a(n.nextRecording),contextTypes:()=>c((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Alt+Right"},{platform:"mac",shortcut:"Meta+Right"}]}),e.Settings.registerSettingExtension({category:"PERFORMANCE",storageType:"Synced",title:a(n.hideChromeFrameInLayersView),settingName:"frame-viewer-hide-chrome-window",settingType:"boolean",defaultValue:!1}),e.Linkifier.registerLinkifier({contextTypes:()=>c((e=>[e.CLSLinkifier.CLSRect])),loadLinkifier:async()=>(await l()).CLSLinkifier.Linkifier.instance()}),t.ContextMenu.registerItem({location:"timelineMenu/open",actionId:"timeline.load-from-file",order:10}),t.ContextMenu.registerItem({location:"timelineMenu/open",actionId:"timeline.save-to-file",order:15});
