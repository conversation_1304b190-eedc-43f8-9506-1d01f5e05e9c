/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 * @oncall react_native
 */

import AnimatedImplementation from './AnimatedImplementation';

export {default as FlatList} from './components/AnimatedFlatList';
export {default as Image} from './components/AnimatedImage';
export {default as ScrollView} from './components/AnimatedScrollView';
export {default as SectionList} from './components/AnimatedSectionList';
export {default as Text} from './components/AnimatedText';
export {default as View} from './components/AnimatedView';
export {default as Color} from './nodes/AnimatedColor';
export {AnimatedEvent as Event} from './AnimatedEvent';
export {default as Interpolation} from './nodes/AnimatedInterpolation';
export {default as Node} from './nodes/AnimatedNode';
export {default as Value} from './nodes/AnimatedValue';
export {default as ValueXY} from './nodes/AnimatedValueXY';

/** @deprecated Use Animated.Interpolation instead */
export type {default as AnimatedInterpolation} from './nodes/AnimatedInterpolation';
/** @deprecated Use Animated.Color instead */
export type {default as AnimatedColor} from './nodes/AnimatedColor';

export type {AnimatedValueConfig as AnimatedConfig} from './nodes/AnimatedValue';
export type {default as AnimatedNode} from './nodes/AnimatedNode';
export type {default as AnimatedAddition} from './nodes/AnimatedAddition';
export type {default as AnimatedDiffClamp} from './nodes/AnimatedDiffClamp';
export type {default as AnimatedDivision} from './nodes/AnimatedDivision';
export type {default as AnimatedModulo} from './nodes/AnimatedModulo';
export type {default as AnimatedMultiplication} from './nodes/AnimatedMultiplication';
export type {default as AnimatedSubtraction} from './nodes/AnimatedSubtraction';
export type {WithAnimatedValue, AnimatedProps} from './createAnimatedComponent';
export type {AnimatedComponentType as AnimatedComponent} from './createAnimatedComponent';

/**
 * Creates a new Animated value composed from two Animated values added
 * together.
 *
 * See https://reactnative.dev/docs/animated#add
 */
export const add = AnimatedImplementation.add;

/**
 * Imperative API to attach an animated value to an event on a view. Prefer
 * using `Animated.event` with `useNativeDrive: true` if possible.
 *
 * See https://reactnative.dev/docs/animated#attachnativeevent
 */
export const attachNativeEvent = AnimatedImplementation.attachNativeEvent;

/**
 * Make any React component Animatable. Used to create `Animated.View`, etc.
 *
 * See https://reactnative.dev/docs/animated#createanimatedcomponent
 */
export const createAnimatedComponent =
  AnimatedImplementation.createAnimatedComponent;

/**
 * Animates a value from an initial velocity to zero based on a decay
 * coefficient.
 *
 * See https://reactnative.dev/docs/animated#decay
 */
export const decay = AnimatedImplementation.decay;

/**
 * Starts an animation after the given delay.
 *
 * See https://reactnative.dev/docs/animated#delay
 */
export const delay = AnimatedImplementation.delay;

/**
 * Create a new Animated value that is limited between 2 values. It uses the
 * difference between the last value so even if the value is far from the
 * bounds it will start changing when the value starts getting closer again.
 *
 * See https://reactnative.dev/docs/animated#diffclamp
 */
export const diffClamp = AnimatedImplementation.diffClamp;

/**
 * Creates a new Animated value composed by dividing the first Animated value
 * by the second Animated value.
 *
 * See https://reactnative.dev/docs/animated#divide
 */
export const divide = AnimatedImplementation.divide;

/**
 * Takes an array of mappings and extracts values from each arg accordingly,
 * then calls `setValue` on the mapped outputs.
 *
 * See https://reactnative.dev/docs/animated#event
 */
export const event = AnimatedImplementation.event;

/**
 * Advanced imperative API for snooping on animated events that are passed in
 * through props. Use values directly where possible.
 *
 * See https://reactnative.dev/docs/animated#forkevent
 */
export const forkEvent = AnimatedImplementation.forkEvent;

/**
 * Loops a given animation continuously, so that each time it reaches the
 * end, it resets and begins again from the start.
 *
 * See https://reactnative.dev/docs/animated#loop
 */
export const loop = AnimatedImplementation.loop;

/**
 * Creates a new Animated value that is the (non-negative) modulo of the
 * provided Animated value.
 *
 * See https://reactnative.dev/docs/animated#modulo
 */
export const modulo = AnimatedImplementation.modulo;

/**
 * Creates a new Animated value composed from two Animated values multiplied
 * together.
 *
 * See https://reactnative.dev/docs/animated#multiply
 */
export const multiply = AnimatedImplementation.multiply;

/**
 * Starts an array of animations all at the same time. By default, if one
 * of the animations is stopped, they will all be stopped. You can override
 * this with the `stopTogether` flag.
 *
 * See https://reactnative.dev/docs/animated#parallel
 */
export const parallel = AnimatedImplementation.parallel;

/**
 * Starts an array of animations in order, waiting for each to complete
 * before starting the next. If the current running animation is stopped, no
 * following animations will be started.
 *
 * See https://reactnative.dev/docs/animated#sequence
 */
export const sequence = AnimatedImplementation.sequence;

/**
 * Animates a value according to an analytical spring model based on
 * damped harmonic oscillation.
 *
 * See https://reactnative.dev/docs/animated#spring
 */
export const spring = AnimatedImplementation.spring;

/**
 * Array of animations may run in parallel (overlap), but are started in
 * sequence with successive delays. Nice for doing trailing effects.
 *
 * See https://reactnative.dev/docs/animated#stagger
 */
export const stagger = AnimatedImplementation.stagger;

/**
 * Creates a new Animated value composed by subtracting the second Animated
 * value from the first Animated value.
 *
 * See https://reactnative.dev/docs/animated#subtract
 */
export const subtract = AnimatedImplementation.subtract;

/**
 * Animates a value along a timed easing curve. The Easing module has tons of
 * predefined curves, or you can use your own function.
 *
 * See https://reactnative.dev/docs/animated#timing
 */
export const timing = AnimatedImplementation.timing;
export const unforkEvent = AnimatedImplementation.unforkEvent;
