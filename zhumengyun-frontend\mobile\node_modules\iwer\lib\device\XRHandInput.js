/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { Gamepad, GamepadMappingType, } from '../gamepad/Gamepad.js';
import { XRSpace } from '../spaces/XRSpace.js';
import { P_GAMEPAD, P_HAND_INPUT, P_JOINT_SPACE, P_SPACE, P_TRACKED_INPUT, } from '../private.js';
import { XRHand, XRHandJoint } from '../input/XRHand.js';
import { XRHandedness, XRInputSource, XRTargetRayMode, } from '../input/XRInputSource.js';
import { mat4, quat, vec3 } from 'gl-matrix';
import { XRJointSpace } from '../spaces/XRJointSpace.js';
import { XRTrackedInput } from './XRTrackedInput.js';
import { pinchHandPose } from './configs/hand/pinch.js';
import { pointHandPose } from './configs/hand/point.js';
import { relaxedHandPose } from './configs/hand/relaxed.js';
export const oculusHandConfig = {
    profileId: 'oculus-hand',
    fallbackProfileIds: [
        'generic-hand',
        'generic-hand-select',
        'generic-trigger',
    ],
    poses: {
        default: relaxedHandPose,
        pinch: pinchHandPose,
        point: pointHandPose,
    },
};
const XRHandGamepadConfig = {
    mapping: GamepadMappingType.None,
    buttons: [{ id: 'pinch', type: 'analog', eventTrigger: 'select' }],
    axes: [],
};
const fromPosition = vec3.create();
const fromQuaternion = quat.create();
const fromScale = vec3.create();
const toPosition = vec3.create();
const toQuaternion = quat.create();
const toScale = vec3.create();
const interpolatedPosition = vec3.create();
const interpolatedQuaternion = quat.create();
const interpolatedScale = vec3.create();
const interpolateMatrix = (out, fromMatrix, toMatrix, alpha) => {
    mat4.getTranslation(fromPosition, fromMatrix);
    mat4.getRotation(fromQuaternion, fromMatrix);
    mat4.getScaling(fromScale, fromMatrix);
    mat4.getTranslation(toPosition, toMatrix);
    mat4.getRotation(toQuaternion, toMatrix);
    mat4.getScaling(toScale, toMatrix);
    vec3.lerp(interpolatedPosition, fromPosition, toPosition, alpha);
    quat.slerp(interpolatedQuaternion, fromQuaternion, toQuaternion, alpha);
    vec3.lerp(interpolatedScale, fromScale, toScale, alpha);
    mat4.fromRotationTranslationScale(out, interpolatedQuaternion, interpolatedPosition, interpolatedScale);
    return out;
};
const mirrorMultiplierMatrix = [
    1, -1, -1, 0, -1, 1, 1, 0, -1, 1, 1, 0, -1, 1, 1, 1,
];
const mirrorMatrixToRight = (matrixLeft) => {
    for (let i = 0; i < 16; i++) {
        matrixLeft[i] *= mirrorMultiplierMatrix[i];
    }
};
export class XRHandInput extends XRTrackedInput {
    constructor(handInputConfig, handedness, globalSpace) {
        if (handedness !== XRHandedness.Left && handedness !== XRHandedness.Right) {
            throw new DOMException('handedness for XRHandInput must be either "left" or "right"', 'InvalidStateError');
        }
        if (!handInputConfig.poses.default || !handInputConfig.poses.pinch) {
            throw new DOMException('"default" and "pinch" hand pose configs are required', 'InvalidStateError');
        }
        const targetRaySpace = new XRSpace(globalSpace);
        const gripSpace = new XRSpace(targetRaySpace);
        const profiles = [
            handInputConfig.profileId,
            ...handInputConfig.fallbackProfileIds,
        ];
        const hand = new XRHand();
        Object.values(XRHandJoint).forEach((jointName) => {
            hand.set(jointName, new XRJointSpace(jointName, targetRaySpace));
        });
        const inputSource = new XRInputSource(handedness, XRTargetRayMode.TrackedPointer, profiles, targetRaySpace, new Gamepad(XRHandGamepadConfig), gripSpace, hand);
        super(inputSource);
        this[P_HAND_INPUT] = {
            poseId: 'default',
            poses: handInputConfig.poses,
        };
        this.updateHandPose();
    }
    get poseId() {
        return this[P_HAND_INPUT].poseId;
    }
    set poseId(poseId) {
        if (!this[P_HAND_INPUT].poses[poseId]) {
            console.warn(`Pose config ${poseId} not found`);
            return;
        }
        this[P_HAND_INPUT].poseId = poseId;
    }
    updateHandPose() {
        const targetPose = this[P_HAND_INPUT].poses[this[P_HAND_INPUT].poseId];
        const pinchPose = this[P_HAND_INPUT].poses.pinch;
        Object.values(XRHandJoint).forEach((jointName) => {
            const targetJointMatrix = targetPose.jointTransforms[jointName].offsetMatrix;
            const pinchJointMatrix = pinchPose.jointTransforms[jointName].offsetMatrix;
            const jointSpace = this.inputSource.hand.get(jointName);
            interpolateMatrix(jointSpace[P_SPACE].offsetMatrix, targetJointMatrix, pinchJointMatrix, this.pinchValue);
            if (this.inputSource.handedness === XRHandedness.Right) {
                mirrorMatrixToRight(jointSpace[P_SPACE].offsetMatrix);
            }
            jointSpace[P_JOINT_SPACE].radius =
                (1 - this.pinchValue) * targetPose.jointTransforms[jointName].radius +
                    this.pinchValue * pinchPose.jointTransforms[jointName].radius;
        });
        if (targetPose.gripOffsetMatrix && pinchPose.gripOffsetMatrix) {
            interpolateMatrix(this.inputSource.gripSpace[P_SPACE].offsetMatrix, targetPose.gripOffsetMatrix, pinchPose.gripOffsetMatrix, this.pinchValue);
        }
    }
    get pinchValue() {
        return this[P_TRACKED_INPUT].inputSource.gamepad[P_GAMEPAD].buttonsMap['pinch'].value;
    }
    updatePinchValue(value) {
        if (value > 1 || value < 0) {
            console.warn(`Out-of-range value ${value} provided for pinch`);
            return;
        }
        const gamepadButton = this[P_TRACKED_INPUT].inputSource.gamepad[P_GAMEPAD].buttonsMap['pinch'];
        gamepadButton[P_GAMEPAD].pendingValue = value;
    }
    onFrameStart(frame) {
        super.onFrameStart(frame);
        this.updateHandPose();
    }
}
//# sourceMappingURL=XRHandInput.js.map