import { type XRC<PERSON>rollerLocomotionRotationOptions, type XRControllerLocomotionTranslationOptions } from '@pmndrs/xr/internals';
import { RootState } from '@react-three/fiber';
import { RefObject } from 'react';
import { Object3D, Vector3 } from 'three';
/**
 * A hook for handling basic locomotion in VR
 * @param target Either a `THREE.Group` ref, or a callback function. Recieves movement input (required).
 * @param translationOptions Options that control the translation of the user. Set to `false` to disable.
 *
 * #### `translationOptions.speed` - The speed at which the user moves.
 *
 * @param rotationOptions Options that control the rotation of the user. Set to `false` to disable.
 *
 * #### `rotationOptions.deadZone` - How far the joystick must be pushed to trigger a turn.
 * #### `rotationOptions.type` - Controls how rotation using the controller functions. Can be either 'smooth' or 'snap'.
 * #### `rotationOptions.degrees` - If `type` is 'snap', this specifies the number of degrees to snap the user's view by.
 * #### `rotationOptions.speed` - If `type` is 'smooth', this specifies the speed at which the user's view rotates.
 *
 * @param translationControllerHand Specifies which hand will control the movement. Can be either 'left' or 'right'.
 */
export declare function useXRControllerLocomotion(target: RefObject<Object3D | null> | ((velocity: Vector3, rotationVelocityY: number, deltaTime: number, state: RootState, frame?: XRFrame) => void), translationOptions?: XRControllerLocomotionTranslationOptions, rotationOptions?: XRControllerLocomotionRotationOptions, translationControllerHand?: Exclude<XRHandedness, 'none'>): void;
