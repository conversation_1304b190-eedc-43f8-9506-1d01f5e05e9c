export type XRControllerVisualResponse = {
    states: Array<'default' | 'touched' | 'pressed'>;
    valueNodeName: string;
} & ({
    componentProperty: 'xAxis' | 'yAxis' | 'button' | 'state';
    valueNodeProperty: 'transform';
    minNodeName: string;
    maxNodeName: string;
} | {
    componentProperty: 'state';
    valueNodeProperty: 'visibility';
});
export type XRControllerComponent = {
    type: 'trigger' | 'squeeze' | 'touchpad' | 'thumbstick' | 'button' | (string & {});
    gamepadIndices: {
        [Key in 'button' | 'xAxis' | 'yAxis']?: number;
    };
    rootNodeName: string;
    touchPointNodeName: string;
    visualResponses: Record<string, XRControllerVisualResponse>;
};
export type XRControllerLayout = {
    selectComponentId: string;
    components: {
        [Key in string]: XRControllerComponent;
    };
    gamepadMapping: string;
    rootNodeName: string;
    assetPath: string;
};
export type XRControllerLayoutLoaderOptions = {
    /**
     * where to load the controller profiles and models from
     * @default 'https://cdn.jsdelivr.net/npm/@webxr-input-profiles/assets@1.0/dist/profiles/'
     */
    baseAssetPath?: string;
    /**
     * profile id that is used if no matching profile id is found
     * @default 'generic-trigger'
     */
    defaultControllerProfileId?: string;
};
export declare class XRControllerLayoutLoader {
    private readonly baseAssetPath;
    private readonly defaultProfileId;
    private profilesListCache;
    private profileCacheMap;
    constructor(options?: XRControllerLayoutLoaderOptions);
    load(inputSourceProfileIds: ReadonlyArray<string>, handedness: XRHandedness): Promise<XRControllerLayout> | XRControllerLayout;
    loadAsync: (inputSourceProfileIds: ReadonlyArray<string>, handedness: XRHandedness) => Promise<XRControllerLayout> | XRControllerLayout;
    private loadProfile;
    private loadProfileFromPath;
}
