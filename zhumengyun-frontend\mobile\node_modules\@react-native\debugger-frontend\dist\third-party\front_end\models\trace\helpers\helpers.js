import*as t from"../../../core/platform/platform.js";import*as e from"../../../core/common/common.js";import*as n from"../types/types.js";const r=[],i=new Map;let o=null;class s{#t=[];#e=[];static initAndActivate(t){const e=i.get(t);if(e)o=e;else{const e=new s(t);i.set(t,e),o=e}return o}static getActiveManager(){if(!o)throw new Error("Attempted to get a SyntheticEventsManager without initializing");return o}static reset(){r.length=0,o=null}static registerSyntheticBasedEvent(t){try{return s.getActiveManager().registerSyntheticBasedEvent(t)}catch(e){return t}}constructor(t){this.#e=t}registerSyntheticBasedEvent(t){const e=this.#e.indexOf(t.rawSourceEvent);if(e<0)throw new Error("Attempted to register a synthetic event paired to an unknown raw event.");const n=t;return this.#t[e]=n,n}syntheticEventForRawEventIndex(t){const e=this.#t.at(t);if(!e)throw new Error(`Attempted to get a synthetic event from an unknown raw event index: ${t}`);return e}getSyntheticTraceEvents(){return this.#t}getRawTraceEvents(){return this.#e}}var a=Object.freeze({__proto__:null,SyntheticEventsManager:s});const c=t=>n.Timing.MicroSeconds(1e3*t),l=t=>n.Timing.MilliSeconds(1e3*t),u=t=>n.Timing.MilliSeconds(t/1e3),d=t=>n.Timing.Seconds(t/1e3/1e3);function m(t){return{startTime:t.ts,endTime:n.Timing.MicroSeconds(t.ts+(t.dur||n.Timing.MicroSeconds(0))),duration:n.Timing.MicroSeconds(t.dur||0),selfTime:n.TraceEvents.isSyntheticTraceEntry(t)?n.Timing.MicroSeconds(t.selfTime||0):n.Timing.MicroSeconds(t.dur||0)}}var h=Object.freeze({__proto__:null,millisecondsToMicroseconds:c,secondsToMilliseconds:l,secondsToMicroseconds:t=>c(l(t)),microSecondsToMilliseconds:u,microSecondsToSeconds:d,timeStampForEventAdjustedByClosestNavigation:function(t,e,r,i){let o=t.ts-e.min;if(t.args?.data?.navigationId){const e=r.get(t.args.data.navigationId);e&&(o=t.ts-e.ts)}else if(t.args?.data?.frame){const e=S(t,t.args.data.frame,i);e&&(o=t.ts-e.ts)}return n.Timing.MicroSeconds(o)},eventTimingsMicroSeconds:m,eventTimingsMilliSeconds:function(t){const e=m(t);return{startTime:u(e.startTime),endTime:u(e.endTime),duration:u(e.duration),selfTime:u(e.selfTime)}},eventTimingsSeconds:function(t){const e=m(t);return{startTime:d(e.startTime),endTime:d(e.endTime),duration:d(e.duration),selfTime:d(e.selfTime)}},traceWindowMilliSeconds:function(t){return{min:u(t.min),max:u(t.max),range:u(t.range)}},traceWindowMillisecondsToMicroSeconds:function(t){return{min:c(t.min),max:c(t.max),range:c(t.range)}},traceWindowFromMilliSeconds:function(t,e){return{min:c(t),max:c(e),range:n.Timing.MicroSeconds(c(e)-c(t))}},traceWindowFromMicroSeconds:function(t,e){return{min:t,max:e,range:n.Timing.MicroSeconds(e-t)}},boundsIncludeTimeRange:function(t){const{min:e,max:n}=t.bounds,{min:r,max:i}=t.timeRange;return e<=i&&n>=r},timestampIsInBounds:function(t,e){return e>=t.min&&e<=t.max}});function f(t,e){const n=t.ts,r=e.ts;if(n<r)return-1;if(n>r)return 1;const i=n+(t.dur??0),o=r+(e.dur??0);return i>o?-1:i<o?1:0}function g(t){t.sort(f)}function p(t,e){const n=[];let r=0,i=0;for(;r<t.length&&i<e.length;){const o=t[r],s=e[i],a=f(o,s);a<=0&&(n.push(o),r++),1===a&&(n.push(s),i++)}for(;r<t.length;)n.push(t[r++]);for(;i<e.length;)n.push(e[i++]);return n}function S(e,n,r){const i=r.get(n);if(!i||""===n)return null;const o=t.ArrayUtilities.nearestIndexFromEnd(i,(t=>t.ts<=e.ts));return null===o?null:i[o]}function v(t){return t.id??t.id2?.global??t.id2?.local}function T(t,e,r,i,o,s){return{cat:"",name:"ProfileCall",nodeId:t.id,args:{},ph:"X",pid:o,tid:s,ts:i,dur:n.Timing.MicroSeconds(0),selfTime:n.Timing.MicroSeconds(0),callFrame:t.callFrame,sampleIndex:r,profileId:e}}function k(e){const n=new Map;for(const r of e){const e=E(r);if(void 0===e)continue;const i=t.MapUtilities.getWithDefault(n,e,(()=>({begin:null,end:null,instant:[]}))),o="b"===r.ph,s="e"===r.ph,a="n"===r.ph;o?i.begin=r:s?i.end=r:a&&(i.instant||(i.instant=[]),i.instant.push(r))}return n}function E(t){const e=v(t);return e&&`${t.cat}:${e}:${t.name}`}function M(t,e){const r=[];for(const[o,a]of t.entries()){const c=a.begin,l=a.end,u=a.instant;if(!c||!l&&!u)continue;const d={beginEvent:c,endEvent:l,instantEvents:u};function i(t){const e=!!t.instantEvents&&t.instantEvents.some((t=>o===E(t))),n=!!t.endEvent&&o===E(t.endEvent);return Boolean(o)&&(e||n)}if(!i(d))continue;const m=l||c,h=s.registerSyntheticBasedEvent({rawSourceEvent:c,cat:m.cat,ph:m.ph,pid:m.pid,tid:m.tid,id:o,name:c.name,dur:n.Timing.MicroSeconds(m.ts-c.ts),ts:c.ts,args:{data:d}});h.dur<0||(e?.(h),r.push(h))}return r.sort(((t,e)=>t.ts-e.ts))}const y="disabled-by-default-devtools.timeline";function b(t){return"JSRoot"===t.name&&"toplevel"===t.cat||t.cat.includes(y)&&"RunTask"===t.name}function I(e,n){let r=t.ArrayUtilities.upperBound(e,n,((t,e)=>t-e.ts))-1;for(;r>0&&!b(e[r]);)r--;return Math.max(r,0)}const w=new Map;var N=Object.freeze({__proto__:null,extractOriginFromTrace:function(t){const n=e.ParsedURL.ParsedURL.fromString(t);return n?n.host.startsWith("www.")?n.host.slice(4):n.host:null},addEventToProcessThread:function(t,e){const{tid:n,pid:r}=t;let i=e.get(r);i||(i=new Map);let o=i.get(n);o||(o=[]),o.push(t),i.set(t.tid,o),e.set(t.pid,i)},eventTimeComparator:f,sortTraceEventsInPlace:g,mergeEventsInOrder:p,getNavigationForTraceEvent:S,extractId:v,activeURLForFrameAtTime:function(t,e,n){const r=n.get(t);if(!r)return null;for(const t of r.values())for(const n of t)if(!(n.window.min>e||n.window.max<e))return n.frame.url;return null},makeProfileCall:T,makeSyntheticTraceEntry:function(t,e,r,i){return{cat:"",name:t,args:{},ph:"X",pid:r,tid:i,ts:e,dur:n.Timing.MicroSeconds(0),selfTime:n.Timing.MicroSeconds(0)}},matchEvents:k,createSortedSyntheticEvents:M,createMatchedSortedSyntheticEvents:function(t,e){return M(k(t),e)},getZeroIndexedLineAndColumnForEvent:function(t){const e=function(t){if(!t.args?.data)return{lineNumber:void 0,columnNumber:void 0};let e,n;"lineNumber"in t.args.data&&"number"==typeof t.args.data.lineNumber&&(e=t.args.data.lineNumber);"columnNumber"in t.args.data&&"number"==typeof t.args.data.columnNumber&&(n=t.args.data.columnNumber);return{lineNumber:e,columnNumber:n}}(t),{lineNumber:n,columnNumber:r}=e;switch(t.name){case"FunctionCall":case"EvaluateScript":case"v8.compile":case"v8.produceCache":return{lineNumber:"number"==typeof n?n-1:void 0,columnNumber:"number"==typeof r?r-1:void 0};default:return e}},getZeroIndexedStackTraceForEvent:function(t){const e=function(t){return t.args?.data?.stackTrace?t.args.data.stackTrace:n.TraceEvents.isTraceEventUpdateLayoutTree(t)&&t.args.beginData?.stackTrace||null}(t);return e?e.map((e=>{const n={...e};switch(t.name){case"ScheduleStyleRecalculation":case"InvalidateLayout":case"UpdateLayoutTree":n.lineNumber=e.lineNumber&&e.lineNumber-1,n.columnNumber=e.columnNumber&&e.columnNumber-1}return n})):null},frameIDForEvent:function(t){return t.args&&"beginData"in t.args&&"object"==typeof t.args.beginData&&null!==t.args.beginData&&"frame"in t.args.beginData&&"string"==typeof t.args.beginData.frame?t.args.beginData.frame:t.args?.data?.frame?t.args.data.frame:null},isTopLevelEvent:b,findUpdateLayoutTreeEvents:function(t,e,r){const i=[];for(let o=I(t,e);o<t.length;o++){const e=t[o];n.TraceEvents.isTraceEventUpdateLayoutTree(e)&&(e.ts>=(r||1/0)||i.push(e))}return i},forEachEvent:function(t,e){const r=e.startTime||n.Timing.MicroSeconds(0),i=e.endTime||n.Timing.MicroSeconds(1/0),o=!1!==e.ignoreAsyncEvents,s=[];for(let a=I(t,r);a<t.length;a++){const c=t[a],l=m(c);if(l.endTime<r)continue;if(l.startTime>i)break;if(o&&n.TraceEvents.isAsyncPhase(c.ph)||n.TraceEvents.isFlowPhase(c.ph))continue;let u=s.at(-1),d=u?m(u).endTime:null;for(;u&&d&&d<=l.startTime;)s.pop(),e.onEndEvent(u),u=s.at(-1),d=u?m(u).endTime:null;e.eventFilter&&!e.eventFilter(c)||(l.duration?(e.onStartEvent(c),s.push(c)):e.onInstantEvent&&e.onInstantEvent(c))}for(;s.length;){const t=s.pop();t&&e.onEndEvent(t)}},eventHasCategory:function(t,e){let n=w.get(t.cat);return n||(n=new Set(t.cat.split(",")||[])),n.has(e)},nodeIdForInvalidationEvent:function(t){return t.args.data.nodeId??null}});let J=0;const x=()=>++J,F=()=>({roots:new Set,maxDepth:0}),C=(t,e)=>({entry:t,id:e,parent:null,children:[],depth:0});function _(t,e){const r=new Map,i=[];J=-1;const o=F();for(let s=0;s<t.length;s++){const a=t[s];if(e&&!e.filter.has(a.name))continue;const c=a.dur||0,l=x(),u=C(a,l);if(0===i.length){o.roots.add(u),a.selfTime=n.Timing.MicroSeconds(c),i.push(u),o.maxDepth=Math.max(o.maxDepth,i.length),r.set(a,u);continue}const d=i.at(-1);if(void 0===d)throw new Error("Impossible: no parent node found in the stack");const m=d.entry,h=a.ts,f=m.ts,g=h+c,p=f+(m.dur||0);if(h<f)throw new Error("Impossible: current event starts before the parent event");if(h>=p){i.pop(),s--,J--;continue}g>p||(u.depth=i.length,u.parent=d,d.children.push(u),a.selfTime=n.Timing.MicroSeconds(c),void 0!==m.selfTime&&(m.selfTime=n.Timing.MicroSeconds(m.selfTime-(a.dur||0))),i.push(u),o.maxDepth=Math.max(o.maxDepth,i.length),r.set(a,u))}return{tree:o,entryToNode:r}}function P(t,e,r,i,o,s){if(!o||function(t,e){const n=t.entry.ts,r=t.entry.ts+(t.entry.dur||0);if(n>=e.min&&n<e.max)return!0;if(r>e.min&&r<=e.max)return!0;if(n<=e.min&&r>=e.max)return!0;return!1}(e,o)){if(void 0!==s){if(n.Timing.MicroSeconds(e.entry.ts+n.Timing.MicroSeconds(e.entry.dur||0))<s)return}r(e.entry);for(const n of e.children)P(t,n,r,i,o,s);i(e.entry)}}function D(t){const e=[];for(const n of t){const t=n.ts,r=n.ts+(n.dur||0);let i=e.at(-1);if(void 0===i){e.push(n);continue}let o=i.ts+(i.dur||0);for(;e.length&&t>=o&&(e.pop(),i=e.at(-1),void 0!==i);)o=i.ts+(i.dur||0);if(e.length&&r>o)return!1;e.push(n)}return!0}var B=Object.freeze({__proto__:null,makeTraceEntryNodeId:x,makeEmptyTraceEntryTree:F,makeEmptyTraceEntryNode:C,treify:_,walkTreeFromEntry:function(t,e,n,r){const i=t.get(e);i&&P(t,i,n,r)},walkEntireTree:function(t,e,n,r,i,o){for(const s of e.roots)P(t,s,n,r,i,o)},canBuildTreesFromEvents:D});var R=Object.freeze({__proto__:null,buildTrackDataFromExtensionEntries:function(e,n){const r=new Map;for(const n of e){const e=n.args.trackGroup||`track-name-${n.args.track}`,i=t.MapUtilities.getWithDefault(r,e,(()=>({name:n.args.trackGroup||n.args.track,isTrackGroup:Boolean(n.args.trackGroup),entriesByTrack:{[n.args.track]:[]}})));i.entriesByTrack[n.args.track]||(i.entriesByTrack[n.args.track]=[]);i.entriesByTrack[n.args.track].push(n)}for(const t of r.values()){for(const e of Object.values(t.entriesByTrack))g(e),D(e)&&_(e);n.push(t)}return n}});var A=Object.freeze({__proto__:null,isSyntheticNetworkRequestEventRenderBlocking:function(t){return"non_blocking"!==t.args.data.renderBlocking}});class j{#n=[];#r=[];#i;#o;#s=[];#a=!1;#c;#l=new Map;#u;#d;jsSampleEvents=[];constructor(t,e,r,i,o){this.#c=t,this.#o=i,this.#i=r,this.#u=o||n.Configuration.defaults(),this.#d=e}buildProfileCalls(t){const e=p(t,this.callsFromProfileSamples()),r=[];for(let t=0;t<e.length;t++){const i=e[t];if("I"===i.ph)continue;if(0===r.length){if(n.TraceEvents.isProfileCall(i)){this.#m(i);continue}r.push(i),this.#h(i);continue}const o=r.at(-1);if(void 0===o)continue;i.ts>=o.ts+(o.dur||0)?(this.#f(o),r.pop(),t--):n.TraceEvents.isProfileCall(i)?this.#m(i,o):(this.#h(i),r.push(i))}for(;r.length;){const t=r.pop();t&&this.#f(t)}return this.#n}#h(t){"RunMicrotasks"!==t.name&&"RunTask"!==t.name||(this.#s=[],this.#g(0,t.ts),this.#a=!1),this.#a&&(this.#g(this.#s.pop()||0,t.ts),this.#a=!1),this.#p(t),this.#s.push(this.#r.length)}#m(t,e){if(e&&n.TraceEvents.isJSInvocationEvent(e)||this.#a)this.#p(t);else if(n.TraceEvents.isProfileCall(t)&&0===this.#r.length){this.#a=!0;const e=this.#r.length;this.#p(t),this.#s.push(e)}}#f(t){const e=n.Timing.MicroSeconds(t.ts+(t.dur||0));this.#g(this.#s.pop()||0,e)}callsFromProfileSamples(){const t=this.#c.samples,e=this.#c.timestamps,r=this.#u.debugMode;if(!t)return[];const i=[];let o;for(let s=0;s<t.length;s++){const t=this.#c.nodeByIndex(s),a=c(n.Timing.MilliSeconds(e[s]));if(!t)continue;const l=T(t,this.#d,s,a,this.#i,this.#o);i.push(l),r&&this.jsSampleEvents.push(this.#S(l,a)),t.id===this.#c.gcNode?.id&&o?this.#l.set(l,o):o=t}return i}#v(t){let e=this.#c.nodeById(t.nodeId);const n=e?.id===this.#c.gcNode?.id;if(n&&(e=this.#l.get(t)||null),!e)return[];const r=new Array(e.depth+1+Number(n));let i=r.length-1;for(n&&(r[i--]=t);e;)r[i--]=T(e,t.profileId,t.sampleIndex,t.ts,this.#i,this.#o),e=e.parent;return r}#p(t){const e=n.TraceEvents.isProfileCall(t)?this.#v(t):this.#r;j.filterStackFrames(e,this.#u);const r=t.ts+(t.dur||0),i=Math.min(e.length,this.#r.length);let o;for(o=this.#s.at(-1)||0;o<i;++o){const t=e[o].callFrame,i=this.#r[o].callFrame;if(!j.framesAreEqual(t,i))break;this.#r[o].dur=n.Timing.MicroSeconds(Math.max(this.#r[o].dur||0,r-this.#r[o].ts))}for(this.#g(o,t.ts);o<e.length;++o){const t=e[o];t.nodeId!==this.#c.programNode?.id&&t.nodeId!==this.#c.root?.id&&t.nodeId!==this.#c.idleNode?.id&&t.nodeId!==this.#c.gcNode?.id&&(this.#r.push(t),this.#n.push(t))}}#g(t,e){if(this.#s.length){const n=this.#s.at(-1);n&&t<n&&(console.error(`Child stack is shallower (${t}) than the parent stack (${n}) at ${e}`),t=n)}this.#r.length<t&&(console.error(`Trying to truncate higher than the current stack size at ${e}`),t=this.#r.length);for(let t=0;t<this.#r.length;++t)this.#r[t].dur=n.Timing.MicroSeconds(Math.max(e-this.#r[t].ts,0));this.#r.length=t}#S(t,e){return{name:"JSSample",cat:"devtools.timeline",args:{data:{stackTrace:this.#v(t).map((t=>t.callFrame))}},ph:"I",ts:e,dur:n.Timing.MicroSeconds(0),pid:this.#i,tid:this.#o}}static framesAreEqual(t,e){return t.scriptId===e.scriptId&&t.functionName===e.functionName&&t.lineNumber===e.lineNumber}static showNativeName(t,e){return e&&Boolean(j.nativeGroup(t))}static nativeGroup(t){return t.startsWith("Parse")?"Parse":t.startsWith("Compile")||t.startsWith("Recompile")?"Compile":null}static isNativeRuntimeFrame(t){return"native V8Runtime"===t.url}static filterStackFrames(t,e){if(e.showAllEvents)return;let n=null,r=0;for(let i=0;i<t.length;++i){const o=t[i].callFrame,s=j.isNativeRuntimeFrame(o);if(s&&!j.showNativeName(o.functionName,e.includeRuntimeCallStats))continue;const a=s?j.nativeGroup(o.functionName):null;n&&n===a||(n=a,t[r++]=t[i])}t.length=r}}var O=Object.freeze({__proto__:null,SamplesIntegrator:j});export{R as Extensions,A as Network,O as SamplesIntegrator,a as SyntheticEvents,h as Timing,N as Trace,B as TreeHelpers};
