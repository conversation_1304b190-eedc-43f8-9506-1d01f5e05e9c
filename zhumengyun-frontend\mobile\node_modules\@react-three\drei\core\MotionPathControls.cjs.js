"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("three"),r=require("react"),n=require("@react-three/fiber"),o=require("maath");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function c(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var s=u(e),a=c(t),i=c(r);const l=i.createContext(null);function f(){const e=i.useContext(l);if(!e)throw new Error("useMotion hook must be used in a MotionPathControls component.");return e}function m({points:e=50,color:t="black"}){const{path:r}=f(),[n,o]=i.useState([]),u=i.useMemo((()=>new a.MeshBasicMaterial({color:t})),[t]),c=i.useMemo((()=>new a.SphereGeometry(.025,16,16)),[]),s=i.useRef([]);return i.useEffect((()=>{r.curves!==s.current&&(o(r.getPoints(e)),s.current=r.curves)})),n.map(((e,t)=>i.createElement("mesh",{key:t,material:u,geometry:c,position:[e.x,e.y,e.z]})))}const p=i.forwardRef((({children:e,curves:t=[],debug:r=!1,debugColor:u="black",object:c,focus:f,loop:p=!0,offset:d,smooth:g=!1,eps:v=1e-5,damping:b=.1,focusDamping:h=.1,maxSpeed:j=1/0,...y},M)=>{const{camera:w}=n.useThree(),P=i.useRef(null),O=i.useRef(null!=d?d:0),x=i.useMemo((()=>new a.CurvePath),[]),C=i.useMemo((()=>({focus:f,object:(null==c?void 0:c.current)instanceof a.Object3D?c:{current:w},path:x,current:O.current,offset:O.current,point:new a.Vector3,tangent:new a.Vector3,next:new a.Vector3})),[f,c]);i.useLayoutEffect((()=>{var e,r;x.curves=[];const n=t.length>0?t:null!==(e=null==(r=P.current)||null==(r=r.__r3f)?void 0:r.objects)&&void 0!==e?e:[];for(let e=0;e<n.length;e++)x.add(n[e]);if(g){const e=x.getPoints("number"==typeof g?g:1),t=new a.CatmullRomCurve3(e);x.curves=[t]}x.updateArcLengths()})),i.useImperativeHandle(M,(()=>Object.assign(P.current,{motion:C})),[C]),i.useLayoutEffect((()=>{O.current=o.misc.repeat(O.current,1)}),[d]);const E=i.useMemo((()=>new a.Vector3),[]);return n.useFrame(((e,t)=>{const r=C.offset;if(o.easing.damp(O,"current",void 0!==d?d:C.current,b,t,j,void 0,v),C.offset=p?o.misc.repeat(O.current,1):o.misc.clamp(O.current,0,1),x.getCurveLengths().length>0){x.getPointAt(C.offset,C.point),x.getTangentAt(C.offset,C.tangent).normalize(),x.getPointAt(o.misc.repeat(O.current-(r-C.offset),1),C.next);const e=(null==c?void 0:c.current)instanceof a.Object3D?c.current:w;e.position.copy(C.point),f&&o.easing.dampLookAt(e,(e=>(null==e?void 0:e.current)instanceof a.Object3D)(f)?f.current.getWorldPosition(E):f,h,t,j,void 0,v)}})),i.createElement("group",s.default({ref:P},y),i.createElement(l.Provider,{value:C},e,r&&i.createElement(m,{color:u})))}));exports.MotionPathControls=p,exports.useMotion=f;
