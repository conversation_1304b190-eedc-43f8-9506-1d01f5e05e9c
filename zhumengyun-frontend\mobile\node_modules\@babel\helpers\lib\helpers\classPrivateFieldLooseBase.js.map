{"version": 3, "names": ["_classPrivateFieldBase", "receiver", "privateKey", "Object", "prototype", "hasOwnProperty", "call", "TypeError"], "sources": ["../../src/helpers/classPrivateFieldLooseBase.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _classPrivateFieldBase<T extends object>(\n  receiver: T,\n  privateKey: PropertyKey,\n) {\n  if (!Object.prototype.hasOwnProperty.call(receiver, privateKey)) {\n    throw new TypeError(\"attempted to use private field on non-instance\");\n  }\n  return receiver;\n}\n"], "mappings": ";;;;;;AAEe,SAASA,sBAAsBA,CAC5CC,QAAW,EACXC,UAAuB,EACvB;EACA,IAAI,CAACC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,QAAQ,EAAEC,UAAU,CAAC,EAAE;IAC/D,MAAM,IAAIK,SAAS,CAAC,gDAAgD,CAAC;EACvE;EACA,OAAON,QAAQ;AACjB", "ignoreList": []}