{"version": 3, "file": "contract_errors.js", "sourceRoot": "", "sources": ["../../../src/errors/contract_errors.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AAKF,OAAO,EACN,YAAY,EACZ,wBAAwB,EACxB,+BAA+B,EAC/B,6BAA6B,EAC7B,0BAA0B,EAC1B,4BAA4B,EAC5B,gCAAgC,EAChC,iCAAiC,EACjC,8BAA8B,EAC9B,2BAA2B,EAC3B,6BAA6B,EAC7B,8BAA8B,GAC9B,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,aAAa,EAAE,iBAAiB,EAAE,MAAM,uBAAuB,CAAC;AAEzE,MAAM,OAAO,iBAAkB,SAAQ,aAAa;IAInD,YAAmB,OAAe,EAAE,OAA4B;QAC/D,KAAK,CAAC,OAAO,CAAC,CAAC;QAJT,SAAI,GAAG,YAAY,CAAC;QAM1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACxB,CAAC;CACD;AACD,MAAM,OAAO,0BAA2B,SAAQ,aAAa;IAG5D,YAA0B,OAAe,EAAS,IAAY;QAC7D,KAAK,CAAC,mBAAmB,OAAO,0CAA0C,IAAI,IAAI,CAAC,CAAC;QAD3D,YAAO,GAAP,OAAO,CAAQ;QAAS,SAAI,GAAJ,IAAI,CAAQ;QAFvD,SAAI,GAAG,6BAA6B,CAAC;IAI5C,CAAC;IAEM,MAAM;QACZ,uCAAY,KAAK,CAAC,MAAM,EAAE,KAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,IAAG;IACtE,CAAC;CACD;AAED,MAAM,OAAO,uBAAwB,SAAQ,aAAa;IAGzD;QACC,KAAK,CACJ,2FAA2F,CAC3F,CAAC;QALI,SAAI,GAAG,wBAAwB,CAAC;IAMvC,CAAC;CACD;AAED,MAAM,OAAO,iCAAkC,SAAQ,aAAa;IAGnE;QACC,KAAK,CAAC,mDAAmD,CAAC,CAAC;QAHrD,SAAI,GAAG,8BAA8B,CAAC;IAI7C,CAAC;CACD;AAED,MAAM,OAAO,8BAA+B,SAAQ,aAAa;IAGhE,YAA0B,SAAiB;QAC1C,KAAK,CAAC,UAAU,SAAS,mCAAmC,CAAC,CAAC;QADrC,cAAS,GAAT,SAAS,CAAQ;QAFpC,SAAI,GAAG,6BAA6B,CAAC;IAI5C,CAAC;IAEM,MAAM;QACZ,uCAAY,KAAK,CAAC,MAAM,EAAE,KAAE,SAAS,EAAE,IAAI,CAAC,SAAS,IAAG;IACzD,CAAC;CACD;AAED,MAAM,OAAO,0BAA2B,SAAQ,aAAa;IAG5D,YAA0B,IAAY;QACrC,KAAK,CAAC,UAAU,IAAI,mCAAmC,CAAC,CAAC;QADhC,SAAI,GAAJ,IAAI,CAAQ;QAF/B,SAAI,GAAG,2BAA2B,CAAC;IAI1C,CAAC;IAEM,MAAM;QACZ,uCAAY,KAAK,CAAC,MAAM,EAAE,KAAE,IAAI,EAAE,IAAI,CAAC,IAAI,IAAG;IAC/C,CAAC;CACD;AAED,MAAM,OAAO,8BAA+B,SAAQ,aAAa;IAGhE;QACC,KAAK,CAAC,4EAA4E,CAAC,CAAC;QAH9E,SAAI,GAAG,gCAAgC,CAAC;IAI/C,CAAC;CACD;AAED,MAAM,OAAO,6BAA8B,SAAQ,aAAa;IAG/D;QACC,KAAK,CAAC,iFAAiF,CAAC,CAAC;QAHnF,SAAI,GAAG,4BAA4B,CAAC;IAI3C,CAAC;CACD;AAED,MAAM,OAAO,iCAAkC,SAAQ,aAAa;IAGnE;QACC,KAAK,CAAC,oFAAoF,CAAC,CAAC;QAHtF,SAAI,GAAG,iCAAiC,CAAC;IAIhD,CAAC;CACD;AAED,MAAM,OAAO,0BAA2B,SAAQ,aAAa;IAA7D;;QACQ,SAAI,GAAG,0BAA0B,CAAC;IAC1C,CAAC;CAAA;AAOD;;;GAGG;AACH,MAAM,OAAO,oBAAqB,SAAQ,iBAAiB;IAW1D,YAAmB,KAA6D;QAC/E,KAAK,CAAC,KAAK,CAAC,OAAO,IAAI,OAAO,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QACrE,wEAAwE;QACxE,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC;QAC5D,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QAEvB,mEAAmE;QACnE,+DAA+D;QAC/D,gDAAgD;QAChD,sIAAsI;QACtI,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YACpC,IAAI,aAA+B,CAAC;YACpC,IAAI,KAAK,CAAC,IAAI,IAAI,eAAe,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;gBACjD,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;YAC1C,CAAC;iBAAM,CAAC;gBACP,0DAA0D;gBAC1D,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC;YAC5B,CAAC;YACD,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;YAC/B,IAAI,CAAC,KAAK,GAAG,IAAI,oBAAoB,CAAC,aAAgD,CAAC,CAAC;QACzF,CAAC;aAAM,CAAC;YACP,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACxB,CAAC;IACF,CAAC;IAEM,oBAAoB,CAC1B,SAAiB,EACjB,cAAuB,EACvB,SAAsC;QAEtC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC5B,CAAC;IAEM,MAAM;QACZ,IAAI,IAAI,GAAG,gCACP,KAAK,CAAC,MAAM,EAAE,KACjB,IAAI,EAAE,IAAI,CAAC,IAAI,GAWf,CAAC;QAEF,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,mCACA,IAAI,KACP,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,SAAS,EAAE,IAAI,CAAC,SAAS,GACzB,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;CACD;AAED;;;GAGG;AACH,MAAM,OAAO,sBAAuB,SAAQ,iBAAiB;IAG5D,YAAmB,QAAsB;QACxC,KAAK,CAAC,2EAA2E,CAAC,CAAC;QACnF,IAAI,CAAC,IAAI,GAAG,+BAA+B,CAAC;QAC5C,IAAI,CAAC,KAAK,GAAG,IAAI,oBAAoB,CAAC,QAA2C,CAAC,CAAC;IACpF,CAAC;CACD;AAED,MAAM,OAAO,oCAAqC,SAAQ,iBAAiB;IAG1E,YAAmB,KAAoE;;QACtF,KAAK,CACJ,SAAS,MAAA,KAAK,CAAC,IAAI,mCAAI,WAAW,YAAY,MAAA,KAAK,CAAC,KAAK,mCAAI,WAAW,EAAE,EAC1E,+HAA+H,CAC/H,CAAC;QANI,SAAI,GAAG,8BAA8B,CAAC;IAO7C,CAAC;CACD"}