/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_VIEWPORT } from '../private.js';
export declare class XRViewport {
    [P_VIEWPORT]: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    constructor(x: number, y: number, width: number, height: number);
    get x(): number;
    get y(): number;
    get width(): number;
    get height(): number;
}
//# sourceMappingURL=XRViewport.d.ts.map