/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { XRMesh } from '../meshes/XRMesh.js';
import { XRPlane } from '../planes/XRPlane.js';
import { P_ANCHOR, P_DEVICE, P_FRAME, P_HIT_TEST, P_MESH, P_PLANE, P_SESSION, P_SPACE, P_WEBGL_LAYER, } from '../private.js';
import { XRAnchorSet, XRAnchorUtils } from '../anchors/XRAnchor.js';
import { XRHitTestResult, XRHitTestSource, } from '../hittest/XRHitTest.js';
import { XRInputSourcesChangeEvent, } from '../events/XRInputSourcesChangeEvent.js';
import { XRReferenceSpace, XRReferenceSpaceType, } from '../spaces/XRReferenceSpace.js';
import { XRRenderState } from './XRRenderState.js';
import { XRSessionEvent, } from '../events/XRSessionEvent.js';
import { XRSpace, XRSpaceUtils } from '../spaces/XRSpace.js';
import { XREye } from '../views/XRView.js';
import { XRFrame } from '../frameloop/XRFrame.js';
import { mat4 } from 'gl-matrix';
export var XREnvironmentBlendMode;
(function (XREnvironmentBlendMode) {
    XREnvironmentBlendMode["Opaque"] = "opaque";
    XREnvironmentBlendMode["AlphaBlend"] = "alpha-blend";
    XREnvironmentBlendMode["Additive"] = "additive";
})(XREnvironmentBlendMode || (XREnvironmentBlendMode = {}));
export var XRInteractionMode;
(function (XRInteractionMode) {
    XRInteractionMode["ScreenSpace"] = "screen-space";
    XRInteractionMode["WorldSpace"] = "world-space";
})(XRInteractionMode || (XRInteractionMode = {}));
export class XRSession extends EventTarget {
    constructor(device, mode, enabledFeatures) {
        super();
        this[P_SESSION] = {
            device,
            mode,
            renderState: new XRRenderState(),
            pendingRenderState: null,
            enabledFeatures: enabledFeatures,
            isSystemKeyboardSupported: false,
            ended: false,
            projectionMatrices: {
                [XREye.Left]: mat4.create(),
                [XREye.Right]: mat4.create(),
                [XREye.None]: mat4.create(),
            },
            getProjectionMatrix: (eye) => {
                return this[P_SESSION].projectionMatrices[eye];
            },
            referenceSpaceIsSupported: (referenceSpaceType) => {
                if (!this[P_SESSION].enabledFeatures.includes(referenceSpaceType)) {
                    return false;
                }
                switch (referenceSpaceType) {
                    case XRReferenceSpaceType.Viewer:
                        return true;
                    case XRReferenceSpaceType.Local:
                    case XRReferenceSpaceType.LocalFloor:
                    case XRReferenceSpaceType.BoundedFloor:
                    case XRReferenceSpaceType.Unbounded:
                        return this[P_SESSION].mode != 'inline';
                }
            },
            frameHandle: 0,
            frameCallbacks: [],
            currentFrameCallbacks: null,
            onDeviceFrame: () => {
                if (this[P_SESSION].ended) {
                    return;
                }
                this[P_SESSION].deviceFrameHandle = globalThis.requestAnimationFrame(this[P_SESSION].onDeviceFrame);
                if (this[P_SESSION].pendingRenderState != null) {
                    this[P_SESSION].renderState = this[P_SESSION].pendingRenderState;
                    this[P_SESSION].pendingRenderState = null;
                    this[P_SESSION].device[P_DEVICE].onBaseLayerSet(this[P_SESSION].renderState.baseLayer);
                }
                const baseLayer = this[P_SESSION].renderState.baseLayer;
                if (baseLayer === null) {
                    return;
                }
                const context = baseLayer.context;
                const canvas = context.canvas;
                /**
                 * This code snippet is designed to clear the buffers attached to an opaque framebuffer
                 * at the beginning of each XR animation frame, but it only applies to immersive XR sessions.
                 * The process is as follows:
                 *
                 * 1. Check if the session is immersive: It verifies if `session.immersive` is true.
                 *    This ensures that the buffer clearing operations are only performed for immersive
                 *    sessions, which have exclusive access to the XR device's display.
                 *
                 * 2. Save current clear values: The current clear values for the color, depth, and
                 *    stencil buffers are stored. These values need to be restored after clearing the
                 *    buffers to maintain the application's rendering state as expected.
                 *
                 * 3. Set clear values to defaults: The clear color is set to transparent black, the
                 *    clear depth to the maximum depth value (1.0), and the clear stencil to 0. This
                 *    ensures that the buffers are reset to a known state, free from any residual data.
                 *
                 * 4. Clear the buffers: The depth, color, and stencil buffers are cleared, removing
                 *    any content from previous frames and preparing them for new rendering operations.
                 *
                 * 5. Restore previous clear values: The original clear values are reinstated to return
                 *    the WebGL context to its state prior to this operation, allowing subsequent rendering
                 *    to proceed without interference.
                 *
                 * This clearing process is crucial for some XR devices to function correctly and to
                 * prevent rendering artifacts from past frames. It ensures that each new frame starts
                 * with a clean slate.
                 */
                if (this[P_SESSION].mode != 'inline') {
                    const currentClearColor = context.getParameter(context.COLOR_CLEAR_VALUE);
                    const currentClearDepth = context.getParameter(context.DEPTH_CLEAR_VALUE);
                    const currentClearStencil = context.getParameter(context.STENCIL_CLEAR_VALUE);
                    context.clearColor(0.0, 0.0, 0.0, 0.0);
                    context.clearDepth(1);
                    context.clearStencil(0.0);
                    context.clear(context.DEPTH_BUFFER_BIT |
                        context.COLOR_BUFFER_BIT |
                        context.STENCIL_BUFFER_BIT);
                    context.clearColor(currentClearColor[0], currentClearColor[1], currentClearColor[2], currentClearColor[3]);
                    context.clearDepth(currentClearDepth);
                    context.clearStencil(currentClearStencil);
                }
                // Calculate projection matrices
                const { depthNear, depthFar } = this[P_SESSION].renderState;
                const { width, height } = canvas;
                if (this[P_SESSION].mode !== 'inline') {
                    const aspect = (width * (this[P_SESSION].device.stereoEnabled ? 0.5 : 1.0)) /
                        height;
                    mat4.perspective(this[P_SESSION].projectionMatrices[XREye.Left], this[P_SESSION].device.fovy, aspect, depthNear, depthFar);
                    mat4.copy(this[P_SESSION].projectionMatrices[XREye.Right], this[P_SESSION].projectionMatrices[XREye.Left]);
                }
                else {
                    const aspect = width / height;
                    mat4.perspective(this[P_SESSION].projectionMatrices[XREye.None], this[P_SESSION].renderState.inlineVerticalFieldOfView, aspect, depthNear, depthFar);
                }
                const frame = new XRFrame(this, this[P_SESSION].frameHandle, true, true, performance.now());
                const time = performance.now();
                const devui = this[P_SESSION].device[P_DEVICE].devui;
                if (devui) {
                    devui.render(time);
                }
                if (this[P_SESSION].mode === 'immersive-ar') {
                    const sem = this[P_SESSION].device[P_DEVICE].sem;
                    if (sem) {
                        sem.render(time);
                    }
                }
                if (this[P_SESSION].enabledFeatures.includes('anchors')) {
                    this[P_SESSION].updateTrackedAnchors();
                }
                if (this[P_SESSION].enabledFeatures.includes('plane-detection')) {
                    this[P_SESSION].updateTrackedPlanes(frame);
                }
                if (this[P_SESSION].enabledFeatures.includes('mesh-detection')) {
                    this[P_SESSION].updateTrackedMeshes(frame);
                }
                if (this[P_SESSION].enabledFeatures.includes('hit-test')) {
                    this[P_SESSION].computeHitTestResults(frame);
                }
                this[P_SESSION].device[P_DEVICE].onFrameStart(frame);
                this[P_SESSION].updateActiveInputSources();
                /*
                 * For each entry in callbacks, in order:
                 * - If the entry’s cancelled boolean is true, continue to the next entry.
                 * - Invoke the Web IDL callback function, passing now and frame as the arguments.
                 * - If an exception is thrown, report the exception.
                 */
                // - Let callbacks be a list of the entries in session’s list of animation frame
                //   callback, in the order in which they were added to the list.
                const callbacks = (this[P_SESSION].currentFrameCallbacks =
                    this[P_SESSION].frameCallbacks);
                // - Set session’s list of animation frame callbacks to the empty list.
                this[P_SESSION].frameCallbacks = [];
                const rightNow = performance.now();
                for (let i = 0; i < callbacks.length; i++) {
                    try {
                        if (!callbacks[i].cancelled) {
                            callbacks[i].callback(rightNow, frame);
                        }
                    }
                    catch (err) {
                        console.error(err);
                    }
                }
                this[P_SESSION].currentFrameCallbacks = null;
                // - Set frame’s active boolean to false.
                frame[P_FRAME].active = false;
            },
            nominalFrameRate: device.internalNominalFrameRate,
            referenceSpaces: [],
            inputSourceArray: [],
            activeInputSources: [],
            updateActiveInputSources: () => {
                const handTrackingOn = this[P_SESSION].enabledFeatures.includes('hand-tracking');
                const prevInputs = this[P_SESSION].activeInputSources;
                const currInputs = this[P_SESSION].device.inputSources.filter((inputSource) => !inputSource.hand || handTrackingOn);
                const added = currInputs.filter((item) => !prevInputs.includes(item));
                const removed = prevInputs.filter((item) => !currInputs.includes(item));
                this[P_SESSION].activeInputSources = currInputs;
                if (added.length > 0 || removed.length > 0) {
                    this.dispatchEvent(new XRInputSourcesChangeEvent('inputsourceschange', {
                        session: this,
                        added,
                        removed,
                    }));
                }
            },
            trackedAnchors: new XRAnchorSet(),
            persistentAnchors: new Map(),
            newAnchors: new Map(),
            frameTrackedAnchors: new XRAnchorSet(),
            updateTrackedAnchors: () => {
                if (this[P_SESSION].enabledFeatures.includes('anchors')) {
                    this[P_SESSION].frameTrackedAnchors.clear();
                    Array.from(this[P_SESSION].trackedAnchors).forEach((anchor) => {
                        if (anchor[P_ANCHOR].deleted) {
                            this[P_SESSION].trackedAnchors.delete(anchor);
                            if (this[P_SESSION].newAnchors.has(anchor)) {
                                const { reject } = this[P_SESSION].newAnchors.get(anchor);
                                reject(new DOMException('Anchor is no longer tracked', 'InvalidStateError'));
                            }
                        }
                        else {
                            this[P_SESSION].frameTrackedAnchors.add(anchor);
                            if (this[P_SESSION].newAnchors.has(anchor)) {
                                const { resolve } = this[P_SESSION].newAnchors.get(anchor);
                                resolve(anchor);
                                this[P_SESSION].newAnchors.delete(anchor);
                            }
                        }
                    });
                }
            },
            trackedPlanes: new Map(),
            updateTrackedPlanes: (frame) => {
                const sem = this[P_SESSION].device[P_DEVICE].sem;
                if (!sem) {
                    return;
                }
                const trackedPlanes = Array.from(this[P_SESSION].trackedPlanes.keys());
                trackedPlanes.forEach((plane) => {
                    if (!sem.trackedPlanes.has(plane)) {
                        this[P_SESSION].trackedPlanes.delete(plane);
                    }
                });
                sem.trackedPlanes.forEach((plane) => {
                    let xrPlane = this[P_SESSION].trackedPlanes.get(plane);
                    if (!xrPlane) {
                        const planeSpace = new XRSpace(this[P_SESSION].device[P_DEVICE].globalSpace, plane.transform.matrix);
                        xrPlane = new XRPlane(plane, planeSpace, plane.polygon, plane.semanticLabel);
                        this[P_SESSION].trackedPlanes.set(plane, xrPlane);
                    }
                    xrPlane[P_PLANE].lastChangedTime = frame.predictedDisplayTime;
                    xrPlane[P_PLANE].frame = frame;
                    frame[P_FRAME].detectedPlanes.add(xrPlane);
                });
            },
            trackedMeshes: new Map(),
            updateTrackedMeshes: (frame) => {
                const sem = this[P_SESSION].device[P_DEVICE].sem;
                if (!sem) {
                    return;
                }
                const trackedMeshes = Array.from(this[P_SESSION].trackedMeshes.keys());
                trackedMeshes.forEach((mesh) => {
                    if (!sem.trackedMeshes.has(mesh)) {
                        this[P_SESSION].trackedMeshes.delete(mesh);
                    }
                });
                sem.trackedMeshes.forEach((mesh) => {
                    let xrMesh = this[P_SESSION].trackedMeshes.get(mesh);
                    if (!xrMesh) {
                        const meshSpace = new XRSpace(this[P_SESSION].device[P_DEVICE].globalSpace, mesh.transform.matrix);
                        xrMesh = new XRMesh(mesh, meshSpace, mesh.vertices, mesh.indices, mesh.semanticLabel);
                        this[P_SESSION].trackedMeshes.set(mesh, xrMesh);
                    }
                    xrMesh[P_MESH].lastChangedTime = frame.predictedDisplayTime;
                    xrMesh[P_MESH].frame = frame;
                    frame[P_FRAME].detectedMeshes.add(xrMesh);
                });
            },
            hitTestSources: new Set(),
            computeHitTestResults: (frame) => {
                const sem = this[P_SESSION].device[P_DEVICE].sem;
                if (!sem)
                    return;
                const globalSpace = this[P_SESSION].device[P_DEVICE].globalSpace;
                this[P_SESSION].hitTestSources.forEach((hitTestSource) => {
                    const sourceSpace = hitTestSource[P_HIT_TEST].space;
                    const sourceGlobalOffset = XRSpaceUtils.calculateGlobalOffsetMatrix(sourceSpace);
                    const rayLocalOffset = hitTestSource[P_HIT_TEST].offsetRay.matrix;
                    const rayGlobalOffset = mat4.create();
                    mat4.multiply(rayGlobalOffset, sourceGlobalOffset, rayLocalOffset);
                    const hitTestResults = [];
                    sem.computeHitTestResults(rayGlobalOffset).forEach((matrix) => {
                        const offsetSpace = new XRSpace(globalSpace, matrix);
                        const hitTestResult = new XRHitTestResult(frame, offsetSpace);
                        hitTestResults.push(hitTestResult);
                    });
                    frame[P_FRAME].hitTestResultsMap.set(hitTestSource, hitTestResults);
                });
            },
            onend: null,
            oninputsourceschange: null,
            onselect: null,
            onselectstart: null,
            onselectend: null,
            onsqueeze: null,
            onsqueezestart: null,
            onsqueezeend: null,
            onvisibilitychange: null,
            onframeratechange: null,
        };
        XRAnchorUtils.recoverPersistentAnchorsFromStorage(this);
        // start the frameloop
        this[P_SESSION].onDeviceFrame();
    }
    get visibilityState() {
        return this[P_SESSION].device.visibilityState;
    }
    get frameRate() {
        return this[P_SESSION].nominalFrameRate;
    }
    get supportedFrameRates() {
        return new Float32Array(this[P_SESSION].device.supportedFrameRates);
    }
    get renderState() {
        return this[P_SESSION].renderState;
    }
    get inputSources() {
        // use the same array object
        this[P_SESSION].inputSourceArray.length = 0;
        if (!this[P_SESSION].ended && this[P_SESSION].mode !== 'inline') {
            this[P_SESSION].inputSourceArray.push(...this[P_SESSION].activeInputSources);
        }
        return this[P_SESSION].inputSourceArray;
    }
    get enabledFeatures() {
        return this[P_SESSION].enabledFeatures;
    }
    get isSystemKeyboardSupported() {
        return this[P_SESSION].isSystemKeyboardSupported;
    }
    get environmentBlendMode() {
        var _a;
        return ((_a = this[P_SESSION].device[P_DEVICE].environmentBlendModes[this[P_SESSION].mode]) !== null && _a !== void 0 ? _a : XREnvironmentBlendMode.Opaque);
    }
    get interactionMode() {
        return this[P_SESSION].device[P_DEVICE].interactionMode;
    }
    updateRenderState(state = {}) {
        var _a, _b, _c, _d;
        if (this[P_SESSION].ended) {
            throw new DOMException('XRSession has already ended.', 'InvalidStateError');
        }
        if (state.baseLayer && state.baseLayer[P_WEBGL_LAYER].session !== this) {
            throw new DOMException('Base layer was created by a different XRSession', 'InvalidStateError');
        }
        if (state.inlineVerticalFieldOfView != null &&
            this[P_SESSION].mode !== 'inline') {
            throw new DOMException('InlineVerticalFieldOfView must not be set for an immersive session', 'InvalidStateError');
        }
        const compoundStateInit = {
            baseLayer: state.baseLayer ||
                ((_a = this[P_SESSION].pendingRenderState) === null || _a === void 0 ? void 0 : _a.baseLayer) ||
                undefined,
            depthFar: state.depthFar ||
                ((_b = this[P_SESSION].pendingRenderState) === null || _b === void 0 ? void 0 : _b.depthFar) ||
                undefined,
            depthNear: state.depthNear ||
                ((_c = this[P_SESSION].pendingRenderState) === null || _c === void 0 ? void 0 : _c.depthNear) ||
                undefined,
            inlineVerticalFieldOfView: state.inlineVerticalFieldOfView ||
                ((_d = this[P_SESSION].pendingRenderState) === null || _d === void 0 ? void 0 : _d.inlineVerticalFieldOfView) ||
                undefined,
        };
        this[P_SESSION].pendingRenderState = new XRRenderState(compoundStateInit, this[P_SESSION].renderState);
    }
    // the nominal frame rate updates are emulated, no actual update to the
    // display frame rate of the device will be executed
    async updateTargetFrameRate(rate) {
        return new Promise((resolve, reject) => {
            if (this[P_SESSION].ended) {
                reject(new DOMException('XRSession has already ended.', 'InvalidStateError'));
            }
            else if (!this[P_SESSION].device.supportedFrameRates.includes(rate)) {
                reject(new DOMException('Requested frame rate not supported.', 'InvalidStateError'));
            }
            else {
                if (this[P_SESSION].nominalFrameRate === rate) {
                    console.log(`Requested frame rate is the same as the current nominal frame rate, no update made`);
                }
                else {
                    this[P_SESSION].nominalFrameRate = rate;
                    this.dispatchEvent(new XRSessionEvent('frameratechange', { session: this }));
                    console.log(`Nominal frame rate updated to ${rate}`);
                }
                resolve();
            }
        });
    }
    async requestReferenceSpace(type) {
        return new Promise((resolve, reject) => {
            if (this[P_SESSION].ended ||
                !this[P_SESSION].referenceSpaceIsSupported(type)) {
                reject(new DOMException('The requested reference space type is not supported.', 'NotSupportedError'));
                return;
            }
            let referenceSpace;
            switch (type) {
                case XRReferenceSpaceType.Viewer:
                    referenceSpace = this[P_SESSION].device.viewerSpace;
                    break;
                case XRReferenceSpaceType.Local:
                    // creating an XRReferenceSpace with the current headset transform in global space
                    referenceSpace = new XRReferenceSpace(type, this[P_SESSION].device[P_DEVICE].globalSpace, this[P_SESSION].device.viewerSpace[P_SPACE].offsetMatrix);
                    break;
                case XRReferenceSpaceType.LocalFloor:
                case XRReferenceSpaceType.BoundedFloor:
                case XRReferenceSpaceType.Unbounded:
                    // TO-DO: add boundary geometry for bounded-floor
                    referenceSpace = new XRReferenceSpace(type, this[P_SESSION].device[P_DEVICE].globalSpace);
                    break;
            }
            this[P_SESSION].referenceSpaces.push(referenceSpace);
            resolve(referenceSpace);
        });
    }
    requestAnimationFrame(callback) {
        if (this[P_SESSION].ended) {
            return 0;
        }
        const frameHandle = ++this[P_SESSION].frameHandle;
        this[P_SESSION].frameCallbacks.push({
            handle: frameHandle,
            callback,
            cancelled: false,
        });
        return frameHandle;
    }
    cancelAnimationFrame(handle) {
        // Remove the callback with that handle from the queue
        let callbacks = this[P_SESSION].frameCallbacks;
        let index = callbacks.findIndex((d) => d && d.handle === handle);
        if (index > -1) {
            callbacks[index].cancelled = true;
            callbacks.splice(index, 1);
        }
        // If cancelAnimationFrame is called from within a frame callback, also check
        // the remaining callbacks for the current frame:
        callbacks = this[P_SESSION].currentFrameCallbacks;
        if (callbacks) {
            index = callbacks.findIndex((d) => d && d.handle === handle);
            if (index > -1) {
                callbacks[index].cancelled = true;
                // Rely on cancelled flag only; don't mutate this array while it's being iterated
            }
        }
    }
    async end() {
        return new Promise((resolve, reject) => {
            if (this[P_SESSION].ended || this[P_SESSION].deviceFrameHandle === null) {
                reject(new DOMException('XRSession has already ended.', 'InvalidStateError'));
            }
            else {
                globalThis.cancelAnimationFrame(this[P_SESSION].deviceFrameHandle);
                this[P_SESSION].device[P_DEVICE].onSessionEnd();
                this.dispatchEvent(new XRSessionEvent('end', { session: this }));
                resolve();
            }
        });
    }
    // anchors
    get persistentAnchors() {
        return Array.from(this[P_SESSION].persistentAnchors.keys());
    }
    restorePersistentAnchor(uuid) {
        return new Promise((resolve, reject) => {
            if (!this[P_SESSION].persistentAnchors.has(uuid)) {
                reject(new DOMException(`Persistent anchor with uuid ${uuid} not found.`, 'InvalidStateError'));
            }
            else if (this[P_SESSION].ended) {
                reject(new DOMException('XRSession has already ended.', 'InvalidStateError'));
            }
            else {
                const anchor = this[P_SESSION].persistentAnchors.get(uuid);
                if (this[P_SESSION].newAnchors.has(anchor)) {
                    reject(new DOMException(`Multiple concurrent attempts detected to restore the anchor with UUID: ${uuid}.`, 'InvalidStateError'));
                }
                else {
                    this[P_SESSION].trackedAnchors.add(anchor);
                    this[P_SESSION].newAnchors.set(anchor, { resolve, reject });
                }
            }
        });
    }
    deletePersistentAnchor(uuid) {
        return new Promise((resolve, reject) => {
            if (!this[P_SESSION].persistentAnchors.has(uuid)) {
                reject(new DOMException(`Persistent anchor with uuid ${uuid} not found.`, 'InvalidStateError'));
            }
            else {
                const anchor = this[P_SESSION].persistentAnchors.get(uuid);
                this[P_SESSION].persistentAnchors.delete(uuid);
                anchor.delete();
                resolve(undefined);
            }
        });
    }
    requestHitTestSource(options) {
        return new Promise((resolve, reject) => {
            if (!this[P_SESSION].enabledFeatures.includes('hit-test')) {
                reject(new DOMException(`WebXR feature "hit-test" is not supported by current session`, 'NotSupportedError'));
            }
            else if (this[P_SESSION].ended) {
                reject(new DOMException('XRSession has already ended.', 'InvalidStateError'));
            }
            else if (!this[P_SESSION].device[P_DEVICE].sem) {
                reject(new DOMException('Synthethic Environment Module required for emulating hit-test', 'OperationError'));
            }
            else {
                const xrHitTestSource = new XRHitTestSource(this, options);
                this[P_SESSION].hitTestSources.add(xrHitTestSource);
                resolve(xrHitTestSource);
            }
        });
    }
    // events
    get onend() {
        var _a;
        return (_a = this[P_SESSION].onend) !== null && _a !== void 0 ? _a : (() => { });
    }
    set onend(callback) {
        if (this[P_SESSION].onend) {
            this.removeEventListener('end', this[P_SESSION].onend);
        }
        this[P_SESSION].onend = callback;
        if (callback) {
            this.addEventListener('end', callback);
        }
    }
    get oninputsourceschange() {
        var _a;
        return (_a = this[P_SESSION].oninputsourceschange) !== null && _a !== void 0 ? _a : (() => { });
    }
    set oninputsourceschange(callback) {
        if (this[P_SESSION].oninputsourceschange) {
            this.removeEventListener('inputsourceschange', this[P_SESSION].oninputsourceschange);
        }
        this[P_SESSION].oninputsourceschange = callback;
        if (callback) {
            this.addEventListener('inputsourceschange', callback);
        }
    }
    get onselect() {
        var _a;
        return (_a = this[P_SESSION].onselect) !== null && _a !== void 0 ? _a : (() => { });
    }
    set onselect(callback) {
        if (this[P_SESSION].onselect) {
            this.removeEventListener('select', this[P_SESSION].onselect);
        }
        this[P_SESSION].onselect = callback;
        if (callback) {
            this.addEventListener('select', callback);
        }
    }
    get onselectstart() {
        var _a;
        return (_a = this[P_SESSION].onselectstart) !== null && _a !== void 0 ? _a : (() => { });
    }
    set onselectstart(callback) {
        if (this[P_SESSION].onselectstart) {
            this.removeEventListener('selectstart', this[P_SESSION].onselectstart);
        }
        this[P_SESSION].onselectstart = callback;
        if (callback) {
            this.addEventListener('selectstart', callback);
        }
    }
    get onselectend() {
        var _a;
        return (_a = this[P_SESSION].onselectend) !== null && _a !== void 0 ? _a : (() => { });
    }
    set onselectend(callback) {
        if (this[P_SESSION].onselectend) {
            this.removeEventListener('selectend', this[P_SESSION].onselectend);
        }
        this[P_SESSION].onselectend = callback;
        if (callback) {
            this.addEventListener('selectend', callback);
        }
    }
    get onsqueeze() {
        var _a;
        return (_a = this[P_SESSION].onsqueeze) !== null && _a !== void 0 ? _a : (() => { });
    }
    set onsqueeze(callback) {
        if (this[P_SESSION].onsqueeze) {
            this.removeEventListener('squeeze', this[P_SESSION].onsqueeze);
        }
        this[P_SESSION].onsqueeze = callback;
        if (callback) {
            this.addEventListener('squeeze', callback);
        }
    }
    get onsqueezestart() {
        var _a;
        return (_a = this[P_SESSION].onsqueezestart) !== null && _a !== void 0 ? _a : (() => { });
    }
    set onsqueezestart(callback) {
        if (this[P_SESSION].onsqueezestart) {
            this.removeEventListener('squeezestart', this[P_SESSION].onsqueezestart);
        }
        this[P_SESSION].onsqueezestart = callback;
        if (callback) {
            this.addEventListener('squeezestart', callback);
        }
    }
    get onsqueezeend() {
        var _a;
        return (_a = this[P_SESSION].onsqueezeend) !== null && _a !== void 0 ? _a : (() => { });
    }
    set onsqueezeend(callback) {
        if (this[P_SESSION].onsqueezeend) {
            this.removeEventListener('squeezeend', this[P_SESSION].onsqueezeend);
        }
        this[P_SESSION].onsqueezeend = callback;
        if (callback) {
            this.addEventListener('squeezeend', callback);
        }
    }
    get onvisibilitychange() {
        var _a;
        return (_a = this[P_SESSION].onvisibilitychange) !== null && _a !== void 0 ? _a : (() => { });
    }
    set onvisibilitychange(callback) {
        if (this[P_SESSION].onvisibilitychange) {
            this.removeEventListener('visibilitychange', this[P_SESSION].onvisibilitychange);
        }
        this[P_SESSION].onvisibilitychange = callback;
        if (callback) {
            this.addEventListener('visibilitychange', callback);
        }
    }
    get onframeratechange() {
        var _a;
        return (_a = this[P_SESSION].onframeratechange) !== null && _a !== void 0 ? _a : (() => { });
    }
    set onframeratechange(callback) {
        if (this[P_SESSION].onframeratechange) {
            this.removeEventListener('frameratechange', this[P_SESSION].onframeratechange);
        }
        this[P_SESSION].onframeratechange = callback;
        if (callback) {
            this.addEventListener('frameratechange', callback);
        }
    }
}
//# sourceMappingURL=XRSession.js.map