'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'

interface Content3DProps {
  type: '3d-model' | 'ar-scene' | 'vr-space' | 'bim-model'
  title: string
  description: string
  modelUrl?: string
  onEnterVR?: () => void
  onEnterAR?: () => void
}

// 简化的3D内容预览组件（不使用Three.js）
function Simple3DPreview({ type }: { type: string }) {
  const [isAnimating, setIsAnimating] = useState(true)

  const getPreviewContent = () => {
    switch (type) {
      case 'bim-model':
        return (
          <div className="relative w-full h-full flex items-center justify-center">
            <motion.div
              className="w-24 h-32 bg-gradient-to-b from-blue-400 to-blue-600 rounded-lg shadow-2xl"
              animate={isAnimating ? {
                rotateY: [0, 360],
                scale: [1, 1.1, 1]
              } : {}}
              transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
            />
            <motion.div
              className="absolute bottom-0 w-32 h-2 bg-gray-600 rounded-full"
              animate={isAnimating ? { scale: [1, 1.2, 1] } : {}}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            />
            <div className="absolute top-4 text-white text-sm font-medium">
              🏗️ BIM模型
            </div>
          </div>
        )
      case 'ar-scene':
        return (
          <div className="relative w-full h-full flex items-center justify-center">
            <motion.div
              className="w-20 h-20 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full shadow-2xl"
              animate={isAnimating ? {
                scale: [1, 1.3, 1],
                rotate: [0, 180, 360]
              } : {}}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
            />
            <motion.div
              className="absolute inset-0 border-2 border-purple-400 rounded-full"
              animate={isAnimating ? {
                scale: [1, 1.5, 1],
                opacity: [0.8, 0.2, 0.8]
              } : {}}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            />
            <div className="absolute top-4 text-white text-sm font-medium">
              🔮 AR场景
            </div>
          </div>
        )
      case 'vr-space':
        return (
          <div className="relative w-full h-full flex items-center justify-center">
            <div className="grid grid-cols-3 gap-2">
              {Array.from({ length: 9 }).map((_, i) => (
                <motion.div
                  key={i}
                  className="w-6 h-6 bg-gradient-to-r from-green-400 to-teal-500 rounded"
                  animate={isAnimating ? {
                    y: [0, -10, 0],
                    opacity: [0.7, 1, 0.7]
                  } : {}}
                  transition={{ 
                    duration: 2, 
                    repeat: Infinity, 
                    delay: i * 0.1,
                    ease: "easeInOut" 
                  }}
                />
              ))}
            </div>
            <div className="absolute top-4 text-white text-sm font-medium">
              🥽 VR空间
            </div>
          </div>
        )
      default:
        return (
          <div className="relative w-full h-full flex items-center justify-center">
            <motion.div
              className="w-20 h-20 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-xl shadow-2xl"
              animate={isAnimating ? {
                rotateX: [0, 360],
                rotateY: [0, 360]
              } : {}}
              transition={{ duration: 5, repeat: Infinity, ease: "linear" }}
            />
            <div className="absolute top-4 text-white text-sm font-medium">
              🎮 3D内容
            </div>
          </div>
        )
    }
  }

  return (
    <div 
      className="w-full h-full bg-gradient-to-br from-gray-900 to-black rounded-lg overflow-hidden cursor-pointer"
      onClick={() => setIsAnimating(!isAnimating)}
    >
      {getPreviewContent()}
    </div>
  )
}

export default function Simple3DViewer({ 
  type, 
  title, 
  description, 
  modelUrl, 
  onEnterVR, 
  onEnterAR 
}: Content3DProps) {
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showControls, setShowControls] = useState(true)

  const getTypeIcon = () => {
    switch (type) {
      case 'bim-model': return '🏗️'
      case 'ar-scene': return '🔮'
      case 'vr-space': return '🥽'
      default: return '🎮'
    }
  }

  const getTypeColor = () => {
    switch (type) {
      case 'bim-model': return 'from-blue-500 to-indigo-600'
      case 'ar-scene': return 'from-purple-500 to-pink-600'
      case 'vr-space': return 'from-green-500 to-teal-600'
      default: return 'from-cyan-500 to-blue-600'
    }
  }

  return (
    <div className={`relative ${isFullscreen ? 'fixed inset-0 z-50' : 'w-full h-64'} bg-black rounded-lg overflow-hidden`}>
      {/* 3D预览区域 */}
      <Simple3DPreview type={type} />

      {/* 控制面板 */}
      {showControls && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute bottom-4 left-4 right-4"
        >
          <div className="bg-black/80 backdrop-blur-lg rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <span className="text-2xl">{getTypeIcon()}</span>
                <div>
                  <h4 className="text-white font-medium text-sm">{title}</h4>
                  <p className="text-gray-400 text-xs">{description}</p>
                </div>
              </div>
              <button
                onClick={() => setIsFullscreen(!isFullscreen)}
                className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center text-white"
              >
                {isFullscreen ? '⤓' : '⤢'}
              </button>
            </div>

            <div className="flex space-x-2">
              {type === 'ar-scene' && (
                <button
                  onClick={onEnterAR}
                  className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white py-2 px-3 rounded-lg text-xs font-medium"
                >
                  🔮 进入AR
                </button>
              )}
              
              {type === 'vr-space' && (
                <button
                  onClick={onEnterVR}
                  className="flex-1 bg-gradient-to-r from-green-500 to-teal-500 text-white py-2 px-3 rounded-lg text-xs font-medium"
                >
                  🥽 进入VR
                </button>
              )}
              
              <button
                className={`flex-1 bg-gradient-to-r ${getTypeColor()} text-white py-2 px-3 rounded-lg text-xs font-medium`}
              >
                🎮 互动体验
              </button>
              
              <button
                onClick={() => setShowControls(false)}
                className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center text-white text-xs"
              >
                ✕
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {/* 隐藏控制面板时的显示按钮 */}
      {!showControls && (
        <button
          onClick={() => setShowControls(true)}
          className="absolute bottom-4 right-4 w-10 h-10 bg-black/80 rounded-full flex items-center justify-center text-white"
        >
          ⚙️
        </button>
      )}

      {/* 全屏模式下的关闭按钮 */}
      {isFullscreen && (
        <button
          onClick={() => setIsFullscreen(false)}
          className="absolute top-4 right-4 w-10 h-10 bg-black/80 rounded-full flex items-center justify-center text-white z-10"
        >
          ✕
        </button>
      )}
    </div>
  )
}
