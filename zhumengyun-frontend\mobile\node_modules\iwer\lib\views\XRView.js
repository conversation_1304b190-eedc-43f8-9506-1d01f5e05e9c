/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_VIEW } from '../private.js';
export var XREye;
(function (XREye) {
    XREye["None"] = "none";
    XREye["Left"] = "left";
    XREye["Right"] = "right";
})(XREye || (XREye = {}));
export class XRView {
    constructor(eye, projectionMatrix, transform, session) {
        this[P_VIEW] = {
            eye,
            projectionMatrix,
            transform,
            recommendedViewportScale: null,
            requestedViewportScale: 1.0,
            session,
        };
    }
    get eye() {
        return this[P_VIEW].eye;
    }
    get projectionMatrix() {
        return this[P_VIEW].projectionMatrix;
    }
    get transform() {
        return this[P_VIEW].transform;
    }
    get recommendedViewportScale() {
        return this[P_VIEW].recommendedViewportScale;
    }
    requestViewportScale(scale) {
        if (scale === null || scale <= 0 || scale > 1) {
            console.warn('Invalid scale value. Scale must be > 0 and <= 1.');
            return;
        }
        this[P_VIEW].requestedViewportScale = scale;
    }
}
//# sourceMappingURL=XRView.js.map