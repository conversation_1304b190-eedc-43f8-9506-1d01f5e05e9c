/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
export class XRInputSourceEvent extends Event {
    constructor(type, eventInitDict) {
        super(type, eventInitDict);
        if (!eventInitDict.frame) {
            throw new Error('XRInputSourceEventInit.frame is required');
        }
        if (!eventInitDict.inputSource) {
            throw new Error('XRInputSourceEventInit.inputSource is required');
        }
        this.frame = eventInitDict.frame;
        this.inputSource = eventInitDict.inputSource;
    }
}
//# sourceMappingURL=XRInputSourceEvent.js.map