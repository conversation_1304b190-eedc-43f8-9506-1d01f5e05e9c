'use client'

import React, { useState, useEffect } from 'react'
import { 中文页面布局, 中文容器, 中文卡片, 中文按钮 } from '../components/GitHubBottomNavigation'
import { Search, Star, GitFork, Eye, Calendar, Users, Code, Zap, Globe, Shield } from 'lucide-react'

interface 仓库 {
  id: number
  name: string
  description: string
  language: string
  stars: number
  forks: number
  watchers: number
  updated: string
  isPrivate: boolean
}

export default function 中文主页() {
  const [repositories, setRepositories] = useState<仓库[]>([])
  const [searchQuery, setSearchQuery] = useState('')

  useEffect(() => {
    // 模拟数据加载
    setRepositories([
      {
        id: 1,
        name: 'nextgen-2025-平台',
        description: 'AI原生数字生活操作系统 - NextGen 2025智慧生活平台',
        language: 'TypeScript',
        stars: 128,
        forks: 32,
        watchers: 45,
        updated: '2小时前',
        isPrivate: false
      },
      {
        id: 2,
        name: '工程发现',
        description: '工程发现与智慧建造平台 - 集成公共资源交易网络',
        language: 'JavaScript',
        stars: 89,
        forks: 21,
        watchers: 34,
        updated: '5小时前',
        isPrivate: false
      },
      {
        id: 3,
        name: '创作者经济系统',
        description: '创作者经济系统 - Web3集成与NFT创作平台',
        language: 'Solidity',
        stars: 156,
        forks: 67,
        watchers: 78,
        updated: '1天前',
        isPrivate: true
      }
    ])
  }, [])

  const getLanguageColor = (language: string) => {
    const colors: { [key: string]: string } = {
      'TypeScript': '#3178c6',
      'JavaScript': '#f1e05a',
      'Solidity': '#aa6746',
      'Python': '#3572a5',
      'React': '#61dafb'
    }
    return colors[language] || '#8c959f'
  }

  return (
    <中文页面布局>
      <中文容器 className="py-6">
        {/* 搜索栏 */}
        <中文卡片 className="mb-4">
          <div className="中文-搜索">
            <Search className="中文-搜索-图标 w-4 h-4" />
            <input
              type="text"
              placeholder="搜索仓库..."
              className="中文-搜索-输入框 w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </中文卡片>

        {/* 欢迎卡片 */}
        <中文卡片 className="mb-4 text-center">
          <Code className="w-12 h-12 text-[#0969da] mx-auto mb-3" />
          <h1 className="text-xl font-bold text-[#24292f] mb-2">NextGen 2025</h1>
          <p className="text-sm text-[#656d76] mb-4">
            AI原生数字生活操作系统
          </p>
          <中文按钮 variant="primary">
            <Zap className="w-4 h-4 mr-2" />
            新建仓库
          </中文按钮>
        </中文卡片>

        {/* 功能卡片 */}
        <div className="grid grid-cols-3 gap-3 mb-4">
          <中文卡片 className="text-center p-3">
            <Globe className="w-6 h-6 text-[#0969da] mx-auto mb-2" />
            <h3 className="text-xs font-semibold text-[#24292f] mb-1">工程发现</h3>
            <p className="text-xs text-[#656d76]">智慧建造</p>
          </中文卡片>
          <中文卡片 className="text-center p-3">
            <Users className="w-6 h-6 text-[#2ea043] mx-auto mb-2" />
            <h3 className="text-xs font-semibold text-[#24292f] mb-1">创作者经济</h3>
            <p className="text-xs text-[#656d76]">Web3生态</p>
          </中文卡片>
          <中文卡片 className="text-center p-3">
            <Shield className="w-6 h-6 text-[#cf222e] mx-auto mb-2" />
            <h3 className="text-xs font-semibold text-[#24292f] mb-1">安全合规</h3>
            <p className="text-xs text-[#656d76]">法规合规</p>
          </中文卡片>
        </div>

        {/* 仓库列表 */}
        <中文卡片>
          <div className="border-b border-[#d0d7de] pb-3 mb-4">
            <h2 className="text-lg font-semibold text-[#24292f]">我的仓库</h2>
          </div>

          <div className="space-y-4">
            {repositories.map((repo) => (
              <div key={repo.id} className="border-b border-[#d0d7de] last:border-b-0 pb-4 last:pb-0">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="font-semibold text-[#0969da]">{repo.name}</h3>
                      {repo.isPrivate && (
                        <span className="text-xs bg-[#fff8c5] text-[#9a6700] px-2 py-1 rounded-full">私有</span>
                      )}
                    </div>
                    <p className="text-sm text-[#656d76] mb-2 text-ellipsis-2">{repo.description}</p>
                    <div className="flex items-center space-x-3 text-xs text-[#656d76]">
                      <div className="flex items-center space-x-1">
                        <div
                          className="w-2 h-2 rounded-full"
                          style={{ backgroundColor: getLanguageColor(repo.language) }}
                        />
                        <span>{repo.language}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-3 h-3" />
                        <span>{repo.stars}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <GitFork className="w-3 h-3" />
                        <span>{repo.forks}</span>
                      </div>
                    </div>
                  </div>
                  <button className="text-[#656d76] hover:text-[#0969da] ml-2">
                    <Star className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </中文卡片>
      </中文容器>
    </中文页面布局>
  )
}
