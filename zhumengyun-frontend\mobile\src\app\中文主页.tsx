'use client'

import React, { useState, useEffect } from 'react'
import { Search, Star, GitFork, Eye, Calendar, Users, Code, Zap, Globe, Shield } from 'lucide-react'

interface 仓库 {
  id: number
  name: string
  description: string
  language: string
  stars: number
  forks: number
  watchers: number
  updated: string
  isPrivate: boolean
}

interface 活动 {
  id: number
  type: string
  repo: string
  message: string
  time: string
  user: string
}

export default function 中文主页() {
  const [repositories, setRepositories] = useState<仓库[]>([])
  const [activities, setActivities] = useState<活动[]>([])
  const [searchQuery, setSearchQuery] = useState('')

  useEffect(() => {
    // 模拟数据加载
    setRepositories([
      {
        id: 1,
        name: 'nextgen-2025-平台',
        description: 'AI原生数字生活操作系统 - NextGen 2025智慧生活平台',
        language: 'TypeScript',
        stars: 128,
        forks: 32,
        watchers: 45,
        updated: '2小时前',
        isPrivate: false
      },
      {
        id: 2,
        name: '工程发现',
        description: '工程发现与智慧建造平台 - 集成公共资源交易网络',
        language: 'JavaScript',
        stars: 89,
        forks: 21,
        watchers: 34,
        updated: '5小时前',
        isPrivate: false
      },
      {
        id: 3,
        name: '创作者经济系统',
        description: '创作者经济系统 - Web3集成与NFT创作平台',
        language: 'Solidity',
        stars: 156,
        forks: 67,
        watchers: 78,
        updated: '1天前',
        isPrivate: true
      }
    ])

    setActivities([
      {
        id: 1,
        type: 'push',
        repo: 'nextgen-2025-平台',
        message: '添加了中文风格设计系统',
        time: '2小时前',
        user: '开发者'
      },
      {
        id: 2,
        type: 'star',
        repo: '工程发现',
        message: '给仓库加了星标',
        time: '4小时前',
        user: '贡献者'
      },
      {
        id: 3,
        type: 'fork',
        repo: '创作者经济系统',
        message: '分叉了仓库',
        time: '6小时前',
        user: '社区成员'
      }
    ])
  }, [])

  const getLanguageColor = (language: string) => {
    const colors: { [key: string]: string } = {
      'TypeScript': '#3178c6',
      'JavaScript': '#f1e05a',
      'Solidity': '#aa6746',
      'Python': '#3572a5',
      'React': '#61dafb'
    }
    return colors[language] || '#8c959f'
  }

  return (
    <div className="中文-页面 min-h-screen">
      {/* 中文头部 */}
      <header className="中文-头部">
        <div className="中文-容器">
          <div className="中文-头部-导航">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Code className="w-8 h-8 text-white" />
                <span className="text-xl font-bold">NextGen 2025</span>
              </div>
              <nav className="hidden md:flex items-center space-x-6">
                <a href="#" className="中文-头部-链接">拉取请求</a>
                <a href="#" className="中文-头部-链接">问题</a>
                <a href="#" className="中文-头部-链接">市场</a>
                <a href="#" className="中文-头部-链接">探索</a>
              </nav>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="中文-搜索 hidden md:block">
                <div className="relative">
                  <Search className="中文-搜索-图标 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="搜索或跳转到..."
                    className="中文-搜索-输入框 w-80"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
              <div className="中文-头像-中">
                <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-medium">
                  用
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="中文-容器 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 左侧边栏 */}
          <div className="lg:col-span-1">
            <div className="中文-侧边栏">
              <div className="中文-侧边栏-部分">
                <h3 className="中文-侧边栏-标题">最近仓库</h3>
                <div className="space-y-2">
                  {repositories.slice(0, 3).map((repo) => (
                    <a key={repo.id} href="#" className="中文-侧边栏-项目 flex items-center space-x-2">
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: getLanguageColor(repo.language) }}
                      />
                      <span className="text-ellipsis-1">{repo.name}</span>
                    </a>
                  ))}
                </div>
              </div>

              <div className="中文-侧边栏-部分">
                <h3 className="中文-侧边栏-标题">最近活动</h3>
                <div className="space-y-3">
                  {activities.map((activity) => (
                    <div key={activity.id} className="text-sm">
                      <div className="text-[#24292f] font-medium">{activity.message}</div>
                      <div className="text-[#656d76] text-xs">{activity.time}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* 主要内容区域 */}
          <div className="lg:col-span-3">
            {/* 欢迎部分 */}
            <div className="中文-主要 p-6 mb-6">
              <div className="flex items-center justify-between mb-4">
                <h1 className="text-2xl font-bold text-[#24292f]">欢迎来到 NextGen 2025</h1>
                <div className="flex items-center space-x-2">
                  <button className="中文-按钮-主要">
                    <Zap className="w-4 h-4 mr-2" />
                    新建仓库
                  </button>
                </div>
              </div>
              
              <p className="text-[#656d76] mb-6">
                AI原生数字生活操作系统 - 构建智慧工程、创作者经济与Web3生态的综合平台
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="中文-卡片 text-center p-4">
                  <Globe className="w-8 h-8 text-[#0969da] mx-auto mb-2" />
                  <h3 className="font-semibold text-[#24292f] mb-1">工程发现</h3>
                  <p className="text-sm text-[#656d76]">智慧建造与工程管理</p>
                </div>
                <div className="中文-卡片 text-center p-4">
                  <Users className="w-8 h-8 text-[#2ea043] mx-auto mb-2" />
                  <h3 className="font-semibold text-[#24292f] mb-1">创作者经济</h3>
                  <p className="text-sm text-[#656d76]">Web3创作与NFT生态</p>
                </div>
                <div className="中文-卡片 text-center p-4">
                  <Shield className="w-8 h-8 text-[#cf222e] mx-auto mb-2" />
                  <h3 className="font-semibold text-[#24292f] mb-1">安全合规</h3>
                  <p className="text-sm text-[#656d76]">国内法规完全合规</p>
                </div>
              </div>
            </div>

            {/* 仓库部分 */}
            <div className="中文-主要">
              <div className="border-b border-[#d0d7de] px-6 py-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-[#24292f]">仓库</h2>
                  <button className="中文-按钮-次要 text-sm">
                    <Search className="w-4 h-4 mr-2" />
                    查找仓库...
                  </button>
                </div>
              </div>

              <div className="中文-列表">
                {repositories.map((repo) => (
                  <div key={repo.id} className="中文-列表-项目">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="中文-列表-项目-标题">
                            <a href="#" className="text-[#0969da] hover:underline">{repo.name}</a>
                          </h3>
                          {repo.isPrivate && (
                            <span className="中文-标签-警告">私有</span>
                          )}
                        </div>
                        <p className="text-sm text-[#656d76] mb-3">{repo.description}</p>
                        <div className="flex items-center space-x-4 text-sm text-[#656d76]">
                          <div className="flex items-center space-x-1">
                            <div 
                              className="w-3 h-3 rounded-full" 
                              style={{ backgroundColor: getLanguageColor(repo.language) }}
                            />
                            <span>{repo.language}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Star className="w-4 h-4" />
                            <span>{repo.stars}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <GitFork className="w-4 h-4" />
                            <span>{repo.forks}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Eye className="w-4 h-4" />
                            <span>{repo.watchers}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="w-4 h-4" />
                            <span>更新于 {repo.updated}</span>
                          </div>
                        </div>
                      </div>
                      <button className="中文-按钮-次要 ml-4">
                        <Star className="w-4 h-4 mr-1" />
                        星标
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
