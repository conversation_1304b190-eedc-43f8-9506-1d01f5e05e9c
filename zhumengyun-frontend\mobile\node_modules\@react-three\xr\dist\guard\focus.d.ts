import { ReactNode } from 'react';
interface SessionVisibleProps {
    children?: ReactNode;
}
/**
 * Guard that only **shows** its children by toggling their visibility based on whether the current session is visible or not.
 * Typically used to hide/show content when operating system overlays are showing
 *
 * @param props ‎
 * #### `children?` - `ReactNode` The ReactNode elements to conditionally show.
 */
export declare function ShowIfSessionVisible({ children }: SessionVisibleProps): import("react/jsx-runtime").JSX.Element;
/**
 * Guard that only **renders** its children to the scene based on whether the current session is visible or not.
 * Typically used to hide/show content when operating system overlays are showing
 *
 * @param props ‎
 * #### `children?` - `ReactNode` The ReactNode elements to conditionally show.
 */
export declare function IfSessionVisible({ children }: SessionVisibleProps): import("react/jsx-runtime").JSX.Element | null;
export {};
