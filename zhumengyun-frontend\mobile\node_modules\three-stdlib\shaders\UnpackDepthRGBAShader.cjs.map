{"version": 3, "file": "UnpackDepthRGBAShader.cjs", "sources": ["../../src/shaders/UnpackDepthRGBAShader.ts"], "sourcesContent": ["/**\n * Unpack RGBA depth shader\n * - show RGBA encoded depth as monochrome color\n */\n\nexport const UnpackDepthRGBAShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    opacity: { value: 1.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float opacity;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    #include <packing>\n\n    void main() {\n\n    \tfloat depth = 1.0 - unpackRGBAToDepth( texture2D( tDiffuse, vUv ) );\n    \tgl_FragColor = vec4( vec3( depth ), opacity );\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";;AAKO,MAAM,wBAAwB;AAAA,EACnC,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,SAAS,EAAE,OAAO,EAAI;AAAA,EACxB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgB7B;;"}