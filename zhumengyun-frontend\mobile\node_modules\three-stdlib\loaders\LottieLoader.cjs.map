{"version": 3, "file": "LottieLoader.cjs", "sources": ["../../src/loaders/LottieLoader.js"], "sourcesContent": ["import { <PERSON><PERSON>oa<PERSON>, Loader, <PERSON>vasTexture, NearestFilter } from 'three'\nimport lottie from '../libs/lottie'\n\nclass LottieLoader extends Loader {\n  setQuality(value) {\n    this._quality = value\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const quality = this._quality || 1\n\n    const texture = new CanvasTexture()\n    texture.minFilter = NearestFilter\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setWithCredentials(this.withCredentials)\n\n    loader.load(\n      url,\n      function (text) {\n        const data = JSON.parse(text)\n\n        // bodymoving uses container.offetWidth and offsetHeight\n        // to define width/height\n\n        const container = document.createElement('div')\n        container.style.width = data.w + 'px'\n        container.style.height = data.h + 'px'\n        document.body.appendChild(container)\n\n        const animation = lottie.loadAnimation({\n          container: container,\n          animType: 'canvas',\n          loop: true,\n          autoplay: true,\n          animationData: data,\n          rendererSettings: { dpr: quality },\n        })\n\n        texture.animation = animation\n        texture.image = animation.container\n\n        animation.addEventListener('enterFrame', function () {\n          texture.needsUpdate = true\n        })\n\n        container.style.display = 'none'\n\n        if (onLoad !== undefined) {\n          onLoad(texture)\n        }\n      },\n      onProgress,\n      onError,\n    )\n\n    return texture\n  }\n}\n\nexport { LottieLoader }\n"], "names": ["Loader", "CanvasTexture", "NearestFilter", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;AAGA,MAAM,qBAAqBA,MAAAA,OAAO;AAAA,EAChC,WAAW,OAAO;AAChB,SAAK,WAAW;AAAA,EACjB;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,UAAU,KAAK,YAAY;AAEjC,UAAM,UAAU,IAAIC,oBAAe;AACnC,YAAQ,YAAYC,MAAa;AAEjC,UAAM,SAAS,IAAIC,iBAAW,KAAK,OAAO;AAC1C,WAAO,QAAQ,KAAK,IAAI;AACxB,WAAO,mBAAmB,KAAK,eAAe;AAE9C,WAAO;AAAA,MACL;AAAA,MACA,SAAU,MAAM;AACd,cAAM,OAAO,KAAK,MAAM,IAAI;AAK5B,cAAM,YAAY,SAAS,cAAc,KAAK;AAC9C,kBAAU,MAAM,QAAQ,KAAK,IAAI;AACjC,kBAAU,MAAM,SAAS,KAAK,IAAI;AAClC,iBAAS,KAAK,YAAY,SAAS;AAEnC,cAAM,YAAY,OAAO,cAAc;AAAA,UACrC;AAAA,UACA,UAAU;AAAA,UACV,MAAM;AAAA,UACN,UAAU;AAAA,UACV,eAAe;AAAA,UACf,kBAAkB,EAAE,KAAK,QAAS;AAAA,QAC5C,CAAS;AAED,gBAAQ,YAAY;AACpB,gBAAQ,QAAQ,UAAU;AAE1B,kBAAU,iBAAiB,cAAc,WAAY;AACnD,kBAAQ,cAAc;AAAA,QAChC,CAAS;AAED,kBAAU,MAAM,UAAU;AAE1B,YAAI,WAAW,QAAW;AACxB,iBAAO,OAAO;AAAA,QACf;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAED,WAAO;AAAA,EACR;AACH;;"}