import { ColorRepresentation } from 'three';
import { HandlesContext } from '../context.js';
import { HandlesProperties } from '../index.js';
import { RegisteredHandle } from '../registered.js';
export declare class PlaneScaleHandle extends RegisteredHandle {
    constructor(context: HandlesContext, tag: 'xy' | 'yz' | 'xz', tagPrefix?: string);
    bind(defaultColor: ColorRepresentation, defaultHoverColor: ColorRepresentation, config?: HandlesProperties): (() => void) | undefined;
}
