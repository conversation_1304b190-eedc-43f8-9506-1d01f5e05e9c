/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated by scripts/releases/set-version.js
 */

package com.facebook.react.modules.systeminfo

public object ReactNativeVersion {
  @JvmField
  public val VERSION: Map<String, Any?> = mapOf(
    "major" to 0,
    "minor" to 80,
    "patch" to 1,
    "prerelease" to null
  )
}
