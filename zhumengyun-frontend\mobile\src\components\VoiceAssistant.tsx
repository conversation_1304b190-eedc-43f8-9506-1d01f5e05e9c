'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface VoiceAssistantProps {
  currentPage: string
  onVoiceCommand: (command: string, params?: any) => void
}

interface VoiceCommand {
  command: string
  action: string
  params?: any
}

export default function VoiceAssistant({ currentPage, onVoiceCommand }: VoiceAssistantProps) {
  const [isListening, setIsListening] = useState(false)
  const [isActive, setIsActive] = useState(false)
  const [transcript, setTranscript] = useState('')
  const [response, setResponse] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  
  const recognitionRef = useRef<SpeechRecognition | null>(null)
  const synthRef = useRef<SpeechSynthesis | null>(null)

  // 初始化语音识别
  useEffect(() => {
    if (typeof window !== 'undefined' && 'webkitSpeechRecognition' in window) {
      const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition
      recognitionRef.current = new SpeechRecognition()
      
      recognitionRef.current.continuous = true
      recognitionRef.current.interimResults = true
      recognitionRef.current.lang = 'zh-CN'
      
      recognitionRef.current.onstart = () => {
        setIsListening(true)
      }
      
      recognitionRef.current.onend = () => {
        setIsListening(false)
      }
      
      recognitionRef.current.onresult = (event) => {
        let finalTranscript = ''
        for (let i = event.resultIndex; i < event.results.length; i++) {
          if (event.results[i].isFinal) {
            finalTranscript += event.results[i][0].transcript
          }
        }
        
        if (finalTranscript) {
          setTranscript(finalTranscript)
          processVoiceCommand(finalTranscript)
        }
      }
    }

    // 初始化语音合成
    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
      synthRef.current = window.speechSynthesis
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop()
      }
    }
  }, [])

  // 语音命令处理
  const processVoiceCommand = useCallback(async (command: string) => {
    setIsProcessing(true)
    
    const lowerCommand = command.toLowerCase()
    let response = ''
    let action = ''
    let params = {}

    // 根据当前页面提供场景感知的命令处理
    if (currentPage === '/') {
      // 首页（宇宙页面）命令
      if (lowerCommand.includes('下一个') || lowerCommand.includes('下一条')) {
        action = 'swipe_up'
        response = '好的，为您切换到下一个内容'
      } else if (lowerCommand.includes('上一个') || lowerCommand.includes('上一条')) {
        action = 'swipe_down'
        response = '好的，为您切换到上一个内容'
      } else if (lowerCommand.includes('点赞') || lowerCommand.includes('喜欢')) {
        action = 'like'
        response = '已为您点赞'
      } else if (lowerCommand.includes('分享')) {
        action = 'share'
        response = '正在为您打开分享菜单'
      } else if (lowerCommand.includes('评论')) {
        action = 'comment'
        response = '正在为您打开评论区'
      } else if (lowerCommand.includes('关注')) {
        action = 'follow'
        response = '已为您关注该创作者'
      } else if (lowerCommand.includes('收藏')) {
        action = 'bookmark'
        response = '已为您收藏该内容'
      } else if (lowerCommand.includes('暂停') || lowerCommand.includes('停止')) {
        action = 'pause'
        response = '已为您暂停播放'
      } else if (lowerCommand.includes('播放') || lowerCommand.includes('继续')) {
        action = 'play'
        response = '已为您继续播放'
      }
    } else if (currentPage === '/engineering') {
      // 发现页面（工程页面）命令
      if (lowerCommand.includes('搜索')) {
        const searchTerm = command.replace(/搜索|查找/g, '').trim()
        action = 'search'
        params = { query: searchTerm }
        response = `正在为您搜索"${searchTerm}"`
      } else if (lowerCommand.includes('筛选') || lowerCommand.includes('过滤')) {
        action = 'filter'
        response = '正在为您打开筛选菜单'
      }
    } else if (currentPage === '/creator') {
      // 创作页面命令
      if (lowerCommand.includes('开始创作') || lowerCommand.includes('新建项目')) {
        action = 'create_new'
        response = '正在为您创建新项目'
      } else if (lowerCommand.includes('保存')) {
        action = 'save'
        response = '正在为您保存当前内容'
      } else if (lowerCommand.includes('发布')) {
        action = 'publish'
        response = '正在为您发布内容'
      }
    } else if (currentPage === '/social') {
      // 消息页面命令
      if (lowerCommand.includes('发送消息')) {
        const message = command.replace(/发送消息|发消息/g, '').trim()
        action = 'send_message'
        params = { message }
        response = `正在为您发送消息："${message}"`
      } else if (lowerCommand.includes('查看消息') || lowerCommand.includes('打开消息')) {
        action = 'open_messages'
        response = '正在为您打开消息列表'
      }
    }

    // 全局命令
    if (!action) {
      if (lowerCommand.includes('回到首页') || lowerCommand.includes('首页')) {
        action = 'navigate'
        params = { path: '/' }
        response = '正在为您返回首页'
      } else if (lowerCommand.includes('发现页') || lowerCommand.includes('工程页')) {
        action = 'navigate'
        params = { path: '/engineering' }
        response = '正在为您打开发现页'
      } else if (lowerCommand.includes('创作页') || lowerCommand.includes('创作')) {
        action = 'navigate'
        params = { path: '/creator' }
        response = '正在为您打开创作页'
      } else if (lowerCommand.includes('消息页') || lowerCommand.includes('社交')) {
        action = 'navigate'
        params = { path: '/social' }
        response = '正在为您打开消息页'
      } else if (lowerCommand.includes('个人页') || lowerCommand.includes('我的')) {
        action = 'navigate'
        params = { path: '/profile' }
        response = '正在为您打开个人页'
      } else if (lowerCommand.includes('刷新')) {
        action = 'refresh'
        response = '正在为您刷新页面'
      } else if (lowerCommand.includes('帮助') || lowerCommand.includes('功能')) {
        action = 'help'
        response = '我是您的智能助手小智，可以帮您控制应用、搜索内容、发送消息等。试试说"下一个"、"点赞"、"搜索工程项目"等命令。'
      } else {
        response = '抱歉，我没有理解您的指令。您可以说"帮助"了解可用命令。'
      }
    }

    setResponse(response)
    
    // 执行命令
    if (action) {
      onVoiceCommand(action, params)
    }

    // 语音回复
    if (synthRef.current && response) {
      const utterance = new SpeechSynthesisUtterance(response)
      utterance.lang = 'zh-CN'
      utterance.rate = 1.2
      utterance.pitch = 1.1
      synthRef.current.speak(utterance)
    }

    setIsProcessing(false)
    
    // 3秒后清除响应
    setTimeout(() => {
      setResponse('')
      setTranscript('')
    }, 3000)
  }, [currentPage, onVoiceCommand])

  // 开始语音识别
  const startListening = useCallback(() => {
    if (recognitionRef.current && !isListening) {
      setIsActive(true)
      setTranscript('')
      setResponse('')
      recognitionRef.current.start()
    }
  }, [isListening])

  // 停止语音识别
  const stopListening = useCallback(() => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop()
      setIsActive(false)
    }
  }, [isListening])

  // 长按音量键唤醒（模拟）
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'F1' || (e.altKey && e.key === 'v')) {
        e.preventDefault()
        if (isActive) {
          stopListening()
        } else {
          startListening()
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isActive, startListening, stopListening])

  return (
    <>
      {/* 语音助手按钮 */}
      <motion.button
        onClick={isActive ? stopListening : startListening}
        className={`fixed bottom-24 right-4 z-50 w-14 h-14 rounded-full flex items-center justify-center shadow-lg transition-all duration-300 ${
          isActive 
            ? 'bg-gradient-to-r from-red-500 to-pink-500 scale-110' 
            : 'bg-gradient-to-r from-blue-500 to-purple-500'
        }`}
        whileTap={{ scale: 0.95 }}
        animate={isListening ? { scale: [1, 1.1, 1] } : {}}
        transition={{ repeat: isListening ? Infinity : 0, duration: 1 }}
      >
        <span className="text-white text-xl">
          {isListening ? '🎤' : '🎵'}
        </span>
      </motion.button>

      {/* 语音识别界面 */}
      <AnimatePresence>
        {isActive && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="fixed inset-0 z-40 flex items-center justify-center bg-black/80 backdrop-blur-sm"
          >
            <div className="bg-gradient-to-br from-blue-900/90 to-purple-900/90 backdrop-blur-lg rounded-3xl p-8 max-w-sm mx-4 text-center">
              {/* 语音波形动画 */}
              <div className="flex justify-center space-x-1 mb-6">
                {[...Array(5)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="w-1 bg-gradient-to-t from-blue-400 to-purple-400 rounded-full"
                    animate={isListening ? {
                      height: [20, 40, 20],
                      opacity: [0.5, 1, 0.5]
                    } : { height: 20, opacity: 0.5 }}
                    transition={{
                      repeat: isListening ? Infinity : 0,
                      duration: 0.8,
                      delay: i * 0.1
                    }}
                  />
                ))}
              </div>

              {/* 状态显示 */}
              <div className="text-white mb-4">
                <h3 className="text-xl font-bold mb-2">小智助手</h3>
                <p className="text-sm opacity-80">
                  {isListening ? '正在聆听...' : isProcessing ? '正在处理...' : '点击开始对话'}
                </p>
              </div>

              {/* 识别文本 */}
              {transcript && (
                <div className="bg-white/10 rounded-2xl p-3 mb-4">
                  <p className="text-white text-sm">{transcript}</p>
                </div>
              )}

              {/* 响应文本 */}
              {response && (
                <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-2xl p-3 mb-4">
                  <p className="text-white text-sm">{response}</p>
                </div>
              )}

              {/* 控制按钮 */}
              <div className="flex justify-center space-x-4">
                <button
                  onClick={stopListening}
                  className="px-6 py-2 bg-red-500/20 text-red-300 rounded-xl text-sm"
                >
                  结束
                </button>
                <button
                  onClick={startListening}
                  disabled={isListening}
                  className="px-6 py-2 bg-blue-500/20 text-blue-300 rounded-xl text-sm disabled:opacity-50"
                >
                  {isListening ? '聆听中...' : '开始'}
                </button>
              </div>

              {/* 提示文本 */}
              <p className="text-white/60 text-xs mt-4">
                试试说："下一个"、"点赞"、"搜索工程项目"
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}
