'use client'

import { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface UserProfile {
  interests: string[]
  profession: string
  learningNeeds: string[]
  preferredScenarios: string[]
  usagePreferences: {
    timeSlots: string[]
    contentTypes: string[]
    interactionStyle: 'visual' | 'audio' | 'mixed'
  }
  privacySettings: {
    dataSharing: boolean
    personalization: boolean
    notifications: boolean
  }
}

interface OnboardingStep {
  id: string
  title: string
  description: string
  component: React.ComponentType<any>
  icon: string
}

export default function UserOnboarding({ 
  isVisible, 
  onComplete 
}: { 
  isVisible: boolean
  onComplete: (profile: UserProfile) => void 
}) {
  const [currentStep, setCurrentStep] = useState(0)
  const [userProfile, setUserProfile] = useState<UserProfile>({
    interests: [],
    profession: '',
    learningNeeds: [],
    preferredScenarios: [],
    usagePreferences: {
      timeSlots: [],
      contentTypes: [],
      interactionStyle: 'mixed'
    },
    privacySettings: {
      dataSharing: true,
      personalization: true,
      notifications: true
    }
  })

  // 简化的引导步骤定义 (从8步减少到5步)
  const onboardingSteps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: '欢迎来到NextGen 2025',
      description: '让我们为您打造专属的智慧生活体验',
      component: WelcomeStep,
      icon: '🌟'
    },
    {
      id: 'profile',
      title: '个人画像',
      description: '告诉我们您的兴趣和职业背景',
      component: ProfileStep,
      icon: '👤'
    },
    {
      id: 'scenarios',
      title: '使用偏好',
      description: '选择您的主要使用场景和时间',
      component: ScenariosStep,
      icon: '🎯'
    },
    {
      id: 'privacy',
      title: '隐私设置',
      description: '快速设置数据和通知偏好',
      component: PrivacyStep,
      icon: '🔒'
    },
    {
      id: 'complete',
      title: '开始体验',
      description: '一切就绪，开始您的智慧之旅',
      component: CompleteStep,
      icon: '🚀'
    }
  ]

  const handleNext = useCallback(() => {
    if (currentStep < onboardingSteps.length - 1) {
      setCurrentStep(prev => prev + 1)
    } else {
      onComplete(userProfile)
    }
  }, [currentStep, onboardingSteps.length, userProfile, onComplete])

  const handlePrevious = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1)
    }
  }, [currentStep])

  const updateProfile = useCallback((updates: Partial<UserProfile>) => {
    setUserProfile(prev => ({ ...prev, ...updates }))
  }, [])

  if (!isVisible) return null

  const CurrentStepComponent = onboardingSteps[currentStep].component
  const progress = ((currentStep + 1) / onboardingSteps.length) * 100

  return (
    <div className="fixed inset-0 z-50 bg-black/90 backdrop-blur-lg flex items-center justify-center p-4 pb-20">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="w-full max-w-md bg-gradient-to-br from-gray-900 to-black rounded-3xl overflow-hidden max-h-[80vh] flex flex-col"
      >
        {/* 进度条 */}
        <div className="relative h-2 bg-gray-800">
          <motion.div
            className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 to-purple-500"
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>

        {/* 步骤指示器 */}
        <div className="flex justify-center items-center py-4 px-6">
          <div className="flex space-x-2">
            {onboardingSteps.map((step, index) => (
              <div
                key={step.id}
                className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                  index <= currentStep ? 'bg-blue-500' : 'bg-gray-600'
                }`}
              />
            ))}
          </div>
        </div>

        {/* 步骤内容 */}
        <div className="flex-1 overflow-y-auto px-6 pb-6">
          <div className="text-center mb-6">
            <div className="text-4xl mb-3">{onboardingSteps[currentStep].icon}</div>
            <h2 className="text-xl font-bold text-white mb-2">
              {onboardingSteps[currentStep].title}
            </h2>
            <p className="text-gray-400 text-sm">
              {onboardingSteps[currentStep].description}
            </p>
          </div>

          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <CurrentStepComponent
                userProfile={userProfile}
                updateProfile={updateProfile}
                onNext={handleNext}
                onPrevious={handlePrevious}
                isFirst={currentStep === 0}
                isLast={currentStep === onboardingSteps.length - 1}
              />
            </motion.div>
          </AnimatePresence>
        </div>
      </motion.div>
    </div>
  )
}

// 欢迎步骤组件
function WelcomeStep({ onNext }: any) {
  return (
    <div className="text-center space-y-6">
      <div className="space-y-4">
        <div className="text-6xl">🌟</div>
        <h3 className="text-lg font-bold text-white">开启智慧生活新体验</h3>
        <p className="text-gray-400 text-sm leading-relaxed">
          NextGen 2025 将为您提供个性化的AI助手、创作工具、社交体验和专业服务。
          让我们花几分钟时间了解您，为您定制专属体验。
        </p>
      </div>
      
      <div className="flex justify-end">
        <button
          onClick={onNext}
          className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:scale-105"
        >
          开始设置
        </button>
      </div>
    </div>
  )
}

// 合并的个人画像步骤组件
function ProfileStep({ userProfile, updateProfile, onNext, onPrevious }: any) {
  const [activeSection, setActiveSection] = useState<'interests' | 'profession' | 'learning'>('interests')

  const interests = [
    { id: 'technology', label: '科技创新', icon: '💻' },
    { id: 'design', label: '设计艺术', icon: '🎨' },
    { id: 'architecture', label: '建筑工程', icon: '🏗️' },
    { id: 'business', label: '商业管理', icon: '💼' },
    { id: 'education', label: '教育学习', icon: '📚' },
    { id: 'entertainment', label: '娱乐休闲', icon: '🎮' }
  ]

  const professions = [
    { id: 'engineer', label: '工程师', icon: '⚙️' },
    { id: 'designer', label: '设计师', icon: '🎨' },
    { id: 'architect', label: '建筑师', icon: '🏗️' },
    { id: 'manager', label: '管理者', icon: '👔' },
    { id: 'student', label: '学生', icon: '🎓' },
    { id: 'other', label: '其他', icon: '🤔' }
  ]

  const learningNeeds = [
    { id: 'technical_skills', label: '技术技能', icon: '💻' },
    { id: 'creative_skills', label: '创意技能', icon: '🎨' },
    { id: 'business_skills', label: '商业技能', icon: '📈' },
    { id: 'ai_knowledge', label: 'AI知识', icon: '🤖' },
    { id: 'web3_blockchain', label: 'Web3区块链', icon: '⛓️' },
    { id: 'personal_growth', label: '个人成长', icon: '🌱' }
  ]

  const toggleInterest = (interestId: string) => {
    const currentInterests = userProfile.interests || []
    const newInterests = currentInterests.includes(interestId)
      ? currentInterests.filter((id: string) => id !== interestId)
      : [...currentInterests, interestId]
    updateProfile({ interests: newInterests })
  }

  const toggleLearningNeed = (needId: string) => {
    const currentNeeds = userProfile.learningNeeds || []
    const newNeeds = currentNeeds.includes(needId)
      ? currentNeeds.filter((id: string) => id !== needId)
      : [...currentNeeds, needId]
    updateProfile({ learningNeeds: newNeeds })
  }

  const isComplete = userProfile.interests?.length > 0 && userProfile.profession && userProfile.learningNeeds?.length > 0

  return (
    <div className="space-y-4">
      {/* 分段选择器 */}
      <div className="flex bg-gray-800/50 rounded-lg p-1">
        {[
          { key: 'interests', label: '兴趣', icon: '🎯' },
          { key: 'profession', label: '职业', icon: '💼' },
          { key: 'learning', label: '学习', icon: '📚' }
        ].map((section) => (
          <button
            key={section.key}
            onClick={() => setActiveSection(section.key as any)}
            className={`flex-1 flex items-center justify-center space-x-1 py-2 rounded-md text-sm transition-colors ${
              activeSection === section.key
                ? 'bg-blue-500 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <span>{section.icon}</span>
            <span>{section.label}</span>
          </button>
        ))}
      </div>

      {/* 兴趣选择 */}
      {activeSection === 'interests' && (
        <div className="grid grid-cols-2 gap-3">
          {interests.map((interest) => (
            <button
              key={interest.id}
              onClick={() => toggleInterest(interest.id)}
              className={`p-3 rounded-lg border transition-all duration-200 ${
                userProfile.interests?.includes(interest.id)
                  ? 'border-blue-500 bg-blue-500/20 text-blue-400'
                  : 'border-gray-600 bg-gray-800/50 text-gray-400 hover:border-gray-500'
              }`}
            >
              <div className="text-xl mb-1">{interest.icon}</div>
              <div className="text-xs font-medium">{interest.label}</div>
            </button>
          ))}
        </div>
      )}

      {/* 职业选择 */}
      {activeSection === 'profession' && (
        <div className="grid grid-cols-2 gap-3">
          {professions.map((profession) => (
            <button
              key={profession.id}
              onClick={() => updateProfile({ profession: profession.id })}
              className={`p-3 rounded-lg border transition-all duration-200 ${
                userProfile.profession === profession.id
                  ? 'border-blue-500 bg-blue-500/20 text-blue-400'
                  : 'border-gray-600 bg-gray-800/50 text-gray-400 hover:border-gray-500'
              }`}
            >
              <div className="text-xl mb-1">{profession.icon}</div>
              <div className="text-xs font-medium">{profession.label}</div>
            </button>
          ))}
        </div>
      )}

      {/* 学习需求选择 */}
      {activeSection === 'learning' && (
        <div className="grid grid-cols-2 gap-3">
          {learningNeeds.map((need) => (
            <button
              key={need.id}
              onClick={() => toggleLearningNeed(need.id)}
              className={`p-3 rounded-lg border transition-all duration-200 ${
                userProfile.learningNeeds?.includes(need.id)
                  ? 'border-green-500 bg-green-500/20 text-green-400'
                  : 'border-gray-600 bg-gray-800/50 text-gray-400 hover:border-gray-500'
              }`}
            >
              <div className="text-xl mb-1">{need.icon}</div>
              <div className="text-xs font-medium">{need.label}</div>
            </button>
          ))}
        </div>
      )}

      <div className="flex justify-between pt-4">
        <button
          onClick={onPrevious}
          className="text-gray-400 px-4 py-2 rounded-lg hover:text-white transition-colors"
        >
          上一步
        </button>
        <button
          onClick={onNext}
          disabled={!isComplete}
          className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          下一步
        </button>
      </div>
    </div>
  )
}

// 简化的使用场景和偏好步骤组件

function ScenariosStep({ userProfile, updateProfile, onNext, onPrevious }: any) {
  const [activeSection, setActiveSection] = useState<'scenarios' | 'time' | 'interaction'>('scenarios')

  const scenarios = [
    { id: 'work', label: '工作办公', icon: '💼' },
    { id: 'learning', label: '学习提升', icon: '📚' },
    { id: 'entertainment', label: '娱乐休闲', icon: '🎮' },
    { id: 'creation', label: '内容创作', icon: '🎨' },
    { id: 'social', label: '社交网络', icon: '👥' }
  ]

  const timeSlots = [
    { id: 'morning', label: '早晨', icon: '🌅' },
    { id: 'afternoon', label: '下午', icon: '🌤️' },
    { id: 'evening', label: '晚上', icon: '🌆' },
    { id: 'night', label: '深夜', icon: '🌙' }
  ]

  const interactionStyles = [
    { id: 'visual', label: '视觉优先', icon: '👁️' },
    { id: 'audio', label: '语音优先', icon: '🎧' },
    { id: 'mixed', label: '混合模式', icon: '🔄' }
  ]

  const toggleScenario = (scenarioId: string) => {
    const currentScenarios = userProfile.preferredScenarios || []
    const newScenarios = currentScenarios.includes(scenarioId)
      ? currentScenarios.filter((id: string) => id !== scenarioId)
      : [...currentScenarios, scenarioId]
    updateProfile({ preferredScenarios: newScenarios })
  }

  const toggleTimeSlot = (slotId: string) => {
    const currentSlots = userProfile.usagePreferences?.timeSlots || []
    const newSlots = currentSlots.includes(slotId)
      ? currentSlots.filter((id: string) => id !== slotId)
      : [...currentSlots, slotId]
    updateProfile({
      usagePreferences: {
        ...userProfile.usagePreferences,
        timeSlots: newSlots
      }
    })
  }

  const setInteractionStyle = (style: string) => {
    updateProfile({
      usagePreferences: {
        ...userProfile.usagePreferences,
        interactionStyle: style
      }
    })
  }

  const isComplete = userProfile.preferredScenarios?.length > 0 &&
                    userProfile.usagePreferences?.timeSlots?.length > 0 &&
                    userProfile.usagePreferences?.interactionStyle

  return (
    <div className="space-y-4">
      {/* 分段选择器 */}
      <div className="flex bg-gray-800/50 rounded-lg p-1">
        {[
          { key: 'scenarios', label: '场景', icon: '🎯' },
          { key: 'time', label: '时间', icon: '⏰' },
          { key: 'interaction', label: '交互', icon: '🤝' }
        ].map((section) => (
          <button
            key={section.key}
            onClick={() => setActiveSection(section.key as any)}
            className={`flex-1 flex items-center justify-center space-x-1 py-2 rounded-md text-sm transition-colors ${
              activeSection === section.key
                ? 'bg-purple-500 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <span>{section.icon}</span>
            <span>{section.label}</span>
          </button>
        ))}
      </div>

      {/* 使用场景 */}
      {activeSection === 'scenarios' && (
        <div className="grid grid-cols-2 gap-3">
          {scenarios.map((scenario) => (
            <button
              key={scenario.id}
              onClick={() => toggleScenario(scenario.id)}
              className={`p-3 rounded-lg border transition-all duration-200 ${
                userProfile.preferredScenarios?.includes(scenario.id)
                  ? 'border-purple-500 bg-purple-500/20 text-purple-400'
                  : 'border-gray-600 bg-gray-800/50 text-gray-400 hover:border-gray-500'
              }`}
            >
              <div className="text-xl mb-1">{scenario.icon}</div>
              <div className="text-xs font-medium">{scenario.label}</div>
            </button>
          ))}
        </div>
      )}

      {/* 使用时间 */}
      {activeSection === 'time' && (
        <div className="grid grid-cols-2 gap-3">
          {timeSlots.map((slot) => (
            <button
              key={slot.id}
              onClick={() => toggleTimeSlot(slot.id)}
              className={`p-3 rounded-lg border transition-all duration-200 ${
                userProfile.usagePreferences?.timeSlots?.includes(slot.id)
                  ? 'border-blue-500 bg-blue-500/20 text-blue-400'
                  : 'border-gray-600 bg-gray-800/50 text-gray-400 hover:border-gray-500'
              }`}
            >
              <div className="text-xl mb-1">{slot.icon}</div>
              <div className="text-xs font-medium">{slot.label}</div>
            </button>
          ))}
        </div>
      )}

      {/* 交互方式 */}
      {activeSection === 'interaction' && (
        <div className="space-y-3">
          {interactionStyles.map((style) => (
            <button
              key={style.id}
              onClick={() => setInteractionStyle(style.id)}
              className={`w-full p-3 rounded-lg border transition-all duration-200 text-left ${
                userProfile.usagePreferences?.interactionStyle === style.id
                  ? 'border-green-500 bg-green-500/20 text-green-400'
                  : 'border-gray-600 bg-gray-800/50 text-gray-400 hover:border-gray-500'
              }`}
            >
              <div className="flex items-center space-x-3">
                <span className="text-xl">{style.icon}</span>
                <span className="font-medium">{style.label}</span>
              </div>
            </button>
          ))}
        </div>
      )}

      <div className="flex justify-between pt-4">
        <button
          onClick={onPrevious}
          className="text-gray-400 px-4 py-2 rounded-lg hover:text-white transition-colors"
        >
          上一步
        </button>
        <button
          onClick={onNext}
          disabled={!isComplete}
          className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          下一步
        </button>
      </div>
    </div>
  )
}

// 简化的隐私设置步骤组件

function PrivacyStep({ userProfile, updateProfile, onNext, onPrevious }: any) {
  const privacyOptions = [
    { id: 'personalization', title: '个性化推荐', icon: '🎯' },
    { id: 'notifications', title: '推送通知', icon: '🔔' },
    { id: 'dataSharing', title: '数据分析', icon: '📊' }
  ]

  const togglePrivacySetting = (settingId: string) => {
    updateProfile({
      privacySettings: {
        ...userProfile.privacySettings,
        [settingId]: !userProfile.privacySettings?.[settingId]
      }
    })
  }

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <div className="text-4xl mb-3">🔒</div>
        <p className="text-gray-400 text-sm">
          快速设置您的隐私偏好，随时可在设置中修改
        </p>
      </div>

      <div className="space-y-3">
        {privacyOptions.map((option) => (
          <div
            key={option.id}
            className="flex items-center justify-between p-4 rounded-lg bg-gray-800/50 border border-gray-600"
          >
            <div className="flex items-center space-x-3">
              <span className="text-xl">{option.icon}</span>
              <span className="text-white font-medium">{option.title}</span>
            </div>
            <button
              onClick={() => togglePrivacySetting(option.id)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                userProfile.privacySettings?.[option.id]
                  ? 'bg-blue-500'
                  : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  userProfile.privacySettings?.[option.id]
                    ? 'translate-x-6'
                    : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        ))}
      </div>

      <div className="flex justify-between pt-4">
        <button
          onClick={onPrevious}
          className="text-gray-400 px-4 py-2 rounded-lg hover:text-white transition-colors"
        >
          上一步
        </button>
        <button
          onClick={onNext}
          className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:scale-105"
        >
          完成设置
        </button>
      </div>
    </div>
  )
}

function CompleteStep({ userProfile, onNext }: any) {
  const getPersonalizedMessage = () => {
    const profession = userProfile.profession
    const interests = userProfile.interests || []

    if (profession === 'architect' || interests.includes('architecture')) {
      return '🏗️ 建筑设计工具和材料供应商网络已为您准备就绪'
    }
    if (profession === 'designer' || interests.includes('design')) {
      return '🎨 AI创作工具和设计模板库已为您准备就绪'
    }
    if (interests.includes('technology')) {
      return '💻 智能技术工具和学习资源已为您准备就绪'
    }
    return '🌟 个性化的智慧生活体验已为您准备就绪'
  }

  return (
    <div className="text-center space-y-6">
      <div className="space-y-4">
        <div className="text-6xl animate-bounce">🚀</div>
        <h3 className="text-xl font-bold text-white">一切就绪！</h3>
        <p className="text-gray-300 text-sm leading-relaxed">
          {getPersonalizedMessage()}
        </p>
      </div>

      <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl p-4 border border-blue-500/30">
        <div className="flex items-center justify-center space-x-4 text-sm">
          <div className="flex items-center space-x-1 text-blue-400">
            <span>✨</span>
            <span>AI助手</span>
          </div>
          <div className="flex items-center space-x-1 text-purple-400">
            <span>🎯</span>
            <span>个性化</span>
          </div>
          <div className="flex items-center space-x-1 text-green-400">
            <span>🚀</span>
            <span>智能推荐</span>
          </div>
        </div>
      </div>

      <button
        onClick={onNext}
        className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-4 rounded-xl font-bold text-lg transition-all duration-200 hover:scale-105 shadow-lg"
      >
        开始探索 NextGen 2025 🌟
      </button>
    </div>
  )
}
