/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<12f3227fae6949dbcac75f9a8dfd8cc4>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Animated/components/AnimatedText.js
 */

import type { AnimatedComponentType } from "../createAnimatedComponent";
import Text from "../../Text/Text";
import * as React from "react";
declare const $$AnimatedText: AnimatedComponentType<React.JSX.LibraryManagedAttributes<typeof Text, React.ComponentProps<typeof Text>>, React.ComponentRef<typeof Text>>;
declare type $$AnimatedText = typeof $$AnimatedText;
export default $$AnimatedText;
