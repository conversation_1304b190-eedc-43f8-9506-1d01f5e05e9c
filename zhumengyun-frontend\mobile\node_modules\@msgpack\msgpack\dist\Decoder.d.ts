import { ExtensionCodecType } from "./ExtensionCodec";
import { KeyDecoder } from "./CachedKeyDecoder";
export declare const DataViewIndexOutOfBoundsError: typeof Error;
export declare class Decoder<ContextType = undefined> {
    private readonly extensionCodec;
    private readonly context;
    private readonly maxStrLength;
    private readonly maxBinLength;
    private readonly maxArrayLength;
    private readonly maxMapLength;
    private readonly maxExtLength;
    private readonly keyDecoder;
    private totalPos;
    private pos;
    private view;
    private bytes;
    private headByte;
    private readonly stack;
    constructor(extensionCodec?: ExtensionCodecType<ContextType>, context?: ContextType, maxStrLength?: number, maxBinLength?: number, maxArrayLength?: number, maxMapLength?: number, maxExtLength?: number, keyDecoder?: KeyDecoder | null);
    private reinitializeState;
    private setBuffer;
    private appendBuffer;
    private hasRemaining;
    private createExtraByteError;
    /**
     * @throws {@link DecodeError}
     * @throws {@link RangeError}
     */
    decode(buffer: ArrayLike<number> | BufferSource): unknown;
    decodeMulti(buffer: ArrayLike<number> | BufferSource): Generator<unknown, void, unknown>;
    decodeAsync(stream: AsyncIterable<ArrayLike<number> | BufferSource>): Promise<unknown>;
    decodeArrayStream(stream: AsyncIterable<ArrayLike<number> | BufferSource>): AsyncGenerator<unknown, void, unknown>;
    decodeStream(stream: AsyncIterable<ArrayLike<number> | BufferSource>): AsyncGenerator<unknown, void, unknown>;
    private decodeMultiAsync;
    private doDecodeSync;
    private readHeadByte;
    private complete;
    private readArraySize;
    private pushMapState;
    private pushArrayState;
    private decodeUtf8String;
    private stateIsMapKey;
    private decodeBinary;
    private decodeExtension;
    private lookU8;
    private lookU16;
    private lookU32;
    private readU8;
    private readI8;
    private readU16;
    private readI16;
    private readU32;
    private readI32;
    private readU64;
    private readI64;
    private readF32;
    private readF64;
}
