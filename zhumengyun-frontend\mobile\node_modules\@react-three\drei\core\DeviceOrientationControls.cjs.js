"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("@react-three/fiber"),r=require("react"),n=require("three-stdlib");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function o(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var c=u(e),i=o(r);const l=i.forwardRef(((e,r)=>{const{camera:u,onChange:o,makeDefault:l,...s}=e,a=t.useThree((e=>e.camera)),f=t.useThree((e=>e.invalidate)),d=t.useThree((e=>e.get)),v=t.useThree((e=>e.set)),b=u||a,h=i.useMemo((()=>new n.DeviceOrientationControls(b)),[b]);return i.useEffect((()=>{const e=e=>{f(),o&&o(e)};return null==h||null==h.addEventListener||h.addEventListener("change",e),()=>null==h||null==h.removeEventListener?void 0:h.removeEventListener("change",e)}),[o,h,f]),t.useFrame((()=>null==h?void 0:h.update()),-1),i.useEffect((()=>{const e=h;return null==e||e.connect(),()=>null==e?void 0:e.dispose()}),[h]),i.useEffect((()=>{if(l){const e=d().controls;return v({controls:h}),()=>v({controls:e})}}),[l,h]),h?i.createElement("primitive",c.default({ref:r,object:h},s)):null}));exports.DeviceOrientationControls=l;
