import { GrabPointerOptions, LinesPointerOptions, Pointer, RayPointerOptions, TouchPointerOptions } from '@pmndrs/pointer-events';
import { PointerCursorModelOptions, PointerRayModelOptions } from '@pmndrs/xr/internals';
import { ReactNode, RefObject } from 'react';
import { Mesh, Object3D } from 'three';
export { isXRInputSourceState, type XRInputSourceState } from '@pmndrs/xr/internals';
/**
 * Component for combining multiple Pointers into one so that only one pointer is active at a time
 *
 * @param props
 * @param props.children? - `Pointer` components to combine
 */
export declare function CombinedPointer({ children }: {
    children?: ReactNode;
}): import("react/jsx-runtime").JSX.Element;
/**
 * Hook for creating a grab pointer
 */
export declare function useGrabPointer(spaceRef: RefObject<Object3D | null>, pointerState: any, currentOptions?: GrabPointerOptions & {
    makeDefault?: boolean;
}, pointerType?: string): Pointer;
/**
 * Hook for creating a ray pointer
 */
export declare function useRayPointer(spaceRef: RefObject<Object3D | null>, pointerState: any, currentOptions?: RayPointerOptions & {
    makeDefault?: boolean;
}, pointerType?: string): Pointer;
/**
 * Hook for creating a ray pointer
 */
export declare function useLinesPointer(spaceRef: RefObject<Object3D | null>, pointerState: any, currentOptions?: LinesPointerOptions & {
    makeDefault?: boolean;
}, pointerType?: string): Pointer;
/**
 * Hook for creating a touch pointer
 */
export declare function useTouchPointer(spaceRef: RefObject<Object3D | null>, pointerState: any, currentOptions?: TouchPointerOptions & {
    makeDefault?: boolean;
}, pointerType?: string): Pointer;
/**
 * Component for rendering a ray for a pointer
 * @param props
 * #### `materialClass` - Material to use for the ray
 * #### `pointer` - Pointer to use for the ray
 * #### `renderOrder` - Render order for the ray
 * @function
 */
export declare const PointerRayModel: import("react").ForwardRefExoticComponent<PointerRayModelOptions & {
    pointer: Pointer;
} & import("react").RefAttributes<Mesh<import("three").BufferGeometry<import("three").NormalBufferAttributes>, import("three").Material | import("three").Material[], import("three").Object3DEventMap>>>;
/**
 * Component for rendering a cursor as a pointer
 *
 * @param props
 * #### `materialClass` - Class of the material to use for the cursor
 * #### `pointer` - Pointer to use for the cursor
 * #### `renderOrder` - Render order for the cursor
 * @function
 */
export declare const PointerCursorModel: import("react").ForwardRefExoticComponent<PointerCursorModelOptions & {
    pointer: Pointer;
} & import("react").RefAttributes<Mesh<import("three").BufferGeometry<import("three").NormalBufferAttributes>, import("three").Material | import("three").Material[], import("three").Object3DEventMap>>>;
/**
 * Hook for binding the xr session events such as `selectstart` to the provided pointer down/up events
 */
export declare function usePointerXRInputSourceEvents(pointer: Pointer, inputSource: XRInputSource, event: 'select' | 'squeeze', missingEvents: ReadonlyArray<XRInputSourceEvent>): void;
