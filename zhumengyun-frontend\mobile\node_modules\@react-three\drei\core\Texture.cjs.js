"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("three"),t=require("@react-three/fiber");function u(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var u=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,u.get?u:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var n=u(e);const o=e=>e===Object(e)&&!Array.isArray(e)&&"function"!=typeof e;function a(u,n){const a=t.useThree((e=>e.gl)),c=t.useLoader(r.Texture<PERSON>oader,o(u)?Object.values(u):u);e.useLayoutEffect((()=>{null==n||n(c)}),[n]),e.useEffect((()=>{if("initTexture"in a){let e=[];Array.isArray(c)?e=c:c instanceof r.Texture?e=[c]:o(c)&&(e=Object.values(c)),e.forEach((e=>{e instanceof r.Texture&&a.initTexture(e)}))}}),[a,c]);return e.useMemo((()=>{if(o(u)){const e={};let r=0;for(const t in u)e[t]=c[r++];return e}return c}),[u,c])}a.preload=e=>t.useLoader.preload(r.TextureLoader,e),a.clear=e=>t.useLoader.clear(r.TextureLoader,e);exports.IsObject=o,exports.Texture=({children:e,input:r,onLoad:t})=>{const u=a(r,t);return n.createElement(n.Fragment,null,null==e?void 0:e(u))},exports.useTexture=a;
