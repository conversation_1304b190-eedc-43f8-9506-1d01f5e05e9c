<p align="center">
    <img height="60px" width="60px" src="https://meta-quest.github.io/immersive-web-emulation-runtime/iwer-text.svg" />
    <h1 align="center">Immersive Web Emulation Runtime</h1>
</p>

<p align="center">
    <a href="https://www.npmjs.com/package/iwer"><img src="https://badgen.net/npm/v/iwer/?icon=npm&color=orange" alt="npm version" /></a>
    <a href="https://www.npmjs.com/package/iwer"><img src="https://badgen.net/npm/dt/iwer" alt="npm download" /></a>
    <a href="https://www.typescriptlang.org/"><img src="https://badgen.net/badge/icon/typescript/?icon=typescript&label=lang" alt="language" /></a>
    <a href="https://raw.githubusercontent.com/meta-quest/immersive-web-emulation-runtime/main/LICENSE"><img src="https://badgen.net/github/license/meta-quest/immersive-web-emulation-runtime/" alt="license" /></a>
</p>

The Immersive Web Emulation Runtime (IWER) is a TypeScript-based tool designed to enable the running of WebXR applications in modern browsers without requiring native WebXR support. By emulating the WebXR Device API, IWER provides developers with the ability to test and run WebXR projects across a wide range of devices, ensuring compatibility and enhancing the development process.

## Documentation

For detailed information about using IWER, including concepts, guides, and API references, please visit our documentation site:

- [IWER Documentation](https://meta-quest.github.io/immersive-web-emulation-runtime)

## License

IWER is licensed under the MIT License. For more details, see the [LICENSE](https://github.com/meta-quest/immersive-web-emulation-runtime/blob/main/LICENSE) file in this repository.

## Contributing

Your contributions are welcome! Please feel free to submit issues and pull requests. Before contributing, make sure to review our [Contributing Guidelines](https://github.com/meta-quest/immersive-web-emulation-runtime/blob/main/CONTRIBUTING.md) and [Code of Conduct](https://github.com/meta-quest/immersive-web-emulation-runtime/blob/main/CODE_OF_CONDUCT.md).
