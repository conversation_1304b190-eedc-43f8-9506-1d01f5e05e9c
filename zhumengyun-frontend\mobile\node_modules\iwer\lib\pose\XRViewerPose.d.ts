/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_VIEWER_POSE } from '../private.js';
import { XRPose } from './XRPose.js';
import { XRRigidTransform } from '../primitives/XRRigidTransform.js';
import { XRView } from '../views/XRView.js';
export declare class XRViewerPose extends XRPose {
    [P_VIEWER_POSE]: {
        views: readonly XRView[];
    };
    constructor(transform: XRRigidTransform, views: XRView[], emulatedPosition?: boolean, linearVelocity?: DOMPointReadOnly | undefined, angularVelocity?: DOMPointReadOnly | undefined);
    get views(): readonly XRView[];
}
//# sourceMappingURL=XRViewerPose.d.ts.map