'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface Certification {
  id: string
  name: string
  category: string
  level: '初级' | '中级' | '高级' | '专家'
  status: 'active' | 'expired' | 'pending' | 'rejected'
  issueDate: Date
  expiryDate: Date
  credentialId: string
  skills: string[]
  description: string
  cost: number
  benefits: string[]
}

interface SkillCertificationProps {
  userId: string
}

export default function SkillCertification({ userId }: SkillCertificationProps) {
  const [certifications, setCertifications] = useState<Certification[]>([])
  const [availableCerts, setAvailableCerts] = useState<Certification[]>([])
  const [loading, setLoading] = useState(true)
  const [showApplyModal, setShowApplyModal] = useState(false)
  const [selectedCert, setSelectedCert] = useState<Certification | null>(null)
  const [activeTab, setActiveTab] = useState<'my' | 'available'>('my')

  useEffect(() => {
    loadCertifications()
  }, [userId])

  const loadCertifications = async () => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const myCerts: Certification[] = [
        {
          id: '1',
          name: '高级建筑师',
          category: '建筑设计',
          level: '高级',
          status: 'active',
          issueDate: new Date('2024-01-15'),
          expiryDate: new Date('2026-01-15'),
          credentialId: 'ARCH-2024-001',
          skills: ['建筑设计', 'BIM建模', '项目管理'],
          description: '具备高级建筑设计能力，能够独立完成大型建筑项目的设计工作',
          cost: 2000,
          benefits: ['提升服务定价', '获得专家标识', '优先推荐']
        },
        {
          id: '2',
          name: 'BIM专家认证',
          category: '技术工具',
          level: '专家',
          status: 'active',
          issueDate: new Date('2024-03-20'),
          expiryDate: new Date('2025-03-20'),
          credentialId: 'BIM-2024-002',
          skills: ['Revit', 'AutoCAD', '3D建模'],
          description: 'BIM技术专家，精通各类BIM软件和建模技术',
          cost: 1500,
          benefits: ['技术专家标识', '高级工具权限', '技术咨询资格']
        }
      ]

      const availableCerts: Certification[] = [
        {
          id: '3',
          name: '智慧城市规划师',
          category: '城市规划',
          level: '高级',
          status: 'pending',
          issueDate: new Date(),
          expiryDate: new Date(),
          credentialId: '',
          skills: ['城市规划', '智慧城市', 'IoT集成'],
          description: '智慧城市规划专家认证，掌握现代城市规划理念和技术',
          cost: 3000,
          benefits: ['专家级定价权', '政府项目优先权', '行业影响力提升']
        },
        {
          id: '4',
          name: '绿色建筑专家',
          category: '可持续发展',
          level: '中级',
          status: 'pending',
          issueDate: new Date(),
          expiryDate: new Date(),
          credentialId: '',
          skills: ['绿色建筑', '节能设计', '环保材料'],
          description: '绿色建筑设计专家，专注于可持续建筑设计',
          cost: 1800,
          benefits: ['绿色项目优先权', '环保标识', '政策补贴资格']
        }
      ]

      setCertifications(myCerts)
      setAvailableCerts(availableCerts)
    } catch (error) {
      console.error('加载认证数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleApplyCertification = async (cert: Certification) => {
    try {
      // 模拟申请认证
      alert(`正在申请${cert.name}认证，费用：¥${cert.cost}`)
      setShowApplyModal(false)
      
      // 更新状态为申请中
      const updatedCert = { ...cert, status: 'pending' as const }
      setCertifications(prev => [...prev, updatedCert])
      setAvailableCerts(prev => prev.filter(c => c.id !== cert.id))
    } catch (error) {
      console.error('申请认证失败:', error)
      alert('申请失败，请稍后重试')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'expired': return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'pending': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'rejected': return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '有效'
      case 'expired': return '已过期'
      case 'pending': return '审核中'
      case 'rejected': return '已拒绝'
      default: return '未知'
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case '初级': return 'from-blue-500 to-cyan-500'
      case '中级': return 'from-green-500 to-emerald-500'
      case '高级': return 'from-yellow-500 to-orange-500'
      case '专家': return 'from-purple-500 to-pink-500'
      default: return 'from-gray-500 to-gray-600'
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-8 bg-white/20 rounded mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-32 bg-white/10 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 标签页导航 */}
      <div className="flex space-x-1 bg-white/10 rounded-lg p-1">
        {[
          { key: 'my', label: '我的认证', count: certifications.length },
          { key: 'available', label: '可申请认证', count: availableCerts.length }
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === tab.key
                ? 'bg-blue-500 text-white'
                : 'text-white/70 hover:text-white'
            }`}
          >
            <span>{tab.label}</span>
            <span className="bg-white/20 rounded-full px-2 py-1 text-xs">{tab.count}</span>
          </button>
        ))}
      </div>

      {/* 我的认证 */}
      {activeTab === 'my' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          {certifications.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📜</div>
              <p className="text-white text-lg mb-2">暂无认证</p>
              <p className="text-gray-400 text-sm">申请专业认证，提升您的服务价值</p>
              <button
                onClick={() => setActiveTab('available')}
                className="mt-4 px-6 py-2 bg-blue-500 rounded-lg text-white font-medium"
              >
                浏览可申请认证
              </button>
            </div>
          ) : (
            certifications.map((cert, index) => (
              <motion.div
                key={cert.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-bold text-white">{cert.name}</h3>
                      <div className={`px-3 py-1 bg-gradient-to-r ${getLevelColor(cert.level)} rounded-full`}>
                        <span className="text-white text-xs font-bold">{cert.level}</span>
                      </div>
                    </div>
                    <p className="text-gray-400 text-sm mb-2">{cert.category}</p>
                    <p className="text-gray-300 text-sm">{cert.description}</p>
                  </div>
                  <div className={`px-3 py-1 rounded-full border text-xs font-medium ${getStatusColor(cert.status)}`}>
                    {getStatusText(cert.status)}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <div className="text-xs text-gray-400 mb-1">认证编号</div>
                    <div className="text-white font-mono text-sm">{cert.credentialId}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-400 mb-1">有效期至</div>
                    <div className="text-white text-sm">{cert.expiryDate.toLocaleDateString()}</div>
                  </div>
                </div>

                <div className="mb-4">
                  <div className="text-xs text-gray-400 mb-2">认证技能</div>
                  <div className="flex flex-wrap gap-2">
                    {cert.skills.map((skill, skillIndex) => (
                      <span
                        key={skillIndex}
                        className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded-lg text-xs"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>

                <div>
                  <div className="text-xs text-gray-400 mb-2">认证权益</div>
                  <div className="space-y-1">
                    {cert.benefits.map((benefit, benefitIndex) => (
                      <div key={benefitIndex} className="flex items-center space-x-2">
                        <span className="text-green-400 text-xs">✓</span>
                        <span className="text-gray-300 text-xs">{benefit}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </motion.div>
      )}

      {/* 可申请认证 */}
      {activeTab === 'available' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          {availableCerts.map((cert, index) => (
            <motion.div
              key={cert.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-bold text-white">{cert.name}</h3>
                    <div className={`px-3 py-1 bg-gradient-to-r ${getLevelColor(cert.level)} rounded-full`}>
                      <span className="text-white text-xs font-bold">{cert.level}</span>
                    </div>
                  </div>
                  <p className="text-gray-400 text-sm mb-2">{cert.category}</p>
                  <p className="text-gray-300 text-sm">{cert.description}</p>
                </div>
                <div className="text-right">
                  <div className="text-green-400 text-xl font-bold">¥{cert.cost}</div>
                  <div className="text-gray-400 text-xs">认证费用</div>
                </div>
              </div>

              <div className="mb-4">
                <div className="text-xs text-gray-400 mb-2">所需技能</div>
                <div className="flex flex-wrap gap-2">
                  {cert.skills.map((skill, skillIndex) => (
                    <span
                      key={skillIndex}
                      className="px-2 py-1 bg-purple-500/20 text-purple-300 rounded-lg text-xs"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>

              <div className="mb-4">
                <div className="text-xs text-gray-400 mb-2">认证权益</div>
                <div className="space-y-1">
                  {cert.benefits.map((benefit, benefitIndex) => (
                    <div key={benefitIndex} className="flex items-center space-x-2">
                      <span className="text-green-400 text-xs">✓</span>
                      <span className="text-gray-300 text-xs">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>

              <button
                onClick={() => {
                  setSelectedCert(cert)
                  setShowApplyModal(true)
                }}
                className="w-full py-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl text-white font-medium hover:shadow-lg transition-all"
              >
                申请认证
              </button>
            </motion.div>
          ))}
        </motion.div>
      )}

      {/* 申请认证弹窗 */}
      <AnimatePresence>
        {showApplyModal && selectedCert && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4"
            onClick={() => setShowApplyModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-blue-500/30"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center mb-6">
                <div className="text-4xl mb-4">📜</div>
                <h3 className="text-xl font-bold text-white mb-2">申请认证</h3>
                <p className="text-gray-400 text-sm">{selectedCert.name}</p>
              </div>

              <div className="space-y-4 mb-6">
                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-blue-200 font-medium">认证费用</span>
                    <span className="text-green-400 font-bold">¥{selectedCert.cost}</span>
                  </div>
                  <div className="text-blue-100 text-xs">
                    <p>• 费用包含认证审核和证书颁发</p>
                    <p>• 认证有效期2年，到期可续费</p>
                    <p>• 支持积分抵扣部分费用</p>
                  </div>
                </div>

                <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                  <div className="text-yellow-200 text-xs">
                    <p className="font-medium mb-1">申请须知：</p>
                    <p>• 需要提供相关技能证明材料</p>
                    <p>• 审核周期为5-10个工作日</p>
                    <p>• 认证费用在审核通过后收取</p>
                    <p>• 如审核不通过，费用全额退还</p>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowApplyModal(false)}
                  className="flex-1 py-3 bg-gray-700 rounded-xl text-white font-medium"
                >
                  取消
                </button>
                <button
                  onClick={() => handleApplyCertification(selectedCert)}
                  className="flex-1 py-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl text-white font-medium"
                >
                  确认申请
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
