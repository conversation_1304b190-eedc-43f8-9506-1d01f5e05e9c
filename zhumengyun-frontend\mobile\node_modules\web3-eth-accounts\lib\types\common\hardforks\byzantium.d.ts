declare const _default: {
    name: string;
    comment: string;
    url: string;
    status: string;
    gasConfig: {};
    gasPrices: {
        modexpGquaddivisor: {
            v: number;
            d: string;
        };
        ecAdd: {
            v: number;
            d: string;
        };
        ecMul: {
            v: number;
            d: string;
        };
        ecPairing: {
            v: number;
            d: string;
        };
        ecPairingWord: {
            v: number;
            d: string;
        };
        revert: {
            v: number;
            d: string;
        };
        staticcall: {
            v: number;
            d: string;
        };
        returndatasize: {
            v: number;
            d: string;
        };
        returndatacopy: {
            v: number;
            d: string;
        };
    };
    vm: {};
    pow: {
        minerReward: {
            v: string;
            d: string;
        };
        difficultyBombDelay: {
            v: number;
            d: string;
        };
    };
};
export default _default;
//# sourceMappingURL=byzantium.d.ts.map