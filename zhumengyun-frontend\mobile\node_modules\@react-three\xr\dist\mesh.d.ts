import { BufferGeometry, Mesh } from 'three';
/**
 * Component for rendering a mesh for the XRMesh based on the detected mesh geometry
 *
 * @param props
 * Accepts the same props as a ThreeJs [Mesh](https://threejs.org/docs/#api/en/objects/Mesh)
 * @function
 */
export declare const XRMeshModel: import("react").ForwardRefExoticComponent<Omit<import("@react-three/fiber/dist/declarations/src/core/utils.js").Mutable<import("@react-three/fiber/dist/declarations/src/core/utils.js").Overwrite<Partial<import("@react-three/fiber/dist/declarations/src/core/utils.js").Overwrite<Mesh<BufferGeometry<import("three").NormalBufferAttributes>, import("three").Material | import("three").Material[], import("three").Object3DEventMap>, import("@react-three/fiber").MathProps<Mesh<BufferGeometry<import("three").NormalBufferAttributes>, import("three").Material | import("three").Material[], import("three").Object3DEventMap>> & import("@react-three/fiber").ReactProps<Mesh<BufferGeometry<import("three").NormalBufferAttributes>, import("three").Material | import("three").Material[], import("three").Object3DEventMap>> & Partial<import("@react-three/fiber").EventHandlers>>>, Omit<import("@react-three/fiber").InstanceProps<Mesh<BufferGeometry<import("three").NormalBufferAttributes>, import("three").Material | import("three").Material[], import("three").Object3DEventMap>, typeof Mesh>, "object">>> & {
    mesh: XRMesh;
}, "ref"> & import("react").RefAttributes<Mesh<BufferGeometry<import("three").NormalBufferAttributes>, import("three").Material | import("three").Material[], import("three").Object3DEventMap>>>;
/**
 * Hook for getting all detected meshes with the provided semantic label
 */
export declare function useXRMeshes(semanticLabel?: string): readonly XRMesh[];
/**
 * Hook for getting the geometry from the detected mesh
 *
 * @param mesh the detected mesh
 * @param disposeBuffer allows to disable auto disposing the geometry buffer
 */
export declare function useXRMeshGeometry(mesh: XRMesh, disposeBuffer?: boolean): BufferGeometry;
