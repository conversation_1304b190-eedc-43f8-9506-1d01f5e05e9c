import { <PERSON><PERSON><PERSON>, Matrix4, <PERSON><PERSON>rn<PERSON>, Vector3, Vector3Tuple } from 'three';
import { Axis, HandleTransformState } from '../state.js';
import { HandleOptions, HandleTransformOptions } from '../store.js';
export type BaseHandleStoreData = {
    initialTargetQuaternion: Quaternion;
    initialTargetRotation: Euler;
    initialTargetPosition: Vector3;
    initialTargetScale: Vector3;
};
export declare function computeHandleTransformState(time: number, pointerAmount: number, targetWorldMatrix: Matrix4, storeData: BaseHandleStoreData, targetParentWorldMatrix: Matrix4 | undefined, options: HandleOptions<unknown> & {
    translate?: HandleTransformOptions;
}): HandleTransformState;
export declare function getDeltaQuaternionOnAxis(normalizedAxis: Vector3, from: Quaternion, to: Quaternion): number;
export declare function applyTransformOptionsToRotation(currentRotation: Quaternion, initialRotation: <PERSON>ule<PERSON>, options: Exclude<HandleTransformOptions, boolean | Array<Vector3Tuple | Vector3> | Axis>): Euler;
export declare function projectOntoSpace(projectRays: boolean | undefined, space: Array<Vector3>, initialWorldPoint: Vector3, worldPointerOrigin: Vector3, worldPoint: Vector3, worldDirection: Vector3 | undefined): void;
export declare function addSpaceFromTransformOptions(target: Array<Vector3>, parentWorldQuaternion: Quaternion, initialLocalRotation: Euler, options: HandleTransformOptions, type: 'translate' | 'rotate' | 'scale'): void;
/**
 * finds the intersection between the given axis (infinite line) and another infinite line provided with point and direction
 */
export declare function projectOntoAxis(initialWorldPoint: Vector3, axis: Vector3, worldPointerOrigin: Vector3, worldPoint: Vector3, worldDirection: Vector3 | undefined): void;
export declare function projectPointOntoAxis(target: Vector3, axisOrigin: Vector3, axisNormal: Vector3): void;
export declare function projectPointOntoNormal(point: Vector3, normal: Vector3): void;
