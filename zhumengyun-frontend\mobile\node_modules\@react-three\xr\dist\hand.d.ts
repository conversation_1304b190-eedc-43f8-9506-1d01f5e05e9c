import { XRHandModelOptions } from '@pmndrs/xr/internals';
import { ReactNode } from 'react';
import { Object3D } from 'three';
export type { XRHandModelOptions };
/**
 * Component for rendering a 3D model for the XRHand
 *
 * @param props
 * #### `colorWrite` - Configures color writing
 * #### `renderOrder` - Configures the render order of the model
 * @function
 */
export declare const XRHandModel: import("react").ForwardRefExoticComponent<XRHandModelOptions & import("react").RefAttributes<Object3D<import("three").Object3DEventMap>>>;
/**
 * Component for placing content in the hand anchored at a specific joint such as the index finger tip.
 *
 * @param props
 * #### `joint` - [XRHandJoint](https://developer.mozilla.org/en-US/docs/Web/API/XRHand#hand_joints) Is the name of the joint where content should be placed (e.g. `"wrist"`)
 * #### `children` - Components to be placed inside the joint (e.g. For visualizing a tooltip over the index finger tip)
 *
 * @function
 * @deprecated use `<XRSpace space="wrist">` instead of `<XRHandJoint joint="wrist">`
 */
export declare const XRHandJoint: import("react").ForwardRefExoticComponent<{
    joint: XRHandJoint;
    children?: ReactNode;
} & import("react").RefAttributes<Object3D<import("three").Object3DEventMap>>>;
