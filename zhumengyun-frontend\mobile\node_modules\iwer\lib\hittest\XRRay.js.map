{"version": 3, "file": "XRRay.js", "sourceRoot": "", "sources": ["../../src/hittest/XRRay.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAE7C,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AAErE,MAAM,gBAAgB;IACrB,YACQ,IAAY,CAAC,EACb,IAAY,CAAC,EACb,IAAY,CAAC,EACb,IAAY,CAAC;QAHb,MAAC,GAAD,CAAC,CAAY;QACb,MAAC,GAAD,CAAC,CAAY;QACb,MAAC,GAAD,CAAC,CAAY;QACb,MAAC,GAAD,CAAC,CAAY;IAClB,CAAC;CACJ;AAED,MAAM,OAAO,KAAK;IAOjB,YACC,MAAwC,EACxC,SAAwB;QAExB,MAAM,OAAO,GAAiB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACzD,MAAM,UAAU,GAAiB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAE7D,IAAI,MAAM,YAAY,gBAAgB,EAAE;YACvC,MAAM,SAAS,GAAG,MAAM,CAAC;YACzB,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;YAChC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAC1B,IAAI,CAAC,MAAM,EAAE,EACb,OAAO,CAAC,CAAE,EACV,OAAO,CAAC,CAAE,EACV,OAAO,CAAC,CAAE,EACV,OAAO,CAAC,CAAE,CACV,CAAC;YACF,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAC7B,IAAI,CAAC,MAAM,EAAE,EACb,UAAU,CAAC,CAAE,EACb,UAAU,CAAC,CAAE,EACb,UAAU,CAAC,CAAE,EACb,UAAU,CAAC,CAAE,CACb,CAAC;YACF,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;YACnD,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;YACzD,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC1B,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC1B,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC1B,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC1B,UAAU,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YAChC,UAAU,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YAChC,UAAU,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YAChC,UAAU,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;SAChC;aAAM;YACN,IAAI,MAAM,EAAE;gBACX,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;gBACrB,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;gBACrB,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;gBACrB,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;aACrB;YACD,IAAI,SAAS,EAAE;gBACd,IACC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC;oBAC7D,SAAS,CAAC,CAAC,KAAK,CAAC,EAChB;oBACD,MAAM,IAAI,YAAY,CACrB,4CAA4C,EAC5C,WAAW,CACX,CAAC;iBACF;gBACD,UAAU,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;gBAC3B,UAAU,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;gBAC3B,UAAU,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;gBAC3B,UAAU,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;aAC3B;SACD;QAED,MAAM,MAAM,GACX,IAAI,CAAC,IAAI,CACR,UAAU,CAAC,CAAE,GAAG,UAAU,CAAC,CAAE;YAC5B,UAAU,CAAC,CAAE,GAAG,UAAU,CAAC,CAAE;YAC7B,UAAU,CAAC,CAAE,GAAG,UAAU,CAAC,CAAE,CAC9B,IAAI,CAAC,CAAC;QACR,UAAU,CAAC,CAAC,GAAG,UAAU,CAAC,CAAE,GAAG,MAAM,CAAC;QACtC,UAAU,CAAC,CAAC,GAAG,UAAU,CAAC,CAAE,GAAG,MAAM,CAAC;QACtC,UAAU,CAAC,CAAC,GAAG,UAAU,CAAC,CAAE,GAAG,MAAM,CAAC;QAEtC,IAAI,CAAC,KAAK,CAAC,GAAG;YACb,MAAM,EAAE,IAAI,gBAAgB,CAC3B,OAAO,CAAC,CAAE,EACV,OAAO,CAAC,CAAE,EACV,OAAO,CAAC,CAAE,EACV,OAAO,CAAC,CAAE,CACV;YACD,SAAS,EAAE,IAAI,gBAAgB,CAC9B,UAAU,CAAC,CAAE,EACb,UAAU,CAAC,CAAE,EACb,UAAU,CAAC,CAAE,EACb,UAAU,CAAC,CAAE,CACb;YACD,MAAM,EAAE,IAAI;SACZ,CAAC;IACH,CAAC;IAED,IAAI,MAAM;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC;IAC9B,CAAC;IAED,IAAI,MAAM;QACT,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE;YACvB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;SAC1B;QACD,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CACtB,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EACpB,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EACpB,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CACpB,CAAC;QACF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CACzB,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,EACvB,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,EACvB,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CACvB,CAAC;QACF,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC/B,IAAI,QAAQ,GAAG,CAAC,CAAC,IAAI,QAAQ,GAAG,CAAC,EAAE;YAClC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC;SACvD;aAAM,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE;YAC3B,IAAI,CAAC,YAAY,CAChB,QAAQ,EACR,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EACnB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAChC,CAAC;SACF;aAAM;YACN,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;SACxB;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,CAAC;QAChE,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;QACnE,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC3B,CAAC;CACD"}