{"version": 3, "file": "engine.io.js", "sources": ["../../engine.io-parser/build/esm/commons.js", "../../engine.io-parser/build/esm/encodePacket.browser.js", "../../engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../../engine.io-parser/build/esm/decodePacket.browser.js", "../../engine.io-parser/build/esm/index.js", "../../socket.io-component-emitter/lib/esm/index.js", "../build/esm-debug/globals.js", "../build/esm-debug/util.js", "../build/esm-debug/contrib/parseqs.js", "../../../node_modules/ms/index.js", "../../../node_modules/debug/src/common.js", "../../../node_modules/debug/src/browser.js", "../build/esm-debug/transport.js", "../build/esm-debug/transports/polling.js", "../build/esm-debug/contrib/has-cors.js", "../build/esm-debug/transports/polling-xhr.js", "../build/esm-debug/transports/websocket.js", "../build/esm-debug/transports/webtransport.js", "../build/esm-debug/transports/index.js", "../build/esm-debug/contrib/parseuri.js", "../build/esm-debug/socket.js", "../build/esm-debug/browser-entrypoint.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach((key) => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data.arrayBuffer().then(toArray).then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, (encoded) => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n", "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE, } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType),\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType),\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1),\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type],\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n", "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET, } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, (encodedPacket) => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, (encodedPacket) => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        },\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* State.READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* State.READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* State.READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* State.READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* State.READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* State.READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else if (state === 2 /* State.READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* State.READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        },\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload, };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\nexport const defaultBinaryType = \"arraybuffer\";\nexport function createCookieJar() { }\n", "import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n/**\n * Generates a random 8-characters string.\n */\nexport function randomString() {\n    return (Date.now().toString(36).substring(3) +\n        Math.random().toString(36).substring(2, 5));\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function(val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n\tcreateDebug.debug = createDebug;\n\tcreateDebug.default = createDebug;\n\tcreateDebug.coerce = coerce;\n\tcreateDebug.disable = disable;\n\tcreateDebug.enable = enable;\n\tcreateDebug.enabled = enabled;\n\tcreateDebug.humanize = require('ms');\n\tcreateDebug.destroy = destroy;\n\n\tObject.keys(env).forEach(key => {\n\t\tcreateDebug[key] = env[key];\n\t});\n\n\t/**\n\t* The currently active debug mode names, and names to skip.\n\t*/\n\n\tcreateDebug.names = [];\n\tcreateDebug.skips = [];\n\n\t/**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/\n\tcreateDebug.formatters = {};\n\n\t/**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/\n\tfunction selectColor(namespace) {\n\t\tlet hash = 0;\n\n\t\tfor (let i = 0; i < namespace.length; i++) {\n\t\t\thash = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t\t\thash |= 0; // Convert to 32bit integer\n\t\t}\n\n\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t}\n\tcreateDebug.selectColor = selectColor;\n\n\t/**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/\n\tfunction createDebug(namespace) {\n\t\tlet prevTime;\n\t\tlet enableOverride = null;\n\t\tlet namespacesCache;\n\t\tlet enabledCache;\n\n\t\tfunction debug(...args) {\n\t\t\t// Disabled?\n\t\t\tif (!debug.enabled) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = debug;\n\n\t\t\t// Set `diff` timestamp\n\t\t\tconst curr = Number(new Date());\n\t\t\tconst ms = curr - (prevTime || curr);\n\t\t\tself.diff = ms;\n\t\t\tself.prev = prevTime;\n\t\t\tself.curr = curr;\n\t\t\tprevTime = curr;\n\n\t\t\targs[0] = createDebug.coerce(args[0]);\n\n\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\targs.unshift('%O');\n\t\t\t}\n\n\t\t\t// Apply any `formatters` transformations\n\t\t\tlet index = 0;\n\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\tif (match === '%%') {\n\t\t\t\t\treturn '%';\n\t\t\t\t}\n\t\t\t\tindex++;\n\t\t\t\tconst formatter = createDebug.formatters[format];\n\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\tconst val = args[index];\n\t\t\t\t\tmatch = formatter.call(self, val);\n\n\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\n\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\tcreateDebug.formatArgs.call(self, args);\n\n\t\t\tconst logFn = self.log || createDebug.log;\n\t\t\tlogFn.apply(self, args);\n\t\t}\n\n\t\tdebug.namespace = namespace;\n\t\tdebug.useColors = createDebug.useColors();\n\t\tdebug.color = createDebug.selectColor(namespace);\n\t\tdebug.extend = extend;\n\t\tdebug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n\t\tObject.defineProperty(debug, 'enabled', {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: false,\n\t\t\tget: () => {\n\t\t\t\tif (enableOverride !== null) {\n\t\t\t\t\treturn enableOverride;\n\t\t\t\t}\n\t\t\t\tif (namespacesCache !== createDebug.namespaces) {\n\t\t\t\t\tnamespacesCache = createDebug.namespaces;\n\t\t\t\t\tenabledCache = createDebug.enabled(namespace);\n\t\t\t\t}\n\n\t\t\t\treturn enabledCache;\n\t\t\t},\n\t\t\tset: v => {\n\t\t\t\tenableOverride = v;\n\t\t\t}\n\t\t});\n\n\t\t// Env-specific initialization logic for debug instances\n\t\tif (typeof createDebug.init === 'function') {\n\t\t\tcreateDebug.init(debug);\n\t\t}\n\n\t\treturn debug;\n\t}\n\n\tfunction extend(namespace, delimiter) {\n\t\tconst newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\tnewDebug.log = this.log;\n\t\treturn newDebug;\n\t}\n\n\t/**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/\n\tfunction enable(namespaces) {\n\t\tcreateDebug.save(namespaces);\n\t\tcreateDebug.namespaces = namespaces;\n\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\n\t\tlet i;\n\t\tconst split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n\t\tconst len = split.length;\n\n\t\tfor (i = 0; i < len; i++) {\n\t\t\tif (!split[i]) {\n\t\t\t\t// ignore empty strings\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tnamespaces = split[i].replace(/\\*/g, '.*?');\n\n\t\t\tif (namespaces[0] === '-') {\n\t\t\t\tcreateDebug.skips.push(new RegExp('^' + namespaces.slice(1) + '$'));\n\t\t\t} else {\n\t\t\t\tcreateDebug.names.push(new RegExp('^' + namespaces + '$'));\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/\n\tfunction disable() {\n\t\tconst namespaces = [\n\t\t\t...createDebug.names.map(toNamespace),\n\t\t\t...createDebug.skips.map(toNamespace).map(namespace => '-' + namespace)\n\t\t].join(',');\n\t\tcreateDebug.enable('');\n\t\treturn namespaces;\n\t}\n\n\t/**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/\n\tfunction enabled(name) {\n\t\tif (name[name.length - 1] === '*') {\n\t\t\treturn true;\n\t\t}\n\n\t\tlet i;\n\t\tlet len;\n\n\t\tfor (i = 0, len = createDebug.skips.length; i < len; i++) {\n\t\t\tif (createDebug.skips[i].test(name)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tfor (i = 0, len = createDebug.names.length; i < len; i++) {\n\t\t\tif (createDebug.names[i].test(name)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t* Convert regexp to namespace\n\t*\n\t* @param {RegExp} regxep\n\t* @return {String} namespace\n\t* @api private\n\t*/\n\tfunction toNamespace(regexp) {\n\t\treturn regexp.toString()\n\t\t\t.substring(2, regexp.toString().length - 2)\n\t\t\t.replace(/\\.\\*\\?$/, '*');\n\t}\n\n\t/**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/\n\tfunction coerce(val) {\n\t\tif (val instanceof Error) {\n\t\t\treturn val.stack || val.message;\n\t\t}\n\t\treturn val;\n\t}\n\n\t/**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/\n\tfunction destroy() {\n\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t}\n\n\tcreateDebug.enable(createDebug.load());\n\n\treturn createDebug;\n}\n\nmodule.exports = setup;\n", "/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\n\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (() => {\n\tlet warned = false;\n\n\treturn () => {\n\t\tif (!warned) {\n\t\t\twarned = true;\n\t\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t\t}\n\t};\n})();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n\t'#0000CC',\n\t'#0000FF',\n\t'#0033CC',\n\t'#0033FF',\n\t'#0066CC',\n\t'#0066FF',\n\t'#0099CC',\n\t'#0099FF',\n\t'#00CC00',\n\t'#00CC33',\n\t'#00CC66',\n\t'#00CC99',\n\t'#00CCCC',\n\t'#00CCFF',\n\t'#3300CC',\n\t'#3300FF',\n\t'#3333CC',\n\t'#3333FF',\n\t'#3366CC',\n\t'#3366FF',\n\t'#3399CC',\n\t'#3399FF',\n\t'#33CC00',\n\t'#33CC33',\n\t'#33CC66',\n\t'#33CC99',\n\t'#33CCCC',\n\t'#33CCFF',\n\t'#6600CC',\n\t'#6600FF',\n\t'#6633CC',\n\t'#6633FF',\n\t'#66CC00',\n\t'#66CC33',\n\t'#9900CC',\n\t'#9900FF',\n\t'#9933CC',\n\t'#9933FF',\n\t'#99CC00',\n\t'#99CC33',\n\t'#CC0000',\n\t'#CC0033',\n\t'#CC0066',\n\t'#CC0099',\n\t'#CC00CC',\n\t'#CC00FF',\n\t'#CC3300',\n\t'#CC3333',\n\t'#CC3366',\n\t'#CC3399',\n\t'#CC33CC',\n\t'#CC33FF',\n\t'#CC6600',\n\t'#CC6633',\n\t'#CC9900',\n\t'#CC9933',\n\t'#CCCC00',\n\t'#CCCC33',\n\t'#FF0000',\n\t'#FF0033',\n\t'#FF0066',\n\t'#FF0099',\n\t'#FF00CC',\n\t'#FF00FF',\n\t'#FF3300',\n\t'#FF3333',\n\t'#FF3366',\n\t'#FF3399',\n\t'#FF33CC',\n\t'#FF33FF',\n\t'#FF6600',\n\t'#FF6633',\n\t'#FF9900',\n\t'#FF9933',\n\t'#FFCC00',\n\t'#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\n// eslint-disable-next-line complexity\nfunction useColors() {\n\t// NB: In an Electron preload script, document will be defined but not fully\n\t// initialized. Since we know we're in Chrome, we'll just detect this case\n\t// explicitly\n\tif (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n\t\treturn true;\n\t}\n\n\t// Internet Explorer and Edge do not support colors.\n\tif (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t\treturn false;\n\t}\n\n\t// Is webkit? http://stackoverflow.com/a/16459606/376773\n\t// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\treturn (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n\t\t// Is firebug? http://stackoverflow.com/a/398120/376773\n\t\t(typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n\t\t// Is firefox >= v31?\n\t\t// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31) ||\n\t\t// Double check webkit in userAgent just in case we are in a worker\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\targs[0] = (this.useColors ? '%c' : '') +\n\t\tthis.namespace +\n\t\t(this.useColors ? ' %c' : ' ') +\n\t\targs[0] +\n\t\t(this.useColors ? '%c ' : ' ') +\n\t\t'+' + module.exports.humanize(this.diff);\n\n\tif (!this.useColors) {\n\t\treturn;\n\t}\n\n\tconst c = 'color: ' + this.color;\n\targs.splice(1, 0, c, 'color: inherit');\n\n\t// The final \"%c\" is somewhat tricky, because there could be other\n\t// arguments passed either before or after the %c, so we need to\n\t// figure out the correct index to insert the CSS into\n\tlet index = 0;\n\tlet lastC = 0;\n\targs[0].replace(/%[a-zA-Z%]/g, match => {\n\t\tif (match === '%%') {\n\t\t\treturn;\n\t\t}\n\t\tindex++;\n\t\tif (match === '%c') {\n\t\t\t// We only are interested in the *last* %c\n\t\t\t// (the user may have provided their own)\n\t\t\tlastC = index;\n\t\t}\n\t});\n\n\targs.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */\nexports.log = console.debug || console.log || (() => {});\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\ttry {\n\t\tif (namespaces) {\n\t\t\texports.storage.setItem('debug', namespaces);\n\t\t} else {\n\t\t\texports.storage.removeItem('debug');\n\t\t}\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\nfunction load() {\n\tlet r;\n\ttry {\n\t\tr = exports.storage.getItem('debug');\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n\n\t// If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\tif (!r && typeof process !== 'undefined' && 'env' in process) {\n\t\tr = process.env.DEBUG;\n\t}\n\n\treturn r;\n}\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n\ttry {\n\t\t// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n\t\t// The Browser also has localStorage in the global context.\n\t\treturn localStorage;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n\ttry {\n\t\treturn JSON.stringify(v);\n\t} catch (error) {\n\t\treturn '[UnexpectedJSONParseError]: ' + error.message;\n\t}\n};\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"engine.io-client:transport\"); // debug()\nexport class TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n            debug(\"transport is not open, discarding packets\");\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { randomString } from \"../util.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"engine.io-client:polling\"); // debug()\nexport class Polling extends Transport {\n    constructor() {\n        super(...arguments);\n        this._polling = false;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this._poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            debug(\"paused\");\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this._polling || !this.writable) {\n            let total = 0;\n            if (this._polling) {\n                debug(\"we are currently polling - waiting to pause\");\n                total++;\n                this.once(\"pollComplete\", function () {\n                    debug(\"pre-pause polling complete\");\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                debug(\"we are currently writing - waiting to pause\");\n                total++;\n                this.once(\"drain\", function () {\n                    debug(\"pre-pause writing complete\");\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    _poll() {\n        debug(\"polling\");\n        this._polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        debug(\"polling got data %s\", data);\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this._polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this._poll();\n            }\n            else {\n                debug('ignoring poll - transport state \"%s\"', this.readyState);\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            debug(\"writing close packet\");\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            debug(\"transport open - closing\");\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            debug(\"transport not open - deferring close\");\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "import { Polling } from \"./polling.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globals.node.js\";\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"engine.io-client:polling\"); // debug()\nfunction empty() { }\nexport class BaseXHR extends Polling {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        debug(\"xhr poll\");\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(createRequest, uri, opts) {\n        super();\n        this.createRequest = createRequest;\n        installTimerFunctions(this, opts);\n        this._opts = opts;\n        this._method = opts.method || \"GET\";\n        this._uri = uri;\n        this._data = undefined !== opts.data ? opts.data : null;\n        this._create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    _create() {\n        var _a;\n        const opts = pick(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this._opts.xd;\n        const xhr = (this._xhr = this.createRequest(opts));\n        try {\n            debug(\"xhr open %s: %s\", this._method, this._uri);\n            xhr.open(this._method, this._uri, true);\n            try {\n                if (this._opts.extraHeaders) {\n                    // @ts-ignore\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this._opts.extraHeaders) {\n                        if (this._opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this._method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this._opts.withCredentials;\n            }\n            if (this._opts.requestTimeout) {\n                xhr.timeout = this._opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(\n                    // @ts-ignore\n                    xhr.getResponseHeader(\"set-cookie\"));\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this._onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            debug(\"xhr data %s\", this._data);\n            xhr.send(this._data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this._onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this._index = Request.requestsCount++;\n            Request.requests[this._index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    _onError(err) {\n        this.emitReserved(\"error\", err, this._xhr);\n        this._cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    _cleanup(fromError) {\n        if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n            return;\n        }\n        this._xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this._xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this._index];\n        }\n        this._xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    _onLoad() {\n        const data = this._xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this._cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this._cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\nconst hasXHR2 = (function () {\n    const xhr = newRequest({\n        xdomain: false,\n    });\n    return xhr && xhr.responseType !== null;\n})();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */\nexport class XHR extends BaseXHR {\n    constructor(opts) {\n        super(opts);\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd }, this.opts);\n        return new Request(newRequest, this.uri(), opts);\n    }\n}\nfunction newRequest(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { pick, randomString } from \"../util.js\";\nimport { encodePacket } from \"engine.io-parser\";\nimport { globalThisShim as globalThis, nextTick } from \"../globals.node.js\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"engine.io-client:websocket\"); // debug()\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class BaseWS extends Transport {\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws = this.createSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    this.doWrite(packet, data);\n                }\n                catch (e) {\n                    debug(\"websocket closed before onclose event\");\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.onerror = () => { };\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\nconst WebSocketCtor = globalThis.WebSocket || globalThis.MozWebSocket;\n/**\n * WebSocket transport based on the built-in `WebSocket` object.\n *\n * Usage: browser, Node.js (since v21), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n * @see https://nodejs.org/api/globals.html#websocket\n */\nexport class WS extends BaseWS {\n    createSocket(uri, protocols, opts) {\n        return !isReactNative\n            ? protocols\n                ? new WebSocketCtor(uri, protocols)\n                : new WebSocketCtor(uri)\n            : new WebSocketCtor(uri, protocols, opts);\n    }\n    doWrite(_packet, data) {\n        this.ws.send(data);\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { nextTick } from \"../globals.node.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"engine.io-client:webtransport\"); // debug()\n/**\n * WebTransport transport based on the built-in `WebTransport` object.\n *\n * Usage: browser, Node.js (with the `@fails-components/webtransport` package)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebTransport\n * @see https://caniuse.com/webtransport\n */\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        try {\n            // @ts-ignore\n            this._transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this._transport.closed\n            .then(() => {\n            debug(\"transport closed gracefully\");\n            this.onClose();\n        })\n            .catch((err) => {\n            debug(\"transport closed due to %s\", err);\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this._transport.ready.then(() => {\n            this._transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this._writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            debug(\"session is closed\");\n                            return;\n                        }\n                        debug(\"received chunk: %o\", value);\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                        debug(\"an error occurred while reading: %s\", err);\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this._writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this._writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this._transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n", "import { XHR } from \"./polling-xhr.node.js\";\nimport { WS } from \"./websocket.node.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: XHR,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    if (str.length > 8000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports as DEFAULT_TRANSPORTS } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { createCookieJar, defaultBinaryType, nextTick, } from \"./globals.node.js\";\nimport debugModule from \"debug\"; // debug()\nconst debug = debugModule(\"engine.io-client:socket\"); // debug()\nconst withEventListeners = typeof addEventListener === \"function\" &&\n    typeof removeEventListener === \"function\";\nconst OFFLINE_EVENT_LISTENERS = [];\nif (withEventListeners) {\n    // within a ServiceWorker, any event handler for the 'offline' event must be added on the initial evaluation of the\n    // script, so we create one single event listener here which will forward the event to the socket instances\n    addEventListener(\"offline\", () => {\n        debug(\"closing %d connection(s) because the network was lost\", OFFLINE_EVENT_LISTENERS.length);\n        OFFLINE_EVENT_LISTENERS.forEach((listener) => listener());\n    }, false);\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes without upgrade mechanism, which means that it will keep the first low-level transport that\n * successfully establishes the connection.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithoutUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithoutUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithUpgrade\n * @see Socket\n */\nexport class SocketWithoutUpgrade extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        this._prevBufferLen = 0;\n        this._pingInterval = -1;\n        this._pingTimeout = -1;\n        this._maxPayload = -1;\n        /**\n         * The expiration timestamp of the {@link _pingTimeoutTimer} object is tracked, in case the timer is throttled and the\n         * callback is not fired on time. This can happen for example when a laptop is suspended or when a phone is locked.\n         */\n        this._pingTimeoutTime = Infinity;\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            const parsedUri = parse(uri);\n            opts.hostname = parsedUri.host;\n            opts.secure =\n                parsedUri.protocol === \"https\" || parsedUri.protocol === \"wss\";\n            opts.port = parsedUri.port;\n            if (parsedUri.query)\n                opts.query = parsedUri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = [];\n        this._transportsByName = {};\n        opts.transports.forEach((t) => {\n            const transportName = t.prototype.name;\n            this.transports.push(transportName);\n            this._transportsByName[transportName] = t;\n        });\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        if (withEventListeners) {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this._beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                debug(\"adding listener for the 'offline' event\");\n                this._offlineEventListener = () => {\n                    this._onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                OFFLINE_EVENT_LISTENERS.push(this._offlineEventListener);\n            }\n        }\n        if (this.opts.withCredentials) {\n            this._cookieJar = createCookieJar();\n        }\n        this._open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        debug('creating transport \"%s\"', name);\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        debug(\"options: %j\", opts);\n        return new this._transportsByName[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    _open() {\n        if (this.transports.length === 0) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        const transportName = this.opts.rememberUpgrade &&\n            SocketWithoutUpgrade.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1\n            ? \"websocket\"\n            : this.transports[0];\n        this.readyState = \"opening\";\n        const transport = this.createTransport(transportName);\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        debug(\"setting transport %s\", transport.name);\n        if (this.transport) {\n            debug(\"clearing existing transport %s\", this.transport.name);\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this._onDrain.bind(this))\n            .on(\"packet\", this._onPacket.bind(this))\n            .on(\"error\", this._onError.bind(this))\n            .on(\"close\", (reason) => this._onClose(\"transport close\", reason));\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        debug(\"socket open\");\n        this.readyState = \"open\";\n        SocketWithoutUpgrade.priorWebsocketSuccess =\n            \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    _onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            debug('socket receive: type \"%s\", data \"%s\"', packet.type, packet.data);\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this._sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    this._resetPingTimeout();\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this._onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n            debug('packet received with socket readyState \"%s\"', this.readyState);\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this._pingInterval = data.pingInterval;\n        this._pingTimeout = data.pingTimeout;\n        this._maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this._resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    _resetPingTimeout() {\n        this.clearTimeoutFn(this._pingTimeoutTimer);\n        const delay = this._pingInterval + this._pingTimeout;\n        this._pingTimeoutTime = Date.now() + delay;\n        this._pingTimeoutTimer = this.setTimeoutFn(() => {\n            this._onClose(\"ping timeout\");\n        }, delay);\n        if (this.opts.autoUnref) {\n            this._pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    _onDrain() {\n        this.writeBuffer.splice(0, this._prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this._prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this._getWritablePackets();\n            debug(\"flushing %d packets in socket\", packets.length);\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this._prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    _getWritablePackets() {\n        const shouldCheckPayloadSize = this._maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this._maxPayload) {\n                debug(\"only send %d out of %d packets\", i, this.writeBuffer.length);\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        debug(\"payload size is %d (max: %d)\", payloadSize, this._maxPayload);\n        return this.writeBuffer;\n    }\n    /**\n     * Checks whether the heartbeat timer has expired but the socket has not yet been notified.\n     *\n     * Note: this method is private for now because it does not really fit the WebSocket API, but if we put it in the\n     * `write()` method then the message would not be buffered by the Socket.IO client.\n     *\n     * @return {boolean}\n     * @private\n     */\n    /* private */ _hasPingExpired() {\n        if (!this._pingTimeoutTime)\n            return true;\n        const hasExpired = Date.now() > this._pingTimeoutTime;\n        if (hasExpired) {\n            debug(\"throttled timer detected, scheduling connection close\");\n            this._pingTimeoutTime = 0;\n            nextTick(() => {\n                this._onClose(\"ping timeout\");\n            }, this.setTimeoutFn);\n        }\n        return hasExpired;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a message. Alias of {@link Socket#write}.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    send(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    _sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this._onClose(\"forced close\");\n            debug(\"socket closing - telling transport to close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    _onError(err) {\n        debug(\"socket error %j\", err);\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        if (this.opts.tryAllTransports &&\n            this.transports.length > 1 &&\n            this.readyState === \"opening\") {\n            debug(\"trying next transport\");\n            this.transports.shift();\n            return this._open();\n        }\n        this.emitReserved(\"error\", err);\n        this._onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    _onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            debug('socket close with reason: \"%s\"', reason);\n            // clear timers\n            this.clearTimeoutFn(this._pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (withEventListeners) {\n                if (this._beforeunloadEventListener) {\n                    removeEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n                }\n                if (this._offlineEventListener) {\n                    const i = OFFLINE_EVENT_LISTENERS.indexOf(this._offlineEventListener);\n                    if (i !== -1) {\n                        debug(\"removing listener for the 'offline' event\");\n                        OFFLINE_EVENT_LISTENERS.splice(i, 1);\n                    }\n                }\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this._prevBufferLen = 0;\n        }\n    }\n}\nSocketWithoutUpgrade.protocol = protocol;\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see Socket\n */\nexport class SocketWithUpgrade extends SocketWithoutUpgrade {\n    constructor() {\n        super(...arguments);\n        this._upgrades = [];\n    }\n    onOpen() {\n        super.onOpen();\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            debug(\"starting upgrade probes\");\n            for (let i = 0; i < this._upgrades.length; i++) {\n                this._probe(this._upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    _probe(name) {\n        debug('probing transport \"%s\"', name);\n        let transport = this.createTransport(name);\n        let failed = false;\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            debug('probe transport \"%s\" opened', name);\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    debug('probe transport \"%s\" pong', name);\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    SocketWithoutUpgrade.priorWebsocketSuccess =\n                        \"websocket\" === transport.name;\n                    debug('pausing current transport \"%s\"', this.transport.name);\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        debug(\"changing transport and sending upgrade packet\");\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    debug('probe transport \"%s\" failed', name);\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            debug('probe transport \"%s\" failed because of error: %s', name, err);\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                debug('\"%s\" works - aborting \"%s\"', to.name, transport.name);\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this._upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    onHandshake(data) {\n        this._upgrades = this._filterUpgrades(data.upgrades);\n        super.onHandshake(data);\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    _filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        for (let i = 0; i < upgrades.length; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * @example\n * import { Socket } from \"engine.io-client\";\n *\n * const socket = new Socket();\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see SocketWithUpgrade\n */\nexport class Socket extends SocketWithUpgrade {\n    constructor(uri, opts = {}) {\n        const o = typeof uri === \"object\" ? uri : opts;\n        if (!o.transports ||\n            (o.transports && typeof o.transports[0] === \"string\")) {\n            o.transports = (o.transports || [\"polling\", \"websocket\", \"webtransport\"])\n                .map((transportName) => DEFAULT_TRANSPORTS[transportName])\n                .filter((t) => !!t);\n        }\n        super(uri, o);\n    }\n}\n", "import { Socket } from \"./socket.js\";\nexport default (uri, opts) => new Socket(uri, opts);\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "_ref", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "toArray", "Uint8Array", "byteOffset", "byteLength", "TEXT_ENCODER", "encodePacketToBinary", "packet", "arrayBuffer", "then", "encoded", "TextEncoder", "encode", "chars", "lookup", "i", "length", "charCodeAt", "decode", "base64", "bufferLength", "len", "p", "encoded1", "encoded2", "encoded3", "encoded4", "arraybuffer", "bytes", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "packetType", "decoded", "SEPARATOR", "String", "fromCharCode", "encodePayload", "packets", "encodedPackets", "Array", "count", "join", "decodePayload", "encodedPayload", "decodedPacket", "push", "createPacketEncoderStream", "TransformStream", "transform", "controller", "payloadLength", "header", "DataView", "setUint8", "view", "setUint16", "setBigUint64", "BigInt", "enqueue", "TEXT_DECODER", "totalLength", "chunks", "reduce", "acc", "chunk", "concatChunks", "size", "shift", "j", "slice", "createPacketDecoderStream", "maxPayload", "TextDecoder", "state", "<PERSON><PERSON><PERSON><PERSON>", "isBinary", "headerArray", "getUint16", "n", "getUint32", "Math", "pow", "protocol", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "_callbacks", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "callbacks", "cb", "splice", "emit", "args", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "nextTick", "isPromiseAvailable", "Promise", "resolve", "setTimeoutFn", "globalThisShim", "self", "window", "Function", "defaultBinaryType", "createCookieJar", "pick", "_len", "attr", "_key", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "bind", "clearTimeoutFn", "BASE64_OVERHEAD", "utf8Length", "ceil", "str", "c", "l", "randomString", "Date", "now", "random", "encodeURIComponent", "qs", "qry", "pairs", "pair", "decodeURIComponent", "s", "m", "h", "d", "w", "y", "ms", "val", "options", "_typeof", "parse", "isFinite", "fmtLong", "fmtShort", "Error", "JSON", "stringify", "match", "exec", "parseFloat", "toLowerCase", "undefined", "msAbs", "abs", "round", "plural", "name", "isPlural", "setup", "env", "createDebug", "debug", "coerce", "disable", "enable", "enabled", "humanize", "require$$0", "destroy", "names", "skips", "formatters", "selectColor", "namespace", "hash", "colors", "prevTime", "enableOverride", "namespacesCache", "enabledCache", "curr", "Number", "diff", "prev", "unshift", "index", "replace", "format", "formatter", "formatArgs", "logFn", "log", "useColors", "color", "extend", "defineProperty", "enumerable", "configurable", "get", "namespaces", "set", "v", "init", "delimiter", "newDebug", "save", "RegExp", "concat", "_toConsumableArray", "map", "toNamespace", "test", "regexp", "stack", "message", "console", "warn", "load", "common", "exports", "storage", "localstorage", "warned", "process", "__nwjs", "navigator", "userAgent", "document", "documentElement", "style", "WebkitAppearance", "firebug", "exception", "table", "parseInt", "$1", "module", "lastC", "setItem", "removeItem", "error", "r", "getItem", "DEBUG", "localStorage", "debugModule", "TransportError", "_Error", "reason", "description", "context", "_this", "_inherits<PERSON><PERSON>e", "_wrapNativeSuper", "Transport", "_Emitter", "_this2", "writable", "query", "socket", "forceBase64", "_proto", "onError", "open", "readyState", "doOpen", "close", "doClose", "onClose", "send", "write", "onOpen", "onData", "onPacket", "details", "pause", "onPause", "createUri", "schema", "_hostname", "_port", "path", "_query", "hostname", "indexOf", "port", "secure", "<PERSON><PERSON><PERSON><PERSON>", "Polling", "_Transport", "_polling", "_poll", "total", "doPoll", "_this3", "_this4", "_this5", "doWrite", "uri", "timestampRequests", "timestampParam", "sid", "b64", "_createClass", "value", "XMLHttpRequest", "err", "hasCORS", "empty", "BaseXHR", "_Polling", "location", "isSSL", "xd", "req", "request", "method", "xhrStatus", "pollXhr", "Request", "createRequest", "_opts", "_method", "_uri", "_data", "_create", "_proto2", "_a", "xdomain", "xhr", "_xhr", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "e", "cookieJar", "addCookies", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "parseCookies", "getResponseHeader", "status", "_onLoad", "_onError", "_index", "requestsCount", "requests", "_cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "terminationEvent", "hasXHR2", "newRequest", "responseType", "XHR", "_BaseXHR", "_this6", "_proto3", "_extends", "isReactNative", "product", "BaseWS", "protocols", "headers", "ws", "createSocket", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "_loop", "lastPacket", "WebSocketCtor", "WebSocket", "MozWebSocket", "WS", "_BaseWS", "_packet", "WT", "_transport", "WebTransport", "transportOptions", "closed", "ready", "createBidirectionalStream", "stream", "decoderStream", "MAX_SAFE_INTEGER", "reader", "readable", "pipeThrough", "<PERSON><PERSON><PERSON><PERSON>", "encoderStream", "pipeTo", "_writer", "getWriter", "read", "done", "transports", "websocket", "webtransport", "polling", "re", "parts", "src", "b", "source", "host", "authority", "ipv6uri", "pathNames", "query<PERSON><PERSON>", "regx", "$0", "$2", "withEventListeners", "OFFLINE_EVENT_LISTENERS", "listener", "SocketWithoutUpgrade", "writeBuffer", "_prevBufferLen", "_pingInterval", "_pingTimeout", "_maxPayload", "_pingTimeoutTime", "Infinity", "parsed<PERSON><PERSON>", "_transportsByName", "t", "transportName", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "closeOnBeforeunload", "_beforeunloadEventListener", "transport", "_offlineEventListener", "_onClose", "_cookieJar", "_open", "createTransport", "EIO", "id", "priorWebsocketSuccess", "setTransport", "_onDrain", "_onPacket", "flush", "onHandshake", "_sendPacket", "_resetPingTimeout", "code", "pingInterval", "pingTimeout", "_pingTimeoutTimer", "delay", "upgrading", "_getWritablePackets", "shouldCheckPayloadSize", "payloadSize", "_hasPingExpired", "hasExpired", "msg", "compress", "cleanupAndClose", "waitForUpgrade", "tryAllTransports", "SocketWithUpgrade", "_SocketWithoutUpgrade", "_this7", "_upgrades", "_probe", "_this8", "failed", "onTransportOpen", "cleanup", "freezeTransport", "onTransportClose", "onupgrade", "to", "_filterUpgrades", "upgrades", "filteredUpgrades", "Socket", "_SocketWithUpgrade", "o", "DEFAULT_TRANSPORTS", "filter"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA,IAAMA,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;EACzCF,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;EAC1BA,YAAY,CAAC,OAAO,CAAC,GAAG,GAAG,CAAA;EAC3BA,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;EAC1BA,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;EAC1BA,YAAY,CAAC,SAAS,CAAC,GAAG,GAAG,CAAA;EAC7BA,YAAY,CAAC,SAAS,CAAC,GAAG,GAAG,CAAA;EAC7BA,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;EAC1B,IAAMG,oBAAoB,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAA;EAChDD,MAAM,CAACG,IAAI,CAACJ,YAAY,CAAC,CAACK,OAAO,CAAC,UAACC,GAAG,EAAK;EACvCH,EAAAA,oBAAoB,CAACH,YAAY,CAACM,GAAG,CAAC,CAAC,GAAGA,GAAG,CAAA;EACjD,CAAC,CAAC,CAAA;EACF,IAAMC,YAAY,GAAG;EAAEC,EAAAA,IAAI,EAAE,OAAO;EAAEC,EAAAA,IAAI,EAAE,cAAA;EAAe,CAAC;;ECX5D,IAAMC,cAAc,GAAG,OAAOC,IAAI,KAAK,UAAU,IAC5C,OAAOA,IAAI,KAAK,WAAW,IACxBV,MAAM,CAACW,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACH,IAAI,CAAC,KAAK,0BAA2B,CAAA;EAC5E,IAAMI,uBAAqB,GAAG,OAAOC,WAAW,KAAK,UAAU,CAAA;EAC/D;EACA,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAIC,GAAG,EAAK;IACpB,OAAO,OAAOF,WAAW,CAACC,MAAM,KAAK,UAAU,GACzCD,WAAW,CAACC,MAAM,CAACC,GAAG,CAAC,GACvBA,GAAG,IAAIA,GAAG,CAACC,MAAM,YAAYH,WAAW,CAAA;EAClD,CAAC,CAAA;EACD,IAAMI,YAAY,GAAG,SAAfA,YAAYA,CAAAC,IAAA,EAAoBC,cAAc,EAAEC,QAAQ,EAAK;EAAA,EAAA,IAA3Cf,IAAI,GAAAa,IAAA,CAAJb,IAAI;MAAEC,IAAI,GAAAY,IAAA,CAAJZ,IAAI,CAAA;EAC9B,EAAA,IAAIC,cAAc,IAAID,IAAI,YAAYE,IAAI,EAAE;EACxC,IAAA,IAAIW,cAAc,EAAE;QAChB,OAAOC,QAAQ,CAACd,IAAI,CAAC,CAAA;EACzB,KAAC,MACI;EACD,MAAA,OAAOe,kBAAkB,CAACf,IAAI,EAAEc,QAAQ,CAAC,CAAA;EAC7C,KAAA;EACJ,GAAC,MACI,IAAIR,uBAAqB,KACzBN,IAAI,YAAYO,WAAW,IAAIC,MAAM,CAACR,IAAI,CAAC,CAAC,EAAE;EAC/C,IAAA,IAAIa,cAAc,EAAE;QAChB,OAAOC,QAAQ,CAACd,IAAI,CAAC,CAAA;EACzB,KAAC,MACI;QACD,OAAOe,kBAAkB,CAAC,IAAIb,IAAI,CAAC,CAACF,IAAI,CAAC,CAAC,EAAEc,QAAQ,CAAC,CAAA;EACzD,KAAA;EACJ,GAAA;EACA;IACA,OAAOA,QAAQ,CAACvB,YAAY,CAACQ,IAAI,CAAC,IAAIC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAA;EACtD,CAAC,CAAA;EACD,IAAMe,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIf,IAAI,EAAEc,QAAQ,EAAK;EAC3C,EAAA,IAAME,UAAU,GAAG,IAAIC,UAAU,EAAE,CAAA;IACnCD,UAAU,CAACE,MAAM,GAAG,YAAY;EAC5B,IAAA,IAAMC,OAAO,GAAGH,UAAU,CAACI,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;EAC/CP,IAAAA,QAAQ,CAAC,GAAG,IAAIK,OAAO,IAAI,EAAE,CAAC,CAAC,CAAA;KAClC,CAAA;EACD,EAAA,OAAOH,UAAU,CAACM,aAAa,CAACtB,IAAI,CAAC,CAAA;EACzC,CAAC,CAAA;EACD,SAASuB,OAAOA,CAACvB,IAAI,EAAE;IACnB,IAAIA,IAAI,YAAYwB,UAAU,EAAE;EAC5B,IAAA,OAAOxB,IAAI,CAAA;EACf,GAAC,MACI,IAAIA,IAAI,YAAYO,WAAW,EAAE;EAClC,IAAA,OAAO,IAAIiB,UAAU,CAACxB,IAAI,CAAC,CAAA;EAC/B,GAAC,MACI;EACD,IAAA,OAAO,IAAIwB,UAAU,CAACxB,IAAI,CAACU,MAAM,EAAEV,IAAI,CAACyB,UAAU,EAAEzB,IAAI,CAAC0B,UAAU,CAAC,CAAA;EACxE,GAAA;EACJ,CAAA;EACA,IAAIC,YAAY,CAAA;EACT,SAASC,oBAAoBA,CAACC,MAAM,EAAEf,QAAQ,EAAE;EACnD,EAAA,IAAIb,cAAc,IAAI4B,MAAM,CAAC7B,IAAI,YAAYE,IAAI,EAAE;EAC/C,IAAA,OAAO2B,MAAM,CAAC7B,IAAI,CAAC8B,WAAW,EAAE,CAACC,IAAI,CAACR,OAAO,CAAC,CAACQ,IAAI,CAACjB,QAAQ,CAAC,CAAA;EACjE,GAAC,MACI,IAAIR,uBAAqB,KACzBuB,MAAM,CAAC7B,IAAI,YAAYO,WAAW,IAAIC,MAAM,CAACqB,MAAM,CAAC7B,IAAI,CAAC,CAAC,EAAE;MAC7D,OAAOc,QAAQ,CAACS,OAAO,CAACM,MAAM,CAAC7B,IAAI,CAAC,CAAC,CAAA;EACzC,GAAA;EACAW,EAAAA,YAAY,CAACkB,MAAM,EAAE,KAAK,EAAE,UAACG,OAAO,EAAK;MACrC,IAAI,CAACL,YAAY,EAAE;EACfA,MAAAA,YAAY,GAAG,IAAIM,WAAW,EAAE,CAAA;EACpC,KAAA;EACAnB,IAAAA,QAAQ,CAACa,YAAY,CAACO,MAAM,CAACF,OAAO,CAAC,CAAC,CAAA;EAC1C,GAAC,CAAC,CAAA;EACN;;EClEA;EACA,IAAMG,KAAK,GAAG,kEAAkE,CAAA;EAChF;EACA,IAAMC,MAAM,GAAG,OAAOZ,UAAU,KAAK,WAAW,GAAG,EAAE,GAAG,IAAIA,UAAU,CAAC,GAAG,CAAC,CAAA;EAC3E,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACnCD,MAAM,CAACD,KAAK,CAACI,UAAU,CAACF,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAA;EACnC,CAAA;EAiBO,IAAMG,QAAM,GAAG,SAATA,MAAMA,CAAIC,MAAM,EAAK;EAC9B,EAAA,IAAIC,YAAY,GAAGD,MAAM,CAACH,MAAM,GAAG,IAAI;MAAEK,GAAG,GAAGF,MAAM,CAACH,MAAM;MAAED,CAAC;EAAEO,IAAAA,CAAC,GAAG,CAAC;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC,QAAQ,CAAA;IAC9G,IAAIP,MAAM,CAACA,MAAM,CAACH,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;EACnCI,IAAAA,YAAY,EAAE,CAAA;MACd,IAAID,MAAM,CAACA,MAAM,CAACH,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;EACnCI,MAAAA,YAAY,EAAE,CAAA;EAClB,KAAA;EACJ,GAAA;EACA,EAAA,IAAMO,WAAW,GAAG,IAAI1C,WAAW,CAACmC,YAAY,CAAC;EAAEQ,IAAAA,KAAK,GAAG,IAAI1B,UAAU,CAACyB,WAAW,CAAC,CAAA;IACtF,KAAKZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,GAAG,EAAEN,CAAC,IAAI,CAAC,EAAE;MACzBQ,QAAQ,GAAGT,MAAM,CAACK,MAAM,CAACF,UAAU,CAACF,CAAC,CAAC,CAAC,CAAA;MACvCS,QAAQ,GAAGV,MAAM,CAACK,MAAM,CAACF,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;MAC3CU,QAAQ,GAAGX,MAAM,CAACK,MAAM,CAACF,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;MAC3CW,QAAQ,GAAGZ,MAAM,CAACK,MAAM,CAACF,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;MAC3Ca,KAAK,CAACN,CAAC,EAAE,CAAC,GAAIC,QAAQ,IAAI,CAAC,GAAKC,QAAQ,IAAI,CAAE,CAAA;EAC9CI,IAAAA,KAAK,CAACN,CAAC,EAAE,CAAC,GAAI,CAACE,QAAQ,GAAG,EAAE,KAAK,CAAC,GAAKC,QAAQ,IAAI,CAAE,CAAA;EACrDG,IAAAA,KAAK,CAACN,CAAC,EAAE,CAAC,GAAI,CAACG,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAKC,QAAQ,GAAG,EAAG,CAAA;EACxD,GAAA;EACA,EAAA,OAAOC,WAAW,CAAA;EACtB,CAAC;;ECxCD,IAAM3C,qBAAqB,GAAG,OAAOC,WAAW,KAAK,UAAU,CAAA;EACxD,IAAM4C,YAAY,GAAG,SAAfA,YAAYA,CAAIC,aAAa,EAAEC,UAAU,EAAK;EACvD,EAAA,IAAI,OAAOD,aAAa,KAAK,QAAQ,EAAE;MACnC,OAAO;EACHrD,MAAAA,IAAI,EAAE,SAAS;EACfC,MAAAA,IAAI,EAAEsD,SAAS,CAACF,aAAa,EAAEC,UAAU,CAAA;OAC5C,CAAA;EACL,GAAA;EACA,EAAA,IAAMtD,IAAI,GAAGqD,aAAa,CAACG,MAAM,CAAC,CAAC,CAAC,CAAA;IACpC,IAAIxD,IAAI,KAAK,GAAG,EAAE;MACd,OAAO;EACHA,MAAAA,IAAI,EAAE,SAAS;QACfC,IAAI,EAAEwD,kBAAkB,CAACJ,aAAa,CAACK,SAAS,CAAC,CAAC,CAAC,EAAEJ,UAAU,CAAA;OAClE,CAAA;EACL,GAAA;EACA,EAAA,IAAMK,UAAU,GAAGhE,oBAAoB,CAACK,IAAI,CAAC,CAAA;IAC7C,IAAI,CAAC2D,UAAU,EAAE;EACb,IAAA,OAAO5D,YAAY,CAAA;EACvB,GAAA;EACA,EAAA,OAAOsD,aAAa,CAACd,MAAM,GAAG,CAAC,GACzB;EACEvC,IAAAA,IAAI,EAAEL,oBAAoB,CAACK,IAAI,CAAC;EAChCC,IAAAA,IAAI,EAAEoD,aAAa,CAACK,SAAS,CAAC,CAAC,CAAA;EACnC,GAAC,GACC;MACE1D,IAAI,EAAEL,oBAAoB,CAACK,IAAI,CAAA;KAClC,CAAA;EACT,CAAC,CAAA;EACD,IAAMyD,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIxD,IAAI,EAAEqD,UAAU,EAAK;EAC7C,EAAA,IAAI/C,qBAAqB,EAAE;EACvB,IAAA,IAAMqD,OAAO,GAAGnB,QAAM,CAACxC,IAAI,CAAC,CAAA;EAC5B,IAAA,OAAOsD,SAAS,CAACK,OAAO,EAAEN,UAAU,CAAC,CAAA;EACzC,GAAC,MACI;MACD,OAAO;EAAEZ,MAAAA,MAAM,EAAE,IAAI;EAAEzC,MAAAA,IAAI,EAAJA,IAAAA;EAAK,KAAC,CAAC;EAClC,GAAA;EACJ,CAAC,CAAA;EACD,IAAMsD,SAAS,GAAG,SAAZA,SAASA,CAAItD,IAAI,EAAEqD,UAAU,EAAK;EACpC,EAAA,QAAQA,UAAU;EACd,IAAA,KAAK,MAAM;QACP,IAAIrD,IAAI,YAAYE,IAAI,EAAE;EACtB;EACA,QAAA,OAAOF,IAAI,CAAA;EACf,OAAC,MACI;EACD;EACA,QAAA,OAAO,IAAIE,IAAI,CAAC,CAACF,IAAI,CAAC,CAAC,CAAA;EAC3B,OAAA;EACJ,IAAA,KAAK,aAAa,CAAA;EAClB,IAAA;QACI,IAAIA,IAAI,YAAYO,WAAW,EAAE;EAC7B;EACA,QAAA,OAAOP,IAAI,CAAA;EACf,OAAC,MACI;EACD;UACA,OAAOA,IAAI,CAACU,MAAM,CAAA;EACtB,OAAA;EACR,GAAA;EACJ,CAAC;;EC1DD,IAAMkD,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,EAAE,CAAC,CAAC;EAC1C,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,OAAO,EAAElD,QAAQ,EAAK;EACzC;EACA,EAAA,IAAMwB,MAAM,GAAG0B,OAAO,CAAC1B,MAAM,CAAA;EAC7B,EAAA,IAAM2B,cAAc,GAAG,IAAIC,KAAK,CAAC5B,MAAM,CAAC,CAAA;IACxC,IAAI6B,KAAK,GAAG,CAAC,CAAA;EACbH,EAAAA,OAAO,CAACpE,OAAO,CAAC,UAACiC,MAAM,EAAEQ,CAAC,EAAK;EAC3B;EACA1B,IAAAA,YAAY,CAACkB,MAAM,EAAE,KAAK,EAAE,UAACuB,aAAa,EAAK;EAC3Ca,MAAAA,cAAc,CAAC5B,CAAC,CAAC,GAAGe,aAAa,CAAA;EACjC,MAAA,IAAI,EAAEe,KAAK,KAAK7B,MAAM,EAAE;EACpBxB,QAAAA,QAAQ,CAACmD,cAAc,CAACG,IAAI,CAACR,SAAS,CAAC,CAAC,CAAA;EAC5C,OAAA;EACJ,KAAC,CAAC,CAAA;EACN,GAAC,CAAC,CAAA;EACN,CAAC,CAAA;EACD,IAAMS,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,cAAc,EAAEjB,UAAU,EAAK;EAClD,EAAA,IAAMY,cAAc,GAAGK,cAAc,CAACjD,KAAK,CAACuC,SAAS,CAAC,CAAA;IACtD,IAAMI,OAAO,GAAG,EAAE,CAAA;EAClB,EAAA,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,cAAc,CAAC3B,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,IAAMkC,aAAa,GAAGpB,YAAY,CAACc,cAAc,CAAC5B,CAAC,CAAC,EAAEgB,UAAU,CAAC,CAAA;EACjEW,IAAAA,OAAO,CAACQ,IAAI,CAACD,aAAa,CAAC,CAAA;EAC3B,IAAA,IAAIA,aAAa,CAACxE,IAAI,KAAK,OAAO,EAAE;EAChC,MAAA,MAAA;EACJ,KAAA;EACJ,GAAA;EACA,EAAA,OAAOiE,OAAO,CAAA;EAClB,CAAC,CAAA;EACM,SAASS,yBAAyBA,GAAG;IACxC,OAAO,IAAIC,eAAe,CAAC;EACvBC,IAAAA,SAAS,EAAAA,SAAAA,SAAAA,CAAC9C,MAAM,EAAE+C,UAAU,EAAE;EAC1BhD,MAAAA,oBAAoB,CAACC,MAAM,EAAE,UAACuB,aAAa,EAAK;EAC5C,QAAA,IAAMyB,aAAa,GAAGzB,aAAa,CAACd,MAAM,CAAA;EAC1C,QAAA,IAAIwC,MAAM,CAAA;EACV;UACA,IAAID,aAAa,GAAG,GAAG,EAAE;EACrBC,UAAAA,MAAM,GAAG,IAAItD,UAAU,CAAC,CAAC,CAAC,CAAA;EAC1B,UAAA,IAAIuD,QAAQ,CAACD,MAAM,CAACpE,MAAM,CAAC,CAACsE,QAAQ,CAAC,CAAC,EAAEH,aAAa,CAAC,CAAA;EAC1D,SAAC,MACI,IAAIA,aAAa,GAAG,KAAK,EAAE;EAC5BC,UAAAA,MAAM,GAAG,IAAItD,UAAU,CAAC,CAAC,CAAC,CAAA;YAC1B,IAAMyD,IAAI,GAAG,IAAIF,QAAQ,CAACD,MAAM,CAACpE,MAAM,CAAC,CAAA;EACxCuE,UAAAA,IAAI,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;EACrBC,UAAAA,IAAI,CAACC,SAAS,CAAC,CAAC,EAAEL,aAAa,CAAC,CAAA;EACpC,SAAC,MACI;EACDC,UAAAA,MAAM,GAAG,IAAItD,UAAU,CAAC,CAAC,CAAC,CAAA;YAC1B,IAAMyD,KAAI,GAAG,IAAIF,QAAQ,CAACD,MAAM,CAACpE,MAAM,CAAC,CAAA;EACxCuE,UAAAA,KAAI,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;YACrBC,KAAI,CAACE,YAAY,CAAC,CAAC,EAAEC,MAAM,CAACP,aAAa,CAAC,CAAC,CAAA;EAC/C,SAAA;EACA;UACA,IAAIhD,MAAM,CAAC7B,IAAI,IAAI,OAAO6B,MAAM,CAAC7B,IAAI,KAAK,QAAQ,EAAE;EAChD8E,UAAAA,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;EACrB,SAAA;EACAF,QAAAA,UAAU,CAACS,OAAO,CAACP,MAAM,CAAC,CAAA;EAC1BF,QAAAA,UAAU,CAACS,OAAO,CAACjC,aAAa,CAAC,CAAA;EACrC,OAAC,CAAC,CAAA;EACN,KAAA;EACJ,GAAC,CAAC,CAAA;EACN,CAAA;EACA,IAAIkC,YAAY,CAAA;EAChB,SAASC,WAAWA,CAACC,MAAM,EAAE;EACzB,EAAA,OAAOA,MAAM,CAACC,MAAM,CAAC,UAACC,GAAG,EAAEC,KAAK,EAAA;EAAA,IAAA,OAAKD,GAAG,GAAGC,KAAK,CAACrD,MAAM,CAAA;EAAA,GAAA,EAAE,CAAC,CAAC,CAAA;EAC/D,CAAA;EACA,SAASsD,YAAYA,CAACJ,MAAM,EAAEK,IAAI,EAAE;IAChC,IAAIL,MAAM,CAAC,CAAC,CAAC,CAAClD,MAAM,KAAKuD,IAAI,EAAE;EAC3B,IAAA,OAAOL,MAAM,CAACM,KAAK,EAAE,CAAA;EACzB,GAAA;EACA,EAAA,IAAMpF,MAAM,GAAG,IAAIc,UAAU,CAACqE,IAAI,CAAC,CAAA;IACnC,IAAIE,CAAC,GAAG,CAAC,CAAA;IACT,KAAK,IAAI1D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwD,IAAI,EAAExD,CAAC,EAAE,EAAE;MAC3B3B,MAAM,CAAC2B,CAAC,CAAC,GAAGmD,MAAM,CAAC,CAAC,CAAC,CAACO,CAAC,EAAE,CAAC,CAAA;MAC1B,IAAIA,CAAC,KAAKP,MAAM,CAAC,CAAC,CAAC,CAAClD,MAAM,EAAE;QACxBkD,MAAM,CAACM,KAAK,EAAE,CAAA;EACdC,MAAAA,CAAC,GAAG,CAAC,CAAA;EACT,KAAA;EACJ,GAAA;EACA,EAAA,IAAIP,MAAM,CAAClD,MAAM,IAAIyD,CAAC,GAAGP,MAAM,CAAC,CAAC,CAAC,CAAClD,MAAM,EAAE;EACvCkD,IAAAA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACD,CAAC,CAAC,CAAA;EAClC,GAAA;EACA,EAAA,OAAOrF,MAAM,CAAA;EACjB,CAAA;EACO,SAASuF,yBAAyBA,CAACC,UAAU,EAAE7C,UAAU,EAAE;IAC9D,IAAI,CAACiC,YAAY,EAAE;EACfA,IAAAA,YAAY,GAAG,IAAIa,WAAW,EAAE,CAAA;EACpC,GAAA;IACA,IAAMX,MAAM,GAAG,EAAE,CAAA;IACjB,IAAIY,KAAK,GAAG,CAAC,yBAAC;IACd,IAAIC,cAAc,GAAG,CAAC,CAAC,CAAA;IACvB,IAAIC,QAAQ,GAAG,KAAK,CAAA;IACpB,OAAO,IAAI5B,eAAe,CAAC;EACvBC,IAAAA,SAAS,EAAAA,SAAAA,SAAAA,CAACgB,KAAK,EAAEf,UAAU,EAAE;EACzBY,MAAAA,MAAM,CAAChB,IAAI,CAACmB,KAAK,CAAC,CAAA;EAClB,MAAA,OAAO,IAAI,EAAE;EACT,QAAA,IAAIS,KAAK,KAAK,CAAC,0BAA0B;EACrC,UAAA,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAG,CAAC,EAAE;EACzB,YAAA,MAAA;EACJ,WAAA;EACA,UAAA,IAAMV,MAAM,GAAGc,YAAY,CAACJ,MAAM,EAAE,CAAC,CAAC,CAAA;YACtCc,QAAQ,GAAG,CAACxB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,IAAI,CAAA;EACtCuB,UAAAA,cAAc,GAAGvB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;YACjC,IAAIuB,cAAc,GAAG,GAAG,EAAE;cACtBD,KAAK,GAAG,CAAC,0BAAC;EACd,WAAC,MACI,IAAIC,cAAc,KAAK,GAAG,EAAE;cAC7BD,KAAK,GAAG,CAAC,qCAAC;EACd,WAAC,MACI;cACDA,KAAK,GAAG,CAAC,qCAAC;EACd,WAAA;EACJ,SAAC,MACI,IAAIA,KAAK,KAAK,CAAC,sCAAsC;EACtD,UAAA,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAG,CAAC,EAAE;EACzB,YAAA,MAAA;EACJ,WAAA;EACA,UAAA,IAAMe,WAAW,GAAGX,YAAY,CAACJ,MAAM,EAAE,CAAC,CAAC,CAAA;YAC3Ca,cAAc,GAAG,IAAItB,QAAQ,CAACwB,WAAW,CAAC7F,MAAM,EAAE6F,WAAW,CAAC9E,UAAU,EAAE8E,WAAW,CAACjE,MAAM,CAAC,CAACkE,SAAS,CAAC,CAAC,CAAC,CAAA;YAC1GJ,KAAK,GAAG,CAAC,0BAAC;EACd,SAAC,MACI,IAAIA,KAAK,KAAK,CAAC,sCAAsC;EACtD,UAAA,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAG,CAAC,EAAE;EACzB,YAAA,MAAA;EACJ,WAAA;EACA,UAAA,IAAMe,YAAW,GAAGX,YAAY,CAACJ,MAAM,EAAE,CAAC,CAAC,CAAA;EAC3C,UAAA,IAAMP,IAAI,GAAG,IAAIF,QAAQ,CAACwB,YAAW,CAAC7F,MAAM,EAAE6F,YAAW,CAAC9E,UAAU,EAAE8E,YAAW,CAACjE,MAAM,CAAC,CAAA;EACzF,UAAA,IAAMmE,CAAC,GAAGxB,IAAI,CAACyB,SAAS,CAAC,CAAC,CAAC,CAAA;EAC3B,UAAA,IAAID,CAAC,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE;EAC9B;EACAhC,YAAAA,UAAU,CAACS,OAAO,CAACvF,YAAY,CAAC,CAAA;EAChC,YAAA,MAAA;EACJ,WAAA;EACAuG,UAAAA,cAAc,GAAGI,CAAC,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG3B,IAAI,CAACyB,SAAS,CAAC,CAAC,CAAC,CAAA;YACxDN,KAAK,GAAG,CAAC,0BAAC;EACd,SAAC,MACI;EACD,UAAA,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAGa,cAAc,EAAE;EACtC,YAAA,MAAA;EACJ,WAAA;EACA,UAAA,IAAMrG,IAAI,GAAG4F,YAAY,CAACJ,MAAM,EAAEa,cAAc,CAAC,CAAA;EACjDzB,UAAAA,UAAU,CAACS,OAAO,CAAClC,YAAY,CAACmD,QAAQ,GAAGtG,IAAI,GAAGsF,YAAY,CAAC9C,MAAM,CAACxC,IAAI,CAAC,EAAEqD,UAAU,CAAC,CAAC,CAAA;YACzF+C,KAAK,GAAG,CAAC,yBAAC;EACd,SAAA;EACA,QAAA,IAAIC,cAAc,KAAK,CAAC,IAAIA,cAAc,GAAGH,UAAU,EAAE;EACrDtB,UAAAA,UAAU,CAACS,OAAO,CAACvF,YAAY,CAAC,CAAA;EAChC,UAAA,MAAA;EACJ,SAAA;EACJ,OAAA;EACJ,KAAA;EACJ,GAAC,CAAC,CAAA;EACN,CAAA;EACO,IAAM+G,QAAQ,GAAG,CAAC;;EC1JzB;EACA;EACA;EACA;EACA;;EAEO,SAASC,OAAOA,CAACrG,GAAG,EAAE;EAC3B,EAAA,IAAIA,GAAG,EAAE,OAAOsG,KAAK,CAACtG,GAAG,CAAC,CAAA;EAC5B,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,SAASsG,KAAKA,CAACtG,GAAG,EAAE;EAClB,EAAA,KAAK,IAAIZ,GAAG,IAAIiH,OAAO,CAAC3G,SAAS,EAAE;MACjCM,GAAG,CAACZ,GAAG,CAAC,GAAGiH,OAAO,CAAC3G,SAAS,CAACN,GAAG,CAAC,CAAA;EACnC,GAAA;EACA,EAAA,OAAOY,GAAG,CAAA;EACZ,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAqG,OAAO,CAAC3G,SAAS,CAAC6G,EAAE,GACpBF,OAAO,CAAC3G,SAAS,CAAC8G,gBAAgB,GAAG,UAASC,KAAK,EAAEC,EAAE,EAAC;IACtD,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,EAAE,CAAA;IACvC,CAAC,IAAI,CAACA,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,GAAG,IAAI,CAACE,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,IAAI,EAAE,EAC/D1C,IAAI,CAAC2C,EAAE,CAAC,CAAA;EACX,EAAA,OAAO,IAAI,CAAA;EACb,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAL,OAAO,CAAC3G,SAAS,CAACkH,IAAI,GAAG,UAASH,KAAK,EAAEC,EAAE,EAAC;IAC1C,SAASH,EAAEA,GAAG;EACZ,IAAA,IAAI,CAACM,GAAG,CAACJ,KAAK,EAAEF,EAAE,CAAC,CAAA;EACnBG,IAAAA,EAAE,CAACI,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAA;EAC3B,GAAA;IAEAR,EAAE,CAACG,EAAE,GAAGA,EAAE,CAAA;EACV,EAAA,IAAI,CAACH,EAAE,CAACE,KAAK,EAAEF,EAAE,CAAC,CAAA;EAClB,EAAA,OAAO,IAAI,CAAA;EACb,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAF,OAAO,CAAC3G,SAAS,CAACmH,GAAG,GACrBR,OAAO,CAAC3G,SAAS,CAACsH,cAAc,GAChCX,OAAO,CAAC3G,SAAS,CAACuH,kBAAkB,GACpCZ,OAAO,CAAC3G,SAAS,CAACwH,mBAAmB,GAAG,UAAST,KAAK,EAAEC,EAAE,EAAC;IACzD,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,EAAE,CAAA;;EAEvC;EACA,EAAA,IAAI,CAAC,IAAII,SAAS,CAAClF,MAAM,EAAE;EACzB,IAAA,IAAI,CAAC8E,UAAU,GAAG,EAAE,CAAA;EACpB,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;;EAEA;IACA,IAAIQ,SAAS,GAAG,IAAI,CAACR,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,CAAA;EAC5C,EAAA,IAAI,CAACU,SAAS,EAAE,OAAO,IAAI,CAAA;;EAE3B;EACA,EAAA,IAAI,CAAC,IAAIJ,SAAS,CAAClF,MAAM,EAAE;EACzB,IAAA,OAAO,IAAI,CAAC8E,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,CAAA;EACnC,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;;EAEA;EACA,EAAA,IAAIW,EAAE,CAAA;EACN,EAAA,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuF,SAAS,CAACtF,MAAM,EAAED,CAAC,EAAE,EAAE;EACzCwF,IAAAA,EAAE,GAAGD,SAAS,CAACvF,CAAC,CAAC,CAAA;MACjB,IAAIwF,EAAE,KAAKV,EAAE,IAAIU,EAAE,CAACV,EAAE,KAAKA,EAAE,EAAE;EAC7BS,MAAAA,SAAS,CAACE,MAAM,CAACzF,CAAC,EAAE,CAAC,CAAC,CAAA;EACtB,MAAA,MAAA;EACF,KAAA;EACF,GAAA;;EAEA;EACA;EACA,EAAA,IAAIuF,SAAS,CAACtF,MAAM,KAAK,CAAC,EAAE;EAC1B,IAAA,OAAO,IAAI,CAAC8E,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,CAAA;EACrC,GAAA;EAEA,EAAA,OAAO,IAAI,CAAA;EACb,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAJ,OAAO,CAAC3G,SAAS,CAAC4H,IAAI,GAAG,UAASb,KAAK,EAAC;IACtC,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,EAAE,CAAA;IAEvC,IAAIY,IAAI,GAAG,IAAI9D,KAAK,CAACsD,SAAS,CAAClF,MAAM,GAAG,CAAC,CAAC;MACtCsF,SAAS,GAAG,IAAI,CAACR,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,CAAA;EAE5C,EAAA,KAAK,IAAI7E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmF,SAAS,CAAClF,MAAM,EAAED,CAAC,EAAE,EAAE;MACzC2F,IAAI,CAAC3F,CAAC,GAAG,CAAC,CAAC,GAAGmF,SAAS,CAACnF,CAAC,CAAC,CAAA;EAC5B,GAAA;EAEA,EAAA,IAAIuF,SAAS,EAAE;EACbA,IAAAA,SAAS,GAAGA,SAAS,CAAC5B,KAAK,CAAC,CAAC,CAAC,CAAA;EAC9B,IAAA,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEM,GAAG,GAAGiF,SAAS,CAACtF,MAAM,EAAED,CAAC,GAAGM,GAAG,EAAE,EAAEN,CAAC,EAAE;QACpDuF,SAAS,CAACvF,CAAC,CAAC,CAACkF,KAAK,CAAC,IAAI,EAAES,IAAI,CAAC,CAAA;EAChC,KAAA;EACF,GAAA;EAEA,EAAA,OAAO,IAAI,CAAA;EACb,CAAC,CAAA;;EAED;EACAlB,OAAO,CAAC3G,SAAS,CAAC8H,YAAY,GAAGnB,OAAO,CAAC3G,SAAS,CAAC4H,IAAI,CAAA;;EAEvD;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAjB,OAAO,CAAC3G,SAAS,CAAC+H,SAAS,GAAG,UAAShB,KAAK,EAAC;IAC3C,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,EAAE,CAAA;IACvC,OAAO,IAAI,CAACA,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,IAAI,EAAE,CAAA;EAC3C,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAJ,OAAO,CAAC3G,SAAS,CAACgI,YAAY,GAAG,UAASjB,KAAK,EAAC;IAC9C,OAAO,CAAC,CAAE,IAAI,CAACgB,SAAS,CAAChB,KAAK,CAAC,CAAC5E,MAAM,CAAA;EACxC,CAAC;;ECxKM,IAAM8F,QAAQ,GAAI,YAAM;EAC3B,EAAA,IAAMC,kBAAkB,GAAG,OAAOC,OAAO,KAAK,UAAU,IAAI,OAAOA,OAAO,CAACC,OAAO,KAAK,UAAU,CAAA;EACjG,EAAA,IAAIF,kBAAkB,EAAE;EACpB,IAAA,OAAO,UAACR,EAAE,EAAA;QAAA,OAAKS,OAAO,CAACC,OAAO,EAAE,CAACxG,IAAI,CAAC8F,EAAE,CAAC,CAAA;EAAA,KAAA,CAAA;EAC7C,GAAC,MACI;MACD,OAAO,UAACA,EAAE,EAAEW,YAAY,EAAA;EAAA,MAAA,OAAKA,YAAY,CAACX,EAAE,EAAE,CAAC,CAAC,CAAA;EAAA,KAAA,CAAA;EACpD,GAAA;EACJ,CAAC,EAAG,CAAA;EACG,IAAMY,cAAc,GAAI,YAAM;EACjC,EAAA,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;EAC7B,IAAA,OAAOA,IAAI,CAAA;EACf,GAAC,MACI,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EACpC,IAAA,OAAOA,MAAM,CAAA;EACjB,GAAC,MACI;EACD,IAAA,OAAOC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAA;EACpC,GAAA;EACJ,CAAC,EAAG,CAAA;EACG,IAAMC,iBAAiB,GAAG,aAAa,CAAA;EACvC,SAASC,eAAeA,GAAG;;ECpB3B,SAASC,IAAIA,CAACtI,GAAG,EAAW;IAAA,KAAAuI,IAAAA,IAAA,GAAAxB,SAAA,CAAAlF,MAAA,EAAN2G,IAAI,OAAA/E,KAAA,CAAA8E,IAAA,GAAAA,CAAAA,GAAAA,IAAA,WAAAE,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA,EAAA,EAAA;EAAJD,IAAAA,IAAI,CAAAC,IAAA,GAAA1B,CAAAA,CAAAA,GAAAA,SAAA,CAAA0B,IAAA,CAAA,CAAA;EAAA,GAAA;IAC7B,OAAOD,IAAI,CAACxD,MAAM,CAAC,UAACC,GAAG,EAAEyD,CAAC,EAAK;EAC3B,IAAA,IAAI1I,GAAG,CAAC2I,cAAc,CAACD,CAAC,CAAC,EAAE;EACvBzD,MAAAA,GAAG,CAACyD,CAAC,CAAC,GAAG1I,GAAG,CAAC0I,CAAC,CAAC,CAAA;EACnB,KAAA;EACA,IAAA,OAAOzD,GAAG,CAAA;KACb,EAAE,EAAE,CAAC,CAAA;EACV,CAAA;EACA;EACA,IAAM2D,kBAAkB,GAAGC,cAAU,CAACC,UAAU,CAAA;EAChD,IAAMC,oBAAoB,GAAGF,cAAU,CAACG,YAAY,CAAA;EAC7C,SAASC,qBAAqBA,CAACjJ,GAAG,EAAEkJ,IAAI,EAAE;IAC7C,IAAIA,IAAI,CAACC,eAAe,EAAE;MACtBnJ,GAAG,CAAC+H,YAAY,GAAGa,kBAAkB,CAACQ,IAAI,CAACP,cAAU,CAAC,CAAA;MACtD7I,GAAG,CAACqJ,cAAc,GAAGN,oBAAoB,CAACK,IAAI,CAACP,cAAU,CAAC,CAAA;EAC9D,GAAC,MACI;MACD7I,GAAG,CAAC+H,YAAY,GAAGc,cAAU,CAACC,UAAU,CAACM,IAAI,CAACP,cAAU,CAAC,CAAA;MACzD7I,GAAG,CAACqJ,cAAc,GAAGR,cAAU,CAACG,YAAY,CAACI,IAAI,CAACP,cAAU,CAAC,CAAA;EACjE,GAAA;EACJ,CAAA;EACA;EACA,IAAMS,eAAe,GAAG,IAAI,CAAA;EAC5B;EACO,SAASrI,UAAUA,CAACjB,GAAG,EAAE;EAC5B,EAAA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACzB,OAAOuJ,UAAU,CAACvJ,GAAG,CAAC,CAAA;EAC1B,GAAA;EACA;EACA,EAAA,OAAOkG,IAAI,CAACsD,IAAI,CAAC,CAACxJ,GAAG,CAACiB,UAAU,IAAIjB,GAAG,CAACoF,IAAI,IAAIkE,eAAe,CAAC,CAAA;EACpE,CAAA;EACA,SAASC,UAAUA,CAACE,GAAG,EAAE;IACrB,IAAIC,CAAC,GAAG,CAAC;EAAE7H,IAAAA,MAAM,GAAG,CAAC,CAAA;EACrB,EAAA,KAAK,IAAID,CAAC,GAAG,CAAC,EAAE+H,CAAC,GAAGF,GAAG,CAAC5H,MAAM,EAAED,CAAC,GAAG+H,CAAC,EAAE/H,CAAC,EAAE,EAAE;EACxC8H,IAAAA,CAAC,GAAGD,GAAG,CAAC3H,UAAU,CAACF,CAAC,CAAC,CAAA;MACrB,IAAI8H,CAAC,GAAG,IAAI,EAAE;EACV7H,MAAAA,MAAM,IAAI,CAAC,CAAA;EACf,KAAC,MACI,IAAI6H,CAAC,GAAG,KAAK,EAAE;EAChB7H,MAAAA,MAAM,IAAI,CAAC,CAAA;OACd,MACI,IAAI6H,CAAC,GAAG,MAAM,IAAIA,CAAC,IAAI,MAAM,EAAE;EAChC7H,MAAAA,MAAM,IAAI,CAAC,CAAA;EACf,KAAC,MACI;EACDD,MAAAA,CAAC,EAAE,CAAA;EACHC,MAAAA,MAAM,IAAI,CAAC,CAAA;EACf,KAAA;EACJ,GAAA;EACA,EAAA,OAAOA,MAAM,CAAA;EACjB,CAAA;EACA;EACA;EACA;EACO,SAAS+H,YAAYA,GAAG;EAC3B,EAAA,OAAQC,IAAI,CAACC,GAAG,EAAE,CAACnK,QAAQ,CAAC,EAAE,CAAC,CAACqD,SAAS,CAAC,CAAC,CAAC,GACxCkD,IAAI,CAAC6D,MAAM,EAAE,CAACpK,QAAQ,CAAC,EAAE,CAAC,CAACqD,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;EAClD;;EC1DA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASvB,MAAMA,CAACzB,GAAG,EAAE;IACxB,IAAIyJ,GAAG,GAAG,EAAE,CAAA;EACZ,EAAA,KAAK,IAAI7H,CAAC,IAAI5B,GAAG,EAAE;EACf,IAAA,IAAIA,GAAG,CAAC2I,cAAc,CAAC/G,CAAC,CAAC,EAAE;EACvB,MAAA,IAAI6H,GAAG,CAAC5H,MAAM,EACV4H,GAAG,IAAI,GAAG,CAAA;EACdA,MAAAA,GAAG,IAAIO,kBAAkB,CAACpI,CAAC,CAAC,GAAG,GAAG,GAAGoI,kBAAkB,CAAChK,GAAG,CAAC4B,CAAC,CAAC,CAAC,CAAA;EACnE,KAAA;EACJ,GAAA;EACA,EAAA,OAAO6H,GAAG,CAAA;EACd,CAAA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAAS1H,MAAMA,CAACkI,EAAE,EAAE;IACvB,IAAIC,GAAG,GAAG,EAAE,CAAA;EACZ,EAAA,IAAIC,KAAK,GAAGF,EAAE,CAACrJ,KAAK,CAAC,GAAG,CAAC,CAAA;EACzB,EAAA,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAE+H,CAAC,GAAGQ,KAAK,CAACtI,MAAM,EAAED,CAAC,GAAG+H,CAAC,EAAE/H,CAAC,EAAE,EAAE;MAC1C,IAAIwI,IAAI,GAAGD,KAAK,CAACvI,CAAC,CAAC,CAAChB,KAAK,CAAC,GAAG,CAAC,CAAA;EAC9BsJ,IAAAA,GAAG,CAACG,kBAAkB,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,kBAAkB,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;EAClE,GAAA;EACA,EAAA,OAAOF,GAAG,CAAA;EACd;;;;;;;;;;;;;IC7BA,IAAII,CAAC,GAAG,IAAI,CAAA;EACZ,EAAA,IAAIC,CAAC,GAAGD,CAAC,GAAG,EAAE,CAAA;EACd,EAAA,IAAIE,CAAC,GAAGD,CAAC,GAAG,EAAE,CAAA;EACd,EAAA,IAAIE,CAAC,GAAGD,CAAC,GAAG,EAAE,CAAA;EACd,EAAA,IAAIE,CAAC,GAAGD,CAAC,GAAG,CAAC,CAAA;EACb,EAAA,IAAIE,CAAC,GAAGF,CAAC,GAAG,MAAM,CAAA;;EAElB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAG,EAAAA,EAAc,GAAG,SAAAA,EAAAA,CAASC,GAAG,EAAEC,OAAO,EAAE;EACtCA,IAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE,CAAA;EACvB,IAAA,IAAIxL,IAAI,GAAAyL,OAAA,CAAUF,GAAG,CAAA,CAAA;MACrB,IAAIvL,IAAI,KAAK,QAAQ,IAAIuL,GAAG,CAAChJ,MAAM,GAAG,CAAC,EAAE;QACvC,OAAOmJ,KAAK,CAACH,GAAG,CAAC,CAAA;OAClB,MAAM,IAAIvL,IAAI,KAAK,QAAQ,IAAI2L,QAAQ,CAACJ,GAAG,CAAC,EAAE;QAC7C,OAAOC,OAAO,CAAK,MAAA,CAAA,GAAGI,OAAO,CAACL,GAAG,CAAC,GAAGM,QAAQ,CAACN,GAAG,CAAC,CAAA;EACnD,KAAA;MACD,MAAM,IAAIO,KAAK,CACb,uDAAuD,GACrDC,IAAI,CAACC,SAAS,CAACT,GAAG,CACxB,CAAG,CAAA;KACF,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;IAEA,SAASG,KAAKA,CAACvB,GAAG,EAAE;EAClBA,IAAAA,GAAG,GAAGrG,MAAM,CAACqG,GAAG,CAAC,CAAA;EACjB,IAAA,IAAIA,GAAG,CAAC5H,MAAM,GAAG,GAAG,EAAE;EACpB,MAAA,OAAA;EACD,KAAA;EACD,IAAA,IAAI0J,KAAK,GAAG,kIAAkI,CAACC,IAAI,CACjJ/B,GACJ,CAAG,CAAA;MACD,IAAI,CAAC8B,KAAK,EAAE;EACV,MAAA,OAAA;EACD,KAAA;MACD,IAAIvF,CAAC,GAAGyF,UAAU,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;EAC5B,IAAA,IAAIjM,IAAI,GAAG,CAACiM,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAEG,WAAW,EAAE,CAAA;EAC3C,IAAA,QAAQpM,IAAI;EACV,MAAA,KAAK,OAAO,CAAA;EACZ,MAAA,KAAK,MAAM,CAAA;EACX,MAAA,KAAK,KAAK,CAAA;EACV,MAAA,KAAK,IAAI,CAAA;EACT,MAAA,KAAK,GAAG;UACN,OAAO0G,CAAC,GAAG2E,CAAC,CAAA;EACd,MAAA,KAAK,OAAO,CAAA;EACZ,MAAA,KAAK,MAAM,CAAA;EACX,MAAA,KAAK,GAAG;UACN,OAAO3E,CAAC,GAAG0E,CAAC,CAAA;EACd,MAAA,KAAK,MAAM,CAAA;EACX,MAAA,KAAK,KAAK,CAAA;EACV,MAAA,KAAK,GAAG;UACN,OAAO1E,CAAC,GAAGyE,CAAC,CAAA;EACd,MAAA,KAAK,OAAO,CAAA;EACZ,MAAA,KAAK,MAAM,CAAA;EACX,MAAA,KAAK,KAAK,CAAA;EACV,MAAA,KAAK,IAAI,CAAA;EACT,MAAA,KAAK,GAAG;UACN,OAAOzE,CAAC,GAAGwE,CAAC,CAAA;EACd,MAAA,KAAK,SAAS,CAAA;EACd,MAAA,KAAK,QAAQ,CAAA;EACb,MAAA,KAAK,MAAM,CAAA;EACX,MAAA,KAAK,KAAK,CAAA;EACV,MAAA,KAAK,GAAG;UACN,OAAOxE,CAAC,GAAGuE,CAAC,CAAA;EACd,MAAA,KAAK,SAAS,CAAA;EACd,MAAA,KAAK,QAAQ,CAAA;EACb,MAAA,KAAK,MAAM,CAAA;EACX,MAAA,KAAK,KAAK,CAAA;EACV,MAAA,KAAK,GAAG;UACN,OAAOvE,CAAC,GAAGsE,CAAC,CAAA;EACd,MAAA,KAAK,cAAc,CAAA;EACnB,MAAA,KAAK,aAAa,CAAA;EAClB,MAAA,KAAK,OAAO,CAAA;EACZ,MAAA,KAAK,MAAM,CAAA;EACX,MAAA,KAAK,IAAI;EACP,QAAA,OAAOtE,CAAC,CAAA;EACV,MAAA;EACE,QAAA,OAAO2F,SAAS,CAAA;EACnB,KAAA;EACH,GAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;IAEA,SAASR,QAAQA,CAACP,EAAE,EAAE;EACpB,IAAA,IAAIgB,KAAK,GAAG1F,IAAI,CAAC2F,GAAG,CAACjB,EAAE,CAAC,CAAA;MACxB,IAAIgB,KAAK,IAAInB,CAAC,EAAE;QACd,OAAOvE,IAAI,CAAC4F,KAAK,CAAClB,EAAE,GAAGH,CAAC,CAAC,GAAG,GAAG,CAAA;EAChC,KAAA;MACD,IAAImB,KAAK,IAAIpB,CAAC,EAAE;QACd,OAAOtE,IAAI,CAAC4F,KAAK,CAAClB,EAAE,GAAGJ,CAAC,CAAC,GAAG,GAAG,CAAA;EAChC,KAAA;MACD,IAAIoB,KAAK,IAAIrB,CAAC,EAAE;QACd,OAAOrE,IAAI,CAAC4F,KAAK,CAAClB,EAAE,GAAGL,CAAC,CAAC,GAAG,GAAG,CAAA;EAChC,KAAA;MACD,IAAIqB,KAAK,IAAItB,CAAC,EAAE;QACd,OAAOpE,IAAI,CAAC4F,KAAK,CAAClB,EAAE,GAAGN,CAAC,CAAC,GAAG,GAAG,CAAA;EAChC,KAAA;MACD,OAAOM,EAAE,GAAG,IAAI,CAAA;EAClB,GAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;IAEA,SAASM,OAAOA,CAACN,EAAE,EAAE;EACnB,IAAA,IAAIgB,KAAK,GAAG1F,IAAI,CAAC2F,GAAG,CAACjB,EAAE,CAAC,CAAA;MACxB,IAAIgB,KAAK,IAAInB,CAAC,EAAE;QACd,OAAOsB,MAAM,CAACnB,EAAE,EAAEgB,KAAK,EAAEnB,CAAC,EAAE,KAAK,CAAC,CAAA;EACnC,KAAA;MACD,IAAImB,KAAK,IAAIpB,CAAC,EAAE;QACd,OAAOuB,MAAM,CAACnB,EAAE,EAAEgB,KAAK,EAAEpB,CAAC,EAAE,MAAM,CAAC,CAAA;EACpC,KAAA;MACD,IAAIoB,KAAK,IAAIrB,CAAC,EAAE;QACd,OAAOwB,MAAM,CAACnB,EAAE,EAAEgB,KAAK,EAAErB,CAAC,EAAE,QAAQ,CAAC,CAAA;EACtC,KAAA;MACD,IAAIqB,KAAK,IAAItB,CAAC,EAAE;QACd,OAAOyB,MAAM,CAACnB,EAAE,EAAEgB,KAAK,EAAEtB,CAAC,EAAE,QAAQ,CAAC,CAAA;EACtC,KAAA;MACD,OAAOM,EAAE,GAAG,KAAK,CAAA;EACnB,GAAA;;EAEA;EACA;EACA;;IAEA,SAASmB,MAAMA,CAACnB,EAAE,EAAEgB,KAAK,EAAE5F,CAAC,EAAEgG,IAAI,EAAE;EAClC,IAAA,IAAIC,QAAQ,GAAGL,KAAK,IAAI5F,CAAC,GAAG,GAAG,CAAA;EAC/B,IAAA,OAAOE,IAAI,CAAC4F,KAAK,CAAClB,EAAE,GAAG5E,CAAC,CAAC,GAAG,GAAG,GAAGgG,IAAI,IAAIC,QAAQ,GAAG,GAAG,GAAG,EAAE,CAAC,CAAA;EAChE,GAAA;;;;EChKA;EACA;EACA;EACA;;EAEA,SAASC,KAAKA,CAACC,GAAG,EAAE;IACnBC,WAAW,CAACC,KAAK,GAAGD,WAAW,CAAA;IAC/BA,WAAW,CAAA,SAAA,CAAQ,GAAGA,WAAW,CAAA;IACjCA,WAAW,CAACE,MAAM,GAAGA,MAAM,CAAA;IAC3BF,WAAW,CAACG,OAAO,GAAGA,OAAO,CAAA;IAC7BH,WAAW,CAACI,MAAM,GAAGA,MAAM,CAAA;IAC3BJ,WAAW,CAACK,OAAO,GAAGA,OAAO,CAAA;EAC7BL,EAAAA,WAAW,CAACM,QAAQ,GAAGC,WAAa,CAAA;IACpCP,WAAW,CAACQ,OAAO,GAAGA,OAAO,CAAA;IAE7B7N,MAAM,CAACG,IAAI,CAACiN,GAAG,CAAC,CAAChN,OAAO,CAAC,UAAAC,GAAG,EAAI;EAC/BgN,IAAAA,WAAW,CAAChN,GAAG,CAAC,GAAG+M,GAAG,CAAC/M,GAAG,CAAC,CAAA;EAC7B,GAAE,CAAC,CAAA;;EAEH;EACA;EACA;;IAECgN,WAAW,CAACS,KAAK,GAAG,EAAE,CAAA;IACtBT,WAAW,CAACU,KAAK,GAAG,EAAE,CAAA;;EAEvB;EACA;EACA;EACA;EACA;EACCV,EAAAA,WAAW,CAACW,UAAU,GAAG,EAAE,CAAA;;EAE5B;EACA;EACA;EACA;EACA;EACA;IACC,SAASC,WAAWA,CAACC,SAAS,EAAE;MAC/B,IAAIC,IAAI,GAAG,CAAC,CAAA;EAEZ,IAAA,KAAK,IAAItL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqL,SAAS,CAACpL,MAAM,EAAED,CAAC,EAAE,EAAE;EAC1CsL,MAAAA,IAAI,GAAI,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAID,SAAS,CAACnL,UAAU,CAACF,CAAC,CAAC,CAAA;QACrDsL,IAAI,IAAI,CAAC,CAAC;EACV,KAAA;EAED,IAAA,OAAOd,WAAW,CAACe,MAAM,CAACjH,IAAI,CAAC2F,GAAG,CAACqB,IAAI,CAAC,GAAGd,WAAW,CAACe,MAAM,CAACtL,MAAM,CAAC,CAAA;EACrE,GAAA;IACDuK,WAAW,CAACY,WAAW,GAAGA,WAAW,CAAA;;EAEtC;EACA;EACA;EACA;EACA;EACA;EACA;IACC,SAASZ,WAAWA,CAACa,SAAS,EAAE;EAC/B,IAAA,IAAIG,QAAQ,CAAA;MACZ,IAAIC,cAAc,GAAG,IAAI,CAAA;EACzB,IAAA,IAAIC,eAAe,CAAA;EACnB,IAAA,IAAIC,YAAY,CAAA;MAEhB,SAASlB,KAAKA,GAAU;EAAA,MAAA,KAAA,IAAA9D,IAAA,GAAAxB,SAAA,CAAAlF,MAAA,EAAN0F,IAAI,GAAA9D,IAAAA,KAAA,CAAA8E,IAAA,GAAAE,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA,EAAA,EAAA;EAAJlB,QAAAA,IAAI,CAAAkB,IAAA,CAAA1B,GAAAA,SAAA,CAAA0B,IAAA,CAAA,CAAA;EAAA,OAAA;EACxB;EACG,MAAA,IAAI,CAAC4D,KAAK,CAACI,OAAO,EAAE;EACnB,QAAA,OAAA;EACA,OAAA;QAED,IAAMxE,IAAI,GAAGoE,KAAK,CAAA;;EAErB;QACG,IAAMmB,IAAI,GAAGC,MAAM,CAAC,IAAI5D,IAAI,EAAE,CAAC,CAAA;EAC/B,MAAA,IAAMe,EAAE,GAAG4C,IAAI,IAAIJ,QAAQ,IAAII,IAAI,CAAC,CAAA;QACpCvF,IAAI,CAACyF,IAAI,GAAG9C,EAAE,CAAA;QACd3C,IAAI,CAAC0F,IAAI,GAAGP,QAAQ,CAAA;QACpBnF,IAAI,CAACuF,IAAI,GAAGA,IAAI,CAAA;EAChBJ,MAAAA,QAAQ,GAAGI,IAAI,CAAA;EAEfjG,MAAAA,IAAI,CAAC,CAAC,CAAC,GAAG6E,WAAW,CAACE,MAAM,CAAC/E,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;EAErC,MAAA,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;EACpC;EACIA,QAAAA,IAAI,CAACqG,OAAO,CAAC,IAAI,CAAC,CAAA;EAClB,OAAA;;EAEJ;QACG,IAAIC,KAAK,GAAG,CAAC,CAAA;EACbtG,MAAAA,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,CAACuG,OAAO,CAAC,eAAe,EAAE,UAACvC,KAAK,EAAEwC,MAAM,EAAK;EACjE;UACI,IAAIxC,KAAK,KAAK,IAAI,EAAE;EACnB,UAAA,OAAO,GAAG,CAAA;EACV,SAAA;EACDsC,QAAAA,KAAK,EAAE,CAAA;EACP,QAAA,IAAMG,SAAS,GAAG5B,WAAW,CAACW,UAAU,CAACgB,MAAM,CAAC,CAAA;EAChD,QAAA,IAAI,OAAOC,SAAS,KAAK,UAAU,EAAE;EACpC,UAAA,IAAMnD,GAAG,GAAGtD,IAAI,CAACsG,KAAK,CAAC,CAAA;YACvBtC,KAAK,GAAGyC,SAAS,CAACpO,IAAI,CAACqI,IAAI,EAAE4C,GAAG,CAAC,CAAA;;EAEtC;EACKtD,UAAAA,IAAI,CAACF,MAAM,CAACwG,KAAK,EAAE,CAAC,CAAC,CAAA;EACrBA,UAAAA,KAAK,EAAE,CAAA;EACP,SAAA;EACD,QAAA,OAAOtC,KAAK,CAAA;EAChB,OAAI,CAAC,CAAA;;EAEL;QACGa,WAAW,CAAC6B,UAAU,CAACrO,IAAI,CAACqI,IAAI,EAAEV,IAAI,CAAC,CAAA;QAEvC,IAAM2G,KAAK,GAAGjG,IAAI,CAACkG,GAAG,IAAI/B,WAAW,CAAC+B,GAAG,CAAA;EACzCD,MAAAA,KAAK,CAACpH,KAAK,CAACmB,IAAI,EAAEV,IAAI,CAAC,CAAA;EACvB,KAAA;MAED8E,KAAK,CAACY,SAAS,GAAGA,SAAS,CAAA;EAC3BZ,IAAAA,KAAK,CAAC+B,SAAS,GAAGhC,WAAW,CAACgC,SAAS,EAAE,CAAA;MACzC/B,KAAK,CAACgC,KAAK,GAAGjC,WAAW,CAACY,WAAW,CAACC,SAAS,CAAC,CAAA;MAChDZ,KAAK,CAACiC,MAAM,GAAGA,MAAM,CAAA;EACrBjC,IAAAA,KAAK,CAACO,OAAO,GAAGR,WAAW,CAACQ,OAAO,CAAC;;EAEpC7N,IAAAA,MAAM,CAACwP,cAAc,CAAClC,KAAK,EAAE,SAAS,EAAE;EACvCmC,MAAAA,UAAU,EAAE,IAAI;EAChBC,MAAAA,YAAY,EAAE,KAAK;QACnBC,GAAG,EAAE,SAAAA,GAAAA,GAAM;UACV,IAAIrB,cAAc,KAAK,IAAI,EAAE;EAC5B,UAAA,OAAOA,cAAc,CAAA;EACrB,SAAA;EACD,QAAA,IAAIC,eAAe,KAAKlB,WAAW,CAACuC,UAAU,EAAE;YAC/CrB,eAAe,GAAGlB,WAAW,CAACuC,UAAU,CAAA;EACxCpB,UAAAA,YAAY,GAAGnB,WAAW,CAACK,OAAO,CAACQ,SAAS,CAAC,CAAA;EAC7C,SAAA;EAED,QAAA,OAAOM,YAAY,CAAA;SACnB;EACDqB,MAAAA,GAAG,EAAE,SAAAA,GAAAC,CAAAA,CAAC,EAAI;EACTxB,QAAAA,cAAc,GAAGwB,CAAC,CAAA;EAClB,OAAA;EACJ,KAAG,CAAC,CAAA;;EAEJ;EACE,IAAA,IAAI,OAAOzC,WAAW,CAAC0C,IAAI,KAAK,UAAU,EAAE;EAC3C1C,MAAAA,WAAW,CAAC0C,IAAI,CAACzC,KAAK,CAAC,CAAA;EACvB,KAAA;EAED,IAAA,OAAOA,KAAK,CAAA;EACZ,GAAA;EAED,EAAA,SAASiC,MAAMA,CAACrB,SAAS,EAAE8B,SAAS,EAAE;EACrC,IAAA,IAAMC,QAAQ,GAAG5C,WAAW,CAAC,IAAI,CAACa,SAAS,IAAI,OAAO8B,SAAS,KAAK,WAAW,GAAG,GAAG,GAAGA,SAAS,CAAC,GAAG9B,SAAS,CAAC,CAAA;EAC/G+B,IAAAA,QAAQ,CAACb,GAAG,GAAG,IAAI,CAACA,GAAG,CAAA;EACvB,IAAA,OAAOa,QAAQ,CAAA;EACf,GAAA;;EAEF;EACA;EACA;EACA;EACA;EACA;EACA;IACC,SAASxC,MAAMA,CAACmC,UAAU,EAAE;EAC3BvC,IAAAA,WAAW,CAAC6C,IAAI,CAACN,UAAU,CAAC,CAAA;MAC5BvC,WAAW,CAACuC,UAAU,GAAGA,UAAU,CAAA;MAEnCvC,WAAW,CAACS,KAAK,GAAG,EAAE,CAAA;MACtBT,WAAW,CAACU,KAAK,GAAG,EAAE,CAAA;EAEtB,IAAA,IAAIlL,CAAC,CAAA;EACL,IAAA,IAAMhB,KAAK,GAAG,CAAC,OAAO+N,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAG,EAAE,EAAE/N,KAAK,CAAC,QAAQ,CAAC,CAAA;EAChF,IAAA,IAAMsB,GAAG,GAAGtB,KAAK,CAACiB,MAAM,CAAA;MAExB,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,GAAG,EAAEN,CAAC,EAAE,EAAE;EACzB,MAAA,IAAI,CAAChB,KAAK,CAACgB,CAAC,CAAC,EAAE;EAClB;EACI,QAAA,SAAA;EACA,OAAA;QAED+M,UAAU,GAAG/N,KAAK,CAACgB,CAAC,CAAC,CAACkM,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;EAE3C,MAAA,IAAIa,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;EAC1BvC,QAAAA,WAAW,CAACU,KAAK,CAAC/I,IAAI,CAAC,IAAImL,MAAM,CAAC,GAAG,GAAGP,UAAU,CAACpJ,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;EACvE,OAAI,MAAM;EACN6G,QAAAA,WAAW,CAACS,KAAK,CAAC9I,IAAI,CAAC,IAAImL,MAAM,CAAC,GAAG,GAAGP,UAAU,GAAG,GAAG,CAAC,CAAC,CAAA;EAC1D,OAAA;EACD,KAAA;EACD,GAAA;;EAEF;EACA;EACA;EACA;EACA;EACA;IACC,SAASpC,OAAOA,GAAG;EAClB,IAAA,IAAMoC,UAAU,GAAG,EAAAQ,CAAAA,MAAA,CAAAC,kBAAA,CACfhD,WAAW,CAACS,KAAK,CAACwC,GAAG,CAACC,WAAW,CAAC,CAAAF,EAAAA,kBAAA,CAClChD,WAAW,CAACU,KAAK,CAACuC,GAAG,CAACC,WAAW,CAAC,CAACD,GAAG,CAAC,UAAApC,SAAS,EAAA;QAAA,OAAI,GAAG,GAAGA,SAAS,CAAA;EAAA,KAAA,CAAC,CACtEtJ,CAAAA,CAAAA,IAAI,CAAC,GAAG,CAAC,CAAA;EACXyI,IAAAA,WAAW,CAACI,MAAM,CAAC,EAAE,CAAC,CAAA;EACtB,IAAA,OAAOmC,UAAU,CAAA;EACjB,GAAA;;EAEF;EACA;EACA;EACA;EACA;EACA;EACA;IACC,SAASlC,OAAOA,CAACT,IAAI,EAAE;MACtB,IAAIA,IAAI,CAACA,IAAI,CAACnK,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;EAClC,MAAA,OAAO,IAAI,CAAA;EACX,KAAA;EAED,IAAA,IAAID,CAAC,CAAA;EACL,IAAA,IAAIM,GAAG,CAAA;EAEP,IAAA,KAAKN,CAAC,GAAG,CAAC,EAAEM,GAAG,GAAGkK,WAAW,CAACU,KAAK,CAACjL,MAAM,EAAED,CAAC,GAAGM,GAAG,EAAEN,CAAC,EAAE,EAAE;QACzD,IAAIwK,WAAW,CAACU,KAAK,CAAClL,CAAC,CAAC,CAAC2N,IAAI,CAACvD,IAAI,CAAC,EAAE;EACpC,QAAA,OAAO,KAAK,CAAA;EACZ,OAAA;EACD,KAAA;EAED,IAAA,KAAKpK,CAAC,GAAG,CAAC,EAAEM,GAAG,GAAGkK,WAAW,CAACS,KAAK,CAAChL,MAAM,EAAED,CAAC,GAAGM,GAAG,EAAEN,CAAC,EAAE,EAAE;QACzD,IAAIwK,WAAW,CAACS,KAAK,CAACjL,CAAC,CAAC,CAAC2N,IAAI,CAACvD,IAAI,CAAC,EAAE;EACpC,QAAA,OAAO,IAAI,CAAA;EACX,OAAA;EACD,KAAA;EAED,IAAA,OAAO,KAAK,CAAA;EACZ,GAAA;;EAEF;EACA;EACA;EACA;EACA;EACA;EACA;IACC,SAASsD,WAAWA,CAACE,MAAM,EAAE;MAC5B,OAAOA,MAAM,CAAC7P,QAAQ,EAAE,CACtBqD,SAAS,CAAC,CAAC,EAAEwM,MAAM,CAAC7P,QAAQ,EAAE,CAACkC,MAAM,GAAG,CAAC,CAAC,CAC1CiM,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAA;EACzB,GAAA;;EAEF;EACA;EACA;EACA;EACA;EACA;EACA;IACC,SAASxB,MAAMA,CAACzB,GAAG,EAAE;MACpB,IAAIA,GAAG,YAAYO,KAAK,EAAE;EACzB,MAAA,OAAOP,GAAG,CAAC4E,KAAK,IAAI5E,GAAG,CAAC6E,OAAO,CAAA;EAC/B,KAAA;EACD,IAAA,OAAO7E,GAAG,CAAA;EACV,GAAA;;EAEF;EACA;EACA;EACA;IACC,SAAS+B,OAAOA,GAAG;EAClB+C,IAAAA,OAAO,CAACC,IAAI,CAAC,uIAAuI,CAAC,CAAA;EACrJ,GAAA;IAEDxD,WAAW,CAACI,MAAM,CAACJ,WAAW,CAACyD,IAAI,EAAE,CAAC,CAAA;EAEtC,EAAA,OAAOzD,WAAW,CAAA;EACnB,CAAA;EAEA,IAAA0D,MAAc,GAAG5D,KAAK;;;;;EC/QtB;EACA;EACA;;IAEA6D,OAAA,CAAA9B,UAAA,GAAqBA,UAAU,CAAA;IAC/B8B,OAAA,CAAAd,IAAA,GAAeA,IAAI,CAAA;IACnBc,OAAA,CAAAF,IAAA,GAAeA,IAAI,CAAA;IACnBE,OAAA,CAAA3B,SAAA,GAAoBA,SAAS,CAAA;EAC7B2B,EAAAA,OAAkB,CAAAC,OAAA,GAAAC,YAAY,EAAE,CAAA;IAChCF,OAAA,CAAAnD,OAAA,GAAmB,YAAM;MACxB,IAAIsD,MAAM,GAAG,KAAK,CAAA;EAElB,IAAA,OAAO,YAAM;QACZ,IAAI,CAACA,MAAM,EAAE;EACZA,QAAAA,MAAM,GAAG,IAAI,CAAA;EACbP,QAAAA,OAAO,CAACC,IAAI,CAAC,uIAAuI,CAAC,CAAA;EACrJ,OAAA;OACD,CAAA;EACF,GAAC,EAAG,CAAA;;EAEJ;EACA;EACA;;EAEAG,EAAAA,OAAiB,CAAA5C,MAAA,GAAA,CAChB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACT,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;IACA,SAASiB,SAASA,GAAG;EACrB;EACA;EACA;MACC,IAAI,OAAOlG,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACiI,OAAO,KAAKjI,MAAM,CAACiI,OAAO,CAAC7Q,IAAI,KAAK,UAAU,IAAI4I,MAAM,CAACiI,OAAO,CAACC,MAAM,CAAC,EAAE;EACrH,MAAA,OAAO,IAAI,CAAA;EACX,KAAA;;EAEF;MACC,IAAI,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,SAAS,IAAID,SAAS,CAACC,SAAS,CAAC5E,WAAW,EAAE,CAACH,KAAK,CAAC,uBAAuB,CAAC,EAAE;EAChI,MAAA,OAAO,KAAK,CAAA;EACZ,KAAA;;EAEF;EACA;MACC,OAAQ,OAAOgF,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAACC,eAAe,IAAID,QAAQ,CAACC,eAAe,CAACC,KAAK,IAAIF,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACC,gBAAgB;EACzJ;MACG,OAAOxI,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACyH,OAAO,KAAKzH,MAAM,CAACyH,OAAO,CAACgB,OAAO,IAAKzI,MAAM,CAACyH,OAAO,CAACiB,SAAS,IAAI1I,MAAM,CAACyH,OAAO,CAACkB,KAAM,CAAE;EACrI;EACA;EACG,IAAA,OAAOR,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,SAAS,IAAID,SAAS,CAACC,SAAS,CAAC5E,WAAW,EAAE,CAACH,KAAK,CAAC,gBAAgB,CAAC,IAAIuF,QAAQ,CAAC5B,MAAM,CAAC6B,EAAE,EAAE,EAAE,CAAC,IAAI,EAAG;EACzJ;EACG,IAAA,OAAOV,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,SAAS,IAAID,SAAS,CAACC,SAAS,CAAC5E,WAAW,EAAE,CAACH,KAAK,CAAC,oBAAoB,CAAE,CAAA;EAC5H,GAAA;;EAEA;EACA;EACA;EACA;EACA;;IAEA,SAAS0C,UAAUA,CAAC1G,IAAI,EAAE;MACzBA,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC6G,SAAS,GAAG,IAAI,GAAG,EAAE,IACpC,IAAI,CAACnB,SAAS,IACb,IAAI,CAACmB,SAAS,GAAG,KAAK,GAAG,GAAG,CAAC,GAC9B7G,IAAI,CAAC,CAAC,CAAC,IACN,IAAI,CAAC6G,SAAS,GAAG,KAAK,GAAG,GAAG,CAAC,GAC9B,GAAG,GAAG4C,MAAM,CAACjB,OAAO,CAACrD,QAAQ,CAAC,IAAI,CAACgB,IAAI,CAAC,CAAA;EAEzC,IAAA,IAAI,CAAC,IAAI,CAACU,SAAS,EAAE;EACpB,MAAA,OAAA;EACA,KAAA;EAED,IAAA,IAAM1E,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC2E,KAAK,CAAA;MAChC9G,IAAI,CAACF,MAAM,CAAC,CAAC,EAAE,CAAC,EAAEqC,CAAC,EAAE,gBAAgB,CAAC,CAAA;;EAEvC;EACA;EACA;MACC,IAAImE,KAAK,GAAG,CAAC,CAAA;MACb,IAAIoD,KAAK,GAAG,CAAC,CAAA;MACb1J,IAAI,CAAC,CAAC,CAAC,CAACuG,OAAO,CAAC,aAAa,EAAE,UAAAvC,KAAK,EAAI;QACvC,IAAIA,KAAK,KAAK,IAAI,EAAE;EACnB,QAAA,OAAA;EACA,OAAA;EACDsC,MAAAA,KAAK,EAAE,CAAA;QACP,IAAItC,KAAK,KAAK,IAAI,EAAE;EACtB;EACA;EACG0F,QAAAA,KAAK,GAAGpD,KAAK,CAAA;EACb,OAAA;EACH,KAAE,CAAC,CAAA;MAEFtG,IAAI,CAACF,MAAM,CAAC4J,KAAK,EAAE,CAAC,EAAEvH,CAAC,CAAC,CAAA;EACzB,GAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAqG,EAAAA,OAAc,CAAA5B,GAAA,GAAAwB,OAAO,CAACtD,KAAK,IAAIsD,OAAO,CAACxB,GAAG,IAAK,YAAM,EAAG,CAAA;;EAExD;EACA;EACA;EACA;EACA;EACA;IACA,SAASc,IAAIA,CAACN,UAAU,EAAE;MACzB,IAAI;EACH,MAAA,IAAIA,UAAU,EAAE;UACfoB,OAAO,CAACC,OAAO,CAACkB,OAAO,CAAC,OAAO,EAAEvC,UAAU,CAAC,CAAA;EAC/C,OAAG,MAAM;EACNoB,QAAAA,OAAO,CAACC,OAAO,CAACmB,UAAU,CAAC,OAAO,CAAC,CAAA;EACnC,OAAA;OACD,CAAC,OAAOC,KAAK,EAAE;EACjB;EACA;EAAA,KAAA;EAEA,GAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;IACA,SAASvB,IAAIA,GAAG;EACf,IAAA,IAAIwB,CAAC,CAAA;MACL,IAAI;QACHA,CAAC,GAAGtB,OAAO,CAACC,OAAO,CAACsB,OAAO,CAAC,OAAO,CAAC,CAAA;OACpC,CAAC,OAAOF,KAAK,EAAE;EACjB;EACA;EAAA,KAAA;;EAGA;MACC,IAAI,CAACC,CAAC,IAAI,OAAOlB,OAAO,KAAK,WAAW,IAAI,KAAK,IAAIA,OAAO,EAAE;EAC7DkB,MAAAA,CAAC,GAAGlB,OAAO,CAAChE,GAAG,CAACoF,KAAK,CAAA;EACrB,KAAA;EAED,IAAA,OAAOF,CAAC,CAAA;EACT,GAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;IAEA,SAASpB,YAAYA,GAAG;MACvB,IAAI;EACL;EACA;EACE,MAAA,OAAOuB,YAAY,CAAA;OACnB,CAAC,OAAOJ,KAAK,EAAE;EACjB;EACA;EAAA,KAAA;EAEA,GAAA;EAEAJ,EAAAA,MAAA,CAAAjB,OAAA,GAAiBpD,MAAmB,CAACoD,OAAO,CAAC,CAAA;EAE7C,EAAA,IAAOhD,UAAU,GAAIiE,MAAM,CAACjB,OAAO,CAA5BhD,UAAU,CAAA;;EAEjB;EACA;EACA;;EAEAA,EAAAA,UAAU,CAACzH,CAAC,GAAG,UAAUuJ,CAAC,EAAE;MAC3B,IAAI;EACH,MAAA,OAAOxD,IAAI,CAACC,SAAS,CAACuD,CAAC,CAAC,CAAA;OACxB,CAAC,OAAOuC,KAAK,EAAE;EACf,MAAA,OAAO,8BAA8B,GAAGA,KAAK,CAAC1B,OAAO,CAAA;EACrD,KAAA;KACD,CAAA;;;;;ECvQD,IAAMrD,OAAK,GAAGoF,WAAW,CAAC,4BAA4B,CAAC,CAAC;EAC3CC,IAAAA,cAAc,0BAAAC,MAAA,EAAA;EACvB,EAAA,SAAAD,eAAYE,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAAA,IAAA,IAAAC,KAAA,CAAA;EACtCA,IAAAA,KAAA,GAAAJ,MAAA,CAAA/R,IAAA,CAAA,IAAA,EAAMgS,MAAM,CAAC,IAAA,IAAA,CAAA;MACbG,KAAA,CAAKF,WAAW,GAAGA,WAAW,CAAA;MAC9BE,KAAA,CAAKD,OAAO,GAAGA,OAAO,CAAA;MACtBC,KAAA,CAAKzS,IAAI,GAAG,gBAAgB,CAAA;EAAC,IAAA,OAAAyS,KAAA,CAAA;EACjC,GAAA;IAACC,cAAA,CAAAN,cAAA,EAAAC,MAAA,CAAA,CAAA;EAAA,EAAA,OAAAD,cAAA,CAAA;EAAA,CAAAO,eAAAA,gBAAA,CAN+B7G,KAAK,CAAA,CAAA,CAAA;EAQ5B8G,IAAAA,SAAS,0BAAAC,QAAA,EAAA;EAClB;EACJ;EACA;EACA;EACA;EACA;IACI,SAAAD,SAAAA,CAAYhJ,IAAI,EAAE;EAAA,IAAA,IAAAkJ,MAAA,CAAA;EACdA,IAAAA,MAAA,GAAAD,QAAA,CAAAvS,IAAA,KAAM,CAAC,IAAA,IAAA,CAAA;MACPwS,MAAA,CAAKC,QAAQ,GAAG,KAAK,CAAA;EACrBpJ,IAAAA,qBAAqB,CAAAmJ,MAAA,EAAOlJ,IAAI,CAAC,CAAA;MACjCkJ,MAAA,CAAKlJ,IAAI,GAAGA,IAAI,CAAA;EAChBkJ,IAAAA,MAAA,CAAKE,KAAK,GAAGpJ,IAAI,CAACoJ,KAAK,CAAA;EACvBF,IAAAA,MAAA,CAAKG,MAAM,GAAGrJ,IAAI,CAACqJ,MAAM,CAAA;EACzBH,IAAAA,MAAA,CAAKhS,cAAc,GAAG,CAAC8I,IAAI,CAACsJ,WAAW,CAAA;EAAC,IAAA,OAAAJ,MAAA,CAAA;EAC5C,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IARIJ,cAAA,CAAAE,SAAA,EAAAC,QAAA,CAAA,CAAA;EAAA,EAAA,IAAAM,MAAA,GAAAP,SAAA,CAAAxS,SAAA,CAAA;IAAA+S,MAAA,CASAC,OAAO,GAAP,SAAAA,OAAAA,CAAQd,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAClCK,IAAAA,QAAA,CAAAzS,SAAA,CAAM8H,YAAY,CAAA5H,IAAA,CAAC,IAAA,EAAA,OAAO,EAAE,IAAI8R,cAAc,CAACE,MAAM,EAAEC,WAAW,EAAEC,OAAO,CAAC,CAAA,CAAA;EAC5E,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA,MAFI;EAAAW,EAAAA,MAAA,CAGAE,IAAI,GAAJ,SAAAA,OAAO;MACH,IAAI,CAACC,UAAU,GAAG,SAAS,CAAA;MAC3B,IAAI,CAACC,MAAM,EAAE,CAAA;EACb,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA,MAFI;EAAAJ,EAAAA,MAAA,CAGAK,KAAK,GAAL,SAAAA,QAAQ;MACJ,IAAI,IAAI,CAACF,UAAU,KAAK,SAAS,IAAI,IAAI,CAACA,UAAU,KAAK,MAAM,EAAE;QAC7D,IAAI,CAACG,OAAO,EAAE,CAAA;QACd,IAAI,CAACC,OAAO,EAAE,CAAA;EAClB,KAAA;EACA,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAP,EAAAA,MAAA,CAKAQ,IAAI,GAAJ,SAAAA,IAAAA,CAAK1P,OAAO,EAAE;EACV,IAAA,IAAI,IAAI,CAACqP,UAAU,KAAK,MAAM,EAAE;EAC5B,MAAA,IAAI,CAACM,KAAK,CAAC3P,OAAO,CAAC,CAAA;EACvB,KAAC,MACI;EACD;QACA8I,OAAK,CAAC,2CAA2C,CAAC,CAAA;EACtD,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAoG,EAAAA,MAAA,CAKAU,MAAM,GAAN,SAAAA,SAAS;MACL,IAAI,CAACP,UAAU,GAAG,MAAM,CAAA;MACxB,IAAI,CAACP,QAAQ,GAAG,IAAI,CAAA;EACpBF,IAAAA,QAAA,CAAAzS,SAAA,CAAM8H,YAAY,CAAA5H,IAAA,OAAC,MAAM,CAAA,CAAA;EAC7B,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAA6S,EAAAA,MAAA,CAMAW,MAAM,GAAN,SAAAA,MAAAA,CAAO7T,IAAI,EAAE;MACT,IAAM6B,MAAM,GAAGsB,YAAY,CAACnD,IAAI,EAAE,IAAI,CAACgT,MAAM,CAAC3P,UAAU,CAAC,CAAA;EACzD,IAAA,IAAI,CAACyQ,QAAQ,CAACjS,MAAM,CAAC,CAAA;EACzB,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAqR,EAAAA,MAAA,CAKAY,QAAQ,GAAR,SAAAA,QAAAA,CAASjS,MAAM,EAAE;MACb+Q,QAAA,CAAAzS,SAAA,CAAM8H,YAAY,CAAA5H,IAAA,CAAA,IAAA,EAAC,QAAQ,EAAEwB,MAAM,CAAA,CAAA;EACvC,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAqR,EAAAA,MAAA,CAKAO,OAAO,GAAP,SAAAA,OAAAA,CAAQM,OAAO,EAAE;MACb,IAAI,CAACV,UAAU,GAAG,QAAQ,CAAA;MAC1BT,QAAA,CAAAzS,SAAA,CAAM8H,YAAY,CAAA5H,IAAA,CAAA,IAAA,EAAC,OAAO,EAAE0T,OAAO,CAAA,CAAA;EACvC,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;IAAAb,MAAA,CAKAc,KAAK,GAAL,SAAAA,MAAMC,OAAO,EAAE,EAAG,CAAA;EAAAf,EAAAA,MAAA,CAClBgB,SAAS,GAAT,SAAAA,SAAAA,CAAUC,MAAM,EAAc;EAAA,IAAA,IAAZpB,KAAK,GAAAvL,SAAA,CAAAlF,MAAA,GAAA,CAAA,IAAAkF,SAAA,CAAA,CAAA,CAAA,KAAA4E,SAAA,GAAA5E,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;MACxB,OAAQ2M,MAAM,GACV,KAAK,GACL,IAAI,CAACC,SAAS,EAAE,GAChB,IAAI,CAACC,KAAK,EAAE,GACZ,IAAI,CAAC1K,IAAI,CAAC2K,IAAI,GACd,IAAI,CAACC,MAAM,CAACxB,KAAK,CAAC,CAAA;KACzB,CAAA;EAAAG,EAAAA,MAAA,CACDkB,SAAS,GAAT,SAAAA,YAAY;EACR,IAAA,IAAMI,QAAQ,GAAG,IAAI,CAAC7K,IAAI,CAAC6K,QAAQ,CAAA;EACnC,IAAA,OAAOA,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAGD,QAAQ,GAAG,GAAG,GAAGA,QAAQ,GAAG,GAAG,CAAA;KACxE,CAAA;EAAAtB,EAAAA,MAAA,CACDmB,KAAK,GAAL,SAAAA,QAAQ;EACJ,IAAA,IAAI,IAAI,CAAC1K,IAAI,CAAC+K,IAAI,KACZ,IAAI,CAAC/K,IAAI,CAACgL,MAAM,IAAIzG,MAAM,CAAC,IAAI,CAACvE,IAAI,CAAC+K,IAAI,KAAK,GAAG,CAAC,IAC/C,CAAC,IAAI,CAAC/K,IAAI,CAACgL,MAAM,IAAIzG,MAAM,CAAC,IAAI,CAACvE,IAAI,CAAC+K,IAAI,CAAC,KAAK,EAAG,CAAC,EAAE;EAC3D,MAAA,OAAO,GAAG,GAAG,IAAI,CAAC/K,IAAI,CAAC+K,IAAI,CAAA;EAC/B,KAAC,MACI;EACD,MAAA,OAAO,EAAE,CAAA;EACb,KAAA;KACH,CAAA;EAAAxB,EAAAA,MAAA,CACDqB,MAAM,GAAN,SAAAA,MAAAA,CAAOxB,KAAK,EAAE;EACV,IAAA,IAAM6B,YAAY,GAAG1S,MAAM,CAAC6Q,KAAK,CAAC,CAAA;MAClC,OAAO6B,YAAY,CAACtS,MAAM,GAAG,GAAG,GAAGsS,YAAY,GAAG,EAAE,CAAA;KACvD,CAAA;EAAA,EAAA,OAAAjC,SAAA,CAAA;EAAA,CAAA,CAjI0B7L,OAAO,CAAA;;ECVtC,IAAMgG,OAAK,GAAGoF,WAAW,CAAC,0BAA0B,CAAC,CAAC;EACzC2C,IAAAA,OAAO,0BAAAC,UAAA,EAAA;EAChB,EAAA,SAAAD,UAAc;EAAA,IAAA,IAAArC,KAAA,CAAA;EACVA,IAAAA,KAAA,GAAAsC,UAAA,CAAAvN,KAAA,CAAA,IAAA,EAASC,SAAS,CAAC,IAAA,IAAA,CAAA;MACnBgL,KAAA,CAAKuC,QAAQ,GAAG,KAAK,CAAA;EAAC,IAAA,OAAAvC,KAAA,CAAA;EAC1B,GAAA;IAACC,cAAA,CAAAoC,OAAA,EAAAC,UAAA,CAAA,CAAA;EAAA,EAAA,IAAA5B,MAAA,GAAA2B,OAAA,CAAA1U,SAAA,CAAA;EAID;EACJ;EACA;EACA;EACA;EACA;EALI+S,EAAAA,MAAA,CAMAI,MAAM,GAAN,SAAAA,SAAS;MACL,IAAI,CAAC0B,KAAK,EAAE,CAAA;EAChB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAA9B,EAAAA,MAAA,CAMAc,KAAK,GAAL,SAAAA,KAAAA,CAAMC,OAAO,EAAE;EAAA,IAAA,IAAApB,MAAA,GAAA,IAAA,CAAA;MACX,IAAI,CAACQ,UAAU,GAAG,SAAS,CAAA;EAC3B,IAAA,IAAMW,KAAK,GAAG,SAARA,KAAKA,GAAS;QAChBlH,OAAK,CAAC,QAAQ,CAAC,CAAA;QACf+F,MAAI,CAACQ,UAAU,GAAG,QAAQ,CAAA;EAC1BY,MAAAA,OAAO,EAAE,CAAA;OACZ,CAAA;MACD,IAAI,IAAI,CAACc,QAAQ,IAAI,CAAC,IAAI,CAACjC,QAAQ,EAAE;QACjC,IAAImC,KAAK,GAAG,CAAC,CAAA;QACb,IAAI,IAAI,CAACF,QAAQ,EAAE;UACfjI,OAAK,CAAC,6CAA6C,CAAC,CAAA;EACpDmI,QAAAA,KAAK,EAAE,CAAA;EACP,QAAA,IAAI,CAAC5N,IAAI,CAAC,cAAc,EAAE,YAAY;YAClCyF,OAAK,CAAC,4BAA4B,CAAC,CAAA;EACnC,UAAA,EAAEmI,KAAK,IAAIjB,KAAK,EAAE,CAAA;EACtB,SAAC,CAAC,CAAA;EACN,OAAA;EACA,MAAA,IAAI,CAAC,IAAI,CAAClB,QAAQ,EAAE;UAChBhG,OAAK,CAAC,6CAA6C,CAAC,CAAA;EACpDmI,QAAAA,KAAK,EAAE,CAAA;EACP,QAAA,IAAI,CAAC5N,IAAI,CAAC,OAAO,EAAE,YAAY;YAC3ByF,OAAK,CAAC,4BAA4B,CAAC,CAAA;EACnC,UAAA,EAAEmI,KAAK,IAAIjB,KAAK,EAAE,CAAA;EACtB,SAAC,CAAC,CAAA;EACN,OAAA;EACJ,KAAC,MACI;EACDA,MAAAA,KAAK,EAAE,CAAA;EACX,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAd,EAAAA,MAAA,CAKA8B,KAAK,GAAL,SAAAA,QAAQ;MACJlI,OAAK,CAAC,SAAS,CAAC,CAAA;MAChB,IAAI,CAACiI,QAAQ,GAAG,IAAI,CAAA;MACpB,IAAI,CAACG,MAAM,EAAE,CAAA;EACb,IAAA,IAAI,CAACjN,YAAY,CAAC,MAAM,CAAC,CAAA;EAC7B,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAiL,EAAAA,MAAA,CAKAW,MAAM,GAAN,SAAAA,MAAAA,CAAO7T,IAAI,EAAE;EAAA,IAAA,IAAAmV,MAAA,GAAA,IAAA,CAAA;EACTrI,IAAAA,OAAK,CAAC,qBAAqB,EAAE9M,IAAI,CAAC,CAAA;EAClC,IAAA,IAAMc,QAAQ,GAAG,SAAXA,QAAQA,CAAIe,MAAM,EAAK;EACzB;QACA,IAAI,SAAS,KAAKsT,MAAI,CAAC9B,UAAU,IAAIxR,MAAM,CAAC9B,IAAI,KAAK,MAAM,EAAE;UACzDoV,MAAI,CAACvB,MAAM,EAAE,CAAA;EACjB,OAAA;EACA;EACA,MAAA,IAAI,OAAO,KAAK/R,MAAM,CAAC9B,IAAI,EAAE;UACzBoV,MAAI,CAAC1B,OAAO,CAAC;EAAEnB,UAAAA,WAAW,EAAE,gCAAA;EAAiC,SAAC,CAAC,CAAA;EAC/D,QAAA,OAAO,KAAK,CAAA;EAChB,OAAA;EACA;EACA6C,MAAAA,MAAI,CAACrB,QAAQ,CAACjS,MAAM,CAAC,CAAA;OACxB,CAAA;EACD;EACAwC,IAAAA,aAAa,CAACrE,IAAI,EAAE,IAAI,CAACgT,MAAM,CAAC3P,UAAU,CAAC,CAACzD,OAAO,CAACkB,QAAQ,CAAC,CAAA;EAC7D;EACA,IAAA,IAAI,QAAQ,KAAK,IAAI,CAACuS,UAAU,EAAE;EAC9B;QACA,IAAI,CAAC0B,QAAQ,GAAG,KAAK,CAAA;EACrB,MAAA,IAAI,CAAC9M,YAAY,CAAC,cAAc,CAAC,CAAA;EACjC,MAAA,IAAI,MAAM,KAAK,IAAI,CAACoL,UAAU,EAAE;UAC5B,IAAI,CAAC2B,KAAK,EAAE,CAAA;EAChB,OAAC,MACI;EACDlI,QAAAA,OAAK,CAAC,sCAAsC,EAAE,IAAI,CAACuG,UAAU,CAAC,CAAA;EAClE,OAAA;EACJ,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAH,EAAAA,MAAA,CAKAM,OAAO,GAAP,SAAAA,UAAU;EAAA,IAAA,IAAA4B,MAAA,GAAA,IAAA,CAAA;EACN,IAAA,IAAM7B,KAAK,GAAG,SAARA,KAAKA,GAAS;QAChBzG,OAAK,CAAC,sBAAsB,CAAC,CAAA;QAC7BsI,MAAI,CAACzB,KAAK,CAAC,CAAC;EAAE5T,QAAAA,IAAI,EAAE,OAAA;EAAQ,OAAC,CAAC,CAAC,CAAA;OAClC,CAAA;EACD,IAAA,IAAI,MAAM,KAAK,IAAI,CAACsT,UAAU,EAAE;QAC5BvG,OAAK,CAAC,0BAA0B,CAAC,CAAA;EACjCyG,MAAAA,KAAK,EAAE,CAAA;EACX,KAAC,MACI;EACD;EACA;QACAzG,OAAK,CAAC,sCAAsC,CAAC,CAAA;EAC7C,MAAA,IAAI,CAACzF,IAAI,CAAC,MAAM,EAAEkM,KAAK,CAAC,CAAA;EAC5B,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAAL,EAAAA,MAAA,CAMAS,KAAK,GAAL,SAAAA,KAAAA,CAAM3P,OAAO,EAAE;EAAA,IAAA,IAAAqR,MAAA,GAAA,IAAA,CAAA;MACX,IAAI,CAACvC,QAAQ,GAAG,KAAK,CAAA;EACrB/O,IAAAA,aAAa,CAACC,OAAO,EAAE,UAAChE,IAAI,EAAK;EAC7BqV,MAAAA,MAAI,CAACC,OAAO,CAACtV,IAAI,EAAE,YAAM;UACrBqV,MAAI,CAACvC,QAAQ,GAAG,IAAI,CAAA;EACpBuC,QAAAA,MAAI,CAACpN,YAAY,CAAC,OAAO,CAAC,CAAA;EAC9B,OAAC,CAAC,CAAA;EACN,KAAC,CAAC,CAAA;EACN,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAiL,EAAAA,MAAA,CAKAqC,GAAG,GAAH,SAAAA,MAAM;MACF,IAAMpB,MAAM,GAAG,IAAI,CAACxK,IAAI,CAACgL,MAAM,GAAG,OAAO,GAAG,MAAM,CAAA;EAClD,IAAA,IAAM5B,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,EAAE,CAAA;EAC9B;EACA,IAAA,IAAI,KAAK,KAAK,IAAI,CAACpJ,IAAI,CAAC6L,iBAAiB,EAAE;QACvCzC,KAAK,CAAC,IAAI,CAACpJ,IAAI,CAAC8L,cAAc,CAAC,GAAGpL,YAAY,EAAE,CAAA;EACpD,KAAA;MACA,IAAI,CAAC,IAAI,CAACxJ,cAAc,IAAI,CAACkS,KAAK,CAAC2C,GAAG,EAAE;QACpC3C,KAAK,CAAC4C,GAAG,GAAG,CAAC,CAAA;EACjB,KAAA;EACA,IAAA,OAAO,IAAI,CAACzB,SAAS,CAACC,MAAM,EAAEpB,KAAK,CAAC,CAAA;KACvC,CAAA;IAAA,OAAA6C,YAAA,CAAAf,OAAA,EAAA,CAAA;MAAAhV,GAAA,EAAA,MAAA;MAAAsP,GAAA,EAlJD,SAAAA,GAAAA,GAAW;EACP,MAAA,OAAO,SAAS,CAAA;EACpB,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,CAAA,CAPwBwD,SAAS,CAAA;;ECLtC;EACA,IAAIkD,KAAK,GAAG,KAAK,CAAA;EACjB,IAAI;IACAA,KAAK,GAAG,OAAOC,cAAc,KAAK,WAAW,IACzC,iBAAiB,IAAI,IAAIA,cAAc,EAAE,CAAA;EACjD,CAAC,CACD,OAAOC,GAAG,EAAE;EACR;EACA;EAAA,CAAA;EAEG,IAAMC,OAAO,GAAGH,KAAK;;ECJ5B,IAAM/I,OAAK,GAAGoF,WAAW,CAAC,0BAA0B,CAAC,CAAC;EACtD,SAAS+D,KAAKA,GAAG,EAAE;EACNC,IAAAA,OAAO,0BAAAC,QAAA,EAAA;EAChB;EACJ;EACA;EACA;EACA;EACA;IACI,SAAAD,OAAAA,CAAYvM,IAAI,EAAE;EAAA,IAAA,IAAA6I,KAAA,CAAA;EACdA,IAAAA,KAAA,GAAA2D,QAAA,CAAA9V,IAAA,CAAA,IAAA,EAAMsJ,IAAI,CAAC,IAAA,IAAA,CAAA;EACX,IAAA,IAAI,OAAOyM,QAAQ,KAAK,WAAW,EAAE;EACjC,MAAA,IAAMC,KAAK,GAAG,QAAQ,KAAKD,QAAQ,CAACvP,QAAQ,CAAA;EAC5C,MAAA,IAAI6N,IAAI,GAAG0B,QAAQ,CAAC1B,IAAI,CAAA;EACxB;QACA,IAAI,CAACA,IAAI,EAAE;EACPA,QAAAA,IAAI,GAAG2B,KAAK,GAAG,KAAK,GAAG,IAAI,CAAA;EAC/B,OAAA;QACA7D,KAAA,CAAK8D,EAAE,GACF,OAAOF,QAAQ,KAAK,WAAW,IAC5BzM,IAAI,CAAC6K,QAAQ,KAAK4B,QAAQ,CAAC5B,QAAQ,IACnCE,IAAI,KAAK/K,IAAI,CAAC+K,IAAI,CAAA;EAC9B,KAAA;EAAC,IAAA,OAAAlC,KAAA,CAAA;EACL,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;IANIC,cAAA,CAAAyD,OAAA,EAAAC,QAAA,CAAA,CAAA;EAAA,EAAA,IAAAjD,MAAA,GAAAgD,OAAA,CAAA/V,SAAA,CAAA;IAAA+S,MAAA,CAOAoC,OAAO,GAAP,SAAAA,QAAQtV,IAAI,EAAEmH,EAAE,EAAE;EAAA,IAAA,IAAA0L,MAAA,GAAA,IAAA,CAAA;EACd,IAAA,IAAM0D,GAAG,GAAG,IAAI,CAACC,OAAO,CAAC;EACrBC,MAAAA,MAAM,EAAE,MAAM;EACdzW,MAAAA,IAAI,EAAEA,IAAAA;EACV,KAAC,CAAC,CAAA;EACFuW,IAAAA,GAAG,CAACvP,EAAE,CAAC,SAAS,EAAEG,EAAE,CAAC,CAAA;MACrBoP,GAAG,CAACvP,EAAE,CAAC,OAAO,EAAE,UAAC0P,SAAS,EAAEnE,OAAO,EAAK;QACpCM,MAAI,CAACM,OAAO,CAAC,gBAAgB,EAAEuD,SAAS,EAAEnE,OAAO,CAAC,CAAA;EACtD,KAAC,CAAC,CAAA;EACN,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAW,EAAAA,MAAA,CAKAgC,MAAM,GAAN,SAAAA,SAAS;EAAA,IAAA,IAAAC,MAAA,GAAA,IAAA,CAAA;MACLrI,OAAK,CAAC,UAAU,CAAC,CAAA;EACjB,IAAA,IAAMyJ,GAAG,GAAG,IAAI,CAACC,OAAO,EAAE,CAAA;EAC1BD,IAAAA,GAAG,CAACvP,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC6M,MAAM,CAAChK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;MACtC0M,GAAG,CAACvP,EAAE,CAAC,OAAO,EAAE,UAAC0P,SAAS,EAAEnE,OAAO,EAAK;QACpC4C,MAAI,CAAChC,OAAO,CAAC,gBAAgB,EAAEuD,SAAS,EAAEnE,OAAO,CAAC,CAAA;EACtD,KAAC,CAAC,CAAA;MACF,IAAI,CAACoE,OAAO,GAAGJ,GAAG,CAAA;KACrB,CAAA;EAAA,EAAA,OAAAL,OAAA,CAAA;EAAA,CAAA,CApDwBrB,OAAO,CAAA,CAAA;EAsDvB+B,IAAAA,OAAO,0BAAAhE,QAAA,EAAA;EAChB;EACJ;EACA;EACA;EACA;EACA;EACI,EAAA,SAAAgE,QAAYC,aAAa,EAAEtB,GAAG,EAAE5L,IAAI,EAAE;EAAA,IAAA,IAAAyL,MAAA,CAAA;EAClCA,IAAAA,MAAA,GAAAxC,QAAA,CAAAvS,IAAA,KAAM,CAAC,IAAA,IAAA,CAAA;MACP+U,MAAA,CAAKyB,aAAa,GAAGA,aAAa,CAAA;EAClCnN,IAAAA,qBAAqB,CAAA0L,MAAA,EAAOzL,IAAI,CAAC,CAAA;MACjCyL,MAAA,CAAK0B,KAAK,GAAGnN,IAAI,CAAA;EACjByL,IAAAA,MAAA,CAAK2B,OAAO,GAAGpN,IAAI,CAAC8M,MAAM,IAAI,KAAK,CAAA;MACnCrB,MAAA,CAAK4B,IAAI,GAAGzB,GAAG,CAAA;EACfH,IAAAA,MAAA,CAAK6B,KAAK,GAAG7K,SAAS,KAAKzC,IAAI,CAAC3J,IAAI,GAAG2J,IAAI,CAAC3J,IAAI,GAAG,IAAI,CAAA;MACvDoV,MAAA,CAAK8B,OAAO,EAAE,CAAA;EAAC,IAAA,OAAA9B,MAAA,CAAA;EACnB,GAAA;EACA;EACJ;EACA;EACA;EACA;IAJI3C,cAAA,CAAAmE,OAAA,EAAAhE,QAAA,CAAA,CAAA;EAAA,EAAA,IAAAuE,OAAA,GAAAP,OAAA,CAAAzW,SAAA,CAAA;EAAAgX,EAAAA,OAAA,CAKAD,OAAO,GAAP,SAAAA,UAAU;EAAA,IAAA,IAAA7B,MAAA,GAAA,IAAA,CAAA;EACN,IAAA,IAAI+B,EAAE,CAAA;MACN,IAAMzN,IAAI,GAAGZ,IAAI,CAAC,IAAI,CAAC+N,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,oBAAoB,EAAE,WAAW,CAAC,CAAA;MAC9HnN,IAAI,CAAC0N,OAAO,GAAG,CAAC,CAAC,IAAI,CAACP,KAAK,CAACR,EAAE,CAAA;MAC9B,IAAMgB,GAAG,GAAI,IAAI,CAACC,IAAI,GAAG,IAAI,CAACV,aAAa,CAAClN,IAAI,CAAE,CAAA;MAClD,IAAI;QACAmD,OAAK,CAAC,iBAAiB,EAAE,IAAI,CAACiK,OAAO,EAAE,IAAI,CAACC,IAAI,CAAC,CAAA;EACjDM,MAAAA,GAAG,CAAClE,IAAI,CAAC,IAAI,CAAC2D,OAAO,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAAC,CAAA;QACvC,IAAI;EACA,QAAA,IAAI,IAAI,CAACF,KAAK,CAACU,YAAY,EAAE;EACzB;YACAF,GAAG,CAACG,qBAAqB,IAAIH,GAAG,CAACG,qBAAqB,CAAC,IAAI,CAAC,CAAA;YAC5D,KAAK,IAAIpV,CAAC,IAAI,IAAI,CAACyU,KAAK,CAACU,YAAY,EAAE;cACnC,IAAI,IAAI,CAACV,KAAK,CAACU,YAAY,CAACpO,cAAc,CAAC/G,CAAC,CAAC,EAAE;EAC3CiV,cAAAA,GAAG,CAACI,gBAAgB,CAACrV,CAAC,EAAE,IAAI,CAACyU,KAAK,CAACU,YAAY,CAACnV,CAAC,CAAC,CAAC,CAAA;EACvD,aAAA;EACJ,WAAA;EACJ,SAAA;EACJ,OAAC,CACD,OAAOsV,CAAC,EAAE,EAAE;EACZ,MAAA,IAAI,MAAM,KAAK,IAAI,CAACZ,OAAO,EAAE;UACzB,IAAI;EACAO,UAAAA,GAAG,CAACI,gBAAgB,CAAC,cAAc,EAAE,0BAA0B,CAAC,CAAA;EACpE,SAAC,CACD,OAAOC,CAAC,EAAE,EAAE;EAChB,OAAA;QACA,IAAI;EACAL,QAAAA,GAAG,CAACI,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;EACzC,OAAC,CACD,OAAOC,CAAC,EAAE,EAAE;QACZ,CAACP,EAAE,GAAG,IAAI,CAACN,KAAK,CAACc,SAAS,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,UAAU,CAACP,GAAG,CAAC,CAAA;EACnF;QACA,IAAI,iBAAiB,IAAIA,GAAG,EAAE;EAC1BA,QAAAA,GAAG,CAACQ,eAAe,GAAG,IAAI,CAAChB,KAAK,CAACgB,eAAe,CAAA;EACpD,OAAA;EACA,MAAA,IAAI,IAAI,CAAChB,KAAK,CAACiB,cAAc,EAAE;EAC3BT,QAAAA,GAAG,CAACU,OAAO,GAAG,IAAI,CAAClB,KAAK,CAACiB,cAAc,CAAA;EAC3C,OAAA;QACAT,GAAG,CAACW,kBAAkB,GAAG,YAAM;EAC3B,QAAA,IAAIb,EAAE,CAAA;EACN,QAAA,IAAIE,GAAG,CAACjE,UAAU,KAAK,CAAC,EAAE;YACtB,CAAC+D,EAAE,GAAG/B,MAAI,CAACyB,KAAK,CAACc,SAAS,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,YAAY;EAChF;EACAZ,UAAAA,GAAG,CAACa,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAA;EACxC,SAAA;EACA,QAAA,IAAI,CAAC,KAAKb,GAAG,CAACjE,UAAU,EACpB,OAAA;UACJ,IAAI,GAAG,KAAKiE,GAAG,CAACc,MAAM,IAAI,IAAI,KAAKd,GAAG,CAACc,MAAM,EAAE;YAC3C/C,MAAI,CAACgD,OAAO,EAAE,CAAA;EAClB,SAAC,MACI;EACD;EACA;YACAhD,MAAI,CAAC7M,YAAY,CAAC,YAAM;EACpB6M,YAAAA,MAAI,CAACiD,QAAQ,CAAC,OAAOhB,GAAG,CAACc,MAAM,KAAK,QAAQ,GAAGd,GAAG,CAACc,MAAM,GAAG,CAAC,CAAC,CAAA;aACjE,EAAE,CAAC,CAAC,CAAA;EACT,SAAA;SACH,CAAA;EACDtL,MAAAA,OAAK,CAAC,aAAa,EAAE,IAAI,CAACmK,KAAK,CAAC,CAAA;EAChCK,MAAAA,GAAG,CAAC5D,IAAI,CAAC,IAAI,CAACuD,KAAK,CAAC,CAAA;OACvB,CACD,OAAOU,CAAC,EAAE;EACN;EACA;EACA;QACA,IAAI,CAACnP,YAAY,CAAC,YAAM;EACpB6M,QAAAA,MAAI,CAACiD,QAAQ,CAACX,CAAC,CAAC,CAAA;SACnB,EAAE,CAAC,CAAC,CAAA;EACL,MAAA,OAAA;EACJ,KAAA;EACA,IAAA,IAAI,OAAO3G,QAAQ,KAAK,WAAW,EAAE;EACjC,MAAA,IAAI,CAACuH,MAAM,GAAG3B,OAAO,CAAC4B,aAAa,EAAE,CAAA;QACrC5B,OAAO,CAAC6B,QAAQ,CAAC,IAAI,CAACF,MAAM,CAAC,GAAG,IAAI,CAAA;EACxC,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAApB,EAAAA,OAAA,CAKAmB,QAAQ,GAAR,SAAAA,QAAAA,CAASvC,GAAG,EAAE;MACV,IAAI,CAAC9N,YAAY,CAAC,OAAO,EAAE8N,GAAG,EAAE,IAAI,CAACwB,IAAI,CAAC,CAAA;EAC1C,IAAA,IAAI,CAACmB,QAAQ,CAAC,IAAI,CAAC,CAAA;EACvB,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAvB,EAAAA,OAAA,CAKAuB,QAAQ,GAAR,SAAAA,QAAAA,CAASC,SAAS,EAAE;EAChB,IAAA,IAAI,WAAW,KAAK,OAAO,IAAI,CAACpB,IAAI,IAAI,IAAI,KAAK,IAAI,CAACA,IAAI,EAAE;EACxD,MAAA,OAAA;EACJ,KAAA;EACA,IAAA,IAAI,CAACA,IAAI,CAACU,kBAAkB,GAAGhC,KAAK,CAAA;EACpC,IAAA,IAAI0C,SAAS,EAAE;QACX,IAAI;EACA,QAAA,IAAI,CAACpB,IAAI,CAACqB,KAAK,EAAE,CAAA;EACrB,OAAC,CACD,OAAOjB,CAAC,EAAE,EAAE;EAChB,KAAA;EACA,IAAA,IAAI,OAAO3G,QAAQ,KAAK,WAAW,EAAE;EACjC,MAAA,OAAO4F,OAAO,CAAC6B,QAAQ,CAAC,IAAI,CAACF,MAAM,CAAC,CAAA;EACxC,KAAA;MACA,IAAI,CAAChB,IAAI,GAAG,IAAI,CAAA;EACpB,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAJ,EAAAA,OAAA,CAKAkB,OAAO,GAAP,SAAAA,UAAU;EACN,IAAA,IAAMrY,IAAI,GAAG,IAAI,CAACuX,IAAI,CAACsB,YAAY,CAAA;MACnC,IAAI7Y,IAAI,KAAK,IAAI,EAAE;EACf,MAAA,IAAI,CAACiI,YAAY,CAAC,MAAM,EAAEjI,IAAI,CAAC,CAAA;EAC/B,MAAA,IAAI,CAACiI,YAAY,CAAC,SAAS,CAAC,CAAA;QAC5B,IAAI,CAACyQ,QAAQ,EAAE,CAAA;EACnB,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAvB,EAAAA,OAAA,CAKAyB,KAAK,GAAL,SAAAA,QAAQ;MACJ,IAAI,CAACF,QAAQ,EAAE,CAAA;KAClB,CAAA;EAAA,EAAA,OAAA9B,OAAA,CAAA;EAAA,CAAA,CAnJwB9P,OAAO,CAAA,CAAA;EAqJpC8P,OAAO,CAAC4B,aAAa,GAAG,CAAC,CAAA;EACzB5B,OAAO,CAAC6B,QAAQ,GAAG,EAAE,CAAA;EACrB;EACA;EACA;EACA;EACA;EACA,IAAI,OAAOzH,QAAQ,KAAK,WAAW,EAAE;EACjC;EACA,EAAA,IAAI,OAAO8H,WAAW,KAAK,UAAU,EAAE;EACnC;EACAA,IAAAA,WAAW,CAAC,UAAU,EAAEC,aAAa,CAAC,CAAA;EAC1C,GAAC,MACI,IAAI,OAAO9R,gBAAgB,KAAK,UAAU,EAAE;MAC7C,IAAM+R,gBAAgB,GAAG,YAAY,IAAI1P,cAAU,GAAG,UAAU,GAAG,QAAQ,CAAA;EAC3ErC,IAAAA,gBAAgB,CAAC+R,gBAAgB,EAAED,aAAa,EAAE,KAAK,CAAC,CAAA;EAC5D,GAAA;EACJ,CAAA;EACA,SAASA,aAAaA,GAAG;EACrB,EAAA,KAAK,IAAI1W,CAAC,IAAIuU,OAAO,CAAC6B,QAAQ,EAAE;MAC5B,IAAI7B,OAAO,CAAC6B,QAAQ,CAACrP,cAAc,CAAC/G,CAAC,CAAC,EAAE;QACpCuU,OAAO,CAAC6B,QAAQ,CAACpW,CAAC,CAAC,CAACuW,KAAK,EAAE,CAAA;EAC/B,KAAA;EACJ,GAAA;EACJ,CAAA;EACA,IAAMK,OAAO,GAAI,YAAY;IACzB,IAAM3B,GAAG,GAAG4B,UAAU,CAAC;EACnB7B,IAAAA,OAAO,EAAE,KAAA;EACb,GAAC,CAAC,CAAA;EACF,EAAA,OAAOC,GAAG,IAAIA,GAAG,CAAC6B,YAAY,KAAK,IAAI,CAAA;EAC3C,CAAC,EAAG,CAAA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACaC,IAAAA,GAAG,0BAAAC,QAAA,EAAA;IACZ,SAAAD,GAAAA,CAAYzP,IAAI,EAAE;EAAA,IAAA,IAAA2P,MAAA,CAAA;EACdA,IAAAA,MAAA,GAAAD,QAAA,CAAAhZ,IAAA,CAAA,IAAA,EAAMsJ,IAAI,CAAC,IAAA,IAAA,CAAA;EACX,IAAA,IAAMsJ,WAAW,GAAGtJ,IAAI,IAAIA,IAAI,CAACsJ,WAAW,CAAA;EAC5CqG,IAAAA,MAAA,CAAKzY,cAAc,GAAGoY,OAAO,IAAI,CAAChG,WAAW,CAAA;EAAC,IAAA,OAAAqG,MAAA,CAAA;EAClD,GAAA;IAAC7G,cAAA,CAAA2G,GAAA,EAAAC,QAAA,CAAA,CAAA;EAAA,EAAA,IAAAE,OAAA,GAAAH,GAAA,CAAAjZ,SAAA,CAAA;EAAAoZ,EAAAA,OAAA,CACD/C,OAAO,GAAP,SAAAA,UAAmB;EAAA,IAAA,IAAX7M,IAAI,GAAAnC,SAAA,CAAAlF,MAAA,GAAA,CAAA,IAAAkF,SAAA,CAAA,CAAA,CAAA,KAAA4E,SAAA,GAAA5E,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;MACbgS,QAAA,CAAc7P,IAAI,EAAE;QAAE2M,EAAE,EAAE,IAAI,CAACA,EAAAA;EAAG,KAAC,EAAE,IAAI,CAAC3M,IAAI,CAAC,CAAA;EAC/C,IAAA,OAAO,IAAIiN,OAAO,CAACsC,UAAU,EAAE,IAAI,CAAC3D,GAAG,EAAE,EAAE5L,IAAI,CAAC,CAAA;KACnD,CAAA;EAAA,EAAA,OAAAyP,GAAA,CAAA;EAAA,CAAA,CAToBlD,OAAO,CAAA,CAAA;EAWhC,SAASgD,UAAUA,CAACvP,IAAI,EAAE;EACtB,EAAA,IAAM0N,OAAO,GAAG1N,IAAI,CAAC0N,OAAO,CAAA;EAC5B;IACA,IAAI;MACA,IAAI,WAAW,KAAK,OAAOvB,cAAc,KAAK,CAACuB,OAAO,IAAIrB,OAAO,CAAC,EAAE;QAChE,OAAO,IAAIF,cAAc,EAAE,CAAA;EAC/B,KAAA;EACJ,GAAC,CACD,OAAO6B,CAAC,EAAE,EAAE;IACZ,IAAI,CAACN,OAAO,EAAE;MACV,IAAI;EACA,MAAA,OAAO,IAAI/N,cAAU,CAAC,CAAC,QAAQ,CAAC,CAACsG,MAAM,CAAC,QAAQ,CAAC,CAACxL,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAA;EACrF,KAAC,CACD,OAAOuT,CAAC,EAAE,EAAE;EAChB,GAAA;EACJ;;EC9QA,IAAM7K,OAAK,GAAGoF,WAAW,CAAC,4BAA4B,CAAC,CAAC;EACxD;EACA,IAAMuH,aAAa,GAAG,OAAO3I,SAAS,KAAK,WAAW,IAClD,OAAOA,SAAS,CAAC4I,OAAO,KAAK,QAAQ,IACrC5I,SAAS,CAAC4I,OAAO,CAACvN,WAAW,EAAE,KAAK,aAAa,CAAA;EACxCwN,IAAAA,MAAM,0BAAA7E,UAAA,EAAA;EAAA,EAAA,SAAA6E,MAAA,GAAA;EAAA,IAAA,OAAA7E,UAAA,CAAAvN,KAAA,CAAA,IAAA,EAAAC,SAAA,CAAA,IAAA,IAAA,CAAA;EAAA,GAAA;IAAAiL,cAAA,CAAAkH,MAAA,EAAA7E,UAAA,CAAA,CAAA;EAAA,EAAA,IAAA5B,MAAA,GAAAyG,MAAA,CAAAxZ,SAAA,CAAA;EAAA+S,EAAAA,MAAA,CAIfI,MAAM,GAAN,SAAAA,SAAS;EACL,IAAA,IAAMiC,GAAG,GAAG,IAAI,CAACA,GAAG,EAAE,CAAA;EACtB,IAAA,IAAMqE,SAAS,GAAG,IAAI,CAACjQ,IAAI,CAACiQ,SAAS,CAAA;EACrC;EACA,IAAA,IAAMjQ,IAAI,GAAG8P,aAAa,GACpB,EAAE,GACF1Q,IAAI,CAAC,IAAI,CAACY,IAAI,EAAE,OAAO,EAAE,mBAAmB,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,oBAAoB,EAAE,cAAc,EAAE,iBAAiB,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAAA;EAC1N,IAAA,IAAI,IAAI,CAACA,IAAI,CAAC6N,YAAY,EAAE;EACxB7N,MAAAA,IAAI,CAACkQ,OAAO,GAAG,IAAI,CAAClQ,IAAI,CAAC6N,YAAY,CAAA;EACzC,KAAA;MACA,IAAI;EACA,MAAA,IAAI,CAACsC,EAAE,GAAG,IAAI,CAACC,YAAY,CAACxE,GAAG,EAAEqE,SAAS,EAAEjQ,IAAI,CAAC,CAAA;OACpD,CACD,OAAOoM,GAAG,EAAE;EACR,MAAA,OAAO,IAAI,CAAC9N,YAAY,CAAC,OAAO,EAAE8N,GAAG,CAAC,CAAA;EAC1C,KAAA;MACA,IAAI,CAAC+D,EAAE,CAACzW,UAAU,GAAG,IAAI,CAAC2P,MAAM,CAAC3P,UAAU,CAAA;MAC3C,IAAI,CAAC2W,iBAAiB,EAAE,CAAA;EAC5B,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA9G,EAAAA,MAAA,CAKA8G,iBAAiB,GAAjB,SAAAA,oBAAoB;EAAA,IAAA,IAAAxH,KAAA,GAAA,IAAA,CAAA;EAChB,IAAA,IAAI,CAACsH,EAAE,CAACG,MAAM,GAAG,YAAM;EACnB,MAAA,IAAIzH,KAAI,CAAC7I,IAAI,CAACuQ,SAAS,EAAE;EACrB1H,QAAAA,KAAI,CAACsH,EAAE,CAACK,OAAO,CAACC,KAAK,EAAE,CAAA;EAC3B,OAAA;QACA5H,KAAI,CAACoB,MAAM,EAAE,CAAA;OAChB,CAAA;EACD,IAAA,IAAI,CAACkG,EAAE,CAACO,OAAO,GAAG,UAACC,UAAU,EAAA;QAAA,OAAK9H,KAAI,CAACiB,OAAO,CAAC;EAC3CnB,QAAAA,WAAW,EAAE,6BAA6B;EAC1CC,QAAAA,OAAO,EAAE+H,UAAAA;EACb,OAAC,CAAC,CAAA;EAAA,KAAA,CAAA;EACF,IAAA,IAAI,CAACR,EAAE,CAACS,SAAS,GAAG,UAACC,EAAE,EAAA;EAAA,MAAA,OAAKhI,KAAI,CAACqB,MAAM,CAAC2G,EAAE,CAACxa,IAAI,CAAC,CAAA;EAAA,KAAA,CAAA;EAChD,IAAA,IAAI,CAAC8Z,EAAE,CAACW,OAAO,GAAG,UAAC9C,CAAC,EAAA;EAAA,MAAA,OAAKnF,KAAI,CAACW,OAAO,CAAC,iBAAiB,EAAEwE,CAAC,CAAC,CAAA;EAAA,KAAA,CAAA;KAC9D,CAAA;EAAAzE,EAAAA,MAAA,CACDS,KAAK,GAAL,SAAAA,KAAAA,CAAM3P,OAAO,EAAE;EAAA,IAAA,IAAA6O,MAAA,GAAA,IAAA,CAAA;MACX,IAAI,CAACC,QAAQ,GAAG,KAAK,CAAA;EACrB;EACA;MAAA,IAAA4H,KAAA,GAAAA,SAAAA,KAAAA,GACyC;EACrC,MAAA,IAAM7Y,MAAM,GAAGmC,OAAO,CAAC3B,CAAC,CAAC,CAAA;QACzB,IAAMsY,UAAU,GAAGtY,CAAC,KAAK2B,OAAO,CAAC1B,MAAM,GAAG,CAAC,CAAA;QAC3C3B,YAAY,CAACkB,MAAM,EAAEgR,MAAI,CAAChS,cAAc,EAAE,UAACb,IAAI,EAAK;EAChD;EACA;EACA;UACA,IAAI;EACA6S,UAAAA,MAAI,CAACyC,OAAO,CAACzT,MAAM,EAAE7B,IAAI,CAAC,CAAA;WAC7B,CACD,OAAO2X,CAAC,EAAE;YACN7K,OAAK,CAAC,uCAAuC,CAAC,CAAA;EAClD,SAAA;EACA,QAAA,IAAI6N,UAAU,EAAE;EACZ;EACA;EACAvS,UAAAA,QAAQ,CAAC,YAAM;cACXyK,MAAI,CAACC,QAAQ,GAAG,IAAI,CAAA;EACpBD,YAAAA,MAAI,CAAC5K,YAAY,CAAC,OAAO,CAAC,CAAA;EAC9B,WAAC,EAAE4K,MAAI,CAACrK,YAAY,CAAC,CAAA;EACzB,SAAA;EACJ,OAAC,CAAC,CAAA;OACL,CAAA;EAtBD,IAAA,KAAK,IAAInG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,OAAO,CAAC1B,MAAM,EAAED,CAAC,EAAE,EAAA;QAAAqY,KAAA,EAAA,CAAA;EAAA,KAAA;KAuB1C,CAAA;EAAAxH,EAAAA,MAAA,CACDM,OAAO,GAAP,SAAAA,UAAU;EACN,IAAA,IAAI,OAAO,IAAI,CAACsG,EAAE,KAAK,WAAW,EAAE;EAChC,MAAA,IAAI,CAACA,EAAE,CAACW,OAAO,GAAG,YAAM,EAAG,CAAA;EAC3B,MAAA,IAAI,CAACX,EAAE,CAACvG,KAAK,EAAE,CAAA;QACf,IAAI,CAACuG,EAAE,GAAG,IAAI,CAAA;EAClB,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA5G,EAAAA,MAAA,CAKAqC,GAAG,GAAH,SAAAA,MAAM;MACF,IAAMpB,MAAM,GAAG,IAAI,CAACxK,IAAI,CAACgL,MAAM,GAAG,KAAK,GAAG,IAAI,CAAA;EAC9C,IAAA,IAAM5B,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,EAAE,CAAA;EAC9B;EACA,IAAA,IAAI,IAAI,CAACpJ,IAAI,CAAC6L,iBAAiB,EAAE;QAC7BzC,KAAK,CAAC,IAAI,CAACpJ,IAAI,CAAC8L,cAAc,CAAC,GAAGpL,YAAY,EAAE,CAAA;EACpD,KAAA;EACA;EACA,IAAA,IAAI,CAAC,IAAI,CAACxJ,cAAc,EAAE;QACtBkS,KAAK,CAAC4C,GAAG,GAAG,CAAC,CAAA;EACjB,KAAA;EACA,IAAA,OAAO,IAAI,CAACzB,SAAS,CAACC,MAAM,EAAEpB,KAAK,CAAC,CAAA;KACvC,CAAA;IAAA,OAAA6C,YAAA,CAAA+D,MAAA,EAAA,CAAA;MAAA9Z,GAAA,EAAA,MAAA;MAAAsP,GAAA,EA7FD,SAAAA,GAAAA,GAAW;EACP,MAAA,OAAO,WAAW,CAAA;EACtB,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,CAAA,CAHuBwD,SAAS,CAAA,CAAA;EAgGrC,IAAMiI,aAAa,GAAGtR,cAAU,CAACuR,SAAS,IAAIvR,cAAU,CAACwR,YAAY,CAAA;EACrE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACaC,IAAAA,EAAE,0BAAAC,OAAA,EAAA;EAAA,EAAA,SAAAD,EAAA,GAAA;EAAA,IAAA,OAAAC,OAAA,CAAAzT,KAAA,CAAA,IAAA,EAAAC,SAAA,CAAA,IAAA,IAAA,CAAA;EAAA,GAAA;IAAAiL,cAAA,CAAAsI,EAAA,EAAAC,OAAA,CAAA,CAAA;EAAA,EAAA,IAAA7D,OAAA,GAAA4D,EAAA,CAAA5a,SAAA,CAAA;IAAAgX,OAAA,CACX4C,YAAY,GAAZ,SAAAA,YAAAA,CAAaxE,GAAG,EAAEqE,SAAS,EAAEjQ,IAAI,EAAE;MAC/B,OAAO,CAAC8P,aAAa,GACfG,SAAS,GACL,IAAIgB,aAAa,CAACrF,GAAG,EAAEqE,SAAS,CAAC,GACjC,IAAIgB,aAAa,CAACrF,GAAG,CAAC,GAC1B,IAAIqF,aAAa,CAACrF,GAAG,EAAEqE,SAAS,EAAEjQ,IAAI,CAAC,CAAA;KAChD,CAAA;IAAAwN,OAAA,CACD7B,OAAO,GAAP,SAAAA,QAAQ2F,OAAO,EAAEjb,IAAI,EAAE;EACnB,IAAA,IAAI,CAAC8Z,EAAE,CAACpG,IAAI,CAAC1T,IAAI,CAAC,CAAA;KACrB,CAAA;EAAA,EAAA,OAAA+a,EAAA,CAAA;EAAA,CAAA,CAVmBpB,MAAM,CAAA;;EChH9B,IAAM7M,OAAK,GAAGoF,WAAW,CAAC,+BAA+B,CAAC,CAAC;EAC3D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACagJ,IAAAA,EAAE,0BAAApG,UAAA,EAAA;EAAA,EAAA,SAAAoG,EAAA,GAAA;EAAA,IAAA,OAAApG,UAAA,CAAAvN,KAAA,CAAA,IAAA,EAAAC,SAAA,CAAA,IAAA,IAAA,CAAA;EAAA,GAAA;IAAAiL,cAAA,CAAAyI,EAAA,EAAApG,UAAA,CAAA,CAAA;EAAA,EAAA,IAAA5B,MAAA,GAAAgI,EAAA,CAAA/a,SAAA,CAAA;EAAA+S,EAAAA,MAAA,CAIXI,MAAM,GAAN,SAAAA,SAAS;EAAA,IAAA,IAAAd,KAAA,GAAA,IAAA,CAAA;MACL,IAAI;EACA;QACA,IAAI,CAAC2I,UAAU,GAAG,IAAIC,YAAY,CAAC,IAAI,CAAClH,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,CAACvK,IAAI,CAAC0R,gBAAgB,CAAC,IAAI,CAAC5O,IAAI,CAAC,CAAC,CAAA;OACrG,CACD,OAAOsJ,GAAG,EAAE;EACR,MAAA,OAAO,IAAI,CAAC9N,YAAY,CAAC,OAAO,EAAE8N,GAAG,CAAC,CAAA;EAC1C,KAAA;EACA,IAAA,IAAI,CAACoF,UAAU,CAACG,MAAM,CACjBvZ,IAAI,CAAC,YAAM;QACZ+K,OAAK,CAAC,6BAA6B,CAAC,CAAA;QACpC0F,KAAI,CAACiB,OAAO,EAAE,CAAA;EAClB,KAAC,CAAC,CAAA,OAAA,CACQ,CAAC,UAACsC,GAAG,EAAK;EAChBjJ,MAAAA,OAAK,CAAC,4BAA4B,EAAEiJ,GAAG,CAAC,CAAA;EACxCvD,MAAAA,KAAI,CAACW,OAAO,CAAC,oBAAoB,EAAE4C,GAAG,CAAC,CAAA;EAC3C,KAAC,CAAC,CAAA;EACF;EACA,IAAA,IAAI,CAACoF,UAAU,CAACI,KAAK,CAACxZ,IAAI,CAAC,YAAM;QAC7ByQ,KAAI,CAAC2I,UAAU,CAACK,yBAAyB,EAAE,CAACzZ,IAAI,CAAC,UAAC0Z,MAAM,EAAK;EACzD,QAAA,IAAMC,aAAa,GAAGzV,yBAAyB,CAACiI,MAAM,CAACyN,gBAAgB,EAAEnJ,KAAI,CAACQ,MAAM,CAAC3P,UAAU,CAAC,CAAA;EAChG,QAAA,IAAMuY,MAAM,GAAGH,MAAM,CAACI,QAAQ,CAACC,WAAW,CAACJ,aAAa,CAAC,CAACK,SAAS,EAAE,CAAA;EACrE,QAAA,IAAMC,aAAa,GAAGvX,yBAAyB,EAAE,CAAA;UACjDuX,aAAa,CAACH,QAAQ,CAACI,MAAM,CAACR,MAAM,CAAC3I,QAAQ,CAAC,CAAA;UAC9CN,KAAI,CAAC0J,OAAO,GAAGF,aAAa,CAAClJ,QAAQ,CAACqJ,SAAS,EAAE,CAAA;EACjD,QAAA,IAAMC,IAAI,GAAG,SAAPA,IAAIA,GAAS;YACfR,MAAM,CACDQ,IAAI,EAAE,CACNra,IAAI,CAAC,UAAAnB,IAAA,EAAqB;EAAA,YAAA,IAAlByb,IAAI,GAAAzb,IAAA,CAAJyb,IAAI;gBAAExG,KAAK,GAAAjV,IAAA,CAALiV,KAAK,CAAA;EACpB,YAAA,IAAIwG,IAAI,EAAE;gBACNvP,OAAK,CAAC,mBAAmB,CAAC,CAAA;EAC1B,cAAA,OAAA;EACJ,aAAA;EACAA,YAAAA,OAAK,CAAC,oBAAoB,EAAE+I,KAAK,CAAC,CAAA;EAClCrD,YAAAA,KAAI,CAACsB,QAAQ,CAAC+B,KAAK,CAAC,CAAA;EACpBuG,YAAAA,IAAI,EAAE,CAAA;EACV,WAAC,CAAC,CAAA,OAAA,CACQ,CAAC,UAACrG,GAAG,EAAK;EAChBjJ,YAAAA,OAAK,CAAC,qCAAqC,EAAEiJ,GAAG,CAAC,CAAA;EACrD,WAAC,CAAC,CAAA;WACL,CAAA;EACDqG,QAAAA,IAAI,EAAE,CAAA;EACN,QAAA,IAAMva,MAAM,GAAG;EAAE9B,UAAAA,IAAI,EAAE,MAAA;WAAQ,CAAA;EAC/B,QAAA,IAAIyS,KAAI,CAACO,KAAK,CAAC2C,GAAG,EAAE;YAChB7T,MAAM,CAAC7B,IAAI,GAAA,aAAA,CAAA4P,MAAA,CAAc4C,KAAI,CAACO,KAAK,CAAC2C,GAAG,EAAI,KAAA,CAAA,CAAA;EAC/C,SAAA;UACAlD,KAAI,CAAC0J,OAAO,CAACvI,KAAK,CAAC9R,MAAM,CAAC,CAACE,IAAI,CAAC,YAAA;EAAA,UAAA,OAAMyQ,KAAI,CAACoB,MAAM,EAAE,CAAA;WAAC,CAAA,CAAA;EACxD,OAAC,CAAC,CAAA;EACN,KAAC,CAAC,CAAA;KACL,CAAA;EAAAV,EAAAA,MAAA,CACDS,KAAK,GAAL,SAAAA,KAAAA,CAAM3P,OAAO,EAAE;EAAA,IAAA,IAAA6O,MAAA,GAAA,IAAA,CAAA;MACX,IAAI,CAACC,QAAQ,GAAG,KAAK,CAAA;MAAC,IAAA4H,KAAA,GAAAA,SAAAA,KAAAA,GACmB;EACrC,MAAA,IAAM7Y,MAAM,GAAGmC,OAAO,CAAC3B,CAAC,CAAC,CAAA;QACzB,IAAMsY,UAAU,GAAGtY,CAAC,KAAK2B,OAAO,CAAC1B,MAAM,GAAG,CAAC,CAAA;QAC3CuQ,MAAI,CAACqJ,OAAO,CAACvI,KAAK,CAAC9R,MAAM,CAAC,CAACE,IAAI,CAAC,YAAM;EAClC,QAAA,IAAI4Y,UAAU,EAAE;EACZvS,UAAAA,QAAQ,CAAC,YAAM;cACXyK,MAAI,CAACC,QAAQ,GAAG,IAAI,CAAA;EACpBD,YAAAA,MAAI,CAAC5K,YAAY,CAAC,OAAO,CAAC,CAAA;EAC9B,WAAC,EAAE4K,MAAI,CAACrK,YAAY,CAAC,CAAA;EACzB,SAAA;EACJ,OAAC,CAAC,CAAA;OACL,CAAA;EAXD,IAAA,KAAK,IAAInG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,OAAO,CAAC1B,MAAM,EAAED,CAAC,EAAE,EAAA;QAAAqY,KAAA,EAAA,CAAA;EAAA,KAAA;KAY1C,CAAA;EAAAxH,EAAAA,MAAA,CACDM,OAAO,GAAP,SAAAA,UAAU;EACN,IAAA,IAAI4D,EAAE,CAAA;MACN,CAACA,EAAE,GAAG,IAAI,CAAC+D,UAAU,MAAM,IAAI,IAAI/D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7D,KAAK,EAAE,CAAA;KACzE,CAAA;IAAA,OAAAqC,YAAA,CAAAsF,EAAA,EAAA,CAAA;MAAArb,GAAA,EAAA,MAAA;MAAAsP,GAAA,EAvED,SAAAA,GAAAA,GAAW;EACP,MAAA,OAAO,cAAc,CAAA;EACzB,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,CAAA,CAHmBwD,SAAS,CAAA;;ECV1B,IAAM2J,UAAU,GAAG;EACtBC,EAAAA,SAAS,EAAExB,EAAE;EACbyB,EAAAA,YAAY,EAAEtB,EAAE;EAChBuB,EAAAA,OAAO,EAAErD,GAAAA;EACb,CAAC;;ECPD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAMsD,EAAE,GAAG,qPAAqP,CAAA;EAChQ,IAAMC,KAAK,GAAG,CACV,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAChJ,CAAA;EACM,SAASlR,KAAKA,CAACvB,GAAG,EAAE;EACvB,EAAA,IAAIA,GAAG,CAAC5H,MAAM,GAAG,IAAI,EAAE;EACnB,IAAA,MAAM,cAAc,CAAA;EACxB,GAAA;IACA,IAAMsa,GAAG,GAAG1S,GAAG;EAAE2S,IAAAA,CAAC,GAAG3S,GAAG,CAACuK,OAAO,CAAC,GAAG,CAAC;EAAEkD,IAAAA,CAAC,GAAGzN,GAAG,CAACuK,OAAO,CAAC,GAAG,CAAC,CAAA;IAC3D,IAAIoI,CAAC,IAAI,CAAC,CAAC,IAAIlF,CAAC,IAAI,CAAC,CAAC,EAAE;EACpBzN,IAAAA,GAAG,GAAGA,GAAG,CAACzG,SAAS,CAAC,CAAC,EAAEoZ,CAAC,CAAC,GAAG3S,GAAG,CAACzG,SAAS,CAACoZ,CAAC,EAAElF,CAAC,CAAC,CAACpJ,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAAGrE,GAAG,CAACzG,SAAS,CAACkU,CAAC,EAAEzN,GAAG,CAAC5H,MAAM,CAAC,CAAA;EACrG,GAAA;IACA,IAAI0I,CAAC,GAAG0R,EAAE,CAACzQ,IAAI,CAAC/B,GAAG,IAAI,EAAE,CAAC;MAAEqL,GAAG,GAAG,EAAE;EAAElT,IAAAA,CAAC,GAAG,EAAE,CAAA;IAC5C,OAAOA,CAAC,EAAE,EAAE;EACRkT,IAAAA,GAAG,CAACoH,KAAK,CAACta,CAAC,CAAC,CAAC,GAAG2I,CAAC,CAAC3I,CAAC,CAAC,IAAI,EAAE,CAAA;EAC9B,GAAA;IACA,IAAIwa,CAAC,IAAI,CAAC,CAAC,IAAIlF,CAAC,IAAI,CAAC,CAAC,EAAE;MACpBpC,GAAG,CAACuH,MAAM,GAAGF,GAAG,CAAA;MAChBrH,GAAG,CAACwH,IAAI,GAAGxH,GAAG,CAACwH,IAAI,CAACtZ,SAAS,CAAC,CAAC,EAAE8R,GAAG,CAACwH,IAAI,CAACza,MAAM,GAAG,CAAC,CAAC,CAACiM,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;MACxEgH,GAAG,CAACyH,SAAS,GAAGzH,GAAG,CAACyH,SAAS,CAACzO,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;MAClFgH,GAAG,CAAC0H,OAAO,GAAG,IAAI,CAAA;EACtB,GAAA;IACA1H,GAAG,CAAC2H,SAAS,GAAGA,SAAS,CAAC3H,GAAG,EAAEA,GAAG,CAAC,MAAM,CAAC,CAAC,CAAA;IAC3CA,GAAG,CAAC4H,QAAQ,GAAGA,QAAQ,CAAC5H,GAAG,EAAEA,GAAG,CAAC,OAAO,CAAC,CAAC,CAAA;EAC1C,EAAA,OAAOA,GAAG,CAAA;EACd,CAAA;EACA,SAAS2H,SAASA,CAACzc,GAAG,EAAE6T,IAAI,EAAE;IAC1B,IAAM8I,IAAI,GAAG,UAAU;EAAE9P,IAAAA,KAAK,GAAGgH,IAAI,CAAC/F,OAAO,CAAC6O,IAAI,EAAE,GAAG,CAAC,CAAC/b,KAAK,CAAC,GAAG,CAAC,CAAA;EACnE,EAAA,IAAIiT,IAAI,CAACtO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,IAAIsO,IAAI,CAAChS,MAAM,KAAK,CAAC,EAAE;EAC9CgL,IAAAA,KAAK,CAACxF,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;EACtB,GAAA;IACA,IAAIwM,IAAI,CAACtO,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;MACvBsH,KAAK,CAACxF,MAAM,CAACwF,KAAK,CAAChL,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;EACrC,GAAA;EACA,EAAA,OAAOgL,KAAK,CAAA;EAChB,CAAA;EACA,SAAS6P,QAAQA,CAAC5H,GAAG,EAAExC,KAAK,EAAE;IAC1B,IAAM/S,IAAI,GAAG,EAAE,CAAA;IACf+S,KAAK,CAACxE,OAAO,CAAC,2BAA2B,EAAE,UAAU8O,EAAE,EAAE7L,EAAE,EAAE8L,EAAE,EAAE;EAC7D,IAAA,IAAI9L,EAAE,EAAE;EACJxR,MAAAA,IAAI,CAACwR,EAAE,CAAC,GAAG8L,EAAE,CAAA;EACjB,KAAA;EACJ,GAAC,CAAC,CAAA;EACF,EAAA,OAAOtd,IAAI,CAAA;EACf;;ECvDA,IAAM8M,KAAK,GAAGoF,WAAW,CAAC,yBAAyB,CAAC,CAAC;EACrD,IAAMqL,kBAAkB,GAAG,OAAOtW,gBAAgB,KAAK,UAAU,IAC7D,OAAOU,mBAAmB,KAAK,UAAU,CAAA;EAC7C,IAAM6V,uBAAuB,GAAG,EAAE,CAAA;EAClC,IAAID,kBAAkB,EAAE;EACpB;EACA;IACAtW,gBAAgB,CAAC,SAAS,EAAE,YAAM;EAC9B6F,IAAAA,KAAK,CAAC,uDAAuD,EAAE0Q,uBAAuB,CAAClb,MAAM,CAAC,CAAA;EAC9Fkb,IAAAA,uBAAuB,CAAC5d,OAAO,CAAC,UAAC6d,QAAQ,EAAA;QAAA,OAAKA,QAAQ,EAAE,CAAA;OAAC,CAAA,CAAA;KAC5D,EAAE,KAAK,CAAC,CAAA;EACb,CAAA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACaC,IAAAA,oBAAoB,0BAAA9K,QAAA,EAAA;EAC7B;EACJ;EACA;EACA;EACA;EACA;EACI,EAAA,SAAA8K,oBAAYnI,CAAAA,GAAG,EAAE5L,IAAI,EAAE;EAAA,IAAA,IAAA6I,KAAA,CAAA;EACnBA,IAAAA,KAAA,GAAAI,QAAA,CAAAvS,IAAA,KAAM,CAAC,IAAA,IAAA,CAAA;MACPmS,KAAA,CAAKnP,UAAU,GAAGwF,iBAAiB,CAAA;MACnC2J,KAAA,CAAKmL,WAAW,GAAG,EAAE,CAAA;MACrBnL,KAAA,CAAKoL,cAAc,GAAG,CAAC,CAAA;EACvBpL,IAAAA,KAAA,CAAKqL,aAAa,GAAG,CAAC,CAAC,CAAA;EACvBrL,IAAAA,KAAA,CAAKsL,YAAY,GAAG,CAAC,CAAC,CAAA;EACtBtL,IAAAA,KAAA,CAAKuL,WAAW,GAAG,CAAC,CAAC,CAAA;EACrB;EACR;EACA;EACA;MACQvL,KAAA,CAAKwL,gBAAgB,GAAGC,QAAQ,CAAA;EAChC,IAAA,IAAI1I,GAAG,IAAI,QAAQ,KAAA/J,OAAA,CAAY+J,GAAG,CAAE,EAAA;EAChC5L,MAAAA,IAAI,GAAG4L,GAAG,CAAA;EACVA,MAAAA,GAAG,GAAG,IAAI,CAAA;EACd,KAAA;EACA,IAAA,IAAIA,GAAG,EAAE;EACL,MAAA,IAAM2I,SAAS,GAAGzS,KAAK,CAAC8J,GAAG,CAAC,CAAA;EAC5B5L,MAAAA,IAAI,CAAC6K,QAAQ,GAAG0J,SAAS,CAACnB,IAAI,CAAA;EAC9BpT,MAAAA,IAAI,CAACgL,MAAM,GACPuJ,SAAS,CAACrX,QAAQ,KAAK,OAAO,IAAIqX,SAAS,CAACrX,QAAQ,KAAK,KAAK,CAAA;EAClE8C,MAAAA,IAAI,CAAC+K,IAAI,GAAGwJ,SAAS,CAACxJ,IAAI,CAAA;QAC1B,IAAIwJ,SAAS,CAACnL,KAAK,EACfpJ,IAAI,CAACoJ,KAAK,GAAGmL,SAAS,CAACnL,KAAK,CAAA;EACpC,KAAC,MACI,IAAIpJ,IAAI,CAACoT,IAAI,EAAE;QAChBpT,IAAI,CAAC6K,QAAQ,GAAG/I,KAAK,CAAC9B,IAAI,CAACoT,IAAI,CAAC,CAACA,IAAI,CAAA;EACzC,KAAA;EACArT,IAAAA,qBAAqB,CAAA8I,KAAA,EAAO7I,IAAI,CAAC,CAAA;MACjC6I,KAAA,CAAKmC,MAAM,GACP,IAAI,IAAIhL,IAAI,CAACgL,MAAM,GACbhL,IAAI,CAACgL,MAAM,GACX,OAAOyB,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAKA,QAAQ,CAACvP,QAAQ,CAAA;MAC3E,IAAI8C,IAAI,CAAC6K,QAAQ,IAAI,CAAC7K,IAAI,CAAC+K,IAAI,EAAE;EAC7B;QACA/K,IAAI,CAAC+K,IAAI,GAAGlC,KAAA,CAAKmC,MAAM,GAAG,KAAK,GAAG,IAAI,CAAA;EAC1C,KAAA;EACAnC,IAAAA,KAAA,CAAKgC,QAAQ,GACT7K,IAAI,CAAC6K,QAAQ,KACR,OAAO4B,QAAQ,KAAK,WAAW,GAAGA,QAAQ,CAAC5B,QAAQ,GAAG,WAAW,CAAC,CAAA;MAC3EhC,KAAA,CAAKkC,IAAI,GACL/K,IAAI,CAAC+K,IAAI,KACJ,OAAO0B,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAAC1B,IAAI,GAC3C0B,QAAQ,CAAC1B,IAAI,GACblC,KAAA,CAAKmC,MAAM,GACP,KAAK,GACL,IAAI,CAAC,CAAA;MACvBnC,KAAA,CAAK8J,UAAU,GAAG,EAAE,CAAA;EACpB9J,IAAAA,KAAA,CAAK2L,iBAAiB,GAAG,EAAE,CAAA;EAC3BxU,IAAAA,IAAI,CAAC2S,UAAU,CAAC1c,OAAO,CAAC,UAACwe,CAAC,EAAK;EAC3B,MAAA,IAAMC,aAAa,GAAGD,CAAC,CAACje,SAAS,CAACsM,IAAI,CAAA;EACtC+F,MAAAA,KAAA,CAAK8J,UAAU,CAAC9X,IAAI,CAAC6Z,aAAa,CAAC,CAAA;EACnC7L,MAAAA,KAAA,CAAK2L,iBAAiB,CAACE,aAAa,CAAC,GAAGD,CAAC,CAAA;EAC7C,KAAC,CAAC,CAAA;EACF5L,IAAAA,KAAA,CAAK7I,IAAI,GAAG6P,QAAA,CAAc;EACtBlF,MAAAA,IAAI,EAAE,YAAY;EAClBgK,MAAAA,KAAK,EAAE,KAAK;EACZxG,MAAAA,eAAe,EAAE,KAAK;EACtByG,MAAAA,OAAO,EAAE,IAAI;EACb9I,MAAAA,cAAc,EAAE,GAAG;EACnB+I,MAAAA,eAAe,EAAE,KAAK;EACtBC,MAAAA,gBAAgB,EAAE,IAAI;EACtBC,MAAAA,kBAAkB,EAAE,IAAI;EACxBC,MAAAA,iBAAiB,EAAE;EACfC,QAAAA,SAAS,EAAE,IAAA;SACd;QACDvD,gBAAgB,EAAE,EAAE;EACpBwD,MAAAA,mBAAmB,EAAE,KAAA;OACxB,EAAElV,IAAI,CAAC,CAAA;MACR6I,KAAA,CAAK7I,IAAI,CAAC2K,IAAI,GACV9B,KAAA,CAAK7I,IAAI,CAAC2K,IAAI,CAAC/F,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAC5BiE,KAAA,CAAK7I,IAAI,CAAC8U,gBAAgB,GAAG,GAAG,GAAG,EAAE,CAAC,CAAA;MAC/C,IAAI,OAAOjM,KAAA,CAAK7I,IAAI,CAACoJ,KAAK,KAAK,QAAQ,EAAE;EACrCP,MAAAA,KAAA,CAAK7I,IAAI,CAACoJ,KAAK,GAAGvQ,MAAM,CAACgQ,KAAA,CAAK7I,IAAI,CAACoJ,KAAK,CAAC,CAAA;EAC7C,KAAA;EACA,IAAA,IAAIwK,kBAAkB,EAAE;EACpB,MAAA,IAAI/K,KAAA,CAAK7I,IAAI,CAACkV,mBAAmB,EAAE;EAC/B;EACA;EACA;UACArM,KAAA,CAAKsM,0BAA0B,GAAG,YAAM;YACpC,IAAItM,KAAA,CAAKuM,SAAS,EAAE;EAChB;EACAvM,YAAAA,KAAA,CAAKuM,SAAS,CAACrX,kBAAkB,EAAE,CAAA;EACnC8K,YAAAA,KAAA,CAAKuM,SAAS,CAACxL,KAAK,EAAE,CAAA;EAC1B,WAAA;WACH,CAAA;UACDtM,gBAAgB,CAAC,cAAc,EAAEuL,KAAA,CAAKsM,0BAA0B,EAAE,KAAK,CAAC,CAAA;EAC5E,OAAA;EACA,MAAA,IAAItM,KAAA,CAAKgC,QAAQ,KAAK,WAAW,EAAE;UAC/B1H,KAAK,CAAC,yCAAyC,CAAC,CAAA;UAChD0F,KAAA,CAAKwM,qBAAqB,GAAG,YAAM;EAC/BxM,UAAAA,KAAA,CAAKyM,QAAQ,CAAC,iBAAiB,EAAE;EAC7B3M,YAAAA,WAAW,EAAE,yBAAA;EACjB,WAAC,CAAC,CAAA;WACL,CAAA;EACDkL,QAAAA,uBAAuB,CAAChZ,IAAI,CAACgO,KAAA,CAAKwM,qBAAqB,CAAC,CAAA;EAC5D,OAAA;EACJ,KAAA;EACA,IAAA,IAAIxM,KAAA,CAAK7I,IAAI,CAACmO,eAAe,EAAE;EAC3BtF,MAAAA,KAAA,CAAK0M,UAAU,GAAGpW,eAAe,EAAE,CAAA;EACvC,KAAA;MACA0J,KAAA,CAAK2M,KAAK,EAAE,CAAA;EAAC,IAAA,OAAA3M,KAAA,CAAA;EACjB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;IANIC,cAAA,CAAAiL,oBAAA,EAAA9K,QAAA,CAAA,CAAA;EAAA,EAAA,IAAAM,MAAA,GAAAwK,oBAAA,CAAAvd,SAAA,CAAA;EAAA+S,EAAAA,MAAA,CAOAkM,eAAe,GAAf,SAAAA,eAAAA,CAAgB3S,IAAI,EAAE;EAClBK,IAAAA,KAAK,CAAC,yBAAyB,EAAEL,IAAI,CAAC,CAAA;EACtC,IAAA,IAAMsG,KAAK,GAAGyG,QAAA,CAAc,EAAE,EAAE,IAAI,CAAC7P,IAAI,CAACoJ,KAAK,CAAC,CAAA;EAChD;MACAA,KAAK,CAACsM,GAAG,GAAGxY,QAAQ,CAAA;EACpB;MACAkM,KAAK,CAACgM,SAAS,GAAGtS,IAAI,CAAA;EACtB;MACA,IAAI,IAAI,CAAC6S,EAAE,EACPvM,KAAK,CAAC2C,GAAG,GAAG,IAAI,CAAC4J,EAAE,CAAA;MACvB,IAAM3V,IAAI,GAAG6P,QAAA,CAAc,EAAE,EAAE,IAAI,CAAC7P,IAAI,EAAE;EACtCoJ,MAAAA,KAAK,EAALA,KAAK;EACLC,MAAAA,MAAM,EAAE,IAAI;QACZwB,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBG,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBD,IAAI,EAAE,IAAI,CAACA,IAAAA;OACd,EAAE,IAAI,CAAC/K,IAAI,CAAC0R,gBAAgB,CAAC5O,IAAI,CAAC,CAAC,CAAA;EACpCK,IAAAA,KAAK,CAAC,aAAa,EAAEnD,IAAI,CAAC,CAAA;MAC1B,OAAO,IAAI,IAAI,CAACwU,iBAAiB,CAAC1R,IAAI,CAAC,CAAC9C,IAAI,CAAC,CAAA;EACjD,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAuJ,EAAAA,MAAA,CAKAiM,KAAK,GAAL,SAAAA,QAAQ;EAAA,IAAA,IAAAtM,MAAA,GAAA,IAAA,CAAA;EACJ,IAAA,IAAI,IAAI,CAACyJ,UAAU,CAACha,MAAM,KAAK,CAAC,EAAE;EAC9B;QACA,IAAI,CAACkG,YAAY,CAAC,YAAM;EACpBqK,QAAAA,MAAI,CAAC5K,YAAY,CAAC,OAAO,EAAE,yBAAyB,CAAC,CAAA;SACxD,EAAE,CAAC,CAAC,CAAA;EACL,MAAA,OAAA;EACJ,KAAA;EACA,IAAA,IAAMoW,aAAa,GAAG,IAAI,CAAC1U,IAAI,CAAC6U,eAAe,IAC3Cd,oBAAoB,CAAC6B,qBAAqB,IAC1C,IAAI,CAACjD,UAAU,CAAC7H,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,GACzC,WAAW,GACX,IAAI,CAAC6H,UAAU,CAAC,CAAC,CAAC,CAAA;MACxB,IAAI,CAACjJ,UAAU,GAAG,SAAS,CAAA;EAC3B,IAAA,IAAM0L,SAAS,GAAG,IAAI,CAACK,eAAe,CAACf,aAAa,CAAC,CAAA;MACrDU,SAAS,CAAC3L,IAAI,EAAE,CAAA;EAChB,IAAA,IAAI,CAACoM,YAAY,CAACT,SAAS,CAAC,CAAA;EAChC,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA7L,EAAAA,MAAA,CAKAsM,YAAY,GAAZ,SAAAA,YAAAA,CAAaT,SAAS,EAAE;EAAA,IAAA,IAAA5J,MAAA,GAAA,IAAA,CAAA;EACpBrI,IAAAA,KAAK,CAAC,sBAAsB,EAAEiS,SAAS,CAACtS,IAAI,CAAC,CAAA;MAC7C,IAAI,IAAI,CAACsS,SAAS,EAAE;QAChBjS,KAAK,CAAC,gCAAgC,EAAE,IAAI,CAACiS,SAAS,CAACtS,IAAI,CAAC,CAAA;EAC5D,MAAA,IAAI,CAACsS,SAAS,CAACrX,kBAAkB,EAAE,CAAA;EACvC,KAAA;EACA;MACA,IAAI,CAACqX,SAAS,GAAGA,SAAS,CAAA;EAC1B;MACAA,SAAS,CACJ/X,EAAE,CAAC,OAAO,EAAE,IAAI,CAACyY,QAAQ,CAAC5V,IAAI,CAAC,IAAI,CAAC,CAAC,CACrC7C,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC0Y,SAAS,CAAC7V,IAAI,CAAC,IAAI,CAAC,CAAC,CACvC7C,EAAE,CAAC,OAAO,EAAE,IAAI,CAACsR,QAAQ,CAACzO,IAAI,CAAC,IAAI,CAAC,CAAC,CACrC7C,EAAE,CAAC,OAAO,EAAE,UAACqL,MAAM,EAAA;EAAA,MAAA,OAAK8C,MAAI,CAAC8J,QAAQ,CAAC,iBAAiB,EAAE5M,MAAM,CAAC,CAAA;OAAC,CAAA,CAAA;EAC1E,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAa,EAAAA,MAAA,CAKAU,MAAM,GAAN,SAAAA,SAAS;MACL9G,KAAK,CAAC,aAAa,CAAC,CAAA;MACpB,IAAI,CAACuG,UAAU,GAAG,MAAM,CAAA;MACxBqK,oBAAoB,CAAC6B,qBAAqB,GACtC,WAAW,KAAK,IAAI,CAACR,SAAS,CAACtS,IAAI,CAAA;EACvC,IAAA,IAAI,CAACxE,YAAY,CAAC,MAAM,CAAC,CAAA;MACzB,IAAI,CAAC0X,KAAK,EAAE,CAAA;EAChB,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAzM,EAAAA,MAAA,CAKAwM,SAAS,GAAT,SAAAA,SAAAA,CAAU7d,MAAM,EAAE;EACd,IAAA,IAAI,SAAS,KAAK,IAAI,CAACwR,UAAU,IAC7B,MAAM,KAAK,IAAI,CAACA,UAAU,IAC1B,SAAS,KAAK,IAAI,CAACA,UAAU,EAAE;QAC/BvG,KAAK,CAAC,sCAAsC,EAAEjL,MAAM,CAAC9B,IAAI,EAAE8B,MAAM,CAAC7B,IAAI,CAAC,CAAA;EACvE,MAAA,IAAI,CAACiI,YAAY,CAAC,QAAQ,EAAEpG,MAAM,CAAC,CAAA;EACnC;EACA,MAAA,IAAI,CAACoG,YAAY,CAAC,WAAW,CAAC,CAAA;QAC9B,QAAQpG,MAAM,CAAC9B,IAAI;EACf,QAAA,KAAK,MAAM;YACP,IAAI,CAAC6f,WAAW,CAAC9T,IAAI,CAACL,KAAK,CAAC5J,MAAM,CAAC7B,IAAI,CAAC,CAAC,CAAA;EACzC,UAAA,MAAA;EACJ,QAAA,KAAK,MAAM;EACP,UAAA,IAAI,CAAC6f,WAAW,CAAC,MAAM,CAAC,CAAA;EACxB,UAAA,IAAI,CAAC5X,YAAY,CAAC,MAAM,CAAC,CAAA;EACzB,UAAA,IAAI,CAACA,YAAY,CAAC,MAAM,CAAC,CAAA;YACzB,IAAI,CAAC6X,iBAAiB,EAAE,CAAA;EACxB,UAAA,MAAA;EACJ,QAAA,KAAK,OAAO;EACR,UAAA,IAAM/J,GAAG,GAAG,IAAIlK,KAAK,CAAC,cAAc,CAAC,CAAA;EACrC;EACAkK,UAAAA,GAAG,CAACgK,IAAI,GAAGle,MAAM,CAAC7B,IAAI,CAAA;EACtB,UAAA,IAAI,CAACsY,QAAQ,CAACvC,GAAG,CAAC,CAAA;EAClB,UAAA,MAAA;EACJ,QAAA,KAAK,SAAS;YACV,IAAI,CAAC9N,YAAY,CAAC,MAAM,EAAEpG,MAAM,CAAC7B,IAAI,CAAC,CAAA;YACtC,IAAI,CAACiI,YAAY,CAAC,SAAS,EAAEpG,MAAM,CAAC7B,IAAI,CAAC,CAAA;EACzC,UAAA,MAAA;EACR,OAAA;EACJ,KAAC,MACI;EACD8M,MAAAA,KAAK,CAAC,6CAA6C,EAAE,IAAI,CAACuG,UAAU,CAAC,CAAA;EACzE,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAAH,EAAAA,MAAA,CAMA0M,WAAW,GAAX,SAAAA,WAAAA,CAAY5f,IAAI,EAAE;EACd,IAAA,IAAI,CAACiI,YAAY,CAAC,WAAW,EAAEjI,IAAI,CAAC,CAAA;EACpC,IAAA,IAAI,CAACsf,EAAE,GAAGtf,IAAI,CAAC0V,GAAG,CAAA;MAClB,IAAI,CAACqJ,SAAS,CAAChM,KAAK,CAAC2C,GAAG,GAAG1V,IAAI,CAAC0V,GAAG,CAAA;EACnC,IAAA,IAAI,CAACmI,aAAa,GAAG7d,IAAI,CAACggB,YAAY,CAAA;EACtC,IAAA,IAAI,CAAClC,YAAY,GAAG9d,IAAI,CAACigB,WAAW,CAAA;EACpC,IAAA,IAAI,CAAClC,WAAW,GAAG/d,IAAI,CAACkG,UAAU,CAAA;MAClC,IAAI,CAAC0N,MAAM,EAAE,CAAA;EACb;EACA,IAAA,IAAI,QAAQ,KAAK,IAAI,CAACP,UAAU,EAC5B,OAAA;MACJ,IAAI,CAACyM,iBAAiB,EAAE,CAAA;EAC5B,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAA5M,EAAAA,MAAA,CAKA4M,iBAAiB,GAAjB,SAAAA,oBAAoB;EAAA,IAAA,IAAA1K,MAAA,GAAA,IAAA,CAAA;EAChB,IAAA,IAAI,CAACtL,cAAc,CAAC,IAAI,CAACoW,iBAAiB,CAAC,CAAA;MAC3C,IAAMC,KAAK,GAAG,IAAI,CAACtC,aAAa,GAAG,IAAI,CAACC,YAAY,CAAA;MACpD,IAAI,CAACE,gBAAgB,GAAG1T,IAAI,CAACC,GAAG,EAAE,GAAG4V,KAAK,CAAA;EAC1C,IAAA,IAAI,CAACD,iBAAiB,GAAG,IAAI,CAAC1X,YAAY,CAAC,YAAM;EAC7C4M,MAAAA,MAAI,CAAC6J,QAAQ,CAAC,cAAc,CAAC,CAAA;OAChC,EAAEkB,KAAK,CAAC,CAAA;EACT,IAAA,IAAI,IAAI,CAACxW,IAAI,CAACuQ,SAAS,EAAE;EACrB,MAAA,IAAI,CAACgG,iBAAiB,CAAC9F,KAAK,EAAE,CAAA;EAClC,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAlH,EAAAA,MAAA,CAKAuM,QAAQ,GAAR,SAAAA,WAAW;MACP,IAAI,CAAC9B,WAAW,CAAC7V,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC8V,cAAc,CAAC,CAAA;EAC/C;EACA;EACA;MACA,IAAI,CAACA,cAAc,GAAG,CAAC,CAAA;EACvB,IAAA,IAAI,CAAC,KAAK,IAAI,CAACD,WAAW,CAACrb,MAAM,EAAE;EAC/B,MAAA,IAAI,CAAC2F,YAAY,CAAC,OAAO,CAAC,CAAA;EAC9B,KAAC,MACI;QACD,IAAI,CAAC0X,KAAK,EAAE,CAAA;EAChB,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAzM,EAAAA,MAAA,CAKAyM,KAAK,GAAL,SAAAA,QAAQ;MACJ,IAAI,QAAQ,KAAK,IAAI,CAACtM,UAAU,IAC5B,IAAI,CAAC0L,SAAS,CAACjM,QAAQ,IACvB,CAAC,IAAI,CAACsN,SAAS,IACf,IAAI,CAACzC,WAAW,CAACrb,MAAM,EAAE;EACzB,MAAA,IAAM0B,OAAO,GAAG,IAAI,CAACqc,mBAAmB,EAAE,CAAA;EAC1CvT,MAAAA,KAAK,CAAC,+BAA+B,EAAE9I,OAAO,CAAC1B,MAAM,CAAC,CAAA;EACtD,MAAA,IAAI,CAACyc,SAAS,CAACrL,IAAI,CAAC1P,OAAO,CAAC,CAAA;EAC5B;EACA;EACA,MAAA,IAAI,CAAC4Z,cAAc,GAAG5Z,OAAO,CAAC1B,MAAM,CAAA;EACpC,MAAA,IAAI,CAAC2F,YAAY,CAAC,OAAO,CAAC,CAAA;EAC9B,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAAiL,EAAAA,MAAA,CAMAmN,mBAAmB,GAAnB,SAAAA,sBAAsB;MAClB,IAAMC,sBAAsB,GAAG,IAAI,CAACvC,WAAW,IAC3C,IAAI,CAACgB,SAAS,CAACtS,IAAI,KAAK,SAAS,IACjC,IAAI,CAACkR,WAAW,CAACrb,MAAM,GAAG,CAAC,CAAA;MAC/B,IAAI,CAACge,sBAAsB,EAAE;QACzB,OAAO,IAAI,CAAC3C,WAAW,CAAA;EAC3B,KAAA;EACA,IAAA,IAAI4C,WAAW,GAAG,CAAC,CAAC;EACpB,IAAA,KAAK,IAAIle,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACsb,WAAW,CAACrb,MAAM,EAAED,CAAC,EAAE,EAAE;QAC9C,IAAMrC,IAAI,GAAG,IAAI,CAAC2d,WAAW,CAACtb,CAAC,CAAC,CAACrC,IAAI,CAAA;EACrC,MAAA,IAAIA,IAAI,EAAE;EACNugB,QAAAA,WAAW,IAAI7e,UAAU,CAAC1B,IAAI,CAAC,CAAA;EACnC,OAAA;QACA,IAAIqC,CAAC,GAAG,CAAC,IAAIke,WAAW,GAAG,IAAI,CAACxC,WAAW,EAAE;UACzCjR,KAAK,CAAC,gCAAgC,EAAEzK,CAAC,EAAE,IAAI,CAACsb,WAAW,CAACrb,MAAM,CAAC,CAAA;UACnE,OAAO,IAAI,CAACqb,WAAW,CAAC3X,KAAK,CAAC,CAAC,EAAE3D,CAAC,CAAC,CAAA;EACvC,OAAA;QACAke,WAAW,IAAI,CAAC,CAAC;EACrB,KAAA;MACAzT,KAAK,CAAC,8BAA8B,EAAEyT,WAAW,EAAE,IAAI,CAACxC,WAAW,CAAC,CAAA;MACpE,OAAO,IAAI,CAACJ,WAAW,CAAA;EAC3B,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACI,gBAAA;EAAAzK,EAAAA,MAAA,CAAcsN,eAAe,GAAf,SAAAA,kBAAkB;EAAA,IAAA,IAAAnL,MAAA,GAAA,IAAA,CAAA;EAC5B,IAAA,IAAI,CAAC,IAAI,CAAC2I,gBAAgB,EACtB,OAAO,IAAI,CAAA;MACf,IAAMyC,UAAU,GAAGnW,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACyT,gBAAgB,CAAA;EACrD,IAAA,IAAIyC,UAAU,EAAE;QACZ3T,KAAK,CAAC,uDAAuD,CAAC,CAAA;QAC9D,IAAI,CAACkR,gBAAgB,GAAG,CAAC,CAAA;EACzB5V,MAAAA,QAAQ,CAAC,YAAM;EACXiN,QAAAA,MAAI,CAAC4J,QAAQ,CAAC,cAAc,CAAC,CAAA;EACjC,OAAC,EAAE,IAAI,CAACzW,YAAY,CAAC,CAAA;EACzB,KAAA;EACA,IAAA,OAAOiY,UAAU,CAAA;EACrB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA,MAPI;IAAAvN,MAAA,CAQAS,KAAK,GAAL,SAAAA,KAAAA,CAAM+M,GAAG,EAAEnV,OAAO,EAAEpE,EAAE,EAAE;MACpB,IAAI,CAAC0Y,WAAW,CAAC,SAAS,EAAEa,GAAG,EAAEnV,OAAO,EAAEpE,EAAE,CAAC,CAAA;EAC7C,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA,MAPI;IAAA+L,MAAA,CAQAQ,IAAI,GAAJ,SAAAA,IAAAA,CAAKgN,GAAG,EAAEnV,OAAO,EAAEpE,EAAE,EAAE;MACnB,IAAI,CAAC0Y,WAAW,CAAC,SAAS,EAAEa,GAAG,EAAEnV,OAAO,EAAEpE,EAAE,CAAC,CAAA;EAC7C,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MARI;EAAA+L,EAAAA,MAAA,CASA2M,WAAW,GAAX,SAAAA,WAAY9f,CAAAA,IAAI,EAAEC,IAAI,EAAEuL,OAAO,EAAEpE,EAAE,EAAE;EACjC,IAAA,IAAI,UAAU,KAAK,OAAOnH,IAAI,EAAE;EAC5BmH,MAAAA,EAAE,GAAGnH,IAAI,CAAA;EACTA,MAAAA,IAAI,GAAGoM,SAAS,CAAA;EACpB,KAAA;EACA,IAAA,IAAI,UAAU,KAAK,OAAOb,OAAO,EAAE;EAC/BpE,MAAAA,EAAE,GAAGoE,OAAO,CAAA;EACZA,MAAAA,OAAO,GAAG,IAAI,CAAA;EAClB,KAAA;MACA,IAAI,SAAS,KAAK,IAAI,CAAC8H,UAAU,IAAI,QAAQ,KAAK,IAAI,CAACA,UAAU,EAAE;EAC/D,MAAA,OAAA;EACJ,KAAA;EACA9H,IAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE,CAAA;EACvBA,IAAAA,OAAO,CAACoV,QAAQ,GAAG,KAAK,KAAKpV,OAAO,CAACoV,QAAQ,CAAA;EAC7C,IAAA,IAAM9e,MAAM,GAAG;EACX9B,MAAAA,IAAI,EAAEA,IAAI;EACVC,MAAAA,IAAI,EAAEA,IAAI;EACVuL,MAAAA,OAAO,EAAEA,OAAAA;OACZ,CAAA;EACD,IAAA,IAAI,CAACtD,YAAY,CAAC,cAAc,EAAEpG,MAAM,CAAC,CAAA;EACzC,IAAA,IAAI,CAAC8b,WAAW,CAACnZ,IAAI,CAAC3C,MAAM,CAAC,CAAA;MAC7B,IAAIsF,EAAE,EACF,IAAI,CAACE,IAAI,CAAC,OAAO,EAAEF,EAAE,CAAC,CAAA;MAC1B,IAAI,CAACwY,KAAK,EAAE,CAAA;EAChB,GAAA;EACA;EACJ;EACA,MAFI;EAAAzM,EAAAA,MAAA,CAGAK,KAAK,GAAL,SAAAA,QAAQ;EAAA,IAAA,IAAA+F,MAAA,GAAA,IAAA,CAAA;EACJ,IAAA,IAAM/F,KAAK,GAAG,SAARA,KAAKA,GAAS;EAChB+F,MAAAA,MAAI,CAAC2F,QAAQ,CAAC,cAAc,CAAC,CAAA;QAC7BnS,KAAK,CAAC,6CAA6C,CAAC,CAAA;EACpDwM,MAAAA,MAAI,CAACyF,SAAS,CAACxL,KAAK,EAAE,CAAA;OACzB,CAAA;EACD,IAAA,IAAMqN,eAAe,GAAG,SAAlBA,eAAeA,GAAS;EAC1BtH,MAAAA,MAAI,CAAChS,GAAG,CAAC,SAAS,EAAEsZ,eAAe,CAAC,CAAA;EACpCtH,MAAAA,MAAI,CAAChS,GAAG,CAAC,cAAc,EAAEsZ,eAAe,CAAC,CAAA;EACzCrN,MAAAA,KAAK,EAAE,CAAA;OACV,CAAA;EACD,IAAA,IAAMsN,cAAc,GAAG,SAAjBA,cAAcA,GAAS;EACzB;EACAvH,MAAAA,MAAI,CAACjS,IAAI,CAAC,SAAS,EAAEuZ,eAAe,CAAC,CAAA;EACrCtH,MAAAA,MAAI,CAACjS,IAAI,CAAC,cAAc,EAAEuZ,eAAe,CAAC,CAAA;OAC7C,CAAA;MACD,IAAI,SAAS,KAAK,IAAI,CAACvN,UAAU,IAAI,MAAM,KAAK,IAAI,CAACA,UAAU,EAAE;QAC7D,IAAI,CAACA,UAAU,GAAG,SAAS,CAAA;EAC3B,MAAA,IAAI,IAAI,CAACsK,WAAW,CAACrb,MAAM,EAAE;EACzB,QAAA,IAAI,CAAC+E,IAAI,CAAC,OAAO,EAAE,YAAM;YACrB,IAAIiS,MAAI,CAAC8G,SAAS,EAAE;EAChBS,YAAAA,cAAc,EAAE,CAAA;EACpB,WAAC,MACI;EACDtN,YAAAA,KAAK,EAAE,CAAA;EACX,WAAA;EACJ,SAAC,CAAC,CAAA;EACN,OAAC,MACI,IAAI,IAAI,CAAC6M,SAAS,EAAE;EACrBS,QAAAA,cAAc,EAAE,CAAA;EACpB,OAAC,MACI;EACDtN,QAAAA,KAAK,EAAE,CAAA;EACX,OAAA;EACJ,KAAA;EACA,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;EAAAL,EAAAA,MAAA,CAKAoF,QAAQ,GAAR,SAAAA,QAAAA,CAASvC,GAAG,EAAE;EACVjJ,IAAAA,KAAK,CAAC,iBAAiB,EAAEiJ,GAAG,CAAC,CAAA;MAC7B2H,oBAAoB,CAAC6B,qBAAqB,GAAG,KAAK,CAAA;EAClD,IAAA,IAAI,IAAI,CAAC5V,IAAI,CAACmX,gBAAgB,IAC1B,IAAI,CAACxE,UAAU,CAACha,MAAM,GAAG,CAAC,IAC1B,IAAI,CAAC+Q,UAAU,KAAK,SAAS,EAAE;QAC/BvG,KAAK,CAAC,uBAAuB,CAAC,CAAA;EAC9B,MAAA,IAAI,CAACwP,UAAU,CAACxW,KAAK,EAAE,CAAA;EACvB,MAAA,OAAO,IAAI,CAACqZ,KAAK,EAAE,CAAA;EACvB,KAAA;EACA,IAAA,IAAI,CAAClX,YAAY,CAAC,OAAO,EAAE8N,GAAG,CAAC,CAAA;EAC/B,IAAA,IAAI,CAACkJ,QAAQ,CAAC,iBAAiB,EAAElJ,GAAG,CAAC,CAAA;EACzC,GAAA;EACA;EACJ;EACA;EACA;EACA,MAJI;IAAA7C,MAAA,CAKA+L,QAAQ,GAAR,SAAAA,SAAS5M,MAAM,EAAEC,WAAW,EAAE;EAC1B,IAAA,IAAI,SAAS,KAAK,IAAI,CAACe,UAAU,IAC7B,MAAM,KAAK,IAAI,CAACA,UAAU,IAC1B,SAAS,KAAK,IAAI,CAACA,UAAU,EAAE;EAC/BvG,MAAAA,KAAK,CAAC,gCAAgC,EAAEuF,MAAM,CAAC,CAAA;EAC/C;EACA,MAAA,IAAI,CAACvI,cAAc,CAAC,IAAI,CAACoW,iBAAiB,CAAC,CAAA;EAC3C;EACA,MAAA,IAAI,CAACnB,SAAS,CAACrX,kBAAkB,CAAC,OAAO,CAAC,CAAA;EAC1C;EACA,MAAA,IAAI,CAACqX,SAAS,CAACxL,KAAK,EAAE,CAAA;EACtB;EACA,MAAA,IAAI,CAACwL,SAAS,CAACrX,kBAAkB,EAAE,CAAA;EACnC,MAAA,IAAI6V,kBAAkB,EAAE;UACpB,IAAI,IAAI,CAACuB,0BAA0B,EAAE;YACjCnX,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAACmX,0BAA0B,EAAE,KAAK,CAAC,CAAA;EAC/E,SAAA;UACA,IAAI,IAAI,CAACE,qBAAqB,EAAE;YAC5B,IAAM3c,CAAC,GAAGmb,uBAAuB,CAAC/I,OAAO,CAAC,IAAI,CAACuK,qBAAqB,CAAC,CAAA;EACrE,UAAA,IAAI3c,CAAC,KAAK,CAAC,CAAC,EAAE;cACVyK,KAAK,CAAC,2CAA2C,CAAC,CAAA;EAClD0Q,YAAAA,uBAAuB,CAAC1V,MAAM,CAACzF,CAAC,EAAE,CAAC,CAAC,CAAA;EACxC,WAAA;EACJ,SAAA;EACJ,OAAA;EACA;QACA,IAAI,CAACgR,UAAU,GAAG,QAAQ,CAAA;EAC1B;QACA,IAAI,CAACiM,EAAE,GAAG,IAAI,CAAA;EACd;QACA,IAAI,CAACrX,YAAY,CAAC,OAAO,EAAEoK,MAAM,EAAEC,WAAW,CAAC,CAAA;EAC/C;EACA;QACA,IAAI,CAACqL,WAAW,GAAG,EAAE,CAAA;QACrB,IAAI,CAACC,cAAc,GAAG,CAAC,CAAA;EAC3B,KAAA;KACH,CAAA;EAAA,EAAA,OAAAF,oBAAA,CAAA;EAAA,CAAA,CAjgBqC5W,OAAO,CAAA,CAAA;EAmgBjD4W,oBAAoB,CAAC7W,QAAQ,GAAGA,QAAQ,CAAA;EACxC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACaka,IAAAA,iBAAiB,0BAAAC,qBAAA,EAAA;EAC1B,EAAA,SAAAD,oBAAc;EAAA,IAAA,IAAAE,MAAA,CAAA;EACVA,IAAAA,MAAA,GAAAD,qBAAA,CAAAzZ,KAAA,CAAA,IAAA,EAASC,SAAS,CAAC,IAAA,IAAA,CAAA;MACnByZ,MAAA,CAAKC,SAAS,GAAG,EAAE,CAAA;EAAC,IAAA,OAAAD,MAAA,CAAA;EACxB,GAAA;IAACxO,cAAA,CAAAsO,iBAAA,EAAAC,qBAAA,CAAA,CAAA;EAAA,EAAA,IAAA7J,OAAA,GAAA4J,iBAAA,CAAA5gB,SAAA,CAAA;EAAAgX,EAAAA,OAAA,CACDvD,MAAM,GAAN,SAAAA,SAAS;EACLoN,IAAAA,qBAAA,CAAA7gB,SAAA,CAAMyT,MAAM,CAAAvT,IAAA,CAAA,IAAA,CAAA,CAAA;MACZ,IAAI,MAAM,KAAK,IAAI,CAACgT,UAAU,IAAI,IAAI,CAAC1J,IAAI,CAAC4U,OAAO,EAAE;QACjDzR,KAAK,CAAC,yBAAyB,CAAC,CAAA;EAChC,MAAA,KAAK,IAAIzK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC6e,SAAS,CAAC5e,MAAM,EAAED,CAAC,EAAE,EAAE;UAC5C,IAAI,CAAC8e,MAAM,CAAC,IAAI,CAACD,SAAS,CAAC7e,CAAC,CAAC,CAAC,CAAA;EAClC,OAAA;EACJ,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAA8U,EAAAA,OAAA,CAMAgK,MAAM,GAAN,SAAAA,MAAAA,CAAO1U,IAAI,EAAE;EAAA,IAAA,IAAA2U,MAAA,GAAA,IAAA,CAAA;EACTtU,IAAAA,KAAK,CAAC,wBAAwB,EAAEL,IAAI,CAAC,CAAA;EACrC,IAAA,IAAIsS,SAAS,GAAG,IAAI,CAACK,eAAe,CAAC3S,IAAI,CAAC,CAAA;MAC1C,IAAI4U,MAAM,GAAG,KAAK,CAAA;MAClB3D,oBAAoB,CAAC6B,qBAAqB,GAAG,KAAK,CAAA;EAClD,IAAA,IAAM+B,eAAe,GAAG,SAAlBA,eAAeA,GAAS;EAC1B,MAAA,IAAID,MAAM,EACN,OAAA;EACJvU,MAAAA,KAAK,CAAC,6BAA6B,EAAEL,IAAI,CAAC,CAAA;QAC1CsS,SAAS,CAACrL,IAAI,CAAC,CAAC;EAAE3T,QAAAA,IAAI,EAAE,MAAM;EAAEC,QAAAA,IAAI,EAAE,OAAA;EAAQ,OAAC,CAAC,CAAC,CAAA;EACjD+e,MAAAA,SAAS,CAAC1X,IAAI,CAAC,QAAQ,EAAE,UAACqZ,GAAG,EAAK;EAC9B,QAAA,IAAIW,MAAM,EACN,OAAA;UACJ,IAAI,MAAM,KAAKX,GAAG,CAAC3gB,IAAI,IAAI,OAAO,KAAK2gB,GAAG,CAAC1gB,IAAI,EAAE;EAC7C8M,UAAAA,KAAK,CAAC,2BAA2B,EAAEL,IAAI,CAAC,CAAA;YACxC2U,MAAI,CAAChB,SAAS,GAAG,IAAI,CAAA;EACrBgB,UAAAA,MAAI,CAACnZ,YAAY,CAAC,WAAW,EAAE8W,SAAS,CAAC,CAAA;YACzC,IAAI,CAACA,SAAS,EACV,OAAA;EACJrB,UAAAA,oBAAoB,CAAC6B,qBAAqB,GACtC,WAAW,KAAKR,SAAS,CAACtS,IAAI,CAAA;YAClCK,KAAK,CAAC,gCAAgC,EAAEsU,MAAI,CAACrC,SAAS,CAACtS,IAAI,CAAC,CAAA;EAC5D2U,UAAAA,MAAI,CAACrC,SAAS,CAAC/K,KAAK,CAAC,YAAM;EACvB,YAAA,IAAIqN,MAAM,EACN,OAAA;EACJ,YAAA,IAAI,QAAQ,KAAKD,MAAI,CAAC/N,UAAU,EAC5B,OAAA;cACJvG,KAAK,CAAC,+CAA+C,CAAC,CAAA;EACtDyU,YAAAA,OAAO,EAAE,CAAA;EACTH,YAAAA,MAAI,CAAC5B,YAAY,CAACT,SAAS,CAAC,CAAA;cAC5BA,SAAS,CAACrL,IAAI,CAAC,CAAC;EAAE3T,cAAAA,IAAI,EAAE,SAAA;EAAU,aAAC,CAAC,CAAC,CAAA;EACrCqhB,YAAAA,MAAI,CAACnZ,YAAY,CAAC,SAAS,EAAE8W,SAAS,CAAC,CAAA;EACvCA,YAAAA,SAAS,GAAG,IAAI,CAAA;cAChBqC,MAAI,CAAChB,SAAS,GAAG,KAAK,CAAA;cACtBgB,MAAI,CAACzB,KAAK,EAAE,CAAA;EAChB,WAAC,CAAC,CAAA;EACN,SAAC,MACI;EACD7S,UAAAA,KAAK,CAAC,6BAA6B,EAAEL,IAAI,CAAC,CAAA;EAC1C,UAAA,IAAMsJ,GAAG,GAAG,IAAIlK,KAAK,CAAC,aAAa,CAAC,CAAA;EACpC;EACAkK,UAAAA,GAAG,CAACgJ,SAAS,GAAGA,SAAS,CAACtS,IAAI,CAAA;EAC9B2U,UAAAA,MAAI,CAACnZ,YAAY,CAAC,cAAc,EAAE8N,GAAG,CAAC,CAAA;EAC1C,SAAA;EACJ,OAAC,CAAC,CAAA;OACL,CAAA;MACD,SAASyL,eAAeA,GAAG;EACvB,MAAA,IAAIH,MAAM,EACN,OAAA;EACJ;EACAA,MAAAA,MAAM,GAAG,IAAI,CAAA;EACbE,MAAAA,OAAO,EAAE,CAAA;QACTxC,SAAS,CAACxL,KAAK,EAAE,CAAA;EACjBwL,MAAAA,SAAS,GAAG,IAAI,CAAA;EACpB,KAAA;EACA;EACA,IAAA,IAAMtE,OAAO,GAAG,SAAVA,OAAOA,CAAI1E,GAAG,EAAK;QACrB,IAAMlE,KAAK,GAAG,IAAIhG,KAAK,CAAC,eAAe,GAAGkK,GAAG,CAAC,CAAA;EAC9C;EACAlE,MAAAA,KAAK,CAACkN,SAAS,GAAGA,SAAS,CAACtS,IAAI,CAAA;EAChC+U,MAAAA,eAAe,EAAE,CAAA;EACjB1U,MAAAA,KAAK,CAAC,kDAAkD,EAAEL,IAAI,EAAEsJ,GAAG,CAAC,CAAA;EACpEqL,MAAAA,MAAI,CAACnZ,YAAY,CAAC,cAAc,EAAE4J,KAAK,CAAC,CAAA;OAC3C,CAAA;MACD,SAAS4P,gBAAgBA,GAAG;QACxBhH,OAAO,CAAC,kBAAkB,CAAC,CAAA;EAC/B,KAAA;EACA;MACA,SAASJ,OAAOA,GAAG;QACfI,OAAO,CAAC,eAAe,CAAC,CAAA;EAC5B,KAAA;EACA;MACA,SAASiH,SAASA,CAACC,EAAE,EAAE;QACnB,IAAI5C,SAAS,IAAI4C,EAAE,CAAClV,IAAI,KAAKsS,SAAS,CAACtS,IAAI,EAAE;UACzCK,KAAK,CAAC,4BAA4B,EAAE6U,EAAE,CAAClV,IAAI,EAAEsS,SAAS,CAACtS,IAAI,CAAC,CAAA;EAC5D+U,QAAAA,eAAe,EAAE,CAAA;EACrB,OAAA;EACJ,KAAA;EACA;EACA,IAAA,IAAMD,OAAO,GAAG,SAAVA,OAAOA,GAAS;EAClBxC,MAAAA,SAAS,CAACtX,cAAc,CAAC,MAAM,EAAE6Z,eAAe,CAAC,CAAA;EACjDvC,MAAAA,SAAS,CAACtX,cAAc,CAAC,OAAO,EAAEgT,OAAO,CAAC,CAAA;EAC1CsE,MAAAA,SAAS,CAACtX,cAAc,CAAC,OAAO,EAAEga,gBAAgB,CAAC,CAAA;EACnDL,MAAAA,MAAI,CAAC9Z,GAAG,CAAC,OAAO,EAAE+S,OAAO,CAAC,CAAA;EAC1B+G,MAAAA,MAAI,CAAC9Z,GAAG,CAAC,WAAW,EAAEoa,SAAS,CAAC,CAAA;OACnC,CAAA;EACD3C,IAAAA,SAAS,CAAC1X,IAAI,CAAC,MAAM,EAAEia,eAAe,CAAC,CAAA;EACvCvC,IAAAA,SAAS,CAAC1X,IAAI,CAAC,OAAO,EAAEoT,OAAO,CAAC,CAAA;EAChCsE,IAAAA,SAAS,CAAC1X,IAAI,CAAC,OAAO,EAAEoa,gBAAgB,CAAC,CAAA;EACzC,IAAA,IAAI,CAACpa,IAAI,CAAC,OAAO,EAAEgT,OAAO,CAAC,CAAA;EAC3B,IAAA,IAAI,CAAChT,IAAI,CAAC,WAAW,EAAEqa,SAAS,CAAC,CAAA;EACjC,IAAA,IAAI,IAAI,CAACR,SAAS,CAACzM,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,IAC7ChI,IAAI,KAAK,cAAc,EAAE;EACzB;QACA,IAAI,CAACjE,YAAY,CAAC,YAAM;UACpB,IAAI,CAAC6Y,MAAM,EAAE;YACTtC,SAAS,CAAC3L,IAAI,EAAE,CAAA;EACpB,SAAA;SACH,EAAE,GAAG,CAAC,CAAA;EACX,KAAC,MACI;QACD2L,SAAS,CAAC3L,IAAI,EAAE,CAAA;EACpB,KAAA;KACH,CAAA;EAAA+D,EAAAA,OAAA,CACDyI,WAAW,GAAX,SAAAA,WAAAA,CAAY5f,IAAI,EAAE;MACd,IAAI,CAACkhB,SAAS,GAAG,IAAI,CAACU,eAAe,CAAC5hB,IAAI,CAAC6hB,QAAQ,CAAC,CAAA;EACpDb,IAAAA,qBAAA,CAAA7gB,SAAA,CAAMyf,WAAW,CAAAvf,IAAA,OAACL,IAAI,CAAA,CAAA;EAC1B,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA,MALI;EAAAmX,EAAAA,OAAA,CAMAyK,eAAe,GAAf,SAAAA,eAAAA,CAAgBC,QAAQ,EAAE;MACtB,IAAMC,gBAAgB,GAAG,EAAE,CAAA;EAC3B,IAAA,KAAK,IAAIzf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwf,QAAQ,CAACvf,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAI,CAAC,IAAI,CAACia,UAAU,CAAC7H,OAAO,CAACoN,QAAQ,CAACxf,CAAC,CAAC,CAAC,EACrCyf,gBAAgB,CAACtd,IAAI,CAACqd,QAAQ,CAACxf,CAAC,CAAC,CAAC,CAAA;EAC1C,KAAA;EACA,IAAA,OAAOyf,gBAAgB,CAAA;KAC1B,CAAA;EAAA,EAAA,OAAAf,iBAAA,CAAA;EAAA,CAAA,CA7IkCrD,oBAAoB,CAAA,CAAA;EA+I3D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACaqE,IAAAA,MAAM,0BAAAC,kBAAA,EAAA;IACf,SAAAD,MAAAA,CAAYxM,GAAG,EAAa;EAAA,IAAA,IAAX5L,IAAI,GAAAnC,SAAA,CAAAlF,MAAA,GAAA,CAAA,IAAAkF,SAAA,CAAA,CAAA,CAAA,KAAA4E,SAAA,GAAA5E,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;MACtB,IAAMya,CAAC,GAAGzW,OAAA,CAAO+J,GAAG,MAAK,QAAQ,GAAGA,GAAG,GAAG5L,IAAI,CAAA;EAC9C,IAAA,IAAI,CAACsY,CAAC,CAAC3F,UAAU,IACZ2F,CAAC,CAAC3F,UAAU,IAAI,OAAO2F,CAAC,CAAC3F,UAAU,CAAC,CAAC,CAAC,KAAK,QAAS,EAAE;EACvD2F,MAAAA,CAAC,CAAC3F,UAAU,GAAG,CAAC2F,CAAC,CAAC3F,UAAU,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,cAAc,CAAC,EACnExM,GAAG,CAAC,UAACuO,aAAa,EAAA;UAAA,OAAK6D,UAAkB,CAAC7D,aAAa,CAAC,CAAA;EAAA,OAAA,CAAC,CACzD8D,MAAM,CAAC,UAAC/D,CAAC,EAAA;UAAA,OAAK,CAAC,CAACA,CAAC,CAAA;SAAC,CAAA,CAAA;EAC3B,KAAA;EAAC,IAAA,OACD4D,kBAAA,CAAA3hB,IAAA,OAAMkV,GAAG,EAAE0M,CAAC,CAAC,IAAA,IAAA,CAAA;EACjB,GAAA;IAACxP,cAAA,CAAAsP,MAAA,EAAAC,kBAAA,CAAA,CAAA;EAAA,EAAA,OAAAD,MAAA,CAAA;EAAA,CAAA,CAVuBhB,iBAAiB,CAAA;;ACvuB7C,0BAAe,CAAA,UAACxL,GAAG,EAAE5L,IAAI,EAAA;EAAA,EAAA,OAAK,IAAIoY,MAAM,CAACxM,GAAG,EAAE5L,IAAI,CAAC,CAAA;EAAA,CAAA;;;;;;;;"}