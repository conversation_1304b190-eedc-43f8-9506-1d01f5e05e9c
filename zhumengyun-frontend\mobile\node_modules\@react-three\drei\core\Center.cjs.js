"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("three"),r=require("react");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var i=n(e),o=u(r);const c=o.forwardRef((function({children:e,disable:r,disableX:n,disableY:u,disableZ:c,left:a,right:l,top:f,bottom:d,front:s,back:p,onCentered:b,precise:m=!0,cacheKey:g=0,...h},x){const y=o.useRef(null),j=o.useRef(null),v=o.useRef(null);return o.useLayoutEffect((()=>{j.current.matrixWorld.identity();const e=(new t.Box3).setFromObject(v.current,m),i=new t.Vector3,o=new t.Sphere,g=e.max.x-e.min.x,h=e.max.y-e.min.y,x=e.max.z-e.min.z;e.getCenter(i),e.getBoundingSphere(o);const O=f?h/2:d?-h/2:0,w=a?-g/2:l?g/2:0,z=s?x/2:p?-x/2:0;j.current.position.set(r||n?0:-i.x+w,r||u?0:-i.y+O,r||c?0:-i.z+z),void 0!==b&&b({parent:y.current.parent,container:y.current,width:g,height:h,depth:x,boundingBox:e,boundingSphere:o,center:i,verticalAlignment:O,horizontalAlignment:w,depthAlignment:z})}),[g,b,f,a,s,r,n,u,c,m,l,d,p]),o.useImperativeHandle(x,(()=>y.current),[]),o.createElement("group",i.default({ref:y},h),o.createElement("group",{ref:j},o.createElement("group",{ref:v},e)))}));exports.Center=c;
