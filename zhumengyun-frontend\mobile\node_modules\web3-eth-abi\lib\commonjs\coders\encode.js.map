{"version": 3, "file": "encode.js", "sourceRoot": "", "sources": ["../../../src/coders/encode.ts"], "names": [], "mappings": ";;AAoEA,4CAYC;AAqBD,sEAYC;AAjHD;;;;;;;;;;;;;;;EAeE;AACF,6CAAuC;AAEvC,2CAAmC;AACnC,mDAAuC;AACvC,8CAA8C;AAC9C,yCAAyC;AAEzC;;;;;;;;;GASG;AACH,SAAS,cAAc,CAAC,MAAiB;IACxC,MAAM,GAAG,GAAmB,EAAE,CAAC;IAC/B,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACtB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1B,MAAM,cAAc,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;YAC7C,GAAG,CAAC,IAAI,CAAC;gBACR,IAAI,EAAE,OAAO;gBACb,UAAU,EAAE,cAAc;gBAC1B,IAAI,EAAE,EAAE;gBACR,iEAAiE;aACjD,CAAC,CAAC;QACpB,CAAC;aAAM,CAAC;YACP,iEAAiE;YACjE,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAA,kBAAK,EAAC,KAAe,EAAE,IAAI,CAAC,EAAkB,CAAC,CAAC;QAClE,CAAC;IACF,CAAC,CAAC,CAAC;IACH,OAAO,GAAG,CAAC;AACZ,CAAC;AAED;;;;;;;;;;;;;;;GAeG;AACH,SAAgB,gBAAgB,CAAC,GAA4B,EAAE,MAAiB;IAC/E,IAAI,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,MAAM,MAAK,MAAM,CAAC,MAAM,EAAE,CAAC;QACnC,MAAM,IAAI,sBAAQ,CAAC,iDAAiD,EAAE;YACrE,QAAQ,EAAE,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,MAAM;YACrB,QAAQ,EAAE,MAAM,CAAC,MAAM;SACvB,CAAC,CAAC;IACJ,CAAC;IAED,MAAM,SAAS,GAAG,IAAA,sBAAW,EAAC,GAAG,CAAC,CAAC;IACnC,OAAO,sBAAK,CAAC,qBAAqB,CACjC,IAAA,sBAAW,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC,OAAO,CAC/E,CAAC;AACH,CAAC;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACH,SAAgB,6BAA6B,CAAC,MAAiB;IAC9D,IAAI,CAAC;QACJ,MAAM,SAAS,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;QACzC,OAAO,sBAAK,CAAC,qBAAqB,CACjC,IAAA,sBAAW,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC,OAAO,CAC/E,CAAC;IACH,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACZ,qDAAqD;QACrD,MAAM,IAAI,sBAAQ,CAAC,yCAAyC,EAAE;YAC7D,MAAM;SACN,CAAC,CAAC;IACJ,CAAC;AACF,CAAC"}