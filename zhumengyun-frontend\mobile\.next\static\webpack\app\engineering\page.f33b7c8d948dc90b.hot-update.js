"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/engineering/page",{

/***/ "(app-pages-browser)/./src/app/engineering/page.tsx":
/*!**************************************!*\
  !*** ./src/app/engineering/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DiscoveryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction DiscoveryPage() {\n    var _selectedContent_tags;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('video');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    // 交互状态\n    const [likedItems, setLikedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [savedItems, setSavedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [followedUsers, setFollowedUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [joinedGroups, setJoinedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'c1'\n    ]));\n    // 弹窗状态\n    const [showContentDetail, setShowContentDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showApplyProject, setShowApplyProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showShareModal, setShowShareModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNFTModal, setShowNFTModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTipModal, setShowTipModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSuccessToast, setShowSuccessToast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [toastMessage, setToastMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 选中的项目\n    const [selectedContent, setSelectedContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedProject, setSelectedProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 加载状态\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 筛选后的内容\n    const [filteredContent, setFilteredContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // 模拟发布的内容数据（对应发布页面的四大平台）\n    const discoveryContent = {\n        video: [\n            {\n                id: 'v1',\n                title: 'NextGen 2025智慧城市建设项目展示',\n                creator: '工程师·张三',\n                avatar: '👨‍💻',\n                thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop',\n                duration: '3:45',\n                views: 12500,\n                likes: 890,\n                publishTime: '2小时前',\n                tags: [\n                    '智慧城市',\n                    'AI技术',\n                    '建筑设计'\n                ],\n                description: '展示了最新的AI驱动智慧城市建设项目，包括智能交通系统和物联网基础设施...',\n                nftEnabled: true,\n                didVerified: true,\n                rewards: 120\n            },\n            {\n                id: 'v2',\n                title: 'AI辅助建筑设计全流程演示',\n                creator: '建筑师·王设计',\n                avatar: '🏗️',\n                thumbnail: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop',\n                duration: '5:20',\n                views: 8900,\n                likes: 567,\n                publishTime: '4小时前',\n                tags: [\n                    '建筑设计',\n                    'AI辅助',\n                    'BIM'\n                ],\n                description: '完整展示AI辅助建筑设计的全流程，从概念设计到施工图生成...',\n                nftEnabled: false,\n                didVerified: true,\n                rewards: 85\n            },\n            {\n                id: 'v3',\n                title: '元宇宙虚拟展厅设计案例',\n                creator: 'VR设计师·小李',\n                avatar: '🌌',\n                thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop',\n                duration: '4:15',\n                views: 15600,\n                likes: 1234,\n                publishTime: '6小时前',\n                tags: [\n                    '元宇宙',\n                    'VR设计',\n                    '虚拟展厅'\n                ],\n                description: '创新的元宇宙虚拟展厅设计，支持多人实时交互和3D展示...',\n                nftEnabled: true,\n                didVerified: true,\n                rewards: 150\n            }\n        ],\n        discovery: [\n            {\n                id: 'd1',\n                title: '2025年建筑行业AI应用趋势报告',\n                creator: '行业分析师·陈专家',\n                avatar: '📊',\n                images: [\n                    'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',\n                    'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop'\n                ],\n                readTime: '8分钟',\n                views: 5600,\n                likes: 234,\n                publishTime: '1小时前',\n                tags: [\n                    '行业报告',\n                    'AI应用',\n                    '建筑趋势'\n                ],\n                description: '深度分析2025年建筑行业AI应用的最新趋势，包括设计自动化、施工机器人等...',\n                nftEnabled: false,\n                didVerified: true,\n                rewards: 75\n            },\n            {\n                id: 'd2',\n                title: '智能建筑物联网系统设计指南',\n                creator: '物联网工程师·刘技术',\n                avatar: '🔗',\n                images: [\n                    'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop'\n                ],\n                readTime: '12分钟',\n                views: 3400,\n                likes: 189,\n                publishTime: '3小时前',\n                tags: [\n                    '物联网',\n                    '智能建筑',\n                    '系统设计'\n                ],\n                description: '详细介绍智能建筑物联网系统的设计原理、技术架构和实施方案...',\n                nftEnabled: true,\n                didVerified: true,\n                rewards: 95\n            }\n        ],\n        community: [\n            {\n                id: 'c1',\n                title: '建筑师AI工具使用经验分享',\n                creator: '建筑师联盟',\n                avatar: '🏗️',\n                groupType: '技术讨论',\n                members: 2340,\n                posts: 156,\n                publishTime: '30分钟前',\n                tags: [\n                    '经验分享',\n                    'AI工具',\n                    '建筑师'\n                ],\n                description: '分享各种AI工具在建筑设计中的实际应用经验，包括Midjourney、Stable Diffusion等...',\n                isJoined: true,\n                activity: 'high'\n            },\n            {\n                id: 'c2',\n                title: 'Web3建设者技术讨论群',\n                creator: 'Web3建设者',\n                avatar: '🌐',\n                groupType: '技术交流',\n                members: 1567,\n                posts: 89,\n                publishTime: '1小时前',\n                tags: [\n                    'Web3',\n                    '区块链',\n                    '技术讨论'\n                ],\n                description: '讨论Web3技术在建筑和工程领域的应用，包括DeFi、NFT、DAO等...',\n                isJoined: false,\n                activity: 'medium'\n            }\n        ],\n        ecosystem: [\n            {\n                id: 'e1',\n                title: '寻求AI建筑设计合作伙伴',\n                creator: '建筑事务所·王总',\n                avatar: '🏢',\n                budget: '50-100万',\n                duration: '3-6个月',\n                location: '北京',\n                publishTime: '2小时前',\n                tags: [\n                    '项目合作',\n                    'AI建筑',\n                    '设计服务'\n                ],\n                description: '我们正在开发一个大型商业综合体项目，需要AI建筑设计方面的合作伙伴...',\n                requirements: [\n                    'AI设计经验',\n                    'BIM技术',\n                    '团队规模10+'\n                ],\n                matchType: '技术合作',\n                status: 'open'\n            },\n            {\n                id: 'e2',\n                title: '智慧城市项目寻求技术团队',\n                creator: '政府采购部门',\n                avatar: '🏛️',\n                budget: '200-500万',\n                duration: '6-12个月',\n                location: '上海',\n                publishTime: '4小时前',\n                tags: [\n                    '政府项目',\n                    '智慧城市',\n                    '技术团队'\n                ],\n                description: '智慧城市基础设施建设项目，需要具备AI、物联网、大数据技术的团队...',\n                requirements: [\n                    '政府项目经验',\n                    '资质齐全',\n                    '技术实力强'\n                ],\n                matchType: '工程项目匹配',\n                status: 'open'\n            }\n        ]\n    };\n    // 分类选项\n    const categories = [\n        {\n            key: 'all',\n            name: '全部',\n            icon: '🌟'\n        },\n        {\n            key: 'ai',\n            name: 'AI技术',\n            icon: '🤖'\n        },\n        {\n            key: 'architecture',\n            name: '建筑设计',\n            icon: '🏗️'\n        },\n        {\n            key: 'smart-city',\n            name: '智慧城市',\n            icon: '🏙️'\n        },\n        {\n            key: 'web3',\n            name: 'Web3',\n            icon: '🌐'\n        },\n        {\n            key: 'iot',\n            name: '物联网',\n            icon: '🔗'\n        },\n        {\n            key: 'vr',\n            name: 'VR/AR',\n            icon: '🥽'\n        }\n    ];\n    // 显示成功提示\n    const showToast = (message)=>{\n        setToastMessage(message);\n        setShowSuccessToast(true);\n        setTimeout(()=>setShowSuccessToast(false), 3000);\n    };\n    // 搜索和筛选功能\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DiscoveryPage.useEffect\": ()=>{\n            const filterContent = {\n                \"DiscoveryPage.useEffect.filterContent\": ()=>{\n                    const filtered = {};\n                    Object.keys(discoveryContent).forEach({\n                        \"DiscoveryPage.useEffect.filterContent\": (type)=>{\n                            filtered[type] = discoveryContent[type].filter({\n                                \"DiscoveryPage.useEffect.filterContent\": (item)=>{\n                                    // 分类筛选\n                                    const matchesCategory = selectedCategory === 'all' || item.tags.some({\n                                        \"DiscoveryPage.useEffect.filterContent\": (tag)=>{\n                                            switch(selectedCategory){\n                                                case 'ai':\n                                                    return tag.includes('AI') || tag.includes('智能');\n                                                case 'architecture':\n                                                    return tag.includes('建筑') || tag.includes('设计');\n                                                case 'smart-city':\n                                                    return tag.includes('智慧城市') || tag.includes('城市');\n                                                case 'web3':\n                                                    return tag.includes('Web3') || tag.includes('区块链') || tag.includes('NFT');\n                                                case 'iot':\n                                                    return tag.includes('物联网') || tag.includes('IoT');\n                                                case 'vr':\n                                                    return tag.includes('VR') || tag.includes('AR') || tag.includes('元宇宙');\n                                                default:\n                                                    return true;\n                                            }\n                                        }\n                                    }[\"DiscoveryPage.useEffect.filterContent\"]);\n                                    return matchesCategory;\n                                }\n                            }[\"DiscoveryPage.useEffect.filterContent\"]);\n                        }\n                    }[\"DiscoveryPage.useEffect.filterContent\"]);\n                    setFilteredContent(filtered);\n                }\n            }[\"DiscoveryPage.useEffect.filterContent\"];\n            filterContent();\n        }\n    }[\"DiscoveryPage.useEffect\"], [\n        selectedCategory\n    ]);\n    // 点赞功能\n    const handleLike = (itemId)=>{\n        setLikedItems((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(itemId)) {\n                newSet.delete(itemId);\n                showToast('取消点赞');\n            } else {\n                newSet.add(itemId);\n                showToast('点赞成功');\n            }\n            return newSet;\n        });\n    };\n    // 收藏功能\n    const handleSave = (itemId)=>{\n        setSavedItems((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(itemId)) {\n                newSet.delete(itemId);\n                showToast('取消收藏');\n            } else {\n                newSet.add(itemId);\n                showToast('收藏成功');\n            }\n            return newSet;\n        });\n    };\n    // 关注功能\n    const handleFollow = (userId)=>{\n        setFollowedUsers((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(userId)) {\n                newSet.delete(userId);\n                showToast('取消关注');\n            } else {\n                newSet.add(userId);\n                showToast('关注成功');\n            }\n            return newSet;\n        });\n    };\n    // 观看视频\n    const handleWatchVideo = (video)=>{\n        setSelectedContent(video);\n        setShowContentDetail(true);\n    };\n    // 阅读文章\n    const handleReadArticle = (article)=>{\n        setSelectedContent(article);\n        setShowContentDetail(true);\n    };\n    // 分享功能\n    const handleShare = (content)=>{\n        setSelectedContent(content);\n        setShowShareModal(true);\n    };\n    // 加入/退出群组\n    const handleJoinGroup = (groupId)=>{\n        setJoinedGroups((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(groupId)) {\n                newSet.delete(groupId);\n                showToast('已退出群组');\n            } else {\n                newSet.add(groupId);\n                showToast('成功加入群组');\n            }\n            return newSet;\n        });\n    };\n    // 查看项目详情\n    const handleViewProject = (project)=>{\n        setSelectedProject(project);\n        setShowContentDetail(true);\n    };\n    // 申请项目\n    const handleApplyProject = (project)=>{\n        setSelectedProject(project);\n        setShowApplyProject(true);\n    };\n    // NFT购买\n    const handleBuyNFT = (content)=>{\n        setSelectedContent(content);\n        setShowNFTModal(true);\n    };\n    // 创作者打赏\n    const handleTipCreator = (content)=>{\n        setSelectedContent(content);\n        setShowTipModal(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 border-b border-white/10 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-r from-purple-400 to-blue-500 rounded-xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-bold text-lg\",\n                                                    children: \"\\uD83D\\uDD0D\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: \"内容发现\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-purple-300\",\n                                                        children: \"探索 • 学习 • 连接\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400\",\n                                                children: \"●\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" 15.6k 在线\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 rounded-lg p-1 mb-4\",\n                                children: [\n                                    {\n                                        key: 'video',\n                                        title: '视频内容',\n                                        icon: '🎬'\n                                    },\n                                    {\n                                        key: 'discovery',\n                                        title: '图文发现',\n                                        icon: '📰'\n                                    },\n                                    {\n                                        key: 'community',\n                                        title: '社群讨论',\n                                        icon: '👥'\n                                    },\n                                    {\n                                        key: 'ecosystem',\n                                        title: '生态匹配',\n                                        icon: '🤝'\n                                    }\n                                ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(tab.key),\n                                        className: \"flex-1 flex flex-col items-center py-3 px-2 rounded-md text-xs font-medium transition-colors \".concat(activeTab === tab.key ? 'text-white border-b-2 border-white' : 'text-white/50 hover:text-white/70'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg mb-1\",\n                                                children: tab.icon\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tab.title\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, tab.key, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2 overflow-x-auto pb-2\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedCategory(category.key),\n                                        className: \"flex-shrink-0 flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors border border-white/20 \".concat(selectedCategory === category.key ? 'text-white border-white' : 'text-white/50 hover:text-white/70 hover:border-white/40'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: category.icon\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: category.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, category.key, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-4 pb-20\",\n                        children: [\n                            activeTab === 'video' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: (filteredContent.video || discoveryContent.video).map((video)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-xl overflow-hidden border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: video.thumbnail,\n                                                        alt: video.title,\n                                                        className: \"w-full h-48 object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute bottom-2 right-2 bg-black/70 px-2 py-1 rounded text-xs text-white\",\n                                                        children: video.duration\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-2 left-2 flex space-x-2\",\n                                                        children: [\n                                                            video.nftEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-purple-500 px-2 py-1 rounded-full text-xs font-medium\",\n                                                                children: \"\\uD83C\\uDFA8 NFT\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            video.didVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-blue-500 px-2 py-1 rounded-full text-xs font-medium\",\n                                                                children: \"\\uD83C\\uDD94 DID\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center\",\n                                                                children: video.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: video.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: [\n                                                                            video.creator,\n                                                                            \" • \",\n                                                                            video.publishTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-green-400 font-bold text-sm\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            video.rewards,\n                                                                            \" NGT\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 446,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: \"奖励\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 447,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 text-sm mb-3 line-clamp-2\",\n                                                        children: video.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2 mb-3\",\n                                                        children: video.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 border border-purple-300/30 rounded text-xs text-purple-300\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    tag\n                                                                ]\n                                                            }, idx, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleLike(video.id),\n                                                                        className: \"flex flex-col items-center space-y-1 transition-colors \".concat(likedItems.has(video.id) ? 'text-red-400' : 'text-gray-400 hover:text-red-400'),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-8 h-8 bg-white/10 rounded-full flex items-center justify-center\",\n                                                                                children: \"\\uD83D\\uDC4D\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 470,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs\",\n                                                                                children: video.likes + (likedItems.has(video.id) ? 1 : 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 473,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleShare(video),\n                                                                        className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-8 h-8 bg-white/10 rounded-full flex items-center justify-center\",\n                                                                                children: \"\\uD83D\\uDD04\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 480,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs\",\n                                                                                children: \"33\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 483,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleSave(video.id),\n                                                                        className: \"flex flex-col items-center space-y-1 transition-colors \".concat(savedItems.has(video.id) ? 'text-yellow-400' : 'text-gray-400 hover:text-yellow-400'),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-8 h-8 bg-white/10 rounded-full flex items-center justify-center\",\n                                                                                children: \"❤️\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 492,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs\",\n                                                                                children: \"45\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 495,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 486,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-8 h-8 bg-white/10 rounded-full flex items-center justify-center\",\n                                                                                children: \"\\uD83D\\uDCAC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 499,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs\",\n                                                                                children: \"67\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 502,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 498,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    video.nftEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleBuyNFT(video),\n                                                                        className: \"px-3 py-1 bg-purple-500/20 border border-purple-500 rounded-lg text-xs font-medium hover:bg-purple-500/30 transition-colors\",\n                                                                        children: \"购买NFT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 508,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleWatchVideo(video),\n                                                                        className: \"px-3 py-1 bg-purple-500 rounded-lg text-xs font-medium hover:bg-purple-600 transition-colors\",\n                                                                        children: \"观看\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 515,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, video.id, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'discovery' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: (filteredContent.discovery || discoveryContent.discovery).map((article)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-xl overflow-hidden border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-sm\",\n                                                                    children: article.avatar\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 537,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: [\n                                                                            article.creator,\n                                                                            \" • \",\n                                                                            article.publishTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 541,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-green-400 font-bold text-sm\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            article.rewards,\n                                                                            \" NGT\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 544,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-bold text-white mb-2\",\n                                                            children: article.title\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 text-sm mb-3 line-clamp-3\",\n                                                            children: article.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2 mb-3\",\n                                                            children: article.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 border border-blue-300/30 rounded text-xs text-blue-300\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        tag\n                                                                    ]\n                                                                }, idx, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleLike(article.id),\n                                                                            className: \"flex flex-col items-center space-y-1 transition-colors \".concat(likedItems.has(article.id) ? 'text-red-400' : 'text-gray-400 hover:text-red-400'),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 bg-white/10 rounded-full flex items-center justify-center\",\n                                                                                    children: \"\\uD83D\\uDC4D\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 568,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs\",\n                                                                                    children: article.likes + (likedItems.has(article.id) ? 1 : 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 571,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 562,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleShare(article),\n                                                                            className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 bg-white/10 rounded-full flex items-center justify-center\",\n                                                                                    children: \"\\uD83D\\uDD04\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 578,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"33\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 581,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 574,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleSave(article.id),\n                                                                            className: \"flex flex-col items-center space-y-1 transition-colors \".concat(savedItems.has(article.id) ? 'text-yellow-400' : 'text-gray-400 hover:text-yellow-400'),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 bg-white/10 rounded-full flex items-center justify-center\",\n                                                                                    children: \"❤️\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 590,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"45\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 593,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 584,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 bg-white/10 rounded-full flex items-center justify-center\",\n                                                                                    children: \"\\uD83D\\uDCAC\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 597,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"67\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 600,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 596,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2\",\n                                                                    children: [\n                                                                        article.nftEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleBuyNFT(article),\n                                                                            className: \"px-3 py-1 bg-purple-500/20 border border-purple-500 rounded-lg text-xs font-medium hover:bg-purple-500/30 transition-colors\",\n                                                                            children: \"购买NFT\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 606,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleReadArticle(article),\n                                                                            className: \"px-3 py-1 bg-blue-500 rounded-lg text-xs font-medium hover:bg-blue-600 transition-colors\",\n                                                                            children: \"阅读\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 613,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 604,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 21\n                                                }, this),\n                                                article.images && article.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 m-4 rounded-lg overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: article.images[0],\n                                                        alt: article.title,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, article.id, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 531,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'community' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: (filteredContent.community || discoveryContent.community).map((community)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-xl p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center text-xl\",\n                                                                children: community.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 645,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: community.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 649,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: [\n                                                                            community.creator,\n                                                                            \" • \",\n                                                                            community.publishTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 650,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 648,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full \".concat(community.activity === 'high' ? 'bg-green-400' : community.activity === 'medium' ? 'bg-yellow-400' : 'bg-gray-400')\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: community.activity === 'high' ? '活跃' : community.activity === 'medium' ? '一般' : '较少'\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 658,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 text-sm mb-3\",\n                                                children: community.description\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2 mb-3\",\n                                                children: community.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 border border-green-300/30 rounded text-xs text-green-300\",\n                                                        children: [\n                                                            \"#\",\n                                                            tag\n                                                        ]\n                                                    }, idx, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"\\uD83D\\uDC65 \",\n                                                                    community.members.toLocaleString(),\n                                                                    \" 成员\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 677,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"\\uD83D\\uDCAC \",\n                                                                    community.posts,\n                                                                    \" 讨论\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 678,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 border border-blue-300/30 rounded text-xs text-blue-300\",\n                                                                children: community.groupType\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 679,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleShare(community),\n                                                                className: \"px-3 py-1 bg-white/10 rounded-lg text-xs font-medium hover:bg-white/20 transition-colors\",\n                                                                children: \"分享\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 684,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleJoinGroup(community.id),\n                                                                className: \"px-4 py-2 rounded-lg text-sm font-medium transition-colors \".concat(joinedGroups.has(community.id) ? 'bg-gray-600 text-gray-300 hover:bg-gray-500' : 'bg-gradient-to-r from-green-500 to-teal-500 text-white hover:from-green-600 hover:to-teal-600'),\n                                                                children: joinedGroups.has(community.id) ? '已加入' : '加入讨论'\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 690,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, community.id, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'ecosystem' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: (filteredContent.ecosystem || discoveryContent.ecosystem).map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-xl p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center text-xl\",\n                                                                children: project.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 714,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: project.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 718,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: [\n                                                                            project.creator,\n                                                                            \" • \",\n                                                                            project.publishTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 713,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(project.status === 'open' ? 'bg-green-500' : 'bg-gray-500'),\n                                                            children: project.status === 'open' ? '招募中' : '已结束'\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 723,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 text-sm mb-3\",\n                                                children: project.description\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"预算范围\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-green-400 font-bold\",\n                                                                children: project.budget\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 736,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"项目周期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 739,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-blue-400 font-bold\",\n                                                                children: project.duration\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"项目地点\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-purple-400 font-bold\",\n                                                                children: project.location\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 744,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"匹配类型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 747,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-orange-400 font-bold\",\n                                                                children: project.matchType\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 748,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mb-2\",\n                                                        children: \"技能要求\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2\",\n                                                        children: project.requirements.map((req, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 border border-orange-300/30 rounded text-xs text-orange-300\",\n                                                                children: req\n                                                            }, idx, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2 mb-3\",\n                                                children: project.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 border border-red-300/30 rounded text-xs text-red-300\",\n                                                        children: [\n                                                            \"#\",\n                                                            tag\n                                                        ]\n                                                    }, idx, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleShare(project),\n                                                        className: \"px-3 py-2 bg-white/10 rounded-lg text-xs font-medium hover:bg-white/20 transition-colors\",\n                                                        children: \"分享\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSave(project.id),\n                                                        className: \"px-3 py-2 rounded-lg text-xs font-medium transition-colors \".concat(savedItems.has(project.id) ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500' : 'bg-white/10 text-gray-300 hover:bg-white/20'),\n                                                        children: savedItems.has(project.id) ? '已收藏' : '收藏'\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleViewProject(project),\n                                                        className: \"flex-1 py-2 bg-white/10 border border-white/20 rounded-lg text-sm font-medium hover:bg-white/20 transition-colors\",\n                                                        children: \"查看详情\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleApplyProject(project),\n                                                        className: \"flex-1 py-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg text-sm font-medium hover:from-orange-600 hover:to-red-600 transition-colors\",\n                                                        children: \"立即申请\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, project.id, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 711,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 709,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, this),\n            showSuccessToast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 812,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: toastMessage\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 813,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 811,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 810,\n                columnNumber: 9\n            }, this),\n            showContentDetail && selectedContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20 max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"内容详情\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 823,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowContentDetail(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 824,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 822,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-xl\",\n                                            children: selectedContent.avatar\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 834,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: selectedContent.title\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 838,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: selectedContent.creator\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 839,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 837,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 833,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-sm\",\n                                    children: selectedContent.description\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 843,\n                                    columnNumber: 15\n                                }, this),\n                                selectedContent.duration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: [\n                                        \"时长: \",\n                                        selectedContent.duration\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 846,\n                                    columnNumber: 17\n                                }, this),\n                                selectedContent.readTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: [\n                                        \"阅读时间: \",\n                                        selectedContent.readTime\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 852,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: (_selectedContent_tags = selectedContent.tags) === null || _selectedContent_tags === void 0 ? void 0 : _selectedContent_tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 border border-purple-300/30 rounded text-xs text-purple-300\",\n                                            children: [\n                                                \"#\",\n                                                tag\n                                            ]\n                                        }, idx, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 859,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 857,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowContentDetail(false);\n                                                handleTipCreator(selectedContent);\n                                            },\n                                            className: \"flex-1 py-3 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl text-white font-medium hover:from-green-600 hover:to-teal-600 transition-colors\",\n                                            children: \"\\uD83D\\uDCB0 打赏创作者\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 866,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowContentDetail(false),\n                                            className: \"flex-1 py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors\",\n                                            children: \"关闭\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 875,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 865,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 832,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 821,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 820,\n                columnNumber: 9\n            }, this),\n            showShareModal && selectedContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-white/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"分享内容\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 892,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowShareModal(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 893,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 891,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-4 mb-6\",\n                            children: [\n                                {\n                                    name: '微信',\n                                    icon: '💬',\n                                    color: 'bg-green-500'\n                                },\n                                {\n                                    name: '微博',\n                                    icon: '📱',\n                                    color: 'bg-red-500'\n                                },\n                                {\n                                    name: 'QQ',\n                                    icon: '🐧',\n                                    color: 'bg-blue-500'\n                                },\n                                {\n                                    name: '钉钉',\n                                    icon: '💼',\n                                    color: 'bg-blue-600'\n                                },\n                                {\n                                    name: '复制链接',\n                                    icon: '🔗',\n                                    color: 'bg-gray-500'\n                                },\n                                {\n                                    name: '更多',\n                                    icon: '⋯',\n                                    color: 'bg-purple-500'\n                                }\n                            ].map((platform)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        showToast(\"已分享到\".concat(platform.name));\n                                        setShowShareModal(false);\n                                    },\n                                    className: \"\".concat(platform.color, \" rounded-xl p-4 text-white text-center hover:opacity-80 transition-opacity\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl mb-1\",\n                                            children: platform.icon\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 918,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs\",\n                                            children: platform.name\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 919,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, platform.name, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 910,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 901,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowShareModal(false),\n                            className: \"w-full py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors\",\n                            children: \"取消\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 924,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 890,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 889,\n                columnNumber: 9\n            }, this),\n            showApplyProject && selectedProject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20 max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"申请项目\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 939,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowApplyProject(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 938,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold text-white mb-2\",\n                                            children: selectedProject.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 950,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: selectedProject.description\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 951,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 949,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"预算范围\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 956,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 font-bold\",\n                                                    children: selectedProject.budget\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 957,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 955,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"项目周期\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 960,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-400 font-bold\",\n                                                    children: selectedProject.duration\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 961,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 959,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-white mb-2\",\n                                            children: \"个人简介\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 966,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            className: \"w-full border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500\",\n                                            rows: 3,\n                                            placeholder: \"请简要介绍您的相关经验和技能...\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 967,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 965,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-white mb-2\",\n                                            children: \"联系方式\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 975,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            className: \"w-full border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500\",\n                                            placeholder: \"请输入您的联系方式...\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 976,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 974,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-white mb-2\",\n                                            children: \"期望报酬\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 984,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            className: \"w-full border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500\",\n                                            placeholder: \"请输入您的期望报酬...\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 985,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 983,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowApplyProject(false),\n                                            className: \"flex-1 py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors\",\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 993,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowApplyProject(false);\n                                                showToast('申请已提交，请等待回复');\n                                            },\n                                            className: \"flex-1 py-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl text-white font-medium hover:from-orange-600 hover:to-red-600 transition-colors\",\n                                            children: \"提交申请\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 999,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 992,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 948,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 937,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 936,\n                columnNumber: 9\n            }, this),\n            showNFTModal && selectedContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"购买NFT\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1019,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowNFTModal(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1020,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 1018,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center text-3xl mx-auto mb-3\",\n                                            children: \"\\uD83C\\uDFA8\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1030,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold text-white\",\n                                            children: selectedContent.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1033,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: [\n                                                \"by \",\n                                                selectedContent.creator\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1034,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1029,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"NFT价格\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 1039,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400 font-bold\",\n                                                    children: \"0.1 ETH\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 1040,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1038,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Gas费用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 1043,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-400\",\n                                                    children: \"0.005 ETH\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 1044,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1042,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t border-white/10 pt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold\",\n                                                        children: \"总计\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400 font-bold\",\n                                                        children: \"0.105 ETH\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 1049,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 1047,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1046,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1037,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"购买后您将获得该内容的NFT所有权证明，可在二级市场交易。\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1054,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowNFTModal(false),\n                                            className: \"flex-1 py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors\",\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1059,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowNFTModal(false);\n                                                showToast('NFT购买成功！');\n                                            },\n                                            className: \"flex-1 py-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl text-white font-medium hover:from-purple-600 hover:to-pink-600 transition-colors\",\n                                            children: \"确认购买\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1065,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1058,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 1028,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 1017,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 1016,\n                columnNumber: 9\n            }, this),\n            showTipModal && selectedContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-white/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"打赏创作者\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1085,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowTipModal(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1086,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 1084,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-2xl mx-auto mb-2\",\n                                            children: selectedContent.avatar\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1096,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: selectedContent.creator\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1099,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"感谢优质内容创作\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1100,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1095,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-3\",\n                                    children: [\n                                        10,\n                                        50,\n                                        100,\n                                        200,\n                                        500,\n                                        1000\n                                    ].map((amount)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowTipModal(false);\n                                                showToast(\"成功打赏 \".concat(amount, \" NGT\"));\n                                            },\n                                            className: \"py-3 bg-white/10 border border-white/20 rounded-lg text-white font-medium hover:bg-white/20 transition-colors\",\n                                            children: [\n                                                amount,\n                                                \" NGT\"\n                                            ]\n                                        }, amount, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1105,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1103,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        placeholder: \"自定义金额\",\n                                        className: \"w-full border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 1119,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1118,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowTipModal(false),\n                                    className: \"w-full py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors\",\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1126,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 1094,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 1083,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 1082,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n        lineNumber: 343,\n        columnNumber: 5\n    }, this);\n}\n_s(DiscoveryPage, \"p5BLHYhD7bS/EXdk2xWA/9sGK9M=\");\n_c = DiscoveryPage;\nvar _c;\n$RefreshReg$(_c, \"DiscoveryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/engineering/page.tsx\n"));

/***/ })

});