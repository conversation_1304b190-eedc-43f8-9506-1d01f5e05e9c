{"version": 3, "file": "Octree.cjs", "sources": ["../../src/math/Octree.js"], "sourcesContent": ["import { Box3, Line3, Plane, Sphere, Triangle, Vector3 } from 'three'\nimport { Capsule } from '../math/Capsule'\n\nconst _v1 = /* @__PURE__ */ new Vector3()\nconst _v2 = /* @__PURE__ */ new Vector3()\nconst _plane = /* @__PURE__ */ new Plane()\nconst _line1 = /* @__PURE__ */ new Line3()\nconst _line2 = /* @__PURE__ */ new Line3()\nconst _sphere = /* @__PURE__ */ new Sphere()\nconst _capsule = /* @__PURE__ */ new Capsule()\n\nclass Octree {\n  constructor(box) {\n    this.triangles = []\n    this.box = box\n    this.subTrees = []\n  }\n\n  addTriangle(triangle) {\n    if (!this.bounds) this.bounds = new Box3()\n\n    this.bounds.min.x = Math.min(this.bounds.min.x, triangle.a.x, triangle.b.x, triangle.c.x)\n    this.bounds.min.y = Math.min(this.bounds.min.y, triangle.a.y, triangle.b.y, triangle.c.y)\n    this.bounds.min.z = Math.min(this.bounds.min.z, triangle.a.z, triangle.b.z, triangle.c.z)\n    this.bounds.max.x = Math.max(this.bounds.max.x, triangle.a.x, triangle.b.x, triangle.c.x)\n    this.bounds.max.y = Math.max(this.bounds.max.y, triangle.a.y, triangle.b.y, triangle.c.y)\n    this.bounds.max.z = Math.max(this.bounds.max.z, triangle.a.z, triangle.b.z, triangle.c.z)\n\n    this.triangles.push(triangle)\n\n    return this\n  }\n\n  calcBox() {\n    this.box = this.bounds.clone()\n\n    // offset small amount to account for regular grid\n    this.box.min.x -= 0.01\n    this.box.min.y -= 0.01\n    this.box.min.z -= 0.01\n\n    return this\n  }\n\n  split(level) {\n    if (!this.box) return\n\n    const subTrees = []\n    const halfsize = _v2.copy(this.box.max).sub(this.box.min).multiplyScalar(0.5)\n\n    for (let x = 0; x < 2; x++) {\n      for (let y = 0; y < 2; y++) {\n        for (let z = 0; z < 2; z++) {\n          const box = new Box3()\n          const v = _v1.set(x, y, z)\n\n          box.min.copy(this.box.min).add(v.multiply(halfsize))\n          box.max.copy(box.min).add(halfsize)\n\n          subTrees.push(new Octree(box))\n        }\n      }\n    }\n\n    let triangle\n\n    while ((triangle = this.triangles.pop())) {\n      for (let i = 0; i < subTrees.length; i++) {\n        if (subTrees[i].box.intersectsTriangle(triangle)) {\n          subTrees[i].triangles.push(triangle)\n        }\n      }\n    }\n\n    for (let i = 0; i < subTrees.length; i++) {\n      const len = subTrees[i].triangles.length\n\n      if (len > 8 && level < 16) {\n        subTrees[i].split(level + 1)\n      }\n\n      if (len !== 0) {\n        this.subTrees.push(subTrees[i])\n      }\n    }\n\n    return this\n  }\n\n  build() {\n    this.calcBox()\n    this.split(0)\n\n    return this\n  }\n\n  getRayTriangles(ray, triangles) {\n    for (let i = 0; i < this.subTrees.length; i++) {\n      const subTree = this.subTrees[i]\n      if (!ray.intersectsBox(subTree.box)) continue\n\n      if (subTree.triangles.length > 0) {\n        for (let j = 0; j < subTree.triangles.length; j++) {\n          if (triangles.indexOf(subTree.triangles[j]) === -1) triangles.push(subTree.triangles[j])\n        }\n      } else {\n        subTree.getRayTriangles(ray, triangles)\n      }\n    }\n\n    return triangles\n  }\n\n  triangleCapsuleIntersect(capsule, triangle) {\n    triangle.getPlane(_plane)\n\n    const d1 = _plane.distanceToPoint(capsule.start) - capsule.radius\n    const d2 = _plane.distanceToPoint(capsule.end) - capsule.radius\n\n    if ((d1 > 0 && d2 > 0) || (d1 < -capsule.radius && d2 < -capsule.radius)) {\n      return false\n    }\n\n    const delta = Math.abs(d1 / (Math.abs(d1) + Math.abs(d2)))\n    const intersectPoint = _v1.copy(capsule.start).lerp(capsule.end, delta)\n\n    if (triangle.containsPoint(intersectPoint)) {\n      return { normal: _plane.normal.clone(), point: intersectPoint.clone(), depth: Math.abs(Math.min(d1, d2)) }\n    }\n\n    const r2 = capsule.radius * capsule.radius\n\n    const line1 = _line1.set(capsule.start, capsule.end)\n\n    const lines = [\n      [triangle.a, triangle.b],\n      [triangle.b, triangle.c],\n      [triangle.c, triangle.a],\n    ]\n\n    for (let i = 0; i < lines.length; i++) {\n      const line2 = _line2.set(lines[i][0], lines[i][1])\n\n      const [point1, point2] = capsule.lineLineMinimumPoints(line1, line2)\n\n      if (point1.distanceToSquared(point2) < r2) {\n        return {\n          normal: point1.clone().sub(point2).normalize(),\n          point: point2.clone(),\n          depth: capsule.radius - point1.distanceTo(point2),\n        }\n      }\n    }\n\n    return false\n  }\n\n  triangleSphereIntersect(sphere, triangle) {\n    triangle.getPlane(_plane)\n\n    if (!sphere.intersectsPlane(_plane)) return false\n\n    const depth = Math.abs(_plane.distanceToSphere(sphere))\n    const r2 = sphere.radius * sphere.radius - depth * depth\n\n    const plainPoint = _plane.projectPoint(sphere.center, _v1)\n\n    if (triangle.containsPoint(sphere.center)) {\n      return {\n        normal: _plane.normal.clone(),\n        point: plainPoint.clone(),\n        depth: Math.abs(_plane.distanceToSphere(sphere)),\n      }\n    }\n\n    const lines = [\n      [triangle.a, triangle.b],\n      [triangle.b, triangle.c],\n      [triangle.c, triangle.a],\n    ]\n\n    for (let i = 0; i < lines.length; i++) {\n      _line1.set(lines[i][0], lines[i][1])\n      _line1.closestPointToPoint(plainPoint, true, _v2)\n\n      const d = _v2.distanceToSquared(sphere.center)\n\n      if (d < r2) {\n        return {\n          normal: sphere.center.clone().sub(_v2).normalize(),\n          point: _v2.clone(),\n          depth: sphere.radius - Math.sqrt(d),\n        }\n      }\n    }\n\n    return false\n  }\n\n  getSphereTriangles(sphere, triangles) {\n    for (let i = 0; i < this.subTrees.length; i++) {\n      const subTree = this.subTrees[i]\n\n      if (!sphere.intersectsBox(subTree.box)) continue\n\n      if (subTree.triangles.length > 0) {\n        for (let j = 0; j < subTree.triangles.length; j++) {\n          if (triangles.indexOf(subTree.triangles[j]) === -1) triangles.push(subTree.triangles[j])\n        }\n      } else {\n        subTree.getSphereTriangles(sphere, triangles)\n      }\n    }\n  }\n\n  getCapsuleTriangles(capsule, triangles) {\n    for (let i = 0; i < this.subTrees.length; i++) {\n      const subTree = this.subTrees[i]\n\n      if (!capsule.intersectsBox(subTree.box)) continue\n\n      if (subTree.triangles.length > 0) {\n        for (let j = 0; j < subTree.triangles.length; j++) {\n          if (triangles.indexOf(subTree.triangles[j]) === -1) triangles.push(subTree.triangles[j])\n        }\n      } else {\n        subTree.getCapsuleTriangles(capsule, triangles)\n      }\n    }\n  }\n\n  sphereIntersect(sphere) {\n    _sphere.copy(sphere)\n\n    const triangles = []\n    let result,\n      hit = false\n\n    this.getSphereTriangles(sphere, triangles)\n\n    for (let i = 0; i < triangles.length; i++) {\n      if ((result = this.triangleSphereIntersect(_sphere, triangles[i]))) {\n        hit = true\n\n        _sphere.center.add(result.normal.multiplyScalar(result.depth))\n      }\n    }\n\n    if (hit) {\n      const collisionVector = _sphere.center.clone().sub(sphere.center)\n      const depth = collisionVector.length()\n\n      return { normal: collisionVector.normalize(), depth: depth }\n    }\n\n    return false\n  }\n\n  capsuleIntersect(capsule) {\n    _capsule.copy(capsule)\n\n    const triangles = []\n    let result,\n      hit = false\n\n    this.getCapsuleTriangles(_capsule, triangles)\n\n    for (let i = 0; i < triangles.length; i++) {\n      if ((result = this.triangleCapsuleIntersect(_capsule, triangles[i]))) {\n        hit = true\n\n        _capsule.translate(result.normal.multiplyScalar(result.depth))\n      }\n    }\n\n    if (hit) {\n      const collisionVector = _capsule.getCenter(new Vector3()).sub(capsule.getCenter(_v1))\n      const depth = collisionVector.length()\n\n      return { normal: collisionVector.normalize(), depth: depth }\n    }\n\n    return false\n  }\n\n  rayIntersect(ray) {\n    if (ray.direction.length() === 0) return\n\n    const triangles = []\n    let triangle,\n      position,\n      distance = 1e100\n\n    this.getRayTriangles(ray, triangles)\n\n    for (let i = 0; i < triangles.length; i++) {\n      const result = ray.intersectTriangle(triangles[i].a, triangles[i].b, triangles[i].c, true, _v1)\n\n      if (result) {\n        const newdistance = result.sub(ray.origin).length()\n\n        if (distance > newdistance) {\n          position = result.clone().add(ray.origin)\n          distance = newdistance\n          triangle = triangles[i]\n        }\n      }\n    }\n\n    return distance < 1e100 ? { distance: distance, triangle: triangle, position: position } : false\n  }\n\n  fromGraphNode(group) {\n    group.updateWorldMatrix(true, true)\n\n    group.traverse((obj) => {\n      if (obj.isMesh === true) {\n        let geometry,\n          isTemp = false\n\n        if (obj.geometry.index !== null) {\n          isTemp = true\n          geometry = obj.geometry.toNonIndexed()\n        } else {\n          geometry = obj.geometry\n        }\n\n        const positionAttribute = geometry.getAttribute('position')\n\n        for (let i = 0; i < positionAttribute.count; i += 3) {\n          const v1 = new Vector3().fromBufferAttribute(positionAttribute, i)\n          const v2 = new Vector3().fromBufferAttribute(positionAttribute, i + 1)\n          const v3 = new Vector3().fromBufferAttribute(positionAttribute, i + 2)\n\n          v1.applyMatrix4(obj.matrixWorld)\n          v2.applyMatrix4(obj.matrixWorld)\n          v3.applyMatrix4(obj.matrixWorld)\n\n          this.addTriangle(new Triangle(v1, v2, v3))\n        }\n\n        if (isTemp) {\n          geometry.dispose()\n        }\n      }\n    })\n\n    this.build()\n\n    return this\n  }\n}\n\nexport { Octree }\n"], "names": ["Vector3", "Plane", "Line3", "Sphere", "Capsule", "Box3", "Triangle"], "mappings": ";;;;AAGA,MAAM,MAAsB,oBAAIA,MAAAA,QAAS;AACzC,MAAM,MAAsB,oBAAIA,MAAAA,QAAS;AACzC,MAAM,SAAyB,oBAAIC,MAAAA,MAAO;AAC1C,MAAM,SAAyB,oBAAIC,MAAAA,MAAO;AAC1C,MAAM,SAAyB,oBAAIA,MAAAA,MAAO;AAC1C,MAAM,UAA0B,oBAAIC,MAAAA,OAAQ;AAC5C,MAAM,WAA2B,oBAAIC,QAAAA,QAAS;AAE9C,MAAM,OAAO;AAAA,EACX,YAAY,KAAK;AACf,SAAK,YAAY,CAAE;AACnB,SAAK,MAAM;AACX,SAAK,WAAW,CAAE;AAAA,EACnB;AAAA,EAED,YAAY,UAAU;AACpB,QAAI,CAAC,KAAK;AAAQ,WAAK,SAAS,IAAIC,MAAAA,KAAM;AAE1C,SAAK,OAAO,IAAI,IAAI,KAAK,IAAI,KAAK,OAAO,IAAI,GAAG,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,SAAS,EAAE,CAAC;AACxF,SAAK,OAAO,IAAI,IAAI,KAAK,IAAI,KAAK,OAAO,IAAI,GAAG,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,SAAS,EAAE,CAAC;AACxF,SAAK,OAAO,IAAI,IAAI,KAAK,IAAI,KAAK,OAAO,IAAI,GAAG,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,SAAS,EAAE,CAAC;AACxF,SAAK,OAAO,IAAI,IAAI,KAAK,IAAI,KAAK,OAAO,IAAI,GAAG,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,SAAS,EAAE,CAAC;AACxF,SAAK,OAAO,IAAI,IAAI,KAAK,IAAI,KAAK,OAAO,IAAI,GAAG,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,SAAS,EAAE,CAAC;AACxF,SAAK,OAAO,IAAI,IAAI,KAAK,IAAI,KAAK,OAAO,IAAI,GAAG,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,SAAS,EAAE,CAAC;AAExF,SAAK,UAAU,KAAK,QAAQ;AAE5B,WAAO;AAAA,EACR;AAAA,EAED,UAAU;AACR,SAAK,MAAM,KAAK,OAAO,MAAO;AAG9B,SAAK,IAAI,IAAI,KAAK;AAClB,SAAK,IAAI,IAAI,KAAK;AAClB,SAAK,IAAI,IAAI,KAAK;AAElB,WAAO;AAAA,EACR;AAAA,EAED,MAAM,OAAO;AACX,QAAI,CAAC,KAAK;AAAK;AAEf,UAAM,WAAW,CAAE;AACnB,UAAM,WAAW,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,GAAG,EAAE,eAAe,GAAG;AAE5E,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAM,MAAM,IAAIA,WAAM;AACtB,gBAAM,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC;AAEzB,cAAI,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE,IAAI,EAAE,SAAS,QAAQ,CAAC;AACnD,cAAI,IAAI,KAAK,IAAI,GAAG,EAAE,IAAI,QAAQ;AAElC,mBAAS,KAAK,IAAI,OAAO,GAAG,CAAC;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAED,QAAI;AAEJ,WAAQ,WAAW,KAAK,UAAU,IAAG,GAAK;AACxC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAI,SAAS,CAAC,EAAE,IAAI,mBAAmB,QAAQ,GAAG;AAChD,mBAAS,CAAC,EAAE,UAAU,KAAK,QAAQ;AAAA,QACpC;AAAA,MACF;AAAA,IACF;AAED,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,MAAM,SAAS,CAAC,EAAE,UAAU;AAElC,UAAI,MAAM,KAAK,QAAQ,IAAI;AACzB,iBAAS,CAAC,EAAE,MAAM,QAAQ,CAAC;AAAA,MAC5B;AAED,UAAI,QAAQ,GAAG;AACb,aAAK,SAAS,KAAK,SAAS,CAAC,CAAC;AAAA,MAC/B;AAAA,IACF;AAED,WAAO;AAAA,EACR;AAAA,EAED,QAAQ;AACN,SAAK,QAAS;AACd,SAAK,MAAM,CAAC;AAEZ,WAAO;AAAA,EACR;AAAA,EAED,gBAAgB,KAAK,WAAW;AAC9B,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,YAAM,UAAU,KAAK,SAAS,CAAC;AAC/B,UAAI,CAAC,IAAI,cAAc,QAAQ,GAAG;AAAG;AAErC,UAAI,QAAQ,UAAU,SAAS,GAAG;AAChC,iBAAS,IAAI,GAAG,IAAI,QAAQ,UAAU,QAAQ,KAAK;AACjD,cAAI,UAAU,QAAQ,QAAQ,UAAU,CAAC,CAAC,MAAM;AAAI,sBAAU,KAAK,QAAQ,UAAU,CAAC,CAAC;AAAA,QACxF;AAAA,MACT,OAAa;AACL,gBAAQ,gBAAgB,KAAK,SAAS;AAAA,MACvC;AAAA,IACF;AAED,WAAO;AAAA,EACR;AAAA,EAED,yBAAyB,SAAS,UAAU;AAC1C,aAAS,SAAS,MAAM;AAExB,UAAM,KAAK,OAAO,gBAAgB,QAAQ,KAAK,IAAI,QAAQ;AAC3D,UAAM,KAAK,OAAO,gBAAgB,QAAQ,GAAG,IAAI,QAAQ;AAEzD,QAAK,KAAK,KAAK,KAAK,KAAO,KAAK,CAAC,QAAQ,UAAU,KAAK,CAAC,QAAQ,QAAS;AACxE,aAAO;AAAA,IACR;AAED,UAAM,QAAQ,KAAK,IAAI,MAAM,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,EAAE;AACzD,UAAM,iBAAiB,IAAI,KAAK,QAAQ,KAAK,EAAE,KAAK,QAAQ,KAAK,KAAK;AAEtE,QAAI,SAAS,cAAc,cAAc,GAAG;AAC1C,aAAO,EAAE,QAAQ,OAAO,OAAO,MAAO,GAAE,OAAO,eAAe,MAAO,GAAE,OAAO,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC,EAAG;AAAA,IAC3G;AAED,UAAM,KAAK,QAAQ,SAAS,QAAQ;AAEpC,UAAM,QAAQ,OAAO,IAAI,QAAQ,OAAO,QAAQ,GAAG;AAEnD,UAAM,QAAQ;AAAA,MACZ,CAAC,SAAS,GAAG,SAAS,CAAC;AAAA,MACvB,CAAC,SAAS,GAAG,SAAS,CAAC;AAAA,MACvB,CAAC,SAAS,GAAG,SAAS,CAAC;AAAA,IACxB;AAED,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,QAAQ,OAAO,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AAEjD,YAAM,CAAC,QAAQ,MAAM,IAAI,QAAQ,sBAAsB,OAAO,KAAK;AAEnE,UAAI,OAAO,kBAAkB,MAAM,IAAI,IAAI;AACzC,eAAO;AAAA,UACL,QAAQ,OAAO,MAAK,EAAG,IAAI,MAAM,EAAE,UAAW;AAAA,UAC9C,OAAO,OAAO,MAAO;AAAA,UACrB,OAAO,QAAQ,SAAS,OAAO,WAAW,MAAM;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AAED,WAAO;AAAA,EACR;AAAA,EAED,wBAAwB,QAAQ,UAAU;AACxC,aAAS,SAAS,MAAM;AAExB,QAAI,CAAC,OAAO,gBAAgB,MAAM;AAAG,aAAO;AAE5C,UAAM,QAAQ,KAAK,IAAI,OAAO,iBAAiB,MAAM,CAAC;AACtD,UAAM,KAAK,OAAO,SAAS,OAAO,SAAS,QAAQ;AAEnD,UAAM,aAAa,OAAO,aAAa,OAAO,QAAQ,GAAG;AAEzD,QAAI,SAAS,cAAc,OAAO,MAAM,GAAG;AACzC,aAAO;AAAA,QACL,QAAQ,OAAO,OAAO,MAAO;AAAA,QAC7B,OAAO,WAAW,MAAO;AAAA,QACzB,OAAO,KAAK,IAAI,OAAO,iBAAiB,MAAM,CAAC;AAAA,MAChD;AAAA,IACF;AAED,UAAM,QAAQ;AAAA,MACZ,CAAC,SAAS,GAAG,SAAS,CAAC;AAAA,MACvB,CAAC,SAAS,GAAG,SAAS,CAAC;AAAA,MACvB,CAAC,SAAS,GAAG,SAAS,CAAC;AAAA,IACxB;AAED,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,aAAO,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AACnC,aAAO,oBAAoB,YAAY,MAAM,GAAG;AAEhD,YAAM,IAAI,IAAI,kBAAkB,OAAO,MAAM;AAE7C,UAAI,IAAI,IAAI;AACV,eAAO;AAAA,UACL,QAAQ,OAAO,OAAO,MAAO,EAAC,IAAI,GAAG,EAAE,UAAW;AAAA,UAClD,OAAO,IAAI,MAAO;AAAA,UAClB,OAAO,OAAO,SAAS,KAAK,KAAK,CAAC;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAED,WAAO;AAAA,EACR;AAAA,EAED,mBAAmB,QAAQ,WAAW;AACpC,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,YAAM,UAAU,KAAK,SAAS,CAAC;AAE/B,UAAI,CAAC,OAAO,cAAc,QAAQ,GAAG;AAAG;AAExC,UAAI,QAAQ,UAAU,SAAS,GAAG;AAChC,iBAAS,IAAI,GAAG,IAAI,QAAQ,UAAU,QAAQ,KAAK;AACjD,cAAI,UAAU,QAAQ,QAAQ,UAAU,CAAC,CAAC,MAAM;AAAI,sBAAU,KAAK,QAAQ,UAAU,CAAC,CAAC;AAAA,QACxF;AAAA,MACT,OAAa;AACL,gBAAQ,mBAAmB,QAAQ,SAAS;AAAA,MAC7C;AAAA,IACF;AAAA,EACF;AAAA,EAED,oBAAoB,SAAS,WAAW;AACtC,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,YAAM,UAAU,KAAK,SAAS,CAAC;AAE/B,UAAI,CAAC,QAAQ,cAAc,QAAQ,GAAG;AAAG;AAEzC,UAAI,QAAQ,UAAU,SAAS,GAAG;AAChC,iBAAS,IAAI,GAAG,IAAI,QAAQ,UAAU,QAAQ,KAAK;AACjD,cAAI,UAAU,QAAQ,QAAQ,UAAU,CAAC,CAAC,MAAM;AAAI,sBAAU,KAAK,QAAQ,UAAU,CAAC,CAAC;AAAA,QACxF;AAAA,MACT,OAAa;AACL,gBAAQ,oBAAoB,SAAS,SAAS;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AAAA,EAED,gBAAgB,QAAQ;AACtB,YAAQ,KAAK,MAAM;AAEnB,UAAM,YAAY,CAAE;AACpB,QAAI,QACF,MAAM;AAER,SAAK,mBAAmB,QAAQ,SAAS;AAEzC,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAK,SAAS,KAAK,wBAAwB,SAAS,UAAU,CAAC,CAAC,GAAI;AAClE,cAAM;AAEN,gBAAQ,OAAO,IAAI,OAAO,OAAO,eAAe,OAAO,KAAK,CAAC;AAAA,MAC9D;AAAA,IACF;AAED,QAAI,KAAK;AACP,YAAM,kBAAkB,QAAQ,OAAO,MAAK,EAAG,IAAI,OAAO,MAAM;AAChE,YAAM,QAAQ,gBAAgB,OAAQ;AAEtC,aAAO,EAAE,QAAQ,gBAAgB,UAAS,GAAI,MAAc;AAAA,IAC7D;AAED,WAAO;AAAA,EACR;AAAA,EAED,iBAAiB,SAAS;AACxB,aAAS,KAAK,OAAO;AAErB,UAAM,YAAY,CAAE;AACpB,QAAI,QACF,MAAM;AAER,SAAK,oBAAoB,UAAU,SAAS;AAE5C,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAK,SAAS,KAAK,yBAAyB,UAAU,UAAU,CAAC,CAAC,GAAI;AACpE,cAAM;AAEN,iBAAS,UAAU,OAAO,OAAO,eAAe,OAAO,KAAK,CAAC;AAAA,MAC9D;AAAA,IACF;AAED,QAAI,KAAK;AACP,YAAM,kBAAkB,SAAS,UAAU,IAAIL,MAAO,QAAA,CAAE,EAAE,IAAI,QAAQ,UAAU,GAAG,CAAC;AACpF,YAAM,QAAQ,gBAAgB,OAAQ;AAEtC,aAAO,EAAE,QAAQ,gBAAgB,UAAS,GAAI,MAAc;AAAA,IAC7D;AAED,WAAO;AAAA,EACR;AAAA,EAED,aAAa,KAAK;AAChB,QAAI,IAAI,UAAU,OAAQ,MAAK;AAAG;AAElC,UAAM,YAAY,CAAE;AACpB,QAAI,UACF,UACA,WAAW;AAEb,SAAK,gBAAgB,KAAK,SAAS;AAEnC,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,YAAM,SAAS,IAAI,kBAAkB,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,MAAM,GAAG;AAE9F,UAAI,QAAQ;AACV,cAAM,cAAc,OAAO,IAAI,IAAI,MAAM,EAAE,OAAQ;AAEnD,YAAI,WAAW,aAAa;AAC1B,qBAAW,OAAO,MAAK,EAAG,IAAI,IAAI,MAAM;AACxC,qBAAW;AACX,qBAAW,UAAU,CAAC;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAED,WAAO,WAAW,QAAQ,EAAE,UAAoB,UAAoB,SAAkB,IAAK;AAAA,EAC5F;AAAA,EAED,cAAc,OAAO;AACnB,UAAM,kBAAkB,MAAM,IAAI;AAElC,UAAM,SAAS,CAAC,QAAQ;AACtB,UAAI,IAAI,WAAW,MAAM;AACvB,YAAI,UACF,SAAS;AAEX,YAAI,IAAI,SAAS,UAAU,MAAM;AAC/B,mBAAS;AACT,qBAAW,IAAI,SAAS,aAAc;AAAA,QAChD,OAAe;AACL,qBAAW,IAAI;AAAA,QAChB;AAED,cAAM,oBAAoB,SAAS,aAAa,UAAU;AAE1D,iBAAS,IAAI,GAAG,IAAI,kBAAkB,OAAO,KAAK,GAAG;AACnD,gBAAM,KAAK,IAAIA,MAAO,QAAA,EAAG,oBAAoB,mBAAmB,CAAC;AACjE,gBAAM,KAAK,IAAIA,cAAS,EAAC,oBAAoB,mBAAmB,IAAI,CAAC;AACrE,gBAAM,KAAK,IAAIA,cAAS,EAAC,oBAAoB,mBAAmB,IAAI,CAAC;AAErE,aAAG,aAAa,IAAI,WAAW;AAC/B,aAAG,aAAa,IAAI,WAAW;AAC/B,aAAG,aAAa,IAAI,WAAW;AAE/B,eAAK,YAAY,IAAIM,MAAAA,SAAS,IAAI,IAAI,EAAE,CAAC;AAAA,QAC1C;AAED,YAAI,QAAQ;AACV,mBAAS,QAAS;AAAA,QACnB;AAAA,MACF;AAAA,IACP,CAAK;AAED,SAAK,MAAO;AAEZ,WAAO;AAAA,EACR;AACH;;"}