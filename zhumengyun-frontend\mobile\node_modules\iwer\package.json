{"name": "iwer", "version": "2.0.1", "description": "Javascript WebXR Runtime for Emulation", "type": "module", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["build", "lib"], "scripts": {"prebuild": "node -p \"'export const VERSION = ' + JSON.stringify(require('./package.json').version) + ';'\" > src/version.ts", "build": "tsc && rollup -c", "doc": "typedoc", "format": "prettier --write ./src/**/*", "test": "jest", "prepublishOnly": "npm run build", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs"}, "repository": {"type": "git", "url": "git+https://github.com/meta-quest/immersive-web-emulation-runtime.git"}, "keywords": [], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/meta-quest/immersive-web-emulation-runtime/issues"}, "homepage": "https://github.com/meta-quest/immersive-web-emulation-runtime#readme", "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@types/gl-matrix": "^3.2.0", "@types/jest": "^29.5.11", "@types/node": "^18.7.13", "@types/three": "^0.149.0", "@types/uuid": "^9.0.8", "@types/webxr": "^0.5.8", "@typescript-eslint/eslint-plugin": "^5.55.0", "@typescript-eslint/parser": "^5.55.0", "eslint": "^8.36.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.0.3", "renamer": "^4.0.0", "rimraf": "^5.0.5", "rollup": "^2.79.1", "ts-jest": "^29.1.1", "typescript": "^4.9.5", "vitepress": "1.0.1"}, "dependencies": {"gl-matrix": "^3.4.3"}}