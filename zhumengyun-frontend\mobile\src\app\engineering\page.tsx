﻿'use client'

import React, { useState, useEffect } from 'react'
import { 中文页面布局, 中文容器, 中文卡片, 中文按钮 } from '../../components/CleanLayout'
import {
  Search,
  MapPin,
  Calendar,
  DollarSign,
  Building,
  Users,
  Clock,
  Star,
  Eye,
  Download,
  ExternalLink
} from 'lucide-react'

export default function 工程页面() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')

  const projects = [
    {
      id: 1,
      title: '杭州市智慧城市基础设施建设项目',
      description: '建设包括5G基站、物联网传感器、智能交通系统等在内的智慧城市基础设施。',
      location: '浙江省杭州市',
      budget: '2.5亿',
      deadline: '2025-12-31',
      status: 'open',
      bidCount: 12,
      viewCount: 256,
      publishDate: '2025-01-10',
      organization: '杭州市发展和改革委员会',
      tags: ['智慧城市', '5G', '物联网', 'AI']
    }
  ]

  return (
    <中文页面布局>
      <中文容器 className="py-6">
        <中文卡片 className="mb-6">
          <div className="space-y-4">
            <div className="中文-搜索">
              <div className="relative">
                <Search className="中文-搜索-图标 w-4 h-4" />
                <input
                  type="text"
                  placeholder="搜索工程项目..."
                  className="中文-搜索-输入框 w-full"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </div>
        </中文卡片>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <中文卡片 className="text-center">
            <Building className="w-8 h-8 text-[#0969da] mx-auto mb-2" />
            <div className="text-2xl font-bold text-[#24292f]">156</div>
            <div className="text-sm text-[#656d76]">活跃项目</div>
          </中文卡片>
        </div>

        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-[#24292f]">项目列表</h2>
          {projects.map((project) => (
            <中文卡片 key={project.id} className="p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-[#24292f] mb-2">
                    <a href="#" className="text-[#0969da] hover:underline">{project.title}</a>
                  </h3>
                  <p className="text-[#656d76] mb-3">{project.description}</p>
                  <div className="flex flex-wrap gap-2 mb-3">
                    {project.tags.map((tag, index) => (
                      <span key={index} className="中文-标签-主要 text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
                <中文按钮 variant="primary">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  查看详情
                </中文按钮>
              </div>
            </中文卡片>
          ))}
        </div>
      </中文容器>
    </中文页面布局>
  )
}
