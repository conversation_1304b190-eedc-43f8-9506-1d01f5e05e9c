{"version": 3, "file": "ExtensionCodec.mjs", "sourceRoot": "", "sources": ["../src/ExtensionCodec.ts"], "names": [], "mappings": "AAAA,kDAAkD;AAElD,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,kBAAkB,EAAE,MAAM,aAAa,CAAC;AAkBjD;IAgBE;QARA,sBAAsB;QACL,oBAAe,GAAgE,EAAE,CAAC;QAClF,oBAAe,GAAgE,EAAE,CAAC;QAEnG,oBAAoB;QACH,aAAQ,GAAgE,EAAE,CAAC;QAC3E,aAAQ,GAAgE,EAAE,CAAC;QAG1F,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;IACpC,CAAC;IAEM,iCAAQ,GAAf,UAAgB,EAQf;YAPC,IAAI,UAAA,EACJ,MAAM,YAAA,EACN,MAAM,YAAA;QAMN,IAAI,IAAI,IAAI,CAAC,EAAE;YACb,oBAAoB;YACpB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;SAC9B;aAAM;YACL,sBAAsB;YACtB,IAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;YACrC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;SACtC;IACH,CAAC;IAEM,oCAAW,GAAlB,UAAmB,MAAe,EAAE,OAAoB;QACtD,sBAAsB;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpD,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1C,IAAI,SAAS,IAAI,IAAI,EAAE;gBACrB,IAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACxC,IAAI,IAAI,IAAI,IAAI,EAAE;oBAChB,IAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBACpB,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;iBAChC;aACF;SACF;QAED,oBAAoB;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,SAAS,IAAI,IAAI,EAAE;gBACrB,IAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACxC,IAAI,IAAI,IAAI,IAAI,EAAE;oBAChB,IAAM,IAAI,GAAG,CAAC,CAAC;oBACf,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;iBAChC;aACF;SACF;QAED,IAAI,MAAM,YAAY,OAAO,EAAE;YAC7B,wBAAwB;YACxB,OAAO,MAAM,CAAC;SACf;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,+BAAM,GAAb,UAAc,IAAgB,EAAE,IAAY,EAAE,OAAoB;QAChE,IAAM,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnF,IAAI,SAAS,EAAE;YACb,OAAO,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;SACvC;aAAM;YACL,mDAAmD;YACnD,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAChC;IACH,CAAC;IAhFsB,2BAAY,GAAkC,IAAI,cAAc,EAAE,CAAC;IAiF5F,qBAAC;CAAA,AAlFD,IAkFC;SAlFY,cAAc"}