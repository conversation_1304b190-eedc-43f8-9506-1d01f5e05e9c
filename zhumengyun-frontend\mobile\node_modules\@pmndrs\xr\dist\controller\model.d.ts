import { Object3D } from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { XRControllerGamepadComponentId } from '../index.js';
import { XRControllerLayout } from './layout.js';
export declare function loadXRControllerModel(layout: XRControllerLayout, loader?: GLTFLoader): Promise<import("three").Group<import("three").Object3DEventMap>>;
/**
 * function for getting the object of a specific component from the xr controller model
 */
export declare function getXRControllerComponentObject(model: Object3D, layout: XRControllerLayout, componentId: XRControllerGamepadComponentId): Object3D<import("three").Object3DEventMap> | undefined;
export type XRControllerModelOptions = {
    /**
     * allows to configure whether the controller is rendered to the color buffer
     * can be used to show the real controller in AR passthrough mode
     */
    colorWrite?: boolean;
    /**
     * allows to configure the render order of the controller model
     * @default undefined
     */
    renderOrder?: number;
};
export declare function configureXRControllerModel(model: Object3D, options?: XRControllerModelOptions): void;
