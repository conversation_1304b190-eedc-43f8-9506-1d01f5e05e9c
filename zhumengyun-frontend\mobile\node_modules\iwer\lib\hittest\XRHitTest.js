/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_HIT_TEST, P_SESSION } from '../private.js';
import { XRRay } from './XRRay.js';
import { XRRigidTransform } from '../primitives/XRRigidTransform.js';
export class XRHitTestSource {
    constructor(session, options) {
        var _a;
        this[P_HIT_TEST] = {
            session,
            space: options.space,
            offsetRay: (_a = options.offsetRay) !== null && _a !== void 0 ? _a : new XRRay(),
        };
    }
    cancel() {
        this[P_HIT_TEST].session[P_SESSION].hitTestSources.delete(this);
    }
}
export class XRHitTestResult {
    constructor(frame, offsetSpace) {
        this[P_HIT_TEST] = { frame, offsetSpace };
    }
    getPose(baseSpace) {
        return this[P_HIT_TEST].frame.getPose(this[P_HIT_TEST].offsetSpace, baseSpace);
    }
    createAnchor() {
        return this[P_HIT_TEST].frame.createAnchor(new XRRigidTransform(), this[P_HIT_TEST].offsetSpace);
    }
}
//# sourceMappingURL=XRHitTest.js.map