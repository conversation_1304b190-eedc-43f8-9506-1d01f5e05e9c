{"version": 3, "file": "BlendShader.cjs", "sources": ["../../src/shaders/BlendShader.ts"], "sourcesContent": ["/**\n * Blend two textures\n */\n\nexport const BlendShader = {\n  uniforms: {\n    tDiffuse1: { value: null },\n    tDiffuse2: { value: null },\n    mixRatio: { value: 0.5 },\n    opacity: { value: 1.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float opacity;\n    uniform float mixRatio;\n\n    uniform sampler2D tDiffuse1;\n    uniform sampler2D tDiffuse2;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 texel1 = texture2D( tDiffuse1, vUv );\n    \tvec4 texel2 = texture2D( tDiffuse2, vUv );\n    \tgl_FragColor = opacity * mix( texel1, texel2, mixRatio );\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";;AAIO,MAAM,cAAc;AAAA,EACzB,UAAU;AAAA,IACR,WAAW,EAAE,OAAO,KAAK;AAAA,IACzB,WAAW,EAAE,OAAO,KAAK;AAAA,IACzB,UAAU,EAAE,OAAO,IAAI;AAAA,IACvB,SAAS,EAAE,OAAO,EAAI;AAAA,EACxB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiB7B;;"}