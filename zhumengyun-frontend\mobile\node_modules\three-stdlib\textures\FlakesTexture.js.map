{"version": 3, "file": "FlakesTexture.js", "sources": ["../../src/textures/FlakesTexture.ts"], "sourcesContent": ["class FlakesTexture {\n  constructor(width = 512, height = 512) {\n    const canvas = document.createElement('canvas')\n    canvas.width = width\n    canvas.height = height\n\n    const context = canvas.getContext('2d') as CanvasRenderingContext2D\n    context.fillStyle = 'rgb(127,127,255)'\n    context.fillRect(0, 0, width, height)\n\n    for (let i = 0; i < 4000; i++) {\n      const x = Math.random() * width\n      const y = Math.random() * height\n      const r = Math.random() * 3 + 3\n\n      let nx = Math.random() * 2 - 1\n      let ny = Math.random() * 2 - 1\n      let nz = 1.5\n\n      const l = Math.sqrt(nx * nx + ny * ny + nz * nz)\n\n      nx /= l\n      ny /= l\n      nz /= l\n\n      context.fillStyle = 'rgb(' + (nx * 127 + 127) + ',' + (ny * 127 + 127) + ',' + nz * 255 + ')'\n      context.beginPath()\n      context.arc(x, y, r, 0, Math.PI * 2)\n      context.fill()\n    }\n\n    return canvas\n  }\n}\n\nexport { FlakesTexture }\n"], "names": [], "mappings": "AAAA,MAAM,cAAc;AAAA,EAClB,YAAY,QAAQ,KAAK,SAAS,KAAK;AAC/B,UAAA,SAAS,SAAS,cAAc,QAAQ;AAC9C,WAAO,QAAQ;AACf,WAAO,SAAS;AAEV,UAAA,UAAU,OAAO,WAAW,IAAI;AACtC,YAAQ,YAAY;AACpB,YAAQ,SAAS,GAAG,GAAG,OAAO,MAAM;AAEpC,aAAS,IAAI,GAAG,IAAI,KAAM,KAAK;AACvB,YAAA,IAAI,KAAK,OAAA,IAAW;AACpB,YAAA,IAAI,KAAK,OAAA,IAAW;AAC1B,YAAM,IAAI,KAAK,OAAO,IAAI,IAAI;AAE9B,UAAI,KAAK,KAAK,OAAO,IAAI,IAAI;AAC7B,UAAI,KAAK,KAAK,OAAO,IAAI,IAAI;AAC7B,UAAI,KAAK;AAEH,YAAA,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAEzC,YAAA;AACA,YAAA;AACA,YAAA;AAEE,cAAA,YAAY,UAAU,KAAK,MAAM,OAAO,OAAO,KAAK,MAAM,OAAO,MAAM,KAAK,MAAM;AAC1F,cAAQ,UAAU;AAClB,cAAQ,IAAI,GAAG,GAAG,GAAG,GAAG,KAAK,KAAK,CAAC;AACnC,cAAQ,KAAK;AAAA,IACf;AAEO,WAAA;AAAA,EACT;AACF;"}