/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { XRDeviceConfig } from '../../XRDevice.js';
export declare const oculusQuest1: XRDeviceConfig;
export declare const metaQuest2: XRDeviceConfig;
export declare const metaQuestPro: XRDeviceConfig;
export declare const metaQuest3: XRDeviceConfig;
//# sourceMappingURL=meta.d.ts.map