/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_HIT_TEST } from '../private.js';
import { XRFrame } from '../frameloop/XRFrame.js';
import { XRPose } from '../pose/XRPose.js';
import { XRRay } from './XRRay.js';
import { XRSession } from '../session/XRSession.js';
import { XRSpace } from '../spaces/XRSpace.js';
export interface XRHitTestOptionsInit {
    space: XRSpace;
    offsetRay: XRRay;
}
export declare class XRHitTestSource {
    [P_HIT_TEST]: {
        session: XRSession;
        space: XRSpace;
        offsetRay: XRRay;
    };
    constructor(session: XRSession, options: XRHitTestOptionsInit);
    cancel(): void;
}
export declare class XRHitTestResult {
    [P_HIT_TEST]: {
        frame: XRFrame;
        offsetSpace: XRSpace;
    };
    constructor(frame: XR<PERSON>rame, offsetSpace: XRSpace);
    getPose(baseSpace: XRSpace): XRPose | undefined;
    createAnchor(): Promise<import("../index.js").XRAnchor>;
}
//# sourceMappingURL=XRHitTest.d.ts.map