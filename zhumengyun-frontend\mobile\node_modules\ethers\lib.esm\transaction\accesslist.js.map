{"version": 3, "file": "accesslist.js", "sourceRoot": "", "sources": ["../../src.ts/transaction/accesslist.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAKhE,SAAS,YAAY,CAAC,IAAY,EAAE,WAA0B;IAC1D,OAAO;QACH,OAAO,EAAE,UAAU,CAAC,IAAI,CAAC;QACzB,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE;YAC/C,cAAc,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,CAAC,EAAE,cAAc,EAAE,eAAgB,KAAM,GAAG,EAAE,UAAU,CAAC,CAAC;YACnG,OAAO,UAAU,CAAC,WAAW,EAAE,CAAC;QACpC,CAAC,CAAC;KACL,CAAC;AACN,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,KAAoB;IAC9C,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACtB,OAA0F,KAAM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YAChH,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACpB,cAAc,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,kBAAkB,EAAE,SAAU,KAAM,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC/E,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;aACtC;YACD,cAAc,CAAC,GAAG,IAAI,IAAI,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,0BAA0B,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YACpG,OAAO,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,WAAW,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;KACN;IAED,cAAc,CAAC,KAAK,IAAI,IAAI,IAAI,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE,qBAAqB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IAEnG,MAAM,MAAM,GAA2D,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QACnG,MAAM,WAAW,GAAyB,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE;YAC/E,KAAK,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;YACzB,OAAO,KAAK,CAAC;QACjB,CAAC,EAAwB,EAAG,CAAC,CAAC;QAC9B,OAAO,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;IAC9D,CAAC,CAAC,CAAC;IACH,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC5D,OAAO,MAAM,CAAC;AAClB,CAAC"}