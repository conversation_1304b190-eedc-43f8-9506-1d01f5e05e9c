@tailwind base;
@tailwind components;
@tailwind utilities;

/* GitHub风格设计系统 */
@layer components {
  /* GitHub颜色变量 */
  :root {
    --github-primary: #0969da;
    --github-primary-hover: #0860ca;
    --github-primary-active: #0757ba;
    --github-success: #1a7f37;
    --github-danger: #cf222e;
    --github-warning: #9a6700;
    --github-bg-primary: #ffffff;
    --github-bg-secondary: #f6f8fa;
    --github-bg-tertiary: #f1f3f4;
    --github-border-primary: #d0d7de;
    --github-border-secondary: #d8dee4;
    --github-text-primary: #24292f;
    --github-text-secondary: #656d76;
    --github-text-tertiary: #8c959f;
    --github-shadow-small: 0 1px 0 rgba(27,31,36,0.04);
    --github-shadow-medium: 0 3px 6px rgba(140,149,159,0.15);
    --github-shadow-large: 0 8px 24px rgba(140,149,159,0.2);
  }

  /* GitHub按钮样式 */
  .github-btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md border transition-all duration-150 ease-in-out;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .github-btn-primary {
    @apply github-btn bg-[#0969da] text-white border-transparent;
    @apply hover:bg-[#0860ca] active:bg-[#0757ba];
    box-shadow: var(--github-shadow-small);
  }

  .github-btn-secondary {
    @apply github-btn bg-[#f6f8fa] text-[#24292f] border-[#d0d7de];
    @apply hover:bg-[#f3f4f6] hover:border-[#d0d7de] active:bg-[#edeff2];
    box-shadow: var(--github-shadow-small);
  }

  .github-btn-outline {
    @apply github-btn bg-transparent text-[#0969da] border-[#0969da];
    @apply hover:bg-[#0969da] hover:text-white active:bg-[#0860ca];
  }

  .github-btn-danger {
    @apply github-btn bg-[#cf222e] text-white border-transparent;
    @apply hover:bg-[#a40e26] active:bg-[#8e1538];
    box-shadow: var(--github-shadow-small);
  }

  .github-btn-sm {
    @apply px-3 py-1 text-xs;
  }

  .github-btn-lg {
    @apply px-6 py-3 text-base;
  }
}

  /* GitHub卡片样式 */
  .github-card {
    @apply bg-white border border-[#d0d7de] rounded-md p-4;
    box-shadow: var(--github-shadow-small);
  }

  .github-card-hover {
    @apply github-card transition-all duration-150 ease-in-out;
    @apply hover:border-[#0969da] hover:shadow-md;
  }

  /* GitHub输入框样式 */
  .github-input {
    @apply w-full px-3 py-2 text-sm border border-[#d0d7de] rounded-md;
    @apply bg-white text-[#24292f] placeholder-[#8c959f];
    @apply focus:outline-none focus:ring-2 focus:ring-[#0969da] focus:border-[#0969da];
    @apply transition-all duration-150 ease-in-out;
    box-shadow: var(--github-shadow-small);
  }

  .github-input:focus {
    box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.1);
  }

  /* GitHub导航样式 */
  .github-nav {
    @apply bg-white border-b border-[#d0d7de];
    box-shadow: var(--github-shadow-small);
  }

  .github-nav-item {
    @apply px-4 py-3 text-sm font-medium text-[#656d76] border-b-2 border-transparent;
    @apply hover:text-[#24292f] hover:border-[#d0d7de] transition-all duration-150 ease-in-out;
  }

  .github-nav-item.active {
    @apply text-[#24292f] border-[#fd8c73];
  }

  /* GitHub标签样式 */
  .github-label {
    @apply inline-flex items-center px-2 py-1 text-xs font-medium rounded-full;
  }

  .github-label-primary {
    @apply github-label bg-[#ddf4ff] text-[#0969da] border border-[#54aeff];
  }

  .github-label-success {
    @apply github-label bg-[#dcffe4] text-[#1a7f37] border border-[#4ac26b];
  }

  .github-label-warning {
    @apply github-label bg-[#fff8c5] text-[#9a6700] border border-[#d4a72c];
  }

  .github-label-danger {
    @apply github-label bg-[#ffebe9] text-[#cf222e] border border-[#ff818a];
  }

/* GitHub基础样式重置 */
@layer base {
  * {
    @apply border-[#d0d7de];
  }

  html {
    @apply scroll-smooth;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  body {
    @apply bg-[#f6f8fa] text-[#24292f];
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    line-height: 1.5;
  }

  /* GitHub输入框优化 */
  input, textarea, select {
    @apply touch-manipulation;
    user-select: text;
    -webkit-user-select: text;
  }

  /* GitHub按钮优化 */
  button {
    @apply touch-manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* GitHub链接优化 */
  a {
    @apply touch-manipulation text-[#0969da] no-underline;
    -webkit-tap-highlight-color: transparent;
  }

  a:hover {
    @apply underline;
  }

  /* GitHub滚动优化 */
  .scroll-smooth {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }
}

/* GitHub组件样式扩展 */
@layer components {
  /* GitHub页面布局 */
  .github-page {
    @apply min-h-screen bg-[#f6f8fa];
  }

  .github-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .github-main {
    @apply bg-white border border-[#d0d7de] rounded-md;
    box-shadow: var(--github-shadow-small);
  }

  /* GitHub头部样式 */
  .github-header {
    @apply bg-[#24292f] text-white;
  }

  .github-header-nav {
    @apply flex items-center space-x-6 px-4 py-3;
  }

  .github-header-link {
    @apply text-white hover:text-[#f0f6fc] text-sm font-medium transition-colors duration-150;
  }

  /* GitHub侧边栏样式 */
  .github-sidebar {
    @apply bg-white border border-[#d0d7de] rounded-md p-4;
    box-shadow: var(--github-shadow-small);
  }

  .github-sidebar-section {
    @apply mb-6 last:mb-0;
  }

  .github-sidebar-title {
    @apply text-sm font-semibold text-[#24292f] mb-3;
  }

  .github-sidebar-item {
    @apply block py-1 text-sm text-[#656d76] hover:text-[#0969da] transition-colors duration-150;
  }

  /* GitHub列表样式 */
  .github-list {
    @apply divide-y divide-[#d0d7de];
  }

  .github-list-item {
    @apply p-4 hover:bg-[#f6f8fa] transition-colors duration-150;
  }

  .github-list-item-title {
    @apply text-base font-medium text-[#24292f] mb-1;
  }

  .github-list-item-meta {
    @apply text-sm text-[#656d76];
  }

  /* GitHub表格样式 */
  .github-table {
    @apply w-full border-collapse;
  }

  .github-table th {
    @apply px-4 py-3 text-left text-xs font-medium text-[#656d76] uppercase tracking-wider;
    @apply bg-[#f6f8fa] border-b border-[#d0d7de];
  }

  .github-table td {
    @apply px-4 py-3 text-sm text-[#24292f] border-b border-[#d0d7de];
  }

  .github-table tr:hover {
    @apply bg-[#f6f8fa];
  }

  /* GitHub代码样式 */
  .github-code {
    @apply bg-[#f6f8fa] border border-[#d0d7de] rounded-md p-3;
    @apply font-mono text-sm text-[#24292f];
  }

  .github-code-inline {
    @apply bg-[#f6f8fa] px-1 py-0.5 rounded text-sm font-mono;
  }

  /* GitHub通知样式 */
  .github-alert {
    @apply p-4 rounded-md border-l-4;
  }

  .github-alert-info {
    @apply github-alert bg-[#ddf4ff] border-[#0969da] text-[#0969da];
  }

  .github-alert-success {
    @apply github-alert bg-[#dcffe4] border-[#1a7f37] text-[#1a7f37];
  }

  .github-alert-warning {
    @apply github-alert bg-[#fff8c5] border-[#9a6700] text-[#9a6700];
  }

  .github-alert-danger {
    @apply github-alert bg-[#ffebe9] border-[#cf222e] text-[#cf222e];
  }

  /* GitHub头像样式 */
  .github-avatar {
    @apply rounded-full border border-[#d0d7de] bg-white;
  }

  .github-avatar-sm {
    @apply github-avatar w-6 h-6;
  }

  .github-avatar-md {
    @apply github-avatar w-8 h-8;
  }

  .github-avatar-lg {
    @apply github-avatar w-12 h-12;
  }

  .github-avatar-xl {
    @apply github-avatar w-16 h-16;
  }

  /* GitHub徽章样式 */
  .github-counter {
    @apply inline-flex items-center px-2 py-1 text-xs font-medium rounded-full;
    @apply bg-[#d0d7de] text-[#24292f];
  }

  /* GitHub分页样式 */
  .github-pagination {
    @apply flex items-center justify-center space-x-1;
  }

  .github-pagination-item {
    @apply px-3 py-2 text-sm border border-[#d0d7de] bg-white text-[#24292f];
    @apply hover:bg-[#f6f8fa] hover:border-[#0969da] transition-all duration-150;
  }

  .github-pagination-item.active {
    @apply bg-[#0969da] text-white border-[#0969da];
  }

  /* GitHub搜索样式 */
  .github-search {
    @apply relative;
  }

  .github-search-input {
    @apply github-input pl-10;
  }

  .github-search-icon {
    @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8c959f];
  }
}

/* GitHub工具类 */
@layer utilities {
  /* 文本截断 */
  .text-ellipsis-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* GitHub特殊效果 */
  .github-glass {
    @apply bg-white bg-opacity-90 backdrop-blur-sm border border-[#d0d7de];
    box-shadow: var(--github-shadow-medium);
  }

  .github-gradient {
    background: linear-gradient(135deg, #0969da, #0860ca);
  }

  .github-gradient-success {
    background: linear-gradient(135deg, #1a7f37, #2da44e);
  }

  /* GitHub阴影效果 */
  .github-shadow-sm {
    box-shadow: var(--github-shadow-small);
  }

  .github-shadow-md {
    box-shadow: var(--github-shadow-medium);
  }

  .github-shadow-lg {
    box-shadow: var(--github-shadow-large);
  }

  /* GitHub动画延迟 */
  .animate-delay-100 {
    animation-delay: 100ms;
  }

  .animate-delay-200 {
    animation-delay: 200ms;
  }

  .animate-delay-300 {
    animation-delay: 300ms;
  }

  .animate-delay-500 {
    animation-delay: 500ms;
  }

  /* GitHub响应式隐藏 */
  .github-mobile-only {
    @apply block sm:hidden;
  }

  .github-desktop-only {
    @apply hidden sm:block;
  }
}

/* GitHub滚动条样式 */
.github-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #d0d7de #f6f8fa;
}

.github-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.github-scrollbar::-webkit-scrollbar-track {
  background: #f6f8fa;
  border-radius: 4px;
}

.github-scrollbar::-webkit-scrollbar-thumb {
  background: #d0d7de;
  border-radius: 4px;
}

.github-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #8c959f;
}

/* GitHub选择样式 */
.github-select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* GitHub触摸优化 */
.github-touch {
  touch-action: manipulation;
}

/* GitHub焦点样式 */
.github-focus:focus-visible {
  outline: 2px solid #0969da;
  outline-offset: 2px;
}

/* GitHub动画效果 */
@keyframes github-fade-in {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes github-slide-in {
  from {
    opacity: 0;
    transform: translateX(-16px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes github-scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes github-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes github-loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes github-bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* GitHub动画类 */
.github-animate-fade-in {
  animation: github-fade-in 0.3s ease-out forwards;
}

.github-animate-slide-in {
  animation: github-slide-in 0.4s ease-out forwards;
}

.github-animate-scale-in {
  animation: github-scale-in 0.2s ease-out forwards;
}

.github-animate-pulse {
  animation: github-pulse 2s ease-in-out infinite;
}

.github-animate-loading {
  animation: github-loading 1s linear infinite;
}

.github-animate-bounce {
  animation: github-bounce 1s ease-in-out;
}

/* GitHub文字效果 */
.github-text-gradient {
  background: linear-gradient(135deg, #0969da, #2ea043);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* GitHub卡片悬停效果 */
.github-card-hover {
  transition: all 0.15s ease-in-out;
}

.github-card-hover:hover {
  transform: translateY(-2px);
  box-shadow: var(--github-shadow-large);
}

/* GitHub按钮悬停效果 */
.github-btn-hover {
  position: relative;
  overflow: hidden;
  transition: all 0.15s ease-in-out;
}

.github-btn-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.github-btn-hover:hover::before {
  left: 100%;
}

/* GitHub加载状态 */
.github-loading-skeleton {
  background: linear-gradient(90deg, #f6f8fa 25%, #e1e4e8 50%, #f6f8fa 75%);
  background-size: 200% 100%;
  animation: github-loading 1.5s infinite;
}

/* GitHub响应式工具 */
@media (max-width: 768px) {
  .github-mobile-stack {
    @apply flex-col space-y-4 space-x-0;
  }

  .github-mobile-full {
    @apply w-full;
  }
}


