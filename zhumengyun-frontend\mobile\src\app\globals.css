@tailwind base;
@tailwind components;
@tailwind utilities;

/* 中文移动端设计系统 */
@layer components {
  /* 中文按钮样式 */
  .中文-按钮 {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md border transition-all duration-150 ease-in-out;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .中文-按钮-主要 {
    @apply 中文-按钮 bg-[#0969da] text-white border-transparent;
    @apply hover:bg-[#0860ca] active:bg-[#0757ba];
  }

  .中文-按钮-次要 {
    @apply 中文-按钮 bg-[#f6f8fa] text-[#24292f] border-[#d0d7de];
    @apply hover:bg-[#f3f4f6] hover:border-[#d0d7de] active:bg-[#edeff2];
  }

  /* 中文卡片样式 */
  .中文-卡片 {
    @apply bg-white border border-[#d0d7de] rounded-md p-4;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .中文-卡片-悬停 {
    @apply 中文-卡片 transition-all duration-150 ease-in-out;
    @apply hover:border-[#0969da] hover:shadow-md;
  }

  /* 中文标签样式 */
  .中文-标签-主要 {
    @apply inline-flex items-center px-2 py-1 text-xs font-medium rounded-full;
    @apply bg-[#ddf4ff] text-[#0969da] border border-[#54aeff];
  }

  /* 中文搜索样式 */
  .中文-搜索 {
    @apply relative;
  }

  .中文-搜索-图标 {
    @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-[#656d76];
  }

  .中文-搜索-输入框 {
    @apply pl-10 pr-4 py-2 text-sm border border-[#d0d7de] rounded-md w-full;
    @apply bg-white text-[#24292f] placeholder-[#8c959f];
    @apply focus:outline-none focus:ring-2 focus:ring-[#0969da] focus:border-[#0969da];
    @apply transition-all duration-150 ease-in-out;
  }

  /* 中文页面布局样式 */
  .中文-页面 {
    @apply min-h-screen bg-[#f6f8fa];
  }

  .中文-容器 {
    @apply max-w-md mx-auto px-4;
  }

  /* 中文底部导航样式 */
  .中文-底部导航 {
    @apply fixed bottom-0 left-0 right-0 bg-white border-t border-[#d0d7de] z-50;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  }

  .中文-底部导航-容器 {
    @apply flex items-center justify-around py-2 px-2;
  }

  .中文-底部导航-项目 {
    @apply flex flex-col items-center space-y-1 py-2 px-2 text-xs flex-1;
    @apply text-[#656d76] transition-colors;
  }

  .中文-底部导航-项目.active {
    @apply text-[#0969da];
  }

  /* 中文工具类 */
  .text-ellipsis-1 {
    @apply truncate;
  }

  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* 中文基础样式重置 */
@layer base {
  body {
    @apply bg-[#f6f8fa] text-[#24292f];
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif;
  }

  a {
    @apply text-[#0969da] no-underline;
  }

  a:hover {
    @apply underline;
  }
}
