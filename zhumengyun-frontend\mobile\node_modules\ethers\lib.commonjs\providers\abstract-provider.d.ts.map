{"version": 3, "file": "abstract-provider.d.ts", "sourceRoot": "", "sources": ["../../src.ts/providers/abstract-provider.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAeH,OAAO,EAIH,YAAY,EAIf,MAAM,mBAAmB,CAAC;AAE3B,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAIhD,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAe,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,MAAM,eAAe,CAAC;AAM1G,OAAO,KAAK,EAAe,WAAW,EAAE,MAAM,qBAAqB,CAAC;AACpE,OAAO,KAAK,EAAE,YAAY,EAAa,MAAM,mBAAmB,CAAC;AACjE,OAAO,KAAK,EAAiB,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAEjE,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAG/C,OAAO,KAAK,EACR,WAAW,EAAE,SAAS,EAAE,wBAAwB,EAChD,yBAAyB,EAC5B,MAAM,iBAAiB,CAAC;AAEzB,OAAO,KAAK,EACR,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,iBAAiB,EAAE,YAAY,EAC9D,0BAA0B,EAAE,QAAQ,EAAE,aAAa,EACnD,kBAAkB,EACrB,MAAM,eAAe,CAAC;AAkCvB;;;GAGG;AACH,MAAM,MAAM,0BAA0B,GAAG;IACrC,MAAM,EAAE,0BAA0B,CAAC;IACnC,OAAO,EAAE,YAAY,CAAA;IACrB,KAAK,EAAE,MAAM,CAAA;IACb,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;CACtB,GAAG;IACA,MAAM,EAAE,4BAA4B,CAAC;IACrC,OAAO,EAAE,YAAY,CAAC;IACtB,MAAM,EAAE,GAAG,CAAA;CACd,GAAG;IACA,MAAM,EAAE,2BAA2B,CAAC;IACpC,OAAO,EAAE,YAAY,CAAC;IACtB,MAAM,EAAE,GAAG,CAAA;CACd,GAAG;IACA,MAAM,EAAE,kBAAkB,CAAC;IAC3B,WAAW,EAAE;QAAE,EAAE,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,MAAM,CAAA;KAAE,CAAA;CAC5C,GAAG;IACA,MAAM,EAAE,2BAA2B,CAAC;IACpC,WAAW,EAAE;QAAE,EAAE,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,MAAM,CAAA;KAAE,CAAA;IACzC,MAAM,EAAE,MAAM,CAAA;CACjB,GAAG;IACA,MAAM,EAAE,0BAA0B,CAAC;IACnC,WAAW,EAAE;QAAE,EAAE,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,MAAM,CAAA;KAAE,CAAA;IACzC,KAAK,EAAE,KAAK,CAAA;CACf,CAAC;AAGF;;;;;GAKG;AACH,MAAM,MAAM,YAAY,GAAG;IACvB,IAAI,EAAE,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,WAAW,GAAG,SAAS,GAAG,SAAS,GAAG,MAAM,CAAC;IAC3F,GAAG,EAAE,MAAM,CAAA;CACd,GAAG;IACA,IAAI,EAAE,aAAa,CAAC;IACpB,GAAG,EAAE,MAAM,CAAC;IACZ,IAAI,EAAE,MAAM,CAAA;CACf,GAAG;IACA,IAAI,EAAE,OAAO,CAAC;IACd,GAAG,EAAE,MAAM,CAAC;IACZ,MAAM,EAAE,WAAW,CAAA;CACtB,GAAG;IACA,IAAI,EAAE,QAAQ,CAAC;IACf,GAAG,EAAE,MAAM,CAAC;IACZ,MAAM,EAAE,YAAY,CAAA;CACvB,CAAC;AAEF;;;;;GAKG;AACH,MAAM,WAAW,UAAU;IACvB;;OAEG;IACH,KAAK,IAAI,IAAI,CAAC;IAEd;;OAEG;IACH,IAAI,IAAI,IAAI,CAAC;IAEb;;;;;OAKG;IACH,KAAK,CAAC,eAAe,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAEvC;;OAEG;IACH,MAAM,IAAI,IAAI,CAAC;IAEf;;;;;OAKG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;CAC5B;AAED;;;;GAIG;AACH,qBAAa,mBAAoB,YAAW,UAAU;IAClD;;OAEG;IACH,IAAI,EAAG,MAAM,CAAC;IAEd;;OAEG;gBACS,IAAI,EAAE,MAAM;IAExB,KAAK,IAAI,IAAI;IACb,IAAI,IAAI,IAAI;IAEZ,KAAK,CAAC,eAAe,CAAC,EAAE,OAAO,GAAG,IAAI;IACtC,MAAM,IAAI,IAAI;CACjB;AAmGD;;;;GAIG;AACH,MAAM,WAAW,sBAAsB;IACnC;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,OAAO,CAAC,QAAQ,EAAE,gBAAgB,GAAG,sBAAsB,CAAC;CAC/D;AAED;;GAEG;AACH,MAAM,MAAM,mBAAmB,GAAG;IAC9B,OAAO,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;IACjC,MAAM,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAC9C,SAAS,CAAC,EAAE,QAAQ,CAAC;IACrB,OAAO,CAAC,EAAE,QAAQ,CAAC;CACtB,GAAG;IACA,OAAO,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;IACjC,MAAM,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAC9C,SAAS,CAAC,EAAE,MAAM,CAAC;CACtB,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,wBAAyB,SAAQ,0BAA0B;IACxE;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;CACjB;AAED;;;GAGG;AACH,MAAM,MAAM,oBAAoB,GAAG;IAC/B,MAAM,EAAE,sBAAsB,CAAC;IAC/B,iBAAiB,EAAE,MAAM,CAAA;CAC5B,GAAG;IACA,MAAM,EAAE,MAAM,CAAC;IACf,WAAW,EAAE,wBAAwB,CAAC;IAAC,QAAQ,EAAE,QAAQ,CAAA;CAC5D,GAAG;IACA,MAAM,EAAE,SAAS,CAAA;CACpB,GAAG;IACA,MAAM,EAAE,aAAa,CAAC;IACtB,WAAW,EAAE,wBAAwB,CAAA;CACxC,GAAG;IACA,MAAM,EAAE,YAAY,CAAC;IACrB,OAAO,EAAE,MAAM,CAAC;IAAC,QAAQ,EAAE,QAAQ,CAAA;CACtC,GAAG;IACA,MAAM,EAAE,UAAU,CAAC;IACnB,QAAQ,EAAE,QAAQ,CAAC;IAAC,mBAAmB,EAAE,OAAO,CAAA;CACnD,GAAG;IACA,MAAM,EAAE,UAAU,CAAC;IACnB,SAAS,EAAE,MAAM,CAAC;IAAC,mBAAmB,EAAE,OAAO,CAAA;CAClD,GAAG;IACA,MAAM,EAAE,gBAAgB,CAAA;CAC3B,GAAG;IACA,MAAM,EAAE,SAAS,CAAC;IAClB,OAAO,EAAE,MAAM,CAAC;IAAC,QAAQ,EAAE,QAAQ,CAAA;CACtC,GAAG;IACA,MAAM,EAAE,aAAa,CAAA;CACxB,GAAG;IACA,MAAM,EAAE,SAAS,CAAC;IAClB,MAAM,EAAE,mBAAmB,CAAA;CAC9B,GAAG;IACA,MAAM,EAAE,gBAAgB,CAAA;CAC3B,GAAG;IACA,MAAM,EAAE,YAAY,CAAC;IACrB,OAAO,EAAE,MAAM,CAAC;IAAC,QAAQ,EAAE,MAAM,CAAC;IAAC,QAAQ,EAAE,QAAQ,CAAA;CACxD,GAAG;IACA,MAAM,EAAE,gBAAgB,CAAC;IACzB,IAAI,EAAE,MAAM,CAAA;CACf,GAAG;IACA,MAAM,EAAE,qBAAqB,CAAC;IAC9B,OAAO,EAAE,MAAM,CAAC;IAAC,QAAQ,EAAE,QAAQ,CAAA;CACtC,GAAG;IACA,MAAM,EAAE,uBAAuB,CAAC;IAChC,IAAI,EAAE,MAAM,CAAA;CACf,GAAG;IACA,MAAM,EAAE,sBAAsB,CAAC;IAC/B,IAAI,EAAE,MAAM,CAAA;CACf,CAAC;AAQF;;;;;;;;;GASG;AACH,MAAM,MAAM,uBAAuB,GAAG;IAClC,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,eAAe,CAAC,EAAE,MAAM,CAAC;CAC5B,CAAC;AAgBF;;;;;GAKG;AACH,qBAAa,gBAAiB,YAAW,QAAQ;;IAyB7C;;;;OAIG;gBACS,QAAQ,CAAC,EAAE,KAAK,GAAG,UAAU,EAAE,OAAO,CAAC,EAAE,uBAAuB;IAgC5E,IAAI,eAAe,IAAI,MAAM,CAA0C;IAEvE;;;OAGG;IACH,IAAI,QAAQ,IAAI,IAAI,CAAiB;IAErC;;OAEG;IACH,IAAI,OAAO,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAE3C;IAED;;OAEG;IACH,YAAY,CAAC,MAAM,EAAE,sBAAsB,GAAG,IAAI;IAQlD;;OAEG;IACH,SAAS,CAAC,CAAC,SAAS,sBAAsB,GAAG,sBAAsB,EAAE,IAAI,EAAE,MAAM,GAAG,IAAI,GAAG,CAAC;IAI5F;;;OAGG;IACH,IAAI,eAAe,IAAI,OAAO,CAAkC;IAChE,IAAI,eAAe,CAAC,KAAK,EAAE,OAAO,EAAsC;IA4BxE;;OAEG;IACG,aAAa,CAAC,EAAE,EAAE,wBAAwB,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IAkEhH;;;;OAIG;IACH,UAAU,CAAC,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,GAAG,KAAK;IAIvD;;;;OAIG;IACH,QAAQ,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,GAAG,GAAG;IAIjD;;;;OAIG;IACH,uBAAuB,CAAC,KAAK,EAAE,wBAAwB,EAAE,OAAO,EAAE,OAAO,GAAG,kBAAkB;IAI9F;;;;OAIG;IACH,wBAAwB,CAAC,EAAE,EAAE,yBAAyB,EAAE,OAAO,EAAE,OAAO,GAAG,mBAAmB;IAI9F;;;;;OAKG;IACH,cAAc,IAAI,OAAO,CAAC,OAAO,CAAC;IAMlC;;;;;OAKG;IACG,QAAQ,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,oBAAoB,GAAG,OAAO,CAAC,CAAC,CAAC;IASxD,cAAc,IAAI,OAAO,CAAC,MAAM,CAAC;IAMvC;;;;OAIG;IACH,WAAW,CAAC,OAAO,EAAE,WAAW,GAAG,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAI3D;;;OAGG;IACH,YAAY,CAAC,QAAQ,CAAC,EAAE,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAgC3D;;;;OAIG;IACH,UAAU,CAAC,MAAM,EAAE,MAAM,GAAG,iBAAiB,GAAG,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC;IAqElG;;;;OAIG;IACH,sBAAsB,CAAC,QAAQ,EAAE,kBAAkB,GAAG,wBAAwB,GAAG,OAAO,CAAC,wBAAwB,CAAC;IAkC5G,UAAU,IAAI,OAAO,CAAC,OAAO,CAAC;IAkD9B,UAAU,IAAI,OAAO,CAAC,OAAO,CAAC;IA+C9B,WAAW,CAAC,GAAG,EAAE,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC;IAmFrD,IAAI,CAAC,GAAG,EAAE,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC;IAqB9C,UAAU,CAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;IAItE,mBAAmB,CAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;IAI/E,OAAO,CAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;IAInE,UAAU,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;IAM/F,oBAAoB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,mBAAmB,CAAC;IAoCpE,QAAQ,CAAC,KAAK,EAAE,QAAQ,GAAG,MAAM,EAAE,WAAW,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC;IAUhF,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,mBAAmB,CAAC;IAUjE,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,kBAAkB,CAAC;IAkBvE,oBAAoB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IAU1D,OAAO,CAAC,OAAO,EAAE,MAAM,GAAG,iBAAiB,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAavE,YAAY,CAAC,OAAO,EAAE,MAAM,GAAG,gBAAgB;IAMzC,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC;IAItD,SAAS,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IAM/C,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IAMjD,aAAa,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IAwCtD,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,IAAI,GAAG,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,kBAAkB,CAAC;IAwCxH,YAAY,CAAC,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC;IAMvD;;OAEG;IACH,aAAa,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAOpC;;;;;;;OAOG;IACH,WAAW,CAAC,KAAK,EAAE,MAAM,IAAI,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM;IAkBxD;;OAEG;IACH,kBAAkB,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,UAAU,KAAK,IAAI,GAAG,IAAI;IAMvD;;;OAGG;IACH,cAAc,CAAC,GAAG,EAAE,YAAY,GAAG,UAAU;IAwB7C;;;;;;;;OAQG;IACH,kBAAkB,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,GAAG,IAAI;IAyC1D,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAW3D,IAAI,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAW7D,IAAI,CAAC,KAAK,EAAE,aAAa,EAAE,GAAG,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;IAuBjE,aAAa,CAAC,KAAK,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC;IAcrD,SAAS,CAAC,KAAK,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAa1D,GAAG,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAiB7D,kBAAkB,CAAC,KAAK,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC;IAexD,WAAW,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAKpE,cAAc,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAI7E;;;;;;OAMG;IACH,IAAI,SAAS,IAAI,OAAO,CAEvB;IAED;;;;;OAKG;IACH,OAAO,IAAI,IAAI;IAYf;;;;;;;;;;OAUG;IACH,IAAI,MAAM,IAAI,OAAO,CAAwC;IAC7D,IAAI,MAAM,CAAC,KAAK,EAAE,OAAO,EAQxB;IAED;;;;OAIG;IACH,KAAK,CAAC,eAAe,CAAC,EAAE,OAAO,GAAG,IAAI;IAsBtC;;OAEG;IACH,MAAM,IAAI,IAAI;CAiBjB"}