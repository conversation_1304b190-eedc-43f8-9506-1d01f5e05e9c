'use client'

import React from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { 
  Home, 
  Search, 
  Plus, 
  MessageSquare, 
  User,
  Code,
  Compass,
  PlusCircle,
  Bell,
  Settings
} from 'lucide-react'

interface NavItem {
  id: string
  label: string
  icon: React.ReactNode
  path: string
  badge?: number
}

export default function 中文底部导航() {
  const pathname = usePathname()
  const router = useRouter()

  const navItems: NavItem[] = [
    {
      id: 'home',
      label: 'Home',
      icon: <Home className="w-5 h-5" />,
      path: '/'
    },
    {
      id: 'engineering',
      label: 'Discover',
      icon: <Compass className="w-5 h-5" />,
      path: '/engineering'
    },
    {
      id: 'creator',
      label: 'Create',
      icon: <PlusCircle className="w-5 h-5" />,
      path: '/creator'
    },
    {
      id: 'social',
      label: 'Messages',
      icon: <MessageSquare className="w-5 h-5" />,
      path: '/social',
      badge: 3
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: <User className="w-5 h-5" />,
      path: '/profile'
    }
  ]

  const handleNavigation = (path: string) => {
    router.push(path)
  }

  return (
    <>
      {/* Desktop Navigation - Hidden on mobile */}
      <nav className="github-desktop-only fixed top-0 left-0 right-0 z-50 github-nav">
        <div className="github-container">
          <div className="flex items-center justify-between py-3">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Code className="w-6 h-6 text-[#24292f]" />
                <span className="text-lg font-bold text-[#24292f]">NextGen 2025</span>
              </div>
              <div className="flex items-center space-x-4">
                {navItems.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => handleNavigation(item.path)}
                    className={`github-nav-item ${pathname === item.path ? 'active' : ''}`}
                  >
                    {item.label}
                  </button>
                ))}
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <button className="relative p-2 text-[#656d76] hover:text-[#24292f] transition-colors">
                <Bell className="w-5 h-5" />
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-[#cf222e] rounded-full text-xs text-white flex items-center justify-center">
                  2
                </span>
              </button>
              <button className="p-2 text-[#656d76] hover:text-[#24292f] transition-colors">
                <Settings className="w-5 h-5" />
              </button>
              <div className="github-avatar-md">
                <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-medium text-sm">
                  U
                </div>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Bottom Navigation */}
      <nav className="github-mobile-only fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-[#d0d7de]">
        <div className="flex items-center justify-around py-2">
          {navItems.map((item) => {
            const isActive = pathname === item.path
            
            return (
              <button
                key={item.id}
                onClick={() => handleNavigation(item.path)}
                className={`
                  flex flex-col items-center justify-center p-2 rounded-lg transition-all duration-150 ease-in-out
                  ${isActive 
                    ? 'text-[#0969da] bg-[#ddf4ff]' 
                    : 'text-[#656d76] hover:text-[#24292f] hover:bg-[#f6f8fa]'
                  }
                `}
              >
                <div className="relative">
                  {item.icon}
                  {item.badge && (
                    <span className="absolute -top-2 -right-2 w-4 h-4 bg-[#cf222e] rounded-full text-xs text-white flex items-center justify-center">
                      {item.badge}
                    </span>
                  )}
                </div>
                <span className="text-xs font-medium mt-1">{item.label}</span>
              </button>
            )
          })}
        </div>
      </nav>

      {/* Spacer for mobile navigation */}
      <div className="github-mobile-only h-16" />
    </>
  )
}

// GitHub桌面版风格的页面布局组件
export function 中文页面布局({
  children,
  title,
  showNavigation = false
}: {
  children: React.ReactNode
  title?: string
  showNavigation?: boolean
}) {
  return (
    <div className="github-desktop-layout">
      {/* GitHub顶部导航 */}
      <header className="github-header">
        <div className="github-header-nav">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Code className="w-6 h-6 text-white" />
              <span className="text-lg font-semibold text-white">NextGen 2025</span>
            </div>
            <nav className="flex items-center space-x-1">
              <a href="/" className="github-nav-tab active">Home</a>
              <a href="/engineering" className="github-nav-tab">Discover</a>
              <a href="/creator" className="github-nav-tab">Create</a>
              <a href="/social" className="github-nav-tab">Messages</a>
              <a href="/profile" className="github-nav-tab">Profile</a>
            </nav>
          </div>

          <div className="flex items-center space-x-4">
            <div className="github-search-desktop">
              <Search className="github-search-icon w-4 h-4" />
              <input
                type="text"
                placeholder="搜索或跳转到..."
                className="github-search-input-desktop"
              />
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="github-main-content">
        {children}
      </main>
    </div>
  )
}

// 中文风格的容器组件
export function 中文容器({
  children,
  className = ''
}: {
  children: React.ReactNode
  className?: string
}) {
  return (
    <div className={`中文-容器 ${className}`}>
      {children}
    </div>
  )
}

// 中文风格的卡片组件
export function 中文卡片({
  children,
  hover = false,
  className = ''
}: {
  children: React.ReactNode
  hover?: boolean
  className?: string
}) {
  return (
    <div className={`中文-卡片 ${hover ? '中文-卡片-悬停' : ''} ${className}`}>
      {children}
    </div>
  )
}

// 中文风格的按钮组件
export function 中文按钮({
  children,
  variant = 'secondary',
  size = 'md',
  onClick,
  disabled = false,
  className = ''
}: {
  children: React.ReactNode
  variant?: 'primary' | 'secondary' | 'outline' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  onClick?: () => void
  disabled?: boolean
  className?: string
}) {
  const baseClass = '中文-按钮'
  const variantClass = `中文-按钮-${variant === 'primary' ? '主要' : variant === 'secondary' ? '次要' : variant === 'outline' ? '轮廓' : '危险'}`
  const sizeClass = size !== 'md' ? `中文-按钮-${size === 'sm' ? '小' : '大'}` : ''

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClass} ${variantClass} ${sizeClass} ${className}`}
    >
      {children}
    </button>
  )
}
