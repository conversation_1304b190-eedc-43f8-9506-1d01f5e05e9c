{"version": 3, "file": "XRPlane.js", "sourceRoot": "", "sources": ["../../src/planes/XRPlane.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAGxC,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAGvD,MAAM,CAAN,IAAY,kBAGX;AAHD,WAAY,kBAAkB;IAC7B,+CAAyB,CAAA;IACzB,2CAAqB,CAAA;AACtB,CAAC,EAHW,kBAAkB,KAAlB,kBAAkB,QAG7B;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAE5B;IACH,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,kBAAkB,CAAC,UAAU;IACtD,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,kBAAkB,CAAC,UAAU;IACvD,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,kBAAkB,CAAC,UAAU;IACvD,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,kBAAkB,CAAC,UAAU;IACzD,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,kBAAkB,CAAC,QAAQ;IACpD,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,kBAAkB,CAAC,QAAQ;IACpD,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,kBAAkB,CAAC,QAAQ;IACtD,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,kBAAkB,CAAC,UAAU;IACvD,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,kBAAkB,CAAC,UAAU;IACvD,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU;IACrD,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,kBAAkB,CAAC,UAAU;IACxD,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,kBAAkB,CAAC,UAAU;IACtD,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,kBAAkB,CAAC,UAAU;IACvD,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,kBAAkB,CAAC,QAAQ;CACvD,CAAC;AAEF,MAAM,OAAO,OAAO;IAWnB,YACC,WAAwB,EACxB,UAAmB,EACnB,OAA2B,EAC3B,aAAgC;QAEhC,IAAI,CAAC,OAAO,CAAC,GAAG;YACf,WAAW;YACX,KAAK,EAAE,SAAU;YACjB,UAAU;YACV,OAAO;YACP,eAAe,EAAE,WAAW,CAAC,GAAG,EAAE;YAClC,aAAa;YACb,WAAW,EAAE,aAAa;gBACzB,CAAC,CAAC,mBAAmB,CAAC,aAAa,CAAC;gBACpC,CAAC,CAAC,SAAS;SACZ,CAAC;IACH,CAAC;IAED,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC;IACjC,CAAC;IAED,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;IAC9B,CAAC;IAED,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC;IAClC,CAAC;IAED,IAAI,eAAe;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC;IACtC,CAAC;IAED,IAAI,aAAa;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC;IACpC,CAAC;CACD;AAED,MAAM,OAAO,UAAW,SAAQ,GAAY;CAAG;AAE/C,MAAM,OAAO,WAAW;IACvB,YACQ,SAA2B,EAC3B,OAA2B,EAC3B,aAA+B;QAF/B,cAAS,GAAT,SAAS,CAAkB;QAC3B,YAAO,GAAP,OAAO,CAAoB;QAC3B,kBAAa,GAAb,aAAa,CAAkB;IACpC,CAAC;CACJ"}