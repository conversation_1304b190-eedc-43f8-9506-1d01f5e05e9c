{"version": 3, "file": "XRTrackedInput.js", "sourceRoot": "", "sources": ["../../src/device/XRTrackedInput.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,eAAe,CAAC;AACpE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,kBAAkB,CAAC;AACvD,OAAO,EAAE,YAAY,EAAiB,MAAM,2BAA2B,CAAC;AAExE,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAEtD,OAAO,EAAE,kBAAkB,EAAE,MAAM,iCAAiC,CAAC;AACrE,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAEjC,MAAM,iBAAiB,GAAG;IACzB,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;QACpB,QAAQ,EAAE,IAAI,OAAO,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;QACvC,UAAU,EAAE,IAAI,UAAU,EAAE;KAC5B;IACD,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;QACrB,QAAQ,EAAE,IAAI,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;QACtC,UAAU,EAAE,IAAI,UAAU,EAAE;KAC5B;IACD,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;QACpB,QAAQ,EAAE,IAAI,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;QACtC,UAAU,EAAE,IAAI,UAAU,EAAE;KAC5B;CACD,CAAC;AAEF,MAAM,OAAO,cAAc;IAW1B,YAAY,WAA0B;QACrC,IAAI,CAAC,eAAe,CAAC,GAAG;YACvB,WAAW;YACX,QAAQ,EAAE,iBAAiB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE;YACpE,UAAU,EAAE,iBAAiB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE;YACxE,SAAS,EAAE,IAAI;YACf,kBAAkB,EAAE,KAAK;YACzB,kBAAkB,EAAE,IAAI;SACxB,CAAC;IACH,CAAC;IAED,IAAI,QAAQ;QACX,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC;IACvC,CAAC;IAED,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,UAAU,CAAC;IACzC,CAAC;IAED,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC;IAC1C,CAAC;IAED,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC;IACxC,CAAC;IAED,IAAI,SAAS,CAAC,KAAc;QAC3B,IAAI,CAAC,eAAe,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;QACxC,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,OAAQ,CAAC,SAAS,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;IACzE,CAAC;IAED,YAAY,CAAC,KAAc;QAC1B,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC;QACxE,IAAI,CAAC,uBAAuB,CAC3B,cAAc,CAAC,OAAO,CAAC,CAAC,YAAY,EACpC,IAAI,CAAC,eAAe,CAAC,CAAC,UAAU,CAAC,IAAI,EACrC,IAAI,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,IAAI,CACnC,CAAC;QAEF,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,OAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACrE,IAAI,MAAM,YAAY,aAAa,EAAE;gBACpC,oDAAoD;gBACpD,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC;gBAC3D,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,IAAI,IAAI,EAAE;oBAC3C,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC;oBACzD,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC;iBACtC;gBACD,8BAA8B;gBAC9B,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,IAAI,IAAI,EAAE;oBAC3C,IACC,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,KAAK,CAAC;wBACtC,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,GAAG,CAAC,EAC1B;wBACD,OAAO,CAAC,aAAa,CACpB,IAAI,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,EAAE;4BACtD,KAAK;4BACL,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW;yBAC9C,CAAC,CACF,CAAC;wBACF,OAAO,CAAC,aAAa,CACpB,IAAI,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,GAAG,OAAO,EAAE;4BAChE,KAAK;4BACL,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW;yBAC9C,CAAC,CACF,CAAC;qBACF;yBAAM,IACN,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,GAAG,CAAC;wBACpC,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,KAAK,CAAC,EAC5B;wBACD,OAAO,CAAC,aAAa,CACpB,IAAI,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,YAAY,GAAG,KAAK,EAAE;4BAC9D,KAAK;4BACL,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW;yBAC9C,CAAC,CACF,CAAC;qBACF;iBACD;aACD;QACF,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,CAAC,CAAC,kBAAkB;YACvC,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC,kBAAkB,CAAC;QAC7D,IAAI,CAAC,eAAe,CAAC,CAAC,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC;IAC3D,CAAC;CACD"}