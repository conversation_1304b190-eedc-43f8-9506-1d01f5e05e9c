import { XRState as BaseXRState, XRStore as BaseXRStore, XRStoreOptions as BaseXRStoreOptions, DefaultXRControllerOptions, DefaultXRGazeOptions, DefaultXRHandOptions, DefaultXRScreenInputOptions, DefaultXRTransientPointerOptions } from '@pmndrs/xr/internals';
import { ComponentType, ReactNode } from 'react';
type XRElementImplementation = {
    /**
     * @default true
     */
    hand: ComponentType | boolean | DefaultXRHandOptions;
    /**
     * @default true
     */
    controller: ComponentType | boolean | DefaultXRControllerOptions;
    /**
     * @default true
     */
    transientPointer: ComponentType | boolean | DefaultXRTransientPointerOptions;
    /**
     * @default true
     */
    gaze: ComponentType | boolean | DefaultXRGazeOptions;
    /**
     * @default true
     */
    screenInput: ComponentType | boolean | DefaultXRScreenInputOptions;
    /**
     * @default false
     */
    detectedMesh: ComponentType | false;
    /**
     * @default false
     */
    detectedPlane: ComponentType | false;
};
export type XRStore = BaseXRStore<XRElementImplementation>;
export type XRStoreOptions = BaseXRStoreOptions<XRElementImplementation>;
export type XRState = BaseXRState<XRElementImplementation>;
/**
 * Starting point for each XR application.
 * Allows to configure the session's features and defaults such as what controllers are rendered and how they can interact with the scene
 * @returns A new XR store
 */
export declare function createXRStore(options?: XRStoreOptions): BaseXRStore<XRElementImplementation>;
export type XRProperties = {
    children?: ReactNode;
    store: XRStore;
};
/**
 * Core XR component for connecting the `XRStore` with the scene.
 * Requires an `XRStore` which it will provide to its children.
 *
 * @param props
 * #### `children` - Children to be rendered inside the context.
 * #### `store` - The `XRStore` to be used for the session.
 */
export declare function XR({ children, store }: XRProperties): import("react/jsx-runtime").JSX.Element;
/**
 * Component for hiding the xr context to all child components. Can be used to create virtual displays and similar allowing the components inside the display to think they are not inside an XR environment, making them behave like when outside XR.
 *
 * @param props
 * @param props.children Children to be rendered inside the context.
 */
export declare function NotInXR({ children }: {
    children?: ReactNode;
}): import("react/jsx-runtime").JSX.Element;
export declare function RootCombinedPointer({ children }: {
    children?: ReactNode;
}): import("react/jsx-runtime").JSX.Element;
/**
 * Hook for getting the xr store from the context
 */
export declare function useXRStore(): XRStore;
/**
 * Returns the XR store object from a parent {@link XR} component. If no component is found `undefined` is returned.
 * You most likely should be using {@link useXRStore} instead.
 */
export declare function UNSAFE_useXRStore(): XRStore | undefined;
/**
 * Hook for reading the state from the xr store
 */
export declare function useXR<T = XRState>(selector?: (s: XRState) => T, equalityFn?: (a: T, b: T) => boolean): T;
export {};
