import { Quaternion, Vector3 } from 'three';
import { XRStore } from './store.js';
export type XRAnchorOptions = XRAnchorWorldOptions | XRAnchorSpaceOptions | XRAnchorHitTestResultOptions;
export type XRAnchorWorldOptions = {
    relativeTo: 'world';
    worldPosition: Vector3;
    worldQuaternion: Quaternion;
    frame?: XRFrame;
};
export type XRAnchorHitTestResultOptions = {
    relativeTo: 'hit-test-result';
    hitTestResult: XRHitTestResult;
    offsetPosition?: Vector3;
    offsetQuaternion?: Quaternion;
};
export type XRAnchorSpaceOptions = {
    relativeTo: 'space';
    space: XRSpace;
    offsetPosition?: Vector3;
    offsetQuaternion?: Quaternion;
    frame?: XRFrame;
};
export declare function requestXRAnchor(store: XRStore<any>, options: XRAnchorOptions): Promise<XRAnchor | undefined>;
