/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_INPUT_SOURCE } from '../private.js';
export var XRHandedness;
(function (XRHandedness) {
    XRHandedness["None"] = "none";
    XRHandedness["Left"] = "left";
    XRHandedness["Right"] = "right";
})(XRHandedness || (XRHandedness = {}));
export var XRTargetRayMode;
(function (XRTargetRayMode) {
    XRTargetRayMode["Gaze"] = "gaze";
    XRTargetRayMode["TrackedPointer"] = "tracked-pointer";
    XRTargetRayMode["Screen"] = "screen";
    XRTargetRayMode["TransientPointer"] = "transient-pointer";
})(XRTargetRayMode || (XRTargetRayMode = {}));
export class XRInputSourceArray extends Array {
}
export class XRInputSource {
    constructor(handedness, targetRayMode, profiles, targetRaySpace, gamepad, gripSpace, hand) {
        this[P_INPUT_SOURCE] = {
            handedness,
            targetRayMode,
            targetRaySpace,
            gripSpace,
            profiles,
            gamepad,
            hand,
        };
    }
    get handedness() {
        return this[P_INPUT_SOURCE].handedness;
    }
    get targetRayMode() {
        return this[P_INPUT_SOURCE].targetRayMode;
    }
    get targetRaySpace() {
        return this[P_INPUT_SOURCE].targetRaySpace;
    }
    get gripSpace() {
        return this[P_INPUT_SOURCE].gripSpace;
    }
    get profiles() {
        return this[P_INPUT_SOURCE].profiles;
    }
    get gamepad() {
        return this[P_INPUT_SOURCE].gamepad;
    }
    get hand() {
        return this[P_INPUT_SOURCE].hand;
    }
}
//# sourceMappingURL=XRInputSource.js.map