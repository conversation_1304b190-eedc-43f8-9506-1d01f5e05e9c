'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface CommunityPost {
  id: string
  author: {
    id: string
    name: string
    avatar: string
    level: string
    verified: boolean
  }
  content: string
  images?: string[]
  type: 'text' | 'image' | 'video' | 'project'
  timestamp: Date
  likes: number
  comments: number
  shares: number
  tags: string[]
  isLiked: boolean
  fanReward?: number
}

interface Comment {
  id: string
  author: {
    id: string
    name: string
    avatar: string
  }
  content: string
  timestamp: Date
  likes: number
  isLiked: boolean
}

interface CommunityInteractionProps {
  userId: string
}

export default function CommunityInteraction({ userId }: CommunityInteractionProps) {
  const [posts, setPosts] = useState<CommunityPost[]>([])
  const [loading, setLoading] = useState(true)
  const [showComments, setShowComments] = useState<string | null>(null)
  const [comments, setComments] = useState<Comment[]>([])
  const [newComment, setNewComment] = useState('')
  const [userPoints, setUserPoints] = useState({ FAN: 1500, DID: 85 })

  useEffect(() => {
    loadCommunityPosts()
  }, [userId])

  const loadCommunityPosts = async () => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockPosts: CommunityPost[] = [
        {
          id: '1',
          author: {
            id: 'user1',
            name: '建筑师小王',
            avatar: '🏗️',
            level: 'Gold',
            verified: true
          },
          content: '刚完成了一个智慧城市项目的设计，使用了最新的AI辅助设计技术。这个项目让我对未来城市的发展有了新的认识。分享一些设计心得给大家！',
          images: ['https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop'],
          type: 'project',
          timestamp: new Date('2024-12-15T10:30:00'),
          likes: 156,
          comments: 23,
          shares: 12,
          tags: ['智慧城市', 'AI设计', '建筑'],
          isLiked: false,
          fanReward: 50
        },
        {
          id: '2',
          author: {
            id: 'user2',
            name: '设计师小李',
            avatar: '🎨',
            level: 'Silver',
            verified: true
          },
          content: '今天学习了新的BIM建模技术，效率提升了很多！感谢平台提供的学习资源，希望能早日通过BIM专家认证。',
          type: 'text',
          timestamp: new Date('2024-12-15T09:15:00'),
          likes: 89,
          comments: 15,
          shares: 8,
          tags: ['BIM', '学习', '认证'],
          isLiked: true,
          fanReward: 30
        },
        {
          id: '3',
          author: {
            id: 'user3',
            name: '工程师老张',
            avatar: '⚙️',
            level: 'Platinum',
            verified: true
          },
          content: '分享一个结构优化的小技巧：在进行钢结构设计时，合理使用拓扑优化算法可以减少20%的材料用量。这不仅降低了成本，还更加环保。',
          type: 'text',
          timestamp: new Date('2024-12-15T08:45:00'),
          likes: 234,
          comments: 45,
          shares: 28,
          tags: ['结构设计', '优化', '环保'],
          isLiked: false,
          fanReward: 80
        }
      ]
      
      setPosts(mockPosts)
    } catch (error) {
      console.error('加载社区动态失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLike = async (postId: string) => {
    try {
      setPosts(prev => prev.map(post => {
        if (post.id === postId) {
          const newIsLiked = !post.isLiked
          return {
            ...post,
            isLiked: newIsLiked,
            likes: newIsLiked ? post.likes + 1 : post.likes - 1
          }
        }
        return post
      }))

      // 模拟点赞奖励
      if (!posts.find(p => p.id === postId)?.isLiked) {
        setUserPoints(prev => ({ ...prev, FAN: prev.FAN + 5 }))
      }
    } catch (error) {
      console.error('点赞失败:', error)
    }
  }

  const handleComment = async (postId: string) => {
    if (!newComment.trim()) return

    try {
      const comment: Comment = {
        id: Date.now().toString(),
        author: {
          id: userId,
          name: '我',
          avatar: '👤'
        },
        content: newComment,
        timestamp: new Date(),
        likes: 0,
        isLiked: false
      }

      setComments(prev => [...prev, comment])
      setNewComment('')
      
      // 更新评论数
      setPosts(prev => prev.map(post => 
        post.id === postId 
          ? { ...post, comments: post.comments + 1 }
          : post
      ))

      // 评论奖励
      setUserPoints(prev => ({ ...prev, FAN: prev.FAN + 10, DID: prev.DID + 1 }))
    } catch (error) {
      console.error('评论失败:', error)
    }
  }

  const handleReward = async (postId: string, amount: number) => {
    try {
      if (userPoints.FAN < amount) {
        alert('FAN积分不足')
        return
      }

      setUserPoints(prev => ({ ...prev, FAN: prev.FAN - amount }))
      alert(`成功打赏 ${amount} FAN积分！`)
    } catch (error) {
      console.error('打赏失败:', error)
    }
  }

  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const hours = Math.floor(diff / (1000 * 60 * 60))
    
    if (hours < 1) return '刚刚'
    if (hours < 24) return `${hours}小时前`
    return `${Math.floor(hours / 24)}天前`
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Bronze': return 'from-amber-600 to-amber-700'
      case 'Silver': return 'from-gray-400 to-gray-500'
      case 'Gold': return 'from-yellow-400 to-yellow-500'
      case 'Platinum': return 'from-blue-400 to-blue-500'
      case 'Diamond': return 'from-purple-400 to-purple-500'
      default: return 'from-gray-400 to-gray-500'
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-48 bg-white/10 rounded-xl mb-4"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 用户积分显示 */}
      <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-2xl p-4 border border-purple-500/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="text-2xl">👤</div>
            <div>
              <div className="text-white font-medium">我的积分</div>
              <div className="text-purple-200 text-sm">参与社区互动获得奖励</div>
            </div>
          </div>
          <div className="flex space-x-4">
            <div className="text-center">
              <div className="text-red-400 font-bold">{userPoints.FAN}</div>
              <div className="text-xs text-gray-400">FAN积分</div>
            </div>
            <div className="text-center">
              <div className="text-yellow-400 font-bold">{userPoints.DID}</div>
              <div className="text-xs text-gray-400">信誉积分</div>
            </div>
          </div>
        </div>
      </div>

      {/* 社区动态 */}
      <div className="space-y-4">
        {posts.map((post, index) => (
          <motion.div
            key={post.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20"
          >
            {/* 作者信息 */}
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-xl">
                {post.author.avatar}
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className="text-white font-medium">{post.author.name}</span>
                  {post.author.verified && (
                    <svg className="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  )}
                  <div className={`px-2 py-1 bg-gradient-to-r ${getLevelColor(post.author.level)} rounded-full`}>
                    <span className="text-white text-xs font-bold">{post.author.level}</span>
                  </div>
                </div>
                <div className="text-gray-400 text-sm">{formatTimeAgo(post.timestamp)}</div>
              </div>
            </div>

            {/* 内容 */}
            <div className="mb-4">
              <p className="text-white leading-relaxed mb-3">{post.content}</p>
              
              {/* 图片 */}
              {post.images && post.images.length > 0 && (
                <div className="grid grid-cols-1 gap-2 mb-3">
                  {post.images.map((image, imgIndex) => (
                    <div key={imgIndex} className="relative h-48 rounded-lg overflow-hidden">
                      <img
                        src={image}
                        alt="Post image"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              )}

              {/* 标签 */}
              <div className="flex flex-wrap gap-2">
                {post.tags.map((tag, tagIndex) => (
                  <span
                    key={tagIndex}
                    className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded-lg text-xs"
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            </div>

            {/* 互动按钮 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <button
                  onClick={() => handleLike(post.id)}
                  className={`flex items-center space-x-2 ${
                    post.isLiked ? 'text-red-400' : 'text-gray-400 hover:text-red-400'
                  } transition-colors`}
                >
                  <span className="text-lg">{post.isLiked ? '❤️' : '🤍'}</span>
                  <span className="text-sm">{post.likes}</span>
                </button>

                <button
                  onClick={() => setShowComments(showComments === post.id ? null : post.id)}
                  className="flex items-center space-x-2 text-gray-400 hover:text-blue-400 transition-colors"
                >
                  <span className="text-lg">💬</span>
                  <span className="text-sm">{post.comments}</span>
                </button>

                <button className="flex items-center space-x-2 text-gray-400 hover:text-green-400 transition-colors">
                  <span className="text-lg">🔄</span>
                  <span className="text-sm">{post.shares}</span>
                </button>
              </div>

              {/* 打赏按钮 */}
              {post.fanReward && (
                <button
                  onClick={() => handleReward(post.id, post.fanReward!)}
                  className="flex items-center space-x-2 px-3 py-1 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-lg hover:bg-yellow-500/30 transition-colors"
                >
                  <span className="text-lg">💰</span>
                  <span className="text-yellow-300 text-sm">{post.fanReward} FAN</span>
                </button>
              )}
            </div>

            {/* 评论区 */}
            <AnimatePresence>
              {showComments === post.id && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-4 pt-4 border-t border-white/10"
                >
                  {/* 评论输入 */}
                  <div className="flex space-x-3 mb-4">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                      <span className="text-sm">👤</span>
                    </div>
                    <div className="flex-1 flex space-x-2">
                      <input
                        type="text"
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        placeholder="写下你的评论..."
                        className="flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 text-sm"
                      />
                      <button
                        onClick={() => handleComment(post.id)}
                        className="px-4 py-2 bg-blue-500 rounded-lg text-white text-sm font-medium hover:bg-blue-600 transition-colors"
                      >
                        发送
                      </button>
                    </div>
                  </div>

                  {/* 评论列表 */}
                  <div className="space-y-3">
                    {comments.map((comment) => (
                      <div key={comment.id} className="flex space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-gray-500 to-gray-600 rounded-full flex items-center justify-center">
                          <span className="text-sm">{comment.author.avatar}</span>
                        </div>
                        <div className="flex-1">
                          <div className="bg-white/5 rounded-lg p-3">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="text-white font-medium text-sm">{comment.author.name}</span>
                              <span className="text-gray-400 text-xs">{formatTimeAgo(comment.timestamp)}</span>
                            </div>
                            <p className="text-gray-300 text-sm">{comment.content}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        ))}
      </div>

      {/* 合规提示 */}
      <div className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-4">
        <div className="text-blue-200 text-xs">
          <p className="font-medium mb-1">社区互动规则：</p>
          <p>• 发布内容和评论可获得FAN积分奖励</p>
          <p>• 积分为平台内虚拟积分，不具有货币属性</p>
          <p>• 请遵守社区规范，文明互动</p>
          <p>• 禁止发布违法违规内容</p>
        </div>
      </div>
    </div>
  )
}
