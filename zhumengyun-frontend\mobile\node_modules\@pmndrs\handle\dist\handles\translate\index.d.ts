import { Group, PerspectiveCamera, OrthographicCamera } from 'three';
import { HandlesContext } from '../context.js';
import { HandlesProperties } from '../index.js';
export * from './free.js';
export * from './axis.js';
export * from './delta.js';
export * from './plane.js';
export declare class TranslateHandles extends Group {
    private readonly context;
    size?: number | undefined;
    fixed?: boolean | undefined;
    private readonly free;
    private readonly translationX;
    private readonly translationY;
    private readonly translationZ;
    private readonly translationNegX;
    private readonly translationNegY;
    private readonly translationNegZ;
    private readonly translationXY;
    private readonly translationYZ;
    private readonly translationXZ;
    private readonly scaleGroup;
    private readonly xAxis;
    private readonly yAxis;
    private readonly zAxis;
    private readonly negXAxis;
    private readonly negYAxis;
    private readonly negZAxis;
    constructor(context: HandlesContext, size?: number | undefined, fixed?: boolean | undefined);
    update(camera: PerspectiveCamera | OrthographicCamera): void;
    bind(options?: HandlesProperties): () => void;
}
