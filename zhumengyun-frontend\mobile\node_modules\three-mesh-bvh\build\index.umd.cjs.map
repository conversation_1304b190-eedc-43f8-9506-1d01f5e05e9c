{"version": 3, "file": "index.umd.cjs", "sources": ["../src/core/Constants.js", "../src/core/build/geometryUtils.js", "../src/core/build/computeBoundsUtils.js", "../src/utils/ArrayBoxUtilities.js", "../src/core/build/splitUtils.js", "../src/core/MeshBVHNode.js", "../src/core/build/sortUtils.generated.js", "../src/core/build/sortUtils_indirect.generated.js", "../src/core/utils/nodeBufferUtils.js", "../src/core/build/buildUtils.js", "../src/core/build/buildTree.js", "../src/math/SeparatingAxisBounds.js", "../src/math/MathUtilities.js", "../src/math/ExtendedTriangle.js", "../src/math/OrientedBox.js", "../src/utils/PrimitivePool.js", "../src/utils/ExtendedTrianglePool.js", "../src/core/utils/BufferStack.js", "../src/core/cast/shapecast.js", "../src/core/cast/closestPointToPoint.js", "../src/utils/ThreeRayIntersectUtilities.js", "../src/utils/TriangleUtilities.js", "../src/core/utils/iterationUtils.generated.js", "../src/core/cast/refit.generated.js", "../src/core/utils/intersectUtils.js", "../src/core/utils/iterationUtils_indirect.generated.js", "../src/core/cast/raycast.generated.js", "../src/core/cast/raycastFirst.generated.js", "../src/core/cast/intersectsGeometry.generated.js", "../src/core/cast/closestPointToGeometry.generated.js", "../src/core/cast/refit_indirect.generated.js", "../src/core/cast/raycast_indirect.generated.js", "../src/core/cast/raycastFirst_indirect.generated.js", "../src/core/cast/intersectsGeometry_indirect.generated.js", "../src/core/cast/closestPointToGeometry_indirect.generated.js", "../src/utils/BufferUtils.js", "../src/core/cast/bvhcast.js", "../src/core/MeshBVH.js", "../src/objects/MeshBVHHelper.js", "../src/debug/Debug.js", "../src/utils/GeometryRayIntersectUtilities.js", "../src/utils/ExtensionUtilities.js", "../src/gpu/VertexAttributeTexture.js", "../src/gpu/MeshBVHUniformStruct.js", "../src/utils/StaticGeometryGenerator.js", "../src/gpu/glsl/common_functions.glsl.js", "../src/gpu/glsl/bvh_distance_functions.glsl.js", "../src/gpu/glsl/bvh_ray_functions.glsl.js", "../src/gpu/glsl/bvh_struct_definitions.glsl.js", "../src/index.js"], "sourcesContent": ["// Split strategy constants\nexport const CENTER = 0;\nexport const AVERAGE = 1;\nexport const SAH = 2;\n\n// Traversal constants\nexport const NOT_INTERSECTED = 0;\nexport const INTERSECTED = 1;\nexport const CONTAINED = 2;\n\n// SAH cost constants\n// TODO: hone these costs more. The relative difference between them should be the\n// difference in measured time to perform a triangle intersection vs traversing\n// bounds.\nexport const TRIANGLE_INTERSECT_COST = 1.25;\nexport const TRAVERSAL_COST = 1;\n\n\n// Build constants\nexport const BYTES_PER_NODE = 6 * 4 + 4 + 4;\nexport const IS_LEAFNODE_FLAG = 0xFFFF;\n\n// EPSILON for computing floating point error during build\n// https://en.wikipedia.org/wiki/Machine_epsilon#Values_for_standard_hardware_floating_point_arithmetics\nexport const FLOAT32_EPSILON = Math.pow( 2, - 24 );\n\nexport const SKIP_GENERATION = Symbol( 'SKIP_GENERATION' );\n", "import { BufferAttribute } from 'three';\n\nexport function getVertexCount( geo ) {\n\n\treturn geo.index ? geo.index.count : geo.attributes.position.count;\n\n}\n\nexport function getTriCount( geo ) {\n\n\treturn getVertexCount( geo ) / 3;\n\n}\n\nexport function getIndexArray( vertexCount, BufferConstructor = ArrayBuffer ) {\n\n\tif ( vertexCount > 65535 ) {\n\n\t\treturn new Uint32Array( new BufferConstructor( 4 * vertexCount ) );\n\n\t} else {\n\n\t\treturn new Uint16Array( new BufferConstructor( 2 * vertexCount ) );\n\n\t}\n\n}\n\n// ensures that an index is present on the geometry\nexport function ensureIndex( geo, options ) {\n\n\tif ( ! geo.index ) {\n\n\t\tconst vertexCount = geo.attributes.position.count;\n\t\tconst BufferConstructor = options.useSharedArrayBuffer ? SharedArrayBuffer : ArrayBuffer;\n\t\tconst index = getIndexArray( vertexCount, BufferConstructor );\n\t\tgeo.setIndex( new BufferAttribute( index, 1 ) );\n\n\t\tfor ( let i = 0; i < vertexCount; i ++ ) {\n\n\t\t\tindex[ i ] = i;\n\n\t\t}\n\n\t}\n\n}\n\n// Computes the set of { offset, count } ranges which need independent BVH roots. Each\n// region in the geometry index that belongs to a different set of material groups requires\n// a separate BVH root, so that triangles indices belonging to one group never get swapped\n// with triangle indices belongs to another group. For example, if the groups were like this:\n//\n// [-------------------------------------------------------------]\n// |__________________|\n//   g0 = [0, 20]  |______________________||_____________________|\n//                      g1 = [16, 40]           g2 = [41, 60]\n//\n// we would need four BVH roots: [0, 15], [16, 20], [21, 40], [41, 60].\nexport function getFullGeometryRange( geo, range ) {\n\n\tconst triCount = getTriCount( geo );\n\tconst drawRange = range ? range : geo.drawRange;\n\tconst start = drawRange.start / 3;\n\tconst end = ( drawRange.start + drawRange.count ) / 3;\n\n\tconst offset = Math.max( 0, start );\n\tconst count = Math.min( triCount, end ) - offset;\n\treturn [ {\n\t\toffset: Math.floor( offset ),\n\t\tcount: Math.floor( count ),\n\t} ];\n\n}\n\nexport function getRootIndexRanges( geo, range ) {\n\n\tif ( ! geo.groups || ! geo.groups.length ) {\n\n\t\treturn getFullGeometryRange( geo, range );\n\n\t}\n\n\tconst ranges = [];\n\tconst rangeBoundaries = new Set();\n\n\tconst drawRange = range ? range : geo.drawRange;\n\tconst drawRangeStart = drawRange.start / 3;\n\tconst drawRangeEnd = ( drawRange.start + drawRange.count ) / 3;\n\tfor ( const group of geo.groups ) {\n\n\t\tconst groupStart = group.start / 3;\n\t\tconst groupEnd = ( group.start + group.count ) / 3;\n\t\trangeBoundaries.add( Math.max( drawRangeStart, groupStart ) );\n\t\trangeBoundaries.add( Math.min( drawRangeEnd, groupEnd ) );\n\n\t}\n\n\n\t// note that if you don't pass in a comparator, it sorts them lexicographically as strings :-(\n\tconst sortedBoundaries = Array.from( rangeBoundaries.values() ).sort( ( a, b ) => a - b );\n\tfor ( let i = 0; i < sortedBoundaries.length - 1; i ++ ) {\n\n\t\tconst start = sortedBoundaries[ i ];\n\t\tconst end = sortedBoundaries[ i + 1 ];\n\n\t\tranges.push( {\n\t\t\toffset: Math.floor( start ),\n\t\t\tcount: Math.floor( end - start ),\n\t\t} );\n\n\t}\n\n\treturn ranges;\n\n}\n\nexport function hasGroupGaps( geometry, range ) {\n\n\tconst vertexCount = getTriCount( geometry );\n\tconst groups = getRootIndexRanges( geometry, range )\n\t\t.sort( ( a, b ) => a.offset - b.offset );\n\n\tconst finalGroup = groups[ groups.length - 1 ];\n\tfinalGroup.count = Math.min( vertexCount - finalGroup.offset, finalGroup.count );\n\n\tlet total = 0;\n\tgroups.forEach( ( { count } ) => total += count );\n\treturn vertexCount !== total;\n\n}\n", "import { FLOAT32_EPSILON } from '../Constants.js';\nimport { getTriCount } from './geometryUtils.js';\n\n// computes the union of the bounds of all of the given triangles and puts the resulting box in \"target\".\n// A bounding box is computed for the centroids of the triangles, as well, and placed in \"centroidTarget\".\n// These are computed together to avoid redundant accesses to bounds array.\nexport function getBounds( triangleBounds, offset, count, target, centroidTarget ) {\n\n\tlet minx = Infinity;\n\tlet miny = Infinity;\n\tlet minz = Infinity;\n\tlet maxx = - Infinity;\n\tlet maxy = - Infinity;\n\tlet maxz = - Infinity;\n\n\tlet cminx = Infinity;\n\tlet cminy = Infinity;\n\tlet cminz = Infinity;\n\tlet cmaxx = - Infinity;\n\tlet cmaxy = - Infinity;\n\tlet cmaxz = - Infinity;\n\n\tfor ( let i = offset * 6, end = ( offset + count ) * 6; i < end; i += 6 ) {\n\n\t\tconst cx = triangleBounds[ i + 0 ];\n\t\tconst hx = triangleBounds[ i + 1 ];\n\t\tconst lx = cx - hx;\n\t\tconst rx = cx + hx;\n\t\tif ( lx < minx ) minx = lx;\n\t\tif ( rx > maxx ) maxx = rx;\n\t\tif ( cx < cminx ) cminx = cx;\n\t\tif ( cx > cmaxx ) cmaxx = cx;\n\n\t\tconst cy = triangleBounds[ i + 2 ];\n\t\tconst hy = triangleBounds[ i + 3 ];\n\t\tconst ly = cy - hy;\n\t\tconst ry = cy + hy;\n\t\tif ( ly < miny ) miny = ly;\n\t\tif ( ry > maxy ) maxy = ry;\n\t\tif ( cy < cminy ) cminy = cy;\n\t\tif ( cy > cmaxy ) cmaxy = cy;\n\n\t\tconst cz = triangleBounds[ i + 4 ];\n\t\tconst hz = triangleBounds[ i + 5 ];\n\t\tconst lz = cz - hz;\n\t\tconst rz = cz + hz;\n\t\tif ( lz < minz ) minz = lz;\n\t\tif ( rz > maxz ) maxz = rz;\n\t\tif ( cz < cminz ) cminz = cz;\n\t\tif ( cz > cmaxz ) cmaxz = cz;\n\n\t}\n\n\ttarget[ 0 ] = minx;\n\ttarget[ 1 ] = miny;\n\ttarget[ 2 ] = minz;\n\n\ttarget[ 3 ] = maxx;\n\ttarget[ 4 ] = maxy;\n\ttarget[ 5 ] = maxz;\n\n\tcentroidTarget[ 0 ] = cminx;\n\tcentroidTarget[ 1 ] = cminy;\n\tcentroidTarget[ 2 ] = cminz;\n\n\tcentroidTarget[ 3 ] = cmaxx;\n\tcentroidTarget[ 4 ] = cmaxy;\n\tcentroidTarget[ 5 ] = cmaxz;\n\n}\n\n// precomputes the bounding box for each triangle; required for quickly calculating tree splits.\n// result is an array of size tris.length * 6 where triangle i maps to a\n// [x_center, x_delta, y_center, y_delta, z_center, z_delta] tuple starting at index i * 6,\n// representing the center and half-extent in each dimension of triangle i\nexport function computeTriangleBounds( geo, target = null, offset = null, count = null ) {\n\n\tconst posAttr = geo.attributes.position;\n\tconst index = geo.index ? geo.index.array : null;\n\tconst triCount = getTriCount( geo );\n\tconst normalized = posAttr.normalized;\n\tlet triangleBounds;\n\tif ( target === null ) {\n\n\t\ttriangleBounds = new Float32Array( triCount * 6 * 4 );\n\t\toffset = 0;\n\t\tcount = triCount;\n\n\t} else {\n\n\t\ttriangleBounds = target;\n\t\toffset = offset || 0;\n\t\tcount = count || triCount;\n\n\t}\n\n\t// used for non-normalized positions\n\tconst posArr = posAttr.array;\n\n\t// support for an interleaved position buffer\n\tconst bufferOffset = posAttr.offset || 0;\n\tlet stride = 3;\n\tif ( posAttr.isInterleavedBufferAttribute ) {\n\n\t\tstride = posAttr.data.stride;\n\n\t}\n\n\t// used for normalized positions\n\tconst getters = [ 'getX', 'getY', 'getZ' ];\n\n\tfor ( let tri = offset; tri < offset + count; tri ++ ) {\n\n\t\tconst tri3 = tri * 3;\n\t\tconst tri6 = tri * 6;\n\n\t\tlet ai = tri3 + 0;\n\t\tlet bi = tri3 + 1;\n\t\tlet ci = tri3 + 2;\n\n\t\tif ( index ) {\n\n\t\t\tai = index[ ai ];\n\t\t\tbi = index[ bi ];\n\t\t\tci = index[ ci ];\n\n\t\t}\n\n\t\t// we add the stride and offset here since we access the array directly\n\t\t// below for the sake of performance\n\t\tif ( ! normalized ) {\n\n\t\t\tai = ai * stride + bufferOffset;\n\t\t\tbi = bi * stride + bufferOffset;\n\t\t\tci = ci * stride + bufferOffset;\n\n\t\t}\n\n\t\tfor ( let el = 0; el < 3; el ++ ) {\n\n\t\t\tlet a, b, c;\n\n\t\t\tif ( normalized ) {\n\n\t\t\t\ta = posAttr[ getters[ el ] ]( ai );\n\t\t\t\tb = posAttr[ getters[ el ] ]( bi );\n\t\t\t\tc = posAttr[ getters[ el ] ]( ci );\n\n\t\t\t} else {\n\n\t\t\t\ta = posArr[ ai + el ];\n\t\t\t\tb = posArr[ bi + el ];\n\t\t\t\tc = posArr[ ci + el ];\n\n\t\t\t}\n\n\t\t\tlet min = a;\n\t\t\tif ( b < min ) min = b;\n\t\t\tif ( c < min ) min = c;\n\n\t\t\tlet max = a;\n\t\t\tif ( b > max ) max = b;\n\t\t\tif ( c > max ) max = c;\n\n\t\t\t// Increase the bounds size by float32 epsilon to avoid precision errors when\n\t\t\t// converting to 32 bit float. Scale the epsilon by the size of the numbers being\n\t\t\t// worked with.\n\t\t\tconst halfExtents = ( max - min ) / 2;\n\t\t\tconst el2 = el * 2;\n\t\t\ttriangleBounds[ tri6 + el2 + 0 ] = min + halfExtents;\n\t\t\ttriangleBounds[ tri6 + el2 + 1 ] = halfExtents + ( Math.abs( min ) + halfExtents ) * FLOAT32_EPSILON;\n\n\t\t}\n\n\t}\n\n\treturn triangleBounds;\n\n}\n", "export function arrayToBox( nodeIndex32, array, target ) {\n\n\ttarget.min.x = array[ nodeIndex32 ];\n\ttarget.min.y = array[ nodeIndex32 + 1 ];\n\ttarget.min.z = array[ nodeIndex32 + 2 ];\n\n\ttarget.max.x = array[ nodeIndex32 + 3 ];\n\ttarget.max.y = array[ nodeIndex32 + 4 ];\n\ttarget.max.z = array[ nodeIndex32 + 5 ];\n\n\treturn target;\n\n}\n\nexport function makeEmptyBounds( target ) {\n\n\ttarget[ 0 ] = target[ 1 ] = target[ 2 ] = Infinity;\n\ttarget[ 3 ] = target[ 4 ] = target[ 5 ] = - Infinity;\n\n}\n\nexport function getLongestEdgeIndex( bounds ) {\n\n\tlet splitDimIdx = - 1;\n\tlet splitDist = - Infinity;\n\n\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\tconst dist = bounds[ i + 3 ] - bounds[ i ];\n\t\tif ( dist > splitDist ) {\n\n\t\t\tsplitDist = dist;\n\t\t\tsplitDimIdx = i;\n\n\t\t}\n\n\t}\n\n\treturn splitDimIdx;\n\n}\n\n// copies bounds a into bounds b\nexport function copyBounds( source, target ) {\n\n\ttarget.set( source );\n\n}\n\n// sets bounds target to the union of bounds a and b\nexport function unionBounds( a, b, target ) {\n\n\tlet aVal, bVal;\n\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\tconst d3 = d + 3;\n\n\t\t// set the minimum values\n\t\taVal = a[ d ];\n\t\tbVal = b[ d ];\n\t\ttarget[ d ] = aVal < bVal ? aVal : bVal;\n\n\t\t// set the max values\n\t\taVal = a[ d3 ];\n\t\tbVal = b[ d3 ];\n\t\ttarget[ d3 ] = aVal > bVal ? aVal : bVal;\n\n\t}\n\n}\n\n// expands the given bounds by the provided triangle bounds\nexport function expandByTriangleBounds( startIndex, triangleBounds, bounds ) {\n\n\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\tconst tCenter = triangleBounds[ startIndex + 2 * d ];\n\t\tconst tHalf = triangleBounds[ startIndex + 2 * d + 1 ];\n\n\t\tconst tMin = tCenter - tHalf;\n\t\tconst tMax = tCenter + tHalf;\n\n\t\tif ( tMin < bounds[ d ] ) {\n\n\t\t\tbounds[ d ] = tMin;\n\n\t\t}\n\n\t\tif ( tMax > bounds[ d + 3 ] ) {\n\n\t\t\tbounds[ d + 3 ] = tMax;\n\n\t\t}\n\n\t}\n\n}\n\n// compute bounds surface area\nexport function computeSurfaceArea( bounds ) {\n\n\tconst d0 = bounds[ 3 ] - bounds[ 0 ];\n\tconst d1 = bounds[ 4 ] - bounds[ 1 ];\n\tconst d2 = bounds[ 5 ] - bounds[ 2 ];\n\n\treturn 2 * ( d0 * d1 + d1 * d2 + d2 * d0 );\n\n}\n", "import { getLongestEdgeIndex, computeSurfaceArea, copyBounds, unionBounds, expandByTriangleBounds } from '../../utils/ArrayBoxUtilities.js';\nimport { CENTER, AVERAGE, SAH, TRIANGLE_INTERSECT_COST, TRAVERSAL_COST } from '../Constants.js';\n\nconst BIN_COUNT = 32;\nconst binsSort = ( a, b ) => a.candidate - b.candidate;\nconst sahBins = new Array( BIN_COUNT ).fill().map( () => {\n\n\treturn {\n\n\t\tcount: 0,\n\t\tbounds: new Float32Array( 6 ),\n\t\trightCacheBounds: new Float32Array( 6 ),\n\t\tleftCacheBounds: new Float32Array( 6 ),\n\t\tcandidate: 0,\n\n\t};\n\n} );\nconst leftBounds = new Float32Array( 6 );\n\nexport function getOptimalSplit( nodeBoundingData, centroidBoundingData, triangleBounds, offset, count, strategy ) {\n\n\tlet axis = - 1;\n\tlet pos = 0;\n\n\t// Center\n\tif ( strategy === CENTER ) {\n\n\t\taxis = getLongestEdgeIndex( centroidBoundingData );\n\t\tif ( axis !== - 1 ) {\n\n\t\t\tpos = ( centroidBoundingData[ axis ] + centroidBoundingData[ axis + 3 ] ) / 2;\n\n\t\t}\n\n\t} else if ( strategy === AVERAGE ) {\n\n\t\taxis = getLongestEdgeIndex( nodeBoundingData );\n\t\tif ( axis !== - 1 ) {\n\n\t\t\tpos = getAverage( triangleBounds, offset, count, axis );\n\n\t\t}\n\n\t} else if ( strategy === SAH ) {\n\n\t\tconst rootSurfaceArea = computeSurfaceArea( nodeBoundingData );\n\t\tlet bestCost = TRIANGLE_INTERSECT_COST * count;\n\n\t\t// iterate over all axes\n\t\tconst cStart = offset * 6;\n\t\tconst cEnd = ( offset + count ) * 6;\n\t\tfor ( let a = 0; a < 3; a ++ ) {\n\n\t\t\tconst axisLeft = centroidBoundingData[ a ];\n\t\t\tconst axisRight = centroidBoundingData[ a + 3 ];\n\t\t\tconst axisLength = axisRight - axisLeft;\n\t\t\tconst binWidth = axisLength / BIN_COUNT;\n\n\t\t\t// If we have fewer triangles than we're planning to split then just check all\n\t\t\t// the triangle positions because it will be faster.\n\t\t\tif ( count < BIN_COUNT / 4 ) {\n\n\t\t\t\t// initialize the bin candidates\n\t\t\t\tconst truncatedBins = [ ...sahBins ];\n\t\t\t\ttruncatedBins.length = count;\n\n\t\t\t\t// set the candidates\n\t\t\t\tlet b = 0;\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6, b ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ b ];\n\t\t\t\t\tbin.candidate = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tbin.count = 0;\n\n\t\t\t\t\tconst {\n\t\t\t\t\t\tbounds,\n\t\t\t\t\t\tleftCacheBounds,\n\t\t\t\t\t\trightCacheBounds,\n\t\t\t\t\t} = bin;\n\t\t\t\t\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\t\t\t\t\trightCacheBounds[ d ] = Infinity;\n\t\t\t\t\t\trightCacheBounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t\tleftCacheBounds[ d ] = Infinity;\n\t\t\t\t\t\tleftCacheBounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t\tbounds[ d ] = Infinity;\n\t\t\t\t\t\tbounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t}\n\n\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bounds );\n\n\t\t\t\t}\n\n\t\t\t\ttruncatedBins.sort( binsSort );\n\n\t\t\t\t// remove redundant splits\n\t\t\t\tlet splitCount = count;\n\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\twhile ( bi + 1 < splitCount && truncatedBins[ bi + 1 ].candidate === bin.candidate ) {\n\n\t\t\t\t\t\ttruncatedBins.splice( bi + 1, 1 );\n\t\t\t\t\t\tsplitCount --;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// find the appropriate bin for each triangle and expand the bounds.\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6 ) {\n\n\t\t\t\t\tconst center = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\t\tif ( center >= bin.candidate ) {\n\n\t\t\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.rightCacheBounds );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.leftCacheBounds );\n\t\t\t\t\t\t\tbin.count ++;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// expand all the bounds\n\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\tconst leftCount = bin.count;\n\t\t\t\t\tconst rightCount = count - bin.count;\n\n\t\t\t\t\t// check the cost of this split\n\t\t\t\t\tconst leftBounds = bin.leftCacheBounds;\n\t\t\t\t\tconst rightBounds = bin.rightCacheBounds;\n\n\t\t\t\t\tlet leftProb = 0;\n\t\t\t\t\tif ( leftCount !== 0 ) {\n\n\t\t\t\t\t\tleftProb = computeSurfaceArea( leftBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tlet rightProb = 0;\n\t\t\t\t\tif ( rightCount !== 0 ) {\n\n\t\t\t\t\t\trightProb = computeSurfaceArea( rightBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst cost = TRAVERSAL_COST + TRIANGLE_INTERSECT_COST * (\n\t\t\t\t\t\tleftProb * leftCount + rightProb * rightCount\n\t\t\t\t\t);\n\n\t\t\t\t\tif ( cost < bestCost ) {\n\n\t\t\t\t\t\taxis = a;\n\t\t\t\t\t\tbestCost = cost;\n\t\t\t\t\t\tpos = bin.candidate;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\t// reset the bins\n\t\t\t\tfor ( let i = 0; i < BIN_COUNT; i ++ ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tbin.count = 0;\n\t\t\t\t\tbin.candidate = axisLeft + binWidth + i * binWidth;\n\n\t\t\t\t\tconst bounds = bin.bounds;\n\t\t\t\t\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\t\t\t\t\tbounds[ d ] = Infinity;\n\t\t\t\t\t\tbounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// iterate over all center positions\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6 ) {\n\n\t\t\t\t\tconst triCenter = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tconst relativeCenter = triCenter - axisLeft;\n\n\t\t\t\t\t// in the partition function if the centroid lies on the split plane then it is\n\t\t\t\t\t// considered to be on the right side of the split\n\t\t\t\t\tlet binIndex = ~ ~ ( relativeCenter / binWidth );\n\t\t\t\t\tif ( binIndex >= BIN_COUNT ) binIndex = BIN_COUNT - 1;\n\n\t\t\t\t\tconst bin = sahBins[ binIndex ];\n\t\t\t\t\tbin.count ++;\n\n\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.bounds );\n\n\t\t\t\t}\n\n\t\t\t\t// cache the unioned bounds from right to left so we don't have to regenerate them each time\n\t\t\t\tconst lastBin = sahBins[ BIN_COUNT - 1 ];\n\t\t\t\tcopyBounds( lastBin.bounds, lastBin.rightCacheBounds );\n\t\t\t\tfor ( let i = BIN_COUNT - 2; i >= 0; i -- ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tconst nextBin = sahBins[ i + 1 ];\n\t\t\t\t\tunionBounds( bin.bounds, nextBin.rightCacheBounds, bin.rightCacheBounds );\n\n\t\t\t\t}\n\n\t\t\t\tlet leftCount = 0;\n\t\t\t\tfor ( let i = 0; i < BIN_COUNT - 1; i ++ ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tconst binCount = bin.count;\n\t\t\t\t\tconst bounds = bin.bounds;\n\n\t\t\t\t\tconst nextBin = sahBins[ i + 1 ];\n\t\t\t\t\tconst rightBounds = nextBin.rightCacheBounds;\n\n\t\t\t\t\t// don't do anything with the bounds if the new bounds have no triangles\n\t\t\t\t\tif ( binCount !== 0 ) {\n\n\t\t\t\t\t\tif ( leftCount === 0 ) {\n\n\t\t\t\t\t\t\tcopyBounds( bounds, leftBounds );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\tunionBounds( bounds, leftBounds, leftBounds );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\tleftCount += binCount;\n\n\t\t\t\t\t// check the cost of this split\n\t\t\t\t\tlet leftProb = 0;\n\t\t\t\t\tlet rightProb = 0;\n\n\t\t\t\t\tif ( leftCount !== 0 ) {\n\n\t\t\t\t\t\tleftProb = computeSurfaceArea( leftBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst rightCount = count - leftCount;\n\t\t\t\t\tif ( rightCount !== 0 ) {\n\n\t\t\t\t\t\trightProb = computeSurfaceArea( rightBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst cost = TRAVERSAL_COST + TRIANGLE_INTERSECT_COST * (\n\t\t\t\t\t\tleftProb * leftCount + rightProb * rightCount\n\t\t\t\t\t);\n\n\t\t\t\t\tif ( cost < bestCost ) {\n\n\t\t\t\t\t\taxis = a;\n\t\t\t\t\t\tbestCost = cost;\n\t\t\t\t\t\tpos = bin.candidate;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t} else {\n\n\t\tconsole.warn( `MeshBVH: Invalid build strategy value ${ strategy } used.` );\n\n\t}\n\n\treturn { axis, pos };\n\n}\n\n// returns the average coordinate on the specified axis of the all the provided triangles\nfunction getAverage( triangleBounds, offset, count, axis ) {\n\n\tlet avg = 0;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tavg += triangleBounds[ i * 6 + axis * 2 ];\n\n\t}\n\n\treturn avg / count;\n\n}\n", "export class MeshBVHNode {\n\n\tconstructor() {\n\n\t\t// internal nodes have boundingData, left, right, and splitAxis\n\t\t// leaf nodes have offset and count (referring to primitives in the mesh geometry)\n\n\t\tthis.boundingData = new Float32Array( 6 );\n\n\t}\n\n}\n", "/********************************************************/\n/* This file is generated from \"sortUtils.template.js\". */\n/********************************************************/\n// reorders `tris` such that for `count` elements after `offset`, elements on the left side of the split\n// will be on the left and elements on the right side of the split will be on the right. returns the index\n// of the first element on the right side, or offset + count if there are no elements on the right side.\nfunction partition( indirectBuffer, index, triangleBounds, offset, count, split ) {\n\n\tlet left = offset;\n\tlet right = offset + count - 1;\n\tconst pos = split.pos;\n\tconst axisOffset = split.axis * 2;\n\n\t// hoare partitioning, see e.g. https://en.wikipedia.org/wiki/Quicksort#Hoare_partition_scheme\n\twhile ( true ) {\n\n\t\twhile ( left <= right && triangleBounds[ left * 6 + axisOffset ] < pos ) {\n\n\t\t\tleft ++;\n\n\t\t}\n\n\t\t// if a triangle center lies on the partition plane it is considered to be on the right side\n\t\twhile ( left <= right && triangleBounds[ right * 6 + axisOffset ] >= pos ) {\n\n\t\t\tright --;\n\n\t\t}\n\n\t\tif ( left < right ) {\n\n\t\t\t// we need to swap all of the information associated with the triangles at index\n\t\t\t// left and right; that's the verts in the geometry index, the bounds,\n\t\t\t// and perhaps the SAH planes\n\n\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\tlet t0 = index[ left * 3 + i ];\n\t\t\t\tindex[ left * 3 + i ] = index[ right * 3 + i ];\n\t\t\t\tindex[ right * 3 + i ] = t0;\n\n\t\t\t}\n\n\n\t\t\t// swap bounds\n\t\t\tfor ( let i = 0; i < 6; i ++ ) {\n\n\t\t\t\tlet tb = triangleBounds[ left * 6 + i ];\n\t\t\t\ttriangleBounds[ left * 6 + i ] = triangleBounds[ right * 6 + i ];\n\t\t\t\ttriangleBounds[ right * 6 + i ] = tb;\n\n\t\t\t}\n\n\t\t\tleft ++;\n\t\t\tright --;\n\n\t\t} else {\n\n\t\t\treturn left;\n\n\t\t}\n\n\t}\n\n}\n\nexport { partition };\n", "/********************************************************/\n/* This file is generated from \"sortUtils.template.js\". */\n/********************************************************/\n// reorders `tris` such that for `count` elements after `offset`, elements on the left side of the split\n// will be on the left and elements on the right side of the split will be on the right. returns the index\n// of the first element on the right side, or offset + count if there are no elements on the right side.\nfunction partition_indirect( indirectBuffer, index, triangleBounds, offset, count, split ) {\n\n\tlet left = offset;\n\tlet right = offset + count - 1;\n\tconst pos = split.pos;\n\tconst axisOffset = split.axis * 2;\n\n\t// hoare partitioning, see e.g. https://en.wikipedia.org/wiki/Quicksort#Hoare_partition_scheme\n\twhile ( true ) {\n\n\t\twhile ( left <= right && triangleBounds[ left * 6 + axisOffset ] < pos ) {\n\n\t\t\tleft ++;\n\n\t\t}\n\n\t\t// if a triangle center lies on the partition plane it is considered to be on the right side\n\t\twhile ( left <= right && triangleBounds[ right * 6 + axisOffset ] >= pos ) {\n\n\t\t\tright --;\n\n\t\t}\n\n\t\tif ( left < right ) {\n\n\t\t\t// we need to swap all of the information associated with the triangles at index\n\t\t\t// left and right; that's the verts in the geometry index, the bounds,\n\t\t\t// and perhaps the SAH planes\n\t\t\tlet t = indirectBuffer[ left ];\n\t\t\tindirectBuffer[ left ] = indirectBuffer[ right ];\n\t\t\tindirectBuffer[ right ] = t;\n\n\n\t\t\t// swap bounds\n\t\t\tfor ( let i = 0; i < 6; i ++ ) {\n\n\t\t\t\tlet tb = triangleBounds[ left * 6 + i ];\n\t\t\t\ttriangleBounds[ left * 6 + i ] = triangleBounds[ right * 6 + i ];\n\t\t\t\ttriangleBounds[ right * 6 + i ] = tb;\n\n\t\t\t}\n\n\t\t\tleft ++;\n\t\t\tright --;\n\n\t\t} else {\n\n\t\t\treturn left;\n\n\t\t}\n\n\t}\n\n}\n\nexport { partition_indirect };\n", "export function IS_LEAF( n16, uint16Array ) {\n\n\treturn uint16Array[ n16 + 15 ] === 0xFFFF;\n\n}\n\nexport function OFFSET( n32, uint32Array ) {\n\n\treturn uint32Array[ n32 + 6 ];\n\n}\n\nexport function COUNT( n16, uint16Array ) {\n\n\treturn uint16Array[ n16 + 14 ];\n\n}\n\nexport function LEFT_NODE( n32 ) {\n\n\treturn n32 + 8;\n\n}\n\nexport function RIGHT_NODE( n32, uint32Array ) {\n\n\treturn uint32Array[ n32 + 6 ];\n\n}\n\nexport function SPLIT_AXIS( n32, uint32Array ) {\n\n\treturn uint32Array[ n32 + 7 ];\n\n}\n\nexport function BOUNDING_DATA_INDEX( n32 ) {\n\n\treturn n32;\n\n}\n", "import { BYTES_PER_NODE, IS_LEAFNODE_FLAG } from '../Constants.js';\nimport { IS_LEAF } from '../utils/nodeBufferUtils.js';\n\nlet float32Array, uint32Array, uint16Array, uint8Array;\nconst MAX_POINTER = Math.pow( 2, 32 );\n\nexport function countNodes( node ) {\n\n\tif ( 'count' in node ) {\n\n\t\treturn 1;\n\n\t} else {\n\n\t\treturn 1 + countNodes( node.left ) + countNodes( node.right );\n\n\t}\n\n}\n\nexport function populateBuffer( byteOffset, node, buffer ) {\n\n\tfloat32Array = new Float32Array( buffer );\n\tuint32Array = new Uint32Array( buffer );\n\tuint16Array = new Uint16Array( buffer );\n\tuint8Array = new Uint8Array( buffer );\n\n\treturn _populateBuffer( byteOffset, node );\n\n}\n\n// pack structure\n// boundingData  \t\t\t\t: 6 float32\n// right / offset \t\t\t\t: 1 uint32\n// splitAxis / isLeaf + count \t: 1 uint32 / 2 uint16\nfunction _populateBuffer( byteOffset, node ) {\n\n\tconst stride4Offset = byteOffset / 4;\n\tconst stride2Offset = byteOffset / 2;\n\tconst isLeaf = 'count' in node;\n\tconst boundingData = node.boundingData;\n\tfor ( let i = 0; i < 6; i ++ ) {\n\n\t\tfloat32Array[ stride4Offset + i ] = boundingData[ i ];\n\n\t}\n\n\tif ( isLeaf ) {\n\n\t\tif ( node.buffer ) {\n\n\t\t\tconst buffer = node.buffer;\n\t\t\tuint8Array.set( new Uint8Array( buffer ), byteOffset );\n\n\t\t\tfor ( let offset = byteOffset, l = byteOffset + buffer.byteLength; offset < l; offset += BYTES_PER_NODE ) {\n\n\t\t\t\tconst offset2 = offset / 2;\n\t\t\t\tif ( ! IS_LEAF( offset2, uint16Array ) ) {\n\n\t\t\t\t\tuint32Array[ ( offset / 4 ) + 6 ] += stride4Offset;\n\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn byteOffset + buffer.byteLength;\n\n\t\t} else {\n\n\t\t\tconst offset = node.offset;\n\t\t\tconst count = node.count;\n\t\t\tuint32Array[ stride4Offset + 6 ] = offset;\n\t\t\tuint16Array[ stride2Offset + 14 ] = count;\n\t\t\tuint16Array[ stride2Offset + 15 ] = IS_LEAFNODE_FLAG;\n\t\t\treturn byteOffset + BYTES_PER_NODE;\n\n\t\t}\n\n\t} else {\n\n\t\tconst left = node.left;\n\t\tconst right = node.right;\n\t\tconst splitAxis = node.splitAxis;\n\n\t\tlet nextUnusedPointer;\n\t\tnextUnusedPointer = _populateBuffer( byteOffset + BYTES_PER_NODE, left );\n\n\t\tif ( ( nextUnusedPointer / 4 ) > MAX_POINTER ) {\n\n\t\t\tthrow new Error( 'MeshBVH: Cannot store child pointer greater than 32 bits.' );\n\n\t\t}\n\n\t\tuint32Array[ stride4Offset + 6 ] = nextUnusedPointer / 4;\n\t\tnextUnusedPointer = _populateBuffer( nextUnusedPointer, right );\n\n\t\tuint32Array[ stride4Offset + 7 ] = splitAxis;\n\t\treturn nextUnusedPointer;\n\n\t}\n\n}\n", "import { ensureIndex, getFullGeometryRange, getRootIndexRanges, getTriCount, hasGroupGaps, } from './geometryUtils.js';\nimport { getBounds, computeTriangleBounds } from './computeBoundsUtils.js';\nimport { getOptimalSplit } from './splitUtils.js';\nimport { MeshBVHNode } from '../MeshBVHNode.js';\nimport { BYTES_PER_NODE } from '../Constants.js';\n\nimport { partition } from './sortUtils.generated.js';\nimport { partition_indirect } from './sortUtils_indirect.generated.js';\nimport { countNodes, populateBuffer } from './buildUtils.js';\n\nexport function generateIndirectBuffer( geometry, useSharedArrayBuffer ) {\n\n\tconst triCount = ( geometry.index ? geometry.index.count : geometry.attributes.position.count ) / 3;\n\tconst useUint32 = triCount > 2 ** 16;\n\tconst byteCount = useUint32 ? 4 : 2;\n\n\tconst buffer = useSharedArrayBuffer ? new SharedArrayBuffer( triCount * byteCount ) : new ArrayBuffer( triCount * byteCount );\n\tconst indirectBuffer = useUint32 ? new Uint32Array( buffer ) : new Uint16Array( buffer );\n\tfor ( let i = 0, l = indirectBuffer.length; i < l; i ++ ) {\n\n\t\tindirectBuffer[ i ] = i;\n\n\t}\n\n\treturn indirectBuffer;\n\n}\n\nexport function buildTree( bvh, triangleBounds, offset, count, options ) {\n\n\t// epxand variables\n\tconst {\n\t\tmaxDepth,\n\t\tverbose,\n\t\tmaxLeafTris,\n\t\tstrategy,\n\t\tonProgress,\n\t\tindirect,\n\t} = options;\n\tconst indirectBuffer = bvh._indirectBuffer;\n\tconst geometry = bvh.geometry;\n\tconst indexArray = geometry.index ? geometry.index.array : null;\n\tconst partionFunc = indirect ? partition_indirect : partition;\n\n\t// generate intermediate variables\n\tconst totalTriangles = getTriCount( geometry );\n\tconst cacheCentroidBoundingData = new Float32Array( 6 );\n\tlet reachedMaxDepth = false;\n\n\tconst root = new MeshBVHNode();\n\tgetBounds( triangleBounds, offset, count, root.boundingData, cacheCentroidBoundingData );\n\tsplitNode( root, offset, count, cacheCentroidBoundingData );\n\treturn root;\n\n\tfunction triggerProgress( trianglesProcessed ) {\n\n\t\tif ( onProgress ) {\n\n\t\t\tonProgress( trianglesProcessed / totalTriangles );\n\n\t\t}\n\n\t}\n\n\t// either recursively splits the given node, creating left and right subtrees for it, or makes it a leaf node,\n\t// recording the offset and count of its triangles and writing them into the reordered geometry index.\n\tfunction splitNode( node, offset, count, centroidBoundingData = null, depth = 0 ) {\n\n\t\tif ( ! reachedMaxDepth && depth >= maxDepth ) {\n\n\t\t\treachedMaxDepth = true;\n\t\t\tif ( verbose ) {\n\n\t\t\t\tconsole.warn( `MeshBVH: Max depth of ${ maxDepth } reached when generating BVH. Consider increasing maxDepth.` );\n\t\t\t\tconsole.warn( geometry );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// early out if we've met our capacity\n\t\tif ( count <= maxLeafTris || depth >= maxDepth ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\t\t\treturn node;\n\n\t\t}\n\n\t\t// Find where to split the volume\n\t\tconst split = getOptimalSplit( node.boundingData, centroidBoundingData, triangleBounds, offset, count, strategy );\n\t\tif ( split.axis === - 1 ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\t\t\treturn node;\n\n\t\t}\n\n\t\tconst splitOffset = partionFunc( indirectBuffer, indexArray, triangleBounds, offset, count, split );\n\n\t\t// create the two new child nodes\n\t\tif ( splitOffset === offset || splitOffset === offset + count ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\n\t\t} else {\n\n\t\t\tnode.splitAxis = split.axis;\n\n\t\t\t// create the left child and compute its bounding box\n\t\t\tconst left = new MeshBVHNode();\n\t\t\tconst lstart = offset;\n\t\t\tconst lcount = splitOffset - offset;\n\t\t\tnode.left = left;\n\n\t\t\tgetBounds( triangleBounds, lstart, lcount, left.boundingData, cacheCentroidBoundingData );\n\t\t\tsplitNode( left, lstart, lcount, cacheCentroidBoundingData, depth + 1 );\n\n\t\t\t// repeat for right\n\t\t\tconst right = new MeshBVHNode();\n\t\t\tconst rstart = splitOffset;\n\t\t\tconst rcount = count - lcount;\n\t\t\tnode.right = right;\n\n\t\t\tgetBounds( triangleBounds, rstart, rcount, right.boundingData, cacheCentroidBoundingData );\n\t\t\tsplitNode( right, rstart, rcount, cacheCentroidBoundingData, depth + 1 );\n\n\t\t}\n\n\t\treturn node;\n\n\t}\n\n}\n\nexport function buildPackedTree( bvh, options ) {\n\n\tconst geometry = bvh.geometry;\n\tif ( options.indirect ) {\n\n\t\tbvh._indirectBuffer = generateIndirectBuffer( geometry, options.useSharedArrayBuffer );\n\n\t\tif ( hasGroupGaps( geometry, options.range ) && ! options.verbose ) {\n\n\t\t\tconsole.warn(\n\t\t\t\t'MeshBVH: Provided geometry contains groups or a range that do not fully span the vertex contents while using the \"indirect\" option. ' +\n\t\t\t\t'BVH may incorrectly report intersections on unrendered portions of the geometry.'\n\t\t\t);\n\n\t\t}\n\n\t}\n\n\tif ( ! bvh._indirectBuffer ) {\n\n\t\tensureIndex( geometry, options );\n\n\t}\n\n\tconst BufferConstructor = options.useSharedArrayBuffer ? SharedArrayBuffer : ArrayBuffer;\n\n\tconst triangleBounds = computeTriangleBounds( geometry );\n\tconst geometryRanges = options.indirect ? getFullGeometryRange( geometry, options.range ) : getRootIndexRanges( geometry, options.range );\n\tbvh._roots = geometryRanges.map( range => {\n\n\t\tconst root = buildTree( bvh, triangleBounds, range.offset, range.count, options );\n\t\tconst nodeCount = countNodes( root );\n\t\tconst buffer = new BufferConstructor( BYTES_PER_NODE * nodeCount );\n\t\tpopulateBuffer( 0, root, buffer );\n\t\treturn buffer;\n\n\t} );\n\n}\n", "import { Vector3 } from 'three';\n\nexport class SeparatingAxisBounds {\n\n\tconstructor() {\n\n\t\tthis.min = Infinity;\n\t\tthis.max = - Infinity;\n\n\t}\n\n\tsetFromPointsField( points, field ) {\n\n\t\tlet min = Infinity;\n\t\tlet max = - Infinity;\n\t\tfor ( let i = 0, l = points.length; i < l; i ++ ) {\n\n\t\t\tconst p = points[ i ];\n\t\t\tconst val = p[ field ];\n\t\t\tmin = val < min ? val : min;\n\t\t\tmax = val > max ? val : max;\n\n\t\t}\n\n\t\tthis.min = min;\n\t\tthis.max = max;\n\n\t}\n\n\tsetFromPoints( axis, points ) {\n\n\t\tlet min = Infinity;\n\t\tlet max = - Infinity;\n\t\tfor ( let i = 0, l = points.length; i < l; i ++ ) {\n\n\t\t\tconst p = points[ i ];\n\t\t\tconst val = axis.dot( p );\n\t\t\tmin = val < min ? val : min;\n\t\t\tmax = val > max ? val : max;\n\n\t\t}\n\n\t\tthis.min = min;\n\t\tthis.max = max;\n\n\t}\n\n\tisSeparated( other ) {\n\n\t\treturn this.min > other.max || other.min > this.max;\n\n\t}\n\n}\n\nSeparatingAxisBounds.prototype.setFromBox = ( function () {\n\n\tconst p = new Vector3();\n\treturn function setFromBox( axis, box ) {\n\n\t\tconst boxMin = box.min;\n\t\tconst boxMax = box.max;\n\t\tlet min = Infinity;\n\t\tlet max = - Infinity;\n\t\tfor ( let x = 0; x <= 1; x ++ ) {\n\n\t\t\tfor ( let y = 0; y <= 1; y ++ ) {\n\n\t\t\t\tfor ( let z = 0; z <= 1; z ++ ) {\n\n\t\t\t\t\tp.x = boxMin.x * x + boxMax.x * ( 1 - x );\n\t\t\t\t\tp.y = boxMin.y * y + boxMax.y * ( 1 - y );\n\t\t\t\t\tp.z = boxMin.z * z + boxMax.z * ( 1 - z );\n\n\t\t\t\t\tconst val = axis.dot( p );\n\t\t\t\t\tmin = Math.min( val, min );\n\t\t\t\t\tmax = Math.max( val, max );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tthis.min = min;\n\t\tthis.max = max;\n\n\t};\n\n} )();\n\nexport const areIntersecting = ( function () {\n\n\tconst cacheSatBounds = new SeparatingAxisBounds();\n\treturn function areIntersecting( shape1, shape2 ) {\n\n\t\tconst points1 = shape1.points;\n\t\tconst satAxes1 = shape1.satAxes;\n\t\tconst satBounds1 = shape1.satBounds;\n\n\t\tconst points2 = shape2.points;\n\t\tconst satAxes2 = shape2.satAxes;\n\t\tconst satBounds2 = shape2.satBounds;\n\n\t\t// check axes of the first shape\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = satBounds1[ i ];\n\t\t\tconst sa = satAxes1[ i ];\n\t\t\tcacheSatBounds.setFromPoints( sa, points2 );\n\t\t\tif ( sb.isSeparated( cacheSatBounds ) ) return false;\n\n\t\t}\n\n\t\t// check axes of the second shape\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = satBounds2[ i ];\n\t\t\tconst sa = satAxes2[ i ];\n\t\t\tcacheSatBounds.setFromPoints( sa, points1 );\n\t\t\tif ( sb.isSeparated( cacheSatBounds ) ) return false;\n\n\t\t}\n\n\t};\n\n} )();\n", "import { Vector3, Vector2, Plane, Line3 } from 'three';\n\nexport const closestPointLineToLine = ( function () {\n\n\t// https://github.com/juj/MathGeoLib/blob/master/src/Geometry/Line.cpp#L56\n\tconst dir1 = new Vector3();\n\tconst dir2 = new Vector3();\n\tconst v02 = new Vector3();\n\treturn function closestPointLineToLine( l1, l2, result ) {\n\n\t\tconst v0 = l1.start;\n\t\tconst v10 = dir1;\n\t\tconst v2 = l2.start;\n\t\tconst v32 = dir2;\n\n\t\tv02.subVectors( v0, v2 );\n\t\tdir1.subVectors( l1.end, l1.start );\n\t\tdir2.subVectors( l2.end, l2.start );\n\n\t\t// float d0232 = v02.Dot(v32);\n\t\tconst d0232 = v02.dot( v32 );\n\n\t\t// float d3210 = v32.Dot(v10);\n\t\tconst d3210 = v32.dot( v10 );\n\n\t\t// float d3232 = v32.Dot(v32);\n\t\tconst d3232 = v32.dot( v32 );\n\n\t\t// float d0210 = v02.Dot(v10);\n\t\tconst d0210 = v02.dot( v10 );\n\n\t\t// float d1010 = v10.Dot(v10);\n\t\tconst d1010 = v10.dot( v10 );\n\n\t\t// float denom = d1010*d3232 - d3210*d3210;\n\t\tconst denom = d1010 * d3232 - d3210 * d3210;\n\n\t\tlet d, d2;\n\t\tif ( denom !== 0 ) {\n\n\t\t\td = ( d0232 * d3210 - d0210 * d3232 ) / denom;\n\n\t\t} else {\n\n\t\t\td = 0;\n\n\t\t}\n\n\t\td2 = ( d0232 + d * d3210 ) / d3232;\n\n\t\tresult.x = d;\n\t\tresult.y = d2;\n\n\t};\n\n} )();\n\nexport const closestPointsSegmentToSegment = ( function () {\n\n\t// https://github.com/juj/MathGeoLib/blob/master/src/Geometry/LineSegment.cpp#L187\n\tconst paramResult = new Vector2();\n\tconst temp1 = new Vector3();\n\tconst temp2 = new Vector3();\n\treturn function closestPointsSegmentToSegment( l1, l2, target1, target2 ) {\n\n\t\tclosestPointLineToLine( l1, l2, paramResult );\n\n\t\tlet d = paramResult.x;\n\t\tlet d2 = paramResult.y;\n\t\tif ( d >= 0 && d <= 1 && d2 >= 0 && d2 <= 1 ) {\n\n\t\t\tl1.at( d, target1 );\n\t\t\tl2.at( d2, target2 );\n\n\t\t\treturn;\n\n\t\t} else if ( d >= 0 && d <= 1 ) {\n\n\t\t\t// Only d2 is out of bounds.\n\t\t\tif ( d2 < 0 ) {\n\n\t\t\t\tl2.at( 0, target2 );\n\n\t\t\t} else {\n\n\t\t\t\tl2.at( 1, target2 );\n\n\t\t\t}\n\n\t\t\tl1.closestPointToPoint( target2, true, target1 );\n\t\t\treturn;\n\n\t\t} else if ( d2 >= 0 && d2 <= 1 ) {\n\n\t\t\t// Only d is out of bounds.\n\t\t\tif ( d < 0 ) {\n\n\t\t\t\tl1.at( 0, target1 );\n\n\t\t\t} else {\n\n\t\t\t\tl1.at( 1, target1 );\n\n\t\t\t}\n\n\t\t\tl2.closestPointToPoint( target1, true, target2 );\n\t\t\treturn;\n\n\t\t} else {\n\n\t\t\t// Both u and u2 are out of bounds.\n\t\t\tlet p;\n\t\t\tif ( d < 0 ) {\n\n\t\t\t\tp = l1.start;\n\n\t\t\t} else {\n\n\t\t\t\tp = l1.end;\n\n\t\t\t}\n\n\t\t\tlet p2;\n\t\t\tif ( d2 < 0 ) {\n\n\t\t\t\tp2 = l2.start;\n\n\t\t\t} else {\n\n\t\t\t\tp2 = l2.end;\n\n\t\t\t}\n\n\t\t\tconst closestPoint = temp1;\n\t\t\tconst closestPoint2 = temp2;\n\t\t\tl1.closestPointToPoint( p2, true, temp1 );\n\t\t\tl2.closestPointToPoint( p, true, temp2 );\n\n\t\t\tif ( closestPoint.distanceToSquared( p2 ) <= closestPoint2.distanceToSquared( p ) ) {\n\n\t\t\t\ttarget1.copy( closestPoint );\n\t\t\t\ttarget2.copy( p2 );\n\t\t\t\treturn;\n\n\t\t\t} else {\n\n\t\t\t\ttarget1.copy( p );\n\t\t\t\ttarget2.copy( closestPoint2 );\n\t\t\t\treturn;\n\n\t\t\t}\n\n\t\t}\n\n\t};\n\n} )();\n\n\nexport const sphereIntersectTriangle = ( function () {\n\n\t// https://stackoverflow.com/questions/34043955/detect-collision-between-sphere-and-triangle-in-three-js\n\tconst closestPointTemp = new Vector3();\n\tconst projectedPointTemp = new Vector3();\n\tconst planeTemp = new Plane();\n\tconst lineTemp = new Line3();\n\treturn function sphereIntersectTriangle( sphere, triangle ) {\n\n\t\tconst { radius, center } = sphere;\n\t\tconst { a, b, c } = triangle;\n\n\t\t// phase 1\n\t\tlineTemp.start = a;\n\t\tlineTemp.end = b;\n\t\tconst closestPoint1 = lineTemp.closestPointToPoint( center, true, closestPointTemp );\n\t\tif ( closestPoint1.distanceTo( center ) <= radius ) return true;\n\n\t\tlineTemp.start = a;\n\t\tlineTemp.end = c;\n\t\tconst closestPoint2 = lineTemp.closestPointToPoint( center, true, closestPointTemp );\n\t\tif ( closestPoint2.distanceTo( center ) <= radius ) return true;\n\n\t\tlineTemp.start = b;\n\t\tlineTemp.end = c;\n\t\tconst closestPoint3 = lineTemp.closestPointToPoint( center, true, closestPointTemp );\n\t\tif ( closestPoint3.distanceTo( center ) <= radius ) return true;\n\n\t\t// phase 2\n\t\tconst plane = triangle.getPlane( planeTemp );\n\t\tconst dp = Math.abs( plane.distanceToPoint( center ) );\n\t\tif ( dp <= radius ) {\n\n\t\t\tconst pp = plane.projectPoint( center, projectedPointTemp );\n\t\t\tconst cp = triangle.containsPoint( pp );\n\t\t\tif ( cp ) return true;\n\n\t\t}\n\n\t\treturn false;\n\n\t};\n\n} )();\n", "import { Triangle, Vector3, Line3, Sphere, Plane } from 'three';\nimport { SeparatingAxisBounds } from './SeparatingAxisBounds.js';\nimport { closestPointsSegmentToSegment, sphereIntersectTriangle } from './MathUtilities.js';\n\nconst ZERO_EPSILON = 1e-15;\nfunction isNearZero( value ) {\n\n\treturn Math.abs( value ) < ZERO_EPSILON;\n\n}\n\nexport class ExtendedTriangle extends Triangle {\n\n\tconstructor( ...args ) {\n\n\t\tsuper( ...args );\n\n\t\tthis.isExtendedTriangle = true;\n\t\tthis.satAxes = new Array( 4 ).fill().map( () => new Vector3() );\n\t\tthis.satBounds = new Array( 4 ).fill().map( () => new SeparatingAxisBounds() );\n\t\tthis.points = [ this.a, this.b, this.c ];\n\t\tthis.sphere = new Sphere();\n\t\tthis.plane = new Plane();\n\t\tthis.needsUpdate = true;\n\n\t}\n\n\tintersectsSphere( sphere ) {\n\n\t\treturn sphereIntersectTriangle( sphere, this );\n\n\t}\n\n\tupdate() {\n\n\t\tconst a = this.a;\n\t\tconst b = this.b;\n\t\tconst c = this.c;\n\t\tconst points = this.points;\n\n\t\tconst satAxes = this.satAxes;\n\t\tconst satBounds = this.satBounds;\n\n\t\tconst axis0 = satAxes[ 0 ];\n\t\tconst sab0 = satBounds[ 0 ];\n\t\tthis.getNormal( axis0 );\n\t\tsab0.setFromPoints( axis0, points );\n\n\t\tconst axis1 = satAxes[ 1 ];\n\t\tconst sab1 = satBounds[ 1 ];\n\t\taxis1.subVectors( a, b );\n\t\tsab1.setFromPoints( axis1, points );\n\n\t\tconst axis2 = satAxes[ 2 ];\n\t\tconst sab2 = satBounds[ 2 ];\n\t\taxis2.subVectors( b, c );\n\t\tsab2.setFromPoints( axis2, points );\n\n\t\tconst axis3 = satAxes[ 3 ];\n\t\tconst sab3 = satBounds[ 3 ];\n\t\taxis3.subVectors( c, a );\n\t\tsab3.setFromPoints( axis3, points );\n\n\t\tthis.sphere.setFromPoints( this.points );\n\t\tthis.plane.setFromNormalAndCoplanarPoint( axis0, a );\n\t\tthis.needsUpdate = false;\n\n\t}\n\n}\n\nExtendedTriangle.prototype.closestPointToSegment = ( function () {\n\n\tconst point1 = new Vector3();\n\tconst point2 = new Vector3();\n\tconst edge = new Line3();\n\n\treturn function distanceToSegment( segment, target1 = null, target2 = null ) {\n\n\t\tconst { start, end } = segment;\n\t\tconst points = this.points;\n\t\tlet distSq;\n\t\tlet closestDistanceSq = Infinity;\n\n\t\t// check the triangle edges\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst nexti = ( i + 1 ) % 3;\n\t\t\tedge.start.copy( points[ i ] );\n\t\t\tedge.end.copy( points[ nexti ] );\n\n\t\t\tclosestPointsSegmentToSegment( edge, segment, point1, point2 );\n\n\t\t\tdistSq = point1.distanceToSquared( point2 );\n\t\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = distSq;\n\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// check end points\n\t\tthis.closestPointToPoint( start, point1 );\n\t\tdistSq = start.distanceToSquared( point1 );\n\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\tclosestDistanceSq = distSq;\n\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\tif ( target2 ) target2.copy( start );\n\n\t\t}\n\n\t\tthis.closestPointToPoint( end, point1 );\n\t\tdistSq = end.distanceToSquared( point1 );\n\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\tclosestDistanceSq = distSq;\n\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\tif ( target2 ) target2.copy( end );\n\n\t\t}\n\n\t\treturn Math.sqrt( closestDistanceSq );\n\n\t};\n\n} )();\n\nExtendedTriangle.prototype.intersectsTriangle = ( function () {\n\n\tconst saTri2 = new ExtendedTriangle();\n\tconst arr1 = new Array( 3 );\n\tconst arr2 = new Array( 3 );\n\tconst cachedSatBounds = new SeparatingAxisBounds();\n\tconst cachedSatBounds2 = new SeparatingAxisBounds();\n\tconst cachedAxis = new Vector3();\n\tconst dir = new Vector3();\n\tconst dir1 = new Vector3();\n\tconst dir2 = new Vector3();\n\tconst tempDir = new Vector3();\n\tconst edge = new Line3();\n\tconst edge1 = new Line3();\n\tconst edge2 = new Line3();\n\tconst tempPoint = new Vector3();\n\n\tfunction triIntersectPlane( tri, plane, targetEdge ) {\n\n\t\t// find the edge that intersects the other triangle plane\n\t\tconst points = tri.points;\n\t\tlet count = 0;\n\t\tlet startPointIntersection = - 1;\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst { start, end } = edge;\n\t\t\tstart.copy( points[ i ] );\n\t\t\tend.copy( points[ ( i + 1 ) % 3 ] );\n\t\t\tedge.delta( dir );\n\n\t\t\tconst startIntersects = isNearZero( plane.distanceToPoint( start ) );\n\t\t\tif ( isNearZero( plane.normal.dot( dir ) ) && startIntersects ) {\n\n\t\t\t\t// if the edge lies on the plane then take the line\n\t\t\t\ttargetEdge.copy( edge );\n\t\t\t\tcount = 2;\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\t// check if the start point is near the plane because \"intersectLine\" is not robust to that case\n\t\t\tconst doesIntersect = plane.intersectLine( edge, tempPoint );\n\t\t\tif ( ! doesIntersect && startIntersects ) {\n\n\t\t\t\ttempPoint.copy( start );\n\n\t\t\t}\n\n\t\t\t// ignore the end point\n\t\t\tif ( ( doesIntersect || startIntersects ) && ! isNearZero( tempPoint.distanceTo( end ) ) ) {\n\n\t\t\t\tif ( count <= 1 ) {\n\n\t\t\t\t\t// assign to the start or end point and save which index was snapped to\n\t\t\t\t\t// the start point if necessary\n\t\t\t\t\tconst point = count === 1 ? targetEdge.start : targetEdge.end;\n\t\t\t\t\tpoint.copy( tempPoint );\n\t\t\t\t\tif ( startIntersects ) {\n\n\t\t\t\t\t\tstartPointIntersection = count;\n\n\t\t\t\t\t}\n\n\t\t\t\t} else if ( count >= 2 ) {\n\n\t\t\t\t\t// if we're here that means that there must have been one point that had\n\t\t\t\t\t// snapped to the start point so replace it here\n\t\t\t\t\tconst point = startPointIntersection === 1 ? targetEdge.start : targetEdge.end;\n\t\t\t\t\tpoint.copy( tempPoint );\n\t\t\t\t\tcount = 2;\n\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t\tcount ++;\n\t\t\t\tif ( count === 2 && startPointIntersection === - 1 ) {\n\n\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn count;\n\n\t}\n\n\t// TODO: If the triangles are coplanar and intersecting the target is nonsensical. It should at least\n\t// be a line contained by both triangles if not a different special case somehow represented in the return result.\n\treturn function intersectsTriangle( other, target = null, suppressLog = false ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tif ( ! other.isExtendedTriangle ) {\n\n\t\t\tsaTri2.copy( other );\n\t\t\tsaTri2.update();\n\t\t\tother = saTri2;\n\n\t\t} else if ( other.needsUpdate ) {\n\n\t\t\tother.update();\n\n\t\t}\n\n\t\tconst plane1 = this.plane;\n\t\tconst plane2 = other.plane;\n\n\t\tif ( Math.abs( plane1.normal.dot( plane2.normal ) ) > 1.0 - 1e-10 ) {\n\n\t\t\t// perform separating axis intersection test only for coplanar triangles\n\t\t\tconst satBounds1 = this.satBounds;\n\t\t\tconst satAxes1 = this.satAxes;\n\t\t\tarr2[ 0 ] = other.a;\n\t\t\tarr2[ 1 ] = other.b;\n\t\t\tarr2[ 2 ] = other.c;\n\t\t\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\t\t\tconst sb = satBounds1[ i ];\n\t\t\t\tconst sa = satAxes1[ i ];\n\t\t\t\tcachedSatBounds.setFromPoints( sa, arr2 );\n\t\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t\t}\n\n\t\t\tconst satBounds2 = other.satBounds;\n\t\t\tconst satAxes2 = other.satAxes;\n\t\t\tarr1[ 0 ] = this.a;\n\t\t\tarr1[ 1 ] = this.b;\n\t\t\tarr1[ 2 ] = this.c;\n\t\t\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\t\t\tconst sb = satBounds2[ i ];\n\t\t\t\tconst sa = satAxes2[ i ];\n\t\t\t\tcachedSatBounds.setFromPoints( sa, arr1 );\n\t\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t\t}\n\n\t\t\t// check crossed axes\n\t\t\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\t\t\tconst sa1 = satAxes1[ i ];\n\t\t\t\tfor ( let i2 = 0; i2 < 4; i2 ++ ) {\n\n\t\t\t\t\tconst sa2 = satAxes2[ i2 ];\n\t\t\t\t\tcachedAxis.crossVectors( sa1, sa2 );\n\t\t\t\t\tcachedSatBounds.setFromPoints( cachedAxis, arr1 );\n\t\t\t\t\tcachedSatBounds2.setFromPoints( cachedAxis, arr2 );\n\t\t\t\t\tif ( cachedSatBounds.isSeparated( cachedSatBounds2 ) ) return false;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( target ) {\n\n\t\t\t\t// TODO find two points that intersect on the edges and make that the result\n\t\t\t\tif ( ! suppressLog ) {\n\n\t\t\t\t\tconsole.warn( 'ExtendedTriangle.intersectsTriangle: Triangles are coplanar which does not support an output edge. Setting edge to 0, 0, 0.' );\n\n\t\t\t\t}\n\n\t\t\t\ttarget.start.set( 0, 0, 0 );\n\t\t\t\ttarget.end.set( 0, 0, 0 );\n\n\t\t\t}\n\n\t\t\treturn true;\n\n\t\t} else {\n\n\t\t\t// find the edge that intersects the other triangle plane\n\t\t\tconst count1 = triIntersectPlane( this, plane2, edge1 );\n\t\t\tif ( count1 === 1 && other.containsPoint( edge1.end ) ) {\n\n\t\t\t\tif ( target ) {\n\n\t\t\t\t\ttarget.start.copy( edge1.end );\n\t\t\t\t\ttarget.end.copy( edge1.end );\n\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\n\t\t\t} else if ( count1 !== 2 ) {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\t// find the other triangles edge that intersects this plane\n\t\t\tconst count2 = triIntersectPlane( other, plane1, edge2 );\n\t\t\tif ( count2 === 1 && this.containsPoint( edge2.end ) ) {\n\n\t\t\t\tif ( target ) {\n\n\t\t\t\t\ttarget.start.copy( edge2.end );\n\t\t\t\t\ttarget.end.copy( edge2.end );\n\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\n\t\t\t} else if ( count2 !== 2 ) {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\t// find swap the second edge so both lines are running the same direction\n\t\t\tedge1.delta( dir1 );\n\t\t\tedge2.delta( dir2 );\n\n\t\t\tif ( dir1.dot( dir2 ) < 0 ) {\n\n\t\t\t\tlet tmp = edge2.start;\n\t\t\t\tedge2.start = edge2.end;\n\t\t\t\tedge2.end = tmp;\n\n\t\t\t}\n\n\t\t\t// check if the edges are overlapping\n\t\t\tconst s1 = edge1.start.dot( dir1 );\n\t\t\tconst e1 = edge1.end.dot( dir1 );\n\t\t\tconst s2 = edge2.start.dot( dir1 );\n\t\t\tconst e2 = edge2.end.dot( dir1 );\n\t\t\tconst separated1 = e1 < s2;\n\t\t\tconst separated2 = s1 < e2;\n\n\t\t\tif ( s1 !== e2 && s2 !== e1 && separated1 === separated2 ) {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\t// assign the target output\n\t\t\tif ( target ) {\n\n\t\t\t\ttempDir.subVectors( edge1.start, edge2.start );\n\t\t\t\tif ( tempDir.dot( dir1 ) > 0 ) {\n\n\t\t\t\t\ttarget.start.copy( edge1.start );\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttarget.start.copy( edge2.start );\n\n\t\t\t\t}\n\n\t\t\t\ttempDir.subVectors( edge1.end, edge2.end );\n\t\t\t\tif ( tempDir.dot( dir1 ) < 0 ) {\n\n\t\t\t\t\ttarget.end.copy( edge1.end );\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttarget.end.copy( edge2.end );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn true;\n\n\t\t}\n\n\t};\n\n} )();\n\n\nExtendedTriangle.prototype.distanceToPoint = ( function () {\n\n\tconst target = new Vector3();\n\treturn function distanceToPoint( point ) {\n\n\t\tthis.closestPointToPoint( point, target );\n\t\treturn point.distanceTo( target );\n\n\t};\n\n} )();\n\n\nExtendedTriangle.prototype.distanceToTriangle = ( function () {\n\n\tconst point = new Vector3();\n\tconst point2 = new Vector3();\n\tconst cornerFields = [ 'a', 'b', 'c' ];\n\tconst line1 = new Line3();\n\tconst line2 = new Line3();\n\n\treturn function distanceToTriangle( other, target1 = null, target2 = null ) {\n\n\t\tconst lineTarget = target1 || target2 ? line1 : null;\n\t\tif ( this.intersectsTriangle( other, lineTarget ) ) {\n\n\t\t\tif ( target1 || target2 ) {\n\n\t\t\t\tif ( target1 ) lineTarget.getCenter( target1 );\n\t\t\t\tif ( target2 ) lineTarget.getCenter( target2 );\n\n\t\t\t}\n\n\t\t\treturn 0;\n\n\t\t}\n\n\t\tlet closestDistanceSq = Infinity;\n\n\t\t// check all point distances\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tlet dist;\n\t\t\tconst field = cornerFields[ i ];\n\t\t\tconst otherVec = other[ field ];\n\t\t\tthis.closestPointToPoint( otherVec, point );\n\n\t\t\tdist = otherVec.distanceToSquared( point );\n\n\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\tif ( target1 ) target1.copy( point );\n\t\t\t\tif ( target2 ) target2.copy( otherVec );\n\n\t\t\t}\n\n\n\t\t\tconst thisVec = this[ field ];\n\t\t\tother.closestPointToPoint( thisVec, point );\n\n\t\t\tdist = thisVec.distanceToSquared( point );\n\n\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\tif ( target1 ) target1.copy( thisVec );\n\t\t\t\tif ( target2 ) target2.copy( point );\n\n\t\t\t}\n\n\t\t}\n\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst f11 = cornerFields[ i ];\n\t\t\tconst f12 = cornerFields[ ( i + 1 ) % 3 ];\n\t\t\tline1.set( this[ f11 ], this[ f12 ] );\n\t\t\tfor ( let i2 = 0; i2 < 3; i2 ++ ) {\n\n\t\t\t\tconst f21 = cornerFields[ i2 ];\n\t\t\t\tconst f22 = cornerFields[ ( i2 + 1 ) % 3 ];\n\t\t\t\tline2.set( other[ f21 ], other[ f22 ] );\n\n\t\t\t\tclosestPointsSegmentToSegment( line1, line2, point, point2 );\n\n\t\t\t\tconst dist = point.distanceToSquared( point2 );\n\t\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\t\tif ( target1 ) target1.copy( point );\n\t\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Math.sqrt( closestDistanceSq );\n\n\t};\n\n} )();\n", "import { Vector3, Matrix4, Line3 } from 'three';\nimport { SeparatingAxisBounds } from './SeparatingAxisBounds.js';\nimport { ExtendedTriangle } from './ExtendedTriangle.js';\nimport { closestPointsSegmentToSegment } from './MathUtilities.js';\n\nexport class OrientedBox {\n\n\tconstructor( min, max, matrix ) {\n\n\t\tthis.isOrientedBox = true;\n\t\tthis.min = new Vector3();\n\t\tthis.max = new Vector3();\n\t\tthis.matrix = new Matrix4();\n\t\tthis.invMatrix = new Matrix4();\n\t\tthis.points = new Array( 8 ).fill().map( () => new Vector3() );\n\t\tthis.satAxes = new Array( 3 ).fill().map( () => new Vector3() );\n\t\tthis.satBounds = new Array( 3 ).fill().map( () => new SeparatingAxisBounds() );\n\t\tthis.alignedSatBounds = new Array( 3 ).fill().map( () => new SeparatingAxisBounds() );\n\t\tthis.needsUpdate = false;\n\n\t\tif ( min ) this.min.copy( min );\n\t\tif ( max ) this.max.copy( max );\n\t\tif ( matrix ) this.matrix.copy( matrix );\n\n\t}\n\n\tset( min, max, matrix ) {\n\n\t\tthis.min.copy( min );\n\t\tthis.max.copy( max );\n\t\tthis.matrix.copy( matrix );\n\t\tthis.needsUpdate = true;\n\n\t}\n\n\tcopy( other ) {\n\n\t\tthis.min.copy( other.min );\n\t\tthis.max.copy( other.max );\n\t\tthis.matrix.copy( other.matrix );\n\t\tthis.needsUpdate = true;\n\n\t}\n\n}\n\nOrientedBox.prototype.update = ( function () {\n\n\treturn function update() {\n\n\t\tconst matrix = this.matrix;\n\t\tconst min = this.min;\n\t\tconst max = this.max;\n\n\t\tconst points = this.points;\n\t\tfor ( let x = 0; x <= 1; x ++ ) {\n\n\t\t\tfor ( let y = 0; y <= 1; y ++ ) {\n\n\t\t\t\tfor ( let z = 0; z <= 1; z ++ ) {\n\n\t\t\t\t\tconst i = ( ( 1 << 0 ) * x ) | ( ( 1 << 1 ) * y ) | ( ( 1 << 2 ) * z );\n\t\t\t\t\tconst v = points[ i ];\n\t\t\t\t\tv.x = x ? max.x : min.x;\n\t\t\t\t\tv.y = y ? max.y : min.y;\n\t\t\t\t\tv.z = z ? max.z : min.z;\n\n\t\t\t\t\tv.applyMatrix4( matrix );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tconst satBounds = this.satBounds;\n\t\tconst satAxes = this.satAxes;\n\t\tconst minVec = points[ 0 ];\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst axis = satAxes[ i ];\n\t\t\tconst sb = satBounds[ i ];\n\t\t\tconst index = 1 << i;\n\t\t\tconst pi = points[ index ];\n\n\t\t\taxis.subVectors( minVec, pi );\n\t\t\tsb.setFromPoints( axis, points );\n\n\t\t}\n\n\t\tconst alignedSatBounds = this.alignedSatBounds;\n\t\talignedSatBounds[ 0 ].setFromPointsField( points, 'x' );\n\t\talignedSatBounds[ 1 ].setFromPointsField( points, 'y' );\n\t\talignedSatBounds[ 2 ].setFromPointsField( points, 'z' );\n\n\t\tthis.invMatrix.copy( this.matrix ).invert();\n\t\tthis.needsUpdate = false;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.intersectsBox = ( function () {\n\n\tconst aabbBounds = new SeparatingAxisBounds();\n\treturn function intersectsBox( box ) {\n\n\t\t// TODO: should this be doing SAT against the AABB?\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tconst min = box.min;\n\t\tconst max = box.max;\n\t\tconst satBounds = this.satBounds;\n\t\tconst satAxes = this.satAxes;\n\t\tconst alignedSatBounds = this.alignedSatBounds;\n\n\t\taabbBounds.min = min.x;\n\t\taabbBounds.max = max.x;\n\t\tif ( alignedSatBounds[ 0 ].isSeparated( aabbBounds ) ) return false;\n\n\t\taabbBounds.min = min.y;\n\t\taabbBounds.max = max.y;\n\t\tif ( alignedSatBounds[ 1 ].isSeparated( aabbBounds ) ) return false;\n\n\t\taabbBounds.min = min.z;\n\t\taabbBounds.max = max.z;\n\t\tif ( alignedSatBounds[ 2 ].isSeparated( aabbBounds ) ) return false;\n\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst axis = satAxes[ i ];\n\t\t\tconst sb = satBounds[ i ];\n\t\t\taabbBounds.setFromBox( axis, box );\n\t\t\tif ( sb.isSeparated( aabbBounds ) ) return false;\n\n\t\t}\n\n\t\treturn true;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.intersectsTriangle = ( function () {\n\n\tconst saTri = new ExtendedTriangle();\n\tconst pointsArr = new Array( 3 );\n\tconst cachedSatBounds = new SeparatingAxisBounds();\n\tconst cachedSatBounds2 = new SeparatingAxisBounds();\n\tconst cachedAxis = new Vector3();\n\treturn function intersectsTriangle( triangle ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tif ( ! triangle.isExtendedTriangle ) {\n\n\t\t\tsaTri.copy( triangle );\n\t\t\tsaTri.update();\n\t\t\ttriangle = saTri;\n\n\t\t} else if ( triangle.needsUpdate ) {\n\n\t\t\ttriangle.update();\n\n\t\t}\n\n\t\tconst satBounds = this.satBounds;\n\t\tconst satAxes = this.satAxes;\n\n\t\tpointsArr[ 0 ] = triangle.a;\n\t\tpointsArr[ 1 ] = triangle.b;\n\t\tpointsArr[ 2 ] = triangle.c;\n\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = satBounds[ i ];\n\t\t\tconst sa = satAxes[ i ];\n\t\t\tcachedSatBounds.setFromPoints( sa, pointsArr );\n\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t}\n\n\t\tconst triSatBounds = triangle.satBounds;\n\t\tconst triSatAxes = triangle.satAxes;\n\t\tconst points = this.points;\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = triSatBounds[ i ];\n\t\t\tconst sa = triSatAxes[ i ];\n\t\t\tcachedSatBounds.setFromPoints( sa, points );\n\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t}\n\n\t\t// check crossed axes\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sa1 = satAxes[ i ];\n\t\t\tfor ( let i2 = 0; i2 < 4; i2 ++ ) {\n\n\t\t\t\tconst sa2 = triSatAxes[ i2 ];\n\t\t\t\tcachedAxis.crossVectors( sa1, sa2 );\n\t\t\t\tcachedSatBounds.setFromPoints( cachedAxis, pointsArr );\n\t\t\t\tcachedSatBounds2.setFromPoints( cachedAxis, points );\n\t\t\t\tif ( cachedSatBounds.isSeparated( cachedSatBounds2 ) ) return false;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn true;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.closestPointToPoint = ( function () {\n\n\treturn function closestPointToPoint( point, target1 ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\ttarget1\n\t\t\t.copy( point )\n\t\t\t.applyMatrix4( this.invMatrix )\n\t\t\t.clamp( this.min, this.max )\n\t\t\t.applyMatrix4( this.matrix );\n\n\t\treturn target1;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.distanceToPoint = ( function () {\n\n\tconst target = new Vector3();\n\treturn function distanceToPoint( point ) {\n\n\t\tthis.closestPointToPoint( point, target );\n\t\treturn point.distanceTo( target );\n\n\t};\n\n} )();\n\nOrientedBox.prototype.distanceToBox = ( function () {\n\n\tconst xyzFields = [ 'x', 'y', 'z' ];\n\tconst segments1 = new Array( 12 ).fill().map( () => new Line3() );\n\tconst segments2 = new Array( 12 ).fill().map( () => new Line3() );\n\n\tconst point1 = new Vector3();\n\tconst point2 = new Vector3();\n\n\t// early out if we find a value below threshold\n\treturn function distanceToBox( box, threshold = 0, target1 = null, target2 = null ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tif ( this.intersectsBox( box ) ) {\n\n\t\t\tif ( target1 || target2 ) {\n\n\t\t\t\tbox.getCenter( point2 );\n\t\t\t\tthis.closestPointToPoint( point2, point1 );\n\t\t\t\tbox.closestPointToPoint( point1, point2 );\n\n\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t}\n\n\t\t\treturn 0;\n\n\t\t}\n\n\t\tconst threshold2 = threshold * threshold;\n\t\tconst min = box.min;\n\t\tconst max = box.max;\n\t\tconst points = this.points;\n\n\n\t\t// iterate over every edge and compare distances\n\t\tlet closestDistanceSq = Infinity;\n\n\t\t// check over all these points\n\t\tfor ( let i = 0; i < 8; i ++ ) {\n\n\t\t\tconst p = points[ i ];\n\t\t\tpoint2.copy( p ).clamp( min, max );\n\n\t\t\tconst dist = p.distanceToSquared( point2 );\n\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\tif ( target1 ) target1.copy( p );\n\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\tif ( dist < threshold2 ) return Math.sqrt( dist );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// generate and check all line segment distances\n\t\tlet count = 0;\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tfor ( let i1 = 0; i1 <= 1; i1 ++ ) {\n\n\t\t\t\tfor ( let i2 = 0; i2 <= 1; i2 ++ ) {\n\n\t\t\t\t\tconst nextIndex = ( i + 1 ) % 3;\n\t\t\t\t\tconst nextIndex2 = ( i + 2 ) % 3;\n\n\t\t\t\t\t// get obb line segments\n\t\t\t\t\tconst index = i1 << nextIndex | i2 << nextIndex2;\n\t\t\t\t\tconst index2 = 1 << i | i1 << nextIndex | i2 << nextIndex2;\n\t\t\t\t\tconst p1 = points[ index ];\n\t\t\t\t\tconst p2 = points[ index2 ];\n\t\t\t\t\tconst line1 = segments1[ count ];\n\t\t\t\t\tline1.set( p1, p2 );\n\n\n\t\t\t\t\t// get aabb line segments\n\t\t\t\t\tconst f1 = xyzFields[ i ];\n\t\t\t\t\tconst f2 = xyzFields[ nextIndex ];\n\t\t\t\t\tconst f3 = xyzFields[ nextIndex2 ];\n\t\t\t\t\tconst line2 = segments2[ count ];\n\t\t\t\t\tconst start = line2.start;\n\t\t\t\t\tconst end = line2.end;\n\n\t\t\t\t\tstart[ f1 ] = min[ f1 ];\n\t\t\t\t\tstart[ f2 ] = i1 ? min[ f2 ] : max[ f2 ];\n\t\t\t\t\tstart[ f3 ] = i2 ? min[ f3 ] : max[ f2 ];\n\n\t\t\t\t\tend[ f1 ] = max[ f1 ];\n\t\t\t\t\tend[ f2 ] = i1 ? min[ f2 ] : max[ f2 ];\n\t\t\t\t\tend[ f3 ] = i2 ? min[ f3 ] : max[ f2 ];\n\n\t\t\t\t\tcount ++;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t// check all the other boxes point\n\t\tfor ( let x = 0; x <= 1; x ++ ) {\n\n\t\t\tfor ( let y = 0; y <= 1; y ++ ) {\n\n\t\t\t\tfor ( let z = 0; z <= 1; z ++ ) {\n\n\t\t\t\t\tpoint2.x = x ? max.x : min.x;\n\t\t\t\t\tpoint2.y = y ? max.y : min.y;\n\t\t\t\t\tpoint2.z = z ? max.z : min.z;\n\n\t\t\t\t\tthis.closestPointToPoint( point2, point1 );\n\t\t\t\t\tconst dist = point2.distanceToSquared( point1 );\n\t\t\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\t\t\tif ( dist < threshold2 ) return Math.sqrt( dist );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tfor ( let i = 0; i < 12; i ++ ) {\n\n\t\t\tconst l1 = segments1[ i ];\n\t\t\tfor ( let i2 = 0; i2 < 12; i2 ++ ) {\n\n\t\t\t\tconst l2 = segments2[ i2 ];\n\t\t\t\tclosestPointsSegmentToSegment( l1, l2, point1, point2 );\n\t\t\t\tconst dist = point1.distanceToSquared( point2 );\n\t\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\t\tif ( dist < threshold2 ) return Math.sqrt( dist );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Math.sqrt( closestDistanceSq );\n\n\t};\n\n} )();\n", "export class PrimitivePool {\n\n\tconstructor( getNewPrimitive ) {\n\n\t\tthis._getNewPrimitive = getNewPrimitive;\n\t\tthis._primitives = [];\n\n\t}\n\n\tgetPrimitive() {\n\n\t\tconst primitives = this._primitives;\n\t\tif ( primitives.length === 0 ) {\n\n\t\t\treturn this._getNewPrimitive();\n\n\t\t} else {\n\n\t\t\treturn primitives.pop();\n\n\t\t}\n\n\t}\n\n\treleasePrimitive( primitive ) {\n\n\t\tthis._primitives.push( primitive );\n\n\t}\n\n}\n", "import { ExtendedTriangle } from '../math/ExtendedTriangle.js';\nimport { PrimitivePool } from './PrimitivePool.js';\n\nclass ExtendedTrianglePoolBase extends PrimitivePool {\n\n\tconstructor() {\n\n\t\tsuper( () => new ExtendedTriangle() );\n\n\t}\n\n}\n\nexport const ExtendedTrianglePool = /* @__PURE__ */ new ExtendedTrianglePoolBase();\n", "class _BufferStack {\n\n\tconstructor() {\n\n\t\tthis.float32Array = null;\n\t\tthis.uint16Array = null;\n\t\tthis.uint32Array = null;\n\n\t\tconst stack = [];\n\t\tlet prevBuffer = null;\n\t\tthis.setBuffer = buffer => {\n\n\t\t\tif ( prevBuffer ) {\n\n\t\t\t\tstack.push( prevBuffer );\n\n\t\t\t}\n\n\t\t\tprevBuffer = buffer;\n\t\t\tthis.float32Array = new Float32Array( buffer );\n\t\t\tthis.uint16Array = new Uint16Array( buffer );\n\t\t\tthis.uint32Array = new Uint32Array( buffer );\n\n\t\t};\n\n\t\tthis.clearBuffer = () => {\n\n\t\t\tprevBuffer = null;\n\t\t\tthis.float32Array = null;\n\t\t\tthis.uint16Array = null;\n\t\t\tthis.uint32Array = null;\n\n\t\t\tif ( stack.length !== 0 ) {\n\n\t\t\t\tthis.setBuffer( stack.pop() );\n\n\t\t\t}\n\n\t\t};\n\n\t}\n\n}\n\nexport const BufferStack = new _BufferStack();\n", "import { Box3 } from 'three';\nimport { CONTAINED } from '../Constants.js';\nimport { arrayToBox } from '../../utils/ArrayBoxUtilities.js';\nimport { PrimitivePool } from '../../utils/PrimitivePool.js';\nimport { COUNT, OFFSET, LEFT_NODE, RIGHT_NODE, IS_LEAF, BOUNDING_DATA_INDEX } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\n\nlet _box1, _box2;\nconst boxStack = [];\nconst boxPool = /* @__PURE__ */ new PrimitivePool( () => new Box3() );\n\nexport function shapecast( bvh, root, intersectsBounds, intersectsRange, boundsTraverseOrder, byteOffset ) {\n\n\t// setup\n\t_box1 = boxPool.getPrimitive();\n\t_box2 = boxPool.getPrimitive();\n\tboxStack.push( _box1, _box2 );\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\n\tconst result = shapecastTraverse( 0, bvh.geometry, intersectsBounds, intersectsRange, boundsTraverseOrder, byteOffset );\n\n\t// cleanup\n\tBufferStack.clearBuffer();\n\tboxPool.releasePrimitive( _box1 );\n\tboxPool.releasePrimitive( _box2 );\n\tboxStack.pop();\n\tboxStack.pop();\n\n\tconst length = boxStack.length;\n\tif ( length > 0 ) {\n\n\t\t_box2 = boxStack[ length - 1 ];\n\t\t_box1 = boxStack[ length - 2 ];\n\n\t}\n\n\treturn result;\n\n}\n\nfunction shapecastTraverse(\n\tnodeIndex32,\n\tgeometry,\n\tintersectsBoundsFunc,\n\tintersectsRangeFunc,\n\tnodeScoreFunc = null,\n\tnodeIndexByteOffset = 0, // offset for unique node identifier\n\tdepth = 0\n) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\t\tarrayToBox( BOUNDING_DATA_INDEX( nodeIndex32 ), float32Array, _box1 );\n\t\treturn intersectsRangeFunc( offset, count, false, depth, nodeIndexByteOffset + nodeIndex32, _box1 );\n\n\t} else {\n\n\t\tconst left = LEFT_NODE( nodeIndex32 );\n\t\tconst right = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\tlet c1 = left;\n\t\tlet c2 = right;\n\n\t\tlet score1, score2;\n\t\tlet box1, box2;\n\t\tif ( nodeScoreFunc ) {\n\n\t\t\tbox1 = _box1;\n\t\t\tbox2 = _box2;\n\n\t\t\t// bounding data is not offset\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c1 ), float32Array, box1 );\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c2 ), float32Array, box2 );\n\n\t\t\tscore1 = nodeScoreFunc( box1 );\n\t\t\tscore2 = nodeScoreFunc( box2 );\n\n\t\t\tif ( score2 < score1 ) {\n\n\t\t\t\tc1 = right;\n\t\t\t\tc2 = left;\n\n\t\t\t\tconst temp = score1;\n\t\t\t\tscore1 = score2;\n\t\t\t\tscore2 = temp;\n\n\t\t\t\tbox1 = box2;\n\t\t\t\t// box2 is always set before use below\n\n\t\t\t}\n\n\t\t}\n\n\t\t// Check box 1 intersection\n\t\tif ( ! box1 ) {\n\n\t\t\tbox1 = _box1;\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c1 ), float32Array, box1 );\n\n\t\t}\n\n\t\tconst isC1Leaf = IS_LEAF( c1 * 2, uint16Array );\n\t\tconst c1Intersection = intersectsBoundsFunc( box1, isC1Leaf, score1, depth + 1, nodeIndexByteOffset + c1 );\n\n\t\tlet c1StopTraversal;\n\t\tif ( c1Intersection === CONTAINED ) {\n\n\t\t\tconst offset = getLeftOffset( c1 );\n\t\t\tconst end = getRightEndOffset( c1 );\n\t\t\tconst count = end - offset;\n\n\t\t\tc1StopTraversal = intersectsRangeFunc( offset, count, true, depth + 1, nodeIndexByteOffset + c1, box1 );\n\n\t\t} else {\n\n\t\t\tc1StopTraversal =\n\t\t\t\tc1Intersection &&\n\t\t\t\tshapecastTraverse(\n\t\t\t\t\tc1,\n\t\t\t\t\tgeometry,\n\t\t\t\t\tintersectsBoundsFunc,\n\t\t\t\t\tintersectsRangeFunc,\n\t\t\t\t\tnodeScoreFunc,\n\t\t\t\t\tnodeIndexByteOffset,\n\t\t\t\t\tdepth + 1\n\t\t\t\t);\n\n\t\t}\n\n\t\tif ( c1StopTraversal ) return true;\n\n\t\t// Check box 2 intersection\n\t\t// cached box2 will have been overwritten by previous traversal\n\t\tbox2 = _box2;\n\t\tarrayToBox( BOUNDING_DATA_INDEX( c2 ), float32Array, box2 );\n\n\t\tconst isC2Leaf = IS_LEAF( c2 * 2, uint16Array );\n\t\tconst c2Intersection = intersectsBoundsFunc( box2, isC2Leaf, score2, depth + 1, nodeIndexByteOffset + c2 );\n\n\t\tlet c2StopTraversal;\n\t\tif ( c2Intersection === CONTAINED ) {\n\n\t\t\tconst offset = getLeftOffset( c2 );\n\t\t\tconst end = getRightEndOffset( c2 );\n\t\t\tconst count = end - offset;\n\n\t\t\tc2StopTraversal = intersectsRangeFunc( offset, count, true, depth + 1, nodeIndexByteOffset + c2, box2 );\n\n\t\t} else {\n\n\t\t\tc2StopTraversal =\n\t\t\t\tc2Intersection &&\n\t\t\t\tshapecastTraverse(\n\t\t\t\t\tc2,\n\t\t\t\t\tgeometry,\n\t\t\t\t\tintersectsBoundsFunc,\n\t\t\t\t\tintersectsRangeFunc,\n\t\t\t\t\tnodeScoreFunc,\n\t\t\t\t\tnodeIndexByteOffset,\n\t\t\t\t\tdepth + 1\n\t\t\t\t);\n\n\t\t}\n\n\t\tif ( c2StopTraversal ) return true;\n\n\t\treturn false;\n\n\t\t// Define these inside the function so it has access to the local variables needed\n\t\t// when converting to the buffer equivalents\n\t\tfunction getLeftOffset( nodeIndex32 ) {\n\n\t\t\tconst { uint16Array, uint32Array } = BufferStack;\n\t\t\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t// traverse until we find a leaf\n\t\t\twhile ( ! IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\t\tnodeIndex32 = LEFT_NODE( nodeIndex32 );\n\t\t\t\tnodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t}\n\n\t\t\treturn OFFSET( nodeIndex32, uint32Array );\n\n\t\t}\n\n\t\tfunction getRightEndOffset( nodeIndex32 ) {\n\n\t\t\tconst { uint16Array, uint32Array } = BufferStack;\n\t\t\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t// traverse until we find a leaf\n\t\t\twhile ( ! IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\t\t// adjust offset to point to the right node\n\t\t\t\tnodeIndex32 = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\t\t\tnodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t}\n\n\t\t\t// return the end offset of the triangle range\n\t\t\treturn OFFSET( nodeIndex32, uint32Array ) + COUNT( nodeIndex16, uint16Array );\n\n\t\t}\n\n\t}\n\n}\n", "import { Vector3 } from 'three';\n\nconst temp = /* @__PURE__ */ new Vector3();\nconst temp1 = /* @__PURE__ */ new Vector3();\n\nexport function closestPointToPoint(\n\tbvh,\n\tpoint,\n\ttarget = { },\n\tminThreshold = 0,\n\tmaxThreshold = Infinity,\n) {\n\n\t// early out if under minThreshold\n\t// skip checking if over maxThreshold\n\t// set minThreshold = maxThreshold to quickly check if a point is within a threshold\n\t// returns Infinity if no value found\n\tconst minThresholdSq = minThreshold * minThreshold;\n\tconst maxThresholdSq = maxThreshold * maxThreshold;\n\tlet closestDistanceSq = Infinity;\n\tlet closestDistanceTriIndex = null;\n\tbvh.shapecast(\n\n\t\t{\n\n\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\ttemp.copy( point ).clamp( box.min, box.max );\n\t\t\t\treturn temp.distanceToSquared( point );\n\n\t\t\t},\n\n\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\treturn score < closestDistanceSq && score < maxThresholdSq;\n\n\t\t\t},\n\n\t\t\tintersectsTriangle: ( tri, triIndex ) => {\n\n\t\t\t\ttri.closestPointToPoint( point, temp );\n\t\t\t\tconst distSq = point.distanceToSquared( temp );\n\t\t\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\t\t\ttemp1.copy( temp );\n\t\t\t\t\tclosestDistanceSq = distSq;\n\t\t\t\t\tclosestDistanceTriIndex = triIndex;\n\n\t\t\t\t}\n\n\t\t\t\tif ( distSq < minThresholdSq ) {\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t} else {\n\n\t\t\t\t\treturn false;\n\n\t\t\t\t}\n\n\t\t\t},\n\n\t\t}\n\n\t);\n\n\tif ( closestDistanceSq === Infinity ) return null;\n\n\tconst closestDistance = Math.sqrt( closestDistanceSq );\n\n\tif ( ! target.point ) target.point = temp1.clone();\n\telse target.point.copy( temp1 );\n\ttarget.distance = closestDistance,\n\ttarget.faceIndex = closestDistanceTriIndex;\n\n\treturn target;\n\n}\n", "import { Vector3, Vector2, <PERSON>, DoubleSide, BackSide } from 'three';\n\n// Ripped and modified From THREE.js Mesh raycast\n// https://github.com/mrdoob/three.js/blob/0aa87c999fe61e216c1133fba7a95772b503eddf/src/objects/Mesh.js#L115\nconst _vA = /* @__PURE__ */ new Vector3();\nconst _vB = /* @__PURE__ */ new Vector3();\nconst _vC = /* @__PURE__ */ new Vector3();\n\nconst _uvA = /* @__PURE__ */ new Vector2();\nconst _uvB = /* @__PURE__ */ new Vector2();\nconst _uvC = /* @__PURE__ */ new Vector2();\n\nconst _normalA = /* @__PURE__ */ new Vector3();\nconst _normalB = /* @__PURE__ */ new Vector3();\nconst _normalC = /* @__PURE__ */ new Vector3();\n\nconst _intersectionPoint = /* @__PURE__ */ new Vector3();\nfunction checkIntersection( ray, pA, pB, pC, point, side, near, far ) {\n\n\tlet intersect;\n\tif ( side === BackSide ) {\n\n\t\tintersect = ray.intersectTriangle( pC, pB, pA, true, point );\n\n\t} else {\n\n\t\tintersect = ray.intersectTriangle( pA, pB, pC, side !== DoubleSide, point );\n\n\t}\n\n\tif ( intersect === null ) return null;\n\n\tconst distance = ray.origin.distanceTo( point );\n\n\tif ( distance < near || distance > far ) return null;\n\n\treturn {\n\n\t\tdistance: distance,\n\t\tpoint: point.clone(),\n\n\t};\n\n}\n\nfunction checkBufferGeometryIntersection( ray, position, normal, uv, uv1, a, b, c, side, near, far ) {\n\n\t_vA.fromBufferAttribute( position, a );\n\t_vB.fromBufferAttribute( position, b );\n\t_vC.fromBufferAttribute( position, c );\n\n\tconst intersection = checkIntersection( ray, _vA, _vB, _vC, _intersectionPoint, side, near, far );\n\n\tif ( intersection ) {\n\n\t\tif ( uv ) {\n\n\t\t\t_uvA.fromBufferAttribute( uv, a );\n\t\t\t_uvB.fromBufferAttribute( uv, b );\n\t\t\t_uvC.fromBufferAttribute( uv, c );\n\n\t\t\tintersection.uv = Triangle.getInterpolation( _intersectionPoint, _vA, _vB, _vC, _uvA, _uvB, _uvC, new Vector2() );\n\n\t\t}\n\n\t\tif ( uv1 ) {\n\n\t\t\t_uvA.fromBufferAttribute( uv1, a );\n\t\t\t_uvB.fromBufferAttribute( uv1, b );\n\t\t\t_uvC.fromBufferAttribute( uv1, c );\n\n\t\t\tintersection.uv1 = Triangle.getInterpolation( _intersectionPoint, _vA, _vB, _vC, _uvA, _uvB, _uvC, new Vector2() );\n\n\t\t}\n\n\t\tif ( normal ) {\n\n\t\t\t_normalA.fromBufferAttribute( normal, a );\n\t\t\t_normalB.fromBufferAttribute( normal, b );\n\t\t\t_normalC.fromBufferAttribute( normal, c );\n\n\t\t\tintersection.normal = Triangle.getInterpolation( _intersectionPoint, _vA, _vB, _vC, _normalA, _normalB, _normalC, new Vector3() );\n\t\t\tif ( intersection.normal.dot( ray.direction ) > 0 ) {\n\n\t\t\t\tintersection.normal.multiplyScalar( - 1 );\n\n\t\t\t}\n\n\t\t}\n\n\t\tconst face = {\n\t\t\ta: a,\n\t\t\tb: b,\n\t\t\tc: c,\n\t\t\tnormal: new Vector3(),\n\t\t\tmaterialIndex: 0\n\t\t};\n\n\t\tTriangle.getNormal( _vA, _vB, _vC, face.normal );\n\n\t\tintersection.face = face;\n\t\tintersection.faceIndex = a;\n\n\t}\n\n\treturn intersection;\n\n}\n\n// https://github.com/mrdoob/three.js/blob/0aa87c999fe61e216c1133fba7a95772b503eddf/src/objects/Mesh.js#L258\nfunction intersectTri( geo, side, ray, tri, intersections, near, far ) {\n\n\tconst triOffset = tri * 3;\n\tlet a = triOffset + 0;\n\tlet b = triOffset + 1;\n\tlet c = triOffset + 2;\n\n\tconst index = geo.index;\n\tif ( geo.index ) {\n\n\t\ta = index.getX( a );\n\t\tb = index.getX( b );\n\t\tc = index.getX( c );\n\n\t}\n\n\tconst { position, normal, uv, uv1 } = geo.attributes;\n\tconst intersection = checkBufferGeometryIntersection( ray, position, normal, uv, uv1, a, b, c, side, near, far );\n\n\tif ( intersection ) {\n\n\t\tintersection.faceIndex = tri;\n\t\tif ( intersections ) intersections.push( intersection );\n\t\treturn intersection;\n\n\t}\n\n\treturn null;\n\n}\n\nexport { intersectTri };\n", "\nimport { Vector2, Vector3, <PERSON> } from 'three';\n\n// sets the vertices of triangle `tri` with the 3 vertices after i\nexport function setTriangle( tri, i, index, pos ) {\n\n\tconst ta = tri.a;\n\tconst tb = tri.b;\n\tconst tc = tri.c;\n\n\tlet i0 = i;\n\tlet i1 = i + 1;\n\tlet i2 = i + 2;\n\tif ( index ) {\n\n\t\ti0 = index.getX( i0 );\n\t\ti1 = index.getX( i1 );\n\t\ti2 = index.getX( i2 );\n\n\t}\n\n\tta.x = pos.getX( i0 );\n\tta.y = pos.getY( i0 );\n\tta.z = pos.getZ( i0 );\n\n\ttb.x = pos.getX( i1 );\n\ttb.y = pos.getY( i1 );\n\ttb.z = pos.getZ( i1 );\n\n\ttc.x = pos.getX( i2 );\n\ttc.y = pos.getY( i2 );\n\ttc.z = pos.getZ( i2 );\n\n}\n\nconst tempV1 = /* @__PURE__ */ new Vector3();\nconst tempV2 = /* @__PURE__ */ new Vector3();\nconst tempV3 = /* @__PURE__ */ new Vector3();\nconst tempUV1 = /* @__PURE__ */ new Vector2();\nconst tempUV2 = /* @__PURE__ */ new Vector2();\nconst tempUV3 = /* @__PURE__ */ new Vector2();\n\nexport function getTriangleHitPointInfo( point, geometry, triangleIndex, target ) {\n\n\tconst indices = geometry.getIndex().array;\n\tconst positions = geometry.getAttribute( 'position' );\n\tconst uvs = geometry.getAttribute( 'uv' );\n\n\tconst a = indices[ triangleIndex * 3 ];\n\tconst b = indices[ triangleIndex * 3 + 1 ];\n\tconst c = indices[ triangleIndex * 3 + 2 ];\n\n\ttempV1.fromBufferAttribute( positions, a );\n\ttempV2.fromBufferAttribute( positions, b );\n\ttempV3.fromBufferAttribute( positions, c );\n\n\t// find the associated material index\n\tlet materialIndex = 0;\n\tconst groups = geometry.groups;\n\tconst firstVertexIndex = triangleIndex * 3;\n\tfor ( let i = 0, l = groups.length; i < l; i ++ ) {\n\n\t\tconst group = groups[ i ];\n\t\tconst { start, count } = group;\n\t\tif ( firstVertexIndex >= start && firstVertexIndex < start + count ) {\n\n\t\t\tmaterialIndex = group.materialIndex;\n\t\t\tbreak;\n\n\t\t}\n\n\t}\n\n\t// extract uvs\n\tlet uv = null;\n\tif ( uvs ) {\n\n\t\ttempUV1.fromBufferAttribute( uvs, a );\n\t\ttempUV2.fromBufferAttribute( uvs, b );\n\t\ttempUV3.fromBufferAttribute( uvs, c );\n\n\t\tif ( target && target.uv ) uv = target.uv;\n\t\telse uv = new Vector2();\n\n\t\tTriangle.getInterpolation( point, tempV1, tempV2, tempV3, tempUV1, tempUV2, tempUV3, uv );\n\n\t}\n\n\t// adjust the provided target or create a new one\n\tif ( target ) {\n\n\t\tif ( ! target.face ) target.face = { };\n\t\ttarget.face.a = a;\n\t\ttarget.face.b = b;\n\t\ttarget.face.c = c;\n\t\ttarget.face.materialIndex = materialIndex;\n\t\tif ( ! target.face.normal ) target.face.normal = new Vector3();\n\t\tTriangle.getNormal( tempV1, tempV2, tempV3, target.face.normal );\n\n\t\tif ( uv ) target.uv = uv;\n\n\t\treturn target;\n\n\t} else {\n\n\t\treturn {\n\t\t\tface: {\n\t\t\t\ta: a,\n\t\t\t\tb: b,\n\t\t\t\tc: c,\n\t\t\t\tmaterialIndex: materialIndex,\n\t\t\t\tnormal: Triangle.getNormal( tempV1, tempV2, tempV3, new Vector3() )\n\t\t\t},\n\t\t\tuv: uv\n\t\t};\n\n\t}\n\n}\n", "import { intersectTri } from '../../utils/ThreeRayIntersectUtilities.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\n\n/*************************************************************/\n/* This file is generated from \"iterationUtils.template.js\". */\n/*************************************************************/\n/* eslint-disable indent */\n\nfunction intersectTris( bvh, side, ray, offset, count, intersections, near, far ) {\n\n\tconst { geometry, _indirectBuffer } = bvh;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\n\t\tintersectTri( geometry, side, ray, i, intersections, near, far );\n\n\n\t}\n\n}\n\nfunction intersectClosestTri( bvh, side, ray, offset, count, near, far ) {\n\n\tconst { geometry, _indirectBuffer } = bvh;\n\tlet dist = Infinity;\n\tlet res = null;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tlet intersection;\n\n\t\tintersection = intersectTri( geometry, side, ray, i, null, near, far );\n\n\n\t\tif ( intersection && intersection.distance < dist ) {\n\n\t\t\tres = intersection;\n\t\t\tdist = intersection.distance;\n\n\t\t}\n\n\t}\n\n\treturn res;\n\n}\n\nfunction iterateOverTriangles(\n\toffset,\n\tcount,\n\tbvh,\n\tintersectsTriangleFunc,\n\tcontained,\n\tdepth,\n\ttriangle\n) {\n\n\tconst { geometry } = bvh;\n\tconst { index } = geometry;\n\tconst pos = geometry.attributes.position;\n\tfor ( let i = offset, l = count + offset; i < l; i ++ ) {\n\n\t\tlet tri;\n\n\t\ttri = i;\n\n\t\tsetTriangle( triangle, tri * 3, index, pos );\n\t\ttriangle.needsUpdate = true;\n\n\t\tif ( intersectsTriangleFunc( triangle, tri, contained, depth ) ) {\n\n\t\t\treturn true;\n\n\t\t}\n\n\t}\n\n\treturn false;\n\n}\n\nexport { intersectClosestTri, intersectTris, iterateOverTriangles };\n", "import { IS_LEAFNODE_FLAG } from '../Constants.js';\n\n/****************************************************/\n/* This file is generated from \"refit.template.js\". */\n/****************************************************/\n\nfunction refit( bvh, nodeIndices = null ) {\n\n\tif ( nodeIndices && Array.isArray( nodeIndices ) ) {\n\n\t\tnodeIndices = new Set( nodeIndices );\n\n\t}\n\n\tconst geometry = bvh.geometry;\n\tconst indexArr = geometry.index ? geometry.index.array : null;\n\tconst posAttr = geometry.attributes.position;\n\n\tlet buffer, uint32Array, uint16Array, float32Array;\n\tlet byteOffset = 0;\n\tconst roots = bvh._roots;\n\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\tbuffer = roots[ i ];\n\t\tuint32Array = new Uint32Array( buffer );\n\t\tuint16Array = new Uint16Array( buffer );\n\t\tfloat32Array = new Float32Array( buffer );\n\n\t\t_traverse( 0, byteOffset );\n\t\tbyteOffset += buffer.byteLength;\n\n\t}\n\n\tfunction _traverse( node32Index, byteOffset, force = false ) {\n\n\t\tconst node16Index = node32Index * 2;\n\t\tconst isLeaf = uint16Array[ node16Index + 15 ] === IS_LEAFNODE_FLAG;\n\t\tif ( isLeaf ) {\n\n\t\t\tconst offset = uint32Array[ node32Index + 6 ];\n\t\t\tconst count = uint16Array[ node16Index + 14 ];\n\n\t\t\tlet minx = Infinity;\n\t\t\tlet miny = Infinity;\n\t\t\tlet minz = Infinity;\n\t\t\tlet maxx = - Infinity;\n\t\t\tlet maxy = - Infinity;\n\t\t\tlet maxz = - Infinity;\n\n\n\t\t\tfor ( let i = 3 * offset, l = 3 * ( offset + count ); i < l; i ++ ) {\n\n\t\t\t\tlet index = indexArr[ i ];\n\t\t\t\tconst x = posAttr.getX( index );\n\t\t\t\tconst y = posAttr.getY( index );\n\t\t\t\tconst z = posAttr.getZ( index );\n\n\t\t\t\tif ( x < minx ) minx = x;\n\t\t\t\tif ( x > maxx ) maxx = x;\n\n\t\t\t\tif ( y < miny ) miny = y;\n\t\t\t\tif ( y > maxy ) maxy = y;\n\n\t\t\t\tif ( z < minz ) minz = z;\n\t\t\t\tif ( z > maxz ) maxz = z;\n\n\t\t\t}\n\n\n\t\t\tif (\n\t\t\t\tfloat32Array[ node32Index + 0 ] !== minx ||\n\t\t\t\tfloat32Array[ node32Index + 1 ] !== miny ||\n\t\t\t\tfloat32Array[ node32Index + 2 ] !== minz ||\n\n\t\t\t\tfloat32Array[ node32Index + 3 ] !== maxx ||\n\t\t\t\tfloat32Array[ node32Index + 4 ] !== maxy ||\n\t\t\t\tfloat32Array[ node32Index + 5 ] !== maxz\n\t\t\t) {\n\n\t\t\t\tfloat32Array[ node32Index + 0 ] = minx;\n\t\t\t\tfloat32Array[ node32Index + 1 ] = miny;\n\t\t\t\tfloat32Array[ node32Index + 2 ] = minz;\n\n\t\t\t\tfloat32Array[ node32Index + 3 ] = maxx;\n\t\t\t\tfloat32Array[ node32Index + 4 ] = maxy;\n\t\t\t\tfloat32Array[ node32Index + 5 ] = maxz;\n\n\t\t\t\treturn true;\n\n\t\t\t} else {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconst left = node32Index + 8;\n\t\t\tconst right = uint32Array[ node32Index + 6 ];\n\n\t\t\t// the identifying node indices provided by the shapecast function include offsets of all\n\t\t\t// root buffers to guarantee they're unique between roots so offset left and right indices here.\n\t\t\tconst offsetLeft = left + byteOffset;\n\t\t\tconst offsetRight = right + byteOffset;\n\t\t\tlet forceChildren = force;\n\t\t\tlet includesLeft = false;\n\t\t\tlet includesRight = false;\n\n\t\t\tif ( nodeIndices ) {\n\n\t\t\t\t// if we see that neither the left or right child are included in the set that need to be updated\n\t\t\t\t// then we assume that all children need to be updated.\n\t\t\t\tif ( ! forceChildren ) {\n\n\t\t\t\t\tincludesLeft = nodeIndices.has( offsetLeft );\n\t\t\t\t\tincludesRight = nodeIndices.has( offsetRight );\n\t\t\t\t\tforceChildren = ! includesLeft && ! includesRight;\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tincludesLeft = true;\n\t\t\t\tincludesRight = true;\n\n\t\t\t}\n\n\t\t\tconst traverseLeft = forceChildren || includesLeft;\n\t\t\tconst traverseRight = forceChildren || includesRight;\n\n\t\t\tlet leftChange = false;\n\t\t\tif ( traverseLeft ) {\n\n\t\t\t\tleftChange = _traverse( left, byteOffset, forceChildren );\n\n\t\t\t}\n\n\t\t\tlet rightChange = false;\n\t\t\tif ( traverseRight ) {\n\n\t\t\t\trightChange = _traverse( right, byteOffset, forceChildren );\n\n\t\t\t}\n\n\t\t\tconst didChange = leftChange || rightChange;\n\t\t\tif ( didChange ) {\n\n\t\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\t\tconst lefti = left + i;\n\t\t\t\t\tconst righti = right + i;\n\t\t\t\t\tconst minLeftValue = float32Array[ lefti ];\n\t\t\t\t\tconst maxLeftValue = float32Array[ lefti + 3 ];\n\t\t\t\t\tconst minRightValue = float32Array[ righti ];\n\t\t\t\t\tconst maxRightValue = float32Array[ righti + 3 ];\n\n\t\t\t\t\tfloat32Array[ node32Index + i ] = minLeftValue < minRightValue ? minLeftValue : minRightValue;\n\t\t\t\t\tfloat32Array[ node32Index + i + 3 ] = maxLeftValue > maxRightValue ? maxLeftValue : maxRightValue;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn didChange;\n\n\t\t}\n\n\t}\n\n}\n\nexport { refit };\n", "/**\n * This function performs intersection tests similar to Ray.intersectBox in three.js,\n * with the difference that the box values are read from an array to improve performance.\n */\nexport function intersectRay( nodeIndex32, array, ray, near, far ) {\n\n\tlet tmin, tmax, tymin, tymax, tzmin, tzmax;\n\n\tconst invdirx = 1 / ray.direction.x,\n\t\tinvdiry = 1 / ray.direction.y,\n\t\tinvdirz = 1 / ray.direction.z;\n\n\tconst ox = ray.origin.x;\n\tconst oy = ray.origin.y;\n\tconst oz = ray.origin.z;\n\n\tlet minx = array[ nodeIndex32 ];\n\tlet maxx = array[ nodeIndex32 + 3 ];\n\n\tlet miny = array[ nodeIndex32 + 1 ];\n\tlet maxy = array[ nodeIndex32 + 3 + 1 ];\n\n\tlet minz = array[ nodeIndex32 + 2 ];\n\tlet maxz = array[ nodeIndex32 + 3 + 2 ];\n\n\tif ( invdirx >= 0 ) {\n\n\t\ttmin = ( minx - ox ) * invdirx;\n\t\ttmax = ( maxx - ox ) * invdirx;\n\n\t} else {\n\n\t\ttmin = ( maxx - ox ) * invdirx;\n\t\ttmax = ( minx - ox ) * invdirx;\n\n\t}\n\n\tif ( invdiry >= 0 ) {\n\n\t\ttymin = ( miny - oy ) * invdiry;\n\t\ttymax = ( maxy - oy ) * invdiry;\n\n\t} else {\n\n\t\ttymin = ( maxy - oy ) * invdiry;\n\t\ttymax = ( miny - oy ) * invdiry;\n\n\t}\n\n\tif ( ( tmin > tymax ) || ( tymin > tmax ) ) return false;\n\n\tif ( tymin > tmin || isNaN( tmin ) ) tmin = tymin;\n\n\tif ( tymax < tmax || isNaN( tmax ) ) tmax = tymax;\n\n\tif ( invdirz >= 0 ) {\n\n\t\ttzmin = ( minz - oz ) * invdirz;\n\t\ttzmax = ( maxz - oz ) * invdirz;\n\n\t} else {\n\n\t\ttzmin = ( maxz - oz ) * invdirz;\n\t\ttzmax = ( minz - oz ) * invdirz;\n\n\t}\n\n\tif ( ( tmin > tzmax ) || ( tzmin > tmax ) ) return false;\n\n\tif ( tzmin > tmin || tmin !== tmin ) tmin = tzmin;\n\n\tif ( tzmax < tmax || tmax !== tmax ) tmax = tzmax;\n\n\t//return point closest to the ray (positive side)\n\n\treturn tmin <= far && tmax >= near;\n\n}\n", "import { intersectTri } from '../../utils/ThreeRayIntersectUtilities.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\n\n/*************************************************************/\n/* This file is generated from \"iterationUtils.template.js\". */\n/*************************************************************/\n/* eslint-disable indent */\n\nfunction intersectTris_indirect( bvh, side, ray, offset, count, intersections, near, far ) {\n\n\tconst { geometry, _indirectBuffer } = bvh;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tlet vi = _indirectBuffer ? _indirectBuffer[ i ] : i;\n\t\tintersectTri( geometry, side, ray, vi, intersections, near, far );\n\n\n\t}\n\n}\n\nfunction intersectClosestTri_indirect( bvh, side, ray, offset, count, near, far ) {\n\n\tconst { geometry, _indirectBuffer } = bvh;\n\tlet dist = Infinity;\n\tlet res = null;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tlet intersection;\n\t\tintersection = intersectTri( geometry, side, ray, _indirectBuffer ? _indirectBuffer[ i ] : i, null, near, far );\n\n\n\t\tif ( intersection && intersection.distance < dist ) {\n\n\t\t\tres = intersection;\n\t\t\tdist = intersection.distance;\n\n\t\t}\n\n\t}\n\n\treturn res;\n\n}\n\nfunction iterateOverTriangles_indirect(\n\toffset,\n\tcount,\n\tbvh,\n\tintersectsTriangleFunc,\n\tcontained,\n\tdepth,\n\ttriangle\n) {\n\n\tconst { geometry } = bvh;\n\tconst { index } = geometry;\n\tconst pos = geometry.attributes.position;\n\tfor ( let i = offset, l = count + offset; i < l; i ++ ) {\n\n\t\tlet tri;\n\t\ttri = bvh.resolveTriangleIndex( i );\n\n\t\tsetTriangle( triangle, tri * 3, index, pos );\n\t\ttriangle.needsUpdate = true;\n\n\t\tif ( intersectsTriangleFunc( triangle, tri, contained, depth ) ) {\n\n\t\t\treturn true;\n\n\t\t}\n\n\t}\n\n\treturn false;\n\n}\n\nexport { intersectClosestTri_indirect, intersectTris_indirect, iterateOverTriangles_indirect };\n", "import { intersectRay } from '../utils/intersectUtils.js';\nimport { IS_LEAF, OFFSET, COUNT, LEFT_NODE, RIGHT_NODE } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\nimport { intersectTris } from '../utils/iterationUtils.generated.js';\nimport '../utils/iterationUtils_indirect.generated.js';\n\n/******************************************************/\n/* This file is generated from \"raycast.template.js\". */\n/******************************************************/\n\nfunction raycast( bvh, root, side, ray, intersects, near, far ) {\n\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\t_raycast( 0, bvh, side, ray, intersects, near, far );\n\tBufferStack.clearBuffer();\n\n}\n\nfunction _raycast( nodeIndex32, bvh, side, ray, intersects, near, far ) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tconst nodeIndex16 = nodeIndex32 * 2;\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\n\t\tintersectTris( bvh, side, ray, offset, count, intersects, near, far );\n\n\n\t} else {\n\n\t\tconst leftIndex = LEFT_NODE( nodeIndex32 );\n\t\tif ( intersectRay( leftIndex, float32Array, ray, near, far ) ) {\n\n\t\t\t_raycast( leftIndex, bvh, side, ray, intersects, near, far );\n\n\t\t}\n\n\t\tconst rightIndex = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\tif ( intersectRay( rightIndex, float32Array, ray, near, far ) ) {\n\n\t\t\t_raycast( rightIndex, bvh, side, ray, intersects, near, far );\n\n\t\t}\n\n\t}\n\n}\n\nexport { raycast };\n", "import { IS_LEAF, OFFSET, COUNT, SPLIT_AXIS, LEFT_NODE, RIGHT_NODE } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\nimport { intersectRay } from '../utils/intersectUtils.js';\nimport { intersectClosestTri } from '../utils/iterationUtils.generated.js';\nimport '../utils/iterationUtils_indirect.generated.js';\n\n/***********************************************************/\n/* This file is generated from \"raycastFirst.template.js\". */\n/***********************************************************/\n\nconst _xyzFields = [ 'x', 'y', 'z' ];\n\nfunction raycastFirst( bvh, root, side, ray, near, far ) {\n\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\tconst result = _raycastFirst( 0, bvh, side, ray, near, far );\n\tBufferStack.clearBuffer();\n\n\treturn result;\n\n}\n\nfunction _raycastFirst( nodeIndex32, bvh, side, ray, near, far ) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\n\t\t// eslint-disable-next-line no-unreachable\n\t\treturn intersectClosestTri( bvh, side, ray, offset, count, near, far );\n\n\n\t} else {\n\n\t\t// consider the position of the split plane with respect to the oncoming ray; whichever direction\n\t\t// the ray is coming from, look for an intersection among that side of the tree first\n\t\tconst splitAxis = SPLIT_AXIS( nodeIndex32, uint32Array );\n\t\tconst xyzAxis = _xyzFields[ splitAxis ];\n\t\tconst rayDir = ray.direction[ xyzAxis ];\n\t\tconst leftToRight = rayDir >= 0;\n\n\t\t// c1 is the child to check first\n\t\tlet c1, c2;\n\t\tif ( leftToRight ) {\n\n\t\t\tc1 = LEFT_NODE( nodeIndex32 );\n\t\t\tc2 = RIGHT_NODE( nodeIndex32, uint32Array );\n\n\t\t} else {\n\n\t\t\tc1 = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\t\tc2 = LEFT_NODE( nodeIndex32 );\n\n\t\t}\n\n\t\tconst c1Intersection = intersectRay( c1, float32Array, ray, near, far );\n\t\tconst c1Result = c1Intersection ? _raycastFirst( c1, bvh, side, ray, near, far ) : null;\n\n\t\t// if we got an intersection in the first node and it's closer than the second node's bounding\n\t\t// box, we don't need to consider the second node because it couldn't possibly be a better result\n\t\tif ( c1Result ) {\n\n\t\t\t// check if the point is within the second bounds\n\t\t\t// \"point\" is in the local frame of the bvh\n\t\t\tconst point = c1Result.point[ xyzAxis ];\n\t\t\tconst isOutside = leftToRight ?\n\t\t\t\tpoint <= float32Array[ c2 + splitAxis ] : // min bounding data\n\t\t\t\tpoint >= float32Array[ c2 + splitAxis + 3 ]; // max bounding data\n\n\t\t\tif ( isOutside ) {\n\n\t\t\t\treturn c1Result;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// either there was no intersection in the first node, or there could still be a closer\n\t\t// intersection in the second, so check the second node and then take the better of the two\n\t\tconst c2Intersection = intersectRay( c2, float32Array, ray, near, far );\n\t\tconst c2Result = c2Intersection ? _raycastFirst( c2, bvh, side, ray, near, far ) : null;\n\n\t\tif ( c1Result && c2Result ) {\n\n\t\t\treturn c1Result.distance <= c2Result.distance ? c1Result : c2Result;\n\n\t\t} else {\n\n\t\t\treturn c1Result || c2Result || null;\n\n\t\t}\n\n\t}\n\n}\n\nexport { raycastFirst };\n", "import { Box3, Matrix4 } from 'three';\nimport { OrientedBox } from '../../math/OrientedBox.js';\nimport { ExtendedTriangle } from '../../math/ExtendedTriangle.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\nimport { arrayToBox } from '../../utils/ArrayBoxUtilities.js';\nimport { IS_LEAF, OFFSET, COUNT, BOUNDING_DATA_INDEX } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\n\n/*****************************************************************/\n/* This file is generated from \"intersectsGeometry.template.js\". */\n/*****************************************************************/\n/* eslint-disable indent */\n\nconst boundingBox = /* @__PURE__ */ new Box3();\nconst triangle = /* @__PURE__ */ new ExtendedTriangle();\nconst triangle2 = /* @__PURE__ */ new ExtendedTriangle();\nconst invertedMat = /* @__PURE__ */ new Matrix4();\n\nconst obb = /* @__PURE__ */ new OrientedBox();\nconst obb2 = /* @__PURE__ */ new OrientedBox();\n\nfunction intersectsGeometry( bvh, root, otherGeometry, geometryToBvh ) {\n\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\tconst result = _intersectsGeometry( 0, bvh, otherGeometry, geometryToBvh );\n\tBufferStack.clearBuffer();\n\n\treturn result;\n\n}\n\nfunction _intersectsGeometry( nodeIndex32, bvh, otherGeometry, geometryToBvh, cachedObb = null ) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\tif ( cachedObb === null ) {\n\n\t\tif ( ! otherGeometry.boundingBox ) {\n\n\t\t\totherGeometry.computeBoundingBox();\n\n\t\t}\n\n\t\tobb.set( otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh );\n\t\tcachedObb = obb;\n\n\t}\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst thisGeometry = bvh.geometry;\n\t\tconst thisIndex = thisGeometry.index;\n\t\tconst thisPos = thisGeometry.attributes.position;\n\n\t\tconst index = otherGeometry.index;\n\t\tconst pos = otherGeometry.attributes.position;\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\t\t// get the inverse of the geometry matrix so we can transform our triangles into the\n\t\t// geometry space we're trying to test. We assume there are fewer triangles being checked\n\t\t// here.\n\t\tinvertedMat.copy( geometryToBvh ).invert();\n\n\t\tif ( otherGeometry.boundsTree ) {\n\n\t\t\t// if there's a bounds tree\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( nodeIndex32 ), float32Array, obb2 );\n\t\t\tobb2.matrix.copy( invertedMat );\n\t\t\tobb2.needsUpdate = true;\n\n\t\t\t// TODO: use a triangle iteration function here\n\t\t\tconst res = otherGeometry.boundsTree.shapecast( {\n\n\t\t\t\tintersectsBounds: box => obb2.intersectsBox( box ),\n\n\t\t\t\tintersectsTriangle: tri => {\n\n\t\t\t\t\ttri.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\ttri.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\ttri.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\ttri.needsUpdate = true;\n\n\n\t\t\t\t\tfor ( let i = offset * 3, l = ( count + offset ) * 3; i < l; i += 3 ) {\n\n\t\t\t\t\t\t// this triangle needs to be transformed into the current BVH coordinate frame\n\t\t\t\t\t\tsetTriangle( triangle2, i, thisIndex, thisPos );\n\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\t\t\t\t\t\tif ( tri.intersectsTriangle( triangle2 ) ) {\n\n\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\n\t\t\t\t\treturn false;\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t\treturn res;\n\n\t\t} else {\n\n\t\t\t// if we're just dealing with raw geometry\n\n\t\t\tfor ( let i = offset * 3, l = ( count + offset ) * 3; i < l; i += 3 ) {\n\n\t\t\t\t// this triangle needs to be transformed into the current BVH coordinate frame\n\t\t\t\tsetTriangle( triangle, i, thisIndex, thisPos );\n\n\n\t\t\t\ttriangle.a.applyMatrix4( invertedMat );\n\t\t\t\ttriangle.b.applyMatrix4( invertedMat );\n\t\t\t\ttriangle.c.applyMatrix4( invertedMat );\n\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\tfor ( let i2 = 0, l2 = index.count; i2 < l2; i2 += 3 ) {\n\n\t\t\t\t\tsetTriangle( triangle2, i2, index, pos );\n\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\tif ( triangle.intersectsTriangle( triangle2 ) ) {\n\n\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\n\t\t\t}\n\n\n\t\t}\n\n\t} else {\n\n\t\tconst left = nodeIndex32 + 8;\n\t\tconst right = uint32Array[ nodeIndex32 + 6 ];\n\n\t\tarrayToBox( BOUNDING_DATA_INDEX( left ), float32Array, boundingBox );\n\t\tconst leftIntersection =\n\t\t\tcachedObb.intersectsBox( boundingBox ) &&\n\t\t\t_intersectsGeometry( left, bvh, otherGeometry, geometryToBvh, cachedObb );\n\n\t\tif ( leftIntersection ) return true;\n\n\t\tarrayToBox( BOUNDING_DATA_INDEX( right ), float32Array, boundingBox );\n\t\tconst rightIntersection =\n\t\t\tcachedObb.intersectsBox( boundingBox ) &&\n\t\t\t_intersectsGeometry( right, bvh, otherGeometry, geometryToBvh, cachedObb );\n\n\t\tif ( rightIntersection ) return true;\n\n\t\treturn false;\n\n\t}\n\n}\n\nexport { intersectsGeometry };\n", "import { Matrix4, Vector3 } from 'three';\nimport { OrientedBox } from '../../math/OrientedBox.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\nimport { getTriCount } from '../build/geometryUtils.js';\nimport { ExtendedTrianglePool } from '../../utils/ExtendedTrianglePool.js';\n\n/*********************************************************************/\n/* This file is generated from \"closestPointToGeometry.template.js\". */\n/*********************************************************************/\n\nconst tempMatrix = /* @__PURE__ */ new Matrix4();\nconst obb = /* @__PURE__ */ new OrientedBox();\nconst obb2 = /* @__PURE__ */ new OrientedBox();\nconst temp1 = /* @__PURE__ */ new Vector3();\nconst temp2 = /* @__PURE__ */ new Vector3();\nconst temp3 = /* @__PURE__ */ new Vector3();\nconst temp4 = /* @__PURE__ */ new Vector3();\n\nfunction closestPointToGeometry(\n\tbvh,\n\totherGeometry,\n\tgeometryToBvh,\n\ttarget1 = { },\n\ttarget2 = { },\n\tminThreshold = 0,\n\tmaxThreshold = Infinity,\n) {\n\n\tif ( ! otherGeometry.boundingBox ) {\n\n\t\totherGeometry.computeBoundingBox();\n\n\t}\n\n\tobb.set( otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh );\n\tobb.needsUpdate = true;\n\n\tconst geometry = bvh.geometry;\n\tconst pos = geometry.attributes.position;\n\tconst index = geometry.index;\n\tconst otherPos = otherGeometry.attributes.position;\n\tconst otherIndex = otherGeometry.index;\n\tconst triangle = ExtendedTrianglePool.getPrimitive();\n\tconst triangle2 = ExtendedTrianglePool.getPrimitive();\n\n\tlet tempTarget1 = temp1;\n\tlet tempTargetDest1 = temp2;\n\tlet tempTarget2 = null;\n\tlet tempTargetDest2 = null;\n\n\tif ( target2 ) {\n\n\t\ttempTarget2 = temp3;\n\t\ttempTargetDest2 = temp4;\n\n\t}\n\n\tlet closestDistance = Infinity;\n\tlet closestDistanceTriIndex = null;\n\tlet closestDistanceOtherTriIndex = null;\n\ttempMatrix.copy( geometryToBvh ).invert();\n\tobb2.matrix.copy( tempMatrix );\n\tbvh.shapecast(\n\t\t{\n\n\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\treturn obb.distanceToBox( box );\n\n\t\t\t},\n\n\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\tif ( score < closestDistance && score < maxThreshold ) {\n\n\t\t\t\t\t// if we know the triangles of this bounds will be intersected next then\n\t\t\t\t\t// save the bounds to use during triangle checks.\n\t\t\t\t\tif ( isLeaf ) {\n\n\t\t\t\t\t\tobb2.min.copy( box.min );\n\t\t\t\t\t\tobb2.max.copy( box.max );\n\t\t\t\t\t\tobb2.needsUpdate = true;\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\n\t\t\t},\n\n\t\t\tintersectsRange: ( offset, count ) => {\n\n\t\t\t\tif ( otherGeometry.boundsTree ) {\n\n\t\t\t\t\t// if the other geometry has a bvh then use the accelerated path where we use shapecast to find\n\t\t\t\t\t// the closest bounds in the other geometry to check.\n\t\t\t\t\tconst otherBvh = otherGeometry.boundsTree;\n\t\t\t\t\treturn otherBvh.shapecast( {\n\t\t\t\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\t\t\t\treturn obb2.distanceToBox( box );\n\n\t\t\t\t\t\t},\n\n\t\t\t\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\t\t\t\treturn score < closestDistance && score < maxThreshold;\n\n\t\t\t\t\t\t},\n\n\t\t\t\t\t\tintersectsRange: ( otherOffset, otherCount ) => {\n\n\t\t\t\t\t\t\tfor ( let i2 = otherOffset, l2 = otherOffset + otherCount; i2 < l2; i2 ++ ) {\n\n\n\t\t\t\t\t\t\t\tsetTriangle( triangle2, 3 * i2, otherIndex, otherPos );\n\n\t\t\t\t\t\t\t\ttriangle2.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\ttriangle2.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\ttriangle2.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\t\t\tfor ( let i = offset, l = offset + count; i < l; i ++ ) {\n\n\n\t\t\t\t\t\t\t\t\tsetTriangle( triangle, 3 * i, index, pos );\n\n\t\t\t\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\t\t\t\tconst dist = triangle.distanceToTriangle( triangle2, tempTarget1, tempTarget2 );\n\t\t\t\t\t\t\t\t\tif ( dist < closestDistance ) {\n\n\t\t\t\t\t\t\t\t\t\ttempTargetDest1.copy( tempTarget1 );\n\n\t\t\t\t\t\t\t\t\t\tif ( tempTargetDest2 ) {\n\n\t\t\t\t\t\t\t\t\t\t\ttempTargetDest2.copy( tempTarget2 );\n\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\tclosestDistance = dist;\n\t\t\t\t\t\t\t\t\t\tclosestDistanceTriIndex = i;\n\t\t\t\t\t\t\t\t\t\tclosestDistanceOtherTriIndex = i2;\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t// stop traversal if we find a point that's under the given threshold\n\t\t\t\t\t\t\t\t\tif ( dist < minThreshold ) {\n\n\t\t\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t},\n\t\t\t\t\t} );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t// If no bounds tree then we'll just check every triangle.\n\t\t\t\t\tconst triCount = getTriCount( otherGeometry );\n\t\t\t\t\tfor ( let i2 = 0, l2 = triCount; i2 < l2; i2 ++ ) {\n\n\t\t\t\t\t\tsetTriangle( triangle2, 3 * i2, otherIndex, otherPos );\n\t\t\t\t\t\ttriangle2.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttriangle2.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttriangle2.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\tfor ( let i = offset, l = offset + count; i < l; i ++ ) {\n\n\n\t\t\t\t\t\t\tsetTriangle( triangle, 3 * i, index, pos );\n\n\t\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\t\tconst dist = triangle.distanceToTriangle( triangle2, tempTarget1, tempTarget2 );\n\t\t\t\t\t\t\tif ( dist < closestDistance ) {\n\n\t\t\t\t\t\t\t\ttempTargetDest1.copy( tempTarget1 );\n\n\t\t\t\t\t\t\t\tif ( tempTargetDest2 ) {\n\n\t\t\t\t\t\t\t\t\ttempTargetDest2.copy( tempTarget2 );\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tclosestDistance = dist;\n\t\t\t\t\t\t\t\tclosestDistanceTriIndex = i;\n\t\t\t\t\t\t\t\tclosestDistanceOtherTriIndex = i2;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// stop traversal if we find a point that's under the given threshold\n\t\t\t\t\t\t\tif ( dist < minThreshold ) {\n\n\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t},\n\n\t\t}\n\n\t);\n\n\tExtendedTrianglePool.releasePrimitive( triangle );\n\tExtendedTrianglePool.releasePrimitive( triangle2 );\n\n\tif ( closestDistance === Infinity ) {\n\n\t\treturn null;\n\n\t}\n\n\tif ( ! target1.point ) {\n\n\t\ttarget1.point = tempTargetDest1.clone();\n\n\t} else {\n\n\t\ttarget1.point.copy( tempTargetDest1 );\n\n\t}\n\n\ttarget1.distance = closestDistance,\n\ttarget1.faceIndex = closestDistanceTriIndex;\n\n\tif ( target2 ) {\n\n\t\tif ( ! target2.point ) target2.point = tempTargetDest2.clone();\n\t\telse target2.point.copy( tempTargetDest2 );\n\t\ttarget2.point.applyMatrix4( tempMatrix );\n\t\ttempTargetDest1.applyMatrix4( tempMatrix );\n\t\ttarget2.distance = tempTargetDest1.sub( target2.point ).length();\n\t\ttarget2.faceIndex = closestDistanceOtherTriIndex;\n\n\t}\n\n\treturn target1;\n\n}\n\nexport { closestPointToGeometry };\n", "import { IS_LEAFNODE_FLAG } from '../Constants.js';\n\n/****************************************************/\n/* This file is generated from \"refit.template.js\". */\n/****************************************************/\n\nfunction refit_indirect( bvh, nodeIndices = null ) {\n\n\tif ( nodeIndices && Array.isArray( nodeIndices ) ) {\n\n\t\tnodeIndices = new Set( nodeIndices );\n\n\t}\n\n\tconst geometry = bvh.geometry;\n\tconst indexArr = geometry.index ? geometry.index.array : null;\n\tconst posAttr = geometry.attributes.position;\n\n\tlet buffer, uint32Array, uint16Array, float32Array;\n\tlet byteOffset = 0;\n\tconst roots = bvh._roots;\n\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\tbuffer = roots[ i ];\n\t\tuint32Array = new Uint32Array( buffer );\n\t\tuint16Array = new Uint16Array( buffer );\n\t\tfloat32Array = new Float32Array( buffer );\n\n\t\t_traverse( 0, byteOffset );\n\t\tbyteOffset += buffer.byteLength;\n\n\t}\n\n\tfunction _traverse( node32Index, byteOffset, force = false ) {\n\n\t\tconst node16Index = node32Index * 2;\n\t\tconst isLeaf = uint16Array[ node16Index + 15 ] === IS_LEAFNODE_FLAG;\n\t\tif ( isLeaf ) {\n\n\t\t\tconst offset = uint32Array[ node32Index + 6 ];\n\t\t\tconst count = uint16Array[ node16Index + 14 ];\n\n\t\t\tlet minx = Infinity;\n\t\t\tlet miny = Infinity;\n\t\t\tlet minz = Infinity;\n\t\t\tlet maxx = - Infinity;\n\t\t\tlet maxy = - Infinity;\n\t\t\tlet maxz = - Infinity;\n\n\t\t\tfor ( let i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\t\t\tconst t = 3 * bvh.resolveTriangleIndex( i );\n\t\t\t\tfor ( let j = 0; j < 3; j ++ ) {\n\n\t\t\t\t\tlet index = t + j;\n\t\t\t\t\tindex = indexArr ? indexArr[ index ] : index;\n\n\t\t\t\t\tconst x = posAttr.getX( index );\n\t\t\t\t\tconst y = posAttr.getY( index );\n\t\t\t\t\tconst z = posAttr.getZ( index );\n\n\t\t\t\t\tif ( x < minx ) minx = x;\n\t\t\t\t\tif ( x > maxx ) maxx = x;\n\n\t\t\t\t\tif ( y < miny ) miny = y;\n\t\t\t\t\tif ( y > maxy ) maxy = y;\n\n\t\t\t\t\tif ( z < minz ) minz = z;\n\t\t\t\t\tif ( z > maxz ) maxz = z;\n\n\n\t\t\t\t}\n\n\t\t\t}\n\n\n\t\t\tif (\n\t\t\t\tfloat32Array[ node32Index + 0 ] !== minx ||\n\t\t\t\tfloat32Array[ node32Index + 1 ] !== miny ||\n\t\t\t\tfloat32Array[ node32Index + 2 ] !== minz ||\n\n\t\t\t\tfloat32Array[ node32Index + 3 ] !== maxx ||\n\t\t\t\tfloat32Array[ node32Index + 4 ] !== maxy ||\n\t\t\t\tfloat32Array[ node32Index + 5 ] !== maxz\n\t\t\t) {\n\n\t\t\t\tfloat32Array[ node32Index + 0 ] = minx;\n\t\t\t\tfloat32Array[ node32Index + 1 ] = miny;\n\t\t\t\tfloat32Array[ node32Index + 2 ] = minz;\n\n\t\t\t\tfloat32Array[ node32Index + 3 ] = maxx;\n\t\t\t\tfloat32Array[ node32Index + 4 ] = maxy;\n\t\t\t\tfloat32Array[ node32Index + 5 ] = maxz;\n\n\t\t\t\treturn true;\n\n\t\t\t} else {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconst left = node32Index + 8;\n\t\t\tconst right = uint32Array[ node32Index + 6 ];\n\n\t\t\t// the identifying node indices provided by the shapecast function include offsets of all\n\t\t\t// root buffers to guarantee they're unique between roots so offset left and right indices here.\n\t\t\tconst offsetLeft = left + byteOffset;\n\t\t\tconst offsetRight = right + byteOffset;\n\t\t\tlet forceChildren = force;\n\t\t\tlet includesLeft = false;\n\t\t\tlet includesRight = false;\n\n\t\t\tif ( nodeIndices ) {\n\n\t\t\t\t// if we see that neither the left or right child are included in the set that need to be updated\n\t\t\t\t// then we assume that all children need to be updated.\n\t\t\t\tif ( ! forceChildren ) {\n\n\t\t\t\t\tincludesLeft = nodeIndices.has( offsetLeft );\n\t\t\t\t\tincludesRight = nodeIndices.has( offsetRight );\n\t\t\t\t\tforceChildren = ! includesLeft && ! includesRight;\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tincludesLeft = true;\n\t\t\t\tincludesRight = true;\n\n\t\t\t}\n\n\t\t\tconst traverseLeft = forceChildren || includesLeft;\n\t\t\tconst traverseRight = forceChildren || includesRight;\n\n\t\t\tlet leftChange = false;\n\t\t\tif ( traverseLeft ) {\n\n\t\t\t\tleftChange = _traverse( left, byteOffset, forceChildren );\n\n\t\t\t}\n\n\t\t\tlet rightChange = false;\n\t\t\tif ( traverseRight ) {\n\n\t\t\t\trightChange = _traverse( right, byteOffset, forceChildren );\n\n\t\t\t}\n\n\t\t\tconst didChange = leftChange || rightChange;\n\t\t\tif ( didChange ) {\n\n\t\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\t\tconst lefti = left + i;\n\t\t\t\t\tconst righti = right + i;\n\t\t\t\t\tconst minLeftValue = float32Array[ lefti ];\n\t\t\t\t\tconst maxLeftValue = float32Array[ lefti + 3 ];\n\t\t\t\t\tconst minRightValue = float32Array[ righti ];\n\t\t\t\t\tconst maxRightValue = float32Array[ righti + 3 ];\n\n\t\t\t\t\tfloat32Array[ node32Index + i ] = minLeftValue < minRightValue ? minLeftValue : minRightValue;\n\t\t\t\t\tfloat32Array[ node32Index + i + 3 ] = maxLeftValue > maxRightValue ? maxLeftValue : maxRightValue;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn didChange;\n\n\t\t}\n\n\t}\n\n}\n\nexport { refit_indirect };\n", "import { intersectRay } from '../utils/intersectUtils.js';\nimport { IS_LEAF, OFFSET, COUNT, LEFT_NODE, RIGHT_NODE } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\nimport '../utils/iterationUtils.generated.js';\nimport { intersectTris_indirect } from '../utils/iterationUtils_indirect.generated.js';\n\n/******************************************************/\n/* This file is generated from \"raycast.template.js\". */\n/******************************************************/\n\nfunction raycast_indirect( bvh, root, side, ray, intersects, near, far ) {\n\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\t_raycast( 0, bvh, side, ray, intersects, near, far );\n\tBufferStack.clearBuffer();\n\n}\n\nfunction _raycast( nodeIndex32, bvh, side, ray, intersects, near, far ) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tconst nodeIndex16 = nodeIndex32 * 2;\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\t\tintersectTris_indirect( bvh, side, ray, offset, count, intersects, near, far );\n\n\n\t} else {\n\n\t\tconst leftIndex = LEFT_NODE( nodeIndex32 );\n\t\tif ( intersectRay( leftIndex, float32Array, ray, near, far ) ) {\n\n\t\t\t_raycast( leftIndex, bvh, side, ray, intersects, near, far );\n\n\t\t}\n\n\t\tconst rightIndex = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\tif ( intersectRay( rightIndex, float32Array, ray, near, far ) ) {\n\n\t\t\t_raycast( rightIndex, bvh, side, ray, intersects, near, far );\n\n\t\t}\n\n\t}\n\n}\n\nexport { raycast_indirect };\n", "import { IS_LEAF, OFFSET, COUNT, SPLIT_AXIS, LEFT_NODE, RIGHT_NODE } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\nimport { intersectRay } from '../utils/intersectUtils.js';\nimport '../utils/iterationUtils.generated.js';\nimport { intersectClosestTri_indirect } from '../utils/iterationUtils_indirect.generated.js';\n\n/***********************************************************/\n/* This file is generated from \"raycastFirst.template.js\". */\n/***********************************************************/\n\nconst _xyzFields = [ 'x', 'y', 'z' ];\n\nfunction raycastFirst_indirect( bvh, root, side, ray, near, far ) {\n\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\tconst result = _raycastFirst( 0, bvh, side, ray, near, far );\n\tBufferStack.clearBuffer();\n\n\treturn result;\n\n}\n\nfunction _raycastFirst( nodeIndex32, bvh, side, ray, near, far ) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\t\treturn intersectClosestTri_indirect( bvh, side, ray, offset, count, near, far );\n\n\n\t} else {\n\n\t\t// consider the position of the split plane with respect to the oncoming ray; whichever direction\n\t\t// the ray is coming from, look for an intersection among that side of the tree first\n\t\tconst splitAxis = SPLIT_AXIS( nodeIndex32, uint32Array );\n\t\tconst xyzAxis = _xyzFields[ splitAxis ];\n\t\tconst rayDir = ray.direction[ xyzAxis ];\n\t\tconst leftToRight = rayDir >= 0;\n\n\t\t// c1 is the child to check first\n\t\tlet c1, c2;\n\t\tif ( leftToRight ) {\n\n\t\t\tc1 = LEFT_NODE( nodeIndex32 );\n\t\t\tc2 = RIGHT_NODE( nodeIndex32, uint32Array );\n\n\t\t} else {\n\n\t\t\tc1 = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\t\tc2 = LEFT_NODE( nodeIndex32 );\n\n\t\t}\n\n\t\tconst c1Intersection = intersectRay( c1, float32Array, ray, near, far );\n\t\tconst c1Result = c1Intersection ? _raycastFirst( c1, bvh, side, ray, near, far ) : null;\n\n\t\t// if we got an intersection in the first node and it's closer than the second node's bounding\n\t\t// box, we don't need to consider the second node because it couldn't possibly be a better result\n\t\tif ( c1Result ) {\n\n\t\t\t// check if the point is within the second bounds\n\t\t\t// \"point\" is in the local frame of the bvh\n\t\t\tconst point = c1Result.point[ xyzAxis ];\n\t\t\tconst isOutside = leftToRight ?\n\t\t\t\tpoint <= float32Array[ c2 + splitAxis ] : // min bounding data\n\t\t\t\tpoint >= float32Array[ c2 + splitAxis + 3 ]; // max bounding data\n\n\t\t\tif ( isOutside ) {\n\n\t\t\t\treturn c1Result;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// either there was no intersection in the first node, or there could still be a closer\n\t\t// intersection in the second, so check the second node and then take the better of the two\n\t\tconst c2Intersection = intersectRay( c2, float32Array, ray, near, far );\n\t\tconst c2Result = c2Intersection ? _raycastFirst( c2, bvh, side, ray, near, far ) : null;\n\n\t\tif ( c1Result && c2Result ) {\n\n\t\t\treturn c1Result.distance <= c2Result.distance ? c1Result : c2Result;\n\n\t\t} else {\n\n\t\t\treturn c1Result || c2Result || null;\n\n\t\t}\n\n\t}\n\n}\n\nexport { raycastFirst_indirect };\n", "import { Box3, Matrix4 } from 'three';\nimport { OrientedBox } from '../../math/OrientedBox.js';\nimport { ExtendedTriangle } from '../../math/ExtendedTriangle.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\nimport { arrayToBox } from '../../utils/ArrayBoxUtilities.js';\nimport { IS_LEAF, OFFSET, COUNT, BOUNDING_DATA_INDEX } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\n\n/*****************************************************************/\n/* This file is generated from \"intersectsGeometry.template.js\". */\n/*****************************************************************/\n/* eslint-disable indent */\n\nconst boundingBox = /* @__PURE__ */ new Box3();\nconst triangle = /* @__PURE__ */ new ExtendedTriangle();\nconst triangle2 = /* @__PURE__ */ new ExtendedTriangle();\nconst invertedMat = /* @__PURE__ */ new Matrix4();\n\nconst obb = /* @__PURE__ */ new OrientedBox();\nconst obb2 = /* @__PURE__ */ new OrientedBox();\n\nfunction intersectsGeometry_indirect( bvh, root, otherGeometry, geometryToBvh ) {\n\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\tconst result = _intersectsGeometry( 0, bvh, otherGeometry, geometryToBvh );\n\tBufferStack.clearBuffer();\n\n\treturn result;\n\n}\n\nfunction _intersectsGeometry( nodeIndex32, bvh, otherGeometry, geometryToBvh, cachedObb = null ) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\tif ( cachedObb === null ) {\n\n\t\tif ( ! otherGeometry.boundingBox ) {\n\n\t\t\totherGeometry.computeBoundingBox();\n\n\t\t}\n\n\t\tobb.set( otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh );\n\t\tcachedObb = obb;\n\n\t}\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst thisGeometry = bvh.geometry;\n\t\tconst thisIndex = thisGeometry.index;\n\t\tconst thisPos = thisGeometry.attributes.position;\n\n\t\tconst index = otherGeometry.index;\n\t\tconst pos = otherGeometry.attributes.position;\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\t\t// get the inverse of the geometry matrix so we can transform our triangles into the\n\t\t// geometry space we're trying to test. We assume there are fewer triangles being checked\n\t\t// here.\n\t\tinvertedMat.copy( geometryToBvh ).invert();\n\n\t\tif ( otherGeometry.boundsTree ) {\n\n\t\t\t// if there's a bounds tree\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( nodeIndex32 ), float32Array, obb2 );\n\t\t\tobb2.matrix.copy( invertedMat );\n\t\t\tobb2.needsUpdate = true;\n\n\t\t\t// TODO: use a triangle iteration function here\n\t\t\tconst res = otherGeometry.boundsTree.shapecast( {\n\n\t\t\t\tintersectsBounds: box => obb2.intersectsBox( box ),\n\n\t\t\t\tintersectsTriangle: tri => {\n\n\t\t\t\t\ttri.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\ttri.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\ttri.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\ttri.needsUpdate = true;\n\n\t\t\t\t\tfor ( let i = offset, l = count + offset; i < l; i ++ ) {\n\n\t\t\t\t\t\t// this triangle needs to be transformed into the current BVH coordinate frame\n\t\t\t\t\t\tsetTriangle( triangle2, 3 * bvh.resolveTriangleIndex( i ), thisIndex, thisPos );\n\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\t\t\t\t\t\tif ( tri.intersectsTriangle( triangle2 ) ) {\n\n\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\n\t\t\t\t\treturn false;\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t\treturn res;\n\n\t\t} else {\n\n\t\t\t// if we're just dealing with raw geometry\n\t\t\tfor ( let i = offset, l = count + offset; i < l; i ++ ) {\n\n\t\t\t\t// this triangle needs to be transformed into the current BVH coordinate frame\n\t\t\t\tconst ti = bvh.resolveTriangleIndex( i );\n\t\t\t\tsetTriangle( triangle, 3 * ti, thisIndex, thisPos );\n\n\n\t\t\t\ttriangle.a.applyMatrix4( invertedMat );\n\t\t\t\ttriangle.b.applyMatrix4( invertedMat );\n\t\t\t\ttriangle.c.applyMatrix4( invertedMat );\n\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\tfor ( let i2 = 0, l2 = index.count; i2 < l2; i2 += 3 ) {\n\n\t\t\t\t\tsetTriangle( triangle2, i2, index, pos );\n\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\tif ( triangle.intersectsTriangle( triangle2 ) ) {\n\n\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\n\t\t}\n\n\t} else {\n\n\t\tconst left = nodeIndex32 + 8;\n\t\tconst right = uint32Array[ nodeIndex32 + 6 ];\n\n\t\tarrayToBox( BOUNDING_DATA_INDEX( left ), float32Array, boundingBox );\n\t\tconst leftIntersection =\n\t\t\tcachedObb.intersectsBox( boundingBox ) &&\n\t\t\t_intersectsGeometry( left, bvh, otherGeometry, geometryToBvh, cachedObb );\n\n\t\tif ( leftIntersection ) return true;\n\n\t\tarrayToBox( BOUNDING_DATA_INDEX( right ), float32Array, boundingBox );\n\t\tconst rightIntersection =\n\t\t\tcachedObb.intersectsBox( boundingBox ) &&\n\t\t\t_intersectsGeometry( right, bvh, otherGeometry, geometryToBvh, cachedObb );\n\n\t\tif ( rightIntersection ) return true;\n\n\t\treturn false;\n\n\t}\n\n}\n\nexport { intersectsGeometry_indirect };\n", "import { Matrix4, Vector3 } from 'three';\nimport { OrientedBox } from '../../math/OrientedBox.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\nimport { getTriCount } from '../build/geometryUtils.js';\nimport { ExtendedTrianglePool } from '../../utils/ExtendedTrianglePool.js';\n\n/*********************************************************************/\n/* This file is generated from \"closestPointToGeometry.template.js\". */\n/*********************************************************************/\n\nconst tempMatrix = /* @__PURE__ */ new Matrix4();\nconst obb = /* @__PURE__ */ new OrientedBox();\nconst obb2 = /* @__PURE__ */ new OrientedBox();\nconst temp1 = /* @__PURE__ */ new Vector3();\nconst temp2 = /* @__PURE__ */ new Vector3();\nconst temp3 = /* @__PURE__ */ new Vector3();\nconst temp4 = /* @__PURE__ */ new Vector3();\n\nfunction closestPointToGeometry_indirect(\n\tbvh,\n\totherGeometry,\n\tgeometryToBvh,\n\ttarget1 = { },\n\ttarget2 = { },\n\tminThreshold = 0,\n\tmaxThreshold = Infinity,\n) {\n\n\tif ( ! otherGeometry.boundingBox ) {\n\n\t\totherGeometry.computeBoundingBox();\n\n\t}\n\n\tobb.set( otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh );\n\tobb.needsUpdate = true;\n\n\tconst geometry = bvh.geometry;\n\tconst pos = geometry.attributes.position;\n\tconst index = geometry.index;\n\tconst otherPos = otherGeometry.attributes.position;\n\tconst otherIndex = otherGeometry.index;\n\tconst triangle = ExtendedTrianglePool.getPrimitive();\n\tconst triangle2 = ExtendedTrianglePool.getPrimitive();\n\n\tlet tempTarget1 = temp1;\n\tlet tempTargetDest1 = temp2;\n\tlet tempTarget2 = null;\n\tlet tempTargetDest2 = null;\n\n\tif ( target2 ) {\n\n\t\ttempTarget2 = temp3;\n\t\ttempTargetDest2 = temp4;\n\n\t}\n\n\tlet closestDistance = Infinity;\n\tlet closestDistanceTriIndex = null;\n\tlet closestDistanceOtherTriIndex = null;\n\ttempMatrix.copy( geometryToBvh ).invert();\n\tobb2.matrix.copy( tempMatrix );\n\tbvh.shapecast(\n\t\t{\n\n\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\treturn obb.distanceToBox( box );\n\n\t\t\t},\n\n\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\tif ( score < closestDistance && score < maxThreshold ) {\n\n\t\t\t\t\t// if we know the triangles of this bounds will be intersected next then\n\t\t\t\t\t// save the bounds to use during triangle checks.\n\t\t\t\t\tif ( isLeaf ) {\n\n\t\t\t\t\t\tobb2.min.copy( box.min );\n\t\t\t\t\t\tobb2.max.copy( box.max );\n\t\t\t\t\t\tobb2.needsUpdate = true;\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\n\t\t\t},\n\n\t\t\tintersectsRange: ( offset, count ) => {\n\n\t\t\t\tif ( otherGeometry.boundsTree ) {\n\n\t\t\t\t\t// if the other geometry has a bvh then use the accelerated path where we use shapecast to find\n\t\t\t\t\t// the closest bounds in the other geometry to check.\n\t\t\t\t\tconst otherBvh = otherGeometry.boundsTree;\n\t\t\t\t\treturn otherBvh.shapecast( {\n\t\t\t\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\t\t\t\treturn obb2.distanceToBox( box );\n\n\t\t\t\t\t\t},\n\n\t\t\t\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\t\t\t\treturn score < closestDistance && score < maxThreshold;\n\n\t\t\t\t\t\t},\n\n\t\t\t\t\t\tintersectsRange: ( otherOffset, otherCount ) => {\n\n\t\t\t\t\t\t\tfor ( let i2 = otherOffset, l2 = otherOffset + otherCount; i2 < l2; i2 ++ ) {\n\n\t\t\t\t\t\t\t\tconst ti2 = otherBvh.resolveTriangleIndex( i2 );\n\t\t\t\t\t\t\t\tsetTriangle( triangle2, 3 * ti2, otherIndex, otherPos );\n\n\t\t\t\t\t\t\t\ttriangle2.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\ttriangle2.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\ttriangle2.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\t\t\tfor ( let i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\t\t\t\t\t\t\t\tconst ti = bvh.resolveTriangleIndex( i );\n\t\t\t\t\t\t\t\t\tsetTriangle( triangle, 3 * ti, index, pos );\n\n\t\t\t\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\t\t\t\tconst dist = triangle.distanceToTriangle( triangle2, tempTarget1, tempTarget2 );\n\t\t\t\t\t\t\t\t\tif ( dist < closestDistance ) {\n\n\t\t\t\t\t\t\t\t\t\ttempTargetDest1.copy( tempTarget1 );\n\n\t\t\t\t\t\t\t\t\t\tif ( tempTargetDest2 ) {\n\n\t\t\t\t\t\t\t\t\t\t\ttempTargetDest2.copy( tempTarget2 );\n\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\tclosestDistance = dist;\n\t\t\t\t\t\t\t\t\t\tclosestDistanceTriIndex = i;\n\t\t\t\t\t\t\t\t\t\tclosestDistanceOtherTriIndex = i2;\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t// stop traversal if we find a point that's under the given threshold\n\t\t\t\t\t\t\t\t\tif ( dist < minThreshold ) {\n\n\t\t\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t},\n\t\t\t\t\t} );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t// If no bounds tree then we'll just check every triangle.\n\t\t\t\t\tconst triCount = getTriCount( otherGeometry );\n\t\t\t\t\tfor ( let i2 = 0, l2 = triCount; i2 < l2; i2 ++ ) {\n\n\t\t\t\t\t\tsetTriangle( triangle2, 3 * i2, otherIndex, otherPos );\n\t\t\t\t\t\ttriangle2.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttriangle2.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttriangle2.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\tfor ( let i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\t\t\t\t\t\tconst ti = bvh.resolveTriangleIndex( i );\n\t\t\t\t\t\t\tsetTriangle( triangle, 3 * ti, index, pos );\n\n\t\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\t\tconst dist = triangle.distanceToTriangle( triangle2, tempTarget1, tempTarget2 );\n\t\t\t\t\t\t\tif ( dist < closestDistance ) {\n\n\t\t\t\t\t\t\t\ttempTargetDest1.copy( tempTarget1 );\n\n\t\t\t\t\t\t\t\tif ( tempTargetDest2 ) {\n\n\t\t\t\t\t\t\t\t\ttempTargetDest2.copy( tempTarget2 );\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tclosestDistance = dist;\n\t\t\t\t\t\t\t\tclosestDistanceTriIndex = i;\n\t\t\t\t\t\t\t\tclosestDistanceOtherTriIndex = i2;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// stop traversal if we find a point that's under the given threshold\n\t\t\t\t\t\t\tif ( dist < minThreshold ) {\n\n\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t},\n\n\t\t}\n\n\t);\n\n\tExtendedTrianglePool.releasePrimitive( triangle );\n\tExtendedTrianglePool.releasePrimitive( triangle2 );\n\n\tif ( closestDistance === Infinity ) {\n\n\t\treturn null;\n\n\t}\n\n\tif ( ! target1.point ) {\n\n\t\ttarget1.point = tempTargetDest1.clone();\n\n\t} else {\n\n\t\ttarget1.point.copy( tempTargetDest1 );\n\n\t}\n\n\ttarget1.distance = closestDistance,\n\ttarget1.faceIndex = closestDistanceTriIndex;\n\n\tif ( target2 ) {\n\n\t\tif ( ! target2.point ) target2.point = tempTargetDest2.clone();\n\t\telse target2.point.copy( tempTargetDest2 );\n\t\ttarget2.point.applyMatrix4( tempMatrix );\n\t\ttempTargetDest1.applyMatrix4( tempMatrix );\n\t\ttarget2.distance = tempTargetDest1.sub( target2.point ).length();\n\t\ttarget2.faceIndex = closestDistanceOtherTriIndex;\n\n\t}\n\n\treturn target1;\n\n}\n\nexport { closestPointToGeometry_indirect };\n", "export function isSharedArrayBufferSupported() {\n\n\treturn typeof SharedArrayBuffer !== 'undefined';\n\n}\n\nexport function convertToBufferType( array, BufferConstructor ) {\n\n\tif ( array === null ) {\n\n\t\treturn array;\n\n\t} else if ( array.buffer ) {\n\n\t\tconst buffer = array.buffer;\n\t\tif ( buffer.constructor === BufferConstructor ) {\n\n\t\t\treturn array;\n\n\t\t}\n\n\t\tconst ArrayConstructor = array.constructor;\n\t\tconst result = new ArrayConstructor( new BufferConstructor( buffer.byteLength ) );\n\t\tresult.set( array );\n\t\treturn result;\n\n\t} else {\n\n\t\tif ( array.constructor === BufferConstructor ) {\n\n\t\t\treturn array;\n\n\t\t}\n\n\t\tconst result = new BufferConstructor( array.byteLength );\n\t\tnew Uint8Array( result ).set( new Uint8Array( array ) );\n\t\treturn result;\n\n\t}\n\n}\n", "import { Box3, Matrix4 } from 'three';\nimport { BufferStack } from '../utils/BufferStack.js';\nimport { BOUNDING_DATA_INDEX, COUNT, IS_LEAF, LEFT_NODE, OFFSET, RIGHT_NODE } from '../utils/nodeBufferUtils.js';\nimport { arrayToBox } from '../../utils/ArrayBoxUtilities.js';\nimport { PrimitivePool } from '../../utils/PrimitivePool.js';\n\nconst _bufferStack1 = new BufferStack.constructor();\nconst _bufferStack2 = new BufferStack.constructor();\nconst _boxPool = new PrimitivePool( () => new Box3() );\nconst _leftBox1 = new Box3();\nconst _rightBox1 = new Box3();\n\nconst _leftBox2 = new Box3();\nconst _rightBox2 = new Box3();\n\nlet _active = false;\n\nexport function bvhcast( bvh, otherBvh, matrixToLocal, intersectsRanges ) {\n\n\tif ( _active ) {\n\n\t\tthrow new Error( 'MeshBVH: Recursive calls to bvhcast not supported.' );\n\n\t}\n\n\t_active = true;\n\n\tconst roots = bvh._roots;\n\tconst otherRoots = otherBvh._roots;\n\tlet result;\n\tlet offset1 = 0;\n\tlet offset2 = 0;\n\tconst invMat = new Matrix4().copy( matrixToLocal ).invert();\n\n\t// iterate over the first set of roots\n\tfor ( let i = 0, il = roots.length; i < il; i ++ ) {\n\n\t\t_bufferStack1.setBuffer( roots[ i ] );\n\t\toffset2 = 0;\n\n\t\t// prep the initial root box\n\t\tconst localBox = _boxPool.getPrimitive();\n\t\tarrayToBox( BOUNDING_DATA_INDEX( 0 ), _bufferStack1.float32Array, localBox );\n\t\tlocalBox.applyMatrix4( invMat );\n\n\t\t// iterate over the second set of roots\n\t\tfor ( let j = 0, jl = otherRoots.length; j < jl; j ++ ) {\n\n\t\t\t_bufferStack2.setBuffer( otherRoots[ j ] );\n\n\t\t\tresult = _traverse(\n\t\t\t\t0, 0, matrixToLocal, invMat, intersectsRanges,\n\t\t\t\toffset1, offset2, 0, 0,\n\t\t\t\tlocalBox,\n\t\t\t);\n\n\t\t\t_bufferStack2.clearBuffer();\n\t\t\toffset2 += otherRoots[ j ].length;\n\n\t\t\tif ( result ) {\n\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// release stack info\n\t\t_boxPool.releasePrimitive( localBox );\n\t\t_bufferStack1.clearBuffer();\n\t\toffset1 += roots[ i ].length;\n\n\t\tif ( result ) {\n\n\t\t\tbreak;\n\n\t\t}\n\n\t}\n\n\t_active = false;\n\treturn result;\n\n}\n\nfunction _traverse(\n\tnode1Index32,\n\tnode2Index32,\n\tmatrix2to1,\n\tmatrix1to2,\n\tintersectsRangesFunc,\n\n\t// offsets for ids\n\tnode1IndexByteOffset = 0,\n\tnode2IndexByteOffset = 0,\n\n\t// tree depth\n\tdepth1 = 0,\n\tdepth2 = 0,\n\n\tcurrBox = null,\n\treversed = false,\n\n) {\n\n\t// get the buffer stacks associated with the current indices\n\tlet bufferStack1, bufferStack2;\n\tif ( reversed ) {\n\n\t\tbufferStack1 = _bufferStack2;\n\t\tbufferStack2 = _bufferStack1;\n\n\t} else {\n\n\t\tbufferStack1 = _bufferStack1;\n\t\tbufferStack2 = _bufferStack2;\n\n\t}\n\n\t// get the local instances of the typed buffers\n\tconst\n\t\tfloat32Array1 = bufferStack1.float32Array,\n\t\tuint32Array1 = bufferStack1.uint32Array,\n\t\tuint16Array1 = bufferStack1.uint16Array,\n\t\tfloat32Array2 = bufferStack2.float32Array,\n\t\tuint32Array2 = bufferStack2.uint32Array,\n\t\tuint16Array2 = bufferStack2.uint16Array;\n\n\tconst node1Index16 = node1Index32 * 2;\n\tconst node2Index16 = node2Index32 * 2;\n\tconst isLeaf1 = IS_LEAF( node1Index16, uint16Array1 );\n\tconst isLeaf2 = IS_LEAF( node2Index16, uint16Array2 );\n\tlet result = false;\n\tif ( isLeaf2 && isLeaf1 ) {\n\n\t\t// if both bounds are leaf nodes then fire the callback if the boxes intersect\n\t\tif ( reversed ) {\n\n\t\t\tresult = intersectsRangesFunc(\n\t\t\t\tOFFSET( node2Index32, uint32Array2 ), COUNT( node2Index32 * 2, uint16Array2 ),\n\t\t\t\tOFFSET( node1Index32, uint32Array1 ), COUNT( node1Index32 * 2, uint16Array1 ),\n\t\t\t\tdepth2, node2IndexByteOffset + node2Index32,\n\t\t\t\tdepth1, node1IndexByteOffset + node1Index32,\n\t\t\t);\n\n\t\t} else {\n\n\t\t\tresult = intersectsRangesFunc(\n\t\t\t\tOFFSET( node1Index32, uint32Array1 ), COUNT( node1Index32 * 2, uint16Array1 ),\n\t\t\t\tOFFSET( node2Index32, uint32Array2 ), COUNT( node2Index32 * 2, uint16Array2 ),\n\t\t\t\tdepth1, node1IndexByteOffset + node1Index32,\n\t\t\t\tdepth2, node2IndexByteOffset + node2Index32,\n\t\t\t);\n\n\t\t}\n\n\t} else if ( isLeaf2 ) {\n\n\t\t// SWAP\n\t\t// If we've traversed to the leaf node on the other bvh then we need to swap over\n\t\t// to traverse down the first one\n\n\t\t// get the new box to use\n\t\tconst newBox = _boxPool.getPrimitive();\n\t\tarrayToBox( BOUNDING_DATA_INDEX( node2Index32 ), float32Array2, newBox );\n\t\tnewBox.applyMatrix4( matrix2to1 );\n\n\t\t// get the child bounds to check before traversal\n\t\tconst cl1 = LEFT_NODE( node1Index32 );\n\t\tconst cr1 = RIGHT_NODE( node1Index32, uint32Array1 );\n\t\tarrayToBox( BOUNDING_DATA_INDEX( cl1 ), float32Array1, _leftBox1 );\n\t\tarrayToBox( BOUNDING_DATA_INDEX( cr1 ), float32Array1, _rightBox1 );\n\n\t\t// precompute the intersections otherwise the global boxes will be modified during traversal\n\t\tconst intersectCl1 = newBox.intersectsBox( _leftBox1 );\n\t\tconst intersectCr1 = newBox.intersectsBox( _rightBox1 );\n\t\tresult = (\n\t\t\tintersectCl1 && _traverse(\n\t\t\t\tnode2Index32, cl1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\tnewBox, ! reversed,\n\t\t\t)\n\t\t) || (\n\t\t\tintersectCr1 && _traverse(\n\t\t\t\tnode2Index32, cr1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\tnewBox, ! reversed,\n\t\t\t)\n\t\t);\n\n\t\t_boxPool.releasePrimitive( newBox );\n\n\t} else {\n\n\t\t// if neither are leaves then we should swap if one of the children does not\n\t\t// intersect with the current bounds\n\n\t\t// get the child bounds to check\n\t\tconst cl2 = LEFT_NODE( node2Index32 );\n\t\tconst cr2 = RIGHT_NODE( node2Index32, uint32Array2 );\n\t\tarrayToBox( BOUNDING_DATA_INDEX( cl2 ), float32Array2, _leftBox2 );\n\t\tarrayToBox( BOUNDING_DATA_INDEX( cr2 ), float32Array2, _rightBox2 );\n\n\t\tconst leftIntersects = currBox.intersectsBox( _leftBox2 );\n\t\tconst rightIntersects = currBox.intersectsBox( _rightBox2 );\n\t\tif ( leftIntersects && rightIntersects ) {\n\n\t\t\t// continue to traverse both children if they both intersect\n\t\t\tresult = _traverse(\n\t\t\t\tnode1Index32, cl2, matrix2to1, matrix1to2, intersectsRangesFunc,\n\t\t\t\tnode1IndexByteOffset, node2IndexByteOffset, depth1, depth2 + 1,\n\t\t\t\tcurrBox, reversed,\n\t\t\t) || _traverse(\n\t\t\t\tnode1Index32, cr2, matrix2to1, matrix1to2, intersectsRangesFunc,\n\t\t\t\tnode1IndexByteOffset, node2IndexByteOffset, depth1, depth2 + 1,\n\t\t\t\tcurrBox, reversed,\n\t\t\t);\n\n\t\t} else if ( leftIntersects ) {\n\n\t\t\tif ( isLeaf1 ) {\n\n\t\t\t\t// if the current box is a leaf then just continue\n\t\t\t\tresult = _traverse(\n\t\t\t\t\tnode1Index32, cl2, matrix2to1, matrix1to2, intersectsRangesFunc,\n\t\t\t\t\tnode1IndexByteOffset, node2IndexByteOffset, depth1, depth2 + 1,\n\t\t\t\t\tcurrBox, reversed,\n\t\t\t\t);\n\n\t\t\t} else {\n\n\t\t\t\t// SWAP\n\t\t\t\t// if only one box intersects then we have to swap to the other bvh to continue\n\t\t\t\tconst newBox = _boxPool.getPrimitive();\n\t\t\t\tnewBox.copy( _leftBox2 ).applyMatrix4( matrix2to1 );\n\n\t\t\t\tconst cl1 = LEFT_NODE( node1Index32 );\n\t\t\t\tconst cr1 = RIGHT_NODE( node1Index32, uint32Array1 );\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( cl1 ), float32Array1, _leftBox1 );\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( cr1 ), float32Array1, _rightBox1 );\n\n\t\t\t\t// precompute the intersections otherwise the global boxes will be modified during traversal\n\t\t\t\tconst intersectCl1 = newBox.intersectsBox( _leftBox1 );\n\t\t\t\tconst intersectCr1 = newBox.intersectsBox( _rightBox1 );\n\t\t\t\tresult = (\n\t\t\t\t\tintersectCl1 && _traverse(\n\t\t\t\t\t\tcl2, cl1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\t\t\tnewBox, ! reversed,\n\t\t\t\t\t)\n\t\t\t\t) || (\n\t\t\t\t\tintersectCr1 && _traverse(\n\t\t\t\t\t\tcl2, cr1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\t\t\tnewBox, ! reversed,\n\t\t\t\t\t)\n\t\t\t\t);\n\n\t\t\t\t_boxPool.releasePrimitive( newBox );\n\n\t\t\t}\n\n\t\t} else if ( rightIntersects ) {\n\n\t\t\tif ( isLeaf1 ) {\n\n\t\t\t\t// if the current box is a leaf then just continue\n\t\t\t\tresult = _traverse(\n\t\t\t\t\tnode1Index32, cr2, matrix2to1, matrix1to2, intersectsRangesFunc,\n\t\t\t\t\tnode1IndexByteOffset, node2IndexByteOffset, depth1, depth2 + 1,\n\t\t\t\t\tcurrBox, reversed,\n\t\t\t\t);\n\n\t\t\t} else {\n\n\t\t\t\t// SWAP\n\t\t\t\t// if only one box intersects then we have to swap to the other bvh to continue\n\t\t\t\tconst newBox = _boxPool.getPrimitive();\n\t\t\t\tnewBox.copy( _rightBox2 ).applyMatrix4( matrix2to1 );\n\n\t\t\t\tconst cl1 = LEFT_NODE( node1Index32 );\n\t\t\t\tconst cr1 = RIGHT_NODE( node1Index32, uint32Array1 );\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( cl1 ), float32Array1, _leftBox1 );\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( cr1 ), float32Array1, _rightBox1 );\n\n\t\t\t\t// precompute the intersections otherwise the global boxes will be modified during traversal\n\t\t\t\tconst intersectCl1 = newBox.intersectsBox( _leftBox1 );\n\t\t\t\tconst intersectCr1 = newBox.intersectsBox( _rightBox1 );\n\t\t\t\tresult = (\n\t\t\t\t\tintersectCl1 && _traverse(\n\t\t\t\t\t\tcr2, cl1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\t\t\tnewBox, ! reversed,\n\t\t\t\t\t)\n\t\t\t\t) || (\n\t\t\t\t\tintersectCr1 && _traverse(\n\t\t\t\t\t\tcr2, cr1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\t\t\tnewBox, ! reversed,\n\t\t\t\t\t)\n\t\t\t\t);\n\n\t\t\t\t_boxPool.releasePrimitive( newBox );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\treturn result;\n\n}\n\n", "import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Box3, FrontSide } from 'three';\nimport { CENTER, BYTES_PER_NODE, IS_LEAFNODE_FLAG, SKIP_GENERATION } from './Constants.js';\nimport { buildPackedTree } from './build/buildTree.js';\nimport { OrientedBox } from '../math/OrientedBox.js';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nimport { ExtendedTrianglePool } from '../utils/ExtendedTrianglePool.js';\nimport { shapecast } from './cast/shapecast.js';\nimport { closestPointToPoint } from './cast/closestPointToPoint.js';\n\nimport { iterateOverTriangles } from './utils/iterationUtils.generated.js';\nimport { refit } from './cast/refit.generated.js';\nimport { raycast } from './cast/raycast.generated.js';\nimport { raycastFirst } from './cast/raycastFirst.generated.js';\nimport { intersectsGeometry } from './cast/intersectsGeometry.generated.js';\nimport { closestPointToGeometry } from './cast/closestPointToGeometry.generated.js';\n\nimport { iterateOverTriangles_indirect } from './utils/iterationUtils_indirect.generated.js';\nimport { refit_indirect } from './cast/refit_indirect.generated.js';\nimport { raycast_indirect } from './cast/raycast_indirect.generated.js';\nimport { raycastFirst_indirect } from './cast/raycastFirst_indirect.generated.js';\nimport { intersectsGeometry_indirect } from './cast/intersectsGeometry_indirect.generated.js';\nimport { closestPointToGeometry_indirect } from './cast/closestPointToGeometry_indirect.generated.js';\nimport { isSharedArrayBufferSupported } from '../utils/BufferUtils.js';\nimport { setTriangle } from '../utils/TriangleUtilities.js';\nimport { bvhcast } from './cast/bvhcast.js';\n\nconst obb = /* @__PURE__ */ new OrientedBox();\nconst tempBox = /* @__PURE__ */ new Box3();\nexport const DEFAULT_OPTIONS = {\n\tstrategy: CENTER,\n\tmaxDepth: 40,\n\tmaxLeafTris: 10,\n\tuseSharedArrayBuffer: false,\n\tsetBoundingBox: true,\n\tonProgress: null,\n\tindirect: false,\n\tverbose: true,\n\trange: null\n};\n\nexport class MeshBVH {\n\n\tstatic serialize( bvh, options = {} ) {\n\n\t\toptions = {\n\t\t\tcloneBuffers: true,\n\t\t\t...options,\n\t\t};\n\n\t\tconst geometry = bvh.geometry;\n\t\tconst rootData = bvh._roots;\n\t\tconst indirectBuffer = bvh._indirectBuffer;\n\t\tconst indexAttribute = geometry.getIndex();\n\t\tlet result;\n\t\tif ( options.cloneBuffers ) {\n\n\t\t\tresult = {\n\t\t\t\troots: rootData.map( root => root.slice() ),\n\t\t\t\tindex: indexAttribute ? indexAttribute.array.slice() : null,\n\t\t\t\tindirectBuffer: indirectBuffer ? indirectBuffer.slice() : null,\n\t\t\t};\n\n\t\t} else {\n\n\t\t\tresult = {\n\t\t\t\troots: rootData,\n\t\t\t\tindex: indexAttribute ? indexAttribute.array : null,\n\t\t\t\tindirectBuffer: indirectBuffer,\n\t\t\t};\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n\tstatic deserialize( data, geometry, options = {} ) {\n\n\t\toptions = {\n\t\t\tsetIndex: true,\n\t\t\tindirect: Boolean( data.indirectBuffer ),\n\t\t\t...options,\n\t\t};\n\n\t\tconst { index, roots, indirectBuffer } = data;\n\t\tconst bvh = new MeshBVH( geometry, { ...options, [ SKIP_GENERATION ]: true } );\n\t\tbvh._roots = roots;\n\t\tbvh._indirectBuffer = indirectBuffer || null;\n\n\t\tif ( options.setIndex ) {\n\n\t\t\tconst indexAttribute = geometry.getIndex();\n\t\t\tif ( indexAttribute === null ) {\n\n\t\t\t\tconst newIndex = new BufferAttribute( data.index, 1, false );\n\t\t\t\tgeometry.setIndex( newIndex );\n\n\t\t\t} else if ( indexAttribute.array !== index ) {\n\n\t\t\t\tindexAttribute.array.set( index );\n\t\t\t\tindexAttribute.needsUpdate = true;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn bvh;\n\n\t}\n\n\tget indirect() {\n\n\t\treturn ! ! this._indirectBuffer;\n\n\t}\n\n\tconstructor( geometry, options = {} ) {\n\n\t\tif ( ! geometry.isBufferGeometry ) {\n\n\t\t\tthrow new Error( 'MeshBVH: Only BufferGeometries are supported.' );\n\n\t\t} else if ( geometry.index && geometry.index.isInterleavedBufferAttribute ) {\n\n\t\t\tthrow new Error( 'MeshBVH: InterleavedBufferAttribute is not supported for the index attribute.' );\n\n\t\t}\n\n\t\t// default options\n\t\toptions = Object.assign( {\n\n\t\t\t...DEFAULT_OPTIONS,\n\n\t\t\t// undocumented options\n\n\t\t\t// Whether to skip generating the tree. Used for deserialization.\n\t\t\t[ SKIP_GENERATION ]: false,\n\n\t\t}, options );\n\n\t\tif ( options.useSharedArrayBuffer && ! isSharedArrayBufferSupported() ) {\n\n\t\t\tthrow new Error( 'MeshBVH: SharedArrayBuffer is not available.' );\n\n\t\t}\n\n\t\t// retain references to the geometry so we can use them it without having to\n\t\t// take a geometry reference in every function.\n\t\tthis.geometry = geometry;\n\t\tthis._roots = null;\n\t\tthis._indirectBuffer = null;\n\t\tif ( ! options[ SKIP_GENERATION ] ) {\n\n\t\t\tbuildPackedTree( this, options );\n\n\t\t\tif ( ! geometry.boundingBox && options.setBoundingBox ) {\n\n\t\t\t\tgeometry.boundingBox = this.getBoundingBox( new Box3() );\n\n\t\t\t}\n\n\t\t}\n\n\t\tthis.resolveTriangleIndex = options.indirect ? i => this._indirectBuffer[ i ] : i => i;\n\n\t}\n\n\trefit( nodeIndices = null ) {\n\n\t\tconst refitFunc = this.indirect ? refit_indirect : refit;\n\t\treturn refitFunc( this, nodeIndices );\n\n\t}\n\n\ttraverse( callback, rootIndex = 0 ) {\n\n\t\tconst buffer = this._roots[ rootIndex ];\n\t\tconst uint32Array = new Uint32Array( buffer );\n\t\tconst uint16Array = new Uint16Array( buffer );\n\t\t_traverse( 0 );\n\n\t\tfunction _traverse( node32Index, depth = 0 ) {\n\n\t\t\tconst node16Index = node32Index * 2;\n\t\t\tconst isLeaf = uint16Array[ node16Index + 15 ] === IS_LEAFNODE_FLAG;\n\t\t\tif ( isLeaf ) {\n\n\t\t\t\tconst offset = uint32Array[ node32Index + 6 ];\n\t\t\t\tconst count = uint16Array[ node16Index + 14 ];\n\t\t\t\tcallback( depth, isLeaf, new Float32Array( buffer, node32Index * 4, 6 ), offset, count );\n\n\t\t\t} else {\n\n\t\t\t\t// TODO: use node functions here\n\t\t\t\tconst left = node32Index + BYTES_PER_NODE / 4;\n\t\t\t\tconst right = uint32Array[ node32Index + 6 ];\n\t\t\t\tconst splitAxis = uint32Array[ node32Index + 7 ];\n\t\t\t\tconst stopTraversal = callback( depth, isLeaf, new Float32Array( buffer, node32Index * 4, 6 ), splitAxis );\n\n\t\t\t\tif ( ! stopTraversal ) {\n\n\t\t\t\t\t_traverse( left, depth + 1 );\n\t\t\t\t\t_traverse( right, depth + 1 );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/* Core Cast Functions */\n\traycast( ray, materialOrSide = FrontSide, near = 0, far = Infinity ) {\n\n\t\tconst roots = this._roots;\n\t\tconst geometry = this.geometry;\n\t\tconst intersects = [];\n\t\tconst isMaterial = materialOrSide.isMaterial;\n\t\tconst isArrayMaterial = Array.isArray( materialOrSide );\n\n\t\tconst groups = geometry.groups;\n\t\tconst side = isMaterial ? materialOrSide.side : materialOrSide;\n\t\tconst raycastFunc = this.indirect ? raycast_indirect : raycast;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tconst materialSide = isArrayMaterial ? materialOrSide[ groups[ i ].materialIndex ].side : side;\n\t\t\tconst startCount = intersects.length;\n\n\t\t\traycastFunc( this, i, materialSide, ray, intersects, near, far );\n\n\t\t\tif ( isArrayMaterial ) {\n\n\t\t\t\tconst materialIndex = groups[ i ].materialIndex;\n\t\t\t\tfor ( let j = startCount, jl = intersects.length; j < jl; j ++ ) {\n\n\t\t\t\t\tintersects[ j ].face.materialIndex = materialIndex;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn intersects;\n\n\t}\n\n\traycastFirst( ray, materialOrSide = FrontSide, near = 0, far = Infinity ) {\n\n\t\tconst roots = this._roots;\n\t\tconst geometry = this.geometry;\n\t\tconst isMaterial = materialOrSide.isMaterial;\n\t\tconst isArrayMaterial = Array.isArray( materialOrSide );\n\n\t\tlet closestResult = null;\n\n\t\tconst groups = geometry.groups;\n\t\tconst side = isMaterial ? materialOrSide.side : materialOrSide;\n\t\tconst raycastFirstFunc = this.indirect ? raycastFirst_indirect : raycastFirst;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tconst materialSide = isArrayMaterial ? materialOrSide[ groups[ i ].materialIndex ].side : side;\n\t\t\tconst result = raycastFirstFunc( this, i, materialSide, ray, near, far );\n\t\t\tif ( result != null && ( closestResult == null || result.distance < closestResult.distance ) ) {\n\n\t\t\t\tclosestResult = result;\n\t\t\t\tif ( isArrayMaterial ) {\n\n\t\t\t\t\tresult.face.materialIndex = groups[ i ].materialIndex;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn closestResult;\n\n\t}\n\n\tintersectsGeometry( otherGeometry, geomToMesh ) {\n\n\t\tlet result = false;\n\t\tconst roots = this._roots;\n\t\tconst intersectsGeometryFunc = this.indirect ? intersectsGeometry_indirect : intersectsGeometry;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tresult = intersectsGeometryFunc( this, i, otherGeometry, geomToMesh );\n\n\t\t\tif ( result ) {\n\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n\tshapecast( callbacks ) {\n\n\t\tconst triangle = ExtendedTrianglePool.getPrimitive();\n\t\tconst iterateFunc = this.indirect ? iterateOverTriangles_indirect : iterateOverTriangles;\n\t\tlet {\n\t\t\tboundsTraverseOrder,\n\t\t\tintersectsBounds,\n\t\t\tintersectsRange,\n\t\t\tintersectsTriangle,\n\t\t} = callbacks;\n\n\t\t// wrap the intersectsRange function\n\t\tif ( intersectsRange && intersectsTriangle ) {\n\n\t\t\tconst originalIntersectsRange = intersectsRange;\n\t\t\tintersectsRange = ( offset, count, contained, depth, nodeIndex ) => {\n\n\t\t\t\tif ( ! originalIntersectsRange( offset, count, contained, depth, nodeIndex ) ) {\n\n\t\t\t\t\treturn iterateFunc( offset, count, this, intersectsTriangle, contained, depth, triangle );\n\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\n\t\t\t};\n\n\t\t} else if ( ! intersectsRange ) {\n\n\t\t\tif ( intersectsTriangle ) {\n\n\t\t\t\tintersectsRange = ( offset, count, contained, depth ) => {\n\n\t\t\t\t\treturn iterateFunc( offset, count, this, intersectsTriangle, contained, depth, triangle );\n\n\t\t\t\t};\n\n\t\t\t} else {\n\n\t\t\t\tintersectsRange = ( offset, count, contained ) => {\n\n\t\t\t\t\treturn contained;\n\n\t\t\t\t};\n\n\t\t\t}\n\n\t\t}\n\n\t\t// run shapecast\n\t\tlet result = false;\n\t\tlet byteOffset = 0;\n\t\tconst roots = this._roots;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tconst root = roots[ i ];\n\t\t\tresult = shapecast( this, i, intersectsBounds, intersectsRange, boundsTraverseOrder, byteOffset );\n\n\t\t\tif ( result ) {\n\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tbyteOffset += root.byteLength;\n\n\t\t}\n\n\t\tExtendedTrianglePool.releasePrimitive( triangle );\n\n\t\treturn result;\n\n\t}\n\n\tbvhcast( otherBvh, matrixToLocal, callbacks ) {\n\n\t\tlet {\n\t\t\tintersectsRanges,\n\t\t\tintersectsTriangles,\n\t\t} = callbacks;\n\n\t\tconst triangle1 = ExtendedTrianglePool.getPrimitive();\n\t\tconst indexAttr1 = this.geometry.index;\n\t\tconst positionAttr1 = this.geometry.attributes.position;\n\t\tconst assignTriangle1 = this.indirect ?\n\t\t\ti1 => {\n\n\n\t\t\t\tconst ti = this.resolveTriangleIndex( i1 );\n\t\t\t\tsetTriangle( triangle1, ti * 3, indexAttr1, positionAttr1 );\n\n\t\t\t} :\n\t\t\ti1 => {\n\n\t\t\t\tsetTriangle( triangle1, i1 * 3, indexAttr1, positionAttr1 );\n\n\t\t\t};\n\n\t\tconst triangle2 = ExtendedTrianglePool.getPrimitive();\n\t\tconst indexAttr2 = otherBvh.geometry.index;\n\t\tconst positionAttr2 = otherBvh.geometry.attributes.position;\n\t\tconst assignTriangle2 = otherBvh.indirect ?\n\t\t\ti2 => {\n\n\t\t\t\tconst ti2 = otherBvh.resolveTriangleIndex( i2 );\n\t\t\t\tsetTriangle( triangle2, ti2 * 3, indexAttr2, positionAttr2 );\n\n\t\t\t} :\n\t\t\ti2 => {\n\n\t\t\t\tsetTriangle( triangle2, i2 * 3, indexAttr2, positionAttr2 );\n\n\t\t\t};\n\n\t\t// generate triangle callback if needed\n\t\tif ( intersectsTriangles ) {\n\n\t\t\tconst iterateOverDoubleTriangles = ( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) => {\n\n\t\t\t\tfor ( let i2 = offset2, l2 = offset2 + count2; i2 < l2; i2 ++ ) {\n\n\t\t\t\t\tassignTriangle2( i2 );\n\n\t\t\t\t\ttriangle2.a.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.b.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.c.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\tfor ( let i1 = offset1, l1 = offset1 + count1; i1 < l1; i1 ++ ) {\n\n\t\t\t\t\t\tassignTriangle1( i1 );\n\n\t\t\t\t\t\ttriangle1.needsUpdate = true;\n\n\t\t\t\t\t\tif ( intersectsTriangles( triangle1, triangle2, i1, i2, depth1, index1, depth2, index2 ) ) {\n\n\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\n\t\t\t};\n\n\t\t\tif ( intersectsRanges ) {\n\n\t\t\t\tconst originalIntersectsRanges = intersectsRanges;\n\t\t\t\tintersectsRanges = function ( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) {\n\n\t\t\t\t\tif ( ! originalIntersectsRanges( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) ) {\n\n\t\t\t\t\t\treturn iterateOverDoubleTriangles( offset1, count1, offset2, count2, depth1, index1, depth2, index2 );\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t};\n\n\t\t\t} else {\n\n\t\t\t\tintersectsRanges = iterateOverDoubleTriangles;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn bvhcast( this, otherBvh, matrixToLocal, intersectsRanges );\n\n\t}\n\n\n\t/* Derived Cast Functions */\n\tintersectsBox( box, boxToMesh ) {\n\n\t\tobb.set( box.min, box.max, boxToMesh );\n\t\tobb.needsUpdate = true;\n\n\t\treturn this.shapecast(\n\t\t\t{\n\t\t\t\tintersectsBounds: box => obb.intersectsBox( box ),\n\t\t\t\tintersectsTriangle: tri => obb.intersectsTriangle( tri )\n\t\t\t}\n\t\t);\n\n\t}\n\n\tintersectsSphere( sphere ) {\n\n\t\treturn this.shapecast(\n\t\t\t{\n\t\t\t\tintersectsBounds: box => sphere.intersectsBox( box ),\n\t\t\t\tintersectsTriangle: tri => tri.intersectsSphere( sphere )\n\t\t\t}\n\t\t);\n\n\t}\n\n\tclosestPointToGeometry( otherGeometry, geometryToBvh, target1 = { }, target2 = { }, minThreshold = 0, maxThreshold = Infinity ) {\n\n\t\tconst closestPointToGeometryFunc = this.indirect ? closestPointToGeometry_indirect : closestPointToGeometry;\n\t\treturn closestPointToGeometryFunc(\n\t\t\tthis,\n\t\t\totherGeometry,\n\t\t\tgeometryToBvh,\n\t\t\ttarget1,\n\t\t\ttarget2,\n\t\t\tminThreshold,\n\t\t\tmaxThreshold,\n\t\t);\n\n\t}\n\n\tclosestPointToPoint( point, target = { }, minThreshold = 0, maxThreshold = Infinity ) {\n\n\t\treturn closestPointToPoint(\n\t\t\tthis,\n\t\t\tpoint,\n\t\t\ttarget,\n\t\t\tminThreshold,\n\t\t\tmaxThreshold,\n\t\t);\n\n\t}\n\n\tgetBoundingBox( target ) {\n\n\t\ttarget.makeEmpty();\n\n\t\tconst roots = this._roots;\n\t\troots.forEach( buffer => {\n\n\t\t\tarrayToBox( 0, new Float32Array( buffer ), tempBox );\n\t\t\ttarget.union( tempBox );\n\n\t\t} );\n\n\t\treturn target;\n\n\t}\n\n}\n", "import { LineBasicMaterial, BufferAttribute, Box3, Group, MeshBasicMaterial, Object3D, BufferGeometry, Mesh, Matrix4 } from 'three';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nimport { MeshBVH } from '../core/MeshBVH.js';\n\nconst boundingBox = /* @__PURE__ */ new Box3();\nconst matrix = /* @__PURE__ */ new Matrix4();\n\nclass MeshBVHRootHelper extends Object3D {\n\n\tget isMesh() {\n\n\t\treturn ! this.displayEdges;\n\n\t}\n\n\tget isLineSegments() {\n\n\t\treturn this.displayEdges;\n\n\t}\n\n\tget isLine() {\n\n\t\treturn this.displayEdges;\n\n\t}\n\n\tgetVertexPosition( ...args ) {\n\n\t\t// implement this function so it works with Box3.setFromObject\n\t\treturn Mesh.prototype.getVertexPosition.call( this, ...args );\n\n\t}\n\n\tconstructor( bvh, material, depth = 10, group = 0 ) {\n\n\t\tsuper();\n\n\t\tthis.material = material;\n\t\tthis.geometry = new BufferGeometry();\n\t\tthis.name = 'MeshBVHRootHelper';\n\t\tthis.depth = depth;\n\t\tthis.displayParents = false;\n\t\tthis.bvh = bvh;\n\t\tthis.displayEdges = true;\n\t\tthis._group = group;\n\n\t}\n\n\traycast() {}\n\n\tupdate() {\n\n\t\tconst geometry = this.geometry;\n\t\tconst boundsTree = this.bvh;\n\t\tconst group = this._group;\n\t\tgeometry.dispose();\n\t\tthis.visible = false;\n\t\tif ( boundsTree ) {\n\n\t\t\t// count the number of bounds required\n\t\t\tconst targetDepth = this.depth - 1;\n\t\t\tconst displayParents = this.displayParents;\n\t\t\tlet boundsCount = 0;\n\t\t\tboundsTree.traverse( ( depth, isLeaf ) => {\n\n\t\t\t\tif ( depth >= targetDepth || isLeaf ) {\n\n\t\t\t\t\tboundsCount ++;\n\t\t\t\t\treturn true;\n\n\t\t\t\t} else if ( displayParents ) {\n\n\t\t\t\t\tboundsCount ++;\n\n\t\t\t\t}\n\n\t\t\t}, group );\n\n\t\t\t// fill in the position buffer with the bounds corners\n\t\t\tlet posIndex = 0;\n\t\t\tconst positionArray = new Float32Array( 8 * 3 * boundsCount );\n\t\t\tboundsTree.traverse( ( depth, isLeaf, boundingData ) => {\n\n\t\t\t\tconst terminate = depth >= targetDepth || isLeaf;\n\t\t\t\tif ( terminate || displayParents ) {\n\n\t\t\t\t\tarrayToBox( 0, boundingData, boundingBox );\n\n\t\t\t\t\tconst { min, max } = boundingBox;\n\t\t\t\t\tfor ( let x = - 1; x <= 1; x += 2 ) {\n\n\t\t\t\t\t\tconst xVal = x < 0 ? min.x : max.x;\n\t\t\t\t\t\tfor ( let y = - 1; y <= 1; y += 2 ) {\n\n\t\t\t\t\t\t\tconst yVal = y < 0 ? min.y : max.y;\n\t\t\t\t\t\t\tfor ( let z = - 1; z <= 1; z += 2 ) {\n\n\t\t\t\t\t\t\t\tconst zVal = z < 0 ? min.z : max.z;\n\t\t\t\t\t\t\t\tpositionArray[ posIndex + 0 ] = xVal;\n\t\t\t\t\t\t\t\tpositionArray[ posIndex + 1 ] = yVal;\n\t\t\t\t\t\t\t\tpositionArray[ posIndex + 2 ] = zVal;\n\n\t\t\t\t\t\t\t\tposIndex += 3;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn terminate;\n\n\t\t\t\t}\n\n\t\t\t}, group );\n\n\t\t\tlet indexArray;\n\t\t\tlet indices;\n\t\t\tif ( this.displayEdges ) {\n\n\t\t\t\t// fill in the index buffer to point to the corner points\n\t\t\t\tindices = new Uint8Array( [\n\t\t\t\t\t// x axis\n\t\t\t\t\t0, 4,\n\t\t\t\t\t1, 5,\n\t\t\t\t\t2, 6,\n\t\t\t\t\t3, 7,\n\n\t\t\t\t\t// y axis\n\t\t\t\t\t0, 2,\n\t\t\t\t\t1, 3,\n\t\t\t\t\t4, 6,\n\t\t\t\t\t5, 7,\n\n\t\t\t\t\t// z axis\n\t\t\t\t\t0, 1,\n\t\t\t\t\t2, 3,\n\t\t\t\t\t4, 5,\n\t\t\t\t\t6, 7,\n\t\t\t\t] );\n\n\t\t\t} else {\n\n\t\t\t\tindices = new Uint8Array( [\n\n\t\t\t\t\t// X-, X+\n\t\t\t\t\t0, 1, 2,\n\t\t\t\t\t2, 1, 3,\n\n\t\t\t\t\t4, 6, 5,\n\t\t\t\t\t6, 7, 5,\n\n\t\t\t\t\t// Y-, Y+\n\t\t\t\t\t1, 4, 5,\n\t\t\t\t\t0, 4, 1,\n\n\t\t\t\t\t2, 3, 6,\n\t\t\t\t\t3, 7, 6,\n\n\t\t\t\t\t// Z-, Z+\n\t\t\t\t\t0, 2, 4,\n\t\t\t\t\t2, 6, 4,\n\n\t\t\t\t\t1, 5, 3,\n\t\t\t\t\t3, 5, 7,\n\n\t\t\t\t] );\n\n\t\t\t}\n\n\t\t\tif ( positionArray.length > 65535 ) {\n\n\t\t\t\tindexArray = new Uint32Array( indices.length * boundsCount );\n\n\t\t\t} else {\n\n\t\t\t\tindexArray = new Uint16Array( indices.length * boundsCount );\n\n\t\t\t}\n\n\t\t\tconst indexLength = indices.length;\n\t\t\tfor ( let i = 0; i < boundsCount; i ++ ) {\n\n\t\t\t\tconst posOffset = i * 8;\n\t\t\t\tconst indexOffset = i * indexLength;\n\t\t\t\tfor ( let j = 0; j < indexLength; j ++ ) {\n\n\t\t\t\t\tindexArray[ indexOffset + j ] = posOffset + indices[ j ];\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// update the geometry\n\t\t\tgeometry.setIndex(\n\t\t\t\tnew BufferAttribute( indexArray, 1, false ),\n\t\t\t);\n\t\t\tgeometry.setAttribute(\n\t\t\t\t'position',\n\t\t\t\tnew BufferAttribute( positionArray, 3, false ),\n\t\t\t);\n\t\t\tthis.visible = true;\n\n\t\t}\n\n\t}\n\n}\n\nclass MeshBVHHelper extends Group {\n\n\tget color() {\n\n\t\treturn this.edgeMaterial.color;\n\n\t}\n\n\tget opacity() {\n\n\t\treturn this.edgeMaterial.opacity;\n\n\t}\n\n\tset opacity( v ) {\n\n\t\tthis.edgeMaterial.opacity = v;\n\t\tthis.meshMaterial.opacity = v;\n\n\t}\n\n\tconstructor( mesh = null, bvh = null, depth = 10 ) {\n\n\t\t// handle bvh, depth signature\n\t\tif ( mesh instanceof MeshBVH ) {\n\n\t\t\tdepth = bvh || 10;\n\t\t\tbvh = mesh;\n\t\t\tmesh = null;\n\n\t\t}\n\n\t\t// handle mesh, depth signature\n\t\tif ( typeof bvh === 'number' ) {\n\n\t\t\tdepth = bvh;\n\t\t\tbvh = null;\n\n\t\t}\n\n\t\tsuper();\n\n\t\tthis.name = 'MeshBVHHelper';\n\t\tthis.depth = depth;\n\t\tthis.mesh = mesh;\n\t\tthis.bvh = bvh;\n\t\tthis.displayParents = false;\n\t\tthis.displayEdges = true;\n\t\tthis.objectIndex = 0;\n\t\tthis._roots = [];\n\n\t\tconst edgeMaterial = new LineBasicMaterial( {\n\t\t\tcolor: 0x00FF88,\n\t\t\ttransparent: true,\n\t\t\topacity: 0.3,\n\t\t\tdepthWrite: false,\n\t\t} );\n\n\t\tconst meshMaterial = new MeshBasicMaterial( {\n\t\t\tcolor: 0x00FF88,\n\t\t\ttransparent: true,\n\t\t\topacity: 0.3,\n\t\t\tdepthWrite: false,\n\t\t} );\n\n\t\tmeshMaterial.color = edgeMaterial.color;\n\n\t\tthis.edgeMaterial = edgeMaterial;\n\t\tthis.meshMaterial = meshMaterial;\n\n\t\tthis.update();\n\n\t}\n\n\tupdate() {\n\n\t\tconst mesh = this.mesh;\n\t\tlet bvh = this.bvh || mesh.geometry.boundsTree || null;\n\t\tif ( mesh.isBatchedMesh && mesh.boundsTrees && ! bvh ) {\n\n\t\t\t// get the bvh from a batchedMesh if not provided\n\t\t\t// TODO: we should have an official way to get the geometry index cleanly\n\t\t\tconst drawInfo = mesh._drawInfo[ this.objectIndex ];\n\t\t\tif ( drawInfo ) {\n\n\t\t\t\tbvh = mesh.boundsTrees[ drawInfo.geometryIndex ] || bvh;\n\n\t\t\t}\n\n\t\t}\n\n\t\tconst totalRoots = bvh ? bvh._roots.length : 0;\n\t\twhile ( this._roots.length > totalRoots ) {\n\n\t\t\tconst root = this._roots.pop();\n\t\t\troot.geometry.dispose();\n\t\t\tthis.remove( root );\n\n\t\t}\n\n\t\tfor ( let i = 0; i < totalRoots; i ++ ) {\n\n\t\t\tconst { depth, edgeMaterial, meshMaterial, displayParents, displayEdges } = this;\n\n\t\t\tif ( i >= this._roots.length ) {\n\n\t\t\t\tconst root = new MeshBVHRootHelper( bvh, edgeMaterial, depth, i );\n\t\t\t\tthis.add( root );\n\t\t\t\tthis._roots.push( root );\n\n\t\t\t}\n\n\t\t\tconst root = this._roots[ i ];\n\t\t\troot.bvh = bvh;\n\t\t\troot.depth = depth;\n\t\t\troot.displayParents = displayParents;\n\t\t\troot.displayEdges = displayEdges;\n\t\t\troot.material = displayEdges ? edgeMaterial : meshMaterial;\n\t\t\troot.update();\n\n\t\t}\n\n\t}\n\n\tupdateMatrixWorld( ...args ) {\n\n\t\tconst mesh = this.mesh;\n\t\tconst parent = this.parent;\n\n\t\tif ( mesh !== null ) {\n\n\t\t\tmesh.updateWorldMatrix( true, false );\n\n\t\t\tif ( parent ) {\n\n\t\t\t\tthis.matrix\n\t\t\t\t\t.copy( parent.matrixWorld )\n\t\t\t\t\t.invert()\n\t\t\t\t\t.multiply( mesh.matrixWorld );\n\n\t\t\t} else {\n\n\t\t\t\tthis.matrix\n\t\t\t\t\t.copy( mesh.matrixWorld );\n\n\t\t\t}\n\n\t\t\t// handle batched and instanced mesh bvhs\n\t\t\tif ( mesh.isInstancedMesh || mesh.isBatchedMesh ) {\n\n\t\t\t\tmesh.getMatrixAt( this.objectIndex, matrix );\n\t\t\t\tthis.matrix.multiply( matrix );\n\n\t\t\t}\n\n\t\t\tthis.matrix.decompose(\n\t\t\t\tthis.position,\n\t\t\t\tthis.quaternion,\n\t\t\t\tthis.scale,\n\t\t\t);\n\n\t\t}\n\n\t\tsuper.updateMatrixWorld( ...args );\n\n\t}\n\n\tcopy( source ) {\n\n\t\tthis.depth = source.depth;\n\t\tthis.mesh = source.mesh;\n\t\tthis.bvh = source.bvh;\n\t\tthis.opacity = source.opacity;\n\t\tthis.color.copy( source.color );\n\n\t}\n\n\tclone() {\n\n\t\treturn new MeshBVHHelper( this.mesh, this.bvh, this.depth );\n\n\t}\n\n\tdispose() {\n\n\t\tthis.edgeMaterial.dispose();\n\t\tthis.meshMaterial.dispose();\n\n\t\tconst children = this.children;\n\t\tfor ( let i = 0, l = children.length; i < l; i ++ ) {\n\n\t\t\tchildren[ i ].geometry.dispose();\n\n\t\t}\n\n\t}\n\n}\n\nexport class MeshBVHVisualizer extends MeshBVHHelper {\n\n\tconstructor( ...args ) {\n\n\t\tsuper( ...args );\n\n\t\tconsole.warn( 'MeshBVHVisualizer: MeshBVHVisualizer has been deprecated. Use MeshBVHHelper, instead.' );\n\n\t}\n\n}\n\nexport { MeshBVHHelper };\n", "import { Box3, Vector3 } from 'three';\nimport { TRAVERSAL_COST, TRIANGLE_INTERSECT_COST } from '../core/Constants.js';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nimport { isSharedArrayBufferSupported } from '../utils/BufferUtils.js';\n\nconst _box1 = /* @__PURE__ */ new Box3();\nconst _box2 = /* @__PURE__ */ new Box3();\nconst _vec = /* @__PURE__ */ new Vector3();\n\n// https://stackoverflow.com/questions/1248302/how-to-get-the-size-of-a-javascript-object\nfunction getPrimitiveSize( el ) {\n\n\tswitch ( typeof el ) {\n\n\t\tcase 'number':\n\t\t\treturn 8;\n\t\tcase 'string':\n\t\t\treturn el.length * 2;\n\t\tcase 'boolean':\n\t\t\treturn 4;\n\t\tdefault:\n\t\t\treturn 0;\n\n\t}\n\n}\n\nfunction isTypedArray( arr ) {\n\n\tconst regex = /(Uint|Int|Float)(8|16|32)Array/;\n\treturn regex.test( arr.constructor.name );\n\n}\n\nfunction getRootExtremes( bvh, group ) {\n\n\tconst result = {\n\t\tnodeCount: 0,\n\t\tleafNodeCount: 0,\n\n\t\tdepth: {\n\t\t\tmin: Infinity, max: - Infinity\n\t\t},\n\t\ttris: {\n\t\t\tmin: Infinity, max: - Infinity\n\t\t},\n\t\tsplits: [ 0, 0, 0 ],\n\t\tsurfaceAreaScore: 0,\n\t};\n\n\tbvh.traverse( ( depth, isLeaf, boundingData, offsetOrSplit, count ) => {\n\n\t\tconst l0 = boundingData[ 0 + 3 ] - boundingData[ 0 ];\n\t\tconst l1 = boundingData[ 1 + 3 ] - boundingData[ 1 ];\n\t\tconst l2 = boundingData[ 2 + 3 ] - boundingData[ 2 ];\n\n\t\tconst surfaceArea = 2 * ( l0 * l1 + l1 * l2 + l2 * l0 );\n\n\t\tresult.nodeCount ++;\n\t\tif ( isLeaf ) {\n\n\t\t\tresult.leafNodeCount ++;\n\n\t\t\tresult.depth.min = Math.min( depth, result.depth.min );\n\t\t\tresult.depth.max = Math.max( depth, result.depth.max );\n\n\t\t\tresult.tris.min = Math.min( count, result.tris.min );\n\t\t\tresult.tris.max = Math.max( count, result.tris.max );\n\n\t\t\tresult.surfaceAreaScore += surfaceArea * TRIANGLE_INTERSECT_COST * count;\n\n\t\t} else {\n\n\t\t\tresult.splits[ offsetOrSplit ] ++;\n\n\t\t\tresult.surfaceAreaScore += surfaceArea * TRAVERSAL_COST;\n\n\t\t}\n\n\t}, group );\n\n\t// If there are no leaf nodes because the tree hasn't finished generating yet.\n\tif ( result.tris.min === Infinity ) {\n\n\t\tresult.tris.min = 0;\n\t\tresult.tris.max = 0;\n\n\t}\n\n\tif ( result.depth.min === Infinity ) {\n\n\t\tresult.depth.min = 0;\n\t\tresult.depth.max = 0;\n\n\t}\n\n\treturn result;\n\n}\n\nfunction getBVHExtremes( bvh ) {\n\n\treturn bvh._roots.map( ( root, i ) => getRootExtremes( bvh, i ) );\n\n}\n\nfunction estimateMemoryInBytes( obj ) {\n\n\tconst traversed = new Set();\n\tconst stack = [ obj ];\n\tlet bytes = 0;\n\n\twhile ( stack.length ) {\n\n\t\tconst curr = stack.pop();\n\t\tif ( traversed.has( curr ) ) {\n\n\t\t\tcontinue;\n\n\t\t}\n\n\t\ttraversed.add( curr );\n\n\t\tfor ( let key in curr ) {\n\n\t\t\tif ( ! Object.hasOwn( curr, key ) ) {\n\n\t\t\t\tcontinue;\n\n\t\t\t}\n\n\t\t\tbytes += getPrimitiveSize( key );\n\n\t\t\tconst value = curr[ key ];\n\t\t\tif ( value && ( typeof value === 'object' || typeof value === 'function' ) ) {\n\n\t\t\t\tif ( isTypedArray( value ) ) {\n\n\t\t\t\t\tbytes += value.byteLength;\n\n\t\t\t\t} else if ( isSharedArrayBufferSupported() && value instanceof SharedArrayBuffer ) {\n\n\t\t\t\t\tbytes += value.byteLength;\n\n\t\t\t\t} else if ( value instanceof ArrayBuffer ) {\n\n\t\t\t\t\tbytes += value.byteLength;\n\n\t\t\t\t} else {\n\n\t\t\t\t\tstack.push( value );\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tbytes += getPrimitiveSize( value );\n\n\t\t\t}\n\n\n\t\t}\n\n\t}\n\n\treturn bytes;\n\n}\n\nfunction validateBounds( bvh ) {\n\n\tconst geometry = bvh.geometry;\n\tconst depthStack = [];\n\tconst index = geometry.index;\n\tconst position = geometry.getAttribute( 'position' );\n\tlet passes = true;\n\n\tbvh.traverse( ( depth, isLeaf, boundingData, offset, count ) => {\n\n\t\tconst info = {\n\t\t\tdepth,\n\t\t\tisLeaf,\n\t\t\tboundingData,\n\t\t\toffset,\n\t\t\tcount,\n\t\t};\n\t\tdepthStack[ depth ] = info;\n\n\t\tarrayToBox( 0, boundingData, _box1 );\n\t\tconst parent = depthStack[ depth - 1 ];\n\n\t\tif ( isLeaf ) {\n\n\t\t\t// check triangles\n\t\t\tfor ( let i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\t\t\tconst triIndex = bvh.resolveTriangleIndex( i );\n\t\t\t\tlet i0 = 3 * triIndex;\n\t\t\t\tlet i1 = 3 * triIndex + 1;\n\t\t\t\tlet i2 = 3 * triIndex + 2;\n\n\t\t\t\tif ( index ) {\n\n\t\t\t\t\ti0 = index.getX( i0 );\n\t\t\t\t\ti1 = index.getX( i1 );\n\t\t\t\t\ti2 = index.getX( i2 );\n\n\t\t\t\t}\n\n\t\t\t\tlet isContained;\n\n\t\t\t\t_vec.fromBufferAttribute( position, i0 );\n\t\t\t\tisContained = _box1.containsPoint( _vec );\n\n\t\t\t\t_vec.fromBufferAttribute( position, i1 );\n\t\t\t\tisContained = isContained && _box1.containsPoint( _vec );\n\n\t\t\t\t_vec.fromBufferAttribute( position, i2 );\n\t\t\t\tisContained = isContained && _box1.containsPoint( _vec );\n\n\t\t\t\tconsole.assert( isContained, 'Leaf bounds does not fully contain triangle.' );\n\t\t\t\tpasses = passes && isContained;\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( parent ) {\n\n\t\t\t// check if my bounds fit in my parents\n\t\t\tarrayToBox( 0, boundingData, _box2 );\n\n\t\t\tconst isContained = _box2.containsBox( _box1 );\n\t\t\tconsole.assert( isContained, 'Parent bounds does not fully contain child.' );\n\t\t\tpasses = passes && isContained;\n\n\t\t}\n\n\t} );\n\n\treturn passes;\n\n}\n\n// Returns a simple, human readable object that represents the BVH.\nfunction getJSONStructure( bvh ) {\n\n\tconst depthStack = [];\n\n\tbvh.traverse( ( depth, isLeaf, boundingData, offset, count ) => {\n\n\t\tconst info = {\n\t\t\tbounds: arrayToBox( 0, boundingData, new Box3() ),\n\t\t};\n\n\t\tif ( isLeaf ) {\n\n\t\t\tinfo.count = count;\n\t\t\tinfo.offset = offset;\n\n\t\t} else {\n\n\t\t\tinfo.left = null;\n\t\t\tinfo.right = null;\n\n\t\t}\n\n\t\tdepthStack[ depth ] = info;\n\n\t\t// traversal hits the left then right node\n\t\tconst parent = depthStack[ depth - 1 ];\n\t\tif ( parent ) {\n\n\t\t\tif ( parent.left === null ) {\n\n\t\t\t\tparent.left = info;\n\n\t\t\t} else {\n\n\t\t\t\tparent.right = info;\n\n\t\t\t}\n\n\t\t}\n\n\t} );\n\n\treturn depthStack[ 0 ];\n\n}\n\nexport { estimateMemoryInBytes, getBVHExtremes, validateBounds, getJSONStructure };\n", "// converts the given BVH raycast intersection to align with the three.js raycast\n// structure (include object, world space distance and point).\nexport function convertRaycastIntersect( hit, object, raycaster ) {\n\n\tif ( hit === null ) {\n\n\t\treturn null;\n\n\t}\n\n\thit.point.applyMatrix4( object.matrixWorld );\n\thit.distance = hit.point.distanceTo( raycaster.ray.origin );\n\thit.object = object;\n\n\treturn hit;\n\n}\n", "import { Ray, Matrix4, Mesh, Vector3, Sphere, REVISION } from 'three';\nimport { convertRaycastIntersect } from './GeometryRayIntersectUtilities.js';\nimport { MeshBVH } from '../core/MeshBVH.js';\nimport * as THREE from 'three';\n\nconst BatchedMesh = THREE.BatchedMesh || null; // this is necessary to not break three.js r157-\nconst IS_REVISION_166 = parseInt( REVISION ) >= 166;\nconst ray = /* @__PURE__ */ new Ray();\nconst direction = /* @__PURE__ */ new Vector3();\nconst tmpInverseMatrix = /* @__PURE__ */ new Matrix4();\nconst origMeshRaycastFunc = Mesh.prototype.raycast;\nconst origBatchedRaycastFunc = BatchedMesh !== null ? BatchedMesh.prototype.raycast : null;\nconst _worldScale = /* @__PURE__ */ new Vector3();\nconst _mesh = /* @__PURE__ */ new Mesh();\nconst _batchIntersects = [];\n\nexport function acceleratedRaycast( raycaster, intersects ) {\n\n\tif ( this.isBatchedMesh ) {\n\n\t\tacceleratedBatchedMeshRaycast.call( this, raycaster, intersects );\n\n\t} else {\n\n\t\tacceleratedMeshRaycast.call( this, raycaster, intersects );\n\n\t}\n\n}\n\nfunction acceleratedBatchedMeshRaycast( raycaster, intersects ) {\n\n\tif ( this.boundsTrees ) {\n\n\t\tconst boundsTrees = this.boundsTrees;\n\t\tconst drawInfo = this._drawInfo;\n\t\tconst drawRanges = this._drawRanges;\n\t\tconst matrixWorld = this.matrixWorld;\n\n\t\t_mesh.material = this.material;\n\t\t_mesh.geometry = this.geometry;\n\n\t\tconst oldBoundsTree = _mesh.geometry.boundsTree;\n\t\tconst oldDrawRange = _mesh.geometry.drawRange;\n\n\t\tif ( _mesh.geometry.boundingSphere === null ) {\n\n\t\t\t_mesh.geometry.boundingSphere = new Sphere();\n\n\t\t}\n\n\t\t// TODO: provide new method to get instances count instead of 'drawInfo.length'\n\t\tfor ( let i = 0, l = drawInfo.length; i < l; i ++ ) {\n\n\t\t\tif ( ! this.getVisibleAt( i ) ) {\n\n\t\t\t\tcontinue;\n\n\t\t\t}\n\n\t\t\t// TODO: use getGeometryIndex\n\t\t\tconst geometryId = drawInfo[ i ].geometryIndex;\n\n\t\t\t_mesh.geometry.boundsTree = boundsTrees[ geometryId ];\n\n\t\t\tthis.getMatrixAt( i, _mesh.matrixWorld ).premultiply( matrixWorld );\n\n\t\t\tif ( ! _mesh.geometry.boundsTree ) {\n\n\t\t\t\tthis.getBoundingBoxAt( geometryId, _mesh.geometry.boundingBox );\n\t\t\t\tthis.getBoundingSphereAt( geometryId, _mesh.geometry.boundingSphere );\n\n\t\t\t\tconst drawRange = drawRanges[ geometryId ];\n\t\t\t\t_mesh.geometry.setDrawRange( drawRange.start, drawRange.count );\n\n\t\t\t}\n\n\t\t\t_mesh.raycast( raycaster, _batchIntersects );\n\n\t\t\tfor ( let j = 0, l = _batchIntersects.length; j < l; j ++ ) {\n\n\t\t\t\tconst intersect = _batchIntersects[ j ];\n\t\t\t\tintersect.object = this;\n\t\t\t\tintersect.batchId = i;\n\t\t\t\tintersects.push( intersect );\n\n\t\t\t}\n\n\t\t\t_batchIntersects.length = 0;\n\n\t\t}\n\n\t\t_mesh.geometry.boundsTree = oldBoundsTree;\n\t\t_mesh.geometry.drawRange = oldDrawRange;\n\t\t_mesh.material = null;\n\t\t_mesh.geometry = null;\n\n\t} else {\n\n\t\torigBatchedRaycastFunc.call( this, raycaster, intersects );\n\n\t}\n\n}\n\nfunction acceleratedMeshRaycast( raycaster, intersects ) {\n\n\tif ( this.geometry.boundsTree ) {\n\n\t\tif ( this.material === undefined ) return;\n\n\t\ttmpInverseMatrix.copy( this.matrixWorld ).invert();\n\t\tray.copy( raycaster.ray ).applyMatrix4( tmpInverseMatrix );\n\n\t\t_worldScale.setFromMatrixScale( this.matrixWorld );\n\t\tdirection.copy( ray.direction ).multiply( _worldScale );\n\n\t\tconst scaleFactor = direction.length();\n\t\tconst near = raycaster.near / scaleFactor;\n\t\tconst far = raycaster.far / scaleFactor;\n\n\t\tconst bvh = this.geometry.boundsTree;\n\t\tif ( raycaster.firstHitOnly === true ) {\n\n\t\t\tconst hit = convertRaycastIntersect( bvh.raycastFirst( ray, this.material, near, far ), this, raycaster );\n\t\t\tif ( hit ) {\n\n\t\t\t\tintersects.push( hit );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconst hits = bvh.raycast( ray, this.material, near, far );\n\t\t\tfor ( let i = 0, l = hits.length; i < l; i ++ ) {\n\n\t\t\t\tconst hit = convertRaycastIntersect( hits[ i ], this, raycaster );\n\t\t\t\tif ( hit ) {\n\n\t\t\t\t\tintersects.push( hit );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t} else {\n\n\t\torigMeshRaycastFunc.call( this, raycaster, intersects );\n\n\t}\n\n}\n\nexport function computeBoundsTree( options = {} ) {\n\n\tthis.boundsTree = new MeshBVH( this, options );\n\treturn this.boundsTree;\n\n}\n\nexport function disposeBoundsTree() {\n\n\tthis.boundsTree = null;\n\n}\n\nexport function computeBatchedBoundsTree( index = - 1, options = {} ) {\n\n\tif ( ! IS_REVISION_166 ) {\n\n\t\tthrow new Error( 'BatchedMesh: Three r166+ is required to compute bounds trees.' );\n\n\t}\n\n\tif ( options.indirect ) {\n\n\t\tconsole.warn( '\"Indirect\" is set to false because it is not supported for BatchedMesh.' );\n\n\t}\n\n\toptions = {\n\t\t...options,\n\t\tindirect: false,\n\t\trange: null\n\t};\n\n\tconst drawRanges = this._drawRanges;\n\tconst geometryCount = this._geometryCount;\n\tif ( ! this.boundsTrees ) {\n\n\t\tthis.boundsTrees = new Array( geometryCount ).fill( null );\n\n\t}\n\n\tconst boundsTrees = this.boundsTrees;\n\twhile ( boundsTrees.length < geometryCount ) {\n\n\t\tboundsTrees.push( null );\n\n\t}\n\n\tif ( index < 0 ) {\n\n\t\tfor ( let i = 0; i < geometryCount; i ++ ) {\n\n\t\t\toptions.range = drawRanges[ i ];\n\t\t\tboundsTrees[ i ] = new MeshBVH( this.geometry, options );\n\n\t\t}\n\n\t\treturn boundsTrees;\n\n\t} else {\n\n\t\tif ( index < drawRanges.length ) {\n\n\t\t\toptions.range = drawRanges[ index ];\n\t\t\tboundsTrees[ index ] = new MeshBVH( this.geometry, options );\n\n\t\t}\n\n\t\treturn boundsTrees[ index ] || null;\n\n\t}\n\n}\n\nexport function disposeBatchedBoundsTree( index = - 1 ) {\n\n\tif ( index < 0 ) {\n\n\t\tthis.boundsTrees.fill( null );\n\n\t} else {\n\n\t\tif ( index < this.boundsTree.length ) {\n\n\t\t\tthis.boundsTrees[ index ] = null;\n\n\t\t}\n\n\t}\n\n}\n", "import {\n\tDataTexture,\n\tFloatType,\n\tIntType,\n\tUnsignedIntType,\n\tByteType,\n\tUnsignedByteType,\n\tShortType,\n\tUnsignedShortType,\n\n\tRedFormat,\n\tRGFormat,\n\tRGBAFormat,\n\n\tRedIntegerFormat,\n\tRGIntegerFormat,\n\tRGBAIntegerFormat,\n\n\tNearestFilter,\n} from 'three';\n\nfunction countToStringFormat( count ) {\n\n\tswitch ( count ) {\n\n\t\tcase 1: return 'R';\n\t\tcase 2: return 'RG';\n\t\tcase 3: return 'RGBA';\n\t\tcase 4: return 'RGBA';\n\n\t}\n\n\tthrow new Error();\n\n}\n\nfunction countToFormat( count ) {\n\n\tswitch ( count ) {\n\n\t\tcase 1: return RedFormat;\n\t\tcase 2: return RGFormat;\n\t\tcase 3: return RGBAFormat;\n\t\tcase 4: return RGBAFormat;\n\n\t}\n\n}\n\nfunction countToIntFormat( count ) {\n\n\tswitch ( count ) {\n\n\t\tcase 1: return RedIntegerFormat;\n\t\tcase 2: return RGIntegerFormat;\n\t\tcase 3: return RGBAIntegerFormat;\n\t\tcase 4: return RGBAIntegerFormat;\n\n\t}\n\n}\n\nexport class VertexAttributeTexture extends DataTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis.minFilter = NearestFilter;\n\t\tthis.magFilter = NearestFilter;\n\t\tthis.generateMipmaps = false;\n\t\tthis.overrideItemSize = null;\n\t\tthis._forcedType = null;\n\n\t}\n\n\tupdateFrom( attr ) {\n\n\t\tconst overrideItemSize = this.overrideItemSize;\n\t\tconst originalItemSize = attr.itemSize;\n\t\tconst originalCount = attr.count;\n\t\tif ( overrideItemSize !== null ) {\n\n\t\t\tif ( ( originalItemSize * originalCount ) % overrideItemSize !== 0.0 ) {\n\n\t\t\t\tthrow new Error( 'VertexAttributeTexture: overrideItemSize must divide evenly into buffer length.' );\n\n\t\t\t}\n\n\t\t\tattr.itemSize = overrideItemSize;\n\t\t\tattr.count = originalCount * originalItemSize / overrideItemSize;\n\n\t\t}\n\n\t\tconst itemSize = attr.itemSize;\n\t\tconst count = attr.count;\n\t\tconst normalized = attr.normalized;\n\t\tconst originalBufferCons = attr.array.constructor;\n\t\tconst byteCount = originalBufferCons.BYTES_PER_ELEMENT;\n\t\tlet targetType = this._forcedType;\n\t\tlet finalStride = itemSize;\n\n\t\t// derive the type of texture this should be in the shader\n\t\tif ( targetType === null ) {\n\n\t\t\tswitch ( originalBufferCons ) {\n\n\t\t\t\tcase Float32Array:\n\t\t\t\t\ttargetType = FloatType;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase Uint8Array:\n\t\t\t\tcase Uint16Array:\n\t\t\t\tcase Uint32Array:\n\t\t\t\t\ttargetType = UnsignedIntType;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase Int8Array:\n\t\t\t\tcase Int16Array:\n\t\t\t\tcase Int32Array:\n\t\t\t\t\ttargetType = IntType;\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// get the target format to store the texture as\n\t\tlet type, format, normalizeValue, targetBufferCons;\n\t\tlet internalFormat = countToStringFormat( itemSize );\n\t\tswitch ( targetType ) {\n\n\t\t\tcase FloatType:\n\t\t\t\tnormalizeValue = 1.0;\n\t\t\t\tformat = countToFormat( itemSize );\n\n\t\t\t\tif ( normalized && byteCount === 1 ) {\n\n\t\t\t\t\ttargetBufferCons = originalBufferCons;\n\t\t\t\t\tinternalFormat += '8';\n\n\t\t\t\t\tif ( originalBufferCons === Uint8Array ) {\n\n\t\t\t\t\t\ttype = UnsignedByteType;\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\ttype = ByteType;\n\t\t\t\t\t\tinternalFormat += '_SNORM';\n\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttargetBufferCons = Float32Array;\n\t\t\t\t\tinternalFormat += '32F';\n\t\t\t\t\ttype = FloatType;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t\tcase IntType:\n\t\t\t\tinternalFormat += byteCount * 8 + 'I';\n\t\t\t\tnormalizeValue = normalized ? Math.pow( 2, originalBufferCons.BYTES_PER_ELEMENT * 8 - 1 ) : 1.0;\n\t\t\t\tformat = countToIntFormat( itemSize );\n\n\t\t\t\tif ( byteCount === 1 ) {\n\n\t\t\t\t\ttargetBufferCons = Int8Array;\n\t\t\t\t\ttype = ByteType;\n\n\t\t\t\t} else if ( byteCount === 2 ) {\n\n\t\t\t\t\ttargetBufferCons = Int16Array;\n\t\t\t\t\ttype = ShortType;\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttargetBufferCons = Int32Array;\n\t\t\t\t\ttype = IntType;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t\tcase UnsignedIntType:\n\t\t\t\tinternalFormat += byteCount * 8 + 'UI';\n\t\t\t\tnormalizeValue = normalized ? Math.pow( 2, originalBufferCons.BYTES_PER_ELEMENT * 8 - 1 ) : 1.0;\n\t\t\t\tformat = countToIntFormat( itemSize );\n\n\t\t\t\tif ( byteCount === 1 ) {\n\n\t\t\t\t\ttargetBufferCons = Uint8Array;\n\t\t\t\t\ttype = UnsignedByteType;\n\n\t\t\t\t} else if ( byteCount === 2 ) {\n\n\t\t\t\t\ttargetBufferCons = Uint16Array;\n\t\t\t\t\ttype = UnsignedShortType;\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttargetBufferCons = Uint32Array;\n\t\t\t\t\ttype = UnsignedIntType;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\t// there will be a mismatch between format length and final length because\n\t\t// RGBFormat and RGBIntegerFormat was removed\n\t\tif ( finalStride === 3 && ( format === RGBAFormat || format === RGBAIntegerFormat ) ) {\n\n\t\t\tfinalStride = 4;\n\n\t\t}\n\n\t\t// copy the data over to the new texture array\n\t\tconst dimension = Math.ceil( Math.sqrt( count ) ) || 1;\n\t\tconst length = finalStride * dimension * dimension;\n\t\tconst dataArray = new targetBufferCons( length );\n\n\t\t// temporarily set the normalized state to false since we have custom normalization logic\n\t\tconst originalNormalized = attr.normalized;\n\t\tattr.normalized = false;\n\t\tfor ( let i = 0; i < count; i ++ ) {\n\n\t\t\tconst ii = finalStride * i;\n\t\t\tdataArray[ ii ] = attr.getX( i ) / normalizeValue;\n\n\t\t\tif ( itemSize >= 2 ) {\n\n\t\t\t\tdataArray[ ii + 1 ] = attr.getY( i ) / normalizeValue;\n\n\t\t\t}\n\n\t\t\tif ( itemSize >= 3 ) {\n\n\t\t\t\tdataArray[ ii + 2 ] = attr.getZ( i ) / normalizeValue;\n\n\t\t\t\tif ( finalStride === 4 ) {\n\n\t\t\t\t\tdataArray[ ii + 3 ] = 1.0;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( itemSize >= 4 ) {\n\n\t\t\t\tdataArray[ ii + 3 ] = attr.getW( i ) / normalizeValue;\n\n\t\t\t}\n\n\t\t}\n\n\t\tattr.normalized = originalNormalized;\n\n\t\tthis.internalFormat = internalFormat;\n\t\tthis.format = format;\n\t\tthis.type = type;\n\t\tthis.image.width = dimension;\n\t\tthis.image.height = dimension;\n\t\tthis.image.data = dataArray;\n\t\tthis.needsUpdate = true;\n\t\tthis.dispose();\n\n\t\tattr.itemSize = originalItemSize;\n\t\tattr.count = originalCount;\n\n\t}\n\n}\n\nexport class UIntVertexAttributeTexture extends VertexAttributeTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis._forcedType = UnsignedIntType;\n\n\t}\n\n}\n\nexport class IntVertexAttributeTexture extends VertexAttributeTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis._forcedType = IntType;\n\n\t}\n\n\n}\n\nexport class FloatVertexAttributeTexture extends VertexAttributeTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis._forcedType = FloatType;\n\n\t}\n\n}\n", "import {\n\tDataTexture,\n\tFloatType,\n\tUnsignedIntType,\n\tRGBAFormat,\n\tRGIntegerFormat,\n\tNearestFilter,\n\tBufferAttribute,\n} from 'three';\nimport {\n\tFloatVertexAttributeTexture,\n\tUIntVertexAttributeTexture,\n} from './VertexAttributeTexture.js';\nimport { BYTES_PER_NODE } from '../core/Constants.js';\nimport {\n\tBOUNDING_DATA_INDEX,\n\tCOUNT,\n\tIS_LEAF,\n\tRIGHT_NODE,\n\tOFFSET,\n\tSPLIT_AXIS,\n} from '../core/utils/nodeBufferUtils.js';\nimport { getIndexArray, getVertexCount } from '../core/build/geometryUtils.js';\n\nexport class MeshBVHUniformStruct {\n\n\tconstructor() {\n\n\t\tthis.index = new UIntVertexAttributeTexture();\n\t\tthis.position = new FloatVertexAttributeTexture();\n\t\tthis.bvhBounds = new DataTexture();\n\t\tthis.bvhContents = new DataTexture();\n\t\tthis._cachedIndexAttr = null;\n\n\t\tthis.index.overrideItemSize = 3;\n\n\t}\n\n\tupdateFrom( bvh ) {\n\n\t\tconst { geometry } = bvh;\n\t\tbvhToTextures( bvh, this.bvhBounds, this.bvhContents );\n\n\t\tthis.position.updateFrom( geometry.attributes.position );\n\n\t\t// dereference a new index attribute if we're using indirect storage\n\t\tif ( bvh.indirect ) {\n\n\t\t\tconst indirectBuffer = bvh._indirectBuffer;\n\t\t\tif (\n\t\t\t\tthis._cachedIndexAttr === null ||\n\t\t\t\tthis._cachedIndexAttr.count !== indirectBuffer.length\n\t\t\t) {\n\n\t\t\t\tif ( geometry.index ) {\n\n\t\t\t\t\tthis._cachedIndexAttr = geometry.index.clone();\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconst array = getIndexArray( getVertexCount( geometry ) );\n\t\t\t\t\tthis._cachedIndexAttr = new BufferAttribute( array, 1, false );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tdereferenceIndex( geometry, indirectBuffer, this._cachedIndexAttr );\n\t\t\tthis.index.updateFrom( this._cachedIndexAttr );\n\n\t\t} else {\n\n\t\t\tthis.index.updateFrom( geometry.index );\n\n\t\t}\n\n\t}\n\n\tdispose() {\n\n\t\tconst { index, position, bvhBounds, bvhContents } = this;\n\n\t\tif ( index ) index.dispose();\n\t\tif ( position ) position.dispose();\n\t\tif ( bvhBounds ) bvhBounds.dispose();\n\t\tif ( bvhContents ) bvhContents.dispose();\n\n\t}\n\n}\n\nfunction dereferenceIndex( geometry, indirectBuffer, target ) {\n\n\tconst unpacked = target.array;\n\tconst indexArray = geometry.index ? geometry.index.array : null;\n\tfor ( let i = 0, l = indirectBuffer.length; i < l; i ++ ) {\n\n\t\tconst i3 = 3 * i;\n\t\tconst v3 = 3 * indirectBuffer[ i ];\n\t\tfor ( let c = 0; c < 3; c ++ ) {\n\n\t\t\tunpacked[ i3 + c ] = indexArray ? indexArray[ v3 + c ] : v3 + c;\n\n\t\t}\n\n\t}\n\n}\n\nfunction bvhToTextures( bvh, boundsTexture, contentsTexture ) {\n\n\tconst roots = bvh._roots;\n\n\tif ( roots.length !== 1 ) {\n\n\t\tthrow new Error( 'MeshBVHUniformStruct: Multi-root BVHs not supported.' );\n\n\t}\n\n\tconst root = roots[ 0 ];\n\tconst uint16Array = new Uint16Array( root );\n\tconst uint32Array = new Uint32Array( root );\n\tconst float32Array = new Float32Array( root );\n\n\t// Both bounds need two elements per node so compute the height so it's twice as long as\n\t// the width so we can expand the row by two and still have a square texture\n\tconst nodeCount = root.byteLength / BYTES_PER_NODE;\n\tconst boundsDimension = 2 * Math.ceil( Math.sqrt( nodeCount / 2 ) );\n\tconst boundsArray = new Float32Array( 4 * boundsDimension * boundsDimension );\n\n\tconst contentsDimension = Math.ceil( Math.sqrt( nodeCount ) );\n\tconst contentsArray = new Uint32Array( 2 * contentsDimension * contentsDimension );\n\n\tfor ( let i = 0; i < nodeCount; i ++ ) {\n\n\t\tconst nodeIndex32 = i * BYTES_PER_NODE / 4;\n\t\tconst nodeIndex16 = nodeIndex32 * 2;\n\t\tconst boundsIndex = BOUNDING_DATA_INDEX( nodeIndex32 );\n\t\tfor ( let b = 0; b < 3; b ++ ) {\n\n\t\t\tboundsArray[ 8 * i + 0 + b ] = float32Array[ boundsIndex + 0 + b ];\n\t\t\tboundsArray[ 8 * i + 4 + b ] = float32Array[ boundsIndex + 3 + b ];\n\n\t\t}\n\n\t\tif ( IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\t\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\n\t\t\tconst mergedLeafCount = 0xffff0000 | count;\n\t\t\tcontentsArray[ i * 2 + 0 ] = mergedLeafCount;\n\t\t\tcontentsArray[ i * 2 + 1 ] = offset;\n\n\t\t} else {\n\n\t\t\tconst rightIndex = 4 * RIGHT_NODE( nodeIndex32, uint32Array ) / BYTES_PER_NODE;\n\t\t\tconst splitAxis = SPLIT_AXIS( nodeIndex32, uint32Array );\n\n\t\t\tcontentsArray[ i * 2 + 0 ] = splitAxis;\n\t\t\tcontentsArray[ i * 2 + 1 ] = rightIndex;\n\n\t\t}\n\n\t}\n\n\tboundsTexture.image.data = boundsArray;\n\tboundsTexture.image.width = boundsDimension;\n\tboundsTexture.image.height = boundsDimension;\n\tboundsTexture.format = RGBAFormat;\n\tboundsTexture.type = FloatType;\n\tboundsTexture.internalFormat = 'RGBA32F';\n\tboundsTexture.minFilter = NearestFilter;\n\tboundsTexture.magFilter = NearestFilter;\n\tboundsTexture.generateMipmaps = false;\n\tboundsTexture.needsUpdate = true;\n\tboundsTexture.dispose();\n\n\tcontentsTexture.image.data = contentsArray;\n\tcontentsTexture.image.width = contentsDimension;\n\tcontentsTexture.image.height = contentsDimension;\n\tcontentsTexture.format = RGIntegerFormat;\n\tcontentsTexture.type = UnsignedIntType;\n\tcontentsTexture.internalFormat = 'RG32UI';\n\tcontentsTexture.minFilter = NearestFilter;\n\tcontentsTexture.magFilter = NearestFilter;\n\tcontentsTexture.generateMipmaps = false;\n\tcontentsTexture.needsUpdate = true;\n\tcontentsTexture.dispose();\n\n}\n", "import { Buffer<PERSON>ttribute, BufferGeometry, Vector3, Vector4, Matrix4, Matrix3 } from 'three';\n\nconst _positionVector = /*@__PURE__*/ new Vector3();\nconst _normalVector = /*@__PURE__*/ new Vector3();\nconst _tangentVector = /*@__PURE__*/ new Vector3();\nconst _tangentVector4 = /*@__PURE__*/ new Vector4();\n\nconst _morphVector = /*@__PURE__*/ new Vector3();\nconst _temp = /*@__PURE__*/ new Vector3();\n\nconst _skinIndex = /*@__PURE__*/ new Vector4();\nconst _skinWeight = /*@__PURE__*/ new Vector4();\nconst _matrix = /*@__PURE__*/ new Matrix4();\nconst _boneMatrix = /*@__PURE__*/ new Matrix4();\n\n// Confirms that the two provided attributes are compatible\nfunction validateAttributes( attr1, attr2 ) {\n\n\tif ( ! attr1 && ! attr2 ) {\n\n\t\treturn;\n\n\t}\n\n\tconst sameCount = attr1.count === attr2.count;\n\tconst sameNormalized = attr1.normalized === attr2.normalized;\n\tconst sameType = attr1.array.constructor === attr2.array.constructor;\n\tconst sameItemSize = attr1.itemSize === attr2.itemSize;\n\n\tif ( ! sameCount || ! sameNormalized || ! sameType || ! sameItemSize ) {\n\n\t\tthrow new Error();\n\n\t}\n\n}\n\n// Clones the given attribute with a new compatible buffer attribute but no data\nfunction createAttributeClone( attr, countOverride = null ) {\n\n\tconst cons = attr.array.constructor;\n\tconst normalized = attr.normalized;\n\tconst itemSize = attr.itemSize;\n\tconst count = countOverride === null ? attr.count : countOverride;\n\n\treturn new BufferAttribute( new cons( itemSize * count ), itemSize, normalized );\n\n}\n\n// target offset is the number of elements in the target buffer stride to skip before copying the\n// attributes contents in to.\nfunction copyAttributeContents( attr, target, targetOffset = 0 ) {\n\n\tif ( attr.isInterleavedBufferAttribute ) {\n\n\t\tconst itemSize = attr.itemSize;\n\t\tfor ( let i = 0, l = attr.count; i < l; i ++ ) {\n\n\t\t\tconst io = i + targetOffset;\n\t\t\ttarget.setX( io, attr.getX( i ) );\n\t\t\tif ( itemSize >= 2 ) target.setY( io, attr.getY( i ) );\n\t\t\tif ( itemSize >= 3 ) target.setZ( io, attr.getZ( i ) );\n\t\t\tif ( itemSize >= 4 ) target.setW( io, attr.getW( i ) );\n\n\t\t}\n\n\t} else {\n\n\t\tconst array = target.array;\n\t\tconst cons = array.constructor;\n\t\tconst byteOffset = array.BYTES_PER_ELEMENT * attr.itemSize * targetOffset;\n\t\tconst temp = new cons( array.buffer, byteOffset, attr.array.length );\n\t\ttemp.set( attr.array );\n\n\t}\n\n}\n\n// Adds the \"matrix\" multiplied by \"scale\" to \"target\"\nfunction addScaledMatrix( target, matrix, scale ) {\n\n\tconst targetArray = target.elements;\n\tconst matrixArray = matrix.elements;\n\tfor ( let i = 0, l = matrixArray.length; i < l; i ++ ) {\n\n\t\ttargetArray[ i ] += matrixArray[ i ] * scale;\n\n\t}\n\n}\n\n// A version of \"SkinnedMesh.boneTransform\" for normals\nfunction boneNormalTransform( mesh, index, target ) {\n\n\tconst skeleton = mesh.skeleton;\n\tconst geometry = mesh.geometry;\n\tconst bones = skeleton.bones;\n\tconst boneInverses = skeleton.boneInverses;\n\n\t_skinIndex.fromBufferAttribute( geometry.attributes.skinIndex, index );\n\t_skinWeight.fromBufferAttribute( geometry.attributes.skinWeight, index );\n\n\t_matrix.elements.fill( 0 );\n\n\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\tconst weight = _skinWeight.getComponent( i );\n\n\t\tif ( weight !== 0 ) {\n\n\t\t\tconst boneIndex = _skinIndex.getComponent( i );\n\t\t\t_boneMatrix.multiplyMatrices( bones[ boneIndex ].matrixWorld, boneInverses[ boneIndex ] );\n\n\t\t\taddScaledMatrix( _matrix, _boneMatrix, weight );\n\n\t\t}\n\n\t}\n\n\t_matrix.multiply( mesh.bindMatrix ).premultiply( mesh.bindMatrixInverse );\n\ttarget.transformDirection( _matrix );\n\n\treturn target;\n\n}\n\n// Applies the morph target data to the target vector\nfunction applyMorphTarget( morphData, morphInfluences, morphTargetsRelative, i, target ) {\n\n\t_morphVector.set( 0, 0, 0 );\n\tfor ( let j = 0, jl = morphData.length; j < jl; j ++ ) {\n\n\t\tconst influence = morphInfluences[ j ];\n\t\tconst morphAttribute = morphData[ j ];\n\n\t\tif ( influence === 0 ) continue;\n\n\t\t_temp.fromBufferAttribute( morphAttribute, i );\n\n\t\tif ( morphTargetsRelative ) {\n\n\t\t\t_morphVector.addScaledVector( _temp, influence );\n\n\t\t} else {\n\n\t\t\t_morphVector.addScaledVector( _temp.sub( target ), influence );\n\n\t\t}\n\n\t}\n\n\ttarget.add( _morphVector );\n\n}\n\n// Modified version of BufferGeometryUtils.mergeBufferGeometries that ignores morph targets and updates a attributes in place\nfunction mergeBufferGeometries( geometries, options = { useGroups: false, updateIndex: false, skipAttributes: [] }, targetGeometry = new BufferGeometry() ) {\n\n\tconst isIndexed = geometries[ 0 ].index !== null;\n\tconst { useGroups = false, updateIndex = false, skipAttributes = [] } = options;\n\n\tconst attributesUsed = new Set( Object.keys( geometries[ 0 ].attributes ) );\n\tconst attributes = {};\n\n\tlet offset = 0;\n\n\ttargetGeometry.clearGroups();\n\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\tconst geometry = geometries[ i ];\n\t\tlet attributesCount = 0;\n\n\t\t// ensure that all geometries are indexed, or none\n\t\tif ( isIndexed !== ( geometry.index !== null ) ) {\n\n\t\t\tthrow new Error( 'StaticGeometryGenerator: All geometries must have compatible attributes; make sure index attribute exists among all geometries, or in none of them.' );\n\n\t\t}\n\n\t\t// gather attributes, exit early if they're different\n\t\tfor ( const name in geometry.attributes ) {\n\n\t\t\tif ( ! attributesUsed.has( name ) ) {\n\n\t\t\t\tthrow new Error( 'StaticGeometryGenerator: All geometries must have compatible attributes; make sure \"' + name + '\" attribute exists among all geometries, or in none of them.' );\n\n\t\t\t}\n\n\t\t\tif ( attributes[ name ] === undefined ) {\n\n\t\t\t\tattributes[ name ] = [];\n\n\t\t\t}\n\n\t\t\tattributes[ name ].push( geometry.attributes[ name ] );\n\t\t\tattributesCount ++;\n\n\t\t}\n\n\t\t// ensure geometries have the same number of attributes\n\t\tif ( attributesCount !== attributesUsed.size ) {\n\n\t\t\tthrow new Error( 'StaticGeometryGenerator: Make sure all geometries have the same number of attributes.' );\n\n\t\t}\n\n\t\tif ( useGroups ) {\n\n\t\t\tlet count;\n\t\t\tif ( isIndexed ) {\n\n\t\t\t\tcount = geometry.index.count;\n\n\t\t\t} else if ( geometry.attributes.position !== undefined ) {\n\n\t\t\t\tcount = geometry.attributes.position.count;\n\n\t\t\t} else {\n\n\t\t\t\tthrow new Error( 'StaticGeometryGenerator: The geometry must have either an index or a position attribute' );\n\n\t\t\t}\n\n\t\t\ttargetGeometry.addGroup( offset, count, i );\n\t\t\toffset += count;\n\n\t\t}\n\n\t}\n\n\t// merge indices\n\tif ( isIndexed ) {\n\n\t\tlet forceUpdateIndex = false;\n\t\tif ( ! targetGeometry.index ) {\n\n\t\t\tlet indexCount = 0;\n\t\t\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\t\t\tindexCount += geometries[ i ].index.count;\n\n\t\t\t}\n\n\t\t\ttargetGeometry.setIndex( new BufferAttribute( new Uint32Array( indexCount ), 1, false ) );\n\t\t\tforceUpdateIndex = true;\n\n\t\t}\n\n\t\tif ( updateIndex || forceUpdateIndex ) {\n\n\t\t\tconst targetIndex = targetGeometry.index;\n\t\t\tlet targetOffset = 0;\n\t\t\tlet indexOffset = 0;\n\t\t\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\t\t\tconst geometry = geometries[ i ];\n\t\t\t\tconst index = geometry.index;\n\t\t\t\tif ( skipAttributes[ i ] !== true ) {\n\n\t\t\t\t\tfor ( let j = 0; j < index.count; ++ j ) {\n\n\t\t\t\t\t\ttargetIndex.setX( targetOffset, index.getX( j ) + indexOffset );\n\t\t\t\t\t\ttargetOffset ++;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tindexOffset += geometry.attributes.position.count;\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t// merge attributes\n\tfor ( const name in attributes ) {\n\n\t\tconst attrList = attributes[ name ];\n\t\tif ( ! ( name in targetGeometry.attributes ) ) {\n\n\t\t\tlet count = 0;\n\t\t\tfor ( const key in attrList ) {\n\n\t\t\t\tcount += attrList[ key ].count;\n\n\t\t\t}\n\n\t\t\ttargetGeometry.setAttribute( name, createAttributeClone( attributes[ name ][ 0 ], count ) );\n\n\t\t}\n\n\t\tconst targetAttribute = targetGeometry.attributes[ name ];\n\t\tlet offset = 0;\n\t\tfor ( let i = 0, l = attrList.length; i < l; i ++ ) {\n\n\t\t\tconst attr = attrList[ i ];\n\t\t\tif ( skipAttributes[ i ] !== true ) {\n\n\t\t\t\tcopyAttributeContents( attr, targetAttribute, offset );\n\n\t\t\t}\n\n\t\t\toffset += attr.count;\n\n\t\t}\n\n\t}\n\n\treturn targetGeometry;\n\n}\n\nfunction checkTypedArrayEquality( a, b ) {\n\n\tif ( a === null || b === null ) {\n\n\t\treturn a === b;\n\n\t}\n\n\tif ( a.length !== b.length ) {\n\n\t\treturn false;\n\n\t}\n\n\tfor ( let i = 0, l = a.length; i < l; i ++ ) {\n\n\t\tif ( a[ i ] !== b[ i ] ) {\n\n\t\t\treturn false;\n\n\t\t}\n\n\t}\n\n\treturn true;\n\n}\n\nfunction invertGeometry( geometry ) {\n\n\tconst { index, attributes } = geometry;\n\tif ( index ) {\n\n\t\tfor ( let i = 0, l = index.count; i < l; i += 3 ) {\n\n\t\t\tconst v0 = index.getX( i );\n\t\t\tconst v2 = index.getX( i + 2 );\n\t\t\tindex.setX( i, v2 );\n\t\t\tindex.setX( i + 2, v0 );\n\n\t\t}\n\n\t} else {\n\n\t\tfor ( const key in attributes ) {\n\n\t\t\tconst attr = attributes[ key ];\n\t\t\tconst itemSize = attr.itemSize;\n\t\t\tfor ( let i = 0, l = attr.count; i < l; i += 3 ) {\n\n\t\t\t\tfor ( let j = 0; j < itemSize; j ++ ) {\n\n\t\t\t\t\tconst v0 = attr.getComponent( i, j );\n\t\t\t\t\tconst v2 = attr.getComponent( i + 2, j );\n\t\t\t\t\tattr.setComponent( i, j, v2 );\n\t\t\t\t\tattr.setComponent( i + 2, j, v0 );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\treturn geometry;\n\n\n}\n\n// Checks whether the geometry changed between this and last evaluation\nclass GeometryDiff {\n\n\tconstructor( mesh ) {\n\n\t\tthis.matrixWorld = new Matrix4();\n\t\tthis.geometryHash = null;\n\t\tthis.boneMatrices = null;\n\t\tthis.primitiveCount = - 1;\n\t\tthis.mesh = mesh;\n\n\t\tthis.update();\n\n\t}\n\n\tupdate() {\n\n\t\tconst mesh = this.mesh;\n\t\tconst geometry = mesh.geometry;\n\t\tconst skeleton = mesh.skeleton;\n\t\tconst primitiveCount = ( geometry.index ? geometry.index.count : geometry.attributes.position.count ) / 3;\n\t\tthis.matrixWorld.copy( mesh.matrixWorld );\n\t\tthis.geometryHash = geometry.attributes.position.version;\n\t\tthis.primitiveCount = primitiveCount;\n\n\t\tif ( skeleton ) {\n\n\t\t\t// ensure the bone matrix array is updated to the appropriate length\n\t\t\tif ( ! skeleton.boneTexture ) {\n\n\t\t\t\tskeleton.computeBoneTexture();\n\n\t\t\t}\n\n\t\t\tskeleton.update();\n\n\t\t\t// copy data if possible otherwise clone it\n\t\t\tconst boneMatrices = skeleton.boneMatrices;\n\t\t\tif ( ! this.boneMatrices || this.boneMatrices.length !== boneMatrices.length ) {\n\n\t\t\t\tthis.boneMatrices = boneMatrices.slice();\n\n\t\t\t} else {\n\n\t\t\t\tthis.boneMatrices.set( boneMatrices );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tthis.boneMatrices = null;\n\n\t\t}\n\n\t}\n\n\tdidChange() {\n\n\t\tconst mesh = this.mesh;\n\t\tconst geometry = mesh.geometry;\n\t\tconst primitiveCount = ( geometry.index ? geometry.index.count : geometry.attributes.position.count ) / 3;\n\t\tconst identical =\n\t\t\tthis.matrixWorld.equals( mesh.matrixWorld ) &&\n\t\t\tthis.geometryHash === geometry.attributes.position.version &&\n\t\t\tcheckTypedArrayEquality( mesh.skeleton && mesh.skeleton.boneMatrices || null, this.boneMatrices ) &&\n\t\t\tthis.primitiveCount === primitiveCount;\n\n\t\treturn ! identical;\n\n\t}\n\n}\n\nexport class StaticGeometryGenerator {\n\n\tconstructor( meshes ) {\n\n\t\tif ( ! Array.isArray( meshes ) ) {\n\n\t\t\tmeshes = [ meshes ];\n\n\t\t}\n\n\t\tconst finalMeshes = [];\n\t\tmeshes.forEach( object => {\n\n\t\t\tobject.traverseVisible( c => {\n\n\t\t\t\tif ( c.isMesh ) {\n\n\t\t\t\t\tfinalMeshes.push( c );\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t} );\n\n\t\tthis.meshes = finalMeshes;\n\t\tthis.useGroups = true;\n\t\tthis.applyWorldTransforms = true;\n\t\tthis.attributes = [ 'position', 'normal', 'color', 'tangent', 'uv', 'uv2' ];\n\t\tthis._intermediateGeometry = new Array( finalMeshes.length ).fill().map( () => new BufferGeometry() );\n\t\tthis._diffMap = new WeakMap();\n\n\t}\n\n\tgetMaterials() {\n\n\t\tconst materials = [];\n\t\tthis.meshes.forEach( mesh => {\n\n\t\t\tif ( Array.isArray( mesh.material ) ) {\n\n\t\t\t\tmaterials.push( ...mesh.material );\n\n\t\t\t} else {\n\n\t\t\t\tmaterials.push( mesh.material );\n\n\t\t\t}\n\n\t\t} );\n\t\treturn materials;\n\n\t}\n\n\tgenerate( targetGeometry = new BufferGeometry() ) {\n\n\t\t// track which attributes have been updated and which to skip to avoid unnecessary attribute copies\n\t\tlet skipAttributes = [];\n\t\tconst { meshes, useGroups, _intermediateGeometry, _diffMap } = this;\n\t\tfor ( let i = 0, l = meshes.length; i < l; i ++ ) {\n\n\t\t\tconst mesh = meshes[ i ];\n\t\t\tconst geom = _intermediateGeometry[ i ];\n\t\t\tconst diff = _diffMap.get( mesh );\n\t\t\tif ( ! diff || diff.didChange( mesh ) ) {\n\n\t\t\t\tthis._convertToStaticGeometry( mesh, geom );\n\t\t\t\tskipAttributes.push( false );\n\n\t\t\t\tif ( ! diff ) {\n\n\t\t\t\t\t_diffMap.set( mesh, new GeometryDiff( mesh ) );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tdiff.update();\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tskipAttributes.push( true );\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( _intermediateGeometry.length === 0 ) {\n\n\t\t\t// if there are no geometries then just create a fake empty geometry to provide\n\t\t\ttargetGeometry.setIndex( null );\n\n\t\t\t// remove all geometry\n\t\t\tconst attrs = targetGeometry.attributes;\n\t\t\tfor ( const key in attrs ) {\n\n\t\t\t\ttargetGeometry.deleteAttribute( key );\n\n\t\t\t}\n\n\t\t\t// create dummy attributes\n\t\t\tfor ( const key in this.attributes ) {\n\n\t\t\t\ttargetGeometry.setAttribute( this.attributes[ key ], new BufferAttribute( new Float32Array( 0 ), 4, false ) );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tmergeBufferGeometries( _intermediateGeometry, { useGroups, skipAttributes }, targetGeometry );\n\n\t\t}\n\n\t\tfor ( const key in targetGeometry.attributes ) {\n\n\t\t\ttargetGeometry.attributes[ key ].needsUpdate = true;\n\n\t\t}\n\n\t\treturn targetGeometry;\n\n\t}\n\n\t_convertToStaticGeometry( mesh, targetGeometry = new BufferGeometry() ) {\n\n\t\tconst geometry = mesh.geometry;\n\t\tconst applyWorldTransforms = this.applyWorldTransforms;\n\t\tconst includeNormal = this.attributes.includes( 'normal' );\n\t\tconst includeTangent = this.attributes.includes( 'tangent' );\n\t\tconst attributes = geometry.attributes;\n\t\tconst targetAttributes = targetGeometry.attributes;\n\n\t\t// initialize the attributes if they don't exist\n\t\tif ( ! targetGeometry.index && geometry.index ) {\n\n\t\t\ttargetGeometry.index = geometry.index.clone();\n\n\t\t}\n\n\t\tif ( ! targetAttributes.position ) {\n\n\t\t\ttargetGeometry.setAttribute( 'position', createAttributeClone( attributes.position ) );\n\n\t\t}\n\n\t\tif ( includeNormal && ! targetAttributes.normal && attributes.normal ) {\n\n\t\t\ttargetGeometry.setAttribute( 'normal', createAttributeClone( attributes.normal ) );\n\n\t\t}\n\n\t\tif ( includeTangent && ! targetAttributes.tangent && attributes.tangent ) {\n\n\t\t\ttargetGeometry.setAttribute( 'tangent', createAttributeClone( attributes.tangent ) );\n\n\t\t}\n\n\t\t// ensure the attributes are consistent\n\t\tvalidateAttributes( geometry.index, targetGeometry.index );\n\t\tvalidateAttributes( attributes.position, targetAttributes.position );\n\n\t\tif ( includeNormal ) {\n\n\t\t\tvalidateAttributes( attributes.normal, targetAttributes.normal );\n\n\t\t}\n\n\t\tif ( includeTangent ) {\n\n\t\t\tvalidateAttributes( attributes.tangent, targetAttributes.tangent );\n\n\t\t}\n\n\t\t// generate transformed vertex attribute data\n\t\tconst position = attributes.position;\n\t\tconst normal = includeNormal ? attributes.normal : null;\n\t\tconst tangent = includeTangent ? attributes.tangent : null;\n\t\tconst morphPosition = geometry.morphAttributes.position;\n\t\tconst morphNormal = geometry.morphAttributes.normal;\n\t\tconst morphTangent = geometry.morphAttributes.tangent;\n\t\tconst morphTargetsRelative = geometry.morphTargetsRelative;\n\t\tconst morphInfluences = mesh.morphTargetInfluences;\n\t\tconst normalMatrix = new Matrix3();\n\t\tnormalMatrix.getNormalMatrix( mesh.matrixWorld );\n\n\t\t// copy the index\n\t\tif ( geometry.index ) {\n\n\t\t\ttargetGeometry.index.array.set( geometry.index.array );\n\n\t\t}\n\n\t\t// copy and apply other attributes\n\t\tfor ( let i = 0, l = attributes.position.count; i < l; i ++ ) {\n\n\t\t\t_positionVector.fromBufferAttribute( position, i );\n\t\t\tif ( normal ) {\n\n\t\t\t\t_normalVector.fromBufferAttribute( normal, i );\n\n\t\t\t}\n\n\t\t\tif ( tangent ) {\n\n\t\t\t\t_tangentVector4.fromBufferAttribute( tangent, i );\n\t\t\t\t_tangentVector.fromBufferAttribute( tangent, i );\n\n\t\t\t}\n\n\t\t\t// apply morph target transform\n\t\t\tif ( morphInfluences ) {\n\n\t\t\t\tif ( morphPosition ) {\n\n\t\t\t\t\tapplyMorphTarget( morphPosition, morphInfluences, morphTargetsRelative, i, _positionVector );\n\n\t\t\t\t}\n\n\t\t\t\tif ( morphNormal ) {\n\n\t\t\t\t\tapplyMorphTarget( morphNormal, morphInfluences, morphTargetsRelative, i, _normalVector );\n\n\t\t\t\t}\n\n\t\t\t\tif ( morphTangent ) {\n\n\t\t\t\t\tapplyMorphTarget( morphTangent, morphInfluences, morphTargetsRelative, i, _tangentVector );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// apply bone transform\n\t\t\tif ( mesh.isSkinnedMesh ) {\n\n\t\t\t\tmesh.applyBoneTransform( i, _positionVector );\n\t\t\t\tif ( normal ) {\n\n\t\t\t\t\tboneNormalTransform( mesh, i, _normalVector );\n\n\t\t\t\t}\n\n\t\t\t\tif ( tangent ) {\n\n\t\t\t\t\tboneNormalTransform( mesh, i, _tangentVector );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// update the vectors of the attributes\n\t\t\tif ( applyWorldTransforms ) {\n\n\t\t\t\t_positionVector.applyMatrix4( mesh.matrixWorld );\n\n\t\t\t}\n\n\t\t\ttargetAttributes.position.setXYZ( i, _positionVector.x, _positionVector.y, _positionVector.z );\n\n\t\t\tif ( normal ) {\n\n\t\t\t\tif ( applyWorldTransforms ) {\n\n\t\t\t\t\t_normalVector.applyNormalMatrix( normalMatrix );\n\n\t\t\t\t}\n\n\t\t\t\ttargetAttributes.normal.setXYZ( i, _normalVector.x, _normalVector.y, _normalVector.z );\n\n\t\t\t}\n\n\t\t\tif ( tangent ) {\n\n\t\t\t\tif ( applyWorldTransforms ) {\n\n\t\t\t\t\t_tangentVector.transformDirection( mesh.matrixWorld );\n\n\t\t\t\t}\n\n\t\t\t\ttargetAttributes.tangent.setXYZW( i, _tangentVector.x, _tangentVector.y, _tangentVector.z, _tangentVector4.w );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// copy other attributes over\n\t\tfor ( const i in this.attributes ) {\n\n\t\t\tconst key = this.attributes[ i ];\n\t\t\tif ( key === 'position' || key === 'tangent' || key === 'normal' || ! ( key in attributes ) ) {\n\n\t\t\t\tcontinue;\n\n\t\t\t}\n\n\t\t\tif ( ! targetAttributes[ key ] ) {\n\n\t\t\t\ttargetGeometry.setAttribute( key, createAttributeClone( attributes[ key ] ) );\n\n\t\t\t}\n\n\t\t\tvalidateAttributes( attributes[ key ], targetAttributes[ key ] );\n\t\t\tcopyAttributeContents( attributes[ key ], targetAttributes[ key ] );\n\n\t\t}\n\n\t\tif ( mesh.matrixWorld.determinant() < 0 ) {\n\n\t\t\tinvertGeometry( targetGeometry );\n\n\t\t}\n\n\t\treturn targetGeometry;\n\n\t}\n\n}\n", "export const common_functions = /* glsl */`\n\n// A stack of uint32 indices can can store the indices for\n// a perfectly balanced tree with a depth up to 31. Lower stack\n// depth gets higher performance.\n//\n// However not all trees are balanced. Best value to set this to\n// is the trees max depth.\n#ifndef BVH_STACK_DEPTH\n#define BVH_STACK_DEPTH 60\n#endif\n\n#ifndef INFINITY\n#define INFINITY 1e20\n#endif\n\n// Utilities\nuvec4 uTexelFetch1D( usampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nivec4 iTexelFetch1D( isampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nvec4 texelFetch1D( sampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nvec4 textureSampleBarycoord( sampler2D tex, vec3 barycoord, uvec3 faceIndices ) {\n\n\treturn\n\t\tbarycoord.x * texelFetch1D( tex, faceIndices.x ) +\n\t\tbarycoord.y * texelFetch1D( tex, faceIndices.y ) +\n\t\tbarycoord.z * texelFetch1D( tex, faceIndices.z );\n\n}\n\nvoid ndcToCameraRay(\n\tvec2 coord, mat4 cameraWorld, mat4 invProjectionMatrix,\n\tout vec3 rayOrigin, out vec3 rayDirection\n) {\n\n\t// get camera look direction and near plane for camera clipping\n\tvec4 lookDirection = cameraWorld * vec4( 0.0, 0.0, - 1.0, 0.0 );\n\tvec4 nearVector = invProjectionMatrix * vec4( 0.0, 0.0, - 1.0, 1.0 );\n\tfloat near = abs( nearVector.z / nearVector.w );\n\n\t// get the camera direction and position from camera matrices\n\tvec4 origin = cameraWorld * vec4( 0.0, 0.0, 0.0, 1.0 );\n\tvec4 direction = invProjectionMatrix * vec4( coord, 0.5, 1.0 );\n\tdirection /= direction.w;\n\tdirection = cameraWorld * direction - origin;\n\n\t// slide the origin along the ray until it sits at the near clip plane position\n\torigin.xyz += direction.xyz * near / dot( direction, lookDirection );\n\n\trayOrigin = origin.xyz;\n\trayDirection = direction.xyz;\n\n}\n`;\n", "// Distance to Point\nexport const bvh_distance_functions = /* glsl */`\n\nfloat dot2( vec3 v ) {\n\n\treturn dot( v, v );\n\n}\n\n// https://www.shadertoy.com/view/ttfGWl\nvec3 closestPointToTriangle( vec3 p, vec3 v0, vec3 v1, vec3 v2, out vec3 barycoord ) {\n\n    vec3 v10 = v1 - v0;\n    vec3 v21 = v2 - v1;\n    vec3 v02 = v0 - v2;\n\n\tvec3 p0 = p - v0;\n\tvec3 p1 = p - v1;\n\tvec3 p2 = p - v2;\n\n    vec3 nor = cross( v10, v02 );\n\n    // method 2, in barycentric space\n    vec3  q = cross( nor, p0 );\n    float d = 1.0 / dot2( nor );\n    float u = d * dot( q, v02 );\n    float v = d * dot( q, v10 );\n    float w = 1.0 - u - v;\n\n\tif( u < 0.0 ) {\n\n\t\tw = clamp( dot( p2, v02 ) / dot2( v02 ), 0.0, 1.0 );\n\t\tu = 0.0;\n\t\tv = 1.0 - w;\n\n\t} else if( v < 0.0 ) {\n\n\t\tu = clamp( dot( p0, v10 ) / dot2( v10 ), 0.0, 1.0 );\n\t\tv = 0.0;\n\t\tw = 1.0 - u;\n\n\t} else if( w < 0.0 ) {\n\n\t\tv = clamp( dot( p1, v21 ) / dot2( v21 ), 0.0, 1.0 );\n\t\tw = 0.0;\n\t\tu = 1.0-v;\n\n\t}\n\n\tbarycoord = vec3( u, v, w );\n    return u * v1 + v * v2 + w * v0;\n\n}\n\nfloat distanceToTriangles(\n\t// geometry info and triangle range\n\tsampler2D positionAttr, usampler2D indexAttr, uint offset, uint count,\n\n\t// point and cut off range\n\tvec3 point, float closestDistanceSquared,\n\n\t// outputs\n\tinout uvec4 faceIndices, inout vec3 faceNormal, inout vec3 barycoord, inout float side, inout vec3 outPoint\n) {\n\n\tbool found = false;\n\tvec3 localBarycoord;\n\tfor ( uint i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\tuvec3 indices = uTexelFetch1D( indexAttr, i ).xyz;\n\t\tvec3 a = texelFetch1D( positionAttr, indices.x ).rgb;\n\t\tvec3 b = texelFetch1D( positionAttr, indices.y ).rgb;\n\t\tvec3 c = texelFetch1D( positionAttr, indices.z ).rgb;\n\n\t\t// get the closest point and barycoord\n\t\tvec3 closestPoint = closestPointToTriangle( point, a, b, c, localBarycoord );\n\t\tvec3 delta = point - closestPoint;\n\t\tfloat sqDist = dot2( delta );\n\t\tif ( sqDist < closestDistanceSquared ) {\n\n\t\t\t// set the output results\n\t\t\tclosestDistanceSquared = sqDist;\n\t\t\tfaceIndices = uvec4( indices.xyz, i );\n\t\t\tfaceNormal = normalize( cross( a - b, b - c ) );\n\t\t\tbarycoord = localBarycoord;\n\t\t\toutPoint = closestPoint;\n\t\t\tside = sign( dot( faceNormal, delta ) );\n\n\t\t}\n\n\t}\n\n\treturn closestDistanceSquared;\n\n}\n\nfloat distanceSqToBounds( vec3 point, vec3 boundsMin, vec3 boundsMax ) {\n\n\tvec3 clampedPoint = clamp( point, boundsMin, boundsMax );\n\tvec3 delta = point - clampedPoint;\n\treturn dot( delta, delta );\n\n}\n\nfloat distanceSqToBVHNodeBoundsPoint( vec3 point, sampler2D bvhBounds, uint currNodeIndex ) {\n\n\tuint cni2 = currNodeIndex * 2u;\n\tvec3 boundsMin = texelFetch1D( bvhBounds, cni2 ).xyz;\n\tvec3 boundsMax = texelFetch1D( bvhBounds, cni2 + 1u ).xyz;\n\treturn distanceSqToBounds( point, boundsMin, boundsMax );\n\n}\n\n// use a macro to hide the fact that we need to expand the struct into separate fields\n#define\\\n\tbvhClosestPointToPoint(\\\n\t\tbvh,\\\n\t\tpoint, faceIndices, faceNormal, barycoord, side, outPoint\\\n\t)\\\n\t_bvhClosestPointToPoint(\\\n\t\tbvh.position, bvh.index, bvh.bvhBounds, bvh.bvhContents,\\\n\t\tpoint, faceIndices, faceNormal, barycoord, side, outPoint\\\n\t)\n\nfloat _bvhClosestPointToPoint(\n\t// bvh info\n\tsampler2D bvh_position, usampler2D bvh_index, sampler2D bvh_bvhBounds, usampler2D bvh_bvhContents,\n\n\t// point to check\n\tvec3 point,\n\n\t// output variables\n\tinout uvec4 faceIndices, inout vec3 faceNormal, inout vec3 barycoord,\n\tinout float side, inout vec3 outPoint\n ) {\n\n\t// stack needs to be twice as long as the deepest tree we expect because\n\t// we push both the left and right child onto the stack every traversal\n\tint ptr = 0;\n\tuint stack[ BVH_STACK_DEPTH ];\n\tstack[ 0 ] = 0u;\n\n\tfloat closestDistanceSquared = pow( 100000.0, 2.0 );\n\tbool found = false;\n\twhile ( ptr > - 1 && ptr < BVH_STACK_DEPTH ) {\n\n\t\tuint currNodeIndex = stack[ ptr ];\n\t\tptr --;\n\n\t\t// check if we intersect the current bounds\n\t\tfloat boundsHitDistance = distanceSqToBVHNodeBoundsPoint( point, bvh_bvhBounds, currNodeIndex );\n\t\tif ( boundsHitDistance > closestDistanceSquared ) {\n\n\t\t\tcontinue;\n\n\t\t}\n\n\t\tuvec2 boundsInfo = uTexelFetch1D( bvh_bvhContents, currNodeIndex ).xy;\n\t\tbool isLeaf = bool( boundsInfo.x & 0xffff0000u );\n\t\tif ( isLeaf ) {\n\n\t\t\tuint count = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint offset = boundsInfo.y;\n\t\t\tclosestDistanceSquared = distanceToTriangles(\n\t\t\t\tbvh_position, bvh_index, offset, count, point, closestDistanceSquared,\n\n\t\t\t\t// outputs\n\t\t\t\tfaceIndices, faceNormal, barycoord, side, outPoint\n\t\t\t);\n\n\t\t} else {\n\n\t\t\tuint leftIndex = currNodeIndex + 1u;\n\t\t\tuint splitAxis = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint rightIndex = boundsInfo.y;\n\t\t\tbool leftToRight = distanceSqToBVHNodeBoundsPoint( point, bvh_bvhBounds, leftIndex ) < distanceSqToBVHNodeBoundsPoint( point, bvh_bvhBounds, rightIndex );//rayDirection[ splitAxis ] >= 0.0;\n\t\t\tuint c1 = leftToRight ? leftIndex : rightIndex;\n\t\t\tuint c2 = leftToRight ? rightIndex : leftIndex;\n\n\t\t\t// set c2 in the stack so we traverse it later. We need to keep track of a pointer in\n\t\t\t// the stack while we traverse. The second pointer added is the one that will be\n\t\t\t// traversed first\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c2;\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c1;\n\n\t\t}\n\n\t}\n\n\treturn sqrt( closestDistanceSquared );\n\n}\n`;\n", "export const bvh_ray_functions = /* glsl */`\n\n#ifndef TRI_INTERSECT_EPSILON\n#define TRI_INTERSECT_EPSILON 1e-5\n#endif\n\n// Raycasting\nbool intersectsBounds( vec3 rayOrigin, vec3 rayDirection, vec3 boundsMin, vec3 boundsMax, out float dist ) {\n\n\t// https://www.reddit.com/r/opengl/comments/8ntzz5/fast_glsl_ray_box_intersection/\n\t// https://tavianator.com/2011/ray_box.html\n\tvec3 invDir = 1.0 / rayDirection;\n\n\t// find intersection distances for each plane\n\tvec3 tMinPlane = invDir * ( boundsMin - rayOrigin );\n\tvec3 tMaxPlane = invDir * ( boundsMax - rayOrigin );\n\n\t// get the min and max distances from each intersection\n\tvec3 tMinHit = min( tMaxPlane, tMinPlane );\n\tvec3 tMaxHit = max( tMaxPlane, tMinPlane );\n\n\t// get the furthest hit distance\n\tvec2 t = max( tMinHit.xx, tMinHit.yz );\n\tfloat t0 = max( t.x, t.y );\n\n\t// get the minimum hit distance\n\tt = min( tMaxHit.xx, tMaxHit.yz );\n\tfloat t1 = min( t.x, t.y );\n\n\t// set distance to 0.0 if the ray starts inside the box\n\tdist = max( t0, 0.0 );\n\n\treturn t1 >= dist;\n\n}\n\nbool intersectsTriangle(\n\tvec3 rayOrigin, vec3 rayDirection, vec3 a, vec3 b, vec3 c,\n\tout vec3 barycoord, out vec3 norm, out float dist, out float side\n) {\n\n\t// https://stackoverflow.com/questions/42740765/intersection-between-line-and-triangle-in-3d\n\tvec3 edge1 = b - a;\n\tvec3 edge2 = c - a;\n\tnorm = cross( edge1, edge2 );\n\n\tfloat det = - dot( rayDirection, norm );\n\tfloat invdet = 1.0 / det;\n\n\tvec3 AO = rayOrigin - a;\n\tvec3 DAO = cross( AO, rayDirection );\n\n\tvec4 uvt;\n\tuvt.x = dot( edge2, DAO ) * invdet;\n\tuvt.y = - dot( edge1, DAO ) * invdet;\n\tuvt.z = dot( AO, norm ) * invdet;\n\tuvt.w = 1.0 - uvt.x - uvt.y;\n\n\t// set the hit information\n\tbarycoord = uvt.wxy; // arranged in A, B, C order\n\tdist = uvt.z;\n\tside = sign( det );\n\tnorm = side * normalize( norm );\n\n\t// add an epsilon to avoid misses between triangles\n\tuvt += vec4( TRI_INTERSECT_EPSILON );\n\n\treturn all( greaterThanEqual( uvt, vec4( 0.0 ) ) );\n\n}\n\nbool intersectTriangles(\n\t// geometry info and triangle range\n\tsampler2D positionAttr, usampler2D indexAttr, uint offset, uint count,\n\n\t// ray\n\tvec3 rayOrigin, vec3 rayDirection,\n\n\t// outputs\n\tinout float minDistance, inout uvec4 faceIndices, inout vec3 faceNormal, inout vec3 barycoord,\n\tinout float side, inout float dist\n) {\n\n\tbool found = false;\n\tvec3 localBarycoord, localNormal;\n\tfloat localDist, localSide;\n\tfor ( uint i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\tuvec3 indices = uTexelFetch1D( indexAttr, i ).xyz;\n\t\tvec3 a = texelFetch1D( positionAttr, indices.x ).rgb;\n\t\tvec3 b = texelFetch1D( positionAttr, indices.y ).rgb;\n\t\tvec3 c = texelFetch1D( positionAttr, indices.z ).rgb;\n\n\t\tif (\n\t\t\tintersectsTriangle( rayOrigin, rayDirection, a, b, c, localBarycoord, localNormal, localDist, localSide )\n\t\t\t&& localDist < minDistance\n\t\t) {\n\n\t\t\tfound = true;\n\t\t\tminDistance = localDist;\n\n\t\t\tfaceIndices = uvec4( indices.xyz, i );\n\t\t\tfaceNormal = localNormal;\n\n\t\t\tside = localSide;\n\t\t\tbarycoord = localBarycoord;\n\t\t\tdist = localDist;\n\n\t\t}\n\n\t}\n\n\treturn found;\n\n}\n\nbool intersectsBVHNodeBounds( vec3 rayOrigin, vec3 rayDirection, sampler2D bvhBounds, uint currNodeIndex, out float dist ) {\n\n\tuint cni2 = currNodeIndex * 2u;\n\tvec3 boundsMin = texelFetch1D( bvhBounds, cni2 ).xyz;\n\tvec3 boundsMax = texelFetch1D( bvhBounds, cni2 + 1u ).xyz;\n\treturn intersectsBounds( rayOrigin, rayDirection, boundsMin, boundsMax, dist );\n\n}\n\n// use a macro to hide the fact that we need to expand the struct into separate fields\n#define\\\n\tbvhIntersectFirstHit(\\\n\t\tbvh,\\\n\t\trayOrigin, rayDirection, faceIndices, faceNormal, barycoord, side, dist\\\n\t)\\\n\t_bvhIntersectFirstHit(\\\n\t\tbvh.position, bvh.index, bvh.bvhBounds, bvh.bvhContents,\\\n\t\trayOrigin, rayDirection, faceIndices, faceNormal, barycoord, side, dist\\\n\t)\n\nbool _bvhIntersectFirstHit(\n\t// bvh info\n\tsampler2D bvh_position, usampler2D bvh_index, sampler2D bvh_bvhBounds, usampler2D bvh_bvhContents,\n\n\t// ray\n\tvec3 rayOrigin, vec3 rayDirection,\n\n\t// output variables split into separate variables due to output precision\n\tinout uvec4 faceIndices, inout vec3 faceNormal, inout vec3 barycoord,\n\tinout float side, inout float dist\n) {\n\n\t// stack needs to be twice as long as the deepest tree we expect because\n\t// we push both the left and right child onto the stack every traversal\n\tint ptr = 0;\n\tuint stack[ BVH_STACK_DEPTH ];\n\tstack[ 0 ] = 0u;\n\n\tfloat triangleDistance = INFINITY;\n\tbool found = false;\n\twhile ( ptr > - 1 && ptr < BVH_STACK_DEPTH ) {\n\n\t\tuint currNodeIndex = stack[ ptr ];\n\t\tptr --;\n\n\t\t// check if we intersect the current bounds\n\t\tfloat boundsHitDistance;\n\t\tif (\n\t\t\t! intersectsBVHNodeBounds( rayOrigin, rayDirection, bvh_bvhBounds, currNodeIndex, boundsHitDistance )\n\t\t\t|| boundsHitDistance > triangleDistance\n\t\t) {\n\n\t\t\tcontinue;\n\n\t\t}\n\n\t\tuvec2 boundsInfo = uTexelFetch1D( bvh_bvhContents, currNodeIndex ).xy;\n\t\tbool isLeaf = bool( boundsInfo.x & 0xffff0000u );\n\n\t\tif ( isLeaf ) {\n\n\t\t\tuint count = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint offset = boundsInfo.y;\n\n\t\t\tfound = intersectTriangles(\n\t\t\t\tbvh_position, bvh_index, offset, count,\n\t\t\t\trayOrigin, rayDirection, triangleDistance,\n\t\t\t\tfaceIndices, faceNormal, barycoord, side, dist\n\t\t\t) || found;\n\n\t\t} else {\n\n\t\t\tuint leftIndex = currNodeIndex + 1u;\n\t\t\tuint splitAxis = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint rightIndex = boundsInfo.y;\n\n\t\t\tbool leftToRight = rayDirection[ splitAxis ] >= 0.0;\n\t\t\tuint c1 = leftToRight ? leftIndex : rightIndex;\n\t\t\tuint c2 = leftToRight ? rightIndex : leftIndex;\n\n\t\t\t// set c2 in the stack so we traverse it later. We need to keep track of a pointer in\n\t\t\t// the stack while we traverse. The second pointer added is the one that will be\n\t\t\t// traversed first\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c2;\n\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c1;\n\n\t\t}\n\n\t}\n\n\treturn found;\n\n}\n`;\n", "// Note that a struct cannot be used for the hit record including faceIndices, faceNormal, barycoord,\n// side, and dist because on some mobile GPUS (such as Adreno) numbers are afforded less precision specifically\n// when in a struct leading to inaccurate hit results. See KhronosGroup/WebGL#3351 for more details.\nexport const bvh_struct_definitions = /* glsl */`\nstruct BVH {\n\n\tusampler2D index;\n\tsampler2D position;\n\n\tsampler2D bvhBounds;\n\tusampler2D bvhContents;\n\n};\n`;\n", "export { MeshBVH } from './core/MeshBVH.js';\nexport { MeshBVHHelper } from './objects/MeshBVHHelper.js';\nexport { CENTER, AVERAGE, SAH, NOT_INTERSECTED, INTERSECTED, CONTAINED } from './core/Constants.js';\nexport { getBVHExtremes, estimateMemoryInBytes, getJSONStructure, validateBounds } from './debug/Debug.js';\nexport * from './utils/ExtensionUtilities.js';\nexport { getTriangleHitPointInfo } from './utils/TriangleUtilities.js';\nexport * from './math/ExtendedTriangle.js';\nexport * from './math/OrientedBox.js';\nexport * from './gpu/MeshBVHUniformStruct.js';\nexport * from './gpu/VertexAttributeTexture.js';\nexport * from './utils/StaticGeometryGenerator.js';\nexport * as BVHShaderGLSL from './gpu/BVHShaderGLSL.js';\n\n// backwards compatibility\nimport * as BVHShaderGLSL from './gpu/BVHShaderGLSL.js';\nexport const shaderStructs = BVHShaderGLSL.bvh_struct_definitions;\nexport const shaderDistanceFunction = BVHShaderGLSL.bvh_distance_functions;\nexport const shaderIntersectFunction = `\n\t${ BVHShaderGLSL.common_functions }\n\t${ BVHShaderGLSL.bvh_ray_functions }\n`;\n"], "names": ["BufferAttribute", "Vector3", "Vector2", "Plane", "Line3", "Triangle", "Sphere", "Matrix4", "_box1", "_box2", "Box3", "temp1", "BackSide", "DoubleSide", "_raycast", "_xyzFields", "_raycastFirst", "boundingBox", "triangle", "triangle2", "invertedMat", "obb", "obb2", "_intersectsGeometry", "tempMatrix", "temp2", "temp3", "temp4", "FrontSide", "Object3D", "<PERSON><PERSON>", "BufferGeometry", "Group", "LineBasicMaterial", "MeshBasicMaterial", "THREE", "REVISION", "<PERSON>", "RedFormat", "RGFormat", "RGBAFormat", "RedIntegerFormat", "RGIntegerFormat", "RGBAIntegerFormat", "DataTexture", "NearestFilter", "FloatType", "UnsignedIntType", "IntType", "UnsignedByteType", "ByteType", "ShortType", "UnsignedShortType", "Vector4", "Matrix3", "BVHShaderGLSL.bvh_struct_definitions", "BVHShaderGLSL.bvh_distance_functions", "BVHShaderGLSL.common_functions", "BVHShaderGLSL.bvh_ray_functions"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;CAAA;AACY,OAAC,MAAM,GAAG,EAAE;AACZ,OAAC,OAAO,GAAG,EAAE;AACb,OAAC,GAAG,GAAG,EAAE;AACrB;CACA;AACY,OAAC,eAAe,GAAG,EAAE;AACrB,OAAC,WAAW,GAAG,EAAE;AACjB,OAAC,SAAS,GAAG,EAAE;AAC3B;CACA;CACA;CACA;CACA;CACO,MAAM,uBAAuB,GAAG,IAAI,CAAC;CACrC,MAAM,cAAc,GAAG,CAAC,CAAC;AAChC;AACA;CACA;CACO,MAAM,cAAc,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CACrC,MAAM,gBAAgB,GAAG,MAAM,CAAC;AACvC;CACA;CACA;CACO,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;AACnD;CACO,MAAM,eAAe,GAAG,MAAM,EAAE,iBAAiB,EAAE;;CCxBnD,SAAS,cAAc,EAAE,GAAG,GAAG;AACtC;CACA,CAAC,OAAO,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;AACpE;CACA,CAAC;AACD;CACO,SAAS,WAAW,EAAE,GAAG,GAAG;AACnC;CACA,CAAC,OAAO,cAAc,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAClC;CACA,CAAC;AACD;CACO,SAAS,aAAa,EAAE,WAAW,EAAE,iBAAiB,GAAG,WAAW,GAAG;AAC9E;CACA,CAAC,KAAK,WAAW,GAAG,KAAK,GAAG;AAC5B;CACA,EAAE,OAAO,IAAI,WAAW,EAAE,IAAI,iBAAiB,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC;AACrE;CACA,EAAE,MAAM;AACR;CACA,EAAE,OAAO,IAAI,WAAW,EAAE,IAAI,iBAAiB,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC;AACrE;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA;CACO,SAAS,WAAW,EAAE,GAAG,EAAE,OAAO,GAAG;AAC5C;CACA,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG;AACpB;CACA,EAAE,MAAM,WAAW,GAAG,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;CACpD,EAAE,MAAM,iBAAiB,GAAG,OAAO,CAAC,oBAAoB,GAAG,iBAAiB,GAAG,WAAW,CAAC;CAC3F,EAAE,MAAM,KAAK,GAAG,aAAa,EAAE,WAAW,EAAE,iBAAiB,EAAE,CAAC;CAChE,EAAE,GAAG,CAAC,QAAQ,EAAE,IAAIA,qBAAe,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;AAClD;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,GAAG;AAC3C;CACA,GAAG,KAAK,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AAClB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAAS,oBAAoB,EAAE,GAAG,EAAE,KAAK,GAAG;AACnD;CACA,CAAC,MAAM,QAAQ,GAAG,WAAW,EAAE,GAAG,EAAE,CAAC;CACrC,CAAC,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC;CACjD,CAAC,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;CACnC,CAAC,MAAM,GAAG,GAAG,EAAE,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,KAAK,CAAC,CAAC;AACvD;CACA,CAAC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC;CACrC,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC;CAClD,CAAC,OAAO,EAAE;CACV,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE;CAC9B,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE;CAC5B,EAAE,EAAE,CAAC;AACL;CACA,CAAC;AACD;CACO,SAAS,kBAAkB,EAAE,GAAG,EAAE,KAAK,GAAG;AACjD;CACA,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG;AAC5C;CACA,EAAE,OAAO,oBAAoB,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;AAC5C;CACA,EAAE;AACF;CACA,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC;CACnB,CAAC,MAAM,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;AACnC;CACA,CAAC,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC;CACjD,CAAC,MAAM,cAAc,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;CAC5C,CAAC,MAAM,YAAY,GAAG,EAAE,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,KAAK,CAAC,CAAC;CAChE,CAAC,MAAM,MAAM,KAAK,IAAI,GAAG,CAAC,MAAM,GAAG;AACnC;CACA,EAAE,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;CACrC,EAAE,MAAM,QAAQ,GAAG,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC;CACrD,EAAE,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,cAAc,EAAE,UAAU,EAAE,EAAE,CAAC;CAChE,EAAE,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,YAAY,EAAE,QAAQ,EAAE,EAAE,CAAC;AAC5D;CACA,EAAE;AACF;AACA;CACA;CACA,CAAC,MAAM,gBAAgB,GAAG,KAAK,CAAC,IAAI,EAAE,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;CAC3F,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC1D;CACA,EAAE,MAAM,KAAK,GAAG,gBAAgB,EAAE,CAAC,EAAE,CAAC;CACtC,EAAE,MAAM,GAAG,GAAG,gBAAgB,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AACxC;CACA,EAAE,MAAM,CAAC,IAAI,EAAE;CACf,GAAG,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE;CAC9B,GAAG,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,EAAE;CACnC,GAAG,EAAE,CAAC;AACN;CACA,EAAE;AACF;CACA,CAAC,OAAO,MAAM,CAAC;AACf;CACA,CAAC;AACD;CACO,SAAS,YAAY,EAAE,QAAQ,EAAE,KAAK,GAAG;AAChD;CACA,CAAC,MAAM,WAAW,GAAG,WAAW,EAAE,QAAQ,EAAE,CAAC;CAC7C,CAAC,MAAM,MAAM,GAAG,kBAAkB,EAAE,QAAQ,EAAE,KAAK,EAAE;CACrD,GAAG,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;AAC3C;CACA,CAAC,MAAM,UAAU,GAAG,MAAM,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;CAChD,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,WAAW,GAAG,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,KAAK,EAAE,CAAC;AAClF;CACA,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC;CACf,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,KAAK,IAAI,KAAK,EAAE,CAAC;CACnD,CAAC,OAAO,WAAW,KAAK,KAAK,CAAC;AAC9B;CACA;;CC/HA;CACA;CACA;CACO,SAAS,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,cAAc,GAAG;AACnF;CACA,CAAC,IAAI,IAAI,GAAG,QAAQ,CAAC;CACrB,CAAC,IAAI,IAAI,GAAG,QAAQ,CAAC;CACrB,CAAC,IAAI,IAAI,GAAG,QAAQ,CAAC;CACrB,CAAC,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;CACvB,CAAC,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;CACvB,CAAC,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;AACvB;CACA,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC;CACtB,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC;CACtB,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC;CACtB,CAAC,IAAI,KAAK,GAAG,EAAE,QAAQ,CAAC;CACxB,CAAC,IAAI,KAAK,GAAG,EAAE,QAAQ,CAAC;CACxB,CAAC,IAAI,KAAK,GAAG,EAAE,QAAQ,CAAC;AACxB;CACA,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,GAAG,GAAG,EAAE,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG;AAC3E;CACA,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACrC,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACrC,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACrB,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACrB,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;CAC7B,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;CAC7B,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;CAC/B,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAC/B;CACA,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACrC,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACrC,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACrB,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACrB,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;CAC7B,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;CAC7B,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;CAC/B,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAC/B;CACA,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACrC,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACrC,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACrB,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACrB,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;CAC7B,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;CAC7B,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;CAC/B,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAC/B;CACA,EAAE;AACF;CACA,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;CACpB,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;CACpB,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AACpB;CACA,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;CACpB,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;CACpB,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AACpB;CACA,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;CAC7B,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;CAC7B,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC7B;CACA,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;CAC7B,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;CAC7B,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC7B;CACA,CAAC;AACD;CACA;CACA;CACA;CACA;CACO,SAAS,qBAAqB,EAAE,GAAG,EAAE,MAAM,GAAG,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAG;AACzF;CACA,CAAC,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC;CACzC,CAAC,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;CAClD,CAAC,MAAM,QAAQ,GAAG,WAAW,EAAE,GAAG,EAAE,CAAC;CACrC,CAAC,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;CACvC,CAAC,IAAI,cAAc,CAAC;CACpB,CAAC,KAAK,MAAM,KAAK,IAAI,GAAG;AACxB;CACA,EAAE,cAAc,GAAG,IAAI,YAAY,EAAE,QAAQ,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACxD,EAAE,MAAM,GAAG,CAAC,CAAC;CACb,EAAE,KAAK,GAAG,QAAQ,CAAC;AACnB;CACA,EAAE,MAAM;AACR;CACA,EAAE,cAAc,GAAG,MAAM,CAAC;CAC1B,EAAE,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC;CACvB,EAAE,KAAK,GAAG,KAAK,IAAI,QAAQ,CAAC;AAC5B;CACA,EAAE;AACF;CACA;CACA,CAAC,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;AAC9B;CACA;CACA,CAAC,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;CAC1C,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;CAChB,CAAC,KAAK,OAAO,CAAC,4BAA4B,GAAG;AAC7C;CACA,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/B;CACA,EAAE;AACF;CACA;CACA,CAAC,MAAM,OAAO,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC5C;CACA,CAAC,MAAM,IAAI,GAAG,GAAG,MAAM,EAAE,GAAG,GAAG,MAAM,GAAG,KAAK,EAAE,GAAG,GAAG,GAAG;AACxD;CACA,EAAE,MAAM,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;CACvB,EAAE,MAAM,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;AACvB;CACA,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,CAAC,CAAC;CACpB,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,CAAC,CAAC;CACpB,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,CAAC,CAAC;AACpB;CACA,EAAE,KAAK,KAAK,GAAG;AACf;CACA,GAAG,EAAE,GAAG,KAAK,EAAE,EAAE,EAAE,CAAC;CACpB,GAAG,EAAE,GAAG,KAAK,EAAE,EAAE,EAAE,CAAC;CACpB,GAAG,EAAE,GAAG,KAAK,EAAE,EAAE,EAAE,CAAC;AACpB;CACA,GAAG;AACH;CACA;CACA;CACA,EAAE,KAAK,EAAE,UAAU,GAAG;AACtB;CACA,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,GAAG,YAAY,CAAC;CACnC,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,GAAG,YAAY,CAAC;CACnC,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,GAAG,YAAY,CAAC;AACnC;CACA,GAAG;AACH;CACA,EAAE,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG;AACpC;CACA,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACf;CACA,GAAG,KAAK,UAAU,GAAG;AACrB;CACA,IAAI,CAAC,GAAG,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;CACvC,IAAI,CAAC,GAAG,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;CACvC,IAAI,CAAC,GAAG,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACvC;CACA,IAAI,MAAM;AACV;CACA,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;CAC1B,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;CAC1B,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC1B;CACA,IAAI;AACJ;CACA,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;CACf,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;CAC1B,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAC1B;CACA,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;CACf,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;CAC1B,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAC1B;CACA;CACA;CACA;CACA,GAAG,MAAM,WAAW,GAAG,EAAE,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;CACzC,GAAG,MAAM,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;CACtB,GAAG,cAAc,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,WAAW,CAAC;CACxD,GAAG,cAAc,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,WAAW,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,WAAW,KAAK,eAAe,CAAC;AACxG;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,cAAc,CAAC;AACvB;CACA;;CClLO,SAAS,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,GAAG;AACzD;CACA,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,EAAE,CAAC;CACrC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;CACzC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AACzC;CACA,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;CACzC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;CACzC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AACzC;CACA,CAAC,OAAO,MAAM,CAAC;AACf;CACA,CAAC;AACD;CACO,SAAS,eAAe,EAAE,MAAM,GAAG;AAC1C;CACA,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC;CACpD,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC;AACtD;CACA,CAAC;AACD;CACO,SAAS,mBAAmB,EAAE,MAAM,GAAG;AAC9C;CACA,CAAC,IAAI,WAAW,GAAG,EAAE,CAAC,CAAC;CACvB,CAAC,IAAI,SAAS,GAAG,EAAE,QAAQ,CAAC;AAC5B;CACA,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChC;CACA,EAAE,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CAC7C,EAAE,KAAK,IAAI,GAAG,SAAS,GAAG;AAC1B;CACA,GAAG,SAAS,GAAG,IAAI,CAAC;CACpB,GAAG,WAAW,GAAG,CAAC,CAAC;AACnB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,WAAW,CAAC;AACpB;CACA,CAAC;AACD;CACA;CACO,SAAS,UAAU,EAAE,MAAM,EAAE,MAAM,GAAG;AAC7C;CACA,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC;AACtB;CACA,CAAC;AACD;CACA;CACO,SAAS,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG;AAC5C;CACA,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC;CAChB,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChC;CACA,EAAE,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AACnB;CACA;CACA,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;CAChB,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;CAChB,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAC1C;CACA;CACA,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;CACjB,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;CACjB,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAC3C;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA;CACO,SAAS,sBAAsB,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,GAAG;AAC7E;CACA,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChC;CACA,EAAE,MAAM,OAAO,GAAG,cAAc,EAAE,UAAU,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACvD,EAAE,MAAM,KAAK,GAAG,cAAc,EAAE,UAAU,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACzD;CACA,EAAE,MAAM,IAAI,GAAG,OAAO,GAAG,KAAK,CAAC;CAC/B,EAAE,MAAM,IAAI,GAAG,OAAO,GAAG,KAAK,CAAC;AAC/B;CACA,EAAE,KAAK,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG;AAC5B;CACA,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AACtB;CACA,GAAG;AACH;CACA,EAAE,KAAK,IAAI,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG;AAChC;CACA,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC1B;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA;CACO,SAAS,kBAAkB,EAAE,MAAM,GAAG;AAC7C;CACA,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CACtC,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CACtC,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;AACtC;CACA,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC5C;CACA;;CCxGA,MAAM,SAAS,GAAG,EAAE,CAAC;CACrB,MAAM,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;CACvD,MAAM,OAAO,GAAG,IAAI,KAAK,EAAE,SAAS,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM;AACzD;CACA,CAAC,OAAO;AACR;CACA,EAAE,KAAK,EAAE,CAAC;CACV,EAAE,MAAM,EAAE,IAAI,YAAY,EAAE,CAAC,EAAE;CAC/B,EAAE,gBAAgB,EAAE,IAAI,YAAY,EAAE,CAAC,EAAE;CACzC,EAAE,eAAe,EAAE,IAAI,YAAY,EAAE,CAAC,EAAE;CACxC,EAAE,SAAS,EAAE,CAAC;AACd;CACA,EAAE,CAAC;AACH;CACA,CAAC,EAAE,CAAC;CACJ,MAAM,UAAU,GAAG,IAAI,YAAY,EAAE,CAAC,EAAE,CAAC;AACzC;CACO,SAAS,eAAe,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,GAAG;AACnH;CACA,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;CAChB,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;AACb;CACA;CACA,CAAC,KAAK,QAAQ,KAAK,MAAM,GAAG;AAC5B;CACA,EAAE,IAAI,GAAG,mBAAmB,EAAE,oBAAoB,EAAE,CAAC;CACrD,EAAE,KAAK,IAAI,KAAK,EAAE,CAAC,GAAG;AACtB;CACA,GAAG,GAAG,GAAG,EAAE,oBAAoB,EAAE,IAAI,EAAE,GAAG,oBAAoB,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;AACjF;CACA,GAAG;AACH;CACA,EAAE,MAAM,KAAK,QAAQ,KAAK,OAAO,GAAG;AACpC;CACA,EAAE,IAAI,GAAG,mBAAmB,EAAE,gBAAgB,EAAE,CAAC;CACjD,EAAE,KAAK,IAAI,KAAK,EAAE,CAAC,GAAG;AACtB;CACA,GAAG,GAAG,GAAG,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;AAC3D;CACA,GAAG;AACH;CACA,EAAE,MAAM,KAAK,QAAQ,KAAK,GAAG,GAAG;AAChC;CACA,EAAE,MAAM,eAAe,GAAG,kBAAkB,EAAE,gBAAgB,EAAE,CAAC;CACjE,EAAE,IAAI,QAAQ,GAAG,uBAAuB,GAAG,KAAK,CAAC;AACjD;CACA;CACA,EAAE,MAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC;CAC5B,EAAE,MAAM,IAAI,GAAG,EAAE,MAAM,GAAG,KAAK,KAAK,CAAC,CAAC;CACtC,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,QAAQ,GAAG,oBAAoB,EAAE,CAAC,EAAE,CAAC;CAC9C,GAAG,MAAM,SAAS,GAAG,oBAAoB,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACnD,GAAG,MAAM,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAC;CAC3C,GAAG,MAAM,QAAQ,GAAG,UAAU,GAAG,SAAS,CAAC;AAC3C;CACA;CACA;CACA,GAAG,KAAK,KAAK,GAAG,SAAS,GAAG,CAAC,GAAG;AAChC;CACA;CACA,IAAI,MAAM,aAAa,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;CACzC,IAAI,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC;AACjC;CACA;CACA,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;CACd,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;CACA,KAAK,MAAM,GAAG,GAAG,aAAa,EAAE,CAAC,EAAE,CAAC;CACpC,KAAK,GAAG,CAAC,SAAS,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACjD,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;AACnB;CACA,KAAK,MAAM;CACX,MAAM,MAAM;CACZ,MAAM,eAAe;CACrB,MAAM,gBAAgB;CACtB,MAAM,GAAG,GAAG,CAAC;CACb,KAAK,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACpC;CACA,MAAM,gBAAgB,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC;CACvC,MAAM,gBAAgB,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC;AAC7C;CACA,MAAM,eAAe,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC;CACtC,MAAM,eAAe,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC;AAC5C;CACA,MAAM,MAAM,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC;CAC7B,MAAM,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC;AACnC;CACA,MAAM;AACN;CACA,KAAK,sBAAsB,EAAE,CAAC,EAAE,cAAc,EAAE,MAAM,EAAE,CAAC;AACzD;CACA,KAAK;AACL;CACA,IAAI,aAAa,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC;AACnC;CACA;CACA,IAAI,IAAI,UAAU,GAAG,KAAK,CAAC;CAC3B,IAAI,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,EAAE,EAAE,GAAG,GAAG;AAC/C;CACA,KAAK,MAAM,GAAG,GAAG,aAAa,EAAE,EAAE,EAAE,CAAC;CACrC,KAAK,QAAQ,EAAE,GAAG,CAAC,GAAG,UAAU,IAAI,aAAa,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,SAAS,KAAK,GAAG,CAAC,SAAS,GAAG;AAC1F;CACA,MAAM,aAAa,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;CACxC,MAAM,UAAU,GAAG,CAAC;AACpB;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA;CACA,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG;AAC7C;CACA,KAAK,MAAM,MAAM,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CAChD,KAAK,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,EAAE,EAAE,GAAG,GAAG;AAChD;CACA,MAAM,MAAM,GAAG,GAAG,aAAa,EAAE,EAAE,EAAE,CAAC;CACtC,MAAM,KAAK,MAAM,IAAI,GAAG,CAAC,SAAS,GAAG;AACrC;CACA,OAAO,sBAAsB,EAAE,CAAC,EAAE,cAAc,EAAE,GAAG,CAAC,gBAAgB,EAAE,CAAC;AACzE;CACA,OAAO,MAAM;AACb;CACA,OAAO,sBAAsB,EAAE,CAAC,EAAE,cAAc,EAAE,GAAG,CAAC,eAAe,EAAE,CAAC;CACxE,OAAO,GAAG,CAAC,KAAK,GAAG,CAAC;AACpB;CACA,OAAO;AACP;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA;CACA,IAAI,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,EAAE,EAAE,GAAG,GAAG;AAC/C;CACA,KAAK,MAAM,GAAG,GAAG,aAAa,EAAE,EAAE,EAAE,CAAC;CACrC,KAAK,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC;CACjC,KAAK,MAAM,UAAU,GAAG,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;AAC1C;CACA;CACA,KAAK,MAAM,UAAU,GAAG,GAAG,CAAC,eAAe,CAAC;CAC5C,KAAK,MAAM,WAAW,GAAG,GAAG,CAAC,gBAAgB,CAAC;AAC9C;CACA,KAAK,IAAI,QAAQ,GAAG,CAAC,CAAC;CACtB,KAAK,KAAK,SAAS,KAAK,CAAC,GAAG;AAC5B;CACA,MAAM,QAAQ,GAAG,kBAAkB,EAAE,UAAU,EAAE,GAAG,eAAe,CAAC;AACpE;CACA,MAAM;AACN;CACA,KAAK,IAAI,SAAS,GAAG,CAAC,CAAC;CACvB,KAAK,KAAK,UAAU,KAAK,CAAC,GAAG;AAC7B;CACA,MAAM,SAAS,GAAG,kBAAkB,EAAE,WAAW,EAAE,GAAG,eAAe,CAAC;AACtE;CACA,MAAM;AACN;CACA,KAAK,MAAM,IAAI,GAAG,cAAc,GAAG,uBAAuB;CAC1D,MAAM,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,UAAU;CACnD,MAAM,CAAC;AACP;CACA,KAAK,KAAK,IAAI,GAAG,QAAQ,GAAG;AAC5B;CACA,MAAM,IAAI,GAAG,CAAC,CAAC;CACf,MAAM,QAAQ,GAAG,IAAI,CAAC;CACtB,MAAM,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC;AAC1B;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA,IAAI,MAAM;AACV;CACA;CACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,GAAG,GAAG;AAC3C;CACA,KAAK,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC9B,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;CACnB,KAAK,GAAG,CAAC,SAAS,GAAG,QAAQ,GAAG,QAAQ,GAAG,CAAC,GAAG,QAAQ,CAAC;AACxD;CACA,KAAK,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;CAC/B,KAAK,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACpC;CACA,MAAM,MAAM,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC;CAC7B,MAAM,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC;AACnC;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA;CACA,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG;AAC7C;CACA,KAAK,MAAM,SAAS,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACnD,KAAK,MAAM,cAAc,GAAG,SAAS,GAAG,QAAQ,CAAC;AACjD;CACA;CACA;CACA,KAAK,IAAI,QAAQ,GAAG,EAAE,IAAI,cAAc,GAAG,QAAQ,EAAE,CAAC;CACtD,KAAK,KAAK,QAAQ,IAAI,SAAS,GAAG,QAAQ,GAAG,SAAS,GAAG,CAAC,CAAC;AAC3D;CACA,KAAK,MAAM,GAAG,GAAG,OAAO,EAAE,QAAQ,EAAE,CAAC;CACrC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC;AAClB;CACA,KAAK,sBAAsB,EAAE,CAAC,EAAE,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC;AAC7D;CACA,KAAK;AACL;CACA;CACA,IAAI,MAAM,OAAO,GAAG,OAAO,EAAE,SAAS,GAAG,CAAC,EAAE,CAAC;CAC7C,IAAI,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,gBAAgB,EAAE,CAAC;CAC3D,IAAI,MAAM,IAAI,CAAC,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AAChD;CACA,KAAK,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC9B,KAAK,MAAM,OAAO,GAAG,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACtC,KAAK,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,gBAAgB,EAAE,GAAG,CAAC,gBAAgB,EAAE,CAAC;AAC/E;CACA,KAAK;AACL;CACA,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC;CACtB,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC/C;CACA,KAAK,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC9B,KAAK,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC;CAChC,KAAK,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;AAC/B;CACA,KAAK,MAAM,OAAO,GAAG,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACtC,KAAK,MAAM,WAAW,GAAG,OAAO,CAAC,gBAAgB,CAAC;AAClD;CACA;CACA,KAAK,KAAK,QAAQ,KAAK,CAAC,GAAG;AAC3B;CACA,MAAM,KAAK,SAAS,KAAK,CAAC,GAAG;AAC7B;CACA,OAAO,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;AACxC;CACA,OAAO,MAAM;AACb;CACA,OAAO,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;AACrD;CACA,OAAO;AACP;CACA,MAAM;AACN;CACA,KAAK,SAAS,IAAI,QAAQ,CAAC;AAC3B;CACA;CACA,KAAK,IAAI,QAAQ,GAAG,CAAC,CAAC;CACtB,KAAK,IAAI,SAAS,GAAG,CAAC,CAAC;AACvB;CACA,KAAK,KAAK,SAAS,KAAK,CAAC,GAAG;AAC5B;CACA,MAAM,QAAQ,GAAG,kBAAkB,EAAE,UAAU,EAAE,GAAG,eAAe,CAAC;AACpE;CACA,MAAM;AACN;CACA,KAAK,MAAM,UAAU,GAAG,KAAK,GAAG,SAAS,CAAC;CAC1C,KAAK,KAAK,UAAU,KAAK,CAAC,GAAG;AAC7B;CACA,MAAM,SAAS,GAAG,kBAAkB,EAAE,WAAW,EAAE,GAAG,eAAe,CAAC;AACtE;CACA,MAAM;AACN;CACA,KAAK,MAAM,IAAI,GAAG,cAAc,GAAG,uBAAuB;CAC1D,MAAM,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,UAAU;CACnD,MAAM,CAAC;AACP;CACA,KAAK,KAAK,IAAI,GAAG,QAAQ,GAAG;AAC5B;CACA,MAAM,IAAI,GAAG,CAAC,CAAC;CACf,MAAM,QAAQ,GAAG,IAAI,CAAC;CACtB,MAAM,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC;AAC1B;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,MAAM;AACR;CACA,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,sCAAsC,GAAG,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC;AAC9E;CACA,EAAE;AACF;CACA,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AACtB;CACA,CAAC;AACD;CACA;CACA,SAAS,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,GAAG;AAC3D;CACA,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;CACb,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG;AAC7D;CACA,EAAE,GAAG,IAAI,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC;AAC5C;CACA,EAAE;AACF;CACA,CAAC,OAAO,GAAG,GAAG,KAAK,CAAC;AACpB;CACA;;CClTO,MAAM,WAAW,CAAC;AACzB;CACA,CAAC,WAAW,GAAG;AACf;CACA;CACA;AACA;CACA,EAAE,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC,EAAE,CAAC;AAC5C;CACA,EAAE;AACF;CACA;;CCXA;CACA;CACA;CACA;CACA;CACA;CACA,SAAS,SAAS,EAAE,cAAc,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG;AAClF;CACA,CAAC,IAAI,IAAI,GAAG,MAAM,CAAC;CACnB,CAAC,IAAI,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;CAChC,CAAC,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;CACvB,CAAC,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;AACnC;CACA;CACA,CAAC,QAAQ,IAAI,GAAG;AAChB;CACA,EAAE,QAAQ,IAAI,IAAI,KAAK,IAAI,cAAc,EAAE,IAAI,GAAG,CAAC,GAAG,UAAU,EAAE,GAAG,GAAG,GAAG;AAC3E;CACA,GAAG,IAAI,GAAG,CAAC;AACX;CACA,GAAG;AACH;CACA;CACA,EAAE,QAAQ,IAAI,IAAI,KAAK,IAAI,cAAc,EAAE,KAAK,GAAG,CAAC,GAAG,UAAU,EAAE,IAAI,GAAG,GAAG;AAC7E;CACA,GAAG,KAAK,GAAG,CAAC;AACZ;CACA,GAAG;AACH;CACA,EAAE,KAAK,IAAI,GAAG,KAAK,GAAG;AACtB;CACA;CACA;CACA;AACA;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,IAAI,IAAI,EAAE,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACnC,IAAI,KAAK,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,KAAK,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACnD,IAAI,KAAK,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;AAChC;CACA,IAAI;AACJ;AACA;CACA;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,IAAI,IAAI,EAAE,GAAG,cAAc,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CAC5C,IAAI,cAAc,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,cAAc,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACrE,IAAI,cAAc,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;AACzC;CACA,IAAI;AACJ;CACA,GAAG,IAAI,GAAG,CAAC;CACX,GAAG,KAAK,GAAG,CAAC;AACZ;CACA,GAAG,MAAM;AACT;CACA,GAAG,OAAO,IAAI,CAAC;AACf;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;;CChEA;CACA;CACA;CACA;CACA;CACA;CACA,SAAS,kBAAkB,EAAE,cAAc,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG;AAC3F;CACA,CAAC,IAAI,IAAI,GAAG,MAAM,CAAC;CACnB,CAAC,IAAI,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;CAChC,CAAC,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;CACvB,CAAC,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;AACnC;CACA;CACA,CAAC,QAAQ,IAAI,GAAG;AAChB;CACA,EAAE,QAAQ,IAAI,IAAI,KAAK,IAAI,cAAc,EAAE,IAAI,GAAG,CAAC,GAAG,UAAU,EAAE,GAAG,GAAG,GAAG;AAC3E;CACA,GAAG,IAAI,GAAG,CAAC;AACX;CACA,GAAG;AACH;CACA;CACA,EAAE,QAAQ,IAAI,IAAI,KAAK,IAAI,cAAc,EAAE,KAAK,GAAG,CAAC,GAAG,UAAU,EAAE,IAAI,GAAG,GAAG;AAC7E;CACA,GAAG,KAAK,GAAG,CAAC;AACZ;CACA,GAAG;AACH;CACA,EAAE,KAAK,IAAI,GAAG,KAAK,GAAG;AACtB;CACA;CACA;CACA;CACA,GAAG,IAAI,CAAC,GAAG,cAAc,EAAE,IAAI,EAAE,CAAC;CAClC,GAAG,cAAc,EAAE,IAAI,EAAE,GAAG,cAAc,EAAE,KAAK,EAAE,CAAC;CACpD,GAAG,cAAc,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;AAC/B;AACA;CACA;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,IAAI,IAAI,EAAE,GAAG,cAAc,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CAC5C,IAAI,cAAc,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,cAAc,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACrE,IAAI,cAAc,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;AACzC;CACA,IAAI;AACJ;CACA,GAAG,IAAI,GAAG,CAAC;CACX,GAAG,KAAK,GAAG,CAAC;AACZ;CACA,GAAG,MAAM;AACT;CACA,GAAG,OAAO,IAAI,CAAC;AACf;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;;CC3DO,SAAS,OAAO,EAAE,GAAG,EAAE,WAAW,GAAG;AAC5C;CACA,CAAC,OAAO,WAAW,EAAE,GAAG,GAAG,EAAE,EAAE,KAAK,MAAM,CAAC;AAC3C;CACA,CAAC;AACD;CACO,SAAS,MAAM,EAAE,GAAG,EAAE,WAAW,GAAG;AAC3C;CACA,CAAC,OAAO,WAAW,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;AAC/B;CACA,CAAC;AACD;CACO,SAAS,KAAK,EAAE,GAAG,EAAE,WAAW,GAAG;AAC1C;CACA,CAAC,OAAO,WAAW,EAAE,GAAG,GAAG,EAAE,EAAE,CAAC;AAChC;CACA,CAAC;AACD;CACO,SAAS,SAAS,EAAE,GAAG,GAAG;AACjC;CACA,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC;AAChB;CACA,CAAC;AACD;CACO,SAAS,UAAU,EAAE,GAAG,EAAE,WAAW,GAAG;AAC/C;CACA,CAAC,OAAO,WAAW,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;AAC/B;CACA,CAAC;AACD;CACO,SAAS,UAAU,EAAE,GAAG,EAAE,WAAW,GAAG;AAC/C;CACA,CAAC,OAAO,WAAW,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;AAC/B;CACA,CAAC;AACD;CACO,SAAS,mBAAmB,EAAE,GAAG,GAAG;AAC3C;CACA,CAAC,OAAO,GAAG,CAAC;AACZ;CACA;;CCrCA,IAAI,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC;CACvD,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;AACtC;CACO,SAAS,UAAU,EAAE,IAAI,GAAG;AACnC;CACA,CAAC,KAAK,OAAO,IAAI,IAAI,GAAG;AACxB;CACA,EAAE,OAAO,CAAC,CAAC;AACX;CACA,EAAE,MAAM;AACR;CACA,EAAE,OAAO,CAAC,GAAG,UAAU,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,UAAU,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AAChE;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,SAAS,cAAc,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,GAAG;AAC3D;CACA,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,MAAM,EAAE,CAAC;CAC3C,CAAC,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;CACzC,CAAC,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;CACzC,CAAC,UAAU,GAAG,IAAI,UAAU,EAAE,MAAM,EAAE,CAAC;AACvC;CACA,CAAC,OAAO,eAAe,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;AAC5C;CACA,CAAC;AACD;CACA;CACA;CACA;CACA;CACA,SAAS,eAAe,EAAE,UAAU,EAAE,IAAI,GAAG;AAC7C;CACA,CAAC,MAAM,aAAa,GAAG,UAAU,GAAG,CAAC,CAAC;CACtC,CAAC,MAAM,aAAa,GAAG,UAAU,GAAG,CAAC,CAAC;CACtC,CAAC,MAAM,MAAM,GAAG,OAAO,IAAI,IAAI,CAAC;CAChC,CAAC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;CACxC,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChC;CACA,EAAE,YAAY,EAAE,aAAa,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;AACxD;CACA,EAAE;AACF;CACA,CAAC,KAAK,MAAM,GAAG;AACf;CACA,EAAE,KAAK,IAAI,CAAC,MAAM,GAAG;AACrB;CACA,GAAG,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;CAC9B,GAAG,UAAU,CAAC,GAAG,EAAE,IAAI,UAAU,EAAE,MAAM,EAAE,EAAE,UAAU,EAAE,CAAC;AAC1D;CACA,GAAG,MAAM,IAAI,MAAM,GAAG,UAAU,EAAE,CAAC,GAAG,UAAU,GAAG,MAAM,CAAC,UAAU,EAAE,MAAM,GAAG,CAAC,EAAE,MAAM,IAAI,cAAc,GAAG;AAC7G;CACA,IAAI,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,CAAC;CAC/B,IAAI,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG;AAC7C;CACA,KAAK,WAAW,EAAE,EAAE,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,IAAI,aAAa,CAAC;AACxD;AACA;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG,OAAO,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;AACzC;CACA,GAAG,MAAM;AACT;CACA,GAAG,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;CAC9B,GAAG,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;CAC5B,GAAG,WAAW,EAAE,aAAa,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC;CAC7C,GAAG,WAAW,EAAE,aAAa,GAAG,EAAE,EAAE,GAAG,KAAK,CAAC;CAC7C,GAAG,WAAW,EAAE,aAAa,GAAG,EAAE,EAAE,GAAG,gBAAgB,CAAC;CACxD,GAAG,OAAO,UAAU,GAAG,cAAc,CAAC;AACtC;CACA,GAAG;AACH;CACA,EAAE,MAAM;AACR;CACA,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CACzB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;CAC3B,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACnC;CACA,EAAE,IAAI,iBAAiB,CAAC;CACxB,EAAE,iBAAiB,GAAG,eAAe,EAAE,UAAU,GAAG,cAAc,EAAE,IAAI,EAAE,CAAC;AAC3E;CACA,EAAE,KAAK,EAAE,iBAAiB,GAAG,CAAC,KAAK,WAAW,GAAG;AACjD;CACA,GAAG,MAAM,IAAI,KAAK,EAAE,2DAA2D,EAAE,CAAC;AAClF;CACA,GAAG;AACH;CACA,EAAE,WAAW,EAAE,aAAa,GAAG,CAAC,EAAE,GAAG,iBAAiB,GAAG,CAAC,CAAC;CAC3D,EAAE,iBAAiB,GAAG,eAAe,EAAE,iBAAiB,EAAE,KAAK,EAAE,CAAC;AAClE;CACA,EAAE,WAAW,EAAE,aAAa,GAAG,CAAC,EAAE,GAAG,SAAS,CAAC;CAC/C,EAAE,OAAO,iBAAiB,CAAC;AAC3B;CACA,EAAE;AACF;CACA;;CC5FO,SAAS,sBAAsB,EAAE,QAAQ,EAAE,oBAAoB,GAAG;AACzE;CACA,CAAC,MAAM,QAAQ,GAAG,EAAE,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC,CAAC;CACrG,CAAC,MAAM,SAAS,GAAG,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;CACtC,CAAC,MAAM,SAAS,GAAG,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;AACrC;CACA,CAAC,MAAM,MAAM,GAAG,oBAAoB,GAAG,IAAI,iBAAiB,EAAE,QAAQ,GAAG,SAAS,EAAE,GAAG,IAAI,WAAW,EAAE,QAAQ,GAAG,SAAS,EAAE,CAAC;CAC/H,CAAC,MAAM,cAAc,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;CAC1F,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC3D;CACA,EAAE,cAAc,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AAC1B;CACA,EAAE;AACF;CACA,CAAC,OAAO,cAAc,CAAC;AACvB;CACA,CAAC;AACD;CACO,SAAS,SAAS,EAAE,GAAG,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,GAAG;AACzE;CACA;CACA,CAAC,MAAM;CACP,EAAE,QAAQ;CACV,EAAE,OAAO;CACT,EAAE,WAAW;CACb,EAAE,QAAQ;CACV,EAAE,UAAU;CACZ,EAAE,QAAQ;CACV,EAAE,GAAG,OAAO,CAAC;CACb,CAAC,MAAM,cAAc,GAAG,GAAG,CAAC,eAAe,CAAC;CAC5C,CAAC,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;CAC/B,CAAC,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;CACjE,CAAC,MAAM,WAAW,GAAG,QAAQ,GAAG,kBAAkB,GAAG,SAAS,CAAC;AAC/D;CACA;CACA,CAAC,MAAM,cAAc,GAAG,WAAW,EAAE,QAAQ,EAAE,CAAC;CAChD,CAAC,MAAM,yBAAyB,GAAG,IAAI,YAAY,EAAE,CAAC,EAAE,CAAC;CACzD,CAAC,IAAI,eAAe,GAAG,KAAK,CAAC;AAC7B;CACA,CAAC,MAAM,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC;CAChC,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,yBAAyB,EAAE,CAAC;CAC1F,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC;CAC7D,CAAC,OAAO,IAAI,CAAC;AACb;CACA,CAAC,SAAS,eAAe,EAAE,kBAAkB,GAAG;AAChD;CACA,EAAE,KAAK,UAAU,GAAG;AACpB;CACA,GAAG,UAAU,EAAE,kBAAkB,GAAG,cAAc,EAAE,CAAC;AACrD;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;CACA;CACA,CAAC,SAAS,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,oBAAoB,GAAG,IAAI,EAAE,KAAK,GAAG,CAAC,GAAG;AACnF;CACA,EAAE,KAAK,EAAE,eAAe,IAAI,KAAK,IAAI,QAAQ,GAAG;AAChD;CACA,GAAG,eAAe,GAAG,IAAI,CAAC;CAC1B,GAAG,KAAK,OAAO,GAAG;AAClB;CACA,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,sBAAsB,GAAG,QAAQ,EAAE,2DAA2D,CAAC,EAAE,CAAC;CACrH,IAAI,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC;AAC7B;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA,EAAE,KAAK,KAAK,IAAI,WAAW,IAAI,KAAK,IAAI,QAAQ,GAAG;AACnD;CACA,GAAG,eAAe,EAAE,MAAM,GAAG,KAAK,EAAE,CAAC;CACrC,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;CACxB,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACtB,GAAG,OAAO,IAAI,CAAC;AACf;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,KAAK,GAAG,eAAe,EAAE,IAAI,CAAC,YAAY,EAAE,oBAAoB,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;CACpH,EAAE,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE,CAAC,GAAG;AAC5B;CACA,GAAG,eAAe,EAAE,MAAM,GAAG,KAAK,EAAE,CAAC;CACrC,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;CACxB,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACtB,GAAG,OAAO,IAAI,CAAC;AACf;CACA,GAAG;AACH;CACA,EAAE,MAAM,WAAW,GAAG,WAAW,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AACtG;CACA;CACA,EAAE,KAAK,WAAW,KAAK,MAAM,IAAI,WAAW,KAAK,MAAM,GAAG,KAAK,GAAG;AAClE;CACA,GAAG,eAAe,EAAE,MAAM,GAAG,KAAK,EAAE,CAAC;CACrC,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;CACxB,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACtB;CACA,GAAG,MAAM;AACT;CACA,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;AAC/B;CACA;CACA,GAAG,MAAM,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC;CAClC,GAAG,MAAM,MAAM,GAAG,MAAM,CAAC;CACzB,GAAG,MAAM,MAAM,GAAG,WAAW,GAAG,MAAM,CAAC;CACvC,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACpB;CACA,GAAG,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,yBAAyB,EAAE,CAAC;CAC7F,GAAG,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,yBAAyB,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;AAC3E;CACA;CACA,GAAG,MAAM,KAAK,GAAG,IAAI,WAAW,EAAE,CAAC;CACnC,GAAG,MAAM,MAAM,GAAG,WAAW,CAAC;CAC9B,GAAG,MAAM,MAAM,GAAG,KAAK,GAAG,MAAM,CAAC;CACjC,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACtB;CACA,GAAG,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,YAAY,EAAE,yBAAyB,EAAE,CAAC;CAC9F,GAAG,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,yBAAyB,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;AAC5E;CACA,GAAG;AACH;CACA,EAAE,OAAO,IAAI,CAAC;AACd;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,SAAS,eAAe,EAAE,GAAG,EAAE,OAAO,GAAG;AAChD;CACA,CAAC,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;CAC/B,CAAC,KAAK,OAAO,CAAC,QAAQ,GAAG;AACzB;CACA,EAAE,GAAG,CAAC,eAAe,GAAG,sBAAsB,EAAE,QAAQ,EAAE,OAAO,CAAC,oBAAoB,EAAE,CAAC;AACzF;CACA,EAAE,KAAK,YAAY,EAAE,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,OAAO,GAAG;AACtE;CACA,GAAG,OAAO,CAAC,IAAI;CACf,IAAI,sIAAsI;CAC1I,IAAI,kFAAkF;CACtF,IAAI,CAAC;AACL;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,KAAK,EAAE,GAAG,CAAC,eAAe,GAAG;AAC9B;CACA,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;AACnC;CACA,EAAE;AACF;CACA,CAAC,MAAM,iBAAiB,GAAG,OAAO,CAAC,oBAAoB,GAAG,iBAAiB,GAAG,WAAW,CAAC;AAC1F;CACA,CAAC,MAAM,cAAc,GAAG,qBAAqB,EAAE,QAAQ,EAAE,CAAC;CAC1D,CAAC,MAAM,cAAc,GAAG,OAAO,CAAC,QAAQ,GAAG,oBAAoB,EAAE,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,GAAG,kBAAkB,EAAE,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC;CAC3I,CAAC,GAAG,CAAC,MAAM,GAAG,cAAc,CAAC,GAAG,EAAE,KAAK,IAAI;AAC3C;CACA,EAAE,MAAM,IAAI,GAAG,SAAS,EAAE,GAAG,EAAE,cAAc,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC;CACpF,EAAE,MAAM,SAAS,GAAG,UAAU,EAAE,IAAI,EAAE,CAAC;CACvC,EAAE,MAAM,MAAM,GAAG,IAAI,iBAAiB,EAAE,cAAc,GAAG,SAAS,EAAE,CAAC;CACrE,EAAE,cAAc,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;CACpC,EAAE,OAAO,MAAM,CAAC;AAChB;CACA,EAAE,EAAE,CAAC;AACL;CACA;;CChLO,MAAM,oBAAoB,CAAC;AAClC;CACA,CAAC,WAAW,GAAG;AACf;CACA,EAAE,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC;CACtB,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,QAAQ,CAAC;AACxB;CACA,EAAE;AACF;CACA,CAAC,kBAAkB,EAAE,MAAM,EAAE,KAAK,GAAG;AACrC;CACA,EAAE,IAAI,GAAG,GAAG,QAAQ,CAAC;CACrB,EAAE,IAAI,GAAG,GAAG,EAAE,QAAQ,CAAC;CACvB,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACpD;CACA,GAAG,MAAM,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CACzB,GAAG,MAAM,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC;CAC1B,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;CAC/B,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/B;CACA,GAAG;AACH;CACA,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;CACjB,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB;CACA,EAAE;AACF;CACA,CAAC,aAAa,EAAE,IAAI,EAAE,MAAM,GAAG;AAC/B;CACA,EAAE,IAAI,GAAG,GAAG,QAAQ,CAAC;CACrB,EAAE,IAAI,GAAG,GAAG,EAAE,QAAQ,CAAC;CACvB,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACpD;CACA,GAAG,MAAM,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CACzB,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;CAC7B,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;CAC/B,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/B;CACA,GAAG;AACH;CACA,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;CACjB,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB;CACA,EAAE;AACF;CACA,CAAC,WAAW,EAAE,KAAK,GAAG;AACtB;CACA,EAAE,OAAO,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACtD;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,oBAAoB,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,YAAY;AAC1D;CACA,CAAC,MAAM,CAAC,GAAG,IAAIC,aAAO,EAAE,CAAC;CACzB,CAAC,OAAO,SAAS,UAAU,EAAE,IAAI,EAAE,GAAG,GAAG;AACzC;CACA,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC;CACzB,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC;CACzB,EAAE,IAAI,GAAG,GAAG,QAAQ,CAAC;CACrB,EAAE,IAAI,GAAG,GAAG,EAAE,QAAQ,CAAC;CACvB,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACnC;CACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACpC;CACA,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;CAC/C,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;CAC/C,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;AAC/C;CACA,KAAK,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;CAC/B,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CAChC,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAChC;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;CACjB,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;CACO,MAAM,eAAe,GAAG,EAAE,YAAY;AAC7C;CACA,CAAC,MAAM,cAAc,GAAG,IAAI,oBAAoB,EAAE,CAAC;CACnD,CAAC,OAAO,SAAS,eAAe,EAAE,MAAM,EAAE,MAAM,GAAG;AACnD;CACA,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;CAChC,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;CAClC,EAAE,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;AACtC;CACA,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;CAChC,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;CAClC,EAAE,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;AACtC;CACA;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;CAC9B,GAAG,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;CAC5B,GAAG,cAAc,CAAC,aAAa,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC;CAC/C,GAAG,KAAK,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,GAAG,OAAO,KAAK,CAAC;AACxD;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;CAC9B,GAAG,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;CAC5B,GAAG,cAAc,CAAC,aAAa,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC;CAC/C,GAAG,KAAK,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,GAAG,OAAO,KAAK,CAAC;AACxD;CACA,GAAG;AACH;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI;;CC5HE,MAAM,sBAAsB,GAAG,EAAE,YAAY;AACpD;CACA;CACA,CAAC,MAAM,IAAI,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC5B,CAAC,MAAM,IAAI,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC5B,CAAC,MAAM,GAAG,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC3B,CAAC,OAAO,SAAS,sBAAsB,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,GAAG;AAC1D;CACA,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC;CACtB,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC;CACnB,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC;CACtB,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC;AACnB;CACA,EAAE,GAAG,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;CAC3B,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC;CACtC,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC;AACtC;CACA;CACA,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC/B;CACA;CACA,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC/B;CACA;CACA,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC/B;CACA;CACA,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC/B;CACA;CACA,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC/B;CACA;CACA,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAC9C;CACA,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;CACZ,EAAE,KAAK,KAAK,KAAK,CAAC,GAAG;AACrB;CACA,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,KAAK,KAAK,CAAC;AACjD;CACA,GAAG,MAAM;AACT;CACA,GAAG,CAAC,GAAG,CAAC,CAAC;AACT;CACA,GAAG;AACH;CACA,EAAE,EAAE,GAAG,EAAE,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,KAAK,CAAC;AACrC;CACA,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;CACf,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;AAChB;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;CACO,MAAM,6BAA6B,GAAG,EAAE,YAAY;AAC3D;CACA;CACA,CAAC,MAAM,WAAW,GAAG,IAAIC,aAAO,EAAE,CAAC;CACnC,CAAC,MAAM,KAAK,GAAG,IAAID,aAAO,EAAE,CAAC;CAC7B,CAAC,MAAM,KAAK,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC7B,CAAC,OAAO,SAAS,6BAA6B,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO,GAAG;AAC3E;CACA,EAAE,sBAAsB,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,CAAC;AAChD;CACA,EAAE,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;CACxB,EAAE,IAAI,EAAE,GAAG,WAAW,CAAC,CAAC,CAAC;CACzB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG;AAChD;CACA,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;CACvB,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC;AACxB;CACA,GAAG,OAAO;AACV;CACA,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;AACjC;CACA;CACA,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG;AACjB;CACA,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;AACxB;CACA,IAAI,MAAM;AACV;CACA,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;AACxB;CACA,IAAI;AACJ;CACA,GAAG,EAAE,CAAC,mBAAmB,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;CACpD,GAAG,OAAO;AACV;CACA,GAAG,MAAM,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG;AACnC;CACA;CACA,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG;AAChB;CACA,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;AACxB;CACA,IAAI,MAAM;AACV;CACA,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;AACxB;CACA,IAAI;AACJ;CACA,GAAG,EAAE,CAAC,mBAAmB,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;CACpD,GAAG,OAAO;AACV;CACA,GAAG,MAAM;AACT;CACA;CACA,GAAG,IAAI,CAAC,CAAC;CACT,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG;AAChB;CACA,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;AACjB;CACA,IAAI,MAAM;AACV;CACA,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;AACf;CACA,IAAI;AACJ;CACA,GAAG,IAAI,EAAE,CAAC;CACV,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG;AACjB;CACA,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC;AAClB;CACA,IAAI,MAAM;AACV;CACA,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;AAChB;CACA,IAAI;AACJ;CACA,GAAG,MAAM,YAAY,GAAG,KAAK,CAAC;CAC9B,GAAG,MAAM,aAAa,GAAG,KAAK,CAAC;CAC/B,GAAG,EAAE,CAAC,mBAAmB,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;CAC7C,GAAG,EAAE,CAAC,mBAAmB,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AAC5C;CACA,GAAG,KAAK,YAAY,CAAC,iBAAiB,EAAE,EAAE,EAAE,IAAI,aAAa,CAAC,iBAAiB,EAAE,CAAC,EAAE,GAAG;AACvF;CACA,IAAI,OAAO,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC;CACjC,IAAI,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CACvB,IAAI,OAAO;AACX;CACA,IAAI,MAAM;AACV;CACA,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;CACtB,IAAI,OAAO,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC;CAClC,IAAI,OAAO;AACX;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;AACA;CACO,MAAM,uBAAuB,GAAG,EAAE,YAAY;AACrD;CACA;CACA,CAAC,MAAM,gBAAgB,GAAG,IAAIA,aAAO,EAAE,CAAC;CACxC,CAAC,MAAM,kBAAkB,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC1C,CAAC,MAAM,SAAS,GAAG,IAAIE,WAAK,EAAE,CAAC;CAC/B,CAAC,MAAM,QAAQ,GAAG,IAAIC,WAAK,EAAE,CAAC;CAC9B,CAAC,OAAO,SAAS,uBAAuB,EAAE,MAAM,EAAE,QAAQ,GAAG;AAC7D;CACA,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;CACpC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC/B;CACA;CACA,EAAE,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;CACrB,EAAE,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC;CACnB,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,mBAAmB,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC;CACvF,EAAE,KAAK,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,MAAM,GAAG,OAAO,IAAI,CAAC;AAClE;CACA,EAAE,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;CACrB,EAAE,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC;CACnB,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,mBAAmB,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC;CACvF,EAAE,KAAK,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,MAAM,GAAG,OAAO,IAAI,CAAC;AAClE;CACA,EAAE,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;CACrB,EAAE,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC;CACnB,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,mBAAmB,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC;CACvF,EAAE,KAAK,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,MAAM,GAAG,OAAO,IAAI,CAAC;AAClE;CACA;CACA,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC;CAC/C,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,eAAe,EAAE,MAAM,EAAE,EAAE,CAAC;CACzD,EAAE,KAAK,EAAE,IAAI,MAAM,GAAG;AACtB;CACA,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,YAAY,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC;CAC/D,GAAG,MAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,EAAE,EAAE,EAAE,CAAC;CAC3C,GAAG,KAAK,EAAE,GAAG,OAAO,IAAI,CAAC;AACzB;CACA,GAAG;AACH;CACA,EAAE,OAAO,KAAK,CAAC;AACf;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI;;CCtML,MAAM,YAAY,GAAG,KAAK,CAAC;CAC3B,SAAS,UAAU,EAAE,KAAK,GAAG;AAC7B;CACA,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,YAAY,CAAC;AACzC;CACA,CAAC;AACD;CACO,MAAM,gBAAgB,SAASC,cAAQ,CAAC;AAC/C;CACA,CAAC,WAAW,EAAE,GAAG,IAAI,GAAG;AACxB;CACA,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,CAAC;AACnB;CACA,EAAE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;CACjC,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAIJ,aAAO,EAAE,EAAE,CAAC;CAClE,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAI,oBAAoB,EAAE,EAAE,CAAC;CACjF,EAAE,IAAI,CAAC,MAAM,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;CAC3C,EAAE,IAAI,CAAC,MAAM,GAAG,IAAIK,YAAM,EAAE,CAAC;CAC7B,EAAE,IAAI,CAAC,KAAK,GAAG,IAAIH,WAAK,EAAE,CAAC;CAC3B,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B;CACA,EAAE;AACF;CACA,CAAC,gBAAgB,EAAE,MAAM,GAAG;AAC5B;CACA,EAAE,OAAO,uBAAuB,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACjD;CACA,EAAE;AACF;CACA,CAAC,MAAM,GAAG;AACV;CACA,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;CACnB,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;CACnB,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;CACnB,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B;CACA,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;CAC/B,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACnC;CACA,EAAE,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC7B,EAAE,MAAM,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC9B,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC;CAC1B,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACtC;CACA,EAAE,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC7B,EAAE,MAAM,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC9B,EAAE,KAAK,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;CAC3B,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACtC;CACA,EAAE,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC7B,EAAE,MAAM,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC9B,EAAE,KAAK,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;CAC3B,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACtC;CACA,EAAE,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC7B,EAAE,MAAM,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC9B,EAAE,KAAK,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;CAC3B,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACtC;CACA,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;CAC3C,EAAE,IAAI,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;CACvD,EAAE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC3B;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,gBAAgB,CAAC,SAAS,CAAC,qBAAqB,GAAG,EAAE,YAAY;AACjE;CACA,CAAC,MAAM,MAAM,GAAG,IAAIF,aAAO,EAAE,CAAC;CAC9B,CAAC,MAAM,MAAM,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC9B,CAAC,MAAM,IAAI,GAAG,IAAIG,WAAK,EAAE,CAAC;AAC1B;CACA,CAAC,OAAO,SAAS,iBAAiB,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,GAAG;AAC9E;CACA,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC;CACjC,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;CAC7B,EAAE,IAAI,MAAM,CAAC;CACb,EAAE,IAAI,iBAAiB,GAAG,QAAQ,CAAC;AACnC;CACA;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,KAAK,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CAC/B,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;CAClC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;AACpC;CACA,GAAG,6BAA6B,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAClE;CACA,GAAG,MAAM,GAAG,MAAM,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;CAC/C,GAAG,KAAK,MAAM,GAAG,iBAAiB,GAAG;AACrC;CACA,IAAI,iBAAiB,GAAG,MAAM,CAAC;CAC/B,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;CAC1C,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC1C;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA,EAAE,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;CAC5C,EAAE,MAAM,GAAG,KAAK,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;CAC7C,EAAE,KAAK,MAAM,GAAG,iBAAiB,GAAG;AACpC;CACA,GAAG,iBAAiB,GAAG,MAAM,CAAC;CAC9B,GAAG,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;CACzC,GAAG,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACxC;CACA,GAAG;AACH;CACA,EAAE,IAAI,CAAC,mBAAmB,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC;CAC1C,EAAE,MAAM,GAAG,GAAG,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;CAC3C,EAAE,KAAK,MAAM,GAAG,iBAAiB,GAAG;AACpC;CACA,GAAG,iBAAiB,GAAG,MAAM,CAAC;CAC9B,GAAG,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;CACzC,GAAG,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AACtC;CACA,GAAG;AACH;CACA,EAAE,OAAO,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC;AACxC;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;CACA,gBAAgB,CAAC,SAAS,CAAC,kBAAkB,GAAG,EAAE,YAAY;AAC9D;CACA,CAAC,MAAM,MAAM,GAAG,IAAI,gBAAgB,EAAE,CAAC;CACvC,CAAC,MAAM,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC;CAC7B,CAAC,MAAM,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC;CAC7B,CAAC,MAAM,eAAe,GAAG,IAAI,oBAAoB,EAAE,CAAC;CACpD,CAAC,MAAM,gBAAgB,GAAG,IAAI,oBAAoB,EAAE,CAAC;CACrD,CAAC,MAAM,UAAU,GAAG,IAAIH,aAAO,EAAE,CAAC;CAClC,CAAC,MAAM,GAAG,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC3B,CAAC,MAAM,IAAI,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC5B,CAAC,MAAM,IAAI,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC5B,CAAC,MAAM,OAAO,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC/B,CAAC,MAAM,IAAI,GAAG,IAAIG,WAAK,EAAE,CAAC;CAC1B,CAAC,MAAM,KAAK,GAAG,IAAIA,WAAK,EAAE,CAAC;CAC3B,CAAC,MAAM,KAAK,GAAG,IAAIA,WAAK,EAAE,CAAC;CAC3B,CAAC,MAAM,SAAS,GAAG,IAAIH,aAAO,EAAE,CAAC;AACjC;CACA,CAAC,SAAS,iBAAiB,EAAE,GAAG,EAAE,KAAK,EAAE,UAAU,GAAG;AACtD;CACA;CACA,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;CAC5B,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC;CAChB,EAAE,IAAI,sBAAsB,GAAG,EAAE,CAAC,CAAC;CACnC,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;CAC/B,GAAG,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;CAC7B,GAAG,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;CACvC,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;AACrB;CACA,GAAG,MAAM,eAAe,GAAG,UAAU,EAAE,KAAK,CAAC,eAAe,EAAE,KAAK,EAAE,EAAE,CAAC;CACxE,GAAG,KAAK,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,IAAI,eAAe,GAAG;AACnE;CACA;CACA,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;CAC5B,IAAI,KAAK,GAAG,CAAC,CAAC;CACd,IAAI,MAAM;AACV;CACA,IAAI;AACJ;CACA;CACA,GAAG,MAAM,aAAa,GAAG,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;CAChE,GAAG,KAAK,EAAE,aAAa,IAAI,eAAe,GAAG;AAC7C;CACA,IAAI,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AAC5B;CACA,IAAI;AACJ;CACA;CACA,GAAG,KAAK,EAAE,aAAa,IAAI,eAAe,MAAM,EAAE,UAAU,EAAE,SAAS,CAAC,UAAU,EAAE,GAAG,EAAE,EAAE,GAAG;AAC9F;CACA,IAAI,KAAK,KAAK,IAAI,CAAC,GAAG;AACtB;CACA;CACA;CACA,KAAK,MAAM,KAAK,GAAG,KAAK,KAAK,CAAC,GAAG,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC;CACnE,KAAK,KAAK,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC;CAC7B,KAAK,KAAK,eAAe,GAAG;AAC5B;CACA,MAAM,sBAAsB,GAAG,KAAK,CAAC;AACrC;CACA,MAAM;AACN;CACA,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC,GAAG;AAC7B;CACA;CACA;CACA,KAAK,MAAM,KAAK,GAAG,sBAAsB,KAAK,CAAC,GAAG,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC;CACpF,KAAK,KAAK,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC;CAC7B,KAAK,KAAK,GAAG,CAAC,CAAC;CACf,KAAK,MAAM;AACX;CACA,KAAK;AACL;CACA,IAAI,KAAK,GAAG,CAAC;CACb,IAAI,KAAK,KAAK,KAAK,CAAC,IAAI,sBAAsB,KAAK,EAAE,CAAC,GAAG;AACzD;CACA,KAAK,MAAM;AACX;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,OAAO,KAAK,CAAC;AACf;CACA,EAAE;AACF;CACA;CACA;CACA,CAAC,OAAO,SAAS,kBAAkB,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI,EAAE,WAAW,GAAG,KAAK,GAAG;AACjF;CACA,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG;AAC1B;CACA,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;CACA,GAAG;AACH;CACA,EAAE,KAAK,EAAE,KAAK,CAAC,kBAAkB,GAAG;AACpC;CACA,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;CACxB,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;CACnB,GAAG,KAAK,GAAG,MAAM,CAAC;AAClB;CACA,GAAG,MAAM,KAAK,KAAK,CAAC,WAAW,GAAG;AAClC;CACA,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;AAClB;CACA,GAAG;AACH;CACA,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;CAC5B,EAAE,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;AAC7B;CACA,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,EAAE,EAAE,GAAG,GAAG,GAAG,KAAK,GAAG;AACtE;CACA;CACA,GAAG,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;CACrC,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC;CACjC,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;CACvB,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;CACvB,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;CACvB,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,IAAI,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;CAC/B,IAAI,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;CAC7B,IAAI,eAAe,CAAC,aAAa,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC;CAC9C,IAAI,KAAK,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,GAAG,OAAO,KAAK,CAAC;AAC1D;CACA,IAAI;AACJ;CACA,GAAG,MAAM,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC;CACtC,GAAG,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;CAClC,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;CACtB,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;CACtB,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;CACtB,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,IAAI,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;CAC/B,IAAI,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;CAC7B,IAAI,eAAe,CAAC,aAAa,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC;CAC9C,IAAI,KAAK,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,GAAG,OAAO,KAAK,CAAC;AAC1D;CACA,IAAI;AACJ;CACA;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,IAAI,MAAM,GAAG,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;CAC9B,IAAI,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG;AACtC;CACA,KAAK,MAAM,GAAG,GAAG,QAAQ,EAAE,EAAE,EAAE,CAAC;CAChC,KAAK,UAAU,CAAC,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACzC,KAAK,eAAe,CAAC,aAAa,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;CACvD,KAAK,gBAAgB,CAAC,aAAa,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;CACxD,KAAK,KAAK,eAAe,CAAC,WAAW,EAAE,gBAAgB,EAAE,GAAG,OAAO,KAAK,CAAC;AACzE;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG,KAAK,MAAM,GAAG;AACjB;CACA;CACA,IAAI,KAAK,EAAE,WAAW,GAAG;AACzB;CACA,KAAK,OAAO,CAAC,IAAI,EAAE,6HAA6H,EAAE,CAAC;AACnJ;CACA,KAAK;AACL;CACA,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;CAChC,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAC9B;CACA,IAAI;AACJ;CACA,GAAG,OAAO,IAAI,CAAC;AACf;CACA,GAAG,MAAM;AACT;CACA;CACA,GAAG,MAAM,MAAM,GAAG,iBAAiB,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;CAC3D,GAAG,KAAK,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG;AAC3D;CACA,IAAI,KAAK,MAAM,GAAG;AAClB;CACA,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;CACpC,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AAClC;CACA,KAAK;AACL;CACA,IAAI,OAAO,IAAI,CAAC;AAChB;CACA,IAAI,MAAM,KAAK,MAAM,KAAK,CAAC,GAAG;AAC9B;CACA,IAAI,OAAO,KAAK,CAAC;AACjB;CACA,IAAI;AACJ;CACA;CACA,GAAG,MAAM,MAAM,GAAG,iBAAiB,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;CAC5D,GAAG,KAAK,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG;AAC1D;CACA,IAAI,KAAK,MAAM,GAAG;AAClB;CACA,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;CACpC,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AAClC;CACA,KAAK;AACL;CACA,IAAI,OAAO,IAAI,CAAC;AAChB;CACA,IAAI,MAAM,KAAK,MAAM,KAAK,CAAC,GAAG;AAC9B;CACA,IAAI,OAAO,KAAK,CAAC;AACjB;CACA,IAAI;AACJ;CACA;CACA,GAAG,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;CACvB,GAAG,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;AACvB;CACA,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG;AAC/B;CACA,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC;CAC1B,IAAI,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;CAC5B,IAAI,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;AACpB;CACA,IAAI;AACJ;CACA;CACA,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;CACtC,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;CACpC,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;CACtC,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;CACpC,GAAG,MAAM,UAAU,GAAG,EAAE,GAAG,EAAE,CAAC;CAC9B,GAAG,MAAM,UAAU,GAAG,EAAE,GAAG,EAAE,CAAC;AAC9B;CACA,GAAG,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,UAAU,KAAK,UAAU,GAAG;AAC9D;CACA,IAAI,OAAO,KAAK,CAAC;AACjB;CACA,IAAI;AACJ;CACA;CACA,GAAG,KAAK,MAAM,GAAG;AACjB;CACA,IAAI,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;CACnD,IAAI,KAAK,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG;AACnC;CACA,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;AACtC;CACA,KAAK,MAAM;AACX;CACA,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;AACtC;CACA,KAAK;AACL;CACA,IAAI,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;CAC/C,IAAI,KAAK,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG;AACnC;CACA,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AAClC;CACA,KAAK,MAAM;AACX;CACA,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AAClC;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG,OAAO,IAAI,CAAC;AACf;CACA,GAAG;AACH;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;AACA;CACA,gBAAgB,CAAC,SAAS,CAAC,eAAe,GAAG,EAAE,YAAY;AAC3D;CACA,CAAC,MAAM,MAAM,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC9B,CAAC,OAAO,SAAS,eAAe,EAAE,KAAK,GAAG;AAC1C;CACA,EAAE,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;CAC5C,EAAE,OAAO,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;AACpC;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;AACA;CACA,gBAAgB,CAAC,SAAS,CAAC,kBAAkB,GAAG,EAAE,YAAY;AAC9D;CACA,CAAC,MAAM,KAAK,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC7B,CAAC,MAAM,MAAM,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC9B,CAAC,MAAM,YAAY,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACxC,CAAC,MAAM,KAAK,GAAG,IAAIG,WAAK,EAAE,CAAC;CAC3B,CAAC,MAAM,KAAK,GAAG,IAAIA,WAAK,EAAE,CAAC;AAC3B;CACA,CAAC,OAAO,SAAS,kBAAkB,EAAE,KAAK,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,GAAG;AAC7E;CACA,EAAE,MAAM,UAAU,GAAG,OAAO,IAAI,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC;CACvD,EAAE,KAAK,IAAI,CAAC,kBAAkB,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;AACtD;CACA,GAAG,KAAK,OAAO,IAAI,OAAO,GAAG;AAC7B;CACA,IAAI,KAAK,OAAO,GAAG,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC;CACnD,IAAI,KAAK,OAAO,GAAG,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC;AACnD;CACA,IAAI;AACJ;CACA,GAAG,OAAO,CAAC,CAAC;AACZ;CACA,GAAG;AACH;CACA,EAAE,IAAI,iBAAiB,GAAG,QAAQ,CAAC;AACnC;CACA;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,IAAI,IAAI,CAAC;CACZ,GAAG,MAAM,KAAK,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;CACnC,GAAG,MAAM,QAAQ,GAAG,KAAK,EAAE,KAAK,EAAE,CAAC;CACnC,GAAG,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;AAC/C;CACA,GAAG,IAAI,GAAG,QAAQ,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC;AAC9C;CACA,GAAG,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACnC;CACA,IAAI,iBAAiB,GAAG,IAAI,CAAC;CAC7B,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;CACzC,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC;AAC5C;CACA,IAAI;AACJ;AACA;CACA,GAAG,MAAM,OAAO,GAAG,IAAI,EAAE,KAAK,EAAE,CAAC;CACjC,GAAG,KAAK,CAAC,mBAAmB,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;AAC/C;CACA,GAAG,IAAI,GAAG,OAAO,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC;AAC7C;CACA,GAAG,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACnC;CACA,IAAI,iBAAiB,GAAG,IAAI,CAAC;CAC7B,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC;CAC3C,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACzC;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,GAAG,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;CACjC,GAAG,MAAM,GAAG,GAAG,YAAY,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;CAC7C,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;CACzC,GAAG,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG;AACrC;CACA,IAAI,MAAM,GAAG,GAAG,YAAY,EAAE,EAAE,EAAE,CAAC;CACnC,IAAI,MAAM,GAAG,GAAG,YAAY,EAAE,EAAE,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;CAC/C,IAAI,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC;AAC5C;CACA,IAAI,6BAA6B,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACjE;CACA,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;CACnD,IAAI,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACpC;CACA,KAAK,iBAAiB,GAAG,IAAI,CAAC;CAC9B,KAAK,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;CAC1C,KAAK,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC3C;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,OAAO,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC;AACxC;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI;;CC5fE,MAAM,WAAW,CAAC;AACzB;CACA,CAAC,WAAW,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,GAAG;AACjC;CACA,EAAE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;CAC5B,EAAE,IAAI,CAAC,GAAG,GAAG,IAAIH,aAAO,EAAE,CAAC;CAC3B,EAAE,IAAI,CAAC,GAAG,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC3B,EAAE,IAAI,CAAC,MAAM,GAAG,IAAIM,aAAO,EAAE,CAAC;CAC9B,EAAE,IAAI,CAAC,SAAS,GAAG,IAAIA,aAAO,EAAE,CAAC;CACjC,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAIN,aAAO,EAAE,EAAE,CAAC;CACjE,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAIA,aAAO,EAAE,EAAE,CAAC;CAClE,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAI,oBAAoB,EAAE,EAAE,CAAC;CACjF,EAAE,IAAI,CAAC,gBAAgB,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAI,oBAAoB,EAAE,EAAE,CAAC;CACxF,EAAE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC3B;CACA,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;CAClC,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;CAClC,EAAE,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC3C;CACA,EAAE;AACF;CACA,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,GAAG;AACzB;CACA,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;CACvB,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;CACvB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;CAC7B,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B;CACA,EAAE;AACF;CACA,CAAC,IAAI,EAAE,KAAK,GAAG;AACf;CACA,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;CAC7B,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;CAC7B,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC;CACnC,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE,YAAY;AAC7C;CACA,CAAC,OAAO,SAAS,MAAM,GAAG;AAC1B;CACA,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;CAC7B,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;CACvB,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACvB;CACA,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;CAC7B,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACnC;CACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACpC;CACA,KAAK,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;CAC5E,KAAK,MAAM,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CAC3B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CAC7B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CAC7B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAC7B;CACA,KAAK,CAAC,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC;AAC9B;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;CACnC,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;CAC/B,EAAE,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CAC7B,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC7B,GAAG,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC7B,GAAG,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;CACxB,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,KAAK,EAAE,CAAC;AAC9B;CACA,GAAG,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;CACjC,GAAG,EAAE,CAAC,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;AACpC;CACA,GAAG;AACH;CACA,EAAE,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;CACjD,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,kBAAkB,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;CAC1D,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,kBAAkB,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;CAC1D,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,kBAAkB,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1D;CACA,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC;CAC9C,EAAE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC3B;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;CACA,WAAW,CAAC,SAAS,CAAC,aAAa,GAAG,EAAE,YAAY;AACpD;CACA,CAAC,MAAM,UAAU,GAAG,IAAI,oBAAoB,EAAE,CAAC;CAC/C,CAAC,OAAO,SAAS,aAAa,EAAE,GAAG,GAAG;AACtC;CACA;CACA,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG;AAC1B;CACA,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;CACA,GAAG;AACH;CACA,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;CACtB,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;CACtB,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;CACnC,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;CAC/B,EAAE,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACjD;CACA,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;CACzB,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;CACzB,EAAE,KAAK,gBAAgB,EAAE,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,GAAG,OAAO,KAAK,CAAC;AACtE;CACA,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;CACzB,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;CACzB,EAAE,KAAK,gBAAgB,EAAE,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,GAAG,OAAO,KAAK,CAAC;AACtE;CACA,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;CACzB,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;CACzB,EAAE,KAAK,gBAAgB,EAAE,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,GAAG,OAAO,KAAK,CAAC;AACtE;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC7B,GAAG,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC7B,GAAG,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;CACtC,GAAG,KAAK,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,GAAG,OAAO,KAAK,CAAC;AACpD;CACA,GAAG;AACH;CACA,EAAE,OAAO,IAAI,CAAC;AACd;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;CACA,WAAW,CAAC,SAAS,CAAC,kBAAkB,GAAG,EAAE,YAAY;AACzD;CACA,CAAC,MAAM,KAAK,GAAG,IAAI,gBAAgB,EAAE,CAAC;CACtC,CAAC,MAAM,SAAS,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC;CAClC,CAAC,MAAM,eAAe,GAAG,IAAI,oBAAoB,EAAE,CAAC;CACpD,CAAC,MAAM,gBAAgB,GAAG,IAAI,oBAAoB,EAAE,CAAC;CACrD,CAAC,MAAM,UAAU,GAAG,IAAIA,aAAO,EAAE,CAAC;CAClC,CAAC,OAAO,SAAS,kBAAkB,EAAE,QAAQ,GAAG;AAChD;CACA,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG;AAC1B;CACA,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;CACA,GAAG;AACH;CACA,EAAE,KAAK,EAAE,QAAQ,CAAC,kBAAkB,GAAG;AACvC;CACA,GAAG,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC;CAC1B,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;CAClB,GAAG,QAAQ,GAAG,KAAK,CAAC;AACpB;CACA,GAAG,MAAM,KAAK,QAAQ,CAAC,WAAW,GAAG;AACrC;CACA,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;AACrB;CACA,GAAG;AACH;CACA,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;CACnC,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;CACA,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;CAC9B,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;CAC9B,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;AAC9B;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC7B,GAAG,MAAM,EAAE,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC3B,GAAG,eAAe,CAAC,aAAa,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC;CAClD,GAAG,KAAK,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,GAAG,OAAO,KAAK,CAAC;AACzD;CACA,GAAG;AACH;CACA,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC;CAC1C,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC;CACtC,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;CAC7B,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,EAAE,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;CAChC,GAAG,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;CAC9B,GAAG,eAAe,CAAC,aAAa,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC;CAC/C,GAAG,KAAK,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,GAAG,OAAO,KAAK,CAAC;AACzD;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC5B,GAAG,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG;AACrC;CACA,IAAI,MAAM,GAAG,GAAG,UAAU,EAAE,EAAE,EAAE,CAAC;CACjC,IAAI,UAAU,CAAC,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACxC,IAAI,eAAe,CAAC,aAAa,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;CAC3D,IAAI,gBAAgB,CAAC,aAAa,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC;CACzD,IAAI,KAAK,eAAe,CAAC,WAAW,EAAE,gBAAgB,EAAE,GAAG,OAAO,KAAK,CAAC;AACxE;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,OAAO,IAAI,CAAC;AACd;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;CACA,WAAW,CAAC,SAAS,CAAC,mBAAmB,GAAG,EAAE,YAAY;AAC1D;CACA,CAAC,OAAO,SAAS,mBAAmB,EAAE,KAAK,EAAE,OAAO,GAAG;AACvD;CACA,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG;AAC1B;CACA,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;CACA,GAAG;AACH;CACA,EAAE,OAAO;CACT,IAAI,IAAI,EAAE,KAAK,EAAE;CACjB,IAAI,YAAY,EAAE,IAAI,CAAC,SAAS,EAAE;CAClC,IAAI,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE;CAC/B,IAAI,YAAY,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;AAChC;CACA,EAAE,OAAO,OAAO,CAAC;AACjB;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;CACA,WAAW,CAAC,SAAS,CAAC,eAAe,GAAG,EAAE,YAAY;AACtD;CACA,CAAC,MAAM,MAAM,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC9B,CAAC,OAAO,SAAS,eAAe,EAAE,KAAK,GAAG;AAC1C;CACA,EAAE,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;CAC5C,EAAE,OAAO,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;AACpC;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;CACA,WAAW,CAAC,SAAS,CAAC,aAAa,GAAG,EAAE,YAAY;AACpD;CACA,CAAC,MAAM,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACrC,CAAC,MAAM,SAAS,GAAG,IAAI,KAAK,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAIG,WAAK,EAAE,EAAE,CAAC;CACnE,CAAC,MAAM,SAAS,GAAG,IAAI,KAAK,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAIA,WAAK,EAAE,EAAE,CAAC;AACnE;CACA,CAAC,MAAM,MAAM,GAAG,IAAIH,aAAO,EAAE,CAAC;CAC9B,CAAC,MAAM,MAAM,GAAG,IAAIA,aAAO,EAAE,CAAC;AAC9B;CACA;CACA,CAAC,OAAO,SAAS,aAAa,EAAE,GAAG,EAAE,SAAS,GAAG,CAAC,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,GAAG;AACrF;CACA,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG;AAC1B;CACA,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;CACA,GAAG;AACH;CACA,EAAE,KAAK,IAAI,CAAC,aAAa,EAAE,GAAG,EAAE,GAAG;AACnC;CACA,GAAG,KAAK,OAAO,IAAI,OAAO,GAAG;AAC7B;CACA,IAAI,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC;CAC5B,IAAI,IAAI,CAAC,mBAAmB,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;CAC/C,IAAI,GAAG,CAAC,mBAAmB,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC9C;CACA,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;CAC1C,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC1C;CACA,IAAI;AACJ;CACA,GAAG,OAAO,CAAC,CAAC;AACZ;CACA,GAAG;AACH;CACA,EAAE,MAAM,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;CAC3C,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;CACtB,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;CACtB,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B;AACA;CACA;CACA,EAAE,IAAI,iBAAiB,GAAG,QAAQ,CAAC;AACnC;CACA;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CACzB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACtC;CACA,GAAG,MAAM,IAAI,GAAG,CAAC,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;CAC9C,GAAG,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACnC;CACA,IAAI,iBAAiB,GAAG,IAAI,CAAC;CAC7B,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;CACrC,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC1C;CACA,IAAI,KAAK,IAAI,GAAG,UAAU,GAAG,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AACtD;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC;CAChB,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,GAAG;AACtC;CACA,IAAI,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,GAAG;AACvC;CACA,KAAK,MAAM,SAAS,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CACrC,KAAK,MAAM,UAAU,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACtC;CACA;CACA,KAAK,MAAM,KAAK,GAAG,EAAE,IAAI,SAAS,GAAG,EAAE,IAAI,UAAU,CAAC;CACtD,KAAK,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,SAAS,GAAG,EAAE,IAAI,UAAU,CAAC;CAChE,KAAK,MAAM,EAAE,GAAG,MAAM,EAAE,KAAK,EAAE,CAAC;CAChC,KAAK,MAAM,EAAE,GAAG,MAAM,EAAE,MAAM,EAAE,CAAC;CACjC,KAAK,MAAM,KAAK,GAAG,SAAS,EAAE,KAAK,EAAE,CAAC;CACtC,KAAK,KAAK,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACzB;AACA;CACA;CACA,KAAK,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC/B,KAAK,MAAM,EAAE,GAAG,SAAS,EAAE,SAAS,EAAE,CAAC;CACvC,KAAK,MAAM,EAAE,GAAG,SAAS,EAAE,UAAU,EAAE,CAAC;CACxC,KAAK,MAAM,KAAK,GAAG,SAAS,EAAE,KAAK,EAAE,CAAC;CACtC,KAAK,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;CAC/B,KAAK,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;AAC3B;CACA,KAAK,KAAK,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;CAC7B,KAAK,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;CAC9C,KAAK,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;AAC9C;CACA,KAAK,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;CAC3B,KAAK,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;CAC5C,KAAK,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;AAC5C;CACA,KAAK,KAAK,GAAG,CAAC;AACd;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACnC;CACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACpC;CACA,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CAClC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CAClC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAClC;CACA,KAAK,IAAI,CAAC,mBAAmB,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;CAChD,KAAK,MAAM,IAAI,GAAG,MAAM,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;CACrD,KAAK,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACrC;CACA,MAAM,iBAAiB,GAAG,IAAI,CAAC;CAC/B,MAAM,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;CAC5C,MAAM,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC5C;CACA,MAAM,KAAK,IAAI,GAAG,UAAU,GAAG,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AACxD;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,GAAG,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC7B,GAAG,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG;AACtC;CACA,IAAI,MAAM,EAAE,GAAG,SAAS,EAAE,EAAE,EAAE,CAAC;CAC/B,IAAI,6BAA6B,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;CAC5D,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;CACpD,IAAI,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACpC;CACA,KAAK,iBAAiB,GAAG,IAAI,CAAC;CAC9B,KAAK,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;CAC3C,KAAK,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC3C;CACA,KAAK,KAAK,IAAI,GAAG,UAAU,GAAG,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AACvD;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,OAAO,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC;AACxC;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI;;CCpaE,MAAM,aAAa,CAAC;AAC3B;CACA,CAAC,WAAW,EAAE,eAAe,GAAG;AAChC;CACA,EAAE,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;CAC1C,EAAE,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACxB;CACA,EAAE;AACF;CACA,CAAC,YAAY,GAAG;AAChB;CACA,EAAE,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;CACtC,EAAE,KAAK,UAAU,CAAC,MAAM,KAAK,CAAC,GAAG;AACjC;CACA,GAAG,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAClC;CACA,GAAG,MAAM;AACT;CACA,GAAG,OAAO,UAAU,CAAC,GAAG,EAAE,CAAC;AAC3B;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,gBAAgB,EAAE,SAAS,GAAG;AAC/B;CACA,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC;AACrC;CACA,EAAE;AACF;CACA;;CC3BA,MAAM,wBAAwB,SAAS,aAAa,CAAC;AACrD;CACA,CAAC,WAAW,GAAG;AACf;CACA,EAAE,KAAK,EAAE,MAAM,IAAI,gBAAgB,EAAE,EAAE,CAAC;AACxC;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,MAAM,oBAAoB,mBAAmB,IAAI,wBAAwB,EAAE;;CCblF,MAAM,YAAY,CAAC;AACnB;CACA,CAAC,WAAW,GAAG;AACf;CACA,EAAE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;CAC3B,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;CAC1B,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B;CACA,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;CACnB,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC;CACxB,EAAE,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI;AAC7B;CACA,GAAG,KAAK,UAAU,GAAG;AACrB;CACA,IAAI,KAAK,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC;AAC7B;CACA,IAAI;AACJ;CACA,GAAG,UAAU,GAAG,MAAM,CAAC;CACvB,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,MAAM,EAAE,CAAC;CAClD,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;CAChD,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;AAChD;CACA,GAAG,CAAC;AACJ;CACA,EAAE,IAAI,CAAC,WAAW,GAAG,MAAM;AAC3B;CACA,GAAG,UAAU,GAAG,IAAI,CAAC;CACrB,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;CAC5B,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;CAC3B,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC3B;CACA,GAAG,KAAK,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG;AAC7B;CACA,IAAI,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC;AAClC;CACA,IAAI;AACJ;CACA,GAAG,CAAC;AACJ;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,MAAM,WAAW,GAAG,IAAI,YAAY,EAAE;;CCrC7C,IAAIO,OAAK,EAAEC,OAAK,CAAC;CACjB,MAAM,QAAQ,GAAG,EAAE,CAAC;CACpB,MAAM,OAAO,mBAAmB,IAAI,aAAa,EAAE,MAAM,IAAIC,UAAI,EAAE,EAAE,CAAC;AACtE;CACO,SAAS,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,EAAE,UAAU,GAAG;AAC3G;CACA;CACA,CAACF,OAAK,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;CAChC,CAACC,OAAK,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;CAChC,CAAC,QAAQ,CAAC,IAAI,EAAED,OAAK,EAAEC,OAAK,EAAE,CAAC;CAC/B,CAAC,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;AAC7C;CACA,CAAC,MAAM,MAAM,GAAG,iBAAiB,EAAE,CAAC,EAAE,GAAG,CAAC,QAAQ,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,EAAE,UAAU,EAAE,CAAC;AACzH;CACA;CACA,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;CAC3B,CAAC,OAAO,CAAC,gBAAgB,EAAED,OAAK,EAAE,CAAC;CACnC,CAAC,OAAO,CAAC,gBAAgB,EAAEC,OAAK,EAAE,CAAC;CACnC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;CAChB,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;AAChB;CACA,CAAC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;CAChC,CAAC,KAAK,MAAM,GAAG,CAAC,GAAG;AACnB;CACA,EAAEA,OAAK,GAAG,QAAQ,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;CACjC,EAAED,OAAK,GAAG,QAAQ,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;AACjC;CACA,EAAE;AACF;CACA,CAAC,OAAO,MAAM,CAAC;AACf;CACA,CAAC;AACD;CACA,SAAS,iBAAiB;CAC1B,CAAC,WAAW;CACZ,CAAC,QAAQ;CACT,CAAC,oBAAoB;CACrB,CAAC,mBAAmB;CACpB,CAAC,aAAa,GAAG,IAAI;CACrB,CAAC,mBAAmB,GAAG,CAAC;CACxB,CAAC,KAAK,GAAG,CAAC;CACV,EAAE;AACF;CACA,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC;CAChE,CAAC,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;AACnC;CACA,CAAC,MAAM,MAAM,GAAG,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,CAAC,KAAK,MAAM,GAAG;AACf;CACA,EAAE,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,EAAE,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CAClD,EAAE,UAAU,EAAE,mBAAmB,EAAE,WAAW,EAAE,EAAE,YAAY,EAAEA,OAAK,EAAE,CAAC;CACxE,EAAE,OAAO,mBAAmB,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,GAAG,WAAW,EAAEA,OAAK,EAAE,CAAC;AACtG;CACA,EAAE,MAAM;AACR;CACA,EAAE,MAAM,IAAI,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;CACxC,EAAE,MAAM,KAAK,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACvD,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;CAChB,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;AACjB;CACA,EAAE,IAAI,MAAM,EAAE,MAAM,CAAC;CACrB,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC;CACjB,EAAE,KAAK,aAAa,GAAG;AACvB;CACA,GAAG,IAAI,GAAGA,OAAK,CAAC;CAChB,GAAG,IAAI,GAAGC,OAAK,CAAC;AAChB;CACA;CACA,GAAG,UAAU,EAAE,mBAAmB,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;CAC/D,GAAG,UAAU,EAAE,mBAAmB,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;AAC/D;CACA,GAAG,MAAM,GAAG,aAAa,EAAE,IAAI,EAAE,CAAC;CAClC,GAAG,MAAM,GAAG,aAAa,EAAE,IAAI,EAAE,CAAC;AAClC;CACA,GAAG,KAAK,MAAM,GAAG,MAAM,GAAG;AAC1B;CACA,IAAI,EAAE,GAAG,KAAK,CAAC;CACf,IAAI,EAAE,GAAG,IAAI,CAAC;AACd;CACA,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC;CACxB,IAAI,MAAM,GAAG,MAAM,CAAC;CACpB,IAAI,MAAM,GAAG,IAAI,CAAC;AAClB;CACA,IAAI,IAAI,GAAG,IAAI,CAAC;CAChB;AACA;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA,EAAE,KAAK,EAAE,IAAI,GAAG;AAChB;CACA,GAAG,IAAI,GAAGD,OAAK,CAAC;CAChB,GAAG,UAAU,EAAE,mBAAmB,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;AAC/D;CACA,GAAG;AACH;CACA,EAAE,MAAM,QAAQ,GAAG,OAAO,EAAE,EAAE,GAAG,CAAC,EAAE,WAAW,EAAE,CAAC;CAClD,EAAE,MAAM,cAAc,GAAG,oBAAoB,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,mBAAmB,GAAG,EAAE,EAAE,CAAC;AAC7G;CACA,EAAE,IAAI,eAAe,CAAC;CACtB,EAAE,KAAK,cAAc,KAAK,SAAS,GAAG;AACtC;CACA,GAAG,MAAM,MAAM,GAAG,aAAa,EAAE,EAAE,EAAE,CAAC;CACtC,GAAG,MAAM,GAAG,GAAG,iBAAiB,EAAE,EAAE,EAAE,CAAC;CACvC,GAAG,MAAM,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC;AAC9B;CACA,GAAG,eAAe,GAAG,mBAAmB,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,mBAAmB,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC;AAC3G;CACA,GAAG,MAAM;AACT;CACA,GAAG,eAAe;CAClB,IAAI,cAAc;CAClB,IAAI,iBAAiB;CACrB,KAAK,EAAE;CACP,KAAK,QAAQ;CACb,KAAK,oBAAoB;CACzB,KAAK,mBAAmB;CACxB,KAAK,aAAa;CAClB,KAAK,mBAAmB;CACxB,KAAK,KAAK,GAAG,CAAC;CACd,KAAK,CAAC;AACN;CACA,GAAG;AACH;CACA,EAAE,KAAK,eAAe,GAAG,OAAO,IAAI,CAAC;AACrC;CACA;CACA;CACA,EAAE,IAAI,GAAGC,OAAK,CAAC;CACf,EAAE,UAAU,EAAE,mBAAmB,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;AAC9D;CACA,EAAE,MAAM,QAAQ,GAAG,OAAO,EAAE,EAAE,GAAG,CAAC,EAAE,WAAW,EAAE,CAAC;CAClD,EAAE,MAAM,cAAc,GAAG,oBAAoB,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,mBAAmB,GAAG,EAAE,EAAE,CAAC;AAC7G;CACA,EAAE,IAAI,eAAe,CAAC;CACtB,EAAE,KAAK,cAAc,KAAK,SAAS,GAAG;AACtC;CACA,GAAG,MAAM,MAAM,GAAG,aAAa,EAAE,EAAE,EAAE,CAAC;CACtC,GAAG,MAAM,GAAG,GAAG,iBAAiB,EAAE,EAAE,EAAE,CAAC;CACvC,GAAG,MAAM,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC;AAC9B;CACA,GAAG,eAAe,GAAG,mBAAmB,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,mBAAmB,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC;AAC3G;CACA,GAAG,MAAM;AACT;CACA,GAAG,eAAe;CAClB,IAAI,cAAc;CAClB,IAAI,iBAAiB;CACrB,KAAK,EAAE;CACP,KAAK,QAAQ;CACb,KAAK,oBAAoB;CACzB,KAAK,mBAAmB;CACxB,KAAK,aAAa;CAClB,KAAK,mBAAmB;CACxB,KAAK,KAAK,GAAG,CAAC;CACd,KAAK,CAAC;AACN;CACA,GAAG;AACH;CACA,EAAE,KAAK,eAAe,GAAG,OAAO,IAAI,CAAC;AACrC;CACA,EAAE,OAAO,KAAK,CAAC;AACf;CACA;CACA;CACA,EAAE,SAAS,aAAa,EAAE,WAAW,GAAG;AACxC;CACA,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC;CACpD,GAAG,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;AACrC;CACA;CACA,GAAG,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;AACnD;CACA,IAAI,WAAW,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;CAC3C,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;AAClC;CACA,IAAI;AACJ;CACA,GAAG,OAAO,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAC7C;CACA,GAAG;AACH;CACA,EAAE,SAAS,iBAAiB,EAAE,WAAW,GAAG;AAC5C;CACA,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC;CACpD,GAAG,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;AACrC;CACA;CACA,GAAG,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;AACnD;CACA;CACA,IAAI,WAAW,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACzD,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;AAClC;CACA,IAAI;AACJ;CACA;CACA,GAAG,OAAO,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACjF;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;;CCnNA,MAAM,IAAI,mBAAmB,IAAIR,aAAO,EAAE,CAAC;CAC3C,MAAMU,OAAK,mBAAmB,IAAIV,aAAO,EAAE,CAAC;AAC5C;CACO,SAAS,mBAAmB;CACnC,CAAC,GAAG;CACJ,CAAC,KAAK;CACN,CAAC,MAAM,GAAG,GAAG;CACb,CAAC,YAAY,GAAG,CAAC;CACjB,CAAC,YAAY,GAAG,QAAQ;CACxB,EAAE;AACF;CACA;CACA;CACA;CACA;CACA,CAAC,MAAM,cAAc,GAAG,YAAY,GAAG,YAAY,CAAC;CACpD,CAAC,MAAM,cAAc,GAAG,YAAY,GAAG,YAAY,CAAC;CACpD,CAAC,IAAI,iBAAiB,GAAG,QAAQ,CAAC;CAClC,CAAC,IAAI,uBAAuB,GAAG,IAAI,CAAC;CACpC,CAAC,GAAG,CAAC,SAAS;AACd;CACA,EAAE;AACF;CACA,GAAG,mBAAmB,EAAE,GAAG,IAAI;AAC/B;CACA,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;CACjD,IAAI,OAAO,IAAI,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC;AAC3C;CACA,IAAI;AACJ;CACA,GAAG,gBAAgB,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,MAAM;AAC/C;CACA,IAAI,OAAO,KAAK,GAAG,iBAAiB,IAAI,KAAK,GAAG,cAAc,CAAC;AAC/D;CACA,IAAI;AACJ;CACA,GAAG,kBAAkB,EAAE,EAAE,GAAG,EAAE,QAAQ,MAAM;AAC5C;CACA,IAAI,GAAG,CAAC,mBAAmB,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;CAC3C,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC;CACnD,IAAI,KAAK,MAAM,GAAG,iBAAiB,GAAG;AACtC;CACA,KAAKU,OAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;CACxB,KAAK,iBAAiB,GAAG,MAAM,CAAC;CAChC,KAAK,uBAAuB,GAAG,QAAQ,CAAC;AACxC;CACA,KAAK;AACL;CACA,IAAI,KAAK,MAAM,GAAG,cAAc,GAAG;AACnC;CACA,KAAK,OAAO,IAAI,CAAC;AACjB;CACA,KAAK,MAAM;AACX;CACA,KAAK,OAAO,KAAK,CAAC;AAClB;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,CAAC;AACH;CACA,CAAC,KAAK,iBAAiB,KAAK,QAAQ,GAAG,OAAO,IAAI,CAAC;AACnD;CACA,CAAC,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC;AACxD;CACA,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,GAAGA,OAAK,CAAC,KAAK,EAAE,CAAC;CACpD,MAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAEA,OAAK,EAAE,CAAC;CACjC,CAAC,MAAM,CAAC,QAAQ,GAAG,eAAe;CAClC,CAAC,MAAM,CAAC,SAAS,GAAG,uBAAuB,CAAC;AAC5C;CACA,CAAC,OAAO,MAAM,CAAC;AACf;CACA;;CC3EA;CACA;CACA,MAAM,GAAG,mBAAmB,IAAIV,aAAO,EAAE,CAAC;CAC1C,MAAM,GAAG,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CAC1C,MAAM,GAAG,mBAAmB,IAAIA,aAAO,EAAE,CAAC;AAC1C;CACA,MAAM,IAAI,mBAAmB,IAAIC,aAAO,EAAE,CAAC;CAC3C,MAAM,IAAI,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CAC3C,MAAM,IAAI,mBAAmB,IAAIA,aAAO,EAAE,CAAC;AAC3C;CACA,MAAM,QAAQ,mBAAmB,IAAID,aAAO,EAAE,CAAC;CAC/C,MAAM,QAAQ,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CAC/C,MAAM,QAAQ,mBAAmB,IAAIA,aAAO,EAAE,CAAC;AAC/C;CACA,MAAM,kBAAkB,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CACzD,SAAS,iBAAiB,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,GAAG;AACtE;CACA,CAAC,IAAI,SAAS,CAAC;CACf,CAAC,KAAK,IAAI,KAAKW,cAAQ,GAAG;AAC1B;CACA,EAAE,SAAS,GAAG,GAAG,CAAC,iBAAiB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AAC/D;CACA,EAAE,MAAM;AACR;CACA,EAAE,SAAS,GAAG,GAAG,CAAC,iBAAiB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,KAAKC,gBAAU,EAAE,KAAK,EAAE,CAAC;AAC9E;CACA,EAAE;AACF;CACA,CAAC,KAAK,SAAS,KAAK,IAAI,GAAG,OAAO,IAAI,CAAC;AACvC;CACA,CAAC,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC;AACjD;CACA,CAAC,KAAK,QAAQ,GAAG,IAAI,IAAI,QAAQ,GAAG,GAAG,GAAG,OAAO,IAAI,CAAC;AACtD;CACA,CAAC,OAAO;AACR;CACA,EAAE,QAAQ,EAAE,QAAQ;CACpB,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE;AACtB;CACA,EAAE,CAAC;AACH;CACA,CAAC;AACD;CACA,SAAS,+BAA+B,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,GAAG;AACrG;CACA,CAAC,GAAG,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;CACxC,CAAC,GAAG,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;CACxC,CAAC,GAAG,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;AACxC;CACA,CAAC,MAAM,YAAY,GAAG,iBAAiB,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,kBAAkB,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AACnG;CACA,CAAC,KAAK,YAAY,GAAG;AACrB;CACA,EAAE,KAAK,EAAE,GAAG;AACZ;CACA,GAAG,IAAI,CAAC,mBAAmB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;CACrC,GAAG,IAAI,CAAC,mBAAmB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;CACrC,GAAG,IAAI,CAAC,mBAAmB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;AACrC;CACA,GAAG,YAAY,CAAC,EAAE,GAAGR,cAAQ,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAIH,aAAO,EAAE,EAAE,CAAC;AACrH;CACA,GAAG;AACH;CACA,EAAE,KAAK,GAAG,GAAG;AACb;CACA,GAAG,IAAI,CAAC,mBAAmB,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;CACtC,GAAG,IAAI,CAAC,mBAAmB,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;CACtC,GAAG,IAAI,CAAC,mBAAmB,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACtC;CACA,GAAG,YAAY,CAAC,GAAG,GAAGG,cAAQ,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAIH,aAAO,EAAE,EAAE,CAAC;AACtH;CACA,GAAG;AACH;CACA,EAAE,KAAK,MAAM,GAAG;AAChB;CACA,GAAG,QAAQ,CAAC,mBAAmB,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;CAC7C,GAAG,QAAQ,CAAC,mBAAmB,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;CAC7C,GAAG,QAAQ,CAAC,mBAAmB,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;AAC7C;CACA,GAAG,YAAY,CAAC,MAAM,GAAGG,cAAQ,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAIJ,aAAO,EAAE,EAAE,CAAC;CACrI,GAAG,KAAK,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,GAAG;AACvD;CACA,IAAI,YAAY,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,CAAC,EAAE,CAAC;AAC9C;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,MAAM,IAAI,GAAG;CACf,GAAG,CAAC,EAAE,CAAC;CACP,GAAG,CAAC,EAAE,CAAC;CACP,GAAG,CAAC,EAAE,CAAC;CACP,GAAG,MAAM,EAAE,IAAIA,aAAO,EAAE;CACxB,GAAG,aAAa,EAAE,CAAC;CACnB,GAAG,CAAC;AACJ;CACA,EAAEI,cAAQ,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;AACnD;CACA,EAAE,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC;CAC3B,EAAE,YAAY,CAAC,SAAS,GAAG,CAAC,CAAC;AAC7B;CACA,EAAE;AACF;CACA,CAAC,OAAO,YAAY,CAAC;AACrB;CACA,CAAC;AACD;CACA;CACA,SAAS,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,GAAG;AACvE;CACA,CAAC,MAAM,SAAS,GAAG,GAAG,GAAG,CAAC,CAAC;CAC3B,CAAC,IAAI,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC;CACvB,CAAC,IAAI,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC;CACvB,CAAC,IAAI,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC;AACvB;CACA,CAAC,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;CACzB,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG;AAClB;CACA,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;CACtB,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;CACtB,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AACtB;CACA,EAAE;AACF;CACA,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC;CACtD,CAAC,MAAM,YAAY,GAAG,+BAA+B,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AAClH;CACA,CAAC,KAAK,YAAY,GAAG;AACrB;CACA,EAAE,YAAY,CAAC,SAAS,GAAG,GAAG,CAAC;CAC/B,EAAE,KAAK,aAAa,GAAG,aAAa,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC;CAC1D,EAAE,OAAO,YAAY,CAAC;AACtB;CACA,EAAE;AACF;CACA,CAAC,OAAO,IAAI,CAAC;AACb;CACA;;CCxIA;CACO,SAAS,WAAW,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,GAAG;AAClD;CACA,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;CAClB,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;CAClB,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAClB;CACA,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;CACZ,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;CAChB,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;CAChB,CAAC,KAAK,KAAK,GAAG;AACd;CACA,EAAE,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CACxB,EAAE,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CACxB,EAAE,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AACxB;CACA,EAAE;AACF;CACA,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB;CACA,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB;CACA,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB;CACA,CAAC;AACD;CACA,MAAM,MAAM,mBAAmB,IAAIJ,aAAO,EAAE,CAAC;CAC7C,MAAM,MAAM,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CAC7C,MAAM,MAAM,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CAC7C,MAAM,OAAO,mBAAmB,IAAIC,aAAO,EAAE,CAAC;CAC9C,MAAM,OAAO,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CAC9C,MAAM,OAAO,mBAAmB,IAAIA,aAAO,EAAE,CAAC;AAC9C;CACO,SAAS,uBAAuB,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,GAAG;AAClF;CACA,CAAC,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC;CAC3C,CAAC,MAAM,SAAS,GAAG,QAAQ,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;CACvD,CAAC,MAAM,GAAG,GAAG,QAAQ,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC;AAC3C;CACA,CAAC,MAAM,CAAC,GAAG,OAAO,EAAE,aAAa,GAAG,CAAC,EAAE,CAAC;CACxC,CAAC,MAAM,CAAC,GAAG,OAAO,EAAE,aAAa,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CAC5C,CAAC,MAAM,CAAC,GAAG,OAAO,EAAE,aAAa,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AAC5C;CACA,CAAC,MAAM,CAAC,mBAAmB,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;CAC5C,CAAC,MAAM,CAAC,mBAAmB,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;CAC5C,CAAC,MAAM,CAAC,mBAAmB,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;AAC5C;CACA;CACA,CAAC,IAAI,aAAa,GAAG,CAAC,CAAC;CACvB,CAAC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;CAChC,CAAC,MAAM,gBAAgB,GAAG,aAAa,GAAG,CAAC,CAAC;CAC5C,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;CACA,EAAE,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CAC5B,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;CACjC,EAAE,KAAK,gBAAgB,IAAI,KAAK,IAAI,gBAAgB,GAAG,KAAK,GAAG,KAAK,GAAG;AACvE;CACA,GAAG,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;CACvC,GAAG,MAAM;AACT;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;CACA,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC;CACf,CAAC,KAAK,GAAG,GAAG;AACZ;CACA,EAAE,OAAO,CAAC,mBAAmB,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;CACxC,EAAE,OAAO,CAAC,mBAAmB,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;CACxC,EAAE,OAAO,CAAC,mBAAmB,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACxC;CACA,EAAE,KAAK,MAAM,IAAI,MAAM,CAAC,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;CAC5C,OAAO,EAAE,GAAG,IAAIA,aAAO,EAAE,CAAC;AAC1B;CACA,EAAEG,cAAQ,CAAC,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;AAC5F;CACA,EAAE;AACF;CACA;CACA,CAAC,KAAK,MAAM,GAAG;AACf;CACA,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC;CACzC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;CACpB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;CACpB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;CACpB,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;CAC5C,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,IAAIJ,aAAO,EAAE,CAAC;CACjE,EAAEI,cAAQ,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AACnE;CACA,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC;AAC3B;CACA,EAAE,OAAO,MAAM,CAAC;AAChB;CACA,EAAE,MAAM;AACR;CACA,EAAE,OAAO;CACT,GAAG,IAAI,EAAE;CACT,IAAI,CAAC,EAAE,CAAC;CACR,IAAI,CAAC,EAAE,CAAC;CACR,IAAI,CAAC,EAAE,CAAC;CACR,IAAI,aAAa,EAAE,aAAa;CAChC,IAAI,MAAM,EAAEA,cAAQ,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAIJ,aAAO,EAAE,EAAE;CACvE,IAAI;CACJ,GAAG,EAAE,EAAE,EAAE;CACT,GAAG,CAAC;AACJ;CACA,EAAE;AACF;CACA;;CCnHA;CACA;CACA;CACA;AACA;CACA,SAAS,aAAa,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,GAAG;AAClF;CACA,CAAC,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,GAAG,CAAC;CAC3C,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG;AAC7D;AACA;CACA,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AACnE;AACA;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,SAAS,mBAAmB,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,GAAG;AACzE;CACA,CAAC,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,GAAG,CAAC;CAC3C,CAAC,IAAI,IAAI,GAAG,QAAQ,CAAC;CACrB,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC;CAChB,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG;AAC7D;CACA,EAAE,IAAI,YAAY,CAAC;AACnB;CACA,EAAE,YAAY,GAAG,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AACzE;AACA;CACA,EAAE,KAAK,YAAY,IAAI,YAAY,CAAC,QAAQ,GAAG,IAAI,GAAG;AACtD;CACA,GAAG,GAAG,GAAG,YAAY,CAAC;CACtB,GAAG,IAAI,GAAG,YAAY,CAAC,QAAQ,CAAC;AAChC;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,GAAG,CAAC;AACZ;CACA,CAAC;AACD;CACA,SAAS,oBAAoB;CAC7B,CAAC,MAAM;CACP,CAAC,KAAK;CACN,CAAC,GAAG;CACJ,CAAC,sBAAsB;CACvB,CAAC,SAAS;CACV,CAAC,KAAK;CACN,CAAC,QAAQ;CACT,EAAE;AACF;CACA,CAAC,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;CAC1B,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,QAAQ,CAAC;CAC5B,CAAC,MAAM,GAAG,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;CAC1C,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,KAAK,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACzD;CACA,EAAE,IAAI,GAAG,CAAC;AACV;CACA,EAAE,GAAG,GAAG,CAAC,CAAC;AACV;CACA,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;CAC/C,EAAE,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AAC9B;CACA,EAAE,KAAK,sBAAsB,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;AACnE;CACA,GAAG,OAAO,IAAI,CAAC;AACf;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,KAAK,CAAC;AACd;CACA;;CC5EA;CACA;CACA;AACA;CACA,SAAS,KAAK,EAAE,GAAG,EAAE,WAAW,GAAG,IAAI,GAAG;AAC1C;CACA,CAAC,KAAK,WAAW,IAAI,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,GAAG;AACpD;CACA,EAAE,WAAW,GAAG,IAAI,GAAG,EAAE,WAAW,EAAE,CAAC;AACvC;CACA,EAAE;AACF;CACA,CAAC,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;CAC/B,CAAC,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;CAC/D,CAAC,MAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;AAC9C;CACA,CAAC,IAAI,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;CACpD,CAAC,IAAI,UAAU,GAAG,CAAC,CAAC;CACpB,CAAC,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC;CAC1B,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClD;CACA,EAAE,MAAM,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC;CACtB,EAAE,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;CAC1C,EAAE,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;CAC1C,EAAE,YAAY,GAAG,IAAI,YAAY,EAAE,MAAM,EAAE,CAAC;AAC5C;CACA,EAAE,SAAS,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC;CAC7B,EAAE,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC;AAClC;CACA,EAAE;AACF;CACA,CAAC,SAAS,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,GAAG,KAAK,GAAG;AAC9D;CACA,EAAE,MAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;CACtC,EAAE,MAAM,MAAM,GAAG,WAAW,EAAE,WAAW,GAAG,EAAE,EAAE,KAAK,gBAAgB,CAAC;CACtE,EAAE,KAAK,MAAM,GAAG;AAChB;CACA,GAAG,MAAM,MAAM,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;CACjD,GAAG,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,EAAE,EAAE,CAAC;AACjD;CACA,GAAG,IAAI,IAAI,GAAG,QAAQ,CAAC;CACvB,GAAG,IAAI,IAAI,GAAG,QAAQ,CAAC;CACvB,GAAG,IAAI,IAAI,GAAG,QAAQ,CAAC;CACvB,GAAG,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;CACzB,GAAG,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;CACzB,GAAG,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;AACzB;AACA;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,KAAK,MAAM,GAAG,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACvE;CACA,IAAI,IAAI,KAAK,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;CAC9B,IAAI,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;CACpC,IAAI,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;CACpC,IAAI,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACpC;CACA,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;CAC7B,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AAC7B;CACA,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;CAC7B,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AAC7B;CACA,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;CAC7B,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AAC7B;CACA,IAAI;AACJ;AACA;CACA,GAAG;CACH,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;CAC5C,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;CAC5C,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;AAC5C;CACA,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;CAC5C,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;CAC5C,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;CAC5C,KAAK;AACL;CACA,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;CAC3C,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;CAC3C,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC3C;CACA,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;CAC3C,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;CAC3C,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC3C;CACA,IAAI,OAAO,IAAI,CAAC;AAChB;CACA,IAAI,MAAM;AACV;CACA,IAAI,OAAO,KAAK,CAAC;AACjB;CACA,IAAI;AACJ;CACA,GAAG,MAAM;AACT;CACA,GAAG,MAAM,IAAI,GAAG,WAAW,GAAG,CAAC,CAAC;CAChC,GAAG,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AAChD;CACA;CACA;CACA,GAAG,MAAM,UAAU,GAAG,IAAI,GAAG,UAAU,CAAC;CACxC,GAAG,MAAM,WAAW,GAAG,KAAK,GAAG,UAAU,CAAC;CAC1C,GAAG,IAAI,aAAa,GAAG,KAAK,CAAC;CAC7B,GAAG,IAAI,YAAY,GAAG,KAAK,CAAC;CAC5B,GAAG,IAAI,aAAa,GAAG,KAAK,CAAC;AAC7B;CACA,GAAG,KAAK,WAAW,GAAG;AACtB;CACA;CACA;CACA,IAAI,KAAK,EAAE,aAAa,GAAG;AAC3B;CACA,KAAK,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE,UAAU,EAAE,CAAC;CAClD,KAAK,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,WAAW,EAAE,CAAC;CACpD,KAAK,aAAa,GAAG,EAAE,YAAY,IAAI,EAAE,aAAa,CAAC;AACvD;CACA,KAAK;AACL;CACA,IAAI,MAAM;AACV;CACA,IAAI,YAAY,GAAG,IAAI,CAAC;CACxB,IAAI,aAAa,GAAG,IAAI,CAAC;AACzB;CACA,IAAI;AACJ;CACA,GAAG,MAAM,YAAY,GAAG,aAAa,IAAI,YAAY,CAAC;CACtD,GAAG,MAAM,aAAa,GAAG,aAAa,IAAI,aAAa,CAAC;AACxD;CACA,GAAG,IAAI,UAAU,GAAG,KAAK,CAAC;CAC1B,GAAG,KAAK,YAAY,GAAG;AACvB;CACA,IAAI,UAAU,GAAG,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;AAC9D;CACA,IAAI;AACJ;CACA,GAAG,IAAI,WAAW,GAAG,KAAK,CAAC;CAC3B,GAAG,KAAK,aAAa,GAAG;AACxB;CACA,IAAI,WAAW,GAAG,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;AAChE;CACA,IAAI;AACJ;CACA,GAAG,MAAM,SAAS,GAAG,UAAU,IAAI,WAAW,CAAC;CAC/C,GAAG,KAAK,SAAS,GAAG;AACpB;CACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnC;CACA,KAAK,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;CAC5B,KAAK,MAAM,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;CAC9B,KAAK,MAAM,YAAY,GAAG,YAAY,EAAE,KAAK,EAAE,CAAC;CAChD,KAAK,MAAM,YAAY,GAAG,YAAY,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;CACpD,KAAK,MAAM,aAAa,GAAG,YAAY,EAAE,MAAM,EAAE,CAAC;CAClD,KAAK,MAAM,aAAa,GAAG,YAAY,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;AACtD;CACA,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,YAAY,GAAG,aAAa,GAAG,YAAY,GAAG,aAAa,CAAC;CACnG,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,GAAG,aAAa,GAAG,YAAY,GAAG,aAAa,CAAC;AACvG;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG,OAAO,SAAS,CAAC;AACpB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;;CCzKA;CACA;CACA;CACA;CACO,SAAS,YAAY,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG;AACnE;CACA,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AAC5C;CACA,CAAC,MAAM,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC;CACpC,EAAE,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC;CAC/B,EAAE,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;AAChC;CACA,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;CACzB,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;CACzB,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;AACzB;CACA,CAAC,IAAI,IAAI,GAAG,KAAK,EAAE,WAAW,EAAE,CAAC;CACjC,CAAC,IAAI,IAAI,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AACrC;CACA,CAAC,IAAI,IAAI,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;CACrC,CAAC,IAAI,IAAI,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACzC;CACA,CAAC,IAAI,IAAI,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;CACrC,CAAC,IAAI,IAAI,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACzC;CACA,CAAC,KAAK,OAAO,IAAI,CAAC,GAAG;AACrB;CACA,EAAE,IAAI,GAAG,EAAE,IAAI,GAAG,EAAE,KAAK,OAAO,CAAC;CACjC,EAAE,IAAI,GAAG,EAAE,IAAI,GAAG,EAAE,KAAK,OAAO,CAAC;AACjC;CACA,EAAE,MAAM;AACR;CACA,EAAE,IAAI,GAAG,EAAE,IAAI,GAAG,EAAE,KAAK,OAAO,CAAC;CACjC,EAAE,IAAI,GAAG,EAAE,IAAI,GAAG,EAAE,KAAK,OAAO,CAAC;AACjC;CACA,EAAE;AACF;CACA,CAAC,KAAK,OAAO,IAAI,CAAC,GAAG;AACrB;CACA,EAAE,KAAK,GAAG,EAAE,IAAI,GAAG,EAAE,KAAK,OAAO,CAAC;CAClC,EAAE,KAAK,GAAG,EAAE,IAAI,GAAG,EAAE,KAAK,OAAO,CAAC;AAClC;CACA,EAAE,MAAM;AACR;CACA,EAAE,KAAK,GAAG,EAAE,IAAI,GAAG,EAAE,KAAK,OAAO,CAAC;CAClC,EAAE,KAAK,GAAG,EAAE,IAAI,GAAG,EAAE,KAAK,OAAO,CAAC;AAClC;CACA,EAAE;AACF;CACA,CAAC,KAAK,EAAE,IAAI,GAAG,KAAK,QAAQ,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,KAAK,CAAC;AAC1D;CACA,CAAC,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,KAAK,CAAC;AACnD;CACA,CAAC,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,KAAK,CAAC;AACnD;CACA,CAAC,KAAK,OAAO,IAAI,CAAC,GAAG;AACrB;CACA,EAAE,KAAK,GAAG,EAAE,IAAI,GAAG,EAAE,KAAK,OAAO,CAAC;CAClC,EAAE,KAAK,GAAG,EAAE,IAAI,GAAG,EAAE,KAAK,OAAO,CAAC;AAClC;CACA,EAAE,MAAM;AACR;CACA,EAAE,KAAK,GAAG,EAAE,IAAI,GAAG,EAAE,KAAK,OAAO,CAAC;CAClC,EAAE,KAAK,GAAG,EAAE,IAAI,GAAG,EAAE,KAAK,OAAO,CAAC;AAClC;CACA,EAAE;AACF;CACA,CAAC,KAAK,EAAE,IAAI,GAAG,KAAK,QAAQ,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,KAAK,CAAC;AAC1D;CACA,CAAC,KAAK,KAAK,GAAG,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC;AACnD;CACA,CAAC,KAAK,KAAK,GAAG,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC;AACnD;CACA;AACA;CACA,CAAC,OAAO,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC;AACpC;CACA;;CC1EA;CACA;CACA;CACA;AACA;CACA,SAAS,sBAAsB,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,GAAG;AAC3F;CACA,CAAC,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,GAAG,CAAC;CAC3C,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG;AAC7D;CACA,EAAE,IAAI,EAAE,GAAG,eAAe,GAAG,eAAe,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;CACtD,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AACpE;AACA;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,SAAS,4BAA4B,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,GAAG;AAClF;CACA,CAAC,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,GAAG,CAAC;CAC3C,CAAC,IAAI,IAAI,GAAG,QAAQ,CAAC;CACrB,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC;CAChB,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG;AAC7D;CACA,EAAE,IAAI,YAAY,CAAC;CACnB,EAAE,YAAY,GAAG,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,eAAe,GAAG,eAAe,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AAClH;AACA;CACA,EAAE,KAAK,YAAY,IAAI,YAAY,CAAC,QAAQ,GAAG,IAAI,GAAG;AACtD;CACA,GAAG,GAAG,GAAG,YAAY,CAAC;CACtB,GAAG,IAAI,GAAG,YAAY,CAAC,QAAQ,CAAC;AAChC;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,GAAG,CAAC;AACZ;CACA,CAAC;AACD;CACA,SAAS,6BAA6B;CACtC,CAAC,MAAM;CACP,CAAC,KAAK;CACN,CAAC,GAAG;CACJ,CAAC,sBAAsB;CACvB,CAAC,SAAS;CACV,CAAC,KAAK;CACN,CAAC,QAAQ;CACT,EAAE;AACF;CACA,CAAC,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;CAC1B,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,QAAQ,CAAC;CAC5B,CAAC,MAAM,GAAG,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;CAC1C,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,KAAK,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACzD;CACA,EAAE,IAAI,GAAG,CAAC;CACV,EAAE,GAAG,GAAG,GAAG,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC;AACtC;CACA,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;CAC/C,EAAE,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AAC9B;CACA,EAAE,KAAK,sBAAsB,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;AACnE;CACA,GAAG,OAAO,IAAI,CAAC;AACf;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,KAAK,CAAC;AACd;CACA;;CCtEA;CACA;CACA;AACA;CACA,SAAS,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,GAAG;AAChE;CACA,CAAC,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;CAC7C,CAACa,UAAQ,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;CACtD,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;AAC3B;CACA,CAAC;AACD;CACA,SAASA,UAAQ,EAAE,WAAW,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,GAAG;AACxE;CACA,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC;CAChE,CAAC,MAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;CACrC,CAAC,MAAM,MAAM,GAAG,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,CAAC,KAAK,MAAM,GAAG;AACf;CACA,EAAE,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,EAAE,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAClD;AACA;CACA,EAAE,aAAa,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;CACA,EAAE,MAAM;AACR;CACA,EAAE,MAAM,SAAS,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;CAC7C,EAAE,KAAK,YAAY,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG;AACjE;CACA,GAAGA,UAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AAChE;CACA,GAAG;AACH;CACA,EAAE,MAAM,UAAU,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CAC5D,EAAE,KAAK,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG;AAClE;CACA,GAAGA,UAAQ,EAAE,UAAU,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AACjE;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;;CC5CA;CACA;CACA;AACA;CACA,MAAMC,YAAU,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACrC;CACA,SAAS,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG;AACzD;CACA,CAAC,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;CAC7C,CAAC,MAAM,MAAM,GAAGC,eAAa,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;CAC9D,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;AAC3B;CACA,CAAC,OAAO,MAAM,CAAC;AACf;CACA,CAAC;AACD;CACA,SAASA,eAAa,EAAE,WAAW,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG;AACjE;CACA,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC;CAChE,CAAC,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;AACnC;CACA,CAAC,MAAM,MAAM,GAAG,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,CAAC,KAAK,MAAM,GAAG;AACf;CACA,EAAE,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,EAAE,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAClD;AACA;CACA;CACA,EAAE,OAAO,mBAAmB,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AACzE;AACA;CACA,EAAE,MAAM;AACR;CACA;CACA;CACA,EAAE,MAAM,SAAS,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CAC3D,EAAE,MAAM,OAAO,GAAGD,YAAU,EAAE,SAAS,EAAE,CAAC;CAC1C,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC;CAC1C,EAAE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,CAAC;AAClC;CACA;CACA,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;CACb,EAAE,KAAK,WAAW,GAAG;AACrB;CACA,GAAG,EAAE,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;CACjC,GAAG,EAAE,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAC/C;CACA,GAAG,MAAM;AACT;CACA,GAAG,EAAE,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CAC/C,GAAG,EAAE,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;AACjC;CACA,GAAG;AACH;CACA,EAAE,MAAM,cAAc,GAAG,YAAY,EAAE,EAAE,EAAE,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;CAC1E,EAAE,MAAM,QAAQ,GAAG,cAAc,GAAGC,eAAa,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAC1F;CACA;CACA;CACA,EAAE,KAAK,QAAQ,GAAG;AAClB;CACA;CACA;CACA,GAAG,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC;CAC3C,GAAG,MAAM,SAAS,GAAG,WAAW;CAChC,IAAI,KAAK,IAAI,YAAY,EAAE,EAAE,GAAG,SAAS,EAAE;CAC3C,IAAI,KAAK,IAAI,YAAY,EAAE,EAAE,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC;AAChD;CACA,GAAG,KAAK,SAAS,GAAG;AACpB;CACA,IAAI,OAAO,QAAQ,CAAC;AACpB;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA;CACA,EAAE,MAAM,cAAc,GAAG,YAAY,EAAE,EAAE,EAAE,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;CAC1E,EAAE,MAAM,QAAQ,GAAG,cAAc,GAAGA,eAAa,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAC1F;CACA,EAAE,KAAK,QAAQ,IAAI,QAAQ,GAAG;AAC9B;CACA,GAAG,OAAO,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AACvE;CACA,GAAG,MAAM;AACT;CACA,GAAG,OAAO,QAAQ,IAAI,QAAQ,IAAI,IAAI,CAAC;AACvC;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;;CC5FA;CACA;CACA;CACA;AACA;CACA,MAAMC,aAAW,mBAAmB,IAAIP,UAAI,EAAE,CAAC;CAC/C,MAAMQ,UAAQ,mBAAmB,IAAI,gBAAgB,EAAE,CAAC;CACxD,MAAMC,WAAS,mBAAmB,IAAI,gBAAgB,EAAE,CAAC;CACzD,MAAMC,aAAW,mBAAmB,IAAIb,aAAO,EAAE,CAAC;AAClD;CACA,MAAMc,KAAG,mBAAmB,IAAI,WAAW,EAAE,CAAC;CAC9C,MAAMC,MAAI,mBAAmB,IAAI,WAAW,EAAE,CAAC;AAC/C;CACA,SAAS,kBAAkB,EAAE,GAAG,EAAE,IAAI,EAAE,aAAa,EAAE,aAAa,GAAG;AACvE;CACA,CAAC,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;CAC7C,CAAC,MAAM,MAAM,GAAGC,qBAAmB,EAAE,CAAC,EAAE,GAAG,EAAE,aAAa,EAAE,aAAa,EAAE,CAAC;CAC5E,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;AAC3B;CACA,CAAC,OAAO,MAAM,CAAC;AACf;CACA,CAAC;AACD;CACA,SAASA,qBAAmB,EAAE,WAAW,EAAE,GAAG,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,GAAG,IAAI,GAAG;AACjG;CACA,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC;CAChE,CAAC,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;AACnC;CACA,CAAC,KAAK,SAAS,KAAK,IAAI,GAAG;AAC3B;CACA,EAAE,KAAK,EAAE,aAAa,CAAC,WAAW,GAAG;AACrC;CACA,GAAG,aAAa,CAAC,kBAAkB,EAAE,CAAC;AACtC;CACA,GAAG;AACH;CACA,EAAEF,KAAG,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,EAAE,CAAC;CACzF,EAAE,SAAS,GAAGA,KAAG,CAAC;AAClB;CACA,EAAE;AACF;CACA,CAAC,MAAM,MAAM,GAAG,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,CAAC,KAAK,MAAM,GAAG;AACf;CACA,EAAE,MAAM,YAAY,GAAG,GAAG,CAAC,QAAQ,CAAC;CACpC,EAAE,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC;CACvC,EAAE,MAAM,OAAO,GAAG,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC;AACnD;CACA,EAAE,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;CACpC,EAAE,MAAM,GAAG,GAAG,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;AAChD;CACA,EAAE,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,EAAE,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAClD;CACA;CACA;CACA;CACA,EAAED,aAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC;AAC7C;CACA,EAAE,KAAK,aAAa,CAAC,UAAU,GAAG;AAClC;CACA;CACA,GAAG,UAAU,EAAE,mBAAmB,EAAE,WAAW,EAAE,EAAE,YAAY,EAAEE,MAAI,EAAE,CAAC;CACxE,GAAGA,MAAI,CAAC,MAAM,CAAC,IAAI,EAAEF,aAAW,EAAE,CAAC;CACnC,GAAGE,MAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC3B;CACA;CACA,GAAG,MAAM,GAAG,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,EAAE;AACnD;CACA,IAAI,gBAAgB,EAAE,GAAG,IAAIA,MAAI,CAAC,aAAa,EAAE,GAAG,EAAE;AACtD;CACA,IAAI,kBAAkB,EAAE,GAAG,IAAI;AAC/B;CACA,KAAK,GAAG,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CACzC,KAAK,GAAG,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CACzC,KAAK,GAAG,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CACzC,KAAK,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B;AACA;CACA,KAAK,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AAC3E;CACA;CACA,MAAM,WAAW,EAAEH,WAAS,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;CACtD,MAAMA,WAAS,CAAC,WAAW,GAAG,IAAI,CAAC;CACnC,MAAM,KAAK,GAAG,CAAC,kBAAkB,EAAEA,WAAS,EAAE,GAAG;AACjD;CACA,OAAO,OAAO,IAAI,CAAC;AACnB;CACA,OAAO;AACP;CACA,MAAM;AACN;AACA;CACA,KAAK,OAAO,KAAK,CAAC;AAClB;CACA,KAAK;AACL;CACA,IAAI,EAAE,CAAC;AACP;CACA,GAAG,OAAO,GAAG,CAAC;AACd;CACA,GAAG,MAAM;AACT;CACA;AACA;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AACzE;CACA;CACA,IAAI,WAAW,EAAED,UAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;AACnD;AACA;CACA,IAAIA,UAAQ,CAAC,CAAC,CAAC,YAAY,EAAEE,aAAW,EAAE,CAAC;CAC3C,IAAIF,UAAQ,CAAC,CAAC,CAAC,YAAY,EAAEE,aAAW,EAAE,CAAC;CAC3C,IAAIF,UAAQ,CAAC,CAAC,CAAC,YAAY,EAAEE,aAAW,EAAE,CAAC;CAC3C,IAAIF,UAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AAChC;CACA,IAAI,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG;AAC3D;CACA,KAAK,WAAW,EAAEC,WAAS,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;CAC9C,KAAKA,WAAS,CAAC,WAAW,GAAG,IAAI,CAAC;AAClC;CACA,KAAK,KAAKD,UAAQ,CAAC,kBAAkB,EAAEC,WAAS,EAAE,GAAG;AACrD;CACA,MAAM,OAAO,IAAI,CAAC;AAClB;CACA,MAAM;AACN;CACA,KAAK;AACL;AACA;CACA,IAAI;AACJ;AACA;CACA,GAAG;AACH;CACA,EAAE,MAAM;AACR;CACA,EAAE,MAAM,IAAI,GAAG,WAAW,GAAG,CAAC,CAAC;CAC/B,EAAE,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AAC/C;CACA,EAAE,UAAU,EAAE,mBAAmB,EAAE,IAAI,EAAE,EAAE,YAAY,EAAEF,aAAW,EAAE,CAAC;CACvE,EAAE,MAAM,gBAAgB;CACxB,GAAG,SAAS,CAAC,aAAa,EAAEA,aAAW,EAAE;CACzC,GAAGM,qBAAmB,EAAE,IAAI,EAAE,GAAG,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;AAC7E;CACA,EAAE,KAAK,gBAAgB,GAAG,OAAO,IAAI,CAAC;AACtC;CACA,EAAE,UAAU,EAAE,mBAAmB,EAAE,KAAK,EAAE,EAAE,YAAY,EAAEN,aAAW,EAAE,CAAC;CACxE,EAAE,MAAM,iBAAiB;CACzB,GAAG,SAAS,CAAC,aAAa,EAAEA,aAAW,EAAE;CACzC,GAAGM,qBAAmB,EAAE,KAAK,EAAE,GAAG,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;AAC9E;CACA,EAAE,KAAK,iBAAiB,GAAG,OAAO,IAAI,CAAC;AACvC;CACA,EAAE,OAAO,KAAK,CAAC;AACf;CACA,EAAE;AACF;CACA;;CChKA;CACA;CACA;AACA;CACA,MAAMC,YAAU,mBAAmB,IAAIjB,aAAO,EAAE,CAAC;CACjD,MAAMc,KAAG,mBAAmB,IAAI,WAAW,EAAE,CAAC;CAC9C,MAAMC,MAAI,mBAAmB,IAAI,WAAW,EAAE,CAAC;CAC/C,MAAMX,OAAK,mBAAmB,IAAIV,aAAO,EAAE,CAAC;CAC5C,MAAMwB,OAAK,mBAAmB,IAAIxB,aAAO,EAAE,CAAC;CAC5C,MAAMyB,OAAK,mBAAmB,IAAIzB,aAAO,EAAE,CAAC;CAC5C,MAAM0B,OAAK,mBAAmB,IAAI1B,aAAO,EAAE,CAAC;AAC5C;CACA,SAAS,sBAAsB;CAC/B,CAAC,GAAG;CACJ,CAAC,aAAa;CACd,CAAC,aAAa;CACd,CAAC,OAAO,GAAG,GAAG;CACd,CAAC,OAAO,GAAG,GAAG;CACd,CAAC,YAAY,GAAG,CAAC;CACjB,CAAC,YAAY,GAAG,QAAQ;CACxB,EAAE;AACF;CACA,CAAC,KAAK,EAAE,aAAa,CAAC,WAAW,GAAG;AACpC;CACA,EAAE,aAAa,CAAC,kBAAkB,EAAE,CAAC;AACrC;CACA,EAAE;AACF;CACA,CAACoB,KAAG,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,EAAE,CAAC;CACxF,CAACA,KAAG,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB;CACA,CAAC,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;CAC/B,CAAC,MAAM,GAAG,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;CAC1C,CAAC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;CAC9B,CAAC,MAAM,QAAQ,GAAG,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;CACpD,CAAC,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC;CACxC,CAAC,MAAM,QAAQ,GAAG,oBAAoB,CAAC,YAAY,EAAE,CAAC;CACtD,CAAC,MAAM,SAAS,GAAG,oBAAoB,CAAC,YAAY,EAAE,CAAC;AACvD;CACA,CAAC,IAAI,WAAW,GAAGV,OAAK,CAAC;CACzB,CAAC,IAAI,eAAe,GAAGc,OAAK,CAAC;CAC7B,CAAC,IAAI,WAAW,GAAG,IAAI,CAAC;CACxB,CAAC,IAAI,eAAe,GAAG,IAAI,CAAC;AAC5B;CACA,CAAC,KAAK,OAAO,GAAG;AAChB;CACA,EAAE,WAAW,GAAGC,OAAK,CAAC;CACtB,EAAE,eAAe,GAAGC,OAAK,CAAC;AAC1B;CACA,EAAE;AACF;CACA,CAAC,IAAI,eAAe,GAAG,QAAQ,CAAC;CAChC,CAAC,IAAI,uBAAuB,GAAG,IAAI,CAAC;CACpC,CAAC,IAAI,4BAA4B,GAAG,IAAI,CAAC;CACzC,CAACH,YAAU,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC;CAC3C,CAACF,MAAI,CAAC,MAAM,CAAC,IAAI,EAAEE,YAAU,EAAE,CAAC;CAChC,CAAC,GAAG,CAAC,SAAS;CACd,EAAE;AACF;CACA,GAAG,mBAAmB,EAAE,GAAG,IAAI;AAC/B;CACA,IAAI,OAAOH,KAAG,CAAC,aAAa,EAAE,GAAG,EAAE,CAAC;AACpC;CACA,IAAI;AACJ;CACA,GAAG,gBAAgB,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,MAAM;AAC/C;CACA,IAAI,KAAK,KAAK,GAAG,eAAe,IAAI,KAAK,GAAG,YAAY,GAAG;AAC3D;CACA;CACA;CACA,KAAK,KAAK,MAAM,GAAG;AACnB;CACA,MAAMC,MAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;CAC/B,MAAMA,MAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;CAC/B,MAAMA,MAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC9B;CACA,MAAM;AACN;CACA,KAAK,OAAO,IAAI,CAAC;AACjB;CACA,KAAK;AACL;CACA,IAAI,OAAO,KAAK,CAAC;AACjB;CACA,IAAI;AACJ;CACA,GAAG,eAAe,EAAE,EAAE,MAAM,EAAE,KAAK,MAAM;AACzC;CACA,IAAI,KAAK,aAAa,CAAC,UAAU,GAAG;AACpC;CACA;CACA;CACA,KAAK,MAAM,QAAQ,GAAG,aAAa,CAAC,UAAU,CAAC;CAC/C,KAAK,OAAO,QAAQ,CAAC,SAAS,EAAE;CAChC,MAAM,mBAAmB,EAAE,GAAG,IAAI;AAClC;CACA,OAAO,OAAOA,MAAI,CAAC,aAAa,EAAE,GAAG,EAAE,CAAC;AACxC;CACA,OAAO;AACP;CACA,MAAM,gBAAgB,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,MAAM;AAClD;CACA,OAAO,OAAO,KAAK,GAAG,eAAe,IAAI,KAAK,GAAG,YAAY,CAAC;AAC9D;CACA,OAAO;AACP;CACA,MAAM,eAAe,EAAE,EAAE,WAAW,EAAE,UAAU,MAAM;AACtD;CACA,OAAO,MAAM,IAAI,EAAE,GAAG,WAAW,EAAE,EAAE,GAAG,WAAW,GAAG,UAAU,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG;AACnF;AACA;CACA,QAAQ,WAAW,EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC;AAC/D;CACA,QAAQ,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAClD,QAAQ,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAClD,QAAQ,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAClD,QAAQ,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;AACrC;CACA,QAAQ,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChE;AACA;CACA,SAAS,WAAW,EAAE,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;AACpD;CACA,SAAS,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AACrC;CACA,SAAS,MAAM,IAAI,GAAG,QAAQ,CAAC,kBAAkB,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACzF,SAAS,KAAK,IAAI,GAAG,eAAe,GAAG;AACvC;CACA,UAAU,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAC9C;CACA,UAAU,KAAK,eAAe,GAAG;AACjC;CACA,WAAW,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAC/C;CACA,WAAW;AACX;CACA,UAAU,eAAe,GAAG,IAAI,CAAC;CACjC,UAAU,uBAAuB,GAAG,CAAC,CAAC;CACtC,UAAU,4BAA4B,GAAG,EAAE,CAAC;AAC5C;CACA,UAAU;AACV;CACA;CACA,SAAS,KAAK,IAAI,GAAG,YAAY,GAAG;AACpC;CACA,UAAU,OAAO,IAAI,CAAC;AACtB;CACA,UAAU;AACV;CACA,SAAS;AACT;CACA,QAAQ;AACR;CACA,OAAO;CACP,MAAM,EAAE,CAAC;AACT;CACA,KAAK,MAAM;AACX;CACA;CACA,KAAK,MAAM,QAAQ,GAAG,WAAW,EAAE,aAAa,EAAE,CAAC;CACnD,KAAK,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,QAAQ,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG;AACvD;CACA,MAAM,WAAW,EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC;CAC7D,MAAM,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAChD,MAAM,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAChD,MAAM,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAChD,MAAM,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;AACnC;CACA,MAAM,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC9D;AACA;CACA,OAAO,WAAW,EAAE,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;AAClD;CACA,OAAO,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AACnC;CACA,OAAO,MAAM,IAAI,GAAG,QAAQ,CAAC,kBAAkB,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACvF,OAAO,KAAK,IAAI,GAAG,eAAe,GAAG;AACrC;CACA,QAAQ,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAC5C;CACA,QAAQ,KAAK,eAAe,GAAG;AAC/B;CACA,SAAS,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAC7C;CACA,SAAS;AACT;CACA,QAAQ,eAAe,GAAG,IAAI,CAAC;CAC/B,QAAQ,uBAAuB,GAAG,CAAC,CAAC;CACpC,QAAQ,4BAA4B,GAAG,EAAE,CAAC;AAC1C;CACA,QAAQ;AACR;CACA;CACA,OAAO,KAAK,IAAI,GAAG,YAAY,GAAG;AAClC;CACA,QAAQ,OAAO,IAAI,CAAC;AACpB;CACA,QAAQ;AACR;CACA,OAAO;AACP;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,CAAC;AACH;CACA,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC;CACnD,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,SAAS,EAAE,CAAC;AACpD;CACA,CAAC,KAAK,eAAe,KAAK,QAAQ,GAAG;AACrC;CACA,EAAE,OAAO,IAAI,CAAC;AACd;CACA,EAAE;AACF;CACA,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,GAAG;AACxB;CACA,EAAE,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC;AAC1C;CACA,EAAE,MAAM;AACR;CACA,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC;AACxC;CACA,EAAE;AACF;CACA,CAAC,OAAO,CAAC,QAAQ,GAAG,eAAe;CACnC,CAAC,OAAO,CAAC,SAAS,GAAG,uBAAuB,CAAC;AAC7C;CACA,CAAC,KAAK,OAAO,GAAG;AAChB;CACA,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC;CACjE,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC;CAC7C,EAAE,OAAO,CAAC,KAAK,CAAC,YAAY,EAAEE,YAAU,EAAE,CAAC;CAC3C,EAAE,eAAe,CAAC,YAAY,EAAEA,YAAU,EAAE,CAAC;CAC7C,EAAE,OAAO,CAAC,QAAQ,GAAG,eAAe,CAAC,GAAG,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;CACnE,EAAE,OAAO,CAAC,SAAS,GAAG,4BAA4B,CAAC;AACnD;CACA,EAAE;AACF;CACA,CAAC,OAAO,OAAO,CAAC;AAChB;CACA;;CC3PA;CACA;CACA;AACA;CACA,SAAS,cAAc,EAAE,GAAG,EAAE,WAAW,GAAG,IAAI,GAAG;AACnD;CACA,CAAC,KAAK,WAAW,IAAI,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,GAAG;AACpD;CACA,EAAE,WAAW,GAAG,IAAI,GAAG,EAAE,WAAW,EAAE,CAAC;AACvC;CACA,EAAE;AACF;CACA,CAAC,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;CAC/B,CAAC,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;CAC/D,CAAC,MAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;AAC9C;CACA,CAAC,IAAI,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;CACpD,CAAC,IAAI,UAAU,GAAG,CAAC,CAAC;CACpB,CAAC,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC;CAC1B,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClD;CACA,EAAE,MAAM,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC;CACtB,EAAE,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;CAC1C,EAAE,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;CAC1C,EAAE,YAAY,GAAG,IAAI,YAAY,EAAE,MAAM,EAAE,CAAC;AAC5C;CACA,EAAE,SAAS,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC;CAC7B,EAAE,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC;AAClC;CACA,EAAE;AACF;CACA,CAAC,SAAS,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,GAAG,KAAK,GAAG;AAC9D;CACA,EAAE,MAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;CACtC,EAAE,MAAM,MAAM,GAAG,WAAW,EAAE,WAAW,GAAG,EAAE,EAAE,KAAK,gBAAgB,CAAC;CACtE,EAAE,KAAK,MAAM,GAAG;AAChB;CACA,GAAG,MAAM,MAAM,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;CACjD,GAAG,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,EAAE,EAAE,CAAC;AACjD;CACA,GAAG,IAAI,IAAI,GAAG,QAAQ,CAAC;CACvB,GAAG,IAAI,IAAI,GAAG,QAAQ,CAAC;CACvB,GAAG,IAAI,IAAI,GAAG,QAAQ,CAAC;CACvB,GAAG,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;CACzB,GAAG,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;CACzB,GAAG,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;AACzB;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC3D;CACA,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC;CAChD,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnC;CACA,KAAK,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;CACvB,KAAK,KAAK,GAAG,QAAQ,GAAG,QAAQ,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;AAClD;CACA,KAAK,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;CACrC,KAAK,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;CACrC,KAAK,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACrC;CACA,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;CAC9B,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AAC9B;CACA,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;CAC9B,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AAC9B;CACA,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;CAC9B,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AAC9B;AACA;CACA,KAAK;AACL;CACA,IAAI;AACJ;AACA;CACA,GAAG;CACH,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;CAC5C,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;CAC5C,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;AAC5C;CACA,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;CAC5C,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;CAC5C,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;CAC5C,KAAK;AACL;CACA,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;CAC3C,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;CAC3C,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC3C;CACA,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;CAC3C,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;CAC3C,IAAI,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC3C;CACA,IAAI,OAAO,IAAI,CAAC;AAChB;CACA,IAAI,MAAM;AACV;CACA,IAAI,OAAO,KAAK,CAAC;AACjB;CACA,IAAI;AACJ;CACA,GAAG,MAAM;AACT;CACA,GAAG,MAAM,IAAI,GAAG,WAAW,GAAG,CAAC,CAAC;CAChC,GAAG,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AAChD;CACA;CACA;CACA,GAAG,MAAM,UAAU,GAAG,IAAI,GAAG,UAAU,CAAC;CACxC,GAAG,MAAM,WAAW,GAAG,KAAK,GAAG,UAAU,CAAC;CAC1C,GAAG,IAAI,aAAa,GAAG,KAAK,CAAC;CAC7B,GAAG,IAAI,YAAY,GAAG,KAAK,CAAC;CAC5B,GAAG,IAAI,aAAa,GAAG,KAAK,CAAC;AAC7B;CACA,GAAG,KAAK,WAAW,GAAG;AACtB;CACA;CACA;CACA,IAAI,KAAK,EAAE,aAAa,GAAG;AAC3B;CACA,KAAK,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE,UAAU,EAAE,CAAC;CAClD,KAAK,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,WAAW,EAAE,CAAC;CACpD,KAAK,aAAa,GAAG,EAAE,YAAY,IAAI,EAAE,aAAa,CAAC;AACvD;CACA,KAAK;AACL;CACA,IAAI,MAAM;AACV;CACA,IAAI,YAAY,GAAG,IAAI,CAAC;CACxB,IAAI,aAAa,GAAG,IAAI,CAAC;AACzB;CACA,IAAI;AACJ;CACA,GAAG,MAAM,YAAY,GAAG,aAAa,IAAI,YAAY,CAAC;CACtD,GAAG,MAAM,aAAa,GAAG,aAAa,IAAI,aAAa,CAAC;AACxD;CACA,GAAG,IAAI,UAAU,GAAG,KAAK,CAAC;CAC1B,GAAG,KAAK,YAAY,GAAG;AACvB;CACA,IAAI,UAAU,GAAG,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;AAC9D;CACA,IAAI;AACJ;CACA,GAAG,IAAI,WAAW,GAAG,KAAK,CAAC;CAC3B,GAAG,KAAK,aAAa,GAAG;AACxB;CACA,IAAI,WAAW,GAAG,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;AAChE;CACA,IAAI;AACJ;CACA,GAAG,MAAM,SAAS,GAAG,UAAU,IAAI,WAAW,CAAC;CAC/C,GAAG,KAAK,SAAS,GAAG;AACpB;CACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnC;CACA,KAAK,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;CAC5B,KAAK,MAAM,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;CAC9B,KAAK,MAAM,YAAY,GAAG,YAAY,EAAE,KAAK,EAAE,CAAC;CAChD,KAAK,MAAM,YAAY,GAAG,YAAY,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;CACpD,KAAK,MAAM,aAAa,GAAG,YAAY,EAAE,MAAM,EAAE,CAAC;CAClD,KAAK,MAAM,aAAa,GAAG,YAAY,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;AACtD;CACA,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,YAAY,GAAG,aAAa,GAAG,YAAY,GAAG,aAAa,CAAC;CACnG,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,GAAG,aAAa,GAAG,YAAY,GAAG,aAAa,CAAC;AACvG;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG,OAAO,SAAS,CAAC;AACpB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;;CC1KA;CACA;CACA;AACA;CACA,SAAS,gBAAgB,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,GAAG;AACzE;CACA,CAAC,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;CAC7C,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;CACtD,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;AAC3B;CACA,CAAC;AACD;CACA,SAAS,QAAQ,EAAE,WAAW,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,GAAG;AACxE;CACA,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC;CAChE,CAAC,MAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;CACrC,CAAC,MAAM,MAAM,GAAG,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,CAAC,KAAK,MAAM,GAAG;AACf;CACA,EAAE,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,EAAE,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAClD;CACA,EAAE,sBAAsB,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AACjF;AACA;CACA,EAAE,MAAM;AACR;CACA,EAAE,MAAM,SAAS,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;CAC7C,EAAE,KAAK,YAAY,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG;AACjE;CACA,GAAG,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AAChE;CACA,GAAG;AACH;CACA,EAAE,MAAM,UAAU,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CAC5D,EAAE,KAAK,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG;AAClE;CACA,GAAG,QAAQ,EAAE,UAAU,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AACjE;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;;CC3CA;CACA;CACA;AACA;CACA,MAAM,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACrC;CACA,SAAS,qBAAqB,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG;AAClE;CACA,CAAC,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;CAC7C,CAAC,MAAM,MAAM,GAAG,aAAa,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;CAC9D,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;AAC3B;CACA,CAAC,OAAO,MAAM,CAAC;AACf;CACA,CAAC;AACD;CACA,SAAS,aAAa,EAAE,WAAW,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG;AACjE;CACA,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC;CAChE,CAAC,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;AACnC;CACA,CAAC,MAAM,MAAM,GAAG,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,CAAC,KAAK,MAAM,GAAG;AACf;CACA,EAAE,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,EAAE,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAClD;CACA,EAAE,OAAO,4BAA4B,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AAClF;AACA;CACA,EAAE,MAAM;AACR;CACA;CACA;CACA,EAAE,MAAM,SAAS,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CAC3D,EAAE,MAAM,OAAO,GAAG,UAAU,EAAE,SAAS,EAAE,CAAC;CAC1C,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC;CAC1C,EAAE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,CAAC;AAClC;CACA;CACA,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;CACb,EAAE,KAAK,WAAW,GAAG;AACrB;CACA,GAAG,EAAE,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;CACjC,GAAG,EAAE,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAC/C;CACA,GAAG,MAAM;AACT;CACA,GAAG,EAAE,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CAC/C,GAAG,EAAE,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;AACjC;CACA,GAAG;AACH;CACA,EAAE,MAAM,cAAc,GAAG,YAAY,EAAE,EAAE,EAAE,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;CAC1E,EAAE,MAAM,QAAQ,GAAG,cAAc,GAAG,aAAa,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAC1F;CACA;CACA;CACA,EAAE,KAAK,QAAQ,GAAG;AAClB;CACA;CACA;CACA,GAAG,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC;CAC3C,GAAG,MAAM,SAAS,GAAG,WAAW;CAChC,IAAI,KAAK,IAAI,YAAY,EAAE,EAAE,GAAG,SAAS,EAAE;CAC3C,IAAI,KAAK,IAAI,YAAY,EAAE,EAAE,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC;AAChD;CACA,GAAG,KAAK,SAAS,GAAG;AACpB;CACA,IAAI,OAAO,QAAQ,CAAC;AACpB;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA;CACA,EAAE,MAAM,cAAc,GAAG,YAAY,EAAE,EAAE,EAAE,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;CAC1E,EAAE,MAAM,QAAQ,GAAG,cAAc,GAAG,aAAa,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAC1F;CACA,EAAE,KAAK,QAAQ,IAAI,QAAQ,GAAG;AAC9B;CACA,GAAG,OAAO,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AACvE;CACA,GAAG,MAAM;AACT;CACA,GAAG,OAAO,QAAQ,IAAI,QAAQ,IAAI,IAAI,CAAC;AACvC;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;;CC1FA;CACA;CACA;CACA;AACA;CACA,MAAMP,aAAW,mBAAmB,IAAIP,UAAI,EAAE,CAAC;CAC/C,MAAM,QAAQ,mBAAmB,IAAI,gBAAgB,EAAE,CAAC;CACxD,MAAM,SAAS,mBAAmB,IAAI,gBAAgB,EAAE,CAAC;CACzD,MAAM,WAAW,mBAAmB,IAAIH,aAAO,EAAE,CAAC;AAClD;CACA,MAAMc,KAAG,mBAAmB,IAAI,WAAW,EAAE,CAAC;CAC9C,MAAMC,MAAI,mBAAmB,IAAI,WAAW,EAAE,CAAC;AAC/C;CACA,SAAS,2BAA2B,EAAE,GAAG,EAAE,IAAI,EAAE,aAAa,EAAE,aAAa,GAAG;AAChF;CACA,CAAC,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;CAC7C,CAAC,MAAM,MAAM,GAAG,mBAAmB,EAAE,CAAC,EAAE,GAAG,EAAE,aAAa,EAAE,aAAa,EAAE,CAAC;CAC5E,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;AAC3B;CACA,CAAC,OAAO,MAAM,CAAC;AACf;CACA,CAAC;AACD;CACA,SAAS,mBAAmB,EAAE,WAAW,EAAE,GAAG,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,GAAG,IAAI,GAAG;AACjG;CACA,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC;CAChE,CAAC,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;AACnC;CACA,CAAC,KAAK,SAAS,KAAK,IAAI,GAAG;AAC3B;CACA,EAAE,KAAK,EAAE,aAAa,CAAC,WAAW,GAAG;AACrC;CACA,GAAG,aAAa,CAAC,kBAAkB,EAAE,CAAC;AACtC;CACA,GAAG;AACH;CACA,EAAED,KAAG,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,EAAE,CAAC;CACzF,EAAE,SAAS,GAAGA,KAAG,CAAC;AAClB;CACA,EAAE;AACF;CACA,CAAC,MAAM,MAAM,GAAG,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,CAAC,KAAK,MAAM,GAAG;AACf;CACA,EAAE,MAAM,YAAY,GAAG,GAAG,CAAC,QAAQ,CAAC;CACpC,EAAE,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC;CACvC,EAAE,MAAM,OAAO,GAAG,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC;AACnD;CACA,EAAE,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;CACpC,EAAE,MAAM,GAAG,GAAG,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;AAChD;CACA,EAAE,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,EAAE,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAClD;CACA;CACA;CACA;CACA,EAAE,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC;AAC7C;CACA,EAAE,KAAK,aAAa,CAAC,UAAU,GAAG;AAClC;CACA;CACA,GAAG,UAAU,EAAE,mBAAmB,EAAE,WAAW,EAAE,EAAE,YAAY,EAAEC,MAAI,EAAE,CAAC;CACxE,GAAGA,MAAI,CAAC,MAAM,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;CACnC,GAAGA,MAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC3B;CACA;CACA,GAAG,MAAM,GAAG,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,EAAE;AACnD;CACA,IAAI,gBAAgB,EAAE,GAAG,IAAIA,MAAI,CAAC,aAAa,EAAE,GAAG,EAAE;AACtD;CACA,IAAI,kBAAkB,EAAE,GAAG,IAAI;AAC/B;CACA,KAAK,GAAG,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CACzC,KAAK,GAAG,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CACzC,KAAK,GAAG,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CACzC,KAAK,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B;CACA,KAAK,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,KAAK,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC7D;CACA;CACA,MAAM,WAAW,EAAE,SAAS,EAAE,CAAC,GAAG,GAAG,CAAC,oBAAoB,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;CACtF,MAAM,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;CACnC,MAAM,KAAK,GAAG,CAAC,kBAAkB,EAAE,SAAS,EAAE,GAAG;AACjD;CACA,OAAO,OAAO,IAAI,CAAC;AACnB;CACA,OAAO;AACP;CACA,MAAM;AACN;AACA;CACA,KAAK,OAAO,KAAK,CAAC;AAClB;CACA,KAAK;AACL;CACA,IAAI,EAAE,CAAC;AACP;CACA,GAAG,OAAO,GAAG,CAAC;AACd;CACA,GAAG,MAAM;AACT;CACA;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,KAAK,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC3D;CACA;CACA,IAAI,MAAM,EAAE,GAAG,GAAG,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC;CAC7C,IAAI,WAAW,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;AACxD;AACA;CACA,IAAI,QAAQ,CAAC,CAAC,CAAC,YAAY,EAAE,WAAW,EAAE,CAAC;CAC3C,IAAI,QAAQ,CAAC,CAAC,CAAC,YAAY,EAAE,WAAW,EAAE,CAAC;CAC3C,IAAI,QAAQ,CAAC,CAAC,CAAC,YAAY,EAAE,WAAW,EAAE,CAAC;CAC3C,IAAI,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AAChC;CACA,IAAI,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG;AAC3D;CACA,KAAK,WAAW,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;CAC9C,KAAK,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;AAClC;CACA,KAAK,KAAK,QAAQ,CAAC,kBAAkB,EAAE,SAAS,EAAE,GAAG;AACrD;CACA,MAAM,OAAO,IAAI,CAAC;AAClB;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA,IAAI;AACJ;AACA;CACA,GAAG;AACH;CACA,EAAE,MAAM;AACR;CACA,EAAE,MAAM,IAAI,GAAG,WAAW,GAAG,CAAC,CAAC;CAC/B,EAAE,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AAC/C;CACA,EAAE,UAAU,EAAE,mBAAmB,EAAE,IAAI,EAAE,EAAE,YAAY,EAAEL,aAAW,EAAE,CAAC;CACvE,EAAE,MAAM,gBAAgB;CACxB,GAAG,SAAS,CAAC,aAAa,EAAEA,aAAW,EAAE;CACzC,GAAG,mBAAmB,EAAE,IAAI,EAAE,GAAG,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;AAC7E;CACA,EAAE,KAAK,gBAAgB,GAAG,OAAO,IAAI,CAAC;AACtC;CACA,EAAE,UAAU,EAAE,mBAAmB,EAAE,KAAK,EAAE,EAAE,YAAY,EAAEA,aAAW,EAAE,CAAC;CACxE,EAAE,MAAM,iBAAiB;CACzB,GAAG,SAAS,CAAC,aAAa,EAAEA,aAAW,EAAE;CACzC,GAAG,mBAAmB,EAAE,KAAK,EAAE,GAAG,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;AAC9E;CACA,EAAE,KAAK,iBAAiB,GAAG,OAAO,IAAI,CAAC;AACvC;CACA,EAAE,OAAO,KAAK,CAAC;AACf;CACA,EAAE;AACF;CACA;;CC9JA;CACA;CACA;AACA;CACA,MAAM,UAAU,mBAAmB,IAAIV,aAAO,EAAE,CAAC;CACjD,MAAMc,KAAG,mBAAmB,IAAI,WAAW,EAAE,CAAC;CAC9C,MAAM,IAAI,mBAAmB,IAAI,WAAW,EAAE,CAAC;CAC/C,MAAM,KAAK,mBAAmB,IAAIpB,aAAO,EAAE,CAAC;CAC5C,MAAM,KAAK,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CAC5C,MAAM,KAAK,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CAC5C,MAAM,KAAK,mBAAmB,IAAIA,aAAO,EAAE,CAAC;AAC5C;CACA,SAAS,+BAA+B;CACxC,CAAC,GAAG;CACJ,CAAC,aAAa;CACd,CAAC,aAAa;CACd,CAAC,OAAO,GAAG,GAAG;CACd,CAAC,OAAO,GAAG,GAAG;CACd,CAAC,YAAY,GAAG,CAAC;CACjB,CAAC,YAAY,GAAG,QAAQ;CACxB,EAAE;AACF;CACA,CAAC,KAAK,EAAE,aAAa,CAAC,WAAW,GAAG;AACpC;CACA,EAAE,aAAa,CAAC,kBAAkB,EAAE,CAAC;AACrC;CACA,EAAE;AACF;CACA,CAACoB,KAAG,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,EAAE,CAAC;CACxF,CAACA,KAAG,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB;CACA,CAAC,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;CAC/B,CAAC,MAAM,GAAG,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;CAC1C,CAAC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;CAC9B,CAAC,MAAM,QAAQ,GAAG,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;CACpD,CAAC,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC;CACxC,CAAC,MAAM,QAAQ,GAAG,oBAAoB,CAAC,YAAY,EAAE,CAAC;CACtD,CAAC,MAAM,SAAS,GAAG,oBAAoB,CAAC,YAAY,EAAE,CAAC;AACvD;CACA,CAAC,IAAI,WAAW,GAAG,KAAK,CAAC;CACzB,CAAC,IAAI,eAAe,GAAG,KAAK,CAAC;CAC7B,CAAC,IAAI,WAAW,GAAG,IAAI,CAAC;CACxB,CAAC,IAAI,eAAe,GAAG,IAAI,CAAC;AAC5B;CACA,CAAC,KAAK,OAAO,GAAG;AAChB;CACA,EAAE,WAAW,GAAG,KAAK,CAAC;CACtB,EAAE,eAAe,GAAG,KAAK,CAAC;AAC1B;CACA,EAAE;AACF;CACA,CAAC,IAAI,eAAe,GAAG,QAAQ,CAAC;CAChC,CAAC,IAAI,uBAAuB,GAAG,IAAI,CAAC;CACpC,CAAC,IAAI,4BAA4B,GAAG,IAAI,CAAC;CACzC,CAAC,UAAU,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC;CAC3C,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC;CAChC,CAAC,GAAG,CAAC,SAAS;CACd,EAAE;AACF;CACA,GAAG,mBAAmB,EAAE,GAAG,IAAI;AAC/B;CACA,IAAI,OAAOA,KAAG,CAAC,aAAa,EAAE,GAAG,EAAE,CAAC;AACpC;CACA,IAAI;AACJ;CACA,GAAG,gBAAgB,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,MAAM;AAC/C;CACA,IAAI,KAAK,KAAK,GAAG,eAAe,IAAI,KAAK,GAAG,YAAY,GAAG;AAC3D;CACA;CACA;CACA,KAAK,KAAK,MAAM,GAAG;AACnB;CACA,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;CAC/B,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;CAC/B,MAAM,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC9B;CACA,MAAM;AACN;CACA,KAAK,OAAO,IAAI,CAAC;AACjB;CACA,KAAK;AACL;CACA,IAAI,OAAO,KAAK,CAAC;AACjB;CACA,IAAI;AACJ;CACA,GAAG,eAAe,EAAE,EAAE,MAAM,EAAE,KAAK,MAAM;AACzC;CACA,IAAI,KAAK,aAAa,CAAC,UAAU,GAAG;AACpC;CACA;CACA;CACA,KAAK,MAAM,QAAQ,GAAG,aAAa,CAAC,UAAU,CAAC;CAC/C,KAAK,OAAO,QAAQ,CAAC,SAAS,EAAE;CAChC,MAAM,mBAAmB,EAAE,GAAG,IAAI;AAClC;CACA,OAAO,OAAO,IAAI,CAAC,aAAa,EAAE,GAAG,EAAE,CAAC;AACxC;CACA,OAAO;AACP;CACA,MAAM,gBAAgB,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,MAAM;AAClD;CACA,OAAO,OAAO,KAAK,GAAG,eAAe,IAAI,KAAK,GAAG,YAAY,CAAC;AAC9D;CACA,OAAO;AACP;CACA,MAAM,eAAe,EAAE,EAAE,WAAW,EAAE,UAAU,MAAM;AACtD;CACA,OAAO,MAAM,IAAI,EAAE,GAAG,WAAW,EAAE,EAAE,GAAG,WAAW,GAAG,UAAU,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG;AACnF;CACA,QAAQ,MAAM,GAAG,GAAG,QAAQ,CAAC,oBAAoB,EAAE,EAAE,EAAE,CAAC;CACxD,QAAQ,WAAW,EAAE,SAAS,EAAE,CAAC,GAAG,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC;AAChE;CACA,QAAQ,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAClD,QAAQ,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAClD,QAAQ,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAClD,QAAQ,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;AACrC;CACA,QAAQ,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChE;CACA,SAAS,MAAM,EAAE,GAAG,GAAG,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC;CAClD,SAAS,WAAW,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;AACrD;CACA,SAAS,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AACrC;CACA,SAAS,MAAM,IAAI,GAAG,QAAQ,CAAC,kBAAkB,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACzF,SAAS,KAAK,IAAI,GAAG,eAAe,GAAG;AACvC;CACA,UAAU,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAC9C;CACA,UAAU,KAAK,eAAe,GAAG;AACjC;CACA,WAAW,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAC/C;CACA,WAAW;AACX;CACA,UAAU,eAAe,GAAG,IAAI,CAAC;CACjC,UAAU,uBAAuB,GAAG,CAAC,CAAC;CACtC,UAAU,4BAA4B,GAAG,EAAE,CAAC;AAC5C;CACA,UAAU;AACV;CACA;CACA,SAAS,KAAK,IAAI,GAAG,YAAY,GAAG;AACpC;CACA,UAAU,OAAO,IAAI,CAAC;AACtB;CACA,UAAU;AACV;CACA,SAAS;AACT;CACA,QAAQ;AACR;CACA,OAAO;CACP,MAAM,EAAE,CAAC;AACT;CACA,KAAK,MAAM;AACX;CACA;CACA,KAAK,MAAM,QAAQ,GAAG,WAAW,EAAE,aAAa,EAAE,CAAC;CACnD,KAAK,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,QAAQ,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG;AACvD;CACA,MAAM,WAAW,EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC;CAC7D,MAAM,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAChD,MAAM,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAChD,MAAM,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAChD,MAAM,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;AACnC;CACA,MAAM,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC9D;CACA,OAAO,MAAM,EAAE,GAAG,GAAG,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC;CAChD,OAAO,WAAW,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;AACnD;CACA,OAAO,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AACnC;CACA,OAAO,MAAM,IAAI,GAAG,QAAQ,CAAC,kBAAkB,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACvF,OAAO,KAAK,IAAI,GAAG,eAAe,GAAG;AACrC;CACA,QAAQ,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAC5C;CACA,QAAQ,KAAK,eAAe,GAAG;AAC/B;CACA,SAAS,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAC7C;CACA,SAAS;AACT;CACA,QAAQ,eAAe,GAAG,IAAI,CAAC;CAC/B,QAAQ,uBAAuB,GAAG,CAAC,CAAC;CACpC,QAAQ,4BAA4B,GAAG,EAAE,CAAC;AAC1C;CACA,QAAQ;AACR;CACA;CACA,OAAO,KAAK,IAAI,GAAG,YAAY,GAAG;AAClC;CACA,QAAQ,OAAO,IAAI,CAAC;AACpB;CACA,QAAQ;AACR;CACA,OAAO;AACP;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,CAAC;AACH;CACA,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC;CACnD,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,SAAS,EAAE,CAAC;AACpD;CACA,CAAC,KAAK,eAAe,KAAK,QAAQ,GAAG;AACrC;CACA,EAAE,OAAO,IAAI,CAAC;AACd;CACA,EAAE;AACF;CACA,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,GAAG;AACxB;CACA,EAAE,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC;AAC1C;CACA,EAAE,MAAM;AACR;CACA,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC;AACxC;CACA,EAAE;AACF;CACA,CAAC,OAAO,CAAC,QAAQ,GAAG,eAAe;CACnC,CAAC,OAAO,CAAC,SAAS,GAAG,uBAAuB,CAAC;AAC7C;CACA,CAAC,KAAK,OAAO,GAAG;AAChB;CACA,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC;CACjE,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC;CAC7C,EAAE,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;CAC3C,EAAE,eAAe,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;CAC7C,EAAE,OAAO,CAAC,QAAQ,GAAG,eAAe,CAAC,GAAG,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;CACnE,EAAE,OAAO,CAAC,SAAS,GAAG,4BAA4B,CAAC;AACnD;CACA,EAAE;AACF;CACA,CAAC,OAAO,OAAO,CAAC;AAChB;CACA;;CC7PO,SAAS,4BAA4B,GAAG;AAC/C;CACA,CAAC,OAAO,OAAO,iBAAiB,KAAK,WAAW,CAAC;AACjD;CACA,CAAC;AACD;CACO,SAAS,mBAAmB,EAAE,KAAK,EAAE,iBAAiB,GAAG;AAChE;CACA,CAAC,KAAK,KAAK,KAAK,IAAI,GAAG;AACvB;CACA,EAAE,OAAO,KAAK,CAAC;AACf;CACA,EAAE,MAAM,KAAK,KAAK,CAAC,MAAM,GAAG;AAC5B;CACA,EAAE,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;CAC9B,EAAE,KAAK,MAAM,CAAC,WAAW,KAAK,iBAAiB,GAAG;AAClD;CACA,GAAG,OAAO,KAAK,CAAC;AAChB;CACA,GAAG;AACH;CACA,EAAE,MAAM,gBAAgB,GAAG,KAAK,CAAC,WAAW,CAAC;CAC7C,EAAE,MAAM,MAAM,GAAG,IAAI,gBAAgB,EAAE,IAAI,iBAAiB,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,CAAC;CACpF,EAAE,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC;CACtB,EAAE,OAAO,MAAM,CAAC;AAChB;CACA,EAAE,MAAM;AACR;CACA,EAAE,KAAK,KAAK,CAAC,WAAW,KAAK,iBAAiB,GAAG;AACjD;CACA,GAAG,OAAO,KAAK,CAAC;AAChB;CACA,GAAG;AACH;CACA,EAAE,MAAM,MAAM,GAAG,IAAI,iBAAiB,EAAE,KAAK,CAAC,UAAU,EAAE,CAAC;CAC3D,EAAE,IAAI,UAAU,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,UAAU,EAAE,KAAK,EAAE,EAAE,CAAC;CAC1D,EAAE,OAAO,MAAM,CAAC;AAChB;CACA,EAAE;AACF;CACA;;CClCA,MAAM,aAAa,GAAG,IAAI,WAAW,CAAC,WAAW,EAAE,CAAC;CACpD,MAAM,aAAa,GAAG,IAAI,WAAW,CAAC,WAAW,EAAE,CAAC;CACpD,MAAM,QAAQ,GAAG,IAAI,aAAa,EAAE,MAAM,IAAIX,UAAI,EAAE,EAAE,CAAC;CACvD,MAAM,SAAS,GAAG,IAAIA,UAAI,EAAE,CAAC;CAC7B,MAAM,UAAU,GAAG,IAAIA,UAAI,EAAE,CAAC;AAC9B;CACA,MAAM,SAAS,GAAG,IAAIA,UAAI,EAAE,CAAC;CAC7B,MAAM,UAAU,GAAG,IAAIA,UAAI,EAAE,CAAC;AAC9B;CACA,IAAI,OAAO,GAAG,KAAK,CAAC;AACpB;CACO,SAAS,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,aAAa,EAAE,gBAAgB,GAAG;AAC1E;CACA,CAAC,KAAK,OAAO,GAAG;AAChB;CACA,EAAE,MAAM,IAAI,KAAK,EAAE,oDAAoD,EAAE,CAAC;AAC1E;CACA,EAAE;AACF;CACA,CAAC,OAAO,GAAG,IAAI,CAAC;AAChB;CACA,CAAC,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC;CAC1B,CAAC,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;CACpC,CAAC,IAAI,MAAM,CAAC;CACZ,CAAC,IAAI,OAAO,GAAG,CAAC,CAAC;CACjB,CAAC,IAAI,OAAO,GAAG,CAAC,CAAC;CACjB,CAAC,MAAM,MAAM,GAAG,IAAIH,aAAO,EAAE,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC;AAC7D;CACA;CACA,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG;AACpD;CACA,EAAE,aAAa,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;CACxC,EAAE,OAAO,GAAG,CAAC,CAAC;AACd;CACA;CACA,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;CAC3C,EAAE,UAAU,EAAE,mBAAmB,EAAE,CAAC,EAAE,EAAE,aAAa,CAAC,YAAY,EAAE,QAAQ,EAAE,CAAC;CAC/E,EAAE,QAAQ,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC;AAClC;CACA;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG;AAC1D;CACA,GAAG,aAAa,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;AAC9C;CACA,GAAG,MAAM,GAAG,SAAS;CACrB,IAAI,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,MAAM,EAAE,gBAAgB;CACjD,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;CAC1B,IAAI,QAAQ;CACZ,IAAI,CAAC;AACL;CACA,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;CAC/B,GAAG,OAAO,IAAI,UAAU,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC;AACrC;CACA,GAAG,KAAK,MAAM,GAAG;AACjB;CACA,IAAI,MAAM;AACV;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA,EAAE,QAAQ,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC;CACxC,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC;CAC9B,EAAE,OAAO,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC;AAC/B;CACA,EAAE,KAAK,MAAM,GAAG;AAChB;CACA,GAAG,MAAM;AACT;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,GAAG,KAAK,CAAC;CACjB,CAAC,OAAO,MAAM,CAAC;AACf;CACA,CAAC;AACD;CACA,SAAS,SAAS;CAClB,CAAC,YAAY;CACb,CAAC,YAAY;CACb,CAAC,UAAU;CACX,CAAC,UAAU;CACX,CAAC,oBAAoB;AACrB;CACA;CACA,CAAC,oBAAoB,GAAG,CAAC;CACzB,CAAC,oBAAoB,GAAG,CAAC;AACzB;CACA;CACA,CAAC,MAAM,GAAG,CAAC;CACX,CAAC,MAAM,GAAG,CAAC;AACX;CACA,CAAC,OAAO,GAAG,IAAI;CACf,CAAC,QAAQ,GAAG,KAAK;AACjB;CACA,EAAE;AACF;CACA;CACA,CAAC,IAAI,YAAY,EAAE,YAAY,CAAC;CAChC,CAAC,KAAK,QAAQ,GAAG;AACjB;CACA,EAAE,YAAY,GAAG,aAAa,CAAC;CAC/B,EAAE,YAAY,GAAG,aAAa,CAAC;AAC/B;CACA,EAAE,MAAM;AACR;CACA,EAAE,YAAY,GAAG,aAAa,CAAC;CAC/B,EAAE,YAAY,GAAG,aAAa,CAAC;AAC/B;CACA,EAAE;AACF;CACA;CACA,CAAC;CACD,EAAE,aAAa,GAAG,YAAY,CAAC,YAAY;CAC3C,EAAE,YAAY,GAAG,YAAY,CAAC,WAAW;CACzC,EAAE,YAAY,GAAG,YAAY,CAAC,WAAW;CACzC,EAAE,aAAa,GAAG,YAAY,CAAC,YAAY;CAC3C,EAAE,YAAY,GAAG,YAAY,CAAC,WAAW;CACzC,EAAE,YAAY,GAAG,YAAY,CAAC,WAAW,CAAC;AAC1C;CACA,CAAC,MAAM,YAAY,GAAG,YAAY,GAAG,CAAC,CAAC;CACvC,CAAC,MAAM,YAAY,GAAG,YAAY,GAAG,CAAC,CAAC;CACvC,CAAC,MAAM,OAAO,GAAG,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC;CACvD,CAAC,MAAM,OAAO,GAAG,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC;CACvD,CAAC,IAAI,MAAM,GAAG,KAAK,CAAC;CACpB,CAAC,KAAK,OAAO,IAAI,OAAO,GAAG;AAC3B;CACA;CACA,EAAE,KAAK,QAAQ,GAAG;AAClB;CACA,GAAG,MAAM,GAAG,oBAAoB;CAChC,IAAI,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,YAAY,GAAG,CAAC,EAAE,YAAY,EAAE;CACjF,IAAI,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,YAAY,GAAG,CAAC,EAAE,YAAY,EAAE;CACjF,IAAI,MAAM,EAAE,oBAAoB,GAAG,YAAY;CAC/C,IAAI,MAAM,EAAE,oBAAoB,GAAG,YAAY;CAC/C,IAAI,CAAC;AACL;CACA,GAAG,MAAM;AACT;CACA,GAAG,MAAM,GAAG,oBAAoB;CAChC,IAAI,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,YAAY,GAAG,CAAC,EAAE,YAAY,EAAE;CACjF,IAAI,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,YAAY,GAAG,CAAC,EAAE,YAAY,EAAE;CACjF,IAAI,MAAM,EAAE,oBAAoB,GAAG,YAAY;CAC/C,IAAI,MAAM,EAAE,oBAAoB,GAAG,YAAY;CAC/C,IAAI,CAAC;AACL;CACA,GAAG;AACH;CACA,EAAE,MAAM,KAAK,OAAO,GAAG;AACvB;CACA;CACA;CACA;AACA;CACA;CACA,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;CACzC,EAAE,UAAU,EAAE,mBAAmB,EAAE,YAAY,EAAE,EAAE,aAAa,EAAE,MAAM,EAAE,CAAC;CAC3E,EAAE,MAAM,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;AACpC;CACA;CACA,EAAE,MAAM,GAAG,GAAG,SAAS,EAAE,YAAY,EAAE,CAAC;CACxC,EAAE,MAAM,GAAG,GAAG,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC;CACvD,EAAE,UAAU,EAAE,mBAAmB,EAAE,GAAG,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;CACrE,EAAE,UAAU,EAAE,mBAAmB,EAAE,GAAG,EAAE,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;AACtE;CACA;CACA,EAAE,MAAM,YAAY,GAAG,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,CAAC;CACzD,EAAE,MAAM,YAAY,GAAG,MAAM,CAAC,aAAa,EAAE,UAAU,EAAE,CAAC;CAC1D,EAAE,MAAM,GAAG;CACX,GAAG,YAAY,IAAI,SAAS;CAC5B,IAAI,YAAY,EAAE,GAAG,EAAE,UAAU,EAAE,UAAU,EAAE,oBAAoB;CACnE,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC;CAClE,IAAI,MAAM,EAAE,EAAE,QAAQ;CACtB,IAAI;CACJ;CACA,GAAG,YAAY,IAAI,SAAS;CAC5B,IAAI,YAAY,EAAE,GAAG,EAAE,UAAU,EAAE,UAAU,EAAE,oBAAoB;CACnE,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC;CAClE,IAAI,MAAM,EAAE,EAAE,QAAQ;CACtB,IAAI;CACJ,GAAG,CAAC;AACJ;CACA,EAAE,QAAQ,CAAC,gBAAgB,EAAE,MAAM,EAAE,CAAC;AACtC;CACA,EAAE,MAAM;AACR;CACA;CACA;AACA;CACA;CACA,EAAE,MAAM,GAAG,GAAG,SAAS,EAAE,YAAY,EAAE,CAAC;CACxC,EAAE,MAAM,GAAG,GAAG,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC;CACvD,EAAE,UAAU,EAAE,mBAAmB,EAAE,GAAG,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;CACrE,EAAE,UAAU,EAAE,mBAAmB,EAAE,GAAG,EAAE,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;AACtE;CACA,EAAE,MAAM,cAAc,GAAG,OAAO,CAAC,aAAa,EAAE,SAAS,EAAE,CAAC;CAC5D,EAAE,MAAM,eAAe,GAAG,OAAO,CAAC,aAAa,EAAE,UAAU,EAAE,CAAC;CAC9D,EAAE,KAAK,cAAc,IAAI,eAAe,GAAG;AAC3C;CACA;CACA,GAAG,MAAM,GAAG,SAAS;CACrB,IAAI,YAAY,EAAE,GAAG,EAAE,UAAU,EAAE,UAAU,EAAE,oBAAoB;CACnE,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC;CAClE,IAAI,OAAO,EAAE,QAAQ;CACrB,IAAI,IAAI,SAAS;CACjB,IAAI,YAAY,EAAE,GAAG,EAAE,UAAU,EAAE,UAAU,EAAE,oBAAoB;CACnE,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC;CAClE,IAAI,OAAO,EAAE,QAAQ;CACrB,IAAI,CAAC;AACL;CACA,GAAG,MAAM,KAAK,cAAc,GAAG;AAC/B;CACA,GAAG,KAAK,OAAO,GAAG;AAClB;CACA;CACA,IAAI,MAAM,GAAG,SAAS;CACtB,KAAK,YAAY,EAAE,GAAG,EAAE,UAAU,EAAE,UAAU,EAAE,oBAAoB;CACpE,KAAK,oBAAoB,EAAE,oBAAoB,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC;CACnE,KAAK,OAAO,EAAE,QAAQ;CACtB,KAAK,CAAC;AACN;CACA,IAAI,MAAM;AACV;CACA;CACA;CACA,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;CAC3C,IAAI,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;AACxD;CACA,IAAI,MAAM,GAAG,GAAG,SAAS,EAAE,YAAY,EAAE,CAAC;CAC1C,IAAI,MAAM,GAAG,GAAG,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC;CACzD,IAAI,UAAU,EAAE,mBAAmB,EAAE,GAAG,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;CACvE,IAAI,UAAU,EAAE,mBAAmB,EAAE,GAAG,EAAE,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;AACxE;CACA;CACA,IAAI,MAAM,YAAY,GAAG,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,CAAC;CAC3D,IAAI,MAAM,YAAY,GAAG,MAAM,CAAC,aAAa,EAAE,UAAU,EAAE,CAAC;CAC5D,IAAI,MAAM,GAAG;CACb,KAAK,YAAY,IAAI,SAAS;CAC9B,MAAM,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,UAAU,EAAE,oBAAoB;CAC5D,MAAM,oBAAoB,EAAE,oBAAoB,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC;CACpE,MAAM,MAAM,EAAE,EAAE,QAAQ;CACxB,MAAM;CACN;CACA,KAAK,YAAY,IAAI,SAAS;CAC9B,MAAM,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,UAAU,EAAE,oBAAoB;CAC5D,MAAM,oBAAoB,EAAE,oBAAoB,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC;CACpE,MAAM,MAAM,EAAE,EAAE,QAAQ;CACxB,MAAM;CACN,KAAK,CAAC;AACN;CACA,IAAI,QAAQ,CAAC,gBAAgB,EAAE,MAAM,EAAE,CAAC;AACxC;CACA,IAAI;AACJ;CACA,GAAG,MAAM,KAAK,eAAe,GAAG;AAChC;CACA,GAAG,KAAK,OAAO,GAAG;AAClB;CACA;CACA,IAAI,MAAM,GAAG,SAAS;CACtB,KAAK,YAAY,EAAE,GAAG,EAAE,UAAU,EAAE,UAAU,EAAE,oBAAoB;CACpE,KAAK,oBAAoB,EAAE,oBAAoB,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC;CACnE,KAAK,OAAO,EAAE,QAAQ;CACtB,KAAK,CAAC;AACN;CACA,IAAI,MAAM;AACV;CACA;CACA;CACA,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;CAC3C,IAAI,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;AACzD;CACA,IAAI,MAAM,GAAG,GAAG,SAAS,EAAE,YAAY,EAAE,CAAC;CAC1C,IAAI,MAAM,GAAG,GAAG,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC;CACzD,IAAI,UAAU,EAAE,mBAAmB,EAAE,GAAG,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;CACvE,IAAI,UAAU,EAAE,mBAAmB,EAAE,GAAG,EAAE,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;AACxE;CACA;CACA,IAAI,MAAM,YAAY,GAAG,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,CAAC;CAC3D,IAAI,MAAM,YAAY,GAAG,MAAM,CAAC,aAAa,EAAE,UAAU,EAAE,CAAC;CAC5D,IAAI,MAAM,GAAG;CACb,KAAK,YAAY,IAAI,SAAS;CAC9B,MAAM,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,UAAU,EAAE,oBAAoB;CAC5D,MAAM,oBAAoB,EAAE,oBAAoB,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC;CACpE,MAAM,MAAM,EAAE,EAAE,QAAQ;CACxB,MAAM;CACN;CACA,KAAK,YAAY,IAAI,SAAS;CAC9B,MAAM,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,UAAU,EAAE,oBAAoB;CAC5D,MAAM,oBAAoB,EAAE,oBAAoB,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC;CACpE,MAAM,MAAM,EAAE,EAAE,QAAQ;CACxB,MAAM;CACN,KAAK,CAAC;AACN;CACA,IAAI,QAAQ,CAAC,gBAAgB,EAAE,MAAM,EAAE,CAAC;AACxC;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,MAAM,CAAC;AACf;CACA;;CC9RA,MAAM,GAAG,mBAAmB,IAAI,WAAW,EAAE,CAAC;CAC9C,MAAM,OAAO,mBAAmB,IAAIG,UAAI,EAAE,CAAC;CACpC,MAAM,eAAe,GAAG;CAC/B,CAAC,QAAQ,EAAE,MAAM;CACjB,CAAC,QAAQ,EAAE,EAAE;CACb,CAAC,WAAW,EAAE,EAAE;CAChB,CAAC,oBAAoB,EAAE,KAAK;CAC5B,CAAC,cAAc,EAAE,IAAI;CACrB,CAAC,UAAU,EAAE,IAAI;CACjB,CAAC,QAAQ,EAAE,KAAK;CAChB,CAAC,OAAO,EAAE,IAAI;CACd,CAAC,KAAK,EAAE,IAAI;CACZ,CAAC,CAAC;AACF;CACO,MAAM,OAAO,CAAC;AACrB;CACA,CAAC,OAAO,SAAS,EAAE,GAAG,EAAE,OAAO,GAAG,EAAE,GAAG;AACvC;CACA,EAAE,OAAO,GAAG;CACZ,GAAG,YAAY,EAAE,IAAI;CACrB,GAAG,GAAG,OAAO;CACb,GAAG,CAAC;AACJ;CACA,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;CAChC,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC;CAC9B,EAAE,MAAM,cAAc,GAAG,GAAG,CAAC,eAAe,CAAC;CAC7C,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;CAC7C,EAAE,IAAI,MAAM,CAAC;CACb,EAAE,KAAK,OAAO,CAAC,YAAY,GAAG;AAC9B;CACA,GAAG,MAAM,GAAG;CACZ,IAAI,KAAK,EAAE,QAAQ,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE;CAC/C,IAAI,KAAK,EAAE,cAAc,GAAG,cAAc,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,IAAI;CAC/D,IAAI,cAAc,EAAE,cAAc,GAAG,cAAc,CAAC,KAAK,EAAE,GAAG,IAAI;CAClE,IAAI,CAAC;AACL;CACA,GAAG,MAAM;AACT;CACA,GAAG,MAAM,GAAG;CACZ,IAAI,KAAK,EAAE,QAAQ;CACnB,IAAI,KAAK,EAAE,cAAc,GAAG,cAAc,CAAC,KAAK,GAAG,IAAI;CACvD,IAAI,cAAc,EAAE,cAAc;CAClC,IAAI,CAAC;AACL;CACA,GAAG;AACH;CACA,EAAE,OAAO,MAAM,CAAC;AAChB;CACA,EAAE;AACF;CACA,CAAC,OAAO,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,GAAG,EAAE,GAAG;AACpD;CACA,EAAE,OAAO,GAAG;CACZ,GAAG,QAAQ,EAAE,IAAI;CACjB,GAAG,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE;CAC3C,GAAG,GAAG,OAAO;CACb,GAAG,CAAC;AACJ;CACA,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;CAChD,EAAE,MAAM,GAAG,GAAG,IAAI,OAAO,EAAE,QAAQ,EAAE,EAAE,GAAG,OAAO,EAAE,EAAE,eAAe,IAAI,IAAI,EAAE,EAAE,CAAC;CACjF,EAAE,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC;CACrB,EAAE,GAAG,CAAC,eAAe,GAAG,cAAc,IAAI,IAAI,CAAC;AAC/C;CACA,EAAE,KAAK,OAAO,CAAC,QAAQ,GAAG;AAC1B;CACA,GAAG,MAAM,cAAc,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;CAC9C,GAAG,KAAK,cAAc,KAAK,IAAI,GAAG;AAClC;CACA,IAAI,MAAM,QAAQ,GAAG,IAAIV,qBAAe,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC;CACjE,IAAI,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC;AAClC;CACA,IAAI,MAAM,KAAK,cAAc,CAAC,KAAK,KAAK,KAAK,GAAG;AAChD;CACA,IAAI,cAAc,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC;CACtC,IAAI,cAAc,CAAC,WAAW,GAAG,IAAI,CAAC;AACtC;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,OAAO,GAAG,CAAC;AACb;CACA,EAAE;AACF;CACA,CAAC,IAAI,QAAQ,GAAG;AAChB;CACA,EAAE,OAAO,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC;AAClC;CACA,EAAE;AACF;CACA,CAAC,WAAW,EAAE,QAAQ,EAAE,OAAO,GAAG,EAAE,GAAG;AACvC;CACA,EAAE,KAAK,EAAE,QAAQ,CAAC,gBAAgB,GAAG;AACrC;CACA,GAAG,MAAM,IAAI,KAAK,EAAE,+CAA+C,EAAE,CAAC;AACtE;CACA,GAAG,MAAM,KAAK,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,4BAA4B,GAAG;AAC9E;CACA,GAAG,MAAM,IAAI,KAAK,EAAE,+EAA+E,EAAE,CAAC;AACtG;CACA,GAAG;AACH;CACA;CACA,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE;AAC3B;CACA,GAAG,GAAG,eAAe;AACrB;CACA;AACA;CACA;CACA,GAAG,EAAE,eAAe,IAAI,KAAK;AAC7B;CACA,GAAG,EAAE,OAAO,EAAE,CAAC;AACf;CACA,EAAE,KAAK,OAAO,CAAC,oBAAoB,IAAI,EAAE,4BAA4B,EAAE,GAAG;AAC1E;CACA,GAAG,MAAM,IAAI,KAAK,EAAE,8CAA8C,EAAE,CAAC;AACrE;CACA,GAAG;AACH;CACA;CACA;CACA,EAAE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;CAC3B,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;CACrB,EAAE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;CAC9B,EAAE,KAAK,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG;AACtC;CACA,GAAG,eAAe,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;AACpC;CACA,GAAG,KAAK,EAAE,QAAQ,CAAC,WAAW,IAAI,OAAO,CAAC,cAAc,GAAG;AAC3D;CACA,IAAI,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,IAAIU,UAAI,EAAE,EAAE,CAAC;AAC7D;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,QAAQ,GAAG,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AACzF;CACA,EAAE;AACF;CACA,CAAC,KAAK,EAAE,WAAW,GAAG,IAAI,GAAG;AAC7B;CACA,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,GAAG,cAAc,GAAG,KAAK,CAAC;CAC3D,EAAE,OAAO,SAAS,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;AACxC;CACA,EAAE;AACF;CACA,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,GAAG,CAAC,GAAG;AACrC;CACA,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC;CAC1C,EAAE,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;CAChD,EAAE,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;CAChD,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;AACjB;CACA,EAAE,SAAS,SAAS,EAAE,WAAW,EAAE,KAAK,GAAG,CAAC,GAAG;AAC/C;CACA,GAAG,MAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;CACvC,GAAG,MAAM,MAAM,GAAG,WAAW,EAAE,WAAW,GAAG,EAAE,EAAE,KAAK,gBAAgB,CAAC;CACvE,GAAG,KAAK,MAAM,GAAG;AACjB;CACA,IAAI,MAAM,MAAM,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;CAClD,IAAI,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,EAAE,EAAE,CAAC;CAClD,IAAI,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,YAAY,EAAE,MAAM,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AAC7F;CACA,IAAI,MAAM;AACV;CACA;CACA,IAAI,MAAM,IAAI,GAAG,WAAW,GAAG,cAAc,GAAG,CAAC,CAAC;CAClD,IAAI,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;CACjD,IAAI,MAAM,SAAS,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;CACrD,IAAI,MAAM,aAAa,GAAG,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,YAAY,EAAE,MAAM,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC;AAC/G;CACA,IAAI,KAAK,EAAE,aAAa,GAAG;AAC3B;CACA,KAAK,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;CAClC,KAAK,SAAS,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;AACnC;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;CACA,CAAC,OAAO,EAAE,GAAG,EAAE,cAAc,GAAGkB,eAAS,EAAE,IAAI,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ,GAAG;AACtE;CACA,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;CAC5B,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,UAAU,GAAG,EAAE,CAAC;CACxB,EAAE,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;CAC/C,EAAE,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,EAAE,cAAc,EAAE,CAAC;AAC1D;CACA,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;CACjC,EAAE,MAAM,IAAI,GAAG,UAAU,GAAG,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC;CACjE,EAAE,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAG,gBAAgB,GAAG,OAAO,CAAC;CACjE,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;CACA,GAAG,MAAM,YAAY,GAAG,eAAe,GAAG,cAAc,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;CAClG,GAAG,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;AACxC;CACA,GAAG,WAAW,EAAE,IAAI,EAAE,CAAC,EAAE,YAAY,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AACpE;CACA,GAAG,KAAK,eAAe,GAAG;AAC1B;CACA,IAAI,MAAM,aAAa,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,aAAa,CAAC;CACpD,IAAI,MAAM,IAAI,CAAC,GAAG,UAAU,EAAE,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG;AACrE;CACA,KAAK,UAAU,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACxD;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,OAAO,UAAU,CAAC;AACpB;CACA,EAAE;AACF;CACA,CAAC,YAAY,EAAE,GAAG,EAAE,cAAc,GAAGA,eAAS,EAAE,IAAI,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ,GAAG;AAC3E;CACA,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;CAC5B,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;CAC/C,EAAE,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,EAAE,cAAc,EAAE,CAAC;AAC1D;CACA,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC;AAC3B;CACA,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;CACjC,EAAE,MAAM,IAAI,GAAG,UAAU,GAAG,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC;CACjE,EAAE,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,GAAG,qBAAqB,GAAG,YAAY,CAAC;CAChF,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;CACA,GAAG,MAAM,YAAY,GAAG,eAAe,GAAG,cAAc,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;CAClG,GAAG,MAAM,MAAM,GAAG,gBAAgB,EAAE,IAAI,EAAE,CAAC,EAAE,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;CAC5E,GAAG,KAAK,MAAM,IAAI,IAAI,MAAM,aAAa,IAAI,IAAI,IAAI,MAAM,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,EAAE,GAAG;AAClG;CACA,IAAI,aAAa,GAAG,MAAM,CAAC;CAC3B,IAAI,KAAK,eAAe,GAAG;AAC3B;CACA,KAAK,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,aAAa,CAAC;AAC3D;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,OAAO,aAAa,CAAC;AACvB;CACA,EAAE;AACF;CACA,CAAC,kBAAkB,EAAE,aAAa,EAAE,UAAU,GAAG;AACjD;CACA,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC;CACrB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;CAC5B,EAAE,MAAM,sBAAsB,GAAG,IAAI,CAAC,QAAQ,GAAG,2BAA2B,GAAG,kBAAkB,CAAC;CAClG,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;CACA,GAAG,MAAM,GAAG,sBAAsB,EAAE,IAAI,EAAE,CAAC,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;AACzE;CACA,GAAG,KAAK,MAAM,GAAG;AACjB;CACA,IAAI,MAAM;AACV;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,OAAO,MAAM,CAAC;AAChB;CACA,EAAE;AACF;CACA,CAAC,SAAS,EAAE,SAAS,GAAG;AACxB;CACA,EAAE,MAAM,QAAQ,GAAG,oBAAoB,CAAC,YAAY,EAAE,CAAC;CACvD,EAAE,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAG,6BAA6B,GAAG,oBAAoB,CAAC;CAC3F,EAAE,IAAI;CACN,GAAG,mBAAmB;CACtB,GAAG,gBAAgB;CACnB,GAAG,eAAe;CAClB,GAAG,kBAAkB;CACrB,GAAG,GAAG,SAAS,CAAC;AAChB;CACA;CACA,EAAE,KAAK,eAAe,IAAI,kBAAkB,GAAG;AAC/C;CACA,GAAG,MAAM,uBAAuB,GAAG,eAAe,CAAC;CACnD,GAAG,eAAe,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,MAAM;AACvE;CACA,IAAI,KAAK,EAAE,uBAAuB,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG;AACnF;CACA,KAAK,OAAO,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,kBAAkB,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;AAC/F;CACA,KAAK;AACL;CACA,IAAI,OAAO,IAAI,CAAC;AAChB;CACA,IAAI,CAAC;AACL;CACA,GAAG,MAAM,KAAK,EAAE,eAAe,GAAG;AAClC;CACA,GAAG,KAAK,kBAAkB,GAAG;AAC7B;CACA,IAAI,eAAe,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,MAAM;AAC7D;CACA,KAAK,OAAO,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,kBAAkB,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;AAC/F;CACA,KAAK,CAAC;AACN;CACA,IAAI,MAAM;AACV;CACA,IAAI,eAAe,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,MAAM;AACtD;CACA,KAAK,OAAO,SAAS,CAAC;AACtB;CACA,KAAK,CAAC;AACN;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC;CACrB,EAAE,IAAI,UAAU,GAAG,CAAC,CAAC;CACrB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;CAC5B,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;CACA,GAAG,MAAM,IAAI,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC;CAC3B,GAAG,MAAM,GAAG,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,EAAE,UAAU,EAAE,CAAC;AACrG;CACA,GAAG,KAAK,MAAM,GAAG;AACjB;CACA,IAAI,MAAM;AACV;CACA,IAAI;AACJ;CACA,GAAG,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC;AACjC;CACA,GAAG;AACH;CACA,EAAE,oBAAoB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC;AACpD;CACA,EAAE,OAAO,MAAM,CAAC;AAChB;CACA,EAAE;AACF;CACA,CAAC,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,GAAG;AAC/C;CACA,EAAE,IAAI;CACN,GAAG,gBAAgB;CACnB,GAAG,mBAAmB;CACtB,GAAG,GAAG,SAAS,CAAC;AAChB;CACA,EAAE,MAAM,SAAS,GAAG,oBAAoB,CAAC,YAAY,EAAE,CAAC;CACxD,EAAE,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;CACzC,EAAE,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;CAC1D,EAAE,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ;CACvC,GAAG,EAAE,IAAI;AACT;AACA;CACA,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,oBAAoB,EAAE,EAAE,EAAE,CAAC;CAC/C,IAAI,WAAW,EAAE,SAAS,EAAE,EAAE,GAAG,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;AAChE;CACA,IAAI;CACJ,GAAG,EAAE,IAAI;AACT;CACA,IAAI,WAAW,EAAE,SAAS,EAAE,EAAE,GAAG,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;AAChE;CACA,IAAI,CAAC;AACL;CACA,EAAE,MAAM,SAAS,GAAG,oBAAoB,CAAC,YAAY,EAAE,CAAC;CACxD,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;CAC7C,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;CAC9D,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,QAAQ;CAC3C,GAAG,EAAE,IAAI;AACT;CACA,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,oBAAoB,EAAE,EAAE,EAAE,CAAC;CACpD,IAAI,WAAW,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;AACjE;CACA,IAAI;CACJ,GAAG,EAAE,IAAI;AACT;CACA,IAAI,WAAW,EAAE,SAAS,EAAE,EAAE,GAAG,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;AAChE;CACA,IAAI,CAAC;AACL;CACA;CACA,EAAE,KAAK,mBAAmB,GAAG;AAC7B;CACA,GAAG,MAAM,0BAA0B,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,MAAM;AAC9G;CACA,IAAI,MAAM,IAAI,EAAE,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,GAAG,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG;AACpE;CACA,KAAK,eAAe,EAAE,EAAE,EAAE,CAAC;AAC3B;CACA,KAAK,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAC/C,KAAK,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAC/C,KAAK,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAC/C,KAAK,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;AAClC;CACA,KAAK,MAAM,IAAI,EAAE,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,GAAG,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG;AACrE;CACA,MAAM,eAAe,EAAE,EAAE,EAAE,CAAC;AAC5B;CACA,MAAM,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;AACnC;CACA,MAAM,KAAK,mBAAmB,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG;AACjG;CACA,OAAO,OAAO,IAAI,CAAC;AACnB;CACA,OAAO;AACP;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA,IAAI,OAAO,KAAK,CAAC;AACjB;CACA,IAAI,CAAC;AACL;CACA,GAAG,KAAK,gBAAgB,GAAG;AAC3B;CACA,IAAI,MAAM,wBAAwB,GAAG,gBAAgB,CAAC;CACtD,IAAI,gBAAgB,GAAG,WAAW,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG;AACrG;CACA,KAAK,KAAK,EAAE,wBAAwB,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG;AAC3G;CACA,MAAM,OAAO,0BAA0B,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC5G;CACA,MAAM;AACN;CACA,KAAK,OAAO,IAAI,CAAC;AACjB;CACA,KAAK,CAAC;AACN;CACA,IAAI,MAAM;AACV;CACA,IAAI,gBAAgB,GAAG,0BAA0B,CAAC;AAClD;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,OAAO,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,gBAAgB,EAAE,CAAC;AACpE;CACA,EAAE;AACF;AACA;CACA;CACA,CAAC,aAAa,EAAE,GAAG,EAAE,SAAS,GAAG;AACjC;CACA,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,SAAS,EAAE,CAAC;CACzC,EAAE,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC;AACzB;CACA,EAAE,OAAO,IAAI,CAAC,SAAS;CACvB,GAAG;CACH,IAAI,gBAAgB,EAAE,GAAG,IAAI,GAAG,CAAC,aAAa,EAAE,GAAG,EAAE;CACrD,IAAI,kBAAkB,EAAE,GAAG,IAAI,GAAG,CAAC,kBAAkB,EAAE,GAAG,EAAE;CAC5D,IAAI;CACJ,GAAG,CAAC;AACJ;CACA,EAAE;AACF;CACA,CAAC,gBAAgB,EAAE,MAAM,GAAG;AAC5B;CACA,EAAE,OAAO,IAAI,CAAC,SAAS;CACvB,GAAG;CACH,IAAI,gBAAgB,EAAE,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,GAAG,EAAE;CACxD,IAAI,kBAAkB,EAAE,GAAG,IAAI,GAAG,CAAC,gBAAgB,EAAE,MAAM,EAAE;CAC7D,IAAI;CACJ,GAAG,CAAC;AACJ;CACA,EAAE;AACF;CACA,CAAC,sBAAsB,EAAE,aAAa,EAAE,aAAa,EAAE,OAAO,GAAG,GAAG,EAAE,OAAO,GAAG,GAAG,EAAE,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,QAAQ,GAAG;AACjI;CACA,EAAE,MAAM,0BAA0B,GAAG,IAAI,CAAC,QAAQ,GAAG,+BAA+B,GAAG,sBAAsB,CAAC;CAC9G,EAAE,OAAO,0BAA0B;CACnC,GAAG,IAAI;CACP,GAAG,aAAa;CAChB,GAAG,aAAa;CAChB,GAAG,OAAO;CACV,GAAG,OAAO;CACV,GAAG,YAAY;CACf,GAAG,YAAY;CACf,GAAG,CAAC;AACJ;CACA,EAAE;AACF;CACA,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG,EAAE,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,QAAQ,GAAG;AACvF;CACA,EAAE,OAAO,mBAAmB;CAC5B,GAAG,IAAI;CACP,GAAG,KAAK;CACR,GAAG,MAAM;CACT,GAAG,YAAY;CACf,GAAG,YAAY;CACf,GAAG,CAAC;AACJ;CACA,EAAE;AACF;CACA,CAAC,cAAc,EAAE,MAAM,GAAG;AAC1B;CACA,EAAE,MAAM,CAAC,SAAS,EAAE,CAAC;AACrB;CACA,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;CAC5B,EAAE,KAAK,CAAC,OAAO,EAAE,MAAM,IAAI;AAC3B;CACA,GAAG,UAAU,EAAE,CAAC,EAAE,IAAI,YAAY,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,CAAC;CACxD,GAAG,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC;AAC3B;CACA,GAAG,EAAE,CAAC;AACN;CACA,EAAE,OAAO,MAAM,CAAC;AAChB;CACA,EAAE;AACF;CACA;;CC/hBA,MAAM,WAAW,mBAAmB,IAAIlB,UAAI,EAAE,CAAC;CAC/C,MAAM,MAAM,mBAAmB,IAAIH,aAAO,EAAE,CAAC;AAC7C;CACA,MAAM,iBAAiB,SAASsB,cAAQ,CAAC;AACzC;CACA,CAAC,IAAI,MAAM,GAAG;AACd;CACA,EAAE,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC;AAC7B;CACA,EAAE;AACF;CACA,CAAC,IAAI,cAAc,GAAG;AACtB;CACA,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC;AAC3B;CACA,EAAE;AACF;CACA,CAAC,IAAI,MAAM,GAAG;AACd;CACA,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC;AAC3B;CACA,EAAE;AACF;CACA,CAAC,iBAAiB,EAAE,GAAG,IAAI,GAAG;AAC9B;CACA;CACA,EAAE,OAAOC,UAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC;AAChE;CACA,EAAE;AACF;CACA,CAAC,WAAW,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,GAAG,EAAE,EAAE,KAAK,GAAG,CAAC,GAAG;AACrD;CACA,EAAE,KAAK,EAAE,CAAC;AACV;CACA,EAAE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;CAC3B,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAIC,oBAAc,EAAE,CAAC;CACvC,EAAE,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC;CAClC,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACrB,EAAE,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;CAC9B,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;CACjB,EAAE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;CAC3B,EAAE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACtB;CACA,EAAE;AACF;CACA,CAAC,OAAO,GAAG,EAAE;AACb;CACA,CAAC,MAAM,GAAG;AACV;CACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC;CAC9B,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;CAC5B,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC;CACrB,EAAE,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;CACvB,EAAE,KAAK,UAAU,GAAG;AACpB;CACA;CACA,GAAG,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;CACtC,GAAG,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;CAC9C,GAAG,IAAI,WAAW,GAAG,CAAC,CAAC;CACvB,GAAG,UAAU,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,MAAM;AAC7C;CACA,IAAI,KAAK,KAAK,IAAI,WAAW,IAAI,MAAM,GAAG;AAC1C;CACA,KAAK,WAAW,GAAG,CAAC;CACpB,KAAK,OAAO,IAAI,CAAC;AACjB;CACA,KAAK,MAAM,KAAK,cAAc,GAAG;AACjC;CACA,KAAK,WAAW,GAAG,CAAC;AACpB;CACA,KAAK;AACL;CACA,IAAI,EAAE,KAAK,EAAE,CAAC;AACd;CACA;CACA,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAC;CACpB,GAAG,MAAM,aAAa,GAAG,IAAI,YAAY,EAAE,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC;CACjE,GAAG,UAAU,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,MAAM;AAC3D;CACA,IAAI,MAAM,SAAS,GAAG,KAAK,IAAI,WAAW,IAAI,MAAM,CAAC;CACrD,IAAI,KAAK,SAAS,IAAI,cAAc,GAAG;AACvC;CACA,KAAK,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,WAAW,EAAE,CAAC;AAChD;CACA,KAAK,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,WAAW,CAAC;CACtC,KAAK,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AACzC;CACA,MAAM,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CACzC,MAAM,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AAC1C;CACA,OAAO,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CAC1C,OAAO,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AAC3C;CACA,QAAQ,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CAC3C,QAAQ,aAAa,EAAE,QAAQ,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;CAC7C,QAAQ,aAAa,EAAE,QAAQ,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;CAC7C,QAAQ,aAAa,EAAE,QAAQ,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC7C;CACA,QAAQ,QAAQ,IAAI,CAAC,CAAC;AACtB;CACA,QAAQ;AACR;CACA,OAAO;AACP;CACA,MAAM;AACN;CACA,KAAK,OAAO,SAAS,CAAC;AACtB;CACA,KAAK;AACL;CACA,IAAI,EAAE,KAAK,EAAE,CAAC;AACd;CACA,GAAG,IAAI,UAAU,CAAC;CAClB,GAAG,IAAI,OAAO,CAAC;CACf,GAAG,KAAK,IAAI,CAAC,YAAY,GAAG;AAC5B;CACA;CACA,IAAI,OAAO,GAAG,IAAI,UAAU,EAAE;CAC9B;CACA,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;AACT;CACA;CACA,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;AACT;CACA;CACA,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,EAAE,CAAC;AACR;CACA,IAAI,MAAM;AACV;CACA,IAAI,OAAO,GAAG,IAAI,UAAU,EAAE;AAC9B;CACA;CACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;CACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;CACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;CACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;CACA;CACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;CACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;CACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;CACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;CACA;CACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;CACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;CACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;CACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;CACA,KAAK,EAAE,CAAC;AACR;CACA,IAAI;AACJ;CACA,GAAG,KAAK,aAAa,CAAC,MAAM,GAAG,KAAK,GAAG;AACvC;CACA,IAAI,UAAU,GAAG,IAAI,WAAW,EAAE,OAAO,CAAC,MAAM,GAAG,WAAW,EAAE,CAAC;AACjE;CACA,IAAI,MAAM;AACV;CACA,IAAI,UAAU,GAAG,IAAI,WAAW,EAAE,OAAO,CAAC,MAAM,GAAG,WAAW,EAAE,CAAC;AACjE;CACA,IAAI;AACJ;CACA,GAAG,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC;CACtC,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,GAAG;AAC5C;CACA,IAAI,MAAM,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;CAC5B,IAAI,MAAM,WAAW,GAAG,CAAC,GAAG,WAAW,CAAC;CACxC,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,GAAG;AAC7C;CACA,KAAK,UAAU,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,SAAS,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;AAC9D;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA;CACA,GAAG,QAAQ,CAAC,QAAQ;CACpB,IAAI,IAAI/B,qBAAe,EAAE,UAAU,EAAE,CAAC,EAAE,KAAK,EAAE;CAC/C,IAAI,CAAC;CACL,GAAG,QAAQ,CAAC,YAAY;CACxB,IAAI,UAAU;CACd,IAAI,IAAIA,qBAAe,EAAE,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE;CAClD,IAAI,CAAC;CACL,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACvB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,MAAM,aAAa,SAASgC,WAAK,CAAC;AAClC;CACA,CAAC,IAAI,KAAK,GAAG;AACb;CACA,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;AACjC;CACA,EAAE;AACF;CACA,CAAC,IAAI,OAAO,GAAG;AACf;CACA,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;AACnC;CACA,EAAE;AACF;CACA,CAAC,IAAI,OAAO,EAAE,CAAC,GAAG;AAClB;CACA,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,CAAC;CAChC,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,CAAC;AAChC;CACA,EAAE;AACF;CACA,CAAC,WAAW,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,GAAG,IAAI,EAAE,KAAK,GAAG,EAAE,GAAG;AACpD;CACA;CACA,EAAE,KAAK,IAAI,YAAY,OAAO,GAAG;AACjC;CACA,GAAG,KAAK,GAAG,GAAG,IAAI,EAAE,CAAC;CACrB,GAAG,GAAG,GAAG,IAAI,CAAC;CACd,GAAG,IAAI,GAAG,IAAI,CAAC;AACf;CACA,GAAG;AACH;CACA;CACA,EAAE,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG;AACjC;CACA,GAAG,KAAK,GAAG,GAAG,CAAC;CACf,GAAG,GAAG,GAAG,IAAI,CAAC;AACd;CACA,GAAG;AACH;CACA,EAAE,KAAK,EAAE,CAAC;AACV;CACA,EAAE,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;CAC9B,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACrB,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;CACnB,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;CACjB,EAAE,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;CAC9B,EAAE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;CAC3B,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;CACvB,EAAE,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AACnB;CACA,EAAE,MAAM,YAAY,GAAG,IAAIC,uBAAiB,EAAE;CAC9C,GAAG,KAAK,EAAE,QAAQ;CAClB,GAAG,WAAW,EAAE,IAAI;CACpB,GAAG,OAAO,EAAE,GAAG;CACf,GAAG,UAAU,EAAE,KAAK;CACpB,GAAG,EAAE,CAAC;AACN;CACA,EAAE,MAAM,YAAY,GAAG,IAAIC,uBAAiB,EAAE;CAC9C,GAAG,KAAK,EAAE,QAAQ;CAClB,GAAG,WAAW,EAAE,IAAI;CACpB,GAAG,OAAO,EAAE,GAAG;CACf,GAAG,UAAU,EAAE,KAAK;CACpB,GAAG,EAAE,CAAC;AACN;CACA,EAAE,YAAY,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;AAC1C;CACA,EAAE,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;CACnC,EAAE,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACnC;CACA,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;AAChB;CACA,EAAE;AACF;CACA,CAAC,MAAM,GAAG;AACV;CACA,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CACzB,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,IAAI,CAAC;CACzD,EAAE,KAAK,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE,GAAG,GAAG;AACzD;CACA;CACA;CACA,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;CACvD,GAAG,KAAK,QAAQ,GAAG;AACnB;CACA,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,aAAa,EAAE,IAAI,GAAG,CAAC;AAC5D;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,MAAM,UAAU,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;CACjD,EAAE,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,GAAG;AAC5C;CACA,GAAG,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;CAClC,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;CAC3B,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC;AACvB;CACA,GAAG;AACH;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,GAAG;AAC1C;CACA,GAAG,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC;AACpF;CACA,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;AAClC;CACA,IAAI,MAAM,IAAI,GAAG,IAAI,iBAAiB,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;CACtE,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;CACrB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AAC7B;CACA,IAAI;AACJ;CACA,GAAG,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC;CACjC,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;CAClB,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACtB,GAAG,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;CACxC,GAAG,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;CACpC,GAAG,IAAI,CAAC,QAAQ,GAAG,YAAY,GAAG,YAAY,GAAG,YAAY,CAAC;CAC9D,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,iBAAiB,EAAE,GAAG,IAAI,GAAG;AAC9B;CACA,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CACzB,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B;CACA,EAAE,KAAK,IAAI,KAAK,IAAI,GAAG;AACvB;CACA,GAAG,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AACzC;CACA,GAAG,KAAK,MAAM,GAAG;AACjB;CACA,IAAI,IAAI,CAAC,MAAM;CACf,MAAM,IAAI,EAAE,MAAM,CAAC,WAAW,EAAE;CAChC,MAAM,MAAM,EAAE;CACd,MAAM,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;AACnC;CACA,IAAI,MAAM;AACV;CACA,IAAI,IAAI,CAAC,MAAM;CACf,MAAM,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;AAC/B;CACA,IAAI;AACJ;CACA;CACA,GAAG,KAAK,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,aAAa,GAAG;AACrD;CACA,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC;CACjD,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC;AACnC;CACA,IAAI;AACJ;CACA,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS;CACxB,IAAI,IAAI,CAAC,QAAQ;CACjB,IAAI,IAAI,CAAC,UAAU;CACnB,IAAI,IAAI,CAAC,KAAK;CACd,IAAI,CAAC;AACL;CACA,GAAG;AACH;CACA,EAAE,KAAK,CAAC,iBAAiB,EAAE,GAAG,IAAI,EAAE,CAAC;AACrC;CACA,EAAE;AACF;CACA,CAAC,IAAI,EAAE,MAAM,GAAG;AAChB;CACA,EAAE,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;CAC5B,EAAE,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;CAC1B,EAAE,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;CACxB,EAAE,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;CAChC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC;AAClC;CACA,EAAE;AACF;CACA,CAAC,KAAK,GAAG;AACT;CACA,EAAE,OAAO,IAAI,aAAa,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AAC9D;CACA,EAAE;AACF;CACA,CAAC,OAAO,GAAG;AACX;CACA,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;CAC9B,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;AAC9B;CACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACtD;CACA,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;AACpC;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,MAAM,iBAAiB,SAAS,aAAa,CAAC;AACrD;CACA,CAAC,WAAW,EAAE,GAAG,IAAI,GAAG;AACxB;CACA,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,CAAC;AACnB;CACA,EAAE,OAAO,CAAC,IAAI,EAAE,uFAAuF,EAAE,CAAC;AAC1G;CACA,EAAE;AACF;CACA;;CC9ZA,MAAM,KAAK,mBAAmB,IAAIxB,UAAI,EAAE,CAAC;CACzC,MAAM,KAAK,mBAAmB,IAAIA,UAAI,EAAE,CAAC;CACzC,MAAM,IAAI,mBAAmB,IAAIT,aAAO,EAAE,CAAC;AAC3C;CACA;CACA,SAAS,gBAAgB,EAAE,EAAE,GAAG;AAChC;CACA,CAAC,SAAS,OAAO,EAAE;AACnB;CACA,EAAE,KAAK,QAAQ;CACf,GAAG,OAAO,CAAC,CAAC;CACZ,EAAE,KAAK,QAAQ;CACf,GAAG,OAAO,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;CACxB,EAAE,KAAK,SAAS;CAChB,GAAG,OAAO,CAAC,CAAC;CACZ,EAAE;CACF,GAAG,OAAO,CAAC,CAAC;AACZ;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,SAAS,YAAY,EAAE,GAAG,GAAG;AAC7B;CACA,CAAC,MAAM,KAAK,GAAG,gCAAgC,CAAC;CAChD,CAAC,OAAO,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;AAC3C;CACA,CAAC;AACD;CACA,SAAS,eAAe,EAAE,GAAG,EAAE,KAAK,GAAG;AACvC;CACA,CAAC,MAAM,MAAM,GAAG;CAChB,EAAE,SAAS,EAAE,CAAC;CACd,EAAE,aAAa,EAAE,CAAC;AAClB;CACA,EAAE,KAAK,EAAE;CACT,GAAG,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE,QAAQ;CACjC,GAAG;CACH,EAAE,IAAI,EAAE;CACR,GAAG,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE,QAAQ;CACjC,GAAG;CACH,EAAE,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;CACrB,EAAE,gBAAgB,EAAE,CAAC;CACrB,EAAE,CAAC;AACH;CACA,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,KAAK,MAAM;AACxE;CACA,EAAE,MAAM,EAAE,GAAG,YAAY,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;CACvD,EAAE,MAAM,EAAE,GAAG,YAAY,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;CACvD,EAAE,MAAM,EAAE,GAAG,YAAY,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;AACvD;CACA,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC1D;CACA,EAAE,MAAM,CAAC,SAAS,GAAG,CAAC;CACtB,EAAE,KAAK,MAAM,GAAG;AAChB;CACA,GAAG,MAAM,CAAC,aAAa,GAAG,CAAC;AAC3B;CACA,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;CAC1D,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AAC1D;CACA,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;CACxD,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AACxD;CACA,GAAG,MAAM,CAAC,gBAAgB,IAAI,WAAW,GAAG,uBAAuB,GAAG,KAAK,CAAC;AAC5E;CACA,GAAG,MAAM;AACT;CACA,GAAG,MAAM,CAAC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAC;AACrC;CACA,GAAG,MAAM,CAAC,gBAAgB,IAAI,WAAW,GAAG,cAAc,CAAC;AAC3D;CACA,GAAG;AACH;CACA,EAAE,EAAE,KAAK,EAAE,CAAC;AACZ;CACA;CACA,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,QAAQ,GAAG;AACrC;CACA,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;CACtB,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACtB;CACA,EAAE;AACF;CACA,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,QAAQ,GAAG;AACtC;CACA,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;CACvB,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AACvB;CACA,EAAE;AACF;CACA,CAAC,OAAO,MAAM,CAAC;AACf;CACA,CAAC;AACD;CACA,SAAS,cAAc,EAAE,GAAG,GAAG;AAC/B;CACA,CAAC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC,MAAM,eAAe,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;AACnE;CACA,CAAC;AACD;CACA,SAAS,qBAAqB,EAAE,GAAG,GAAG;AACtC;CACA,CAAC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;CAC7B,CAAC,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;CACvB,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC;AACf;CACA,CAAC,QAAQ,KAAK,CAAC,MAAM,GAAG;AACxB;CACA,EAAE,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;CAC3B,EAAE,KAAK,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG;AAC/B;CACA,GAAG,SAAS;AACZ;CACA,GAAG;AACH;CACA,EAAE,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;AACxB;CACA,EAAE,MAAM,IAAI,GAAG,IAAI,IAAI,GAAG;AAC1B;CACA,GAAG,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG;AACvC;CACA,IAAI,SAAS;AACb;CACA,IAAI;AACJ;CACA,GAAG,KAAK,IAAI,gBAAgB,EAAE,GAAG,EAAE,CAAC;AACpC;CACA,GAAG,MAAM,KAAK,GAAG,IAAI,EAAE,GAAG,EAAE,CAAC;CAC7B,GAAG,KAAK,KAAK,MAAM,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,GAAG;AAChF;CACA,IAAI,KAAK,YAAY,EAAE,KAAK,EAAE,GAAG;AACjC;CACA,KAAK,KAAK,IAAI,KAAK,CAAC,UAAU,CAAC;AAC/B;CACA,KAAK,MAAM,KAAK,4BAA4B,EAAE,IAAI,KAAK,YAAY,iBAAiB,GAAG;AACvF;CACA,KAAK,KAAK,IAAI,KAAK,CAAC,UAAU,CAAC;AAC/B;CACA,KAAK,MAAM,KAAK,KAAK,YAAY,WAAW,GAAG;AAC/C;CACA,KAAK,KAAK,IAAI,KAAK,CAAC,UAAU,CAAC;AAC/B;CACA,KAAK,MAAM;AACX;CACA,KAAK,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACzB;CACA,KAAK;AACL;CACA,IAAI,MAAM;AACV;CACA,IAAI,KAAK,IAAI,gBAAgB,EAAE,KAAK,EAAE,CAAC;AACvC;CACA,IAAI;AACJ;AACA;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,KAAK,CAAC;AACd;CACA,CAAC;AACD;CACA,SAAS,cAAc,EAAE,GAAG,GAAG;AAC/B;CACA,CAAC,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;CAC/B,CAAC,MAAM,UAAU,GAAG,EAAE,CAAC;CACvB,CAAC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;CAC9B,CAAC,MAAM,QAAQ,GAAG,QAAQ,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;CACtD,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC;AACnB;CACA,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,MAAM;AACjE;CACA,EAAE,MAAM,IAAI,GAAG;CACf,GAAG,KAAK;CACR,GAAG,MAAM;CACT,GAAG,YAAY;CACf,GAAG,MAAM;CACT,GAAG,KAAK;CACR,GAAG,CAAC;CACJ,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;AAC7B;CACA,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;CACvC,EAAE,MAAM,MAAM,GAAG,UAAU,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;AACzC;CACA,EAAE,KAAK,MAAM,GAAG;AAChB;CACA;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC3D;CACA,IAAI,MAAM,QAAQ,GAAG,GAAG,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC;CACnD,IAAI,IAAI,EAAE,GAAG,CAAC,GAAG,QAAQ,CAAC;CAC1B,IAAI,IAAI,EAAE,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC;CAC9B,IAAI,IAAI,EAAE,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC;AAC9B;CACA,IAAI,KAAK,KAAK,GAAG;AACjB;CACA,KAAK,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CAC3B,KAAK,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CAC3B,KAAK,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AAC3B;CACA,KAAK;AACL;CACA,IAAI,IAAI,WAAW,CAAC;AACpB;CACA,IAAI,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;CAC7C,IAAI,WAAW,GAAG,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC;AAC9C;CACA,IAAI,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;CAC7C,IAAI,WAAW,GAAG,WAAW,IAAI,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC;AAC7D;CACA,IAAI,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;CAC7C,IAAI,WAAW,GAAG,WAAW,IAAI,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC;AAC7D;CACA,IAAI,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,8CAA8C,EAAE,CAAC;CAClF,IAAI,MAAM,GAAG,MAAM,IAAI,WAAW,CAAC;AACnC;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,KAAK,MAAM,GAAG;AAChB;CACA;CACA,GAAG,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;AACxC;CACA,GAAG,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC;CAClD,GAAG,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,6CAA6C,EAAE,CAAC;CAChF,GAAG,MAAM,GAAG,MAAM,IAAI,WAAW,CAAC;AAClC;CACA,GAAG;AACH;CACA,EAAE,EAAE,CAAC;AACL;CACA,CAAC,OAAO,MAAM,CAAC;AACf;CACA,CAAC;AACD;CACA;CACA,SAAS,gBAAgB,EAAE,GAAG,GAAG;AACjC;CACA,CAAC,MAAM,UAAU,GAAG,EAAE,CAAC;AACvB;CACA,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,MAAM;AACjE;CACA,EAAE,MAAM,IAAI,GAAG;CACf,GAAG,MAAM,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,IAAIS,UAAI,EAAE,EAAE;CACpD,GAAG,CAAC;AACJ;CACA,EAAE,KAAK,MAAM,GAAG;AAChB;CACA,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACtB,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACxB;CACA,GAAG,MAAM;AACT;CACA,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;CACpB,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACrB;CACA,GAAG;AACH;CACA,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;AAC7B;CACA;CACA,EAAE,MAAM,MAAM,GAAG,UAAU,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;CACzC,EAAE,KAAK,MAAM,GAAG;AAChB;CACA,GAAG,KAAK,MAAM,CAAC,IAAI,KAAK,IAAI,GAAG;AAC/B;CACA,IAAI,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;AACvB;CACA,IAAI,MAAM;AACV;CACA,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;AACxB;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,EAAE,CAAC;AACL;CACA,CAAC,OAAO,UAAU,EAAE,CAAC,EAAE,CAAC;AACxB;CACA;;CCjSA;CACA;CACO,SAAS,uBAAuB,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,GAAG;AAClE;CACA,CAAC,KAAK,GAAG,KAAK,IAAI,GAAG;AACrB;CACA,EAAE,OAAO,IAAI,CAAC;AACd;CACA,EAAE;AACF;CACA,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC;CAC9C,CAAC,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;CAC7D,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB;CACA,CAAC,OAAO,GAAG,CAAC;AACZ;CACA;;CCXA,MAAM,WAAW,GAAGyB,gBAAK,CAAC,WAAW,IAAI,IAAI,CAAC;CAC9C,MAAM,eAAe,GAAG,QAAQ,EAAEC,cAAQ,EAAE,IAAI,GAAG,CAAC;CACpD,MAAM,GAAG,mBAAmB,IAAIC,SAAG,EAAE,CAAC;CACtC,MAAM,SAAS,mBAAmB,IAAIpC,aAAO,EAAE,CAAC;CAChD,MAAM,gBAAgB,mBAAmB,IAAIM,aAAO,EAAE,CAAC;CACvD,MAAM,mBAAmB,GAAGuB,UAAI,CAAC,SAAS,CAAC,OAAO,CAAC;CACnD,MAAM,sBAAsB,GAAG,WAAW,KAAK,IAAI,GAAG,WAAW,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC;CAC3F,MAAM,WAAW,mBAAmB,IAAI7B,aAAO,EAAE,CAAC;CAClD,MAAM,KAAK,mBAAmB,IAAI6B,UAAI,EAAE,CAAC;CACzC,MAAM,gBAAgB,GAAG,EAAE,CAAC;AAC5B;CACO,SAAS,kBAAkB,EAAE,SAAS,EAAE,UAAU,GAAG;AAC5D;CACA,CAAC,KAAK,IAAI,CAAC,aAAa,GAAG;AAC3B;CACA,EAAE,6BAA6B,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;AACpE;CACA,EAAE,MAAM;AACR;CACA,EAAE,sBAAsB,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;AAC7D;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,SAAS,6BAA6B,EAAE,SAAS,EAAE,UAAU,GAAG;AAChE;CACA,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG;AACzB;CACA,EAAE,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;CACvC,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;CAClC,EAAE,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;CACtC,EAAE,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;AACvC;CACA,EAAE,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjC;CACA,EAAE,MAAM,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC;CAClD,EAAE,MAAM,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC;AAChD;CACA,EAAE,KAAK,KAAK,CAAC,QAAQ,CAAC,cAAc,KAAK,IAAI,GAAG;AAChD;CACA,GAAG,KAAK,CAAC,QAAQ,CAAC,cAAc,GAAG,IAAIxB,YAAM,EAAE,CAAC;AAChD;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACtD;CACA,GAAG,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,EAAE,GAAG;AACnC;CACA,IAAI,SAAS;AACb;CACA,IAAI;AACJ;CACA;CACA,GAAG,MAAM,UAAU,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC,aAAa,CAAC;AAClD;CACA,GAAG,KAAK,CAAC,QAAQ,CAAC,UAAU,GAAG,WAAW,EAAE,UAAU,EAAE,CAAC;AACzD;CACA,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,CAAC;AACvE;CACA,GAAG,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,UAAU,GAAG;AACtC;CACA,IAAI,IAAI,CAAC,gBAAgB,EAAE,UAAU,EAAE,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;CACpE,IAAI,IAAI,CAAC,mBAAmB,EAAE,UAAU,EAAE,KAAK,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;AAC1E;CACA,IAAI,MAAM,SAAS,GAAG,UAAU,EAAE,UAAU,EAAE,CAAC;CAC/C,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,EAAE,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE,CAAC;AACpE;CACA,IAAI;AACJ;CACA,GAAG,KAAK,CAAC,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC;AAChD;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC/D;CACA,IAAI,MAAM,SAAS,GAAG,gBAAgB,EAAE,CAAC,EAAE,CAAC;CAC5C,IAAI,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;CAC5B,IAAI,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC;CAC1B,IAAI,UAAU,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC;AACjC;CACA,IAAI;AACJ;CACA,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;AAC/B;CACA,GAAG;AACH;CACA,EAAE,KAAK,CAAC,QAAQ,CAAC,UAAU,GAAG,aAAa,CAAC;CAC5C,EAAE,KAAK,CAAC,QAAQ,CAAC,SAAS,GAAG,YAAY,CAAC;CAC1C,EAAE,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;CACxB,EAAE,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;AACxB;CACA,EAAE,MAAM;AACR;CACA,EAAE,sBAAsB,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;AAC7D;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,SAAS,sBAAsB,EAAE,SAAS,EAAE,UAAU,GAAG;AACzD;CACA,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG;AACjC;CACA,EAAE,KAAK,IAAI,CAAC,QAAQ,KAAK,SAAS,GAAG,OAAO;AAC5C;CACA,EAAE,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,CAAC;CACrD,EAAE,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,gBAAgB,EAAE,CAAC;AAC7D;CACA,EAAE,WAAW,CAAC,kBAAkB,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;CACrD,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,CAAC;AAC1D;CACA,EAAE,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;CACzC,EAAE,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,GAAG,WAAW,CAAC;CAC5C,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC,GAAG,GAAG,WAAW,CAAC;AAC1C;CACA,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;CACvC,EAAE,KAAK,SAAS,CAAC,YAAY,KAAK,IAAI,GAAG;AACzC;CACA,GAAG,MAAM,GAAG,GAAG,uBAAuB,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;CAC7G,GAAG,KAAK,GAAG,GAAG;AACd;CACA,IAAI,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AAC3B;CACA,IAAI;AACJ;CACA,GAAG,MAAM;AACT;CACA,GAAG,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;CAC7D,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;CACA,IAAI,MAAM,GAAG,GAAG,uBAAuB,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;CACtE,IAAI,KAAK,GAAG,GAAG;AACf;CACA,KAAK,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AAC5B;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,MAAM;AACR;CACA,EAAE,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;AAC1D;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,SAAS,iBAAiB,EAAE,OAAO,GAAG,EAAE,GAAG;AAClD;CACA,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;CAChD,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC;AACxB;CACA,CAAC;AACD;CACO,SAAS,iBAAiB,GAAG;AACpC;CACA,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACxB;CACA,CAAC;AACD;CACO,SAAS,wBAAwB,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE,OAAO,GAAG,EAAE,GAAG;AACtE;CACA,CAAC,KAAK,EAAE,eAAe,GAAG;AAC1B;CACA,EAAE,MAAM,IAAI,KAAK,EAAE,+DAA+D,EAAE,CAAC;AACrF;CACA,EAAE;AACF;CACA,CAAC,KAAK,OAAO,CAAC,QAAQ,GAAG;AACzB;CACA,EAAE,OAAO,CAAC,IAAI,EAAE,yEAAyE,EAAE,CAAC;AAC5F;CACA,EAAE;AACF;CACA,CAAC,OAAO,GAAG;CACX,EAAE,GAAG,OAAO;CACZ,EAAE,QAAQ,EAAE,KAAK;CACjB,EAAE,KAAK,EAAE,IAAI;CACb,EAAE,CAAC;AACH;CACA,CAAC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;CACrC,CAAC,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;CAC3C,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,GAAG;AAC3B;CACA,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,KAAK,EAAE,aAAa,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AAC7D;CACA,EAAE;AACF;CACA,CAAC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;CACtC,CAAC,QAAQ,WAAW,CAAC,MAAM,GAAG,aAAa,GAAG;AAC9C;CACA,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AAC3B;CACA,EAAE;AACF;CACA,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG;AAClB;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,GAAG,GAAG;AAC7C;CACA,GAAG,OAAO,CAAC,KAAK,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;CACnC,GAAG,WAAW,EAAE,CAAC,EAAE,GAAG,IAAI,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;AAC5D;CACA,GAAG;AACH;CACA,EAAE,OAAO,WAAW,CAAC;AACrB;CACA,EAAE,MAAM;AACR;CACA,EAAE,KAAK,KAAK,GAAG,UAAU,CAAC,MAAM,GAAG;AACnC;CACA,GAAG,OAAO,CAAC,KAAK,GAAG,UAAU,EAAE,KAAK,EAAE,CAAC;CACvC,GAAG,WAAW,EAAE,KAAK,EAAE,GAAG,IAAI,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;AAChE;CACA,GAAG;AACH;CACA,EAAE,OAAO,WAAW,EAAE,KAAK,EAAE,IAAI,IAAI,CAAC;AACtC;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,SAAS,wBAAwB,EAAE,KAAK,GAAG,EAAE,CAAC,GAAG;AACxD;CACA,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG;AAClB;CACA,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AAChC;CACA,EAAE,MAAM;AACR;CACA,EAAE,KAAK,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG;AACxC;CACA,GAAG,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;AACpC;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;;CChOA,SAAS,mBAAmB,EAAE,KAAK,GAAG;AACtC;CACA,CAAC,SAAS,KAAK;AACf;CACA,EAAE,KAAK,CAAC,EAAE,OAAO,GAAG,CAAC;CACrB,EAAE,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC;CACtB,EAAE,KAAK,CAAC,EAAE,OAAO,MAAM,CAAC;CACxB,EAAE,KAAK,CAAC,EAAE,OAAO,MAAM,CAAC;AACxB;CACA,EAAE;AACF;CACA,CAAC,MAAM,IAAI,KAAK,EAAE,CAAC;AACnB;CACA,CAAC;AACD;CACA,SAAS,aAAa,EAAE,KAAK,GAAG;AAChC;CACA,CAAC,SAAS,KAAK;AACf;CACA,EAAE,KAAK,CAAC,EAAE,OAAOgC,eAAS,CAAC;CAC3B,EAAE,KAAK,CAAC,EAAE,OAAOC,cAAQ,CAAC;CAC1B,EAAE,KAAK,CAAC,EAAE,OAAOC,gBAAU,CAAC;CAC5B,EAAE,KAAK,CAAC,EAAE,OAAOA,gBAAU,CAAC;AAC5B;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,SAAS,gBAAgB,EAAE,KAAK,GAAG;AACnC;CACA,CAAC,SAAS,KAAK;AACf;CACA,EAAE,KAAK,CAAC,EAAE,OAAOC,sBAAgB,CAAC;CAClC,EAAE,KAAK,CAAC,EAAE,OAAOC,qBAAe,CAAC;CACjC,EAAE,KAAK,CAAC,EAAE,OAAOC,uBAAiB,CAAC;CACnC,EAAE,KAAK,CAAC,EAAE,OAAOA,uBAAiB,CAAC;AACnC;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,MAAM,sBAAsB,SAASC,iBAAW,CAAC;AACxD;CACA,CAAC,WAAW,GAAG;AACf;CACA,EAAE,KAAK,EAAE,CAAC;CACV,EAAE,IAAI,CAAC,SAAS,GAAGC,mBAAa,CAAC;CACjC,EAAE,IAAI,CAAC,SAAS,GAAGA,mBAAa,CAAC;CACjC,EAAE,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;CAC/B,EAAE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;CAC/B,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B;CACA,EAAE;AACF;CACA,CAAC,UAAU,EAAE,IAAI,GAAG;AACpB;CACA,EAAE,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;CACjD,EAAE,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;CACzC,EAAE,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC;CACnC,EAAE,KAAK,gBAAgB,KAAK,IAAI,GAAG;AACnC;CACA,GAAG,KAAK,EAAE,gBAAgB,GAAG,aAAa,KAAK,gBAAgB,KAAK,GAAG,GAAG;AAC1E;CACA,IAAI,MAAM,IAAI,KAAK,EAAE,iFAAiF,EAAE,CAAC;AACzG;CACA,IAAI;AACJ;CACA,GAAG,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC;CACpC,GAAG,IAAI,CAAC,KAAK,GAAG,aAAa,GAAG,gBAAgB,GAAG,gBAAgB,CAAC;AACpE;CACA,GAAG;AACH;CACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;CAC3B,EAAE,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;CACrC,EAAE,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;CACpD,EAAE,MAAM,SAAS,GAAG,kBAAkB,CAAC,iBAAiB,CAAC;CACzD,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;CACpC,EAAE,IAAI,WAAW,GAAG,QAAQ,CAAC;AAC7B;CACA;CACA,EAAE,KAAK,UAAU,KAAK,IAAI,GAAG;AAC7B;CACA,GAAG,SAAS,kBAAkB;AAC9B;CACA,IAAI,KAAK,YAAY;CACrB,KAAK,UAAU,GAAGC,eAAS,CAAC;CAC5B,KAAK,MAAM;AACX;CACA,IAAI,KAAK,UAAU,CAAC;CACpB,IAAI,KAAK,WAAW,CAAC;CACrB,IAAI,KAAK,WAAW;CACpB,KAAK,UAAU,GAAGC,qBAAe,CAAC;CAClC,KAAK,MAAM;AACX;CACA,IAAI,KAAK,SAAS,CAAC;CACnB,IAAI,KAAK,UAAU,CAAC;CACpB,IAAI,KAAK,UAAU;CACnB,KAAK,UAAU,GAAGC,aAAO,CAAC;CAC1B,KAAK,MAAM;AACX;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA,EAAE,IAAI,IAAI,EAAE,MAAM,EAAE,cAAc,EAAE,gBAAgB,CAAC;CACrD,EAAE,IAAI,cAAc,GAAG,mBAAmB,EAAE,QAAQ,EAAE,CAAC;CACvD,EAAE,SAAS,UAAU;AACrB;CACA,GAAG,KAAKF,eAAS;CACjB,IAAI,cAAc,GAAG,GAAG,CAAC;CACzB,IAAI,MAAM,GAAG,aAAa,EAAE,QAAQ,EAAE,CAAC;AACvC;CACA,IAAI,KAAK,UAAU,IAAI,SAAS,KAAK,CAAC,GAAG;AACzC;CACA,KAAK,gBAAgB,GAAG,kBAAkB,CAAC;CAC3C,KAAK,cAAc,IAAI,GAAG,CAAC;AAC3B;CACA,KAAK,KAAK,kBAAkB,KAAK,UAAU,GAAG;AAC9C;CACA,MAAM,IAAI,GAAGG,sBAAgB,CAAC;AAC9B;CACA,MAAM,MAAM;AACZ;CACA,MAAM,IAAI,GAAGC,cAAQ,CAAC;CACtB,MAAM,cAAc,IAAI,QAAQ,CAAC;AACjC;CACA,MAAM;AACN;CACA,KAAK,MAAM;AACX;CACA,KAAK,gBAAgB,GAAG,YAAY,CAAC;CACrC,KAAK,cAAc,IAAI,KAAK,CAAC;CAC7B,KAAK,IAAI,GAAGJ,eAAS,CAAC;AACtB;CACA,KAAK;AACL;CACA,IAAI,MAAM;AACV;CACA,GAAG,KAAKE,aAAO;CACf,IAAI,cAAc,IAAI,SAAS,GAAG,CAAC,GAAG,GAAG,CAAC;CAC1C,IAAI,cAAc,GAAG,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC;CACpG,IAAI,MAAM,GAAG,gBAAgB,EAAE,QAAQ,EAAE,CAAC;AAC1C;CACA,IAAI,KAAK,SAAS,KAAK,CAAC,GAAG;AAC3B;CACA,KAAK,gBAAgB,GAAG,SAAS,CAAC;CAClC,KAAK,IAAI,GAAGE,cAAQ,CAAC;AACrB;CACA,KAAK,MAAM,KAAK,SAAS,KAAK,CAAC,GAAG;AAClC;CACA,KAAK,gBAAgB,GAAG,UAAU,CAAC;CACnC,KAAK,IAAI,GAAGC,eAAS,CAAC;AACtB;CACA,KAAK,MAAM;AACX;CACA,KAAK,gBAAgB,GAAG,UAAU,CAAC;CACnC,KAAK,IAAI,GAAGH,aAAO,CAAC;AACpB;CACA,KAAK;AACL;CACA,IAAI,MAAM;AACV;CACA,GAAG,KAAKD,qBAAe;CACvB,IAAI,cAAc,IAAI,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC;CAC3C,IAAI,cAAc,GAAG,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC;CACpG,IAAI,MAAM,GAAG,gBAAgB,EAAE,QAAQ,EAAE,CAAC;AAC1C;CACA,IAAI,KAAK,SAAS,KAAK,CAAC,GAAG;AAC3B;CACA,KAAK,gBAAgB,GAAG,UAAU,CAAC;CACnC,KAAK,IAAI,GAAGE,sBAAgB,CAAC;AAC7B;CACA,KAAK,MAAM,KAAK,SAAS,KAAK,CAAC,GAAG;AAClC;CACA,KAAK,gBAAgB,GAAG,WAAW,CAAC;CACpC,KAAK,IAAI,GAAGG,uBAAiB,CAAC;AAC9B;CACA,KAAK,MAAM;AACX;CACA,KAAK,gBAAgB,GAAG,WAAW,CAAC;CACpC,KAAK,IAAI,GAAGL,qBAAe,CAAC;AAC5B;CACA,KAAK;AACL;CACA,IAAI,MAAM;AACV;CACA,GAAG;AACH;CACA;CACA;CACA,EAAE,KAAK,WAAW,KAAK,CAAC,MAAM,MAAM,KAAKP,gBAAU,IAAI,MAAM,KAAKG,uBAAiB,EAAE,GAAG;AACxF;CACA,GAAG,WAAW,GAAG,CAAC,CAAC;AACnB;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC,CAAC;CACzD,EAAE,MAAM,MAAM,GAAG,WAAW,GAAG,SAAS,GAAG,SAAS,CAAC;CACrD,EAAE,MAAM,SAAS,GAAG,IAAI,gBAAgB,EAAE,MAAM,EAAE,CAAC;AACnD;CACA;CACA,EAAE,MAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC;CAC7C,EAAE,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;CAC1B,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG;AACrC;CACA,GAAG,MAAM,EAAE,GAAG,WAAW,GAAG,CAAC,CAAC;CAC9B,GAAG,SAAS,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,cAAc,CAAC;AACrD;CACA,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG;AACxB;CACA,IAAI,SAAS,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,cAAc,CAAC;AAC1D;CACA,IAAI;AACJ;CACA,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG;AACxB;CACA,IAAI,SAAS,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,cAAc,CAAC;AAC1D;CACA,IAAI,KAAK,WAAW,KAAK,CAAC,GAAG;AAC7B;CACA,KAAK,SAAS,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC;AAC/B;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG;AACxB;CACA,IAAI,SAAS,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,cAAc,CAAC;AAC1D;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,IAAI,CAAC,UAAU,GAAG,kBAAkB,CAAC;AACvC;CACA,EAAE,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;CACvC,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;CACvB,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;CACnB,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;CAC/B,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;CAChC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC;CAC9B,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;CAC1B,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;AACjB;CACA,EAAE,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC;CACnC,EAAE,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC;AAC7B;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,MAAM,0BAA0B,SAAS,sBAAsB,CAAC;AACvE;CACA,CAAC,WAAW,GAAG;AACf;CACA,EAAE,KAAK,EAAE,CAAC;CACV,EAAE,IAAI,CAAC,WAAW,GAAGI,qBAAe,CAAC;AACrC;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,MAAM,yBAAyB,SAAS,sBAAsB,CAAC;AACtE;CACA,CAAC,WAAW,GAAG;AACf;CACA,EAAE,KAAK,EAAE,CAAC;CACV,EAAE,IAAI,CAAC,WAAW,GAAGC,aAAO,CAAC;AAC7B;CACA,EAAE;AACF;AACA;CACA,CAAC;AACD;CACO,MAAM,2BAA2B,SAAS,sBAAsB,CAAC;AACxE;CACA,CAAC,WAAW,GAAG;AACf;CACA,EAAE,KAAK,EAAE,CAAC;CACV,EAAE,IAAI,CAAC,WAAW,GAAGF,eAAS,CAAC;AAC/B;CACA,EAAE;AACF;CACA;;CC5RO,MAAM,oBAAoB,CAAC;AAClC;CACA,CAAC,WAAW,GAAG;AACf;CACA,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,0BAA0B,EAAE,CAAC;CAChD,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,2BAA2B,EAAE,CAAC;CACpD,EAAE,IAAI,CAAC,SAAS,GAAG,IAAIF,iBAAW,EAAE,CAAC;CACrC,EAAE,IAAI,CAAC,WAAW,GAAG,IAAIA,iBAAW,EAAE,CAAC;CACvC,EAAE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAC/B;CACA,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,CAAC;AAClC;CACA,EAAE;AACF;CACA,CAAC,UAAU,EAAE,GAAG,GAAG;AACnB;CACA,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;CAC3B,EAAE,aAAa,EAAE,GAAG,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;AACzD;CACA,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAC3D;CACA;CACA,EAAE,KAAK,GAAG,CAAC,QAAQ,GAAG;AACtB;CACA,GAAG,MAAM,cAAc,GAAG,GAAG,CAAC,eAAe,CAAC;CAC9C,GAAG;CACH,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI;CAClC,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,KAAK,cAAc,CAAC,MAAM;CACzD,KAAK;AACL;CACA,IAAI,KAAK,QAAQ,CAAC,KAAK,GAAG;AAC1B;CACA,KAAK,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AACpD;CACA,KAAK,MAAM;AACX;CACA,KAAK,MAAM,KAAK,GAAG,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,EAAE,CAAC;CAC/D,KAAK,IAAI,CAAC,gBAAgB,GAAG,IAAI5C,qBAAe,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC;AACpE;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG,gBAAgB,EAAE,QAAQ,EAAE,cAAc,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC;CACvE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAClD;CACA,GAAG,MAAM;AACT;CACA,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,KAAK,EAAE,CAAC;AAC3C;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,GAAG;AACX;CACA,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;AAC3D;CACA,EAAE,KAAK,KAAK,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;CAC/B,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;CACrC,EAAE,KAAK,SAAS,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;CACvC,EAAE,KAAK,WAAW,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;AAC3C;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,SAAS,gBAAgB,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,GAAG;AAC9D;CACA,CAAC,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC;CAC/B,CAAC,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;CACjE,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC3D;CACA,EAAE,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;CACnB,EAAE,MAAM,EAAE,GAAG,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,CAAC;CACrC,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,QAAQ,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,UAAU,GAAG,UAAU,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACnE;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,SAAS,aAAa,EAAE,GAAG,EAAE,aAAa,EAAE,eAAe,GAAG;AAC9D;CACA,CAAC,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC;AAC1B;CACA,CAAC,KAAK,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG;AAC3B;CACA,EAAE,MAAM,IAAI,KAAK,EAAE,sDAAsD,EAAE,CAAC;AAC5E;CACA,EAAE;AACF;CACA,CAAC,MAAM,IAAI,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC;CACzB,CAAC,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,IAAI,EAAE,CAAC;CAC7C,CAAC,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,IAAI,EAAE,CAAC;CAC7C,CAAC,MAAM,YAAY,GAAG,IAAI,YAAY,EAAE,IAAI,EAAE,CAAC;AAC/C;CACA;CACA;CACA,CAAC,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC;CACpD,CAAC,MAAM,eAAe,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,SAAS,GAAG,CAAC,EAAE,EAAE,CAAC;CACrE,CAAC,MAAM,WAAW,GAAG,IAAI,YAAY,EAAE,CAAC,GAAG,eAAe,GAAG,eAAe,EAAE,CAAC;AAC/E;CACA,CAAC,MAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,CAAC;CAC/D,CAAC,MAAM,aAAa,GAAG,IAAI,WAAW,EAAE,CAAC,GAAG,iBAAiB,GAAG,iBAAiB,EAAE,CAAC;AACpF;CACA,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,GAAG,GAAG;AACxC;CACA,EAAE,MAAM,WAAW,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,CAAC;CAC7C,EAAE,MAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;CACtC,EAAE,MAAM,WAAW,GAAG,mBAAmB,EAAE,WAAW,EAAE,CAAC;CACzD,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,WAAW,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACtE,GAAG,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,WAAW,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACtE;CACA,GAAG;AACH;CACA,EAAE,KAAK,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;AAC7C;CACA,GAAG,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACnD,GAAG,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACrD;CACA,GAAG,MAAM,eAAe,GAAG,UAAU,GAAG,KAAK,CAAC;CAC9C,GAAG,aAAa,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,eAAe,CAAC;CAChD,GAAG,aAAa,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC;AACvC;CACA,GAAG,MAAM;AACT;CACA,GAAG,MAAM,UAAU,GAAG,CAAC,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,cAAc,CAAC;CAClF,GAAG,MAAM,SAAS,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAC5D;CACA,GAAG,aAAa,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,SAAS,CAAC;CAC1C,GAAG,aAAa,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC;AAC3C;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,GAAG,WAAW,CAAC;CACxC,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,eAAe,CAAC;CAC7C,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,eAAe,CAAC;CAC9C,CAAC,aAAa,CAAC,MAAM,GAAGwC,gBAAU,CAAC;CACnC,CAAC,aAAa,CAAC,IAAI,GAAGM,eAAS,CAAC;CAChC,CAAC,aAAa,CAAC,cAAc,GAAG,SAAS,CAAC;CAC1C,CAAC,aAAa,CAAC,SAAS,GAAGD,mBAAa,CAAC;CACzC,CAAC,aAAa,CAAC,SAAS,GAAGA,mBAAa,CAAC;CACzC,CAAC,aAAa,CAAC,eAAe,GAAG,KAAK,CAAC;CACvC,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC;CAClC,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;AACzB;CACA,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC;CAC5C,CAAC,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,iBAAiB,CAAC;CACjD,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG,iBAAiB,CAAC;CAClD,CAAC,eAAe,CAAC,MAAM,GAAGH,qBAAe,CAAC;CAC1C,CAAC,eAAe,CAAC,IAAI,GAAGK,qBAAe,CAAC;CACxC,CAAC,eAAe,CAAC,cAAc,GAAG,QAAQ,CAAC;CAC3C,CAAC,eAAe,CAAC,SAAS,GAAGF,mBAAa,CAAC;CAC3C,CAAC,eAAe,CAAC,SAAS,GAAGA,mBAAa,CAAC;CAC3C,CAAC,eAAe,CAAC,eAAe,GAAG,KAAK,CAAC;CACzC,CAAC,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC;CACpC,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;AAC3B;CACA;;CC5LA,MAAM,eAAe,iBAAiB,IAAI5C,aAAO,EAAE,CAAC;CACpD,MAAM,aAAa,iBAAiB,IAAIA,aAAO,EAAE,CAAC;CAClD,MAAM,cAAc,iBAAiB,IAAIA,aAAO,EAAE,CAAC;CACnD,MAAM,eAAe,iBAAiB,IAAIoD,aAAO,EAAE,CAAC;AACpD;CACA,MAAM,YAAY,iBAAiB,IAAIpD,aAAO,EAAE,CAAC;CACjD,MAAM,KAAK,iBAAiB,IAAIA,aAAO,EAAE,CAAC;AAC1C;CACA,MAAM,UAAU,iBAAiB,IAAIoD,aAAO,EAAE,CAAC;CAC/C,MAAM,WAAW,iBAAiB,IAAIA,aAAO,EAAE,CAAC;CAChD,MAAM,OAAO,iBAAiB,IAAI9C,aAAO,EAAE,CAAC;CAC5C,MAAM,WAAW,iBAAiB,IAAIA,aAAO,EAAE,CAAC;AAChD;CACA;CACA,SAAS,kBAAkB,EAAE,KAAK,EAAE,KAAK,GAAG;AAC5C;CACA,CAAC,KAAK,EAAE,KAAK,IAAI,EAAE,KAAK,GAAG;AAC3B;CACA,EAAE,OAAO;AACT;CACA,EAAE;AACF;CACA,CAAC,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,CAAC;CAC/C,CAAC,MAAM,cAAc,GAAG,KAAK,CAAC,UAAU,KAAK,KAAK,CAAC,UAAU,CAAC;CAC9D,CAAC,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,KAAK,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC;CACtE,CAAC,MAAM,YAAY,GAAG,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC;AACxD;CACA,CAAC,KAAK,EAAE,SAAS,IAAI,EAAE,cAAc,IAAI,EAAE,QAAQ,IAAI,EAAE,YAAY,GAAG;AACxE;CACA,EAAE,MAAM,IAAI,KAAK,EAAE,CAAC;AACpB;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA;CACA,SAAS,oBAAoB,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,GAAG;AAC5D;CACA,CAAC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;CACrC,CAAC,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;CACpC,CAAC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CAChC,CAAC,MAAM,KAAK,GAAG,aAAa,KAAK,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC;AACnE;CACA,CAAC,OAAO,IAAIP,qBAAe,EAAE,IAAI,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;AAClF;CACA,CAAC;AACD;CACA;CACA;CACA,SAAS,qBAAqB,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,GAAG,CAAC,GAAG;AACjE;CACA,CAAC,KAAK,IAAI,CAAC,4BAA4B,GAAG;AAC1C;CACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjD;CACA,GAAG,MAAM,EAAE,GAAG,CAAC,GAAG,YAAY,CAAC;CAC/B,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;CACrC,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;CAC1D,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;CAC1D,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;AAC1D;CACA,GAAG;AACH;CACA,EAAE,MAAM;AACR;CACA,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;CAC7B,EAAE,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC;CACjC,EAAE,MAAM,UAAU,GAAG,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC;CAC5E,EAAE,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;CACvE,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AACzB;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA;CACA,SAAS,eAAe,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,GAAG;AAClD;CACA,CAAC,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC;CACrC,CAAC,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC;CACrC,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACxD;CACA,EAAE,WAAW,EAAE,CAAC,EAAE,IAAI,WAAW,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC/C;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA;CACA,SAAS,mBAAmB,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,GAAG;AACpD;CACA,CAAC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CAChC,CAAC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CAChC,CAAC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;CAC9B,CAAC,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;AAC5C;CACA,CAAC,UAAU,CAAC,mBAAmB,EAAE,QAAQ,CAAC,UAAU,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC;CACxE,CAAC,WAAW,CAAC,mBAAmB,EAAE,QAAQ,CAAC,UAAU,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC;AAC1E;CACA,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAC5B;CACA,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChC;CACA,EAAE,MAAM,MAAM,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC;AAC/C;CACA,EAAE,KAAK,MAAM,KAAK,CAAC,GAAG;AACtB;CACA,GAAG,MAAM,SAAS,GAAG,UAAU,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC;CAClD,GAAG,WAAW,CAAC,gBAAgB,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,EAAE,CAAC;AAC7F;CACA,GAAG,eAAe,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;AACnD;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC;CAC3E,CAAC,MAAM,CAAC,kBAAkB,EAAE,OAAO,EAAE,CAAC;AACtC;CACA,CAAC,OAAO,MAAM,CAAC;AACf;CACA,CAAC;AACD;CACA;CACA,SAAS,gBAAgB,EAAE,SAAS,EAAE,eAAe,EAAE,oBAAoB,EAAE,CAAC,EAAE,MAAM,GAAG;AACzF;CACA,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;CAC7B,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG;AACxD;CACA,EAAE,MAAM,SAAS,GAAG,eAAe,EAAE,CAAC,EAAE,CAAC;CACzC,EAAE,MAAM,cAAc,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;AACxC;CACA,EAAE,KAAK,SAAS,KAAK,CAAC,GAAG,SAAS;AAClC;CACA,EAAE,KAAK,CAAC,mBAAmB,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC;AACjD;CACA,EAAE,KAAK,oBAAoB,GAAG;AAC9B;CACA,GAAG,YAAY,CAAC,eAAe,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACpD;CACA,GAAG,MAAM;AACT;CACA,GAAG,YAAY,CAAC,eAAe,EAAE,KAAK,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,CAAC;AAClE;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,MAAM,CAAC,GAAG,EAAE,YAAY,EAAE,CAAC;AAC5B;CACA,CAAC;AACD;CACA;CACA,SAAS,qBAAqB,EAAE,UAAU,EAAE,OAAO,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,EAAE,cAAc,GAAG,IAAI+B,oBAAc,EAAE,GAAG;AAC5J;CACA,CAAC,MAAM,SAAS,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC;CAClD,CAAC,MAAM,EAAE,SAAS,GAAG,KAAK,EAAE,WAAW,GAAG,KAAK,EAAE,cAAc,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjF;CACA,CAAC,MAAM,cAAc,GAAG,IAAI,GAAG,EAAE,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC;CAC7E,CAAC,MAAM,UAAU,GAAG,EAAE,CAAC;AACvB;CACA,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;AAChB;CACA,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;CAC9B,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG;AAChD;CACA,EAAE,MAAM,QAAQ,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;CACnC,EAAE,IAAI,eAAe,GAAG,CAAC,CAAC;AAC1B;CACA;CACA,EAAE,KAAK,SAAS,OAAO,QAAQ,CAAC,KAAK,KAAK,IAAI,EAAE,GAAG;AACnD;CACA,GAAG,MAAM,IAAI,KAAK,EAAE,qJAAqJ,EAAE,CAAC;AAC5K;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,MAAM,IAAI,IAAI,QAAQ,CAAC,UAAU,GAAG;AAC5C;CACA,GAAG,KAAK,EAAE,cAAc,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG;AACvC;CACA,IAAI,MAAM,IAAI,KAAK,EAAE,sFAAsF,GAAG,IAAI,GAAG,8DAA8D,EAAE,CAAC;AACtL;CACA,IAAI;AACJ;CACA,GAAG,KAAK,UAAU,EAAE,IAAI,EAAE,KAAK,SAAS,GAAG;AAC3C;CACA,IAAI,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AAC5B;CACA,IAAI;AACJ;CACA,GAAG,UAAU,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,CAAC;CAC1D,GAAG,eAAe,GAAG,CAAC;AACtB;CACA,GAAG;AACH;CACA;CACA,EAAE,KAAK,eAAe,KAAK,cAAc,CAAC,IAAI,GAAG;AACjD;CACA,GAAG,MAAM,IAAI,KAAK,EAAE,uFAAuF,EAAE,CAAC;AAC9G;CACA,GAAG;AACH;CACA,EAAE,KAAK,SAAS,GAAG;AACnB;CACA,GAAG,IAAI,KAAK,CAAC;CACb,GAAG,KAAK,SAAS,GAAG;AACpB;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC;AACjC;CACA,IAAI,MAAM,KAAK,QAAQ,CAAC,UAAU,CAAC,QAAQ,KAAK,SAAS,GAAG;AAC5D;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;AAC/C;CACA,IAAI,MAAM;AACV;CACA,IAAI,MAAM,IAAI,KAAK,EAAE,yFAAyF,EAAE,CAAC;AACjH;CACA,IAAI;AACJ;CACA,GAAG,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;CAC/C,GAAG,MAAM,IAAI,KAAK,CAAC;AACnB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;CACA,CAAC,KAAK,SAAS,GAAG;AAClB;CACA,EAAE,IAAI,gBAAgB,GAAG,KAAK,CAAC;CAC/B,EAAE,KAAK,EAAE,cAAc,CAAC,KAAK,GAAG;AAChC;CACA,GAAG,IAAI,UAAU,GAAG,CAAC,CAAC;CACtB,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG;AAClD;CACA,IAAI,UAAU,IAAI,UAAU,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;AAC9C;CACA,IAAI;AACJ;CACA,GAAG,cAAc,CAAC,QAAQ,EAAE,IAAI/B,qBAAe,EAAE,IAAI,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;CAC7F,GAAG,gBAAgB,GAAG,IAAI,CAAC;AAC3B;CACA,GAAG;AACH;CACA,EAAE,KAAK,WAAW,IAAI,gBAAgB,GAAG;AACzC;CACA,GAAG,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC;CAC5C,GAAG,IAAI,YAAY,GAAG,CAAC,CAAC;CACxB,GAAG,IAAI,WAAW,GAAG,CAAC,CAAC;CACvB,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG;AAClD;CACA,IAAI,MAAM,QAAQ,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;CACrC,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;CACjC,IAAI,KAAK,cAAc,EAAE,CAAC,EAAE,KAAK,IAAI,GAAG;AACxC;CACA,KAAK,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG;AAC9C;CACA,MAAM,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,WAAW,EAAE,CAAC;CACtE,MAAM,YAAY,GAAG,CAAC;AACtB;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA,IAAI,WAAW,IAAI,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;AACtD;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;CACA,CAAC,MAAM,MAAM,IAAI,IAAI,UAAU,GAAG;AAClC;CACA,EAAE,MAAM,QAAQ,GAAG,UAAU,EAAE,IAAI,EAAE,CAAC;CACtC,EAAE,KAAK,IAAI,IAAI,IAAI,cAAc,CAAC,UAAU,EAAE,GAAG;AACjD;CACA,GAAG,IAAI,KAAK,GAAG,CAAC,CAAC;CACjB,GAAG,MAAM,MAAM,GAAG,IAAI,QAAQ,GAAG;AACjC;CACA,IAAI,KAAK,IAAI,QAAQ,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC;AACnC;CACA,IAAI;AACJ;CACA,GAAG,cAAc,CAAC,YAAY,EAAE,IAAI,EAAE,oBAAoB,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC;AAC/F;CACA,GAAG;AACH;CACA,EAAE,MAAM,eAAe,GAAG,cAAc,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC;CAC5D,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;CACjB,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACtD;CACA,GAAG,MAAM,IAAI,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;CAC9B,GAAG,KAAK,cAAc,EAAE,CAAC,EAAE,KAAK,IAAI,GAAG;AACvC;CACA,IAAI,qBAAqB,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,CAAC;AAC3D;CACA,IAAI;AACJ;CACA,GAAG,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC;AACxB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,cAAc,CAAC;AACvB;CACA,CAAC;AACD;CACA,SAAS,uBAAuB,EAAE,CAAC,EAAE,CAAC,GAAG;AACzC;CACA,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,GAAG;AACjC;CACA,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;AACjB;CACA,EAAE;AACF;CACA,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,GAAG;AAC9B;CACA,EAAE,OAAO,KAAK,CAAC;AACf;CACA,EAAE;AACF;CACA,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC9C;CACA,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,GAAG;AAC3B;CACA,GAAG,OAAO,KAAK,CAAC;AAChB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,IAAI,CAAC;AACb;CACA,CAAC;AACD;CACA,SAAS,cAAc,EAAE,QAAQ,GAAG;AACpC;CACA,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,QAAQ,CAAC;CACxC,CAAC,KAAK,KAAK,GAAG;AACd;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AACpD;CACA,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;CAC9B,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CAClC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;CACvB,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AAC3B;CACA,GAAG;AACH;CACA,EAAE,MAAM;AACR;CACA,EAAE,MAAM,MAAM,GAAG,IAAI,UAAU,GAAG;AAClC;CACA,GAAG,MAAM,IAAI,GAAG,UAAU,EAAE,GAAG,EAAE,CAAC;CAClC,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CAClC,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AACpD;CACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,GAAG;AAC1C;CACA,KAAK,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;CAC1C,KAAK,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;CAC9C,KAAK,IAAI,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;CACnC,KAAK,IAAI,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;AACvC;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,QAAQ,CAAC;AACjB;AACA;CACA,CAAC;AACD;CACA;CACA,MAAM,YAAY,CAAC;AACnB;CACA,CAAC,WAAW,EAAE,IAAI,GAAG;AACrB;CACA,EAAE,IAAI,CAAC,WAAW,GAAG,IAAIO,aAAO,EAAE,CAAC;CACnC,EAAE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;CAC3B,EAAE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;CAC3B,EAAE,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,CAAC;CAC5B,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACnB;CACA,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;AAChB;CACA,EAAE;AACF;CACA,CAAC,MAAM,GAAG;AACV;CACA,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CACzB,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,cAAc,GAAG,EAAE,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC,CAAC;CAC5G,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;CAC5C,EAAE,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;CAC3D,EAAE,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;AACvC;CACA,EAAE,KAAK,QAAQ,GAAG;AAClB;CACA;CACA,GAAG,KAAK,EAAE,QAAQ,CAAC,WAAW,GAAG;AACjC;CACA,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;AAClC;CACA,IAAI;AACJ;CACA,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;AACrB;CACA;CACA,GAAG,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;CAC9C,GAAG,KAAK,EAAE,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM,GAAG;AAClF;CACA,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,KAAK,EAAE,CAAC;AAC7C;CACA,IAAI,MAAM;AACV;CACA,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,EAAE,CAAC;AAC1C;CACA,IAAI;AACJ;CACA,GAAG,MAAM;AACT;CACA,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC5B;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,SAAS,GAAG;AACb;CACA,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CACzB,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,cAAc,GAAG,EAAE,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC,CAAC;CAC5G,EAAE,MAAM,SAAS;CACjB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE;CAC9C,GAAG,IAAI,CAAC,YAAY,KAAK,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO;CAC7D,GAAG,uBAAuB,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE;CACpG,GAAG,IAAI,CAAC,cAAc,KAAK,cAAc,CAAC;AAC1C;CACA,EAAE,OAAO,EAAE,SAAS,CAAC;AACrB;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,MAAM,uBAAuB,CAAC;AACrC;CACA,CAAC,WAAW,EAAE,MAAM,GAAG;AACvB;CACA,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG;AACnC;CACA,GAAG,MAAM,GAAG,EAAE,MAAM,EAAE,CAAC;AACvB;CACA,GAAG;AACH;CACA,EAAE,MAAM,WAAW,GAAG,EAAE,CAAC;CACzB,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,IAAI;AAC5B;CACA,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC,IAAI;AAChC;CACA,IAAI,KAAK,CAAC,CAAC,MAAM,GAAG;AACpB;CACA,KAAK,WAAW,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAC3B;CACA,KAAK;AACL;CACA,IAAI,EAAE,CAAC;AACP;CACA,GAAG,EAAE,CAAC;AACN;CACA,EAAE,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;CAC5B,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;CACxB,EAAE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;CACnC,EAAE,IAAI,CAAC,UAAU,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;CAC9E,EAAE,IAAI,CAAC,qBAAqB,GAAG,IAAI,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAIwB,oBAAc,EAAE,EAAE,CAAC;CACxG,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,EAAE,CAAC;AAChC;CACA,EAAE;AACF;CACA,CAAC,YAAY,GAAG;AAChB;CACA,EAAE,MAAM,SAAS,GAAG,EAAE,CAAC;CACvB,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,IAAI;AAC/B;CACA,GAAG,KAAK,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,GAAG;AACzC;CACA,IAAI,SAAS,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AACvC;CACA,IAAI,MAAM;AACV;CACA,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;AACpC;CACA,IAAI;AACJ;CACA,GAAG,EAAE,CAAC;CACN,EAAE,OAAO,SAAS,CAAC;AACnB;CACA,EAAE;AACF;CACA,CAAC,QAAQ,EAAE,cAAc,GAAG,IAAIA,oBAAc,EAAE,GAAG;AACnD;CACA;CACA,EAAE,IAAI,cAAc,GAAG,EAAE,CAAC;CAC1B,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,qBAAqB,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;CACtE,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACpD;CACA,GAAG,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CAC5B,GAAG,MAAM,IAAI,GAAG,qBAAqB,EAAE,CAAC,EAAE,CAAC;CAC3C,GAAG,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;CACrC,GAAG,KAAK,EAAE,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,GAAG;AAC3C;CACA,IAAI,IAAI,CAAC,wBAAwB,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;CAChD,IAAI,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACjC;CACA,IAAI,KAAK,EAAE,IAAI,GAAG;AAClB;CACA,KAAK,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,YAAY,EAAE,IAAI,EAAE,EAAE,CAAC;AACpD;CACA,KAAK,MAAM;AACX;CACA,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;AACnB;CACA,KAAK;AACL;CACA,IAAI,MAAM;AACV;CACA,IAAI,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AAChC;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,KAAK,qBAAqB,CAAC,MAAM,KAAK,CAAC,GAAG;AAC5C;CACA;CACA,GAAG,cAAc,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;AACnC;CACA;CACA,GAAG,MAAM,KAAK,GAAG,cAAc,CAAC,UAAU,CAAC;CAC3C,GAAG,MAAM,MAAM,GAAG,IAAI,KAAK,GAAG;AAC9B;CACA,IAAI,cAAc,CAAC,eAAe,EAAE,GAAG,EAAE,CAAC;AAC1C;CACA,IAAI;AACJ;CACA;CACA,GAAG,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,UAAU,GAAG;AACxC;CACA,IAAI,cAAc,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE,GAAG,EAAE,EAAE,IAAI/B,qBAAe,EAAE,IAAI,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AAClH;CACA,IAAI;AACJ;CACA,GAAG,MAAM;AACT;CACA,GAAG,qBAAqB,EAAE,qBAAqB,EAAE,EAAE,SAAS,EAAE,cAAc,EAAE,EAAE,cAAc,EAAE,CAAC;AACjG;CACA,GAAG;AACH;CACA,EAAE,MAAM,MAAM,GAAG,IAAI,cAAc,CAAC,UAAU,GAAG;AACjD;CACA,GAAG,cAAc,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC;AACvD;CACA,GAAG;AACH;CACA,EAAE,OAAO,cAAc,CAAC;AACxB;CACA,EAAE;AACF;CACA,CAAC,wBAAwB,EAAE,IAAI,EAAE,cAAc,GAAG,IAAI+B,oBAAc,EAAE,GAAG;AACzE;CACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;CACzD,EAAE,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC;CAC7D,EAAE,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC;CAC/D,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;CACzC,EAAE,MAAM,gBAAgB,GAAG,cAAc,CAAC,UAAU,CAAC;AACrD;CACA;CACA,EAAE,KAAK,EAAE,cAAc,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,GAAG;AAClD;CACA,GAAG,cAAc,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AACjD;CACA,GAAG;AACH;CACA,EAAE,KAAK,EAAE,gBAAgB,CAAC,QAAQ,GAAG;AACrC;CACA,GAAG,cAAc,CAAC,YAAY,EAAE,UAAU,EAAE,oBAAoB,EAAE,UAAU,CAAC,QAAQ,EAAE,EAAE,CAAC;AAC1F;CACA,GAAG;AACH;CACA,EAAE,KAAK,aAAa,IAAI,EAAE,gBAAgB,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,GAAG;AACzE;CACA,GAAG,cAAc,CAAC,YAAY,EAAE,QAAQ,EAAE,oBAAoB,EAAE,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;AACtF;CACA,GAAG;AACH;CACA,EAAE,KAAK,cAAc,IAAI,EAAE,gBAAgB,CAAC,OAAO,IAAI,UAAU,CAAC,OAAO,GAAG;AAC5E;CACA,GAAG,cAAc,CAAC,YAAY,EAAE,SAAS,EAAE,oBAAoB,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC;AACxF;CACA,GAAG;AACH;CACA;CACA,EAAE,kBAAkB,EAAE,QAAQ,CAAC,KAAK,EAAE,cAAc,CAAC,KAAK,EAAE,CAAC;CAC7D,EAAE,kBAAkB,EAAE,UAAU,CAAC,QAAQ,EAAE,gBAAgB,CAAC,QAAQ,EAAE,CAAC;AACvE;CACA,EAAE,KAAK,aAAa,GAAG;AACvB;CACA,GAAG,kBAAkB,EAAE,UAAU,CAAC,MAAM,EAAE,gBAAgB,CAAC,MAAM,EAAE,CAAC;AACpE;CACA,GAAG;AACH;CACA,EAAE,KAAK,cAAc,GAAG;AACxB;CACA,GAAG,kBAAkB,EAAE,UAAU,CAAC,OAAO,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC;AACtE;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;CACvC,EAAE,MAAM,MAAM,GAAG,aAAa,GAAG,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;CAC1D,EAAE,MAAM,OAAO,GAAG,cAAc,GAAG,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;CAC7D,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC;CAC1D,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,eAAe,CAAC,MAAM,CAAC;CACtD,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC;CACxD,EAAE,MAAM,oBAAoB,GAAG,QAAQ,CAAC,oBAAoB,CAAC;CAC7D,EAAE,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC;CACrD,EAAE,MAAM,YAAY,GAAG,IAAIuB,aAAO,EAAE,CAAC;CACrC,EAAE,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;AACnD;CACA;CACA,EAAE,KAAK,QAAQ,CAAC,KAAK,GAAG;AACxB;CACA,GAAG,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AAC1D;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChE;CACA,GAAG,eAAe,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;CACtD,GAAG,KAAK,MAAM,GAAG;AACjB;CACA,IAAI,aAAa,CAAC,mBAAmB,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;AACnD;CACA,IAAI;AACJ;CACA,GAAG,KAAK,OAAO,GAAG;AAClB;CACA,IAAI,eAAe,CAAC,mBAAmB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;CACtD,IAAI,cAAc,CAAC,mBAAmB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;AACrD;CACA,IAAI;AACJ;CACA;CACA,GAAG,KAAK,eAAe,GAAG;AAC1B;CACA,IAAI,KAAK,aAAa,GAAG;AACzB;CACA,KAAK,gBAAgB,EAAE,aAAa,EAAE,eAAe,EAAE,oBAAoB,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC;AAClG;CACA,KAAK;AACL;CACA,IAAI,KAAK,WAAW,GAAG;AACvB;CACA,KAAK,gBAAgB,EAAE,WAAW,EAAE,eAAe,EAAE,oBAAoB,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC;AAC9F;CACA,KAAK;AACL;CACA,IAAI,KAAK,YAAY,GAAG;AACxB;CACA,KAAK,gBAAgB,EAAE,YAAY,EAAE,eAAe,EAAE,oBAAoB,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC;AAChG;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA;CACA,GAAG,KAAK,IAAI,CAAC,aAAa,GAAG;AAC7B;CACA,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC;CAClD,IAAI,KAAK,MAAM,GAAG;AAClB;CACA,KAAK,mBAAmB,EAAE,IAAI,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC;AACnD;CACA,KAAK;AACL;CACA,IAAI,KAAK,OAAO,GAAG;AACnB;CACA,KAAK,mBAAmB,EAAE,IAAI,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC;AACpD;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA;CACA,GAAG,KAAK,oBAAoB,GAAG;AAC/B;CACA,IAAI,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;AACrD;CACA,IAAI;AACJ;CACA,GAAG,gBAAgB,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC;AAClG;CACA,GAAG,KAAK,MAAM,GAAG;AACjB;CACA,IAAI,KAAK,oBAAoB,GAAG;AAChC;CACA,KAAK,aAAa,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;AACrD;CACA,KAAK;AACL;CACA,IAAI,gBAAgB,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,CAAC;AAC3F;CACA,IAAI;AACJ;CACA,GAAG,KAAK,OAAO,GAAG;AAClB;CACA,IAAI,KAAK,oBAAoB,GAAG;AAChC;CACA,KAAK,cAAc,CAAC,kBAAkB,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;AAC3D;CACA,KAAK;AACL;CACA,IAAI,gBAAgB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC;AACnH;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,MAAM,CAAC,IAAI,IAAI,CAAC,UAAU,GAAG;AACrC;CACA,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC;CACpC,GAAG,KAAK,GAAG,KAAK,UAAU,IAAI,GAAG,KAAK,SAAS,IAAI,GAAG,KAAK,QAAQ,IAAI,IAAI,GAAG,IAAI,UAAU,EAAE,GAAG;AACjG;CACA,IAAI,SAAS;AACb;CACA,IAAI;AACJ;CACA,GAAG,KAAK,EAAE,gBAAgB,EAAE,GAAG,EAAE,GAAG;AACpC;CACA,IAAI,cAAc,CAAC,YAAY,EAAE,GAAG,EAAE,oBAAoB,EAAE,UAAU,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;AAClF;CACA,IAAI;AACJ;CACA,GAAG,kBAAkB,EAAE,UAAU,EAAE,GAAG,EAAE,EAAE,gBAAgB,EAAE,GAAG,EAAE,EAAE,CAAC;CACpE,GAAG,qBAAqB,EAAE,UAAU,EAAE,GAAG,EAAE,EAAE,gBAAgB,EAAE,GAAG,EAAE,EAAE,CAAC;AACvE;CACA,GAAG;AACH;CACA,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG;AAC5C;CACA,GAAG,cAAc,EAAE,cAAc,EAAE,CAAC;AACpC;CACA,GAAG;AACH;CACA,EAAE,OAAO,cAAc,CAAC;AACxB;CACA,EAAE;AACF;CACA;;CCrwBO,MAAM,gBAAgB,aAAa,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;CClFD;CACO,MAAM,sBAAsB,aAAa,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;CClMM,MAAM,iBAAiB,aAAa,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;CCpND;CACA;CACA;CACO,MAAM,sBAAsB,aAAa,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;ACEW,OAAC,aAAa,GAAGC,uBAAqC;AACtD,OAAC,sBAAsB,GAAGC,uBAAqC;AAC/D,OAAC,uBAAuB,GAAG,CAAC;AACxC,CAAC,GAAGC,gBAA8B,EAAE;AACpC,CAAC,GAAGC,iBAA+B,EAAE;AACrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}