import { Pointer } from '@pmndrs/pointer-events';
import { Mesh, Object3D } from 'three';
import { PointerCursorModelOptions } from '../pointer/cursor.js';
import { PointerRayModelOptions } from '../pointer/ray.js';
export declare class PointerRayModel extends Mesh {
    constructor(pointer: Pointer, options?: PointerRayModelOptions);
}
export declare class PointerCursorModel extends Mesh {
    constructor(pointerGroup: Object3D, pointer: Pointer, options?: PointerCursorModelOptions);
}
