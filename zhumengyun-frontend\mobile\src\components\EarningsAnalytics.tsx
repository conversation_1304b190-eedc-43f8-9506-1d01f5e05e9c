'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'

interface EarningsData {
  daily: { date: string; amount: number; type: string }[]
  monthly: { month: string; amount: number; growth: number }[]
  yearly: { year: string; amount: number; growth: number }[]
  sources: { name: string; amount: number; percentage: number; color: string }[]
  taxes: { period: string; income: number; tax: number; netIncome: number }[]
}

interface EarningsAnalyticsProps {
  userId: string
}

export default function EarningsAnalytics({ userId }: EarningsAnalyticsProps) {
  const [timeRange, setTimeRange] = useState<'daily' | 'monthly' | 'yearly'>('monthly')
  const [earningsData, setEarningsData] = useState<EarningsData>({
    daily: [],
    monthly: [],
    yearly: [],
    sources: [],
    taxes: []
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadEarningsData()
  }, [userId, timeRange])

  const loadEarningsData = async () => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockData: EarningsData = {
        daily: [
          { date: '2024-12-01', amount: 1200, type: '内容销售' },
          { date: '2024-12-02', amount: 800, type: '技能服务' },
          { date: '2024-12-03', amount: 1500, type: '内容销售' },
          { date: '2024-12-04', amount: 600, type: '平台奖励' },
          { date: '2024-12-05', amount: 2000, type: '技能服务' }
        ],
        monthly: [
          { month: '2024-09', amount: 18500, growth: 12.5 },
          { month: '2024-10', amount: 22300, growth: 20.5 },
          { month: '2024-11', amount: 25600, growth: 14.8 },
          { month: '2024-12', amount: 28900, growth: 12.9 }
        ],
        yearly: [
          { year: '2022', amount: 156000, growth: 0 },
          { year: '2023', amount: 234000, growth: 50.0 },
          { year: '2024', amount: 312000, growth: 33.3 }
        ],
        sources: [
          { name: '内容销售', amount: 180000, percentage: 45, color: 'from-blue-500 to-cyan-500' },
          { name: '技能服务', amount: 120000, percentage: 30, color: 'from-green-500 to-emerald-500' },
          { name: '平台奖励', amount: 60000, percentage: 15, color: 'from-yellow-500 to-orange-500' },
          { name: '其他收入', amount: 40000, percentage: 10, color: 'from-purple-500 to-pink-500' }
        ],
        taxes: [
          { period: '2024-Q1', income: 78000, tax: 12480, netIncome: 65520 },
          { period: '2024-Q2', income: 82000, tax: 13120, netIncome: 68880 },
          { period: '2024-Q3', income: 85000, tax: 13600, netIncome: 71400 },
          { period: '2024-Q4', income: 89000, tax: 14240, netIncome: 74760 }
        ]
      }
      
      setEarningsData(mockData)
    } catch (error) {
      console.error('加载收益数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => `¥${amount.toLocaleString()}`

  const getCurrentData = () => {
    switch (timeRange) {
      case 'daily': return earningsData.daily
      case 'monthly': return earningsData.monthly
      case 'yearly': return earningsData.yearly
      default: return earningsData.monthly
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-8 bg-white/20 rounded mb-4"></div>
          <div className="h-64 bg-white/10 rounded-xl"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 时间范围选择 */}
      <div className="flex space-x-1 bg-white/10 rounded-lg p-1">
        {[
          { key: 'daily', label: '日收益' },
          { key: 'monthly', label: '月收益' },
          { key: 'yearly', label: '年收益' }
        ].map((range) => (
          <button
            key={range.key}
            onClick={() => setTimeRange(range.key as any)}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              timeRange === range.key
                ? 'bg-blue-500 text-white'
                : 'text-white/70 hover:text-white'
            }`}
          >
            {range.label}
          </button>
        ))}
      </div>

      {/* 收益趋势图 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20"
      >
        <h3 className="text-lg font-bold text-white mb-4">收益趋势</h3>
        
        <div className="space-y-3">
          {getCurrentData().map((item, index) => {
            const maxAmount = Math.max(...getCurrentData().map(d => 'amount' in d ? d.amount : 0))
            const percentage = ('amount' in item ? item.amount : 0) / maxAmount * 100
            
            return (
              <div key={index} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">
                    {'date' in item ? item.date : 'month' in item ? item.month : item.year}
                  </span>
                  <div className="flex items-center space-x-2">
                    <span className="text-white font-medium">
                      {formatCurrency('amount' in item ? item.amount : 0)}
                    </span>
                    {'growth' in item && item.growth !== undefined && (
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        item.growth > 0 ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                      }`}>
                        {item.growth > 0 ? '+' : ''}{item.growth.toFixed(1)}%
                      </span>
                    )}
                  </div>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${percentage}%` }}
                    transition={{ duration: 0.8, delay: index * 0.1 }}
                    className="h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
                  />
                </div>
              </div>
            )
          })}
        </div>
      </motion.div>

      {/* 收入来源分析 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20"
      >
        <h3 className="text-lg font-bold text-white mb-4">收入来源分析</h3>
        
        <div className="space-y-4">
          {earningsData.sources.map((source, index) => (
            <div key={index} className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-white font-medium">{source.name}</span>
                <div className="text-right">
                  <div className="text-white font-bold">{formatCurrency(source.amount)}</div>
                  <div className="text-xs text-gray-400">{source.percentage}%</div>
                </div>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${source.percentage}%` }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  className={`h-2 bg-gradient-to-r ${source.color} rounded-full`}
                />
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* 税务分析 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20"
      >
        <h3 className="text-lg font-bold text-white mb-4">税务分析</h3>
        
        <div className="space-y-3">
          {earningsData.taxes.map((tax, index) => (
            <div key={index} className="bg-white/5 rounded-lg p-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-white font-medium">{tax.period}</span>
                <span className="text-xs text-gray-400">季度报告</span>
              </div>
              
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-blue-400 font-bold">{formatCurrency(tax.income)}</div>
                  <div className="text-xs text-gray-400">总收入</div>
                </div>
                <div>
                  <div className="text-red-400 font-bold">{formatCurrency(tax.tax)}</div>
                  <div className="text-xs text-gray-400">个人所得税</div>
                </div>
                <div>
                  <div className="text-green-400 font-bold">{formatCurrency(tax.netIncome)}</div>
                  <div className="text-xs text-gray-400">税后收入</div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-4 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
          <div className="text-yellow-200 text-xs">
            <p className="font-medium mb-1">税务提醒：</p>
            <p>• 平台已自动代扣代缴个人所得税</p>
            <p>• 税务凭证可在"我的-税务记录"中下载</p>
            <p>• 如需专业税务咨询，请联系客服</p>
          </div>
        </div>
      </motion.div>

      {/* 收益预测 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20"
      >
        <h3 className="text-lg font-bold text-white mb-4">收益预测</h3>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-xl p-4 border border-green-500/20">
            <div className="text-green-400 text-2xl font-bold">¥35,600</div>
            <div className="text-gray-400 text-sm">预计下月收入</div>
            <div className="text-green-300 text-xs mt-1">基于历史数据分析</div>
          </div>
          
          <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-xl p-4 border border-blue-500/20">
            <div className="text-blue-400 text-2xl font-bold">¥420,000</div>
            <div className="text-gray-400 text-sm">预计年收入</div>
            <div className="text-blue-300 text-xs mt-1">按当前趋势计算</div>
          </div>
        </div>

        <div className="mt-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
          <div className="text-blue-200 text-xs">
            <p className="font-medium mb-1">预测说明：</p>
            <p>• 基于过去6个月的收益数据进行AI分析</p>
            <p>• 考虑了季节性因素和市场趋势</p>
            <p>• 预测仅供参考，实际收益可能有所差异</p>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
