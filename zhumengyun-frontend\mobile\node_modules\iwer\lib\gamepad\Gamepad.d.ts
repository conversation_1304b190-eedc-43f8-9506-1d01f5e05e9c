/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_GAMEPAD } from '../private.js';
export declare enum GamepadMappingType {
    None = "",
    Standard = "standard",
    XRStandard = "xr-standard"
}
export interface Button {
    id: string;
    type: 'binary' | 'analog' | 'manual';
    eventTrigger?: 'select' | 'squeeze';
}
export interface Axis {
    id: string;
    type: 'x-axis' | 'y-axis' | 'manual';
}
export interface GamepadConfig {
    mapping: GamepadMappingType;
    buttons: (Button | null)[];
    axes: (Axis | null)[];
}
export declare class GamepadButton {
    [P_GAMEPAD]: {
        type: 'analog' | 'binary' | 'manual';
        eventTrigger: 'select' | 'squeeze' | null;
        pressed: boolean;
        touched: boolean;
        value: number;
        lastFrameValue: number;
        pendingValue: number | null;
    };
    constructor(type: 'analog' | 'binary' | 'manual', eventTrigger: 'select' | 'squeeze' | null);
    get pressed(): boolean;
    get touched(): boolean;
    get value(): number;
}
export declare class EmptyGamepadButton {
    pressed: boolean;
    touched: boolean;
    value: number;
}
export declare class Gamepad {
    [P_GAMEPAD]: {
        id: string;
        index: number;
        connected: boolean;
        timestamp: DOMHighResTimeStamp;
        mapping: GamepadMappingType;
        buttonsMap: {
            [id: string]: GamepadButton | null;
        };
        buttonsSequence: (string | null)[];
        axesMap: {
            [id: string]: {
                x: number;
                y: number;
            };
        };
        axesSequence: (string | null)[];
        hapticActuators: GamepadHapticActuator[];
    };
    constructor(gamepadConfig: GamepadConfig, id?: string, index?: number);
    get id(): string;
    get index(): number;
    get connected(): boolean;
    get timestamp(): number;
    get mapping(): GamepadMappingType;
    get axes(): (number | null)[];
    get buttons(): (GamepadButton | EmptyGamepadButton | null)[];
    get hapticActuators(): GamepadHapticActuator[];
    get vibrationActuator(): null;
}
//# sourceMappingURL=Gamepad.d.ts.map