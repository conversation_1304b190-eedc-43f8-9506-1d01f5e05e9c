"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("@react-three/fiber"),n=require("three"),u=require("./Instances.cjs.js"),a=require("./Billboard.cjs.js"),c=require("./useSpriteLoader.cjs.js");function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function s(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}require("react-composer"),require("../helpers/deprecated.cjs.js");var o=l(e),i=s(r),m=s(n);const f=i.createContext(null);const p=new m.PlaneGeometry(1,1),d=i.forwardRef((({startFrame:e=0,endFrame:r,fps:n=30,frameName:l="",textureDataURL:s,textureImageURL:d,loop:h=!1,numberOfFrames:v=1,autoPlay:y=!0,animationNames:w,onStart:E,onEnd:F,onLoopEnd:b,onFrame:g,play:x,pause:S=!1,flipX:j=!1,alphaTest:R=0,children:A,asSprite:M=!1,offset:N,playBackwards:O=!1,resetOnEnd:q=!1,maxItems:z=1,instanceItems:D=[[0,0,0]],spriteDataset:L,canvasRenderingContext2DSettings:P,roundFramePosition:T=!1,meshProps:I={},...k},B)=>{const C=i.useRef(new m.Group),V=i.useRef(null),_=i.useRef(null),U=i.useRef(null),G=i.useRef(window.performance.now()),H=i.useRef(e),J=i.useRef(l),W=n>0?1e3/n:0,[X,K]=i.useState(new m.Texture),Q=i.useRef(0),[Y,Z]=i.useState(new m.Vector3(1,1,1)),$=j?-1:1,ee=i.useRef(S),re=i.useRef(N),te=i.useRef(!1),{spriteObj:ne,loadJsonAndTexture:ue}=c.useSpriteLoader(null,null,w,v,void 0,P),ae=i.useRef(l),ce=i.useCallback(((e,r)=>{if(null===r)v&&(Q.current=v,O&&(H.current=v-1),V.current=r);else{var t,n;V.current=r,V.current&&Array.isArray(V.current.frames)?Q.current=V.current.frames.length:V.current&&"object"==typeof V.current&&ae.current?Q.current=V.current.frames[ae.current].length:Q.current=0,O&&(H.current=Q.current-1);const{w:u,h:a}=c.getFirstFrame(null!==(t=null==(n=V.current)?void 0:n.frames)&&void 0!==t?t:[],ae.current).sourceSize,l=oe(u,a);Z(l),_.current&&(_.current.map=e)}K(e)}),[v,O]),le=i.useCallback((()=>{if(!V.current)return;const{meta:{size:e},frames:r}=V.current,{w:t,h:n}=Array.isArray(r)?r[0].sourceSize:l&&r[l]?r[l][0].sourceSize:{w:0,h:0};_.current&&_.current.map&&(_.current.map.wrapS=_.current.map.wrapT=m.RepeatWrapping,_.current.map.center.set(0,0),_.current.map.repeat.set(1*$/(e.w/t),1/(e.h/n)));const u=1/((e.h-1)/n);_.current&&_.current.map&&(_.current.map.offset.x=0,_.current.map.offset.y=1-u),E&&E({currentFrameName:null!=l?l:"",currentFrame:H.current})}),[$,l,E]),se=i.useMemo((()=>({current:re.current,offset:re.current,imageUrl:d,hasEnded:!1,ref:B})),[d,B]);i.useImperativeHandle(B,(()=>C.current),[]),i.useLayoutEffect((()=>{re.current=N}),[N]);const oe=(e,r)=>{var t;const n=new m.Vector3,u=r/e;return n.set(1,u,1),null==(t=U.current)||t.scale.copy(n),n};i.useEffect((()=>{var e;L?ce(null==L||null==(e=L.spriteTexture)?void 0:e.clone(),L.spriteData):d&&s&&ue(d,s)}),[ue,L,s,d,ce]),i.useEffect((()=>{var e;ne&&ce(null==ne||null==(e=ne.spriteTexture)?void 0:e.clone(),null==ne?void 0:ne.spriteData)}),[ne,ce]),i.useEffect((()=>{var e;(se.hasEnded=!1,V.current&&!0===O)?H.current=(null!==(e=V.current.frames.length)&&void 0!==e?e:0)-1:H.current=0}),[O,se]),i.useLayoutEffect((()=>{le()}),[X,j,le]),i.useEffect((()=>{y&&(ee.current=!1)}),[y]),i.useLayoutEffect((()=>{if(J.current!==l&&l&&(H.current=0,J.current=l,se.hasEnded=!1,W<=0&&(H.current=r||e||0),V.current)){const{w:e,h:r}=c.getFirstFrame(V.current.frames,l).sourceSize,t=oe(e,r);Z(t)}}),[l,W,se,r,e]);const ie=(e,r,t,n)=>{var u=void 0===N?se.current:N;const a=H.current;let c=0,l=0;oe(e,r);const s=T?Math.round((t.w-1)/e):(t.w-1)/e,o=T?Math.round((t.h-1)/r):(t.h-1)/r;if(!n[a])return;const{frame:{x:i,y:m},sourceSize:{w:f,h:p}}=n[a],d=1/s,h=1/o;if(_.current&&_.current.map&&(c=$>0?d*(i/f):d*(i/f)-_.current.map.repeat.x,l=Math.abs(1-h)-h*(m/p),_.current.map.offset.x=c,_.current.map.offset.y=l),null!=u){let e=Math.floor(u*n.length);e=Math.max(0,Math.min(e,n.length-1)),isNaN(e)&&(e=0),H.current=e}else O?H.current-=1:H.current+=1};return t.useFrame(((t,n)=>{var u,a;null!=(u=V.current)&&u.frames&&null!=(a=_.current)&&a.map&&(ee.current||se.hasEnded||!y&&!x||((()=>{if(null===(t=V.current)||!("meta"in t)||!("frames"in t))return;var t;const{meta:{size:n},frames:u}=V.current,{w:a,h:s}=c.getFirstFrame(u,l).sourceSize,o=Array.isArray(u)?u:l?u[l]:[],i=r||o.length-1;var m=void 0===N?se.current:N;if(W<=0)return H.current=r||e||0,void ie(a,s,n,o);const f=window.performance.now(),p=f-G.current;if(!(p<=W)){var d=O?H.current<0:H.current>i,v=O?H.current===i:0===H.current,y=O?H.current<0:H.current>=i;if(d){if(H.current=h&&null!=e?e:0,O&&(H.current=i),h?null==b||b({currentFrameName:null!=l?l:"",currentFrame:H.current}):(null==F||F({currentFrameName:null!=l?l:"",currentFrame:H.current}),se.hasEnded=!q,q&&(ee.current=!0)),!h)return}else v&&(null==E||E({currentFrameName:null!=l?l:"",currentFrame:H.current}));void 0!==m&&y?!1===te.current&&(null==F||F({currentFrameName:null!=l?l:"",currentFrame:H.current}),te.current=!0):te.current=!1,p<=W||(G.current=f-p%W,ie(a,s,n,o))}})(),null==g||g({currentFrameName:J.current,currentFrame:H.current})))})),i.createElement("group",o.default({},k,{ref:C,scale:function(e=new m.Vector3(1,1,1),r=1){return"number"==typeof r?e.multiplyScalar(r):Array.isArray(r)?e.multiply(new m.Vector3(...r)):r instanceof m.Vector3?e.multiply(r):void 0}(Y,k.scale)}),i.createElement(f.Provider,{value:se},M&&i.createElement(a.Billboard,null,i.createElement("mesh",o.default({ref:U,scale:1,geometry:p},I),i.createElement("meshBasicMaterial",{premultipliedAlpha:!1,toneMapped:!1,side:m.DoubleSide,ref:_,map:X,transparent:!0,alphaTest:null!=R?R:0}))),!M&&i.createElement(u.Instances,o.default({geometry:p,limit:null!=z?z:1},I),i.createElement("meshBasicMaterial",{premultipliedAlpha:!1,toneMapped:!1,side:m.DoubleSide,ref:_,map:X,transparent:!0,alphaTest:null!=R?R:0}),(null!=D?D:[0]).map(((e,r)=>i.createElement(u.Instance,o.default({key:r,ref:1===(null==D?void 0:D.length)?U:null,position:e,scale:1},I))))),A))}));exports.SpriteAnimator=d,exports.useSpriteAnimator=function(){return i.useContext(f)};
