import*as e from"../../../../core/common/common.js";import*as t from"../../../../core/i18n/i18n.js";import*as n from"../../../components/icon_button/icon_button.js";import*as i from"../../legacy.js";import*as o from"../../../../core/host/host.js";import*as s from"../../../../core/platform/platform.js";import*as r from"../../../../core/root/root.js";import*as a from"../../../../core/sdk/sdk.js";import*as l from"../../../../models/javascript_metadata/javascript_metadata.js";import*as c from"../../../../models/text_utils/text_utils.js";import*as p from"../../../components/text_editor/text_editor.js";import*as d from"../../../visual_logging/visual_logging.js";import*as h from"../../../../models/formatter/formatter.js";import*as u from"../../../../models/source_map_scopes/source_map_scopes.js";import*as m from"../utils/utils.js";const b=new CSSStyleSheet;b.replaceSync(".custom-expandable-section{display:inline-flex;flex-direction:column}.custom-expand-icon{user-select:none;margin-right:4px;margin-bottom:-4px}.custom-expandable-section-standard-section{display:inline-flex}.custom-expandable-section-default-body{padding-left:12px}\n/*# sourceURL=customPreviewComponent.css */\n");const v={emptyD:"empty × {PH1}",empty:"empty",thePropertyIsComputedWithAGetter:"The property is computed with a getter"},y=t.i18n.registerUIStrings("ui/legacy/components/object_ui/RemoteObjectPreviewFormatter.ts",v),g=t.i18n.getLocalizedString.bind(void 0,y);class f{static objectPropertyComparator(e,t){return n(e)-n(t);function n(e){return"[[PromiseState]]"===e.name?1:"[[PromiseResult]]"===e.name?2:"[[GeneratorState]]"===e.name||"[[PrimitiveValue]]"===e.name||"[[WeakRefTarget]]"===e.name?3:"function"===e.type||e.name.startsWith("#")?5:4}}appendObjectPreview(e,t,n){const o=t.description,s=new Set(["arraybuffer","dataview","error","null","regexp","webassemblymemory","internal#entry","trustedtype"]);if("object"!==t.type||t.subtype&&s.has(t.subtype)||n)return void e.appendChild(this.renderPropertyPreview(t.type,t.subtype,void 0,o));const r="array"===t.subtype||"typedarray"===t.subtype;if(o){let n;if(r){const e=a.RemoteObject.RemoteObject.arrayLength(t),i=e>1?"("+e+")":"",s=a.RemoteObject.RemoteObject.arrayNameFromDescription(o);n="Array"===s?i:s+i}else{n="Object"===o?"":o}n.length>0&&(e.createChild("span","object-description").textContent=n+" ")}const l=e.createChild("span","object-properties-preview");if(i.UIUtils.createTextChild(l,r?"[":"{"),t.entries?this.appendEntriesPreview(l,t):r?this.appendArrayPropertiesPreview(l,t):this.appendObjectPropertiesPreview(l,t),t.overflow){const e=l.textContent&&l.textContent.length>1?", …":"…";l.createChild("span").textContent=e}i.UIUtils.createTextChild(l,r?"]":"}")}abbreviateFullQualifiedClassName(e){const t=e.split(".");for(let e=0;e<t.length-1;++e)t[e]=s.StringUtilities.trimMiddle(t[e],3);return t.join(".")}appendObjectPropertiesPreview(e,t){const n=t.properties.filter((e=>"accessor"!==e.type)).sort(f.objectPropertyComparator);for(let o=0;o<n.length;++o){o>0&&i.UIUtils.createTextChild(e,", ");const s=n[o],r=s.name;if("promise"===t.subtype&&"[[PromiseState]]"===r){e.appendChild(this.renderDisplayName("<"+s.value+">"));const t=o+1<n.length?n[o+1]:null;t&&"[[PromiseResult]]"===t.name&&("pending"!==s.value&&(i.UIUtils.createTextChild(e,": "),e.appendChild(this.renderPropertyPreviewOrAccessor([t]))),o++)}else"generator"===t.subtype&&"[[GeneratorState]]"===r?e.appendChild(this.renderDisplayName("<"+s.value+">")):"[[PrimitiveValue]]"===r?e.appendChild(this.renderPropertyPreviewOrAccessor([s])):"[[WeakRefTarget]]"===r?"undefined"===s.type?e.appendChild(this.renderDisplayName("<cleared>")):e.appendChild(this.renderPropertyPreviewOrAccessor([s])):(e.appendChild(this.renderDisplayName(r)),i.UIUtils.createTextChild(e,": "),e.appendChild(this.renderPropertyPreviewOrAccessor([s])))}}appendArrayPropertiesPreview(e,t){const n=a.RemoteObject.RemoteObject.arrayLength(t),o=t.properties.filter((e=>-1!==r(e.name))).sort((function(e,t){return r(e.name)-r(t.name)})),s=t.properties.filter((e=>-1===r(e.name))).sort(f.objectPropertyComparator);function r(e){const t=Number(e)>>>0;return String(t)===e&&t<n?t:-1}const l=!t.overflow;let c=-1,p=!1;for(let t=0;t<o.length;++t){p&&i.UIUtils.createTextChild(e,", ");const n=o[t],s=r(n.name);l&&s-c>1&&(d(s),i.UIUtils.createTextChild(e,", ")),l||t===s||(e.appendChild(this.renderDisplayName(n.name)),i.UIUtils.createTextChild(e,": ")),e.appendChild(this.renderPropertyPreviewOrAccessor([n])),c=s,p=!0}l&&n-c>1&&(p&&i.UIUtils.createTextChild(e,", "),d(n));for(let t=0;t<s.length;++t){p&&i.UIUtils.createTextChild(e,", ");const n=s[t];e.appendChild(this.renderDisplayName(n.name)),i.UIUtils.createTextChild(e,": "),e.appendChild(this.renderPropertyPreviewOrAccessor([n])),p=!0}function d(t){const n=e.createChild("span","object-value-undefined"),i=t-c-1;n.textContent=1!==i?g(v.emptyD,{PH1:i}):g(v.empty),p=!0}}appendEntriesPreview(e,t){if(t.entries)for(let n=0;n<t.entries.length;++n){n>0&&i.UIUtils.createTextChild(e,", ");const o=t.entries[n];o.key&&(this.appendObjectPreview(e,o.key,!0),i.UIUtils.createTextChild(e," => ")),this.appendObjectPreview(e,o.value,!0)}}renderDisplayName(e){const t=document.createElement("span");t.classList.add("name");const n=/^\s|\s$|^$|\n/.test(e);return t.textContent=n?'"'+e.replace(/\n/g,"↵")+'"':e,t}renderPropertyPreviewOrAccessor(e){const t=e[e.length-1];if(!t)throw new Error("Could not find property");return this.renderPropertyPreview(t.type,t.subtype,t.name,t.value)}renderPropertyPreview(e,t,n,o){const r=document.createElement("span");if(r.classList.add("object-value-"+(t||e)),o=o||"","accessor"===e)return r.textContent="(...)",i.Tooltip.Tooltip.install(r,g(v.thePropertyIsComputedWithAGetter)),r;if("function"===e)return r.textContent="ƒ",r;if("object"===e&&"trustedtype"===t&&n)return w(r,o,n),r;if("object"===e&&"node"===t&&o)return x(r,o),r;if("string"===e)return i.UIUtils.createTextChildren(r,s.StringUtilities.formatAsJSLiteral(o)),r;if("object"===e&&!t){let e=this.abbreviateFullQualifiedClassName(o);return"Object"===e&&(e="{…}"),r.textContent=e,i.Tooltip.Tooltip.install(r,o),r}return r.textContent=o,r}}const x=function(e,t){const n=t.match(/([^#.]+)(#[^.]+)?(\..*)?/);n&&(e.createChild("span","webkit-html-tag-name").textContent=n[1],n[2]&&(e.createChild("span","webkit-html-attribute-value").textContent=n[2]),n[3]&&(e.createChild("span","webkit-html-attribute-name").textContent=n[3]))},w=function(e,t,n){i.UIUtils.createTextChildren(e,`${n} `);const o=document.createElement("span");o.classList.add("object-value-string"),i.UIUtils.createTextChildren(o,'"',t.replace(/\n/g,"↵"),'"'),e.appendChild(o)};var C=Object.freeze({__proto__:null,RemoteObjectPreviewFormatter:f,createSpansForNodeTitle:x,createSpanForTrustedType:w});class j{static wrapObjectLiteral(e){const t=/^\s*\{\s*(.*)\s*\}[\s;]*$/.exec(e);if(null===t)return e;const[,n]=t;let i=0;for(const t of n)if("{"===t)i++;else if("}"===t&&--i<0)return e;const o=(async()=>0).constructor;try{o("return {"+n+"};");const e="({"+n+"})";return o(e),e}catch(t){return e}}static async evaluateAndBuildPreview(e,t,n,o,s,r,l=!1,c=!1){const p=i.Context.Context.instance().flavor(a.RuntimeModel.ExecutionContext),d=e.length>E;if(!e||!p||t&&d)return{preview:document.createDocumentFragment(),result:null};let m=e;const b=p.debuggerModel.selectedCallFrame();if(b&&b.script.isJavaScript()){const e=await u.NamesResolver.allVariablesInCallFrame(b);try{m=await h.FormatterWorkerPool.formatterWorkerPool().javaScriptSubstitute(m,e)}catch{}}m=j.wrapObjectLiteral(m);const v={expression:m,generatePreview:!0,includeCommandLineAPI:!0,throwOnSideEffect:t,timeout:o,objectGroup:r,disableBreaks:!0,replMode:n,silent:c},y=await p.evaluate(v,!1,l);return{preview:j.buildEvaluationPreview(y,s),result:y}}static buildEvaluationPreview(e,t){const n=document.createDocumentFragment();if("error"in e)return n;if(e.exceptionDetails&&e.exceptionDetails.exception&&e.exceptionDetails.exception.description){const i=e.exceptionDetails.exception.description;return(i.startsWith("TypeError: ")||t)&&(n.createChild("span").textContent=e.exceptionDetails.text+" "+i),n}const i=new f,{preview:o,type:r,subtype:a,className:l,description:c}=e.object;if(o&&"object"===r&&"node"!==a&&"trustedtype"!==a)i.appendObjectPreview(n,o,!1);else{const e=i.renderPropertyPreview(r,a,l,s.StringUtilities.trimEndWithMaxLength(c||"",400));n.appendChild(e)}return n}}let E=2e3;var P=Object.freeze({__proto__:null,JavaScriptREPL:j,setMaxLengthForEvaluation:function(e){E=e},getMaxLengthForEvaluation:function(){return E}});const T=new CSSStyleSheet;T.replaceSync('.object-properties-section-dimmed{opacity:60%}.object-properties-section{padding:0;color:var(--sys-color-on-surface);display:flex;flex-direction:column}.object-properties-section li{user-select:text}.object-properties-section li::before{top:-1px;margin-right:2px}.object-properties-section li.editing-sub-part{padding:3px 12px 8px 6px;margin:-1px -6px -8px;text-overflow:clip}.object-properties-section li.editing{margin-left:10px;text-overflow:clip}.tree-outline ol.title-less-mode{padding-left:0}.object-properties-section .own-property{font-weight:bold}.object-properties-section .synthetic-property{color:var(--sys-color-token-subtle)}.object-properties-section .private-property-hash{color:var(--sys-color-on-surface)}.object-properties-section-root-element{display:flex;flex-direction:row}.object-properties-section .editable-div{overflow:hidden}.object-properties-section [data-webidl="true"] > .name-and-value > .adorner{flex-shrink:0;width:16px;height:16px;margin-right:2px}.object-properties-section [data-webidl="true"] > .name-and-value > .name{font-weight:bold}.name-and-value{overflow:hidden;line-height:16px;display:flex;white-space:nowrap}.name-and-value .separator{white-space:pre;flex-shrink:0}.editing-sub-part .name-and-value{overflow:visible;display:inline-flex}.property-prompt{margin-left:4px}.tree-outline.hide-selection-when-blurred .selected:focus-visible{background:none}.tree-outline.hide-selection-when-blurred .selected:focus-visible ::slotted(*),\n.tree-outline.hide-selection-when-blurred .selected:focus-visible .tree-element-title,\n.tree-outline.hide-selection-when-blurred .selected:focus-visible .name-and-value,\n.tree-outline.hide-selection-when-blurred .selected:focus-visible .gray-info-message{background:var(--sys-color-state-focus-highlight);border-radius:2px}@media (forced-colors: active){.object-properties-section-dimmed{opacity:100%}.tree-outline.hide-selection-when-blurred .selected:focus-visible{background:Highlight}.tree-outline li:hover .tree-element-title,\n  .tree-outline li.selected .tree-element-title{color:ButtonText}.tree-outline.hide-selection-when-blurred .selected:focus-visible .tree-element-title,\n  .tree-outline.hide-selection-when-blurred .selected:focus-visible .name-and-value{background:transparent;box-shadow:none}.tree-outline.hide-selection-when-blurred .selected:focus-visible span,\n  .tree-outline.hide-selection-when-blurred .selected:focus-visible .gray-info-message{color:HighlightText}.tree-outline-disclosure:hover li.parent::before{background-color:ButtonText}}\n/*# sourceURL=objectPropertiesSection.css */\n');const O=new CSSStyleSheet;O.replaceSync(".value.object-value-node:hover{background-color:var(--sys-color-state-hover-on-subtle)}.object-value-function-prefix,\n.object-value-boolean{color:var(--sys-color-token-attribute-value)}.object-value-function{font-style:italic}.object-value-function.linkified:hover{--override-linkified-hover-background:rgb(0 0 0/10%);background-color:var(--override-linkified-hover-background);cursor:pointer}.theme-with-dark-background .object-value-function.linkified:hover,\n:host-context(.theme-with-dark-background) .object-value-function.linkified:hover{--override-linkified-hover-background:rgb(230 230 230/10%)}.object-value-number{color:var(--sys-color-token-attribute-value)}.object-value-bigint{color:var(--sys-color-token-comment)}.object-value-string,\n.object-value-regexp,\n.object-value-symbol{white-space:pre;unicode-bidi:-webkit-isolate;color:var(--sys-color-token-property-special)}.object-value-node{position:relative;vertical-align:baseline;color:var(--sys-color-token-variable);white-space:nowrap}.object-value-null,\n.object-value-undefined{color:var(--sys-color-state-disabled)}.object-value-unavailable{color:var(--sys-color-token-tag)}.object-value-calculate-value-button:hover{text-decoration:underline}.object-properties-section-custom-section{display:inline-flex;flex-direction:column}.theme-with-dark-background .object-value-number,\n:host-context(.theme-with-dark-background) .object-value-number,\n.theme-with-dark-background .object-value-boolean,\n:host-context(.theme-with-dark-background) .object-value-boolean{--override-primitive-dark-mode-color:hsl(252deg 100% 75%);color:var(--override-primitive-dark-mode-color)}.object-properties-section .object-description{color:var(--sys-color-token-subtle)}.value .object-properties-preview{white-space:nowrap}.name{color:var(--sys-color-token-tag);flex-shrink:0}.object-properties-preview .name{color:var(--sys-color-token-subtle)}@media (forced-colors: active){.object-value-calculate-value-button:hover{forced-color-adjust:none;color:Highlight}}\n/*# sourceURL=objectValue.css */\n");const I={exceptionS:"[Exception: {PH1}]",unknown:"unknown",expandRecursively:"Expand recursively",collapseChildren:"Collapse children",noProperties:"No properties",dots:"(...)",invokePropertyGetter:"Invoke property getter",showAllD:"Show all {PH1}",valueUnavailable:"<value unavailable>",valueNotAccessibleToTheDebugger:"Value is not accessible to the debugger",copyValue:"Copy value",copyPropertyPath:"Copy property path",stringIsTooLargeToEdit:"<string is too large to edit>",showMoreS:"Show more ({PH1})",longTextWasTruncatedS:"long text was truncated ({PH1})",copy:"Copy",revealInMemoryInpector:"Reveal in Memory inspector panel"},S=t.i18n.registerUIStrings("ui/legacy/components/object_ui/ObjectPropertiesSection.ts",I),k=t.i18n.getLocalizedString.bind(void 0,S),L=new WeakMap,M=new WeakMap,N=l.JavaScriptMetadata.JavaScriptMetadataImpl.domPinnedProperties.DOMPinnedProperties;class F extends i.TreeOutline.TreeOutlineInShadow{object;editable;objectTreeElementInternal;titleElement;skipProtoInternal;constructor(e,t,n,i){super(),this.object=e,this.editable=!0,i||this.hideOverflow(),this.setFocusable(!0),this.setShowSelectionOnKeyboardFocus(!0),this.objectTreeElementInternal=new R(e,n),this.appendChild(this.objectTreeElementInternal),"string"!=typeof t&&t?(this.titleElement=t,this.element.appendChild(t)):(this.titleElement=this.element.createChild("span"),this.titleElement.textContent=t||""),this.titleElement instanceof HTMLElement&&!this.titleElement.hasAttribute("tabIndex")&&(this.titleElement.tabIndex=-1),M.set(this.element,this),this.registerCSSFiles([O,T]),this.rootElement().childrenListElement.classList.add("source-code","object-properties-section")}static defaultObjectPresentation(e,t,n,i){const o=F.defaultObjectPropertiesSection(e,t,n,i);return e.hasChildren?o.element:o.titleElement}static defaultObjectPropertiesSection(e,t,n,o){const s=document.createElement("span");s.classList.add("source-code");const r=i.UIUtils.createShadowRootWithCoreStyles(s,{cssFile:[O],delegatesFocus:void 0}),a=F.createPropertyValue(e,!1,!0);r.appendChild(a.element);const l=new F(e,s,t);return l.editable=!1,n&&l.skipProto(),o&&l.setEditable(!1),l}static assignWebIDLMetadata(e,t){if(!e)return;const n="object"===e.type&&null!==e.className?N[e.className]:void 0;if(!n)return;e.webIdl={info:n,state:new Map};const i=(n.includes?.map((e=>N[e]))??[]).flatMap((e=>Object.entries(e?.props??{}))),o={...n.props,...Object.fromEntries(i)};for(const e of t){const t=o[e.name];t&&(e.webIdl={info:t})}const s=F.getPropertyValuesByNames(t),r=e.webIdl.info.rules;if(r)for(const{when:t,is:n}of r)s.get(t)?.value===n&&e.webIdl.state.set(t,n);for(const n of t)if(n.webIdl){const t=e.webIdl.state,i=n.webIdl.info.rules;n.webIdl.applicable=!r&&!i||(!i||i?.some((e=>t.get(e.when)===e.is)))}}static getPropertyValuesByNames(e){const t=new Map;for(const n of e)t.set(n.name,n.value);return t}static compareProperties(e,t){if(!e.synthetic&&t.synthetic)return 1;if(!t.synthetic&&e.synthetic)return-1;if(!e.isOwn&&t.isOwn)return 1;if(!t.isOwn&&e.isOwn)return-1;if(!e.enumerable&&t.enumerable)return 1;if(!t.enumerable&&e.enumerable)return-1;if(e.symbol&&!t.symbol)return 1;if(t.symbol&&!e.symbol)return-1;if(e.private&&!t.private)return 1;if(t.private&&!e.private)return-1;const n=e.name,i=t.name;return n.startsWith("_")&&!i.startsWith("_")?1:i.startsWith("_")&&!n.startsWith("_")?-1:s.StringUtilities.naturalOrderComparator(n,i)}static createNameElement(e,t){return null===e?i.Fragment.html`<span class="name"></span>`:/^\s|\s$|^$|\n/.test(e)?i.Fragment.html`<span class="name">"${e.replace(/\n/g,"↵")}"</span>`:t?i.Fragment.html`<span class="name">
  <span class="private-property-hash">${e[0]}</span>${e.substring(1)}
  </span>`:i.Fragment.html`<span class="name">${e}</span>`}static valueElementForFunctionDescription(e,t,n){const o=document.createElement("span");o.classList.add("object-value-function");const r=(e=e||"").replace(/^function [gs]et /,"function ").replace(/^function [gs]et\(/,"function(").replace(/^[gs]et /,"");n=n||"";const a=r.match(/^(async\s+function)/),l=r.startsWith("function*"),c=r.startsWith("*"),p=!l&&r.startsWith("function"),d=r.startsWith("class ")||r.startsWith("class{"),h=r.indexOf("=>"),u=!a&&!l&&!p&&!d&&h>0;let m;if(d){m=r.substring(5);const e=/^[^{\s]+/.exec(m.trim());let t=n;e&&(t=e[0].trim()||n),v("class",m,t)}else if(a)m=r.substring(a[1].length),v("async ƒ",m,b(m));else if(l)m=r.substring(9),v("ƒ*",m,b(m));else if(c)m=r.substring(1),v("ƒ*",m,b(m));else if(p)m=r.substring(8),v("ƒ",m,b(m));else if(u){const e=60;let t=r;n?t=n+"()":r.length>e&&(t=r.substring(0,h+2)+" {…}"),v("",r,t)}else v("ƒ",r,b(r));return i.Tooltip.Tooltip.install(o,s.StringUtilities.trimEndWithMaxLength(e,500)),o;function b(e){const t=e.indexOf("("),i=e.match(/\)\s*{/);if(-1!==t&&i&&void 0!==i.index&&i.index>t){return(e.substring(0,t).trim()||n)+e.substring(t,i.index+1)}return n+"()"}function v(e,n,r){e.length&&(o.createChild("span","object-value-function-prefix").textContent=e+" "),t?i.UIUtils.createTextChild(o,s.StringUtilities.trimEndWithMaxLength(n.trim(),200)):i.UIUtils.createTextChild(o,r.replace(/\n/g," "))}}static createPropertyValueWithCustomSupport(e,t,n,i,o,s,r){if(e.customPreview()){const t=new q(e).element;return t.classList.add("object-properties-section-custom-section"),new H(t)}return F.createPropertyValue(e,t,n,i,o,s,r)}static appendMemoryIcon(t,o,s){if(!o.isLinearMemoryInspectable())return;const r=new n.Icon.Icon;r.data={iconName:"memory",color:"var(--icon-default)",width:"16px",height:"13px"},r.addEventListener("click",(t=>{t.consume(),e.Revealer.reveal(new a.RemoteObject.LinearMemoryInspectable(o,s))})),r.setAttribute("jslog",`${d.action("open-memory-inspector").track({click:!0})}`);const l=k(I.revealInMemoryInpector);i.Tooltip.Tooltip.install(r,l),i.ARIAUtils.setLabel(r,l),r.style.setProperty("vertical-align","sub"),r.style.setProperty("cursor","pointer"),t.appendChild(r)}static createPropertyValue(n,o,r,l,c,p=!1,d){let h;const u=n.type,m=n.subtype,b=n.description||"",v=n.className;if("object"===u&&"internal#location"===m){const e=n.debuggerModel().createRawLocationByScriptId(n.value.scriptId,n.value.lineNumber,n.value.columnNumber);if(e&&c)return new H(c.linkifyRawLocation(e,s.DevToolsPath.EmptyUrlString));h=new H(function(){const e=document.createElement("span");return e.textContent="<"+k(I.unknown)+">",i.Tooltip.Tooltip.install(e,b||""),e}())}else if("string"===u&&"string"==typeof b)h=y();else if("object"===u&&"trustedtype"===m)h=function(){const e=document.createElement("span");e.classList.add("object-value-trustedtype");const t=`${v} "${b}"`;let n;if(t.length>U)n=new J(e,t,50);else{const o=y();i.UIUtils.createTextChild(e,`${v} `),e.appendChild(o.element),n=new H(e),i.Tooltip.Tooltip.install(e,t)}return n}();else if("function"===u)h=new H(F.valueElementForFunctionDescription(b));else if("object"===u&&"node"===m&&b)h=new H(function(){const t=document.createElement("span");return t.classList.add("object-value-node"),x(t,b),t.addEventListener("click",(t=>{e.Revealer.reveal(n),t.consume(!0)}),!1),t.addEventListener("mousemove",(()=>a.OverlayModel.OverlayModel.highlightObjectAsDOMNode(n)),!1),t.addEventListener("mouseleave",(()=>a.OverlayModel.OverlayModel.hideDOMNodeHighlight()),!1),t}());else{const e=document.createElement("span");if(e.classList.add("object-value-"+(m||u)),n.preview&&r){(new f).appendObjectPreview(e,n.preview,!1),h=new H(e),i.Tooltip.Tooltip.install(h.element,b||"")}else b.length>U?h=new J(e,b,50):(h=new H(e),h.element.textContent=b,i.Tooltip.Tooltip.install(h.element,b));p||this.appendMemoryIcon(e,n,d)}if(o){const e=document.createElement("span");e.classList.add("error"),e.classList.add("value"),e.appendChild(t.i18n.getFormatLocalizedString(S,I.exceptionS,{PH1:h.element})),h.element=e}return h.element.classList.add("value"),h;function y(){const e=document.createElement("span");e.classList.add("object-value-string");const t=JSON.stringify(b);let n;return b.length>U?n=new J(e,t,50):(i.UIUtils.createTextChild(e,t),n=new H(e),i.Tooltip.Tooltip.install(e,b)),n}}static formatObjectAsFunction(t,n,i,o){return t.debuggerModel().functionDetailsPromise(t).then((function(s){i&&s&&s.location&&(n.classList.add("linkified"),n.addEventListener("click",(()=>(e.Revealer.reveal(s.location),!1))));let r=o?"":"anonymous";s&&s.functionName&&(r=s.functionName);const a=F.valueElementForFunctionDescription(t.description,o,r);n.appendChild(a)}))}static isDisplayableProperty(e,t){if(!t||!t.synthetic)return!0;const n=e.name;return!("[[Entries]]"===t.name&&("length"===n||"__proto__"===n))}skipProto(){this.skipProtoInternal=!0}expand(){this.objectTreeElementInternal.expand()}setEditable(e){this.editable=e}objectTreeElement(){return this.objectTreeElementInternal}enableContextMenu(){this.element.addEventListener("contextmenu",this.contextMenuEventFired.bind(this),!1)}contextMenuEventFired(e){const t=new i.ContextMenu.ContextMenu(e);t.appendApplicableItems(this.object),this.object instanceof a.RemoteObject.LocalJSONObject&&(t.viewSection().appendItem(k(I.expandRecursively),this.objectTreeElementInternal.expandRecursively.bind(this.objectTreeElementInternal,100),{jslogContext:"expand-recursively"}),t.viewSection().appendItem(k(I.collapseChildren),this.objectTreeElementInternal.collapseChildren.bind(this.objectTreeElementInternal),{jslogContext:"collapse-children"})),t.show()}titleLessMode(){this.objectTreeElementInternal.listItemElement.classList.add("hidden"),this.objectTreeElementInternal.childrenListElement.classList.add("title-less-mode"),this.objectTreeElementInternal.expand()}}let U=1e4;class A extends i.TreeOutline.TreeOutlineInShadow{editable;constructor(e){super(),this.registerCSSFiles([O,T]),this.editable=!(e&&e.readOnly),this.contentElement.classList.add("source-code"),this.contentElement.classList.add("object-properties-section")}}class R extends i.TreeOutline.TreeElement{object;linkifier;emptyPlaceholder;propertiesMode;extraProperties;targetObject;toggleOnClick;constructor(e,t,n,i=1,o=[],s=e){super(document.createElement("slot")),this.object=e,this.linkifier=t,this.emptyPlaceholder=n,this.propertiesMode=i,this.extraProperties=o,this.targetObject=s,this.setExpandable(!0),this.selectable=!0,this.toggleOnClick=!0,this.listItemElement.classList.add("object-properties-section-root-element"),this.listItemElement.addEventListener("contextmenu",this.onContextMenu.bind(this),!1)}onexpand(){this.treeOutline&&this.treeOutline.element.classList.add("expanded")}oncollapse(){this.treeOutline&&this.treeOutline.element.classList.remove("expanded")}ondblclick(e){return!0}onContextMenu(e){const t=new i.ContextMenu.ContextMenu(e);if(t.appendApplicableItems(this.object),this.object instanceof a.RemoteObject.LocalJSONObject){const{value:e}=this.object,n="object"==typeof e?JSON.stringify(e,null,2):e,i=()=>{o.userMetrics.actionTaken(o.UserMetrics.Action.NetworkPanelCopyValue),o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(n)};t.clipboardSection().appendItem(k(I.copyValue),i,{jslogContext:"copy-value"})}t.viewSection().appendItem(k(I.expandRecursively),this.expandRecursively.bind(this,100),{jslogContext:"expand-recursively"}),t.viewSection().appendItem(k(I.collapseChildren),this.collapseChildren.bind(this),{jslogContext:"collapse-children"}),t.show()}async onpopulate(){const e=this.treeOutline,t=!!e&&Boolean(e.skipProtoInternal);return D.populate(this,this.object,t,!1,this.linkifier,this.emptyPlaceholder,this.propertiesMode,this.extraProperties,this.targetObject)}}class D extends i.TreeOutline.TreeElement{property;toggleOnClick;highlightChanges;linkifier;maxNumPropertiesToShow;nameElement;valueElement;rowContainer;readOnly;prompt;editableDiv;propertyValue;expandedValueElement;constructor(e,t){super(),this.property=e,this.toggleOnClick=!0,this.highlightChanges=[],this.linkifier=t,this.maxNumPropertiesToShow=200,this.listItemElement.addEventListener("contextmenu",this.contextMenuFired.bind(this),!1),this.listItemElement.dataset.objectPropertyNameForTest=e.name,this.setExpandRecursively("[[Prototype]]"!==e.name)}static async populate(e,t,n,i,o,s,r=1,l,c){if(t.arrayLength()>100)return e.removeChildren(),void _.populateArray(e,t,0,t.arrayLength()-1,o);let p,d=null;switch(r){case 0:({properties:p}=await t.getAllProperties(!1,!0));break;case 1:({properties:p,internalProperties:d}=await a.RemoteObject.RemoteObject.loadFromObjectPerProto(t,!0))}e.removeChildren(),p&&(void 0!==l&&p.push(...l),D.populateWithProperties(e,p,d,n,i,c||t,o,s))}static populateWithProperties(e,t,n,i,o,s,r,l){F.assignWebIDLMetadata(s,t),t.sort(F.compareProperties);const c=(n=n||[]).find((e=>"[[Entries]]"===e.name));if(c){L.set(c,s);const t=new D(c,r);t.setExpandable(!0),t.expand(),e.appendChild(t)}const p=[];for(let n=0;n<t.length;++n){const i=t[n];if(L.set(i,s),!F.isDisplayableProperty(i,e.property))continue;if(i.isOwn&&!o){if(i.getter){const e=new a.RemoteObject.RemoteObjectProperty("get "+i.name,i.getter,!1);L.set(e,s),p.push(e)}if(i.setter){const e=new a.RemoteObject.RemoteObjectProperty("set "+i.name,i.setter,!1);L.set(e,s),p.push(e)}}if(i.getter||!i.isAccessorProperty()){const t=new D(i,r);"memories"===i.name&&"Memories"===i.value?.className&&(t.updateExpandable(),t.isExpandable()&&t.expand()),e.appendChild(t)}}for(let t=0;t<p.length;++t)e.appendChild(new D(p[t],r));for(const t of n){L.set(t,s);const n=new D(t,r);"[[Entries]]"!==t.name&&("[[Prototype]]"===t.name&&i||e.appendChild(n))}D.appendEmptyPlaceholderIfNeeded(e,l)}static appendEmptyPlaceholderIfNeeded(e,t){if(e.childCount())return;const n=document.createElement("div");n.classList.add("gray-info-message"),n.textContent=t||k(I.noProperties);const o=new i.TreeOutline.TreeElement(n);e.appendChild(o)}static createRemoteObjectAccessorPropertySpan(e,t,n){const o=document.createElement("span"),s=o.createChild("span");if(s.textContent=k(I.dots),!e)return o;function r(e){let t=this;const n=JSON.parse(e);for(let e=0,i=n.length;e<i;++e)t=t[n[e]];return t}return s.classList.add("object-value-calculate-value-button"),i.Tooltip.Tooltip.install(s,k(I.invokePropertyGetter)),s.addEventListener("click",(function(i){i.consume(),e&&e.callFunction(r,[{value:JSON.stringify(t)}]).then(n)}),!1),o}setSearchRegex(e,t){let n=i.UIUtils.highlightedSearchResultClassName;if(t&&(n+=" "+t),this.revertHighlightChanges(),this.applySearch(e,this.nameElement,n),this.property.value){"object"!==this.property.value.type&&this.applySearch(e,this.valueElement,n)}return Boolean(this.highlightChanges.length)}applySearch(e,t,n){const o=[],s=t.textContent||"";e.lastIndex=0;let r=e.exec(s);for(;r;)o.push(new c.TextRange.SourceRange(r.index,r[0].length)),r=e.exec(s);o.length&&i.UIUtils.highlightRangesWithStyleClass(t,o,n,this.highlightChanges)}showAllPropertiesElementSelected(e){return this.removeChild(e),this.children().forEach((e=>{e.hidden=!1})),!1}createShowAllPropertiesButton(){const e=document.createElement("div");e.classList.add("object-value-calculate-value-button"),e.textContent=k(I.dots),i.Tooltip.Tooltip.install(e,k(I.showAllD,{PH1:this.childCount()}));const t=this.children();for(let e=this.maxNumPropertiesToShow;e<this.childCount();++e)t[e].hidden=!0;const n=new i.TreeOutline.TreeElement(e);n.onselect=this.showAllPropertiesElementSelected.bind(this,n),this.appendChild(n)}revertHighlightChanges(){i.UIUtils.revertDomChanges(this.highlightChanges),this.highlightChanges=[]}async onpopulate(){const e=this.property.value;console.assert(void 0!==e);const t=this.treeOutline,n=!!t&&Boolean(t.skipProtoInternal),i="[[Prototype]]"!==this.property.name?e:L.get(this.property);i&&(await D.populate(this,e,n,!1,this.linkifier,void 0,void 0,void 0,i),this.childCount()>this.maxNumPropertiesToShow&&this.createShowAllPropertiesButton())}ondblclick(e){const t=e.target,n=t.isSelfOrDescendant(this.valueElement)||this.expandedValueElement&&t.isSelfOrDescendant(this.expandedValueElement);return this.property.value&&!this.property.value.customPreview()&&n&&(this.property.writable||this.property.setter)&&this.startEditing(),!1}onenter(){return!(!this.property.value||this.property.value.customPreview()||!this.property.writable&&!this.property.setter)&&(this.startEditing(),!0)}onattach(){this.update(),this.updateExpandable()}onexpand(){this.showExpandedValueElement(!0)}oncollapse(){this.showExpandedValueElement(!1)}showExpandedValueElement(e){this.expandedValueElement&&(e?this.rowContainer.replaceChild(this.expandedValueElement,this.valueElement):this.rowContainer.replaceChild(this.valueElement,this.expandedValueElement))}createExpandedValueElement(e,t){if(!(e.hasChildren&&!e.customPreview()&&"node"!==e.subtype&&"function"!==e.type&&("object"!==e.type||e.preview)))return null;const n=document.createElement("span");return n.classList.add("value"),"Object"===e.description?n.textContent="":n.setTextContentTruncatedIfNeeded(e.description||""),n.classList.add("object-value-"+(e.subtype||e.type)),i.Tooltip.Tooltip.install(n,e.description||""),t||F.appendMemoryIcon(n,e),n}update(){this.nameElement=F.createNameElement(this.property.name,this.property.private),this.property.enumerable||this.nameElement.classList.add("object-properties-section-dimmed"),this.property.isOwn&&this.nameElement.classList.add("own-property"),this.property.synthetic&&this.nameElement.classList.add("synthetic-property"),this.updatePropertyPath();const e=this.property.synthetic&&"[[Entries]]"===this.property.name;if(e)this.valueElement=document.createElement("span"),this.valueElement.classList.add("value");else if(this.property.value){const e="[[Prototype]]"!==this.property.name;this.propertyValue=F.createPropertyValueWithCustomSupport(this.property.value,this.property.wasThrown,e,this.listItemElement,this.linkifier,this.property.synthetic,this.path()),this.valueElement=this.propertyValue.element}else if(this.property.getter){this.valueElement=document.createElement("span");const e=this.valueElement.createChild("span");e.textContent=k(I.dots),e.classList.add("object-value-calculate-value-button"),i.Tooltip.Tooltip.install(e,k(I.invokePropertyGetter));const t=L.get(this.property),n=this.property.getter;e.addEventListener("click",(e=>{e.consume();t.callFunction("\n          function invokeGetter(getter) {\n            return Function.prototype.apply.call(getter, this, []);\n          }",[a.RemoteObject.RemoteObject.toCallArgument(n)]).then(this.onInvokeGetterClick.bind(this))}),!1)}else this.valueElement=document.createElement("span"),this.valueElement.classList.add("object-value-unavailable"),this.valueElement.textContent=k(I.valueUnavailable),i.Tooltip.Tooltip.install(this.valueElement,k(I.valueNotAccessibleToTheDebugger));const t=this.valueElement.textContent;this.property.value&&t&&!this.property.wasThrown&&(this.expandedValueElement=this.createExpandedValueElement(this.property.value,this.property.synthetic));const o=r.Runtime.experiments.isEnabled("important-dom-properties");let s,l="";if(this.property.webIdl?.applicable&&o){const e=new n.Icon.Icon;e.data={iconName:"star",color:"var(--icon-default)",width:"16px",height:"16px"},l=i.Fragment.html`
         <span class='adorner'>${e}</span>
       `}s=e?i.Fragment.html`
        <span class='name-and-value'>${l}${this.nameElement}</span>
      `:i.Fragment.html`
        <span class='name-and-value'>${l}${this.nameElement}<span class='separator'>: </span>${this.valueElement}</span>
      `,this.listItemElement.removeChildren(),this.rowContainer=s,this.listItemElement.appendChild(this.rowContainer),o&&(this.listItemElement.dataset.webidl=this.property.webIdl?.applicable?"true":"false")}updatePropertyPath(){if(this.nameElement.title)return;const e=this.property.name;if(this.property.synthetic)return void i.Tooltip.Tooltip.install(this.nameElement,e);const t=this.parent instanceof D&&this.parent.nameElement&&!this.parent.property.synthetic?this.parent.nameElement.title:"";this.property.private||/^(?:[$_\p{ID_Start}])(?:[$_\u200C\u200D\p{ID_Continue}])*$/u.test(e)?i.Tooltip.Tooltip.install(this.nameElement,t?`${t}.${e}`:e):/^(?:0|[1-9]\d*)$/.test(e)?i.Tooltip.Tooltip.install(this.nameElement,`${t}[${e}]`):i.Tooltip.Tooltip.install(this.nameElement,`${t}[${JSON.stringify(e)}]`)}contextMenuFired(e){const t=new i.ContextMenu.ContextMenu(e);if(t.appendApplicableItems(this),this.property.symbol&&t.appendApplicableItems(this.property.symbol),this.property.value&&(t.appendApplicableItems(this.property.value),L.get(this.property)instanceof a.RemoteObject.LocalJSONObject)){const{value:{value:e}}=this.property,n="object"==typeof e?JSON.stringify(e,null,2):e,i=()=>{o.userMetrics.actionTaken(o.UserMetrics.Action.NetworkPanelCopyValue),o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(n)};t.clipboardSection().appendItem(k(I.copyValue),i,{jslogContext:"copy-value"})}if(!this.property.synthetic&&this.nameElement&&this.nameElement.title){const e=o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText.bind(o.InspectorFrontendHost.InspectorFrontendHostInstance,this.nameElement.title);t.clipboardSection().appendItem(k(I.copyPropertyPath),e,{jslogContext:"copy-property-path"})}L.get(this.property)instanceof a.RemoteObject.LocalJSONObject&&(t.viewSection().appendItem(k(I.expandRecursively),this.expandRecursively.bind(this,100),{jslogContext:"expand-recursively"}),t.viewSection().appendItem(k(I.collapseChildren),this.collapseChildren.bind(this),{jslogContext:"collapse-children"})),this.propertyValue&&this.propertyValue.appendApplicableItems(e,t,{}),t.show()}startEditing(){const e=this.treeOutline;if(this.prompt||!e||!e.editable||this.readOnly)return;if(this.editableDiv=this.rowContainer.createChild("span","editable-div"),this.property.value){let e=this.property.value.description;"string"===this.property.value.type&&"string"==typeof e&&(e=`"${e}"`),this.editableDiv.setTextContentTruncatedIfNeeded(e,k(I.stringIsTooLargeToEdit))}const t=this.editableDiv.textContent||"";this.setExpandable(!1),this.listItemElement.classList.add("editing-sub-part"),this.valueElement.classList.add("hidden"),this.prompt=new W;const n=this.prompt.attachAndStartEditing(this.editableDiv,this.editingCommitted.bind(this,t));n.classList.add("property-prompt");const i=this.listItemElement.getComponentSelection();i&&i.selectAllChildren(this.editableDiv),n.addEventListener("keydown",this.promptKeyDown.bind(this,t),!1)}editingEnded(){this.prompt&&(this.prompt.detach(),delete this.prompt),this.editableDiv.remove(),this.updateExpandable(),this.listItemElement.scrollLeft=0,this.listItemElement.classList.remove("editing-sub-part"),this.select()}editingCancelled(){this.valueElement.classList.remove("hidden"),this.editingEnded()}async editingCommitted(e){const t=this.prompt?this.prompt.text():"";t!==e?(this.editingEnded(),await this.applyExpression(t)):this.editingCancelled()}promptKeyDown(e,t){const n=t;return"Enter"===n.key?(n.consume(),void this.editingCommitted(e)):n.key===s.KeyboardUtilities.ESCAPE_KEY?(n.consume(),void this.editingCancelled()):void 0}async applyExpression(e){const t=a.RemoteObject.RemoteObject.toCallArgument(this.property.symbol||this.property.name);if(e=j.wrapObjectLiteral(e.trim()),this.property.synthetic){let t=!1;if(e&&(t=await this.property.setSyntheticValue(e)),t){const e=this.parent;e&&(e.invalidateChildren(),e.onpopulate())}else this.update();return}const n=L.get(this.property),i=e?n.setPropertyValue(t,e):n.deleteProperty(t);if(await i)this.update();else if(e){const e=this.parent;e&&(e.invalidateChildren(),e.onpopulate())}else this.parent&&this.parent.removeChild(this)}onInvokeGetterClick(e){e.object&&(this.property.value=e.object,this.property.wasThrown=e.wasThrown||!1,this.update(),this.invalidateChildren(),this.updateExpandable())}updateExpandable(){this.property.value?this.setExpandable(!this.property.value.customPreview()&&this.property.value.hasChildren&&!this.property.wasThrown):this.setExpandable(!1)}path(){return this.nameElement.title}}class _ extends i.TreeOutline.TreeElement{toggleOnClick;fromIndex;toIndex;object;readOnly;propertyCount;linkifier;constructor(e,t,n,i,o){super(s.StringUtilities.sprintf("[%d … %d]",t,n),!0),this.toggleOnClick=!0,this.fromIndex=t,this.toIndex=n,this.object=e,this.readOnly=!0,this.propertyCount=i,this.linkifier=o}static async populateArray(e,t,n,i,o){await _.populateRanges(e,t,n,i,!0,o)}static async populateRanges(e,t,n,i,o,s){const r=await t.callFunctionJSON((function(e,t,n,i){if(void 0===e||void 0===t||void 0===i||void 0===n)return;let o=null;const s=t-e>=i&&ArrayBuffer.isView(this);function*r(n){if(void 0!==e&&void 0!==t&&void 0!==i)if(t-e<i)for(let i=e;i<=t;++i)i in n&&(yield i);else{o=o||Object.getOwnPropertyNames(n);for(let n=0;n<o.length;++n){const i=o[n],s=Number(i)>>>0;String(s)===i&&e<=s&&s<=t&&(yield s)}}}let a=0;if(s)a=t-e+1;else for(const e of r(this))++a;let l=a;l=a<=n?a:Math.pow(n,Math.ceil(Math.log(a)/Math.log(n))-1);const c=[];if(s)for(let n=e;n<=t;n+=l){const e=n;let i=e+l-1;i>t&&(i=t),c.push([e,i,i-e+1])}else{a=0;let e=-1,t=0;for(const n of r(this))-1===e&&(e=n),t=n,++a===l&&(c.push([e,t,a]),a=0,e=-1);a>0&&c.push([e,t,a])}return{ranges:c}}),[{value:n},{value:i},{value:_.bucketThreshold},{value:_.sparseIterationThreshold}]);await async function(n){if(!n)return;const i=n.ranges;if(1===i.length)await _.populateAsFragment(e,t,i[0][0],i[0][1],s);else for(let n=0;n<i.length;++n){const o=i[n][0],r=i[n][1],a=i[n][2];o===r?await _.populateAsFragment(e,t,o,r,s):e.appendChild(new _(t,o,r,a,s))}o&&await _.populateNonIndexProperties(e,t,s)}(r)}static async populateAsFragment(e,t,n,i,o){const s=await t.callFunction((function(e,t,n){const i=Object.create(null);if(void 0===e||void 0===t||void 0===n)return;if(t-e<n)for(let n=e;n<=t;++n)n in this&&(i[n]=this[n]);else{const n=Object.getOwnPropertyNames(this);for(let o=0;o<n.length;++o){const s=n[o],r=Number(s)>>>0;String(r)===s&&e<=r&&r<=t&&(i[r]=this[r])}}return i}),[{value:n},{value:i},{value:_.sparseIterationThreshold}]);if(!s.object||s.wasThrown)return;const r=s.object,a=await r.getAllProperties(!1,!0);r.release();const l=a.properties;if(l){l.sort(F.compareProperties);for(let t=0;t<l.length;++t){L.set(l[t],this.object);const n=new D(l[t],o);n.readOnly=!0,e.appendChild(n)}}}static async populateNonIndexProperties(e,t,n){const{properties:i,internalProperties:o}=await a.RemoteObject.RemoteObject.loadFromObjectPerProto(t,!0,!0);i&&D.populateWithProperties(e,i,o,!1,!1,t,n)}async onpopulate(){this.propertyCount>=_.bucketThreshold?await _.populateRanges(this,this.object,this.fromIndex,this.toIndex,!1,this.linkifier):await _.populateAsFragment(this,this.object,this.fromIndex,this.toIndex,this.linkifier)}onattach(){this.listItemElement.classList.add("object-properties-section-name")}static bucketThreshold=100;static sparseIterationThreshold=25e4}class W extends i.TextPrompt.TextPrompt{constructor(){super(),this.initialize(p.JavaScript.completeInContext)}}class V{static#e=new WeakMap;static#t=new WeakMap;#n=new Set;constructor(e){e.addEventListener(i.TreeOutline.Events.ElementAttached,this.#i,this),e.addEventListener(i.TreeOutline.Events.ElementExpanded,this.#o,this),e.addEventListener(i.TreeOutline.Events.ElementCollapsed,this.#s,this)}watchSection(e,t){V.#t.set(t,e),this.#n.has(e)&&t.expand()}stopWatchSectionsWithId(e){for(const t of this.#n)t.startsWith(e+":")&&this.#n.delete(t)}#i(e){const t=e.data;t.isExpandable()&&this.#n.has(this.#r(t))&&t.expand()}#o(e){const t=e.data;this.#n.add(this.#r(t))}#s(e){const t=e.data;this.#n.delete(this.#r(t))}#r(e){const t=V.#e.get(e);if(t)return t;let n=e,i=n;if(!e.treeOutline)throw new Error("No tree outline available");const o=e.treeOutline.rootElement();let s;for(;n!==o;){let e="";e=n.property?n.property.name:"string"==typeof n.title?n.title:n.title.textContent||"",s=e+(s?"."+s:""),i=n,n.parent&&(n=n.parent)}return s=V.#t.get(i)+(s?":"+s:""),V.#e.set(e,s),s}}let $;class B{static instance(e={forceNew:!1}){const{forceNew:t}=e;return $&&!t||($=new B),$}async render(e,t){if(!(e instanceof a.RemoteObject.RemoteObject))throw new Error("Can't render "+e);const n=(t=t||{title:void 0,editable:void 0}).title,i=new F(e,n);return n||i.titleLessMode(),i.editable=Boolean(t.editable),{node:i.element,tree:i}}}class H{element;constructor(e){this.element=e}appendApplicableItems(e,t,n){}}class J extends H{text;maxLength;expandElement;maxDisplayableTextLength;expandElementText;copyButtonText;constructor(e,t,n){super(e);const o=e.createChild("span");this.text=t,this.maxLength=n,o.textContent=t.slice(0,n),i.Tooltip.Tooltip.install(o,`${t.slice(0,n)}…`),this.expandElement=o.createChild("button"),this.maxDisplayableTextLength=1e7;const r=s.StringUtilities.countWtf8Bytes(t),a=s.NumberUtilities.bytesToString(r);this.text.length<this.maxDisplayableTextLength?(this.expandElementText=k(I.showMoreS,{PH1:a}),this.expandElement.setAttribute("data-text",this.expandElementText),this.expandElement.setAttribute("jslog",`${d.action("expand").track({click:!0})}`),this.expandElement.classList.add("expandable-inline-button"),this.expandElement.addEventListener("click",this.expandText.bind(this))):(this.expandElement.setAttribute("data-text",k(I.longTextWasTruncatedS,{PH1:a})),this.expandElement.classList.add("undisplayable-text")),this.copyButtonText=k(I.copy);const l=o.createChild("button","expandable-inline-button");l.setAttribute("data-text",this.copyButtonText),l.setAttribute("jslog",`${d.action("copy").track({click:!0})}`),l.addEventListener("click",this.copyText.bind(this))}appendApplicableItems(e,t,n){this.text.length<this.maxDisplayableTextLength&&this.expandElement&&t.clipboardSection().appendItem(this.expandElementText||"",this.expandText.bind(this),{jslogContext:"show-more"}),t.clipboardSection().appendItem(this.copyButtonText,this.copyText.bind(this),{jslogContext:"copy"})}expandText(){this.expandElement&&(this.expandElement.parentElement&&this.expandElement.parentElement.insertBefore(document.createTextNode(this.text.slice(this.maxLength)),this.expandElement),this.expandElement.remove(),this.expandElement=null)}copyText(){o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(this.text)}}var G=Object.freeze({__proto__:null,getObjectPropertiesSectionFrom:e=>M.get(e),ObjectPropertiesSection:F,setMaxRenderableStringLength:function(e){U=e},getMaxRenderableStringLength:function(){return U},ObjectPropertiesSectionsTreeOutline:A,RootElement:R,InitialVisibleChildrenLimit:200,ObjectPropertyTreeElement:D,ArrayGroupingTreeElement:_,ObjectPropertyPrompt:W,ObjectPropertiesSectionsTreeExpandController:V,Renderer:B,ObjectPropertyValue:H,ExpandableTextPropertyValue:J});const z={showAsJavascriptObject:"Show as JavaScript object"},K=t.i18n.registerUIStrings("ui/legacy/components/object_ui/CustomPreviewComponent.ts",z),X=t.i18n.getLocalizedString.bind(void 0,K);class Q{sectionElement;object;expanded;cachedContent;header;expandIcon;constructor(t){this.sectionElement=document.createElement("span"),this.sectionElement.classList.add("custom-expandable-section"),this.object=t,this.expanded=!1,this.cachedContent=null;const i=t.customPreview();if(!i)return;let o;try{o=JSON.parse(i.header)}catch(t){return void e.Console.Console.instance().error("Broken formatter: header is invalid json "+t)}this.header=this.renderJSONMLTag(o),this.header.nodeType!==Node.TEXT_NODE?(i.bodyGetterId&&(this.header instanceof Element&&this.header.classList.add("custom-expandable-section-header"),this.header.addEventListener("click",this.onClick.bind(this),!1),this.expandIcon=n.Icon.create("triangle-right","custom-expand-icon"),this.header.insertBefore(this.expandIcon,this.header.firstChild)),this.sectionElement.appendChild(this.header)):e.Console.Console.instance().error("Broken formatter: header should be an element node.")}element(){return this.sectionElement}renderJSONMLTag(e){return Array.isArray(e)?"object"===e[0]?this.layoutObjectTag(e):this.renderElement(e):document.createTextNode(String(e))}renderElement(t){const n=t.shift();if(!Y.includes(n))return e.Console.Console.instance().error("Broken formatter: element "+n+" is not allowed!"),document.createElement("span");const i=document.createElement(n);if("object"==typeof t[0]&&!Array.isArray(t[0])){const e=t.shift();for(const t in e){const n=e[t];"style"===t&&"string"==typeof n&&i.setAttribute(t,n)}}return this.appendJsonMLTags(i,t),i}layoutObjectTag(e){e.shift();const t=e.shift(),n=this.object.runtimeModel().createRemoteObject(t);if(n.customPreview())return new Q(n).element();const i=F.defaultObjectPresentation(n);return i.classList.toggle("custom-expandable-section-standard-section",n.hasChildren),i}appendJsonMLTags(e,t){for(let n=0;n<t.length;++n)e.appendChild(this.renderJSONMLTag(t[n]))}onClick(e){e.consume(!0),this.cachedContent?this.toggleExpand():this.loadBody()}toggleExpand(){this.expanded=!this.expanded,this.header instanceof Element&&this.header.classList.toggle("expanded",this.expanded),this.cachedContent instanceof Element&&this.cachedContent.classList.toggle("hidden",!this.expanded),this.expandIcon&&(this.expanded?this.expandIcon.name="triangle-down":this.expandIcon.name="triangle-right")}defaultBodyTreeOutline;async loadBody(){const e=this.object.customPreview();if(e&&e.bodyGetterId){const t=await this.object.callFunctionJSON((e=>e()),[{objectId:e.bodyGetterId}]);null===t?(this.defaultBodyTreeOutline=new A({readOnly:!0}),this.defaultBodyTreeOutline.setShowSelectionOnKeyboardFocus(!0,!1),this.defaultBodyTreeOutline.element.classList.add("custom-expandable-section-default-body"),D.populate(this.defaultBodyTreeOutline.rootElement(),this.object,!1,!1),this.cachedContent=this.defaultBodyTreeOutline.element):this.cachedContent=this.renderJSONMLTag(t),this.sectionElement.appendChild(this.cachedContent),this.toggleExpand()}}}const Y=["span","div","ol","li","table","tr","td"];class q{object;customPreviewSection;element;constructor(e){this.object=e,this.customPreviewSection=new Q(e),this.element=document.createElement("span"),this.element.classList.add("source-code");const t=i.UIUtils.createShadowRootWithCoreStyles(this.element,{cssFile:[b],delegatesFocus:void 0});this.element.addEventListener("contextmenu",this.contextMenuEventFired.bind(this),!1),t.appendChild(this.customPreviewSection.element())}expandIfPossible(){const e=this.object.customPreview();e&&e.bodyGetterId&&this.customPreviewSection&&this.customPreviewSection.loadBody()}contextMenuEventFired(e){const t=new i.ContextMenu.ContextMenu(e);this.customPreviewSection&&t.revealSection().appendItem(X(z.showAsJavascriptObject),this.disassemble.bind(this),{jslogContext:"show-as-javascript-object"}),t.appendApplicableItems(this.object),t.show()}disassemble(){this.element.shadowRoot&&(this.element.shadowRoot.textContent="",this.customPreviewSection=null,this.element.shadowRoot.appendChild(F.defaultObjectPresentation(this.object)))}}var Z=Object.freeze({__proto__:null,CustomPreviewSection:Q,CustomPreviewComponent:q});const ee=new CSSStyleSheet;ee.replaceSync(".object-popover-content{display:block;position:relative;overflow:hidden;flex:1 1 auto}.object-popover-title{text-overflow:ellipsis;overflow:hidden;white-space:nowrap;font-weight:bold;padding-left:18px;padding-bottom:2px}.object-popover-tree{border-top:1px solid var(--sys-color-divider);overflow:auto;width:100%;height:calc(100% - 13px)}.object-popover-container{display:inline-block}.object-popover-description-box{padding:6px;max-width:350px;line-height:1.4}.object-popover-footer{margin-top:8px}\n/*# sourceURL=objectPopover.css */\n");const te={learnMore:"Learn more"},ne=t.i18n.registerUIStrings("ui/legacy/components/object_ui/ObjectPopoverHelper.ts",te),ie=t.i18n.getLocalizedString.bind(void 0,ne);class oe{linkifier;resultHighlightedAsDOM;constructor(e,t){this.linkifier=e,this.resultHighlightedAsDOM=t}dispose(){this.resultHighlightedAsDOM&&a.OverlayModel.OverlayModel.hideDOMNodeHighlight(),this.linkifier&&this.linkifier.dispose()}static async buildObjectPopover(e,t){const n=s.StringUtilities.trimEndWithMaxLength(e.description||"",se);let o=null;if("function"===e.type||"object"===e.type){let s=null,r=!1;if("node"===e.subtype&&(a.OverlayModel.OverlayModel.highlightObjectAsDOMNode(e),r=!0),e.customPreview()){const t=new q(e);t.expandIfPossible(),o=t.element}else{o=document.createElement("div"),o.classList.add("object-popover-content"),t.registerCSSFiles([O,ee]);const i=o.createChild("div","object-popover-title");"function"===e.type?(i.classList.add("source-code"),i.appendChild(F.valueElementForFunctionDescription(e.description))):(i.classList.add("monospace"),i.createChild("span").textContent=n),s=new m.Linkifier.Linkifier;const r=new F(e,"",s,!0);r.element.classList.add("object-popover-tree"),r.titleLessMode(),o.appendChild(r.element)}return o.dataset.stableNameForTest="object-popover-content",t.setMaxContentSize(new i.Geometry.Size(300,250)),t.setSizeBehavior("SetExactSize"),t.contentElement.appendChild(o),new oe(s,r)}o=document.createElement("span"),o.dataset.stableNameForTest="object-popover-content",t.registerCSSFiles([O,ee]);const r=o.createChild("span","monospace object-value-"+e.type);return r.style.whiteSpace="pre","string"===e.type?i.UIUtils.createTextChildren(r,`"${n}"`):r.textContent=n,t.contentElement.appendChild(o),new oe(null,!1)}static buildDescriptionPopover(e,t,n){const o=document.createElement("div");o.classList.add("object-popover-description-box");const s=document.createElement("div");s.dataset.stableNameForTest="object-popover-content",n.registerCSSFiles([ee]),s.textContent=e;const r=i.XLink.XLink.create(t,ie(te.learnMore),void 0,void 0,"learn-more"),a=document.createElement("div");return a.classList.add("object-popover-footer"),a.appendChild(r),o.appendChild(s),o.appendChild(a),n.contentElement.appendChild(o),new oe(null,!1)}}const se=1e4;var re=Object.freeze({__proto__:null,ObjectPopoverHelper:oe});export{Z as CustomPreviewComponent,P as JavaScriptREPL,re as ObjectPopoverHelper,G as ObjectPropertiesSection,C as RemoteObjectPreviewFormatter};
