{"version": 3, "file": "meta.js", "sourceRoot": "", "sources": ["../../../../src/device/configs/headset/meta.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EACN,sBAAsB,EACtB,iBAAiB,GACjB,MAAM,+BAA+B,CAAC;AACvC,OAAO,EACN,kBAAkB,EAClB,iBAAiB,EACjB,aAAa,EACb,aAAa,GACb,MAAM,uBAAuB,CAAC;AAI/B,MAAM,CAAC,MAAM,YAAY,GAAmB;IAC3C,IAAI,EAAE,gBAAgB;IACtB,gBAAgB,EAAE,aAAa;IAC/B,qBAAqB,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,cAAc,CAAC;IACjE,iBAAiB,EAAE;QAClB,QAAQ;QACR,OAAO;QACP,aAAa;QACb,eAAe;QACf,WAAW;QACX,SAAS;QACT,iBAAiB;QACjB,eAAe;KACf;IACD,mBAAmB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACjC,yBAAyB,EAAE,IAAI;IAC/B,wBAAwB,EAAE,EAAE;IAC5B,qBAAqB,EAAE;QACtB,CAAC,cAAc,CAAC,EAAE,sBAAsB,CAAC,MAAM;QAC/C,CAAC,cAAc,CAAC,EAAE,sBAAsB,CAAC,UAAU;KACnD;IACD,eAAe,EAAE,iBAAiB,CAAC,UAAU;IAC7C,SAAS,EACR,mJAAmJ;CACpJ,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAAmB;IACzC,IAAI,EAAE,cAAc;IACpB,gBAAgB,EAAE,aAAa;IAC/B,qBAAqB,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,cAAc,CAAC;IACjE,iBAAiB,EAAE;QAClB,QAAQ;QACR,OAAO;QACP,aAAa;QACb,eAAe;QACf,WAAW;QACX,SAAS;QACT,iBAAiB;QACjB,gBAAgB;QAChB,UAAU;QACV,eAAe;KACf;IACD,mBAAmB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IACtC,yBAAyB,EAAE,IAAI;IAC/B,wBAAwB,EAAE,EAAE;IAC5B,qBAAqB,EAAE;QACtB,CAAC,cAAc,CAAC,EAAE,sBAAsB,CAAC,MAAM;QAC/C,CAAC,cAAc,CAAC,EAAE,sBAAsB,CAAC,UAAU;KACnD;IACD,eAAe,EAAE,iBAAiB,CAAC,UAAU;IAC7C,SAAS,EACR,mJAAmJ;CACpJ,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAmB;IAC3C,IAAI,EAAE,gBAAgB;IACtB,gBAAgB,EAAE,iBAAiB;IACnC,qBAAqB,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,cAAc,CAAC;IACjE,iBAAiB,EAAE;QAClB,QAAQ;QACR,OAAO;QACP,aAAa;QACb,eAAe;QACf,WAAW;QACX,SAAS;QACT,iBAAiB;QACjB,gBAAgB;QAChB,UAAU;QACV,eAAe;KACf;IACD,mBAAmB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IACtC,yBAAyB,EAAE,IAAI;IAC/B,wBAAwB,EAAE,EAAE;IAC5B,qBAAqB,EAAE;QACtB,CAAC,cAAc,CAAC,EAAE,sBAAsB,CAAC,MAAM;QAC/C,CAAC,cAAc,CAAC,EAAE,sBAAsB,CAAC,UAAU;KACnD;IACD,eAAe,EAAE,iBAAiB,CAAC,UAAU;IAC7C,SAAS,EACR,qJAAqJ;CACtJ,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAAmB;IACzC,IAAI,EAAE,cAAc;IACpB,gBAAgB,EAAE,kBAAkB;IACpC,qBAAqB,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,cAAc,CAAC;IACjE,iBAAiB,EAAE;QAClB,QAAQ;QACR,OAAO;QACP,aAAa;QACb,eAAe;QACf,WAAW;QACX,SAAS;QACT,iBAAiB;QACjB,gBAAgB;QAChB,UAAU;QACV,eAAe;QACf,eAAe;KACf;IACD,mBAAmB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IACtC,yBAAyB,EAAE,IAAI;IAC/B,wBAAwB,EAAE,EAAE;IAC5B,qBAAqB,EAAE;QACtB,CAAC,cAAc,CAAC,EAAE,sBAAsB,CAAC,MAAM;QAC/C,CAAC,cAAc,CAAC,EAAE,sBAAsB,CAAC,UAAU;KACnD;IACD,eAAe,EAAE,iBAAiB,CAAC,UAAU;IAC7C,SAAS,EACR,mJAAmJ;CACpJ,CAAC"}