{"version": 3, "file": "web3_subscription_manager.js", "sourceRoot": "", "sources": ["../../src/web3_subscription_manager.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;;;;;;;;;;AAEF,OAAO,EAEN,qBAAqB,GAQrB,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,aAAa,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAC/D,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AACvC,OAAO,EAAE,sBAAsB,EAAE,MAAM,YAAY,CAAC;AACpD,OAAO,EAAsB,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AAYxF,MAAM,OAAO,uBAAuB;IAkCnC,YACiB,cAAuC,EACvC,uBAAuC,EACtC,+BAAwC,KAAK;QAF9C,mBAAc,GAAd,cAAc,CAAyB;QACvC,4BAAuB,GAAvB,uBAAuB,CAAgB;QACtC,iCAA4B,GAA5B,4BAA4B,CAAiB;QA/B9C,mBAAc,GAG3B,IAAI,GAAG,EAAE,CAAC;QA8Bb,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,uBAAuB,CAAC,sBAAsB,EAAE,GAAS,EAAE;YACjF,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAC1B,CAAC,CAAA,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,GAAG,EAAE;YACrE,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAC/B,CAAC;IAEO,sBAAsB;QAC7B,MAAM,qBAAqB,GAAG,IAAI,CAAC,cAAc,CAAC,QAA4B,CAAC;QAC/E,IACC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ;YAC7B,CAAC,OAAO,CAAA,qBAAqB,aAArB,qBAAqB,uBAArB,qBAAqB,CAAE,qBAAqB,CAAA,KAAK,UAAU;gBAClE,CAAC,CAAA,qBAAqB,aAArB,qBAAqB,uBAArB,qBAAqB,CAAE,qBAAqB,EAAE,CAAA,CAAC,EAChD,CAAC;YACF,OAAO;QACR,CAAC;QAED,IAAI,OAAQ,IAAI,CAAC,cAAc,CAAC,QAAiC,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC;YACrF,IACC,OAAQ,IAAI,CAAC,cAAc,CAAC,QAAiC,CAAC,OAAO,KAAK,UAAU,EACnF,CAAC;gBACF,uCAAuC;gBACtC,IAAI,CAAC,cAAc,CAAC,QAAiC,CAAC,EAAE,CACxD,SAAS;gBACT,qGAAqG;gBACrG,CAAC,OAAY,EAAE,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAC/C,CAAC;YACH,CAAC;iBAAM,CAAC;gBACP,qGAAqG;gBACrG,qBAAqB,CAAC,EAAE,CAAM,MAAM,EAAE,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;YAClF,CAAC;QACF,CAAC;IACF,CAAC;IAES,eAAe,CACxB,IAG2B;;QAE3B,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,MAAM,IAAI,iBAAiB,CAAC,wDAAwD,CAAC,CAAC;QACvF,CAAC;QACD,MAAM,cAAc,GACnB,CAAA,MAAC,IAA4B,CAAC,MAAM,0CAAE,YAAY;aAClD,MAAC,IAAqC,CAAC,IAAI,0CAAE,YAAY,CAAA;aACzD,MAAC,IAAkC,CAAC,EAAE,0CAAE,QAAQ,CAAC,EAAE,CAAC,CAAA,CAAC;QAEtD,4DAA4D;QAC5D,IAAI,cAAc,EAAE,CAAC;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YACpD,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;IACF,CAAC;IACD;;;;;;;;;OASG;IACU,SAAS;6DACrB,IAAO,EACP,IAAkD,EAClD,eAA2B,qBAAqB;YAEhD,MAAM,KAAK,GAAsB,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YACpE,IAAI,CAAC,KAAK,EAAE,CAAC;gBACZ,MAAM,IAAI,iBAAiB,CAAC,2BAA2B,CAAC,CAAC;YAC1D,CAAC;YAED,iEAAiE;YACjE,MAAM,YAAY,GAAG,IAAI,KAAK,CAAC,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,SAAS,EAAE;gBACjD,mBAAmB,EAAE,IAAoD;gBACzE,YAAY;gBACZ,4DAA4D;aACrD,CAAoC,CAAC;YAE7C,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAEzC,OAAO,YAAY,CAAC;QACrB,CAAC;KAAA;IAED;;OAEG;IACH,IAAW,aAAa;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC5B,CAAC;IAED;;;;;OAKG;IACU,eAAe,CAAC,GAAuD;;YACnF,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;gBACnC,MAAM,IAAI,aAAa,CAAC,wBAAwB,CAAC,CAAC;YACnD,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;gBACnC,MAAM,IAAI,iBAAiB,CAAC,qDAAqD,CAAC,CAAC;YACpF,CAAC;YAED,IAAI,GAAG,CAAC,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;gBAC/C,MAAM,IAAI,iBAAiB,CAAC,yBAAyB,GAAG,CAAC,EAAE,kBAAkB,CAAC,CAAC;YAChF,CAAC;YAED,MAAM,GAAG,CAAC,uBAAuB,EAAE,CAAC;YAEpC,IAAI,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;gBACvB,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,CAAC,CAAC;YACpE,CAAC;YAED,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;YAErC,OAAO,GAAG,CAAC,EAAE,CAAC;QACf,CAAC;KAAA;IAED;;;;OAIG;IACU,kBAAkB,CAAC,GAAuD;;YACtF,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC;YAEnB,IAAI,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC;gBACnB,MAAM,IAAI,iBAAiB,CAC1B,iHAAiH,CACjH,CAAC;YACH,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE,CAAC;gBACxE,MAAM,IAAI,iBAAiB,CAAC,yBAAyB,EAAE,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAC;YACxF,CAAC;YAED,MAAM,GAAG,CAAC,sBAAsB,EAAE,CAAC;YACnC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC/B,OAAO,EAAE,CAAC;QACX,CAAC;KAAA;IACD;;;;;OAKG;IACU,WAAW,CAAC,SAAsC;;YAC9D,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,KAAK,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;gBACtD,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,SAAS,KAAK,UAAU,IAAI,SAAS,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC;oBAC/E,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC3C,CAAC;YACF,CAAC;YAED,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5B,CAAC;KAAA;IAED;;OAEG;IACI,KAAK;QACX,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACI,qBAAqB;QAC3B,OAAO,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;YAC7C,CAAC,CAAC,KAAK;YACP,CAAC,CAAC,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IACzD,CAAC;CACD"}