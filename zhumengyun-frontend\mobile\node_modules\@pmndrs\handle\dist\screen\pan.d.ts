import { OrthographicCamera, PerspectiveCamera, Scene, Vector3Tuple } from 'three';
import { StoreApi } from 'zustand/vanilla';
import { defaultScreenCameraApply, ScreenCameraStateAndFunctions } from './camera.js';
import { ScreenHandleStore } from './store.js';
export declare class PanScreenHandleStore extends ScreenHandleStore<Readonly<Vector3Tuple>> {
    private readonly canvas;
    filter: (map: ScreenHandleStore['map']) => boolean;
    customApply?: typeof defaultScreenCameraApply | undefined;
    speed?: number | undefined;
    space?: "screen" | "xz" | undefined;
    constructor(canvas: HTMLCanvasElement, store: StoreApi<ScreenCameraStateAndFunctions>, getCamera: () => PerspectiveCamera | OrthographicCamera, filter: (map: ScreenHandleStore['map']) => boolean, customApply?: typeof defaultScreenCameraApply | undefined, speed?: number | undefined, space?: "screen" | "xz" | undefined);
    bind(scene: Scene): () => void;
}
