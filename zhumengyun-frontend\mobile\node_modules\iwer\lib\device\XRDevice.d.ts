/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { GlobalSpace, XRSpace } from '../spaces/XRSpace.js';
import { P_DEVICE } from '../private.js';
import { Quaternion, Vector3 } from '../utils/Math.js';
import { XRController, XRControllerConfig } from './XRController.js';
import { XREnvironmentBlendMode, XRInteractionMode, XRSession, XRSessionMode, XRVisibilityState } from '../session/XRSession.js';
import { XREye, XRView } from '../views/XRView.js';
import { XRHandInput } from './XRHandInput.js';
import { XRHandedness, XRInputSource } from '../input/XRInputSource.js';
import { XRWebGLLayer } from '../layers/XRWebGLLayer.js';
import { XRReferenceSpace } from '../spaces/XRReferenceSpace.js';
import { mat4 } from 'gl-matrix';
import { ActionPlayer } from '../action/ActionPlayer.js';
import { InputSchema } from '../action/ActionRecorder.js';
import { XRFrame } from '../frameloop/XRFrame.js';
import { XRSystem } from '../initialization/XRSystem.js';
import { XRTrackedInput } from './XRTrackedInput.js';
import { XRViewport } from '../views/XRViewport.js';
import { NativePlane } from '../planes/XRPlane.js';
import { NativeMesh } from '../meshes/XRMesh.js';
export type WebXRFeature = 'viewer' | 'local' | 'local-floor' | 'bounded-floor' | 'unbounded' | 'dom-overlay' | 'anchors' | 'plane-detection' | 'mesh-detection' | 'hit-test' | 'hand-tracking' | 'depth-sensing';
export interface XRDeviceConfig {
    name: string;
    controllerConfig: XRControllerConfig | undefined;
    supportedSessionModes: XRSessionMode[];
    supportedFeatures: WebXRFeature[];
    supportedFrameRates: number[];
    isSystemKeyboardSupported: boolean;
    internalNominalFrameRate: number;
    environmentBlendModes: Partial<{
        [sessionMode in XRSessionMode]: XREnvironmentBlendMode;
    }>;
    interactionMode: XRInteractionMode;
    userAgent: string;
}
export interface XRDeviceOptions {
    ipd: number;
    fovy: number;
    stereoEnabled: boolean;
    headsetPosition: Vector3;
    headsetQuaternion: Quaternion;
    canvasContainer: HTMLDivElement;
}
export interface DevUIConstructor {
    new (xrDevice: XRDevice): DevUI;
}
export interface DevUI {
    version: string;
    render(time: number): void;
    get devUICanvas(): HTMLCanvasElement;
    get devUIContainer(): HTMLDivElement;
}
export interface SEMConstructor {
    new (xrDevice: XRDevice): SyntheticEnvironmentModule;
}
export interface SyntheticEnvironmentModule {
    version: string;
    render(time: number): void;
    loadEnvironment(json: any): void;
    loadDefaultEnvironment(envId: string): void;
    planesVisible: boolean;
    boundingBoxesVisible: boolean;
    meshesVisible: boolean;
    get environmentCanvas(): HTMLCanvasElement;
    get trackedPlanes(): Set<NativePlane>;
    get trackedMeshes(): Set<NativeMesh>;
    computeHitTestResults(rayMatrix: mat4): mat4[];
}
/**
 * XRDevice is not a standard API class outlined in the WebXR Device API Specifications
 * Instead, it serves as an user-facing interface to control the emulated XR Device
 */
export declare class XRDevice {
    readonly version = "2.0.1";
    [P_DEVICE]: {
        name: string;
        supportedSessionModes: string[];
        supportedFeatures: string[];
        supportedFrameRates: number[];
        isSystemKeyboardSupported: boolean;
        internalNominalFrameRate: number;
        environmentBlendModes: Partial<{
            [sessionMode in XRSessionMode]: XREnvironmentBlendMode;
        }>;
        interactionMode: XRInteractionMode;
        userAgent: string;
        position: Vector3;
        quaternion: Quaternion;
        stereoEnabled: boolean;
        ipd: number;
        fovy: number;
        controllers: {
            [key in XRHandedness]?: XRController;
        };
        hands: {
            [key in XRHandedness]?: XRHandInput;
        };
        primaryInputMode: 'controller' | 'hand';
        pendingReferenceSpaceReset: boolean;
        visibilityState: XRVisibilityState;
        pendingVisibilityState: XRVisibilityState | null;
        xrSystem: XRSystem | null;
        matrix: mat4;
        globalSpace: GlobalSpace;
        viewerSpace: XRReferenceSpace;
        viewSpaces: {
            [key in XREye]: XRSpace;
        };
        canvasData?: {
            canvas: HTMLCanvasElement;
            parent: HTMLElement | null;
            width: number;
            height: number;
            zIndex: string;
        };
        canvasContainer: HTMLDivElement;
        getViewport: (layer: XRWebGLLayer, view: XRView) => XRViewport;
        updateViews: () => void;
        onBaseLayerSet: (baseLayer: XRWebGLLayer | null) => void;
        onSessionEnd: () => void;
        onFrameStart: (frame: XRFrame) => void;
        actionPlayer?: ActionPlayer;
        devui?: DevUI;
        sem?: SyntheticEnvironmentModule;
    };
    constructor(deviceConfig: XRDeviceConfig, deviceOptions?: Partial<XRDeviceOptions>);
    installRuntime(globalObject?: any): void;
    installDevUI(devUIConstructor: DevUIConstructor): void;
    installSEM(semConstructor: SEMConstructor): void;
    get supportedSessionModes(): string[];
    get supportedFeatures(): string[];
    get supportedFrameRates(): number[];
    get isSystemKeyboardSupported(): boolean;
    get internalNominalFrameRate(): number;
    get stereoEnabled(): boolean;
    set stereoEnabled(value: boolean);
    get ipd(): number;
    set ipd(value: number);
    get fovy(): number;
    set fovy(value: number);
    get position(): Vector3;
    get quaternion(): Quaternion;
    get viewerSpace(): XRReferenceSpace;
    get viewSpaces(): {
        none: XRSpace;
        left: XRSpace;
        right: XRSpace;
    };
    get controllers(): {
        none?: XRController | undefined;
        left?: XRController | undefined;
        right?: XRController | undefined;
    };
    get hands(): {
        none?: XRHandInput | undefined;
        left?: XRHandInput | undefined;
        right?: XRHandInput | undefined;
    };
    get primaryInputMode(): 'controller' | 'hand';
    set primaryInputMode(mode: 'controller' | 'hand');
    get activeInputs(): XRTrackedInput[];
    get inputSources(): XRInputSource[];
    get canvasContainer(): HTMLDivElement;
    get canvasDimensions(): {
        width: number;
        height: number;
    } | undefined;
    get activeSession(): XRSession | undefined;
    get sessionOffered(): boolean;
    get name(): string;
    grantOfferedSession(): void;
    recenter(): void;
    get visibilityState(): XRVisibilityState;
    updateVisibilityState(state: XRVisibilityState): void;
    createActionPlayer(refSpace: XRReferenceSpace, recording: {
        schema: {
            0: number;
            1: InputSchema;
        }[];
        frames: any[];
    }): ActionPlayer;
    get devui(): DevUI | undefined;
    get sem(): SyntheticEnvironmentModule | undefined;
}
//# sourceMappingURL=XRDevice.d.ts.map