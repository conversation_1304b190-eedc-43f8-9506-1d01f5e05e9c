{"version": 3, "file": "GLTFLoader.js", "sources": ["../../src/loaders/GLTFLoader.js"], "sourcesContent": ["import {\n  AnimationClip,\n  Bone,\n  Box3,\n  BufferAttribute,\n  BufferGeometry,\n  ClampToEdgeWrapping,\n  Color,\n  DirectionalLight,\n  DoubleSide,\n  FileLoader,\n  FrontSide,\n  Group,\n  ImageBitmapLoader,\n  InstancedMesh,\n  InterleavedBuffer,\n  InterleavedBufferAttribute,\n  Interpolant,\n  InterpolateDiscrete,\n  InterpolateLinear,\n  Line,\n  LineBasicMaterial,\n  LineLoop,\n  LineSegments,\n  LinearFilter,\n  LinearMipmapLinearFilter,\n  LinearMipmapNearestFilter,\n  Loader,\n  LoaderUtils,\n  Material,\n  MathUtils,\n  Matrix4,\n  Mesh,\n  MeshBasicMaterial,\n  MeshPhysicalMaterial,\n  MeshStandardMaterial,\n  MirroredRepeatWrapping,\n  NearestFilter,\n  NearestMipmapLinearFilter,\n  NearestMipmapNearestFilter,\n  NumberKeyframeTrack,\n  Object3D,\n  OrthographicCamera,\n  PerspectiveCamera,\n  PointLight,\n  Points,\n  PointsMaterial,\n  PropertyBinding,\n  Quaternion,\n  QuaternionKeyframeTrack,\n  RepeatWrapping,\n  Skeleton,\n  SkinnedMesh,\n  Sphere,\n  SpotLight,\n  Texture,\n  TextureLoader,\n  TriangleFanDrawMode,\n  TriangleStripDrawMode,\n  Vector2,\n  Vector3,\n  VectorKeyframeTrack,\n  InstancedBufferAttribute,\n} from 'three'\nimport { toTrianglesDrawMode } from '../utils/BufferGeometryUtils'\nimport { version } from '../_polyfill/constants'\nimport { decodeText } from '../_polyfill/LoaderUtils'\n\nconst SRGBColorSpace = 'srgb'\nconst LinearSRGBColorSpace = 'srgb-linear'\nconst sRGBEncoding = 3001\nconst LinearEncoding = 3000\n\nclass GLTFLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.dracoLoader = null\n    this.ktx2Loader = null\n    this.meshoptDecoder = null\n\n    this.pluginCallbacks = []\n\n    this.register(function (parser) {\n      return new GLTFMaterialsClearcoatExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsDispersionExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFTextureBasisUExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFTextureWebPExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFTextureAVIFExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsSheenExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsTransmissionExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsVolumeExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsIorExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsEmissiveStrengthExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsSpecularExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsIridescenceExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsAnisotropyExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsBumpExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFLightsExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMeshoptCompression(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMeshGpuInstancing(parser)\n    })\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    let resourcePath\n\n    if (this.resourcePath !== '') {\n      resourcePath = this.resourcePath\n    } else if (this.path !== '') {\n      // If a base path is set, resources will be relative paths from that plus the relative path of the gltf file\n      // Example  path = 'https://my-cnd-server.com/', url = 'assets/models/model.gltf'\n      // resourcePath = 'https://my-cnd-server.com/assets/models/'\n      // referenced resource 'model.bin' will be loaded from 'https://my-cnd-server.com/assets/models/model.bin'\n      // referenced resource '../textures/texture.png' will be loaded from 'https://my-cnd-server.com/assets/textures/texture.png'\n      const relativeUrl = LoaderUtils.extractUrlBase(url)\n      resourcePath = LoaderUtils.resolveURL(relativeUrl, this.path)\n    } else {\n      resourcePath = LoaderUtils.extractUrlBase(url)\n    }\n\n    // Tells the LoadingManager to track an extra item, which resolves after\n    // the model is fully loaded. This means the count of items loaded will\n    // be incorrect, but ensures manager.onLoad() does not fire early.\n    this.manager.itemStart(url)\n\n    const _onError = function (e) {\n      if (onError) {\n        onError(e)\n      } else {\n        console.error(e)\n      }\n\n      scope.manager.itemError(url)\n      scope.manager.itemEnd(url)\n    }\n\n    const loader = new FileLoader(this.manager)\n\n    loader.setPath(this.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n\n    loader.load(\n      url,\n      function (data) {\n        try {\n          scope.parse(\n            data,\n            resourcePath,\n            function (gltf) {\n              onLoad(gltf)\n\n              scope.manager.itemEnd(url)\n            },\n            _onError,\n          )\n        } catch (e) {\n          _onError(e)\n        }\n      },\n      onProgress,\n      _onError,\n    )\n  }\n\n  setDRACOLoader(dracoLoader) {\n    this.dracoLoader = dracoLoader\n    return this\n  }\n\n  setDDSLoader() {\n    throw new Error('THREE.GLTFLoader: \"MSFT_texture_dds\" no longer supported. Please update to \"KHR_texture_basisu\".')\n  }\n\n  setKTX2Loader(ktx2Loader) {\n    this.ktx2Loader = ktx2Loader\n    return this\n  }\n\n  setMeshoptDecoder(meshoptDecoder) {\n    this.meshoptDecoder = meshoptDecoder\n    return this\n  }\n\n  register(callback) {\n    if (this.pluginCallbacks.indexOf(callback) === -1) {\n      this.pluginCallbacks.push(callback)\n    }\n\n    return this\n  }\n\n  unregister(callback) {\n    if (this.pluginCallbacks.indexOf(callback) !== -1) {\n      this.pluginCallbacks.splice(this.pluginCallbacks.indexOf(callback), 1)\n    }\n\n    return this\n  }\n\n  parse(data, path, onLoad, onError) {\n    let json\n    const extensions = {}\n    const plugins = {}\n\n    if (typeof data === 'string') {\n      json = JSON.parse(data)\n    } else if (data instanceof ArrayBuffer) {\n      const magic = decodeText(new Uint8Array(data.slice(0, 4)))\n\n      if (magic === BINARY_EXTENSION_HEADER_MAGIC) {\n        try {\n          extensions[EXTENSIONS.KHR_BINARY_GLTF] = new GLTFBinaryExtension(data)\n        } catch (error) {\n          if (onError) onError(error)\n          return\n        }\n\n        json = JSON.parse(extensions[EXTENSIONS.KHR_BINARY_GLTF].content)\n      } else {\n        json = JSON.parse(decodeText(new Uint8Array(data)))\n      }\n    } else {\n      json = data\n    }\n\n    if (json.asset === undefined || json.asset.version[0] < 2) {\n      if (onError) onError(new Error('THREE.GLTFLoader: Unsupported asset. glTF versions >=2.0 are supported.'))\n      return\n    }\n\n    const parser = new GLTFParser(json, {\n      path: path || this.resourcePath || '',\n      crossOrigin: this.crossOrigin,\n      requestHeader: this.requestHeader,\n      manager: this.manager,\n      ktx2Loader: this.ktx2Loader,\n      meshoptDecoder: this.meshoptDecoder,\n    })\n\n    parser.fileLoader.setRequestHeader(this.requestHeader)\n\n    for (let i = 0; i < this.pluginCallbacks.length; i++) {\n      const plugin = this.pluginCallbacks[i](parser)\n\n      if (!plugin.name) console.error('THREE.GLTFLoader: Invalid plugin found: missing name')\n\n      plugins[plugin.name] = plugin\n\n      // Workaround to avoid determining as unknown extension\n      // in addUnknownExtensionsToUserData().\n      // Remove this workaround if we move all the existing\n      // extension handlers to plugin system\n      extensions[plugin.name] = true\n    }\n\n    if (json.extensionsUsed) {\n      for (let i = 0; i < json.extensionsUsed.length; ++i) {\n        const extensionName = json.extensionsUsed[i]\n        const extensionsRequired = json.extensionsRequired || []\n\n        switch (extensionName) {\n          case EXTENSIONS.KHR_MATERIALS_UNLIT:\n            extensions[extensionName] = new GLTFMaterialsUnlitExtension()\n            break\n\n          case EXTENSIONS.KHR_DRACO_MESH_COMPRESSION:\n            extensions[extensionName] = new GLTFDracoMeshCompressionExtension(json, this.dracoLoader)\n            break\n\n          case EXTENSIONS.KHR_TEXTURE_TRANSFORM:\n            extensions[extensionName] = new GLTFTextureTransformExtension()\n            break\n\n          case EXTENSIONS.KHR_MESH_QUANTIZATION:\n            extensions[extensionName] = new GLTFMeshQuantizationExtension()\n            break\n\n          default:\n            if (extensionsRequired.indexOf(extensionName) >= 0 && plugins[extensionName] === undefined) {\n              console.warn('THREE.GLTFLoader: Unknown extension \"' + extensionName + '\".')\n            }\n        }\n      }\n    }\n\n    parser.setExtensions(extensions)\n    parser.setPlugins(plugins)\n    parser.parse(onLoad, onError)\n  }\n\n  parseAsync(data, path) {\n    const scope = this\n\n    return new Promise(function (resolve, reject) {\n      scope.parse(data, path, resolve, reject)\n    })\n  }\n}\n\n/* GLTFREGISTRY */\n\nfunction GLTFRegistry() {\n  let objects = {}\n\n  return {\n    get: function (key) {\n      return objects[key]\n    },\n\n    add: function (key, object) {\n      objects[key] = object\n    },\n\n    remove: function (key) {\n      delete objects[key]\n    },\n\n    removeAll: function () {\n      objects = {}\n    },\n  }\n}\n\n/*********************************/\n/********** EXTENSIONS ***********/\n/*********************************/\n\nconst EXTENSIONS = {\n  KHR_BINARY_GLTF: 'KHR_binary_glTF',\n  KHR_DRACO_MESH_COMPRESSION: 'KHR_draco_mesh_compression',\n  KHR_LIGHTS_PUNCTUAL: 'KHR_lights_punctual',\n  KHR_MATERIALS_CLEARCOAT: 'KHR_materials_clearcoat',\n  KHR_MATERIALS_DISPERSION: 'KHR_materials_dispersion',\n  KHR_MATERIALS_IOR: 'KHR_materials_ior',\n  KHR_MATERIALS_SHEEN: 'KHR_materials_sheen',\n  KHR_MATERIALS_SPECULAR: 'KHR_materials_specular',\n  KHR_MATERIALS_TRANSMISSION: 'KHR_materials_transmission',\n  KHR_MATERIALS_IRIDESCENCE: 'KHR_materials_iridescence',\n  KHR_MATERIALS_ANISOTROPY: 'KHR_materials_anisotropy',\n  KHR_MATERIALS_UNLIT: 'KHR_materials_unlit',\n  KHR_MATERIALS_VOLUME: 'KHR_materials_volume',\n  KHR_TEXTURE_BASISU: 'KHR_texture_basisu',\n  KHR_TEXTURE_TRANSFORM: 'KHR_texture_transform',\n  KHR_MESH_QUANTIZATION: 'KHR_mesh_quantization',\n  KHR_MATERIALS_EMISSIVE_STRENGTH: 'KHR_materials_emissive_strength',\n  EXT_MATERIALS_BUMP: 'EXT_materials_bump',\n  EXT_TEXTURE_WEBP: 'EXT_texture_webp',\n  EXT_TEXTURE_AVIF: 'EXT_texture_avif',\n  EXT_MESHOPT_COMPRESSION: 'EXT_meshopt_compression',\n  EXT_MESH_GPU_INSTANCING: 'EXT_mesh_gpu_instancing',\n}\n\n/**\n * Punctual Lights Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_lights_punctual\n */\nclass GLTFLightsExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_LIGHTS_PUNCTUAL\n\n    // Object3D instance caches\n    this.cache = { refs: {}, uses: {} }\n  }\n\n  _markDefs() {\n    const parser = this.parser\n    const nodeDefs = this.parser.json.nodes || []\n\n    for (let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex++) {\n      const nodeDef = nodeDefs[nodeIndex]\n\n      if (nodeDef.extensions && nodeDef.extensions[this.name] && nodeDef.extensions[this.name].light !== undefined) {\n        parser._addNodeRef(this.cache, nodeDef.extensions[this.name].light)\n      }\n    }\n  }\n\n  _loadLight(lightIndex) {\n    const parser = this.parser\n    const cacheKey = 'light:' + lightIndex\n    let dependency = parser.cache.get(cacheKey)\n\n    if (dependency) return dependency\n\n    const json = parser.json\n    const extensions = (json.extensions && json.extensions[this.name]) || {}\n    const lightDefs = extensions.lights || []\n    const lightDef = lightDefs[lightIndex]\n    let lightNode\n\n    const color = new Color(0xffffff)\n\n    if (lightDef.color !== undefined)\n      color.setRGB(lightDef.color[0], lightDef.color[1], lightDef.color[2], LinearSRGBColorSpace)\n\n    const range = lightDef.range !== undefined ? lightDef.range : 0\n\n    switch (lightDef.type) {\n      case 'directional':\n        lightNode = new DirectionalLight(color)\n        lightNode.target.position.set(0, 0, -1)\n        lightNode.add(lightNode.target)\n        break\n\n      case 'point':\n        lightNode = new PointLight(color)\n        lightNode.distance = range\n        break\n\n      case 'spot':\n        lightNode = new SpotLight(color)\n        lightNode.distance = range\n        // Handle spotlight properties.\n        lightDef.spot = lightDef.spot || {}\n        lightDef.spot.innerConeAngle = lightDef.spot.innerConeAngle !== undefined ? lightDef.spot.innerConeAngle : 0\n        lightDef.spot.outerConeAngle =\n          lightDef.spot.outerConeAngle !== undefined ? lightDef.spot.outerConeAngle : Math.PI / 4.0\n        lightNode.angle = lightDef.spot.outerConeAngle\n        lightNode.penumbra = 1.0 - lightDef.spot.innerConeAngle / lightDef.spot.outerConeAngle\n        lightNode.target.position.set(0, 0, -1)\n        lightNode.add(lightNode.target)\n        break\n\n      default:\n        throw new Error('THREE.GLTFLoader: Unexpected light type: ' + lightDef.type)\n    }\n\n    // Some lights (e.g. spot) default to a position other than the origin. Reset the position\n    // here, because node-level parsing will only override position if explicitly specified.\n    lightNode.position.set(0, 0, 0)\n\n    lightNode.decay = 2\n\n    assignExtrasToUserData(lightNode, lightDef)\n\n    if (lightDef.intensity !== undefined) lightNode.intensity = lightDef.intensity\n\n    lightNode.name = parser.createUniqueName(lightDef.name || 'light_' + lightIndex)\n\n    dependency = Promise.resolve(lightNode)\n\n    parser.cache.add(cacheKey, dependency)\n\n    return dependency\n  }\n\n  getDependency(type, index) {\n    if (type !== 'light') return\n\n    return this._loadLight(index)\n  }\n\n  createNodeAttachment(nodeIndex) {\n    const self = this\n    const parser = this.parser\n    const json = parser.json\n    const nodeDef = json.nodes[nodeIndex]\n    const lightDef = (nodeDef.extensions && nodeDef.extensions[this.name]) || {}\n    const lightIndex = lightDef.light\n\n    if (lightIndex === undefined) return null\n\n    return this._loadLight(lightIndex).then(function (light) {\n      return parser._getNodeRef(self.cache, lightIndex, light)\n    })\n  }\n}\n\n/**\n * Unlit Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_unlit\n */\nclass GLTFMaterialsUnlitExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_MATERIALS_UNLIT\n  }\n\n  getMaterialType() {\n    return MeshBasicMaterial\n  }\n\n  extendParams(materialParams, materialDef, parser) {\n    const pending = []\n\n    materialParams.color = new Color(1.0, 1.0, 1.0)\n    materialParams.opacity = 1.0\n\n    const metallicRoughness = materialDef.pbrMetallicRoughness\n\n    if (metallicRoughness) {\n      if (Array.isArray(metallicRoughness.baseColorFactor)) {\n        const array = metallicRoughness.baseColorFactor\n\n        materialParams.color.setRGB(array[0], array[1], array[2], LinearSRGBColorSpace)\n        materialParams.opacity = array[3]\n      }\n\n      if (metallicRoughness.baseColorTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace))\n      }\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials Emissive Strength Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/blob/5768b3ce0ef32bc39cdf1bef10b948586635ead3/extensions/2.0/Khronos/KHR_materials_emissive_strength/README.md\n */\nclass GLTFMaterialsEmissiveStrengthExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_EMISSIVE_STRENGTH\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const emissiveStrength = materialDef.extensions[this.name].emissiveStrength\n\n    if (emissiveStrength !== undefined) {\n      materialParams.emissiveIntensity = emissiveStrength\n    }\n\n    return Promise.resolve()\n  }\n}\n\n/**\n * Clearcoat Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_clearcoat\n */\nclass GLTFMaterialsClearcoatExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_CLEARCOAT\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    if (extension.clearcoatFactor !== undefined) {\n      materialParams.clearcoat = extension.clearcoatFactor\n    }\n\n    if (extension.clearcoatTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatMap', extension.clearcoatTexture))\n    }\n\n    if (extension.clearcoatRoughnessFactor !== undefined) {\n      materialParams.clearcoatRoughness = extension.clearcoatRoughnessFactor\n    }\n\n    if (extension.clearcoatRoughnessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatRoughnessMap', extension.clearcoatRoughnessTexture))\n    }\n\n    if (extension.clearcoatNormalTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatNormalMap', extension.clearcoatNormalTexture))\n\n      if (extension.clearcoatNormalTexture.scale !== undefined) {\n        const scale = extension.clearcoatNormalTexture.scale\n\n        materialParams.clearcoatNormalScale = new Vector2(scale, scale)\n      }\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials dispersion Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_dispersion\n */\nclass GLTFMaterialsDispersionExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_DISPERSION\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const extension = materialDef.extensions[this.name]\n\n    materialParams.dispersion = extension.dispersion !== undefined ? extension.dispersion : 0\n\n    return Promise.resolve()\n  }\n}\n\n/**\n * Iridescence Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_iridescence\n */\nclass GLTFMaterialsIridescenceExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_IRIDESCENCE\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    if (extension.iridescenceFactor !== undefined) {\n      materialParams.iridescence = extension.iridescenceFactor\n    }\n\n    if (extension.iridescenceTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'iridescenceMap', extension.iridescenceTexture))\n    }\n\n    if (extension.iridescenceIor !== undefined) {\n      materialParams.iridescenceIOR = extension.iridescenceIor\n    }\n\n    if (materialParams.iridescenceThicknessRange === undefined) {\n      materialParams.iridescenceThicknessRange = [100, 400]\n    }\n\n    if (extension.iridescenceThicknessMinimum !== undefined) {\n      materialParams.iridescenceThicknessRange[0] = extension.iridescenceThicknessMinimum\n    }\n\n    if (extension.iridescenceThicknessMaximum !== undefined) {\n      materialParams.iridescenceThicknessRange[1] = extension.iridescenceThicknessMaximum\n    }\n\n    if (extension.iridescenceThicknessTexture !== undefined) {\n      pending.push(\n        parser.assignTexture(materialParams, 'iridescenceThicknessMap', extension.iridescenceThicknessTexture),\n      )\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Sheen Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_sheen\n */\nclass GLTFMaterialsSheenExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_SHEEN\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    materialParams.sheenColor = new Color(0, 0, 0)\n    materialParams.sheenRoughness = 0\n    materialParams.sheen = 1\n\n    const extension = materialDef.extensions[this.name]\n\n    if (extension.sheenColorFactor !== undefined) {\n      const colorFactor = extension.sheenColorFactor\n      materialParams.sheenColor.setRGB(colorFactor[0], colorFactor[1], colorFactor[2], LinearSRGBColorSpace)\n    }\n\n    if (extension.sheenRoughnessFactor !== undefined) {\n      materialParams.sheenRoughness = extension.sheenRoughnessFactor\n    }\n\n    if (extension.sheenColorTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'sheenColorMap', extension.sheenColorTexture, SRGBColorSpace))\n    }\n\n    if (extension.sheenRoughnessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'sheenRoughnessMap', extension.sheenRoughnessTexture))\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Transmission Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_transmission\n * Draft: https://github.com/KhronosGroup/glTF/pull/1698\n */\nclass GLTFMaterialsTransmissionExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_TRANSMISSION\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    if (extension.transmissionFactor !== undefined) {\n      materialParams.transmission = extension.transmissionFactor\n    }\n\n    if (extension.transmissionTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'transmissionMap', extension.transmissionTexture))\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials Volume Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_volume\n */\nclass GLTFMaterialsVolumeExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_VOLUME\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    materialParams.thickness = extension.thicknessFactor !== undefined ? extension.thicknessFactor : 0\n\n    if (extension.thicknessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'thicknessMap', extension.thicknessTexture))\n    }\n\n    materialParams.attenuationDistance = extension.attenuationDistance || Infinity\n\n    const colorArray = extension.attenuationColor || [1, 1, 1]\n    materialParams.attenuationColor = new Color().setRGB(\n      colorArray[0],\n      colorArray[1],\n      colorArray[2],\n      LinearSRGBColorSpace,\n    )\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials ior Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_ior\n */\nclass GLTFMaterialsIorExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_IOR\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const extension = materialDef.extensions[this.name]\n\n    materialParams.ior = extension.ior !== undefined ? extension.ior : 1.5\n\n    return Promise.resolve()\n  }\n}\n\n/**\n * Materials specular Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_specular\n */\nclass GLTFMaterialsSpecularExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_SPECULAR\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    materialParams.specularIntensity = extension.specularFactor !== undefined ? extension.specularFactor : 1.0\n\n    if (extension.specularTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'specularIntensityMap', extension.specularTexture))\n    }\n\n    const colorArray = extension.specularColorFactor || [1, 1, 1]\n    materialParams.specularColor = new Color().setRGB(colorArray[0], colorArray[1], colorArray[2], LinearSRGBColorSpace)\n\n    if (extension.specularColorTexture !== undefined) {\n      pending.push(\n        parser.assignTexture(materialParams, 'specularColorMap', extension.specularColorTexture, SRGBColorSpace),\n      )\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials bump Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/EXT_materials_bump\n */\nclass GLTFMaterialsBumpExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.EXT_MATERIALS_BUMP\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    materialParams.bumpScale = extension.bumpFactor !== undefined ? extension.bumpFactor : 1.0\n\n    if (extension.bumpTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'bumpMap', extension.bumpTexture))\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials anisotropy Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_anisotropy\n */\nclass GLTFMaterialsAnisotropyExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_ANISOTROPY\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    if (extension.anisotropyStrength !== undefined) {\n      materialParams.anisotropy = extension.anisotropyStrength\n    }\n\n    if (extension.anisotropyRotation !== undefined) {\n      materialParams.anisotropyRotation = extension.anisotropyRotation\n    }\n\n    if (extension.anisotropyTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'anisotropyMap', extension.anisotropyTexture))\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * BasisU Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_basisu\n */\nclass GLTFTextureBasisUExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_TEXTURE_BASISU\n  }\n\n  loadTexture(textureIndex) {\n    const parser = this.parser\n    const json = parser.json\n\n    const textureDef = json.textures[textureIndex]\n\n    if (!textureDef.extensions || !textureDef.extensions[this.name]) {\n      return null\n    }\n\n    const extension = textureDef.extensions[this.name]\n    const loader = parser.options.ktx2Loader\n\n    if (!loader) {\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(this.name) >= 0) {\n        throw new Error('THREE.GLTFLoader: setKTX2Loader must be called before loading KTX2 textures')\n      } else {\n        // Assumes that the extension is optional and that a fallback texture is present\n        return null\n      }\n    }\n\n    return parser.loadTextureImage(textureIndex, extension.source, loader)\n  }\n}\n\n/**\n * WebP Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_webp\n */\nclass GLTFTextureWebPExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.EXT_TEXTURE_WEBP\n    this.isSupported = null\n  }\n\n  loadTexture(textureIndex) {\n    const name = this.name\n    const parser = this.parser\n    const json = parser.json\n\n    const textureDef = json.textures[textureIndex]\n\n    if (!textureDef.extensions || !textureDef.extensions[name]) {\n      return null\n    }\n\n    const extension = textureDef.extensions[name]\n    const source = json.images[extension.source]\n\n    let loader = parser.textureLoader\n    if (source.uri) {\n      const handler = parser.options.manager.getHandler(source.uri)\n      if (handler !== null) loader = handler\n    }\n\n    return this.detectSupport().then(function (isSupported) {\n      if (isSupported) return parser.loadTextureImage(textureIndex, extension.source, loader)\n\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(name) >= 0) {\n        throw new Error('THREE.GLTFLoader: WebP required by asset but unsupported.')\n      }\n\n      // Fall back to PNG or JPEG.\n      return parser.loadTexture(textureIndex)\n    })\n  }\n\n  detectSupport() {\n    if (!this.isSupported) {\n      this.isSupported = new Promise(function (resolve) {\n        const image = new Image()\n\n        // Lossy test image. Support for lossy images doesn't guarantee support for all\n        // WebP images, unfortunately.\n        image.src = 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA'\n\n        image.onload = image.onerror = function () {\n          resolve(image.height === 1)\n        }\n      })\n    }\n\n    return this.isSupported\n  }\n}\n\n/**\n * AVIF Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_avif\n */\nclass GLTFTextureAVIFExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.EXT_TEXTURE_AVIF\n    this.isSupported = null\n  }\n\n  loadTexture(textureIndex) {\n    const name = this.name\n    const parser = this.parser\n    const json = parser.json\n\n    const textureDef = json.textures[textureIndex]\n\n    if (!textureDef.extensions || !textureDef.extensions[name]) {\n      return null\n    }\n\n    const extension = textureDef.extensions[name]\n    const source = json.images[extension.source]\n\n    let loader = parser.textureLoader\n    if (source.uri) {\n      const handler = parser.options.manager.getHandler(source.uri)\n      if (handler !== null) loader = handler\n    }\n\n    return this.detectSupport().then(function (isSupported) {\n      if (isSupported) return parser.loadTextureImage(textureIndex, extension.source, loader)\n\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(name) >= 0) {\n        throw new Error('THREE.GLTFLoader: AVIF required by asset but unsupported.')\n      }\n\n      // Fall back to PNG or JPEG.\n      return parser.loadTexture(textureIndex)\n    })\n  }\n\n  detectSupport() {\n    if (!this.isSupported) {\n      this.isSupported = new Promise(function (resolve) {\n        const image = new Image()\n\n        // Lossy test image.\n        image.src =\n          'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAABcAAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAEAAAABAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQAMAAAAABNjb2xybmNseAACAAIABoAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAAB9tZGF0EgAKCBgABogQEDQgMgkQAAAAB8dSLfI='\n        image.onload = image.onerror = function () {\n          resolve(image.height === 1)\n        }\n      })\n    }\n\n    return this.isSupported\n  }\n}\n\n/**\n * meshopt BufferView Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_meshopt_compression\n */\nclass GLTFMeshoptCompression {\n  constructor(parser) {\n    this.name = EXTENSIONS.EXT_MESHOPT_COMPRESSION\n    this.parser = parser\n  }\n\n  loadBufferView(index) {\n    const json = this.parser.json\n    const bufferView = json.bufferViews[index]\n\n    if (bufferView.extensions && bufferView.extensions[this.name]) {\n      const extensionDef = bufferView.extensions[this.name]\n\n      const buffer = this.parser.getDependency('buffer', extensionDef.buffer)\n      const decoder = this.parser.options.meshoptDecoder\n\n      if (!decoder || !decoder.supported) {\n        if (json.extensionsRequired && json.extensionsRequired.indexOf(this.name) >= 0) {\n          throw new Error('THREE.GLTFLoader: setMeshoptDecoder must be called before loading compressed files')\n        } else {\n          // Assumes that the extension is optional and that fallback buffer data is present\n          return null\n        }\n      }\n\n      return buffer.then(function (res) {\n        const byteOffset = extensionDef.byteOffset || 0\n        const byteLength = extensionDef.byteLength || 0\n\n        const count = extensionDef.count\n        const stride = extensionDef.byteStride\n\n        const source = new Uint8Array(res, byteOffset, byteLength)\n\n        if (decoder.decodeGltfBufferAsync) {\n          return decoder\n            .decodeGltfBufferAsync(count, stride, source, extensionDef.mode, extensionDef.filter)\n            .then(function (res) {\n              return res.buffer\n            })\n        } else {\n          // Support for MeshoptDecoder 0.18 or earlier, without decodeGltfBufferAsync\n          return decoder.ready.then(function () {\n            const result = new ArrayBuffer(count * stride)\n            decoder.decodeGltfBuffer(\n              new Uint8Array(result),\n              count,\n              stride,\n              source,\n              extensionDef.mode,\n              extensionDef.filter,\n            )\n            return result\n          })\n        }\n      })\n    } else {\n      return null\n    }\n  }\n}\n\n/**\n * GPU Instancing Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_mesh_gpu_instancing\n *\n */\nclass GLTFMeshGpuInstancing {\n  constructor(parser) {\n    this.name = EXTENSIONS.EXT_MESH_GPU_INSTANCING\n    this.parser = parser\n  }\n\n  createNodeMesh(nodeIndex) {\n    const json = this.parser.json\n    const nodeDef = json.nodes[nodeIndex]\n\n    if (!nodeDef.extensions || !nodeDef.extensions[this.name] || nodeDef.mesh === undefined) {\n      return null\n    }\n\n    const meshDef = json.meshes[nodeDef.mesh]\n\n    // No Points or Lines + Instancing support yet\n\n    for (const primitive of meshDef.primitives) {\n      if (\n        primitive.mode !== WEBGL_CONSTANTS.TRIANGLES &&\n        primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_STRIP &&\n        primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_FAN &&\n        primitive.mode !== undefined\n      ) {\n        return null\n      }\n    }\n\n    const extensionDef = nodeDef.extensions[this.name]\n    const attributesDef = extensionDef.attributes\n\n    // @TODO: Can we support InstancedMesh + SkinnedMesh?\n\n    const pending = []\n    const attributes = {}\n\n    for (const key in attributesDef) {\n      pending.push(\n        this.parser.getDependency('accessor', attributesDef[key]).then((accessor) => {\n          attributes[key] = accessor\n          return attributes[key]\n        }),\n      )\n    }\n\n    if (pending.length < 1) {\n      return null\n    }\n\n    pending.push(this.parser.createNodeMesh(nodeIndex))\n\n    return Promise.all(pending).then((results) => {\n      const nodeObject = results.pop()\n      const meshes = nodeObject.isGroup ? nodeObject.children : [nodeObject]\n      const count = results[0].count // All attribute counts should be same\n      const instancedMeshes = []\n\n      for (const mesh of meshes) {\n        // Temporal variables\n        const m = new Matrix4()\n        const p = new Vector3()\n        const q = new Quaternion()\n        const s = new Vector3(1, 1, 1)\n\n        const instancedMesh = new InstancedMesh(mesh.geometry, mesh.material, count)\n\n        for (let i = 0; i < count; i++) {\n          if (attributes.TRANSLATION) {\n            p.fromBufferAttribute(attributes.TRANSLATION, i)\n          }\n\n          if (attributes.ROTATION) {\n            q.fromBufferAttribute(attributes.ROTATION, i)\n          }\n\n          if (attributes.SCALE) {\n            s.fromBufferAttribute(attributes.SCALE, i)\n          }\n\n          instancedMesh.setMatrixAt(i, m.compose(p, q, s))\n        }\n\n        // Add instance attributes to the geometry, excluding TRS.\n        for (const attributeName in attributes) {\n          if (attributeName === '_COLOR_0') {\n            const attr = attributes[attributeName]\n            instancedMesh.instanceColor = new InstancedBufferAttribute(attr.array, attr.itemSize, attr.normalized)\n          } else if (attributeName !== 'TRANSLATION' && attributeName !== 'ROTATION' && attributeName !== 'SCALE') {\n            mesh.geometry.setAttribute(attributeName, attributes[attributeName])\n          }\n        }\n\n        // Just in case\n        Object3D.prototype.copy.call(instancedMesh, mesh)\n\n        this.parser.assignFinalMaterial(instancedMesh)\n\n        instancedMeshes.push(instancedMesh)\n      }\n\n      if (nodeObject.isGroup) {\n        nodeObject.clear()\n\n        nodeObject.add(...instancedMeshes)\n\n        return nodeObject\n      }\n\n      return instancedMeshes[0]\n    })\n  }\n}\n\n/* BINARY EXTENSION */\nconst BINARY_EXTENSION_HEADER_MAGIC = 'glTF'\nconst BINARY_EXTENSION_HEADER_LENGTH = 12\nconst BINARY_EXTENSION_CHUNK_TYPES = { JSON: 0x4e4f534a, BIN: 0x004e4942 }\n\nclass GLTFBinaryExtension {\n  constructor(data) {\n    this.name = EXTENSIONS.KHR_BINARY_GLTF\n    this.content = null\n    this.body = null\n\n    const headerView = new DataView(data, 0, BINARY_EXTENSION_HEADER_LENGTH)\n\n    this.header = {\n      magic: decodeText(new Uint8Array(data.slice(0, 4))),\n      version: headerView.getUint32(4, true),\n      length: headerView.getUint32(8, true),\n    }\n\n    if (this.header.magic !== BINARY_EXTENSION_HEADER_MAGIC) {\n      throw new Error('THREE.GLTFLoader: Unsupported glTF-Binary header.')\n    } else if (this.header.version < 2.0) {\n      throw new Error('THREE.GLTFLoader: Legacy binary file detected.')\n    }\n\n    const chunkContentsLength = this.header.length - BINARY_EXTENSION_HEADER_LENGTH\n    const chunkView = new DataView(data, BINARY_EXTENSION_HEADER_LENGTH)\n    let chunkIndex = 0\n\n    while (chunkIndex < chunkContentsLength) {\n      const chunkLength = chunkView.getUint32(chunkIndex, true)\n      chunkIndex += 4\n\n      const chunkType = chunkView.getUint32(chunkIndex, true)\n      chunkIndex += 4\n\n      if (chunkType === BINARY_EXTENSION_CHUNK_TYPES.JSON) {\n        const contentArray = new Uint8Array(data, BINARY_EXTENSION_HEADER_LENGTH + chunkIndex, chunkLength)\n        this.content = decodeText(contentArray)\n      } else if (chunkType === BINARY_EXTENSION_CHUNK_TYPES.BIN) {\n        const byteOffset = BINARY_EXTENSION_HEADER_LENGTH + chunkIndex\n        this.body = data.slice(byteOffset, byteOffset + chunkLength)\n      }\n\n      // Clients must ignore chunks with unknown types.\n\n      chunkIndex += chunkLength\n    }\n\n    if (this.content === null) {\n      throw new Error('THREE.GLTFLoader: JSON content not found.')\n    }\n  }\n}\n\n/**\n * DRACO Mesh Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_draco_mesh_compression\n */\nclass GLTFDracoMeshCompressionExtension {\n  constructor(json, dracoLoader) {\n    if (!dracoLoader) {\n      throw new Error('THREE.GLTFLoader: No DRACOLoader instance provided.')\n    }\n\n    this.name = EXTENSIONS.KHR_DRACO_MESH_COMPRESSION\n    this.json = json\n    this.dracoLoader = dracoLoader\n    this.dracoLoader.preload()\n  }\n\n  decodePrimitive(primitive, parser) {\n    const json = this.json\n    const dracoLoader = this.dracoLoader\n    const bufferViewIndex = primitive.extensions[this.name].bufferView\n    const gltfAttributeMap = primitive.extensions[this.name].attributes\n    const threeAttributeMap = {}\n    const attributeNormalizedMap = {}\n    const attributeTypeMap = {}\n\n    for (const attributeName in gltfAttributeMap) {\n      const threeAttributeName = ATTRIBUTES[attributeName] || attributeName.toLowerCase()\n\n      threeAttributeMap[threeAttributeName] = gltfAttributeMap[attributeName]\n    }\n\n    for (const attributeName in primitive.attributes) {\n      const threeAttributeName = ATTRIBUTES[attributeName] || attributeName.toLowerCase()\n\n      if (gltfAttributeMap[attributeName] !== undefined) {\n        const accessorDef = json.accessors[primitive.attributes[attributeName]]\n        const componentType = WEBGL_COMPONENT_TYPES[accessorDef.componentType]\n\n        attributeTypeMap[threeAttributeName] = componentType.name\n        attributeNormalizedMap[threeAttributeName] = accessorDef.normalized === true\n      }\n    }\n\n    return parser.getDependency('bufferView', bufferViewIndex).then(function (bufferView) {\n      return new Promise(function (resolve, reject) {\n        dracoLoader.decodeDracoFile(\n          bufferView,\n          function (geometry) {\n            for (const attributeName in geometry.attributes) {\n              const attribute = geometry.attributes[attributeName]\n              const normalized = attributeNormalizedMap[attributeName]\n\n              if (normalized !== undefined) attribute.normalized = normalized\n            }\n\n            resolve(geometry)\n          },\n          threeAttributeMap,\n          attributeTypeMap,\n          LinearSRGBColorSpace,\n          reject,\n        )\n      })\n    })\n  }\n}\n\n/**\n * Texture Transform Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_transform\n */\nclass GLTFTextureTransformExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_TEXTURE_TRANSFORM\n  }\n\n  extendTexture(texture, transform) {\n    if (\n      (transform.texCoord === undefined || transform.texCoord === texture.channel) &&\n      transform.offset === undefined &&\n      transform.rotation === undefined &&\n      transform.scale === undefined\n    ) {\n      // See https://github.com/mrdoob/three.js/issues/21819.\n      return texture\n    }\n\n    texture = texture.clone()\n\n    if (transform.texCoord !== undefined) {\n      texture.channel = transform.texCoord\n    }\n\n    if (transform.offset !== undefined) {\n      texture.offset.fromArray(transform.offset)\n    }\n\n    if (transform.rotation !== undefined) {\n      texture.rotation = transform.rotation\n    }\n\n    if (transform.scale !== undefined) {\n      texture.repeat.fromArray(transform.scale)\n    }\n\n    texture.needsUpdate = true\n\n    return texture\n  }\n}\n\n/**\n * Mesh Quantization Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization\n */\nclass GLTFMeshQuantizationExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_MESH_QUANTIZATION\n  }\n}\n\n/*********************************/\n/********** INTERPOLATION ********/\n/*********************************/\n\n// Spline Interpolation\n// Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#appendix-c-spline-interpolation\nclass GLTFCubicSplineInterpolant extends Interpolant {\n  constructor(parameterPositions, sampleValues, sampleSize, resultBuffer) {\n    super(parameterPositions, sampleValues, sampleSize, resultBuffer)\n  }\n\n  copySampleValue_(index) {\n    // Copies a sample value to the result buffer. See description of glTF\n    // CUBICSPLINE values layout in interpolate_() function below.\n\n    const result = this.resultBuffer,\n      values = this.sampleValues,\n      valueSize = this.valueSize,\n      offset = index * valueSize * 3 + valueSize\n\n    for (let i = 0; i !== valueSize; i++) {\n      result[i] = values[offset + i]\n    }\n\n    return result\n  }\n\n  interpolate_(i1, t0, t, t1) {\n    const result = this.resultBuffer\n    const values = this.sampleValues\n    const stride = this.valueSize\n\n    const stride2 = stride * 2\n    const stride3 = stride * 3\n\n    const td = t1 - t0\n\n    const p = (t - t0) / td\n    const pp = p * p\n    const ppp = pp * p\n\n    const offset1 = i1 * stride3\n    const offset0 = offset1 - stride3\n\n    const s2 = -2 * ppp + 3 * pp\n    const s3 = ppp - pp\n    const s0 = 1 - s2\n    const s1 = s3 - pp + p\n\n    // Layout of keyframe output values for CUBICSPLINE animations:\n    //   [ inTangent_1, splineVertex_1, outTangent_1, inTangent_2, splineVertex_2, ... ]\n    for (let i = 0; i !== stride; i++) {\n      const p0 = values[offset0 + i + stride] // splineVertex_k\n      const m0 = values[offset0 + i + stride2] * td // outTangent_k * (t_k+1 - t_k)\n      const p1 = values[offset1 + i + stride] // splineVertex_k+1\n      const m1 = values[offset1 + i] * td // inTangent_k+1 * (t_k+1 - t_k)\n\n      result[i] = s0 * p0 + s1 * m0 + s2 * p1 + s3 * m1\n    }\n\n    return result\n  }\n}\n\nconst _q = /* @__PURE__ */ new Quaternion()\n\nclass GLTFCubicSplineQuaternionInterpolant extends GLTFCubicSplineInterpolant {\n  interpolate_(i1, t0, t, t1) {\n    const result = super.interpolate_(i1, t0, t, t1)\n\n    _q.fromArray(result).normalize().toArray(result)\n\n    return result\n  }\n}\n\n/*********************************/\n/********** INTERNALS ************/\n/*********************************/\n\n/* CONSTANTS */\n\nconst WEBGL_CONSTANTS = {\n  FLOAT: 5126,\n  //FLOAT_MAT2: 35674,\n  FLOAT_MAT3: 35675,\n  FLOAT_MAT4: 35676,\n  FLOAT_VEC2: 35664,\n  FLOAT_VEC3: 35665,\n  FLOAT_VEC4: 35666,\n  LINEAR: 9729,\n  REPEAT: 10497,\n  SAMPLER_2D: 35678,\n  POINTS: 0,\n  LINES: 1,\n  LINE_LOOP: 2,\n  LINE_STRIP: 3,\n  TRIANGLES: 4,\n  TRIANGLE_STRIP: 5,\n  TRIANGLE_FAN: 6,\n  UNSIGNED_BYTE: 5121,\n  UNSIGNED_SHORT: 5123,\n}\n\nconst WEBGL_COMPONENT_TYPES = {\n  5120: Int8Array,\n  5121: Uint8Array,\n  5122: Int16Array,\n  5123: Uint16Array,\n  5125: Uint32Array,\n  5126: Float32Array,\n}\n\nconst WEBGL_FILTERS = {\n  9728: NearestFilter,\n  9729: LinearFilter,\n  9984: NearestMipmapNearestFilter,\n  9985: LinearMipmapNearestFilter,\n  9986: NearestMipmapLinearFilter,\n  9987: LinearMipmapLinearFilter,\n}\n\nconst WEBGL_WRAPPINGS = {\n  33071: ClampToEdgeWrapping,\n  33648: MirroredRepeatWrapping,\n  10497: RepeatWrapping,\n}\n\nconst WEBGL_TYPE_SIZES = {\n  SCALAR: 1,\n  VEC2: 2,\n  VEC3: 3,\n  VEC4: 4,\n  MAT2: 4,\n  MAT3: 9,\n  MAT4: 16,\n}\n\nconst ATTRIBUTES = {\n  POSITION: 'position',\n  NORMAL: 'normal',\n  TANGENT: 'tangent',\n  // uv => uv1, 4 uv channels\n  // https://github.com/mrdoob/three.js/pull/25943\n  // https://github.com/mrdoob/three.js/pull/25788\n  ...(version >= 152\n    ? {\n        TEXCOORD_0: 'uv',\n        TEXCOORD_1: 'uv1',\n        TEXCOORD_2: 'uv2',\n        TEXCOORD_3: 'uv3',\n      }\n    : {\n        TEXCOORD_0: 'uv',\n        TEXCOORD_1: 'uv2',\n      }),\n\n  COLOR_0: 'color',\n  WEIGHTS_0: 'skinWeight',\n  JOINTS_0: 'skinIndex',\n}\n\nconst PATH_PROPERTIES = {\n  scale: 'scale',\n  translation: 'position',\n  rotation: 'quaternion',\n  weights: 'morphTargetInfluences',\n}\n\nconst INTERPOLATION = {\n  CUBICSPLINE: undefined, // We use a custom interpolant (GLTFCubicSplineInterpolation) for CUBICSPLINE tracks. Each\n  // keyframe track will be initialized with a default interpolation type, then modified.\n  LINEAR: InterpolateLinear,\n  STEP: InterpolateDiscrete,\n}\n\nconst ALPHA_MODES = {\n  OPAQUE: 'OPAQUE',\n  MASK: 'MASK',\n  BLEND: 'BLEND',\n}\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#default-material\n */\nfunction createDefaultMaterial(cache) {\n  if (cache['DefaultMaterial'] === undefined) {\n    cache['DefaultMaterial'] = new MeshStandardMaterial({\n      color: 0xffffff,\n      emissive: 0x000000,\n      metalness: 1,\n      roughness: 1,\n      transparent: false,\n      depthTest: true,\n      side: FrontSide,\n    })\n  }\n\n  return cache['DefaultMaterial']\n}\n\nfunction addUnknownExtensionsToUserData(knownExtensions, object, objectDef) {\n  // Add unknown glTF extensions to an object's userData.\n\n  for (const name in objectDef.extensions) {\n    if (knownExtensions[name] === undefined) {\n      object.userData.gltfExtensions = object.userData.gltfExtensions || {}\n      object.userData.gltfExtensions[name] = objectDef.extensions[name]\n    }\n  }\n}\n\n/**\n * @param {Object3D|Material|BufferGeometry} object\n * @param {GLTF.definition} gltfDef\n */\nfunction assignExtrasToUserData(object, gltfDef) {\n  if (gltfDef.extras !== undefined) {\n    if (typeof gltfDef.extras === 'object') {\n      Object.assign(object.userData, gltfDef.extras)\n    } else {\n      console.warn('THREE.GLTFLoader: Ignoring primitive type .extras, ' + gltfDef.extras)\n    }\n  }\n}\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#morph-targets\n *\n * @param {BufferGeometry} geometry\n * @param {Array<GLTF.Target>} targets\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addMorphTargets(geometry, targets, parser) {\n  let hasMorphPosition = false\n  let hasMorphNormal = false\n  let hasMorphColor = false\n\n  for (let i = 0, il = targets.length; i < il; i++) {\n    const target = targets[i]\n\n    if (target.POSITION !== undefined) hasMorphPosition = true\n    if (target.NORMAL !== undefined) hasMorphNormal = true\n    if (target.COLOR_0 !== undefined) hasMorphColor = true\n\n    if (hasMorphPosition && hasMorphNormal && hasMorphColor) break\n  }\n\n  if (!hasMorphPosition && !hasMorphNormal && !hasMorphColor) return Promise.resolve(geometry)\n\n  const pendingPositionAccessors = []\n  const pendingNormalAccessors = []\n  const pendingColorAccessors = []\n\n  for (let i = 0, il = targets.length; i < il; i++) {\n    const target = targets[i]\n\n    if (hasMorphPosition) {\n      const pendingAccessor =\n        target.POSITION !== undefined ? parser.getDependency('accessor', target.POSITION) : geometry.attributes.position\n\n      pendingPositionAccessors.push(pendingAccessor)\n    }\n\n    if (hasMorphNormal) {\n      const pendingAccessor =\n        target.NORMAL !== undefined ? parser.getDependency('accessor', target.NORMAL) : geometry.attributes.normal\n\n      pendingNormalAccessors.push(pendingAccessor)\n    }\n\n    if (hasMorphColor) {\n      const pendingAccessor =\n        target.COLOR_0 !== undefined ? parser.getDependency('accessor', target.COLOR_0) : geometry.attributes.color\n\n      pendingColorAccessors.push(pendingAccessor)\n    }\n  }\n\n  return Promise.all([\n    Promise.all(pendingPositionAccessors),\n    Promise.all(pendingNormalAccessors),\n    Promise.all(pendingColorAccessors),\n  ]).then(function (accessors) {\n    const morphPositions = accessors[0]\n    const morphNormals = accessors[1]\n    const morphColors = accessors[2]\n\n    if (hasMorphPosition) geometry.morphAttributes.position = morphPositions\n    if (hasMorphNormal) geometry.morphAttributes.normal = morphNormals\n    if (hasMorphColor) geometry.morphAttributes.color = morphColors\n    geometry.morphTargetsRelative = true\n\n    return geometry\n  })\n}\n\n/**\n * @param {Mesh} mesh\n * @param {GLTF.Mesh} meshDef\n */\nfunction updateMorphTargets(mesh, meshDef) {\n  mesh.updateMorphTargets()\n\n  if (meshDef.weights !== undefined) {\n    for (let i = 0, il = meshDef.weights.length; i < il; i++) {\n      mesh.morphTargetInfluences[i] = meshDef.weights[i]\n    }\n  }\n\n  // .extras has user-defined data, so check that .extras.targetNames is an array.\n  if (meshDef.extras && Array.isArray(meshDef.extras.targetNames)) {\n    const targetNames = meshDef.extras.targetNames\n\n    if (mesh.morphTargetInfluences.length === targetNames.length) {\n      mesh.morphTargetDictionary = {}\n\n      for (let i = 0, il = targetNames.length; i < il; i++) {\n        mesh.morphTargetDictionary[targetNames[i]] = i\n      }\n    } else {\n      console.warn('THREE.GLTFLoader: Invalid extras.targetNames length. Ignoring names.')\n    }\n  }\n}\n\nfunction createPrimitiveKey(primitiveDef) {\n  let geometryKey\n\n  const dracoExtension = primitiveDef.extensions && primitiveDef.extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION]\n\n  if (dracoExtension) {\n    geometryKey =\n      'draco:' +\n      dracoExtension.bufferView +\n      ':' +\n      dracoExtension.indices +\n      ':' +\n      createAttributesKey(dracoExtension.attributes)\n  } else {\n    geometryKey = primitiveDef.indices + ':' + createAttributesKey(primitiveDef.attributes) + ':' + primitiveDef.mode\n  }\n\n  if (primitiveDef.targets !== undefined) {\n    for (let i = 0, il = primitiveDef.targets.length; i < il; i++) {\n      geometryKey += ':' + createAttributesKey(primitiveDef.targets[i])\n    }\n  }\n\n  return geometryKey\n}\n\nfunction createAttributesKey(attributes) {\n  let attributesKey = ''\n\n  const keys = Object.keys(attributes).sort()\n\n  for (let i = 0, il = keys.length; i < il; i++) {\n    attributesKey += keys[i] + ':' + attributes[keys[i]] + ';'\n  }\n\n  return attributesKey\n}\n\nfunction getNormalizedComponentScale(constructor) {\n  // Reference:\n  // https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization#encoding-quantized-data\n\n  switch (constructor) {\n    case Int8Array:\n      return 1 / 127\n\n    case Uint8Array:\n      return 1 / 255\n\n    case Int16Array:\n      return 1 / 32767\n\n    case Uint16Array:\n      return 1 / 65535\n\n    default:\n      throw new Error('THREE.GLTFLoader: Unsupported normalized accessor component type.')\n  }\n}\n\nfunction getImageURIMimeType(uri) {\n  if (uri.search(/\\.jpe?g($|\\?)/i) > 0 || uri.search(/^data\\:image\\/jpeg/) === 0) return 'image/jpeg'\n  if (uri.search(/\\.webp($|\\?)/i) > 0 || uri.search(/^data\\:image\\/webp/) === 0) return 'image/webp'\n\n  return 'image/png'\n}\n\nconst _identityMatrix = /* @__PURE__ */ new Matrix4()\n\n/* GLTF PARSER */\n\nclass GLTFParser {\n  constructor(json = {}, options = {}) {\n    this.json = json\n    this.extensions = {}\n    this.plugins = {}\n    this.options = options\n\n    // loader object cache\n    this.cache = new GLTFRegistry()\n\n    // associations between Three.js objects and glTF elements\n    this.associations = new Map()\n\n    // BufferGeometry caching\n    this.primitiveCache = {}\n\n    // Node cache\n    this.nodeCache = {}\n\n    // Object3D instance caches\n    this.meshCache = { refs: {}, uses: {} }\n    this.cameraCache = { refs: {}, uses: {} }\n    this.lightCache = { refs: {}, uses: {} }\n\n    this.sourceCache = {}\n    this.textureCache = {}\n\n    // Track node names, to ensure no duplicates\n    this.nodeNamesUsed = {}\n\n    // Use an ImageBitmapLoader if imageBitmaps are supported. Moves much of the\n    // expensive work of uploading a texture to the GPU off the main thread.\n\n    let isSafari = false\n    let isFirefox = false\n    let firefoxVersion = -1\n\n    if (typeof navigator !== 'undefined' && typeof navigator.userAgent !== 'undefined') {\n      isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent) === true\n      isFirefox = navigator.userAgent.indexOf('Firefox') > -1\n      firefoxVersion = isFirefox ? navigator.userAgent.match(/Firefox\\/([0-9]+)\\./)[1] : -1\n    }\n\n    if (typeof createImageBitmap === 'undefined' || isSafari || (isFirefox && firefoxVersion < 98)) {\n      this.textureLoader = new TextureLoader(this.options.manager)\n    } else {\n      this.textureLoader = new ImageBitmapLoader(this.options.manager)\n    }\n\n    this.textureLoader.setCrossOrigin(this.options.crossOrigin)\n    this.textureLoader.setRequestHeader(this.options.requestHeader)\n\n    this.fileLoader = new FileLoader(this.options.manager)\n    this.fileLoader.setResponseType('arraybuffer')\n\n    if (this.options.crossOrigin === 'use-credentials') {\n      this.fileLoader.setWithCredentials(true)\n    }\n  }\n\n  setExtensions(extensions) {\n    this.extensions = extensions\n  }\n\n  setPlugins(plugins) {\n    this.plugins = plugins\n  }\n\n  parse(onLoad, onError) {\n    const parser = this\n    const json = this.json\n    const extensions = this.extensions\n\n    // Clear the loader cache\n    this.cache.removeAll()\n    this.nodeCache = {}\n\n    // Mark the special nodes/meshes in json for efficient parse\n    this._invokeAll(function (ext) {\n      return ext._markDefs && ext._markDefs()\n    })\n\n    Promise.all(\n      this._invokeAll(function (ext) {\n        return ext.beforeRoot && ext.beforeRoot()\n      }),\n    )\n      .then(function () {\n        return Promise.all([\n          parser.getDependencies('scene'),\n          parser.getDependencies('animation'),\n          parser.getDependencies('camera'),\n        ])\n      })\n      .then(function (dependencies) {\n        const result = {\n          scene: dependencies[0][json.scene || 0],\n          scenes: dependencies[0],\n          animations: dependencies[1],\n          cameras: dependencies[2],\n          asset: json.asset,\n          parser: parser,\n          userData: {},\n        }\n\n        addUnknownExtensionsToUserData(extensions, result, json)\n\n        assignExtrasToUserData(result, json)\n\n        return Promise.all(\n          parser._invokeAll(function (ext) {\n            return ext.afterRoot && ext.afterRoot(result)\n          }),\n        ).then(function () {\n          for (const scene of result.scenes) {\n            scene.updateMatrixWorld()\n          }\n\n          onLoad(result)\n        })\n      })\n      .catch(onError)\n  }\n\n  /**\n   * Marks the special nodes/meshes in json for efficient parse.\n   */\n  _markDefs() {\n    const nodeDefs = this.json.nodes || []\n    const skinDefs = this.json.skins || []\n    const meshDefs = this.json.meshes || []\n\n    // Nothing in the node definition indicates whether it is a Bone or an\n    // Object3D. Use the skins' joint references to mark bones.\n    for (let skinIndex = 0, skinLength = skinDefs.length; skinIndex < skinLength; skinIndex++) {\n      const joints = skinDefs[skinIndex].joints\n\n      for (let i = 0, il = joints.length; i < il; i++) {\n        nodeDefs[joints[i]].isBone = true\n      }\n    }\n\n    // Iterate over all nodes, marking references to shared resources,\n    // as well as skeleton joints.\n    for (let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex++) {\n      const nodeDef = nodeDefs[nodeIndex]\n\n      if (nodeDef.mesh !== undefined) {\n        this._addNodeRef(this.meshCache, nodeDef.mesh)\n\n        // Nothing in the mesh definition indicates whether it is\n        // a SkinnedMesh or Mesh. Use the node's mesh reference\n        // to mark SkinnedMesh if node has skin.\n        if (nodeDef.skin !== undefined) {\n          meshDefs[nodeDef.mesh].isSkinnedMesh = true\n        }\n      }\n\n      if (nodeDef.camera !== undefined) {\n        this._addNodeRef(this.cameraCache, nodeDef.camera)\n      }\n    }\n  }\n\n  /**\n   * Counts references to shared node / Object3D resources. These resources\n   * can be reused, or \"instantiated\", at multiple nodes in the scene\n   * hierarchy. Mesh, Camera, and Light instances are instantiated and must\n   * be marked. Non-scenegraph resources (like Materials, Geometries, and\n   * Textures) can be reused directly and are not marked here.\n   *\n   * Example: CesiumMilkTruck sample model reuses \"Wheel\" meshes.\n   */\n  _addNodeRef(cache, index) {\n    if (index === undefined) return\n\n    if (cache.refs[index] === undefined) {\n      cache.refs[index] = cache.uses[index] = 0\n    }\n\n    cache.refs[index]++\n  }\n\n  /** Returns a reference to a shared resource, cloning it if necessary. */\n  _getNodeRef(cache, index, object) {\n    if (cache.refs[index] <= 1) return object\n\n    const ref = object.clone()\n\n    // Propagates mappings to the cloned object, prevents mappings on the\n    // original object from being lost.\n    const updateMappings = (original, clone) => {\n      const mappings = this.associations.get(original)\n      if (mappings != null) {\n        this.associations.set(clone, mappings)\n      }\n\n      for (const [i, child] of original.children.entries()) {\n        updateMappings(child, clone.children[i])\n      }\n    }\n\n    updateMappings(object, ref)\n\n    ref.name += '_instance_' + cache.uses[index]++\n\n    return ref\n  }\n\n  _invokeOne(func) {\n    const extensions = Object.values(this.plugins)\n    extensions.push(this)\n\n    for (let i = 0; i < extensions.length; i++) {\n      const result = func(extensions[i])\n\n      if (result) return result\n    }\n\n    return null\n  }\n\n  _invokeAll(func) {\n    const extensions = Object.values(this.plugins)\n    extensions.unshift(this)\n\n    const pending = []\n\n    for (let i = 0; i < extensions.length; i++) {\n      const result = func(extensions[i])\n\n      if (result) pending.push(result)\n    }\n\n    return pending\n  }\n\n  /**\n   * Requests the specified dependency asynchronously, with caching.\n   * @param {string} type\n   * @param {number} index\n   * @return {Promise<Object3D|Material|THREE.Texture|AnimationClip|ArrayBuffer|Object>}\n   */\n  getDependency(type, index) {\n    const cacheKey = type + ':' + index\n    let dependency = this.cache.get(cacheKey)\n\n    if (!dependency) {\n      switch (type) {\n        case 'scene':\n          dependency = this.loadScene(index)\n          break\n\n        case 'node':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadNode && ext.loadNode(index)\n          })\n          break\n\n        case 'mesh':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadMesh && ext.loadMesh(index)\n          })\n          break\n\n        case 'accessor':\n          dependency = this.loadAccessor(index)\n          break\n\n        case 'bufferView':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadBufferView && ext.loadBufferView(index)\n          })\n          break\n\n        case 'buffer':\n          dependency = this.loadBuffer(index)\n          break\n\n        case 'material':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadMaterial && ext.loadMaterial(index)\n          })\n          break\n\n        case 'texture':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadTexture && ext.loadTexture(index)\n          })\n          break\n\n        case 'skin':\n          dependency = this.loadSkin(index)\n          break\n\n        case 'animation':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadAnimation && ext.loadAnimation(index)\n          })\n          break\n\n        case 'camera':\n          dependency = this.loadCamera(index)\n          break\n\n        default:\n          dependency = this._invokeOne(function (ext) {\n            return ext != this && ext.getDependency && ext.getDependency(type, index)\n          })\n\n          if (!dependency) {\n            throw new Error('Unknown type: ' + type)\n          }\n\n          break\n      }\n\n      this.cache.add(cacheKey, dependency)\n    }\n\n    return dependency\n  }\n\n  /**\n   * Requests all dependencies of the specified type asynchronously, with caching.\n   * @param {string} type\n   * @return {Promise<Array<Object>>}\n   */\n  getDependencies(type) {\n    let dependencies = this.cache.get(type)\n\n    if (!dependencies) {\n      const parser = this\n      const defs = this.json[type + (type === 'mesh' ? 'es' : 's')] || []\n\n      dependencies = Promise.all(\n        defs.map(function (def, index) {\n          return parser.getDependency(type, index)\n        }),\n      )\n\n      this.cache.add(type, dependencies)\n    }\n\n    return dependencies\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n   * @param {number} bufferIndex\n   * @return {Promise<ArrayBuffer>}\n   */\n  loadBuffer(bufferIndex) {\n    const bufferDef = this.json.buffers[bufferIndex]\n    const loader = this.fileLoader\n\n    if (bufferDef.type && bufferDef.type !== 'arraybuffer') {\n      throw new Error('THREE.GLTFLoader: ' + bufferDef.type + ' buffer type is not supported.')\n    }\n\n    // If present, GLB container is required to be the first buffer.\n    if (bufferDef.uri === undefined && bufferIndex === 0) {\n      return Promise.resolve(this.extensions[EXTENSIONS.KHR_BINARY_GLTF].body)\n    }\n\n    const options = this.options\n\n    return new Promise(function (resolve, reject) {\n      loader.load(LoaderUtils.resolveURL(bufferDef.uri, options.path), resolve, undefined, function () {\n        reject(new Error('THREE.GLTFLoader: Failed to load buffer \"' + bufferDef.uri + '\".'))\n      })\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n   * @param {number} bufferViewIndex\n   * @return {Promise<ArrayBuffer>}\n   */\n  loadBufferView(bufferViewIndex) {\n    const bufferViewDef = this.json.bufferViews[bufferViewIndex]\n\n    return this.getDependency('buffer', bufferViewDef.buffer).then(function (buffer) {\n      const byteLength = bufferViewDef.byteLength || 0\n      const byteOffset = bufferViewDef.byteOffset || 0\n      return buffer.slice(byteOffset, byteOffset + byteLength)\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#accessors\n   * @param {number} accessorIndex\n   * @return {Promise<BufferAttribute|InterleavedBufferAttribute>}\n   */\n  loadAccessor(accessorIndex) {\n    const parser = this\n    const json = this.json\n\n    const accessorDef = this.json.accessors[accessorIndex]\n\n    if (accessorDef.bufferView === undefined && accessorDef.sparse === undefined) {\n      const itemSize = WEBGL_TYPE_SIZES[accessorDef.type]\n      const TypedArray = WEBGL_COMPONENT_TYPES[accessorDef.componentType]\n      const normalized = accessorDef.normalized === true\n\n      const array = new TypedArray(accessorDef.count * itemSize)\n      return Promise.resolve(new BufferAttribute(array, itemSize, normalized))\n    }\n\n    const pendingBufferViews = []\n\n    if (accessorDef.bufferView !== undefined) {\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.bufferView))\n    } else {\n      pendingBufferViews.push(null)\n    }\n\n    if (accessorDef.sparse !== undefined) {\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.sparse.indices.bufferView))\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.sparse.values.bufferView))\n    }\n\n    return Promise.all(pendingBufferViews).then(function (bufferViews) {\n      const bufferView = bufferViews[0]\n\n      const itemSize = WEBGL_TYPE_SIZES[accessorDef.type]\n      const TypedArray = WEBGL_COMPONENT_TYPES[accessorDef.componentType]\n\n      // For VEC3: itemSize is 3, elementBytes is 4, itemBytes is 12.\n      const elementBytes = TypedArray.BYTES_PER_ELEMENT\n      const itemBytes = elementBytes * itemSize\n      const byteOffset = accessorDef.byteOffset || 0\n      const byteStride =\n        accessorDef.bufferView !== undefined ? json.bufferViews[accessorDef.bufferView].byteStride : undefined\n      const normalized = accessorDef.normalized === true\n      let array, bufferAttribute\n\n      // The buffer is not interleaved if the stride is the item size in bytes.\n      if (byteStride && byteStride !== itemBytes) {\n        // Each \"slice\" of the buffer, as defined by 'count' elements of 'byteStride' bytes, gets its own InterleavedBuffer\n        // This makes sure that IBA.count reflects accessor.count properly\n        const ibSlice = Math.floor(byteOffset / byteStride)\n        const ibCacheKey =\n          'InterleavedBuffer:' +\n          accessorDef.bufferView +\n          ':' +\n          accessorDef.componentType +\n          ':' +\n          ibSlice +\n          ':' +\n          accessorDef.count\n        let ib = parser.cache.get(ibCacheKey)\n\n        if (!ib) {\n          array = new TypedArray(bufferView, ibSlice * byteStride, (accessorDef.count * byteStride) / elementBytes)\n\n          // Integer parameters to IB/IBA are in array elements, not bytes.\n          ib = new InterleavedBuffer(array, byteStride / elementBytes)\n\n          parser.cache.add(ibCacheKey, ib)\n        }\n\n        bufferAttribute = new InterleavedBufferAttribute(\n          ib,\n          itemSize,\n          (byteOffset % byteStride) / elementBytes,\n          normalized,\n        )\n      } else {\n        if (bufferView === null) {\n          array = new TypedArray(accessorDef.count * itemSize)\n        } else {\n          array = new TypedArray(bufferView, byteOffset, accessorDef.count * itemSize)\n        }\n\n        bufferAttribute = new BufferAttribute(array, itemSize, normalized)\n      }\n\n      // https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#sparse-accessors\n      if (accessorDef.sparse !== undefined) {\n        const itemSizeIndices = WEBGL_TYPE_SIZES.SCALAR\n        const TypedArrayIndices = WEBGL_COMPONENT_TYPES[accessorDef.sparse.indices.componentType]\n\n        const byteOffsetIndices = accessorDef.sparse.indices.byteOffset || 0\n        const byteOffsetValues = accessorDef.sparse.values.byteOffset || 0\n\n        const sparseIndices = new TypedArrayIndices(\n          bufferViews[1],\n          byteOffsetIndices,\n          accessorDef.sparse.count * itemSizeIndices,\n        )\n        const sparseValues = new TypedArray(bufferViews[2], byteOffsetValues, accessorDef.sparse.count * itemSize)\n\n        if (bufferView !== null) {\n          // Avoid modifying the original ArrayBuffer, if the bufferView wasn't initialized with zeroes.\n          bufferAttribute = new BufferAttribute(\n            bufferAttribute.array.slice(),\n            bufferAttribute.itemSize,\n            bufferAttribute.normalized,\n          )\n        }\n\n        for (let i = 0, il = sparseIndices.length; i < il; i++) {\n          const index = sparseIndices[i]\n\n          bufferAttribute.setX(index, sparseValues[i * itemSize])\n          if (itemSize >= 2) bufferAttribute.setY(index, sparseValues[i * itemSize + 1])\n          if (itemSize >= 3) bufferAttribute.setZ(index, sparseValues[i * itemSize + 2])\n          if (itemSize >= 4) bufferAttribute.setW(index, sparseValues[i * itemSize + 3])\n          if (itemSize >= 5) throw new Error('THREE.GLTFLoader: Unsupported itemSize in sparse BufferAttribute.')\n        }\n      }\n\n      return bufferAttribute\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#textures\n   * @param {number} textureIndex\n   * @return {Promise<THREE.Texture|null>}\n   */\n  loadTexture(textureIndex) {\n    const json = this.json\n    const options = this.options\n    const textureDef = json.textures[textureIndex]\n    const sourceIndex = textureDef.source\n    const sourceDef = json.images[sourceIndex]\n\n    let loader = this.textureLoader\n\n    if (sourceDef.uri) {\n      const handler = options.manager.getHandler(sourceDef.uri)\n      if (handler !== null) loader = handler\n    }\n\n    return this.loadTextureImage(textureIndex, sourceIndex, loader)\n  }\n\n  loadTextureImage(textureIndex, sourceIndex, loader) {\n    const parser = this\n    const json = this.json\n\n    const textureDef = json.textures[textureIndex]\n    const sourceDef = json.images[sourceIndex]\n\n    const cacheKey = (sourceDef.uri || sourceDef.bufferView) + ':' + textureDef.sampler\n\n    if (this.textureCache[cacheKey]) {\n      // See https://github.com/mrdoob/three.js/issues/21559.\n      return this.textureCache[cacheKey]\n    }\n\n    const promise = this.loadImageSource(sourceIndex, loader)\n      .then(function (texture) {\n        texture.flipY = false\n\n        texture.name = textureDef.name || sourceDef.name || ''\n\n        if (\n          texture.name === '' &&\n          typeof sourceDef.uri === 'string' &&\n          sourceDef.uri.startsWith('data:image/') === false\n        ) {\n          texture.name = sourceDef.uri\n        }\n\n        const samplers = json.samplers || {}\n        const sampler = samplers[textureDef.sampler] || {}\n\n        texture.magFilter = WEBGL_FILTERS[sampler.magFilter] || LinearFilter\n        texture.minFilter = WEBGL_FILTERS[sampler.minFilter] || LinearMipmapLinearFilter\n        texture.wrapS = WEBGL_WRAPPINGS[sampler.wrapS] || RepeatWrapping\n        texture.wrapT = WEBGL_WRAPPINGS[sampler.wrapT] || RepeatWrapping\n\n        parser.associations.set(texture, { textures: textureIndex })\n\n        return texture\n      })\n      .catch(function () {\n        return null\n      })\n\n    this.textureCache[cacheKey] = promise\n\n    return promise\n  }\n\n  loadImageSource(sourceIndex, loader) {\n    const parser = this\n    const json = this.json\n    const options = this.options\n\n    if (this.sourceCache[sourceIndex] !== undefined) {\n      return this.sourceCache[sourceIndex].then((texture) => texture.clone())\n    }\n\n    const sourceDef = json.images[sourceIndex]\n\n    const URL = self.URL || self.webkitURL\n\n    let sourceURI = sourceDef.uri || ''\n    let isObjectURL = false\n\n    if (sourceDef.bufferView !== undefined) {\n      // Load binary image data from bufferView, if provided.\n\n      sourceURI = parser.getDependency('bufferView', sourceDef.bufferView).then(function (bufferView) {\n        isObjectURL = true\n        const blob = new Blob([bufferView], { type: sourceDef.mimeType })\n        sourceURI = URL.createObjectURL(blob)\n        return sourceURI\n      })\n    } else if (sourceDef.uri === undefined) {\n      throw new Error('THREE.GLTFLoader: Image ' + sourceIndex + ' is missing URI and bufferView')\n    }\n\n    const promise = Promise.resolve(sourceURI)\n      .then(function (sourceURI) {\n        return new Promise(function (resolve, reject) {\n          let onLoad = resolve\n\n          if (loader.isImageBitmapLoader === true) {\n            onLoad = function (imageBitmap) {\n              const texture = new Texture(imageBitmap)\n              texture.needsUpdate = true\n\n              resolve(texture)\n            }\n          }\n\n          loader.load(LoaderUtils.resolveURL(sourceURI, options.path), onLoad, undefined, reject)\n        })\n      })\n      .then(function (texture) {\n        // Clean up resources and configure Texture.\n\n        if (isObjectURL === true) {\n          URL.revokeObjectURL(sourceURI)\n        }\n\n        assignExtrasToUserData(texture, sourceDef)\n\n        texture.userData.mimeType = sourceDef.mimeType || getImageURIMimeType(sourceDef.uri)\n\n        return texture\n      })\n      .catch(function (error) {\n        console.error(\"THREE.GLTFLoader: Couldn't load texture\", sourceURI)\n        throw error\n      })\n\n    this.sourceCache[sourceIndex] = promise\n    return promise\n  }\n\n  /**\n   * Asynchronously assigns a texture to the given material parameters.\n   * @param {Object} materialParams\n   * @param {string} mapName\n   * @param {Object} mapDef\n   * @return {Promise<Texture>}\n   */\n  assignTexture(materialParams, mapName, mapDef, colorSpace) {\n    const parser = this\n\n    return this.getDependency('texture', mapDef.index).then(function (texture) {\n      if (!texture) return null\n\n      if (mapDef.texCoord !== undefined && mapDef.texCoord > 0) {\n        texture = texture.clone()\n        texture.channel = mapDef.texCoord\n      }\n\n      if (parser.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM]) {\n        const transform =\n          mapDef.extensions !== undefined ? mapDef.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM] : undefined\n\n        if (transform) {\n          const gltfReference = parser.associations.get(texture)\n          texture = parser.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM].extendTexture(texture, transform)\n          parser.associations.set(texture, gltfReference)\n        }\n      }\n\n      if (colorSpace !== undefined) {\n        // Convert from legacy encoding to colorSpace\n        if (typeof colorSpace === 'number')\n          colorSpace = colorSpace === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace\n\n        // Set colorSpace if able, fallback to legacy encoding\n        if ('colorSpace' in texture) texture.colorSpace = colorSpace\n        else texture.encoding = colorSpace === SRGBColorSpace ? sRGBEncoding : LinearEncoding\n      }\n\n      materialParams[mapName] = texture\n\n      return texture\n    })\n  }\n\n  /**\n   * Assigns final material to a Mesh, Line, or Points instance. The instance\n   * already has a material (generated from the glTF material options alone)\n   * but reuse of the same glTF material may require multiple threejs materials\n   * to accommodate different primitive types, defines, etc. New materials will\n   * be created if necessary, and reused from a cache.\n   * @param  {Object3D} mesh Mesh, Line, or Points instance.\n   */\n  assignFinalMaterial(mesh) {\n    const geometry = mesh.geometry\n    let material = mesh.material\n\n    const useDerivativeTangents = geometry.attributes.tangent === undefined\n    const useVertexColors = geometry.attributes.color !== undefined\n    const useFlatShading = geometry.attributes.normal === undefined\n\n    if (mesh.isPoints) {\n      const cacheKey = 'PointsMaterial:' + material.uuid\n\n      let pointsMaterial = this.cache.get(cacheKey)\n\n      if (!pointsMaterial) {\n        pointsMaterial = new PointsMaterial()\n        Material.prototype.copy.call(pointsMaterial, material)\n        pointsMaterial.color.copy(material.color)\n        pointsMaterial.map = material.map\n        pointsMaterial.sizeAttenuation = false // glTF spec says points should be 1px\n\n        this.cache.add(cacheKey, pointsMaterial)\n      }\n\n      material = pointsMaterial\n    } else if (mesh.isLine) {\n      const cacheKey = 'LineBasicMaterial:' + material.uuid\n\n      let lineMaterial = this.cache.get(cacheKey)\n\n      if (!lineMaterial) {\n        lineMaterial = new LineBasicMaterial()\n        Material.prototype.copy.call(lineMaterial, material)\n        lineMaterial.color.copy(material.color)\n        lineMaterial.map = material.map\n\n        this.cache.add(cacheKey, lineMaterial)\n      }\n\n      material = lineMaterial\n    }\n\n    // Clone the material if it will be modified\n    if (useDerivativeTangents || useVertexColors || useFlatShading) {\n      let cacheKey = 'ClonedMaterial:' + material.uuid + ':'\n\n      if (useDerivativeTangents) cacheKey += 'derivative-tangents:'\n      if (useVertexColors) cacheKey += 'vertex-colors:'\n      if (useFlatShading) cacheKey += 'flat-shading:'\n\n      let cachedMaterial = this.cache.get(cacheKey)\n\n      if (!cachedMaterial) {\n        cachedMaterial = material.clone()\n\n        if (useVertexColors) cachedMaterial.vertexColors = true\n        if (useFlatShading) cachedMaterial.flatShading = true\n\n        if (useDerivativeTangents) {\n          // https://github.com/mrdoob/three.js/issues/11438#issuecomment-507003995\n          if (cachedMaterial.normalScale) cachedMaterial.normalScale.y *= -1\n          if (cachedMaterial.clearcoatNormalScale) cachedMaterial.clearcoatNormalScale.y *= -1\n        }\n\n        this.cache.add(cacheKey, cachedMaterial)\n\n        this.associations.set(cachedMaterial, this.associations.get(material))\n      }\n\n      material = cachedMaterial\n    }\n\n    mesh.material = material\n  }\n\n  getMaterialType(/* materialIndex */) {\n    return MeshStandardMaterial\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#materials\n   * @param {number} materialIndex\n   * @return {Promise<Material>}\n   */\n  loadMaterial(materialIndex) {\n    const parser = this\n    const json = this.json\n    const extensions = this.extensions\n    const materialDef = json.materials[materialIndex]\n\n    let materialType\n    const materialParams = {}\n    const materialExtensions = materialDef.extensions || {}\n\n    const pending = []\n\n    if (materialExtensions[EXTENSIONS.KHR_MATERIALS_UNLIT]) {\n      const kmuExtension = extensions[EXTENSIONS.KHR_MATERIALS_UNLIT]\n      materialType = kmuExtension.getMaterialType()\n      pending.push(kmuExtension.extendParams(materialParams, materialDef, parser))\n    } else {\n      // Specification:\n      // https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#metallic-roughness-material\n\n      const metallicRoughness = materialDef.pbrMetallicRoughness || {}\n\n      materialParams.color = new Color(1.0, 1.0, 1.0)\n      materialParams.opacity = 1.0\n\n      if (Array.isArray(metallicRoughness.baseColorFactor)) {\n        const array = metallicRoughness.baseColorFactor\n\n        materialParams.color.setRGB(array[0], array[1], array[2], LinearSRGBColorSpace)\n        materialParams.opacity = array[3]\n      }\n\n      if (metallicRoughness.baseColorTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace))\n      }\n\n      materialParams.metalness = metallicRoughness.metallicFactor !== undefined ? metallicRoughness.metallicFactor : 1.0\n      materialParams.roughness =\n        metallicRoughness.roughnessFactor !== undefined ? metallicRoughness.roughnessFactor : 1.0\n\n      if (metallicRoughness.metallicRoughnessTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'metalnessMap', metallicRoughness.metallicRoughnessTexture))\n        pending.push(parser.assignTexture(materialParams, 'roughnessMap', metallicRoughness.metallicRoughnessTexture))\n      }\n\n      materialType = this._invokeOne(function (ext) {\n        return ext.getMaterialType && ext.getMaterialType(materialIndex)\n      })\n\n      pending.push(\n        Promise.all(\n          this._invokeAll(function (ext) {\n            return ext.extendMaterialParams && ext.extendMaterialParams(materialIndex, materialParams)\n          }),\n        ),\n      )\n    }\n\n    if (materialDef.doubleSided === true) {\n      materialParams.side = DoubleSide\n    }\n\n    const alphaMode = materialDef.alphaMode || ALPHA_MODES.OPAQUE\n\n    if (alphaMode === ALPHA_MODES.BLEND) {\n      materialParams.transparent = true\n\n      // See: https://github.com/mrdoob/three.js/issues/17706\n      materialParams.depthWrite = false\n    } else {\n      materialParams.transparent = false\n\n      if (alphaMode === ALPHA_MODES.MASK) {\n        materialParams.alphaTest = materialDef.alphaCutoff !== undefined ? materialDef.alphaCutoff : 0.5\n      }\n    }\n\n    if (materialDef.normalTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'normalMap', materialDef.normalTexture))\n\n      materialParams.normalScale = new Vector2(1, 1)\n\n      if (materialDef.normalTexture.scale !== undefined) {\n        const scale = materialDef.normalTexture.scale\n\n        materialParams.normalScale.set(scale, scale)\n      }\n    }\n\n    if (materialDef.occlusionTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'aoMap', materialDef.occlusionTexture))\n\n      if (materialDef.occlusionTexture.strength !== undefined) {\n        materialParams.aoMapIntensity = materialDef.occlusionTexture.strength\n      }\n    }\n\n    if (materialDef.emissiveFactor !== undefined && materialType !== MeshBasicMaterial) {\n      const emissiveFactor = materialDef.emissiveFactor\n      materialParams.emissive = new Color().setRGB(\n        emissiveFactor[0],\n        emissiveFactor[1],\n        emissiveFactor[2],\n        LinearSRGBColorSpace,\n      )\n    }\n\n    if (materialDef.emissiveTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'emissiveMap', materialDef.emissiveTexture, SRGBColorSpace))\n    }\n\n    return Promise.all(pending).then(function () {\n      const material = new materialType(materialParams)\n\n      if (materialDef.name) material.name = materialDef.name\n\n      assignExtrasToUserData(material, materialDef)\n\n      parser.associations.set(material, { materials: materialIndex })\n\n      if (materialDef.extensions) addUnknownExtensionsToUserData(extensions, material, materialDef)\n\n      return material\n    })\n  }\n\n  /** When Object3D instances are targeted by animation, they need unique names. */\n  createUniqueName(originalName) {\n    const sanitizedName = PropertyBinding.sanitizeNodeName(originalName || '')\n\n    if (sanitizedName in this.nodeNamesUsed) {\n      return sanitizedName + '_' + ++this.nodeNamesUsed[sanitizedName]\n    } else {\n      this.nodeNamesUsed[sanitizedName] = 0\n\n      return sanitizedName\n    }\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#geometry\n   *\n   * Creates BufferGeometries from primitives.\n   *\n   * @param {Array<GLTF.Primitive>} primitives\n   * @return {Promise<Array<BufferGeometry>>}\n   */\n  loadGeometries(primitives) {\n    const parser = this\n    const extensions = this.extensions\n    const cache = this.primitiveCache\n\n    function createDracoPrimitive(primitive) {\n      return extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION]\n        .decodePrimitive(primitive, parser)\n        .then(function (geometry) {\n          return addPrimitiveAttributes(geometry, primitive, parser)\n        })\n    }\n\n    const pending = []\n\n    for (let i = 0, il = primitives.length; i < il; i++) {\n      const primitive = primitives[i]\n      const cacheKey = createPrimitiveKey(primitive)\n\n      // See if we've already created this geometry\n      const cached = cache[cacheKey]\n\n      if (cached) {\n        // Use the cached geometry if it exists\n        pending.push(cached.promise)\n      } else {\n        let geometryPromise\n\n        if (primitive.extensions && primitive.extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION]) {\n          // Use DRACO geometry if available\n          geometryPromise = createDracoPrimitive(primitive)\n        } else {\n          // Otherwise create a new geometry\n          geometryPromise = addPrimitiveAttributes(new BufferGeometry(), primitive, parser)\n        }\n\n        // Cache this geometry\n        cache[cacheKey] = { primitive: primitive, promise: geometryPromise }\n\n        pending.push(geometryPromise)\n      }\n    }\n\n    return Promise.all(pending)\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#meshes\n   * @param {number} meshIndex\n   * @return {Promise<Group|Mesh|SkinnedMesh>}\n   */\n  loadMesh(meshIndex) {\n    const parser = this\n    const json = this.json\n    const extensions = this.extensions\n\n    const meshDef = json.meshes[meshIndex]\n    const primitives = meshDef.primitives\n\n    const pending = []\n\n    for (let i = 0, il = primitives.length; i < il; i++) {\n      const material =\n        primitives[i].material === undefined\n          ? createDefaultMaterial(this.cache)\n          : this.getDependency('material', primitives[i].material)\n\n      pending.push(material)\n    }\n\n    pending.push(parser.loadGeometries(primitives))\n\n    return Promise.all(pending).then(function (results) {\n      const materials = results.slice(0, results.length - 1)\n      const geometries = results[results.length - 1]\n\n      const meshes = []\n\n      for (let i = 0, il = geometries.length; i < il; i++) {\n        const geometry = geometries[i]\n        const primitive = primitives[i]\n\n        // 1. create Mesh\n\n        let mesh\n\n        const material = materials[i]\n\n        if (\n          primitive.mode === WEBGL_CONSTANTS.TRIANGLES ||\n          primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP ||\n          primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN ||\n          primitive.mode === undefined\n        ) {\n          // .isSkinnedMesh isn't in glTF spec. See ._markDefs()\n          mesh = meshDef.isSkinnedMesh === true ? new SkinnedMesh(geometry, material) : new Mesh(geometry, material)\n\n          if (mesh.isSkinnedMesh === true) {\n            // normalize skin weights to fix malformed assets (see #15319)\n            mesh.normalizeSkinWeights()\n          }\n\n          if (primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP) {\n            mesh.geometry = toTrianglesDrawMode(mesh.geometry, TriangleStripDrawMode)\n          } else if (primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN) {\n            mesh.geometry = toTrianglesDrawMode(mesh.geometry, TriangleFanDrawMode)\n          }\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINES) {\n          mesh = new LineSegments(geometry, material)\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINE_STRIP) {\n          mesh = new Line(geometry, material)\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINE_LOOP) {\n          mesh = new LineLoop(geometry, material)\n        } else if (primitive.mode === WEBGL_CONSTANTS.POINTS) {\n          mesh = new Points(geometry, material)\n        } else {\n          throw new Error('THREE.GLTFLoader: Primitive mode unsupported: ' + primitive.mode)\n        }\n\n        if (Object.keys(mesh.geometry.morphAttributes).length > 0) {\n          updateMorphTargets(mesh, meshDef)\n        }\n\n        mesh.name = parser.createUniqueName(meshDef.name || 'mesh_' + meshIndex)\n\n        assignExtrasToUserData(mesh, meshDef)\n\n        if (primitive.extensions) addUnknownExtensionsToUserData(extensions, mesh, primitive)\n\n        parser.assignFinalMaterial(mesh)\n\n        meshes.push(mesh)\n      }\n\n      for (let i = 0, il = meshes.length; i < il; i++) {\n        parser.associations.set(meshes[i], {\n          meshes: meshIndex,\n          primitives: i,\n        })\n      }\n\n      if (meshes.length === 1) {\n        if (meshDef.extensions) addUnknownExtensionsToUserData(extensions, meshes[0], meshDef)\n\n        return meshes[0]\n      }\n\n      const group = new Group()\n\n      if (meshDef.extensions) addUnknownExtensionsToUserData(extensions, group, meshDef)\n\n      parser.associations.set(group, { meshes: meshIndex })\n\n      for (let i = 0, il = meshes.length; i < il; i++) {\n        group.add(meshes[i])\n      }\n\n      return group\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#cameras\n   * @param {number} cameraIndex\n   * @return {Promise<THREE.Camera>}\n   */\n  loadCamera(cameraIndex) {\n    let camera\n    const cameraDef = this.json.cameras[cameraIndex]\n    const params = cameraDef[cameraDef.type]\n\n    if (!params) {\n      console.warn('THREE.GLTFLoader: Missing camera parameters.')\n      return\n    }\n\n    if (cameraDef.type === 'perspective') {\n      camera = new PerspectiveCamera(\n        MathUtils.radToDeg(params.yfov),\n        params.aspectRatio || 1,\n        params.znear || 1,\n        params.zfar || 2e6,\n      )\n    } else if (cameraDef.type === 'orthographic') {\n      camera = new OrthographicCamera(-params.xmag, params.xmag, params.ymag, -params.ymag, params.znear, params.zfar)\n    }\n\n    if (cameraDef.name) camera.name = this.createUniqueName(cameraDef.name)\n\n    assignExtrasToUserData(camera, cameraDef)\n\n    return Promise.resolve(camera)\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#skins\n   * @param {number} skinIndex\n   * @return {Promise<Skeleton>}\n   */\n  loadSkin(skinIndex) {\n    const skinDef = this.json.skins[skinIndex]\n\n    const pending = []\n\n    for (let i = 0, il = skinDef.joints.length; i < il; i++) {\n      pending.push(this._loadNodeShallow(skinDef.joints[i]))\n    }\n\n    if (skinDef.inverseBindMatrices !== undefined) {\n      pending.push(this.getDependency('accessor', skinDef.inverseBindMatrices))\n    } else {\n      pending.push(null)\n    }\n\n    return Promise.all(pending).then(function (results) {\n      const inverseBindMatrices = results.pop()\n      const jointNodes = results\n\n      // Note that bones (joint nodes) may or may not be in the\n      // scene graph at this time.\n\n      const bones = []\n      const boneInverses = []\n\n      for (let i = 0, il = jointNodes.length; i < il; i++) {\n        const jointNode = jointNodes[i]\n\n        if (jointNode) {\n          bones.push(jointNode)\n\n          const mat = new Matrix4()\n\n          if (inverseBindMatrices !== null) {\n            mat.fromArray(inverseBindMatrices.array, i * 16)\n          }\n\n          boneInverses.push(mat)\n        } else {\n          console.warn('THREE.GLTFLoader: Joint \"%s\" could not be found.', skinDef.joints[i])\n        }\n      }\n\n      return new Skeleton(bones, boneInverses)\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#animations\n   * @param {number} animationIndex\n   * @return {Promise<AnimationClip>}\n   */\n  loadAnimation(animationIndex) {\n    const json = this.json\n    const parser = this\n\n    const animationDef = json.animations[animationIndex]\n    const animationName = animationDef.name ? animationDef.name : 'animation_' + animationIndex\n\n    const pendingNodes = []\n    const pendingInputAccessors = []\n    const pendingOutputAccessors = []\n    const pendingSamplers = []\n    const pendingTargets = []\n\n    for (let i = 0, il = animationDef.channels.length; i < il; i++) {\n      const channel = animationDef.channels[i]\n      const sampler = animationDef.samplers[channel.sampler]\n      const target = channel.target\n      const name = target.node\n      const input = animationDef.parameters !== undefined ? animationDef.parameters[sampler.input] : sampler.input\n      const output = animationDef.parameters !== undefined ? animationDef.parameters[sampler.output] : sampler.output\n\n      if (target.node === undefined) continue\n\n      pendingNodes.push(this.getDependency('node', name))\n      pendingInputAccessors.push(this.getDependency('accessor', input))\n      pendingOutputAccessors.push(this.getDependency('accessor', output))\n      pendingSamplers.push(sampler)\n      pendingTargets.push(target)\n    }\n\n    return Promise.all([\n      Promise.all(pendingNodes),\n      Promise.all(pendingInputAccessors),\n      Promise.all(pendingOutputAccessors),\n      Promise.all(pendingSamplers),\n      Promise.all(pendingTargets),\n    ]).then(function (dependencies) {\n      const nodes = dependencies[0]\n      const inputAccessors = dependencies[1]\n      const outputAccessors = dependencies[2]\n      const samplers = dependencies[3]\n      const targets = dependencies[4]\n\n      const tracks = []\n\n      for (let i = 0, il = nodes.length; i < il; i++) {\n        const node = nodes[i]\n        const inputAccessor = inputAccessors[i]\n        const outputAccessor = outputAccessors[i]\n        const sampler = samplers[i]\n        const target = targets[i]\n\n        if (node === undefined) continue\n\n        if (node.updateMatrix) {\n          node.updateMatrix()\n        }\n\n        const createdTracks = parser._createAnimationTracks(node, inputAccessor, outputAccessor, sampler, target)\n\n        if (createdTracks) {\n          for (let k = 0; k < createdTracks.length; k++) {\n            tracks.push(createdTracks[k])\n          }\n        }\n      }\n\n      return new AnimationClip(animationName, undefined, tracks)\n    })\n  }\n\n  createNodeMesh(nodeIndex) {\n    const json = this.json\n    const parser = this\n    const nodeDef = json.nodes[nodeIndex]\n\n    if (nodeDef.mesh === undefined) return null\n\n    return parser.getDependency('mesh', nodeDef.mesh).then(function (mesh) {\n      const node = parser._getNodeRef(parser.meshCache, nodeDef.mesh, mesh)\n\n      // if weights are provided on the node, override weights on the mesh.\n      if (nodeDef.weights !== undefined) {\n        node.traverse(function (o) {\n          if (!o.isMesh) return\n\n          for (let i = 0, il = nodeDef.weights.length; i < il; i++) {\n            o.morphTargetInfluences[i] = nodeDef.weights[i]\n          }\n        })\n      }\n\n      return node\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#nodes-and-hierarchy\n   * @param {number} nodeIndex\n   * @return {Promise<Object3D>}\n   */\n  loadNode(nodeIndex) {\n    const json = this.json\n    const parser = this\n\n    const nodeDef = json.nodes[nodeIndex]\n\n    const nodePending = parser._loadNodeShallow(nodeIndex)\n\n    const childPending = []\n    const childrenDef = nodeDef.children || []\n\n    for (let i = 0, il = childrenDef.length; i < il; i++) {\n      childPending.push(parser.getDependency('node', childrenDef[i]))\n    }\n\n    const skeletonPending =\n      nodeDef.skin === undefined ? Promise.resolve(null) : parser.getDependency('skin', nodeDef.skin)\n\n    return Promise.all([nodePending, Promise.all(childPending), skeletonPending]).then(function (results) {\n      const node = results[0]\n      const children = results[1]\n      const skeleton = results[2]\n\n      if (skeleton !== null) {\n        // This full traverse should be fine because\n        // child glTF nodes have not been added to this node yet.\n        node.traverse(function (mesh) {\n          if (!mesh.isSkinnedMesh) return\n\n          mesh.bind(skeleton, _identityMatrix)\n        })\n      }\n\n      for (let i = 0, il = children.length; i < il; i++) {\n        node.add(children[i])\n      }\n\n      return node\n    })\n  }\n\n  // ._loadNodeShallow() parses a single node.\n  // skin and child nodes are created and added in .loadNode() (no '_' prefix).\n  _loadNodeShallow(nodeIndex) {\n    const json = this.json\n    const extensions = this.extensions\n    const parser = this\n\n    // This method is called from .loadNode() and .loadSkin().\n    // Cache a node to avoid duplication.\n\n    if (this.nodeCache[nodeIndex] !== undefined) {\n      return this.nodeCache[nodeIndex]\n    }\n\n    const nodeDef = json.nodes[nodeIndex]\n\n    // reserve node's name before its dependencies, so the root has the intended name.\n    const nodeName = nodeDef.name ? parser.createUniqueName(nodeDef.name) : ''\n\n    const pending = []\n\n    const meshPromise = parser._invokeOne(function (ext) {\n      return ext.createNodeMesh && ext.createNodeMesh(nodeIndex)\n    })\n\n    if (meshPromise) {\n      pending.push(meshPromise)\n    }\n\n    if (nodeDef.camera !== undefined) {\n      pending.push(\n        parser.getDependency('camera', nodeDef.camera).then(function (camera) {\n          return parser._getNodeRef(parser.cameraCache, nodeDef.camera, camera)\n        }),\n      )\n    }\n\n    parser\n      ._invokeAll(function (ext) {\n        return ext.createNodeAttachment && ext.createNodeAttachment(nodeIndex)\n      })\n      .forEach(function (promise) {\n        pending.push(promise)\n      })\n\n    this.nodeCache[nodeIndex] = Promise.all(pending).then(function (objects) {\n      let node\n\n      // .isBone isn't in glTF spec. See ._markDefs\n      if (nodeDef.isBone === true) {\n        node = new Bone()\n      } else if (objects.length > 1) {\n        node = new Group()\n      } else if (objects.length === 1) {\n        node = objects[0]\n      } else {\n        node = new Object3D()\n      }\n\n      if (node !== objects[0]) {\n        for (let i = 0, il = objects.length; i < il; i++) {\n          node.add(objects[i])\n        }\n      }\n\n      if (nodeDef.name) {\n        node.userData.name = nodeDef.name\n        node.name = nodeName\n      }\n\n      assignExtrasToUserData(node, nodeDef)\n\n      if (nodeDef.extensions) addUnknownExtensionsToUserData(extensions, node, nodeDef)\n\n      if (nodeDef.matrix !== undefined) {\n        const matrix = new Matrix4()\n        matrix.fromArray(nodeDef.matrix)\n        node.applyMatrix4(matrix)\n      } else {\n        if (nodeDef.translation !== undefined) {\n          node.position.fromArray(nodeDef.translation)\n        }\n\n        if (nodeDef.rotation !== undefined) {\n          node.quaternion.fromArray(nodeDef.rotation)\n        }\n\n        if (nodeDef.scale !== undefined) {\n          node.scale.fromArray(nodeDef.scale)\n        }\n      }\n\n      if (!parser.associations.has(node)) {\n        parser.associations.set(node, {})\n      }\n\n      parser.associations.get(node).nodes = nodeIndex\n\n      return node\n    })\n\n    return this.nodeCache[nodeIndex]\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#scenes\n   * @param {number} sceneIndex\n   * @return {Promise<Group>}\n   */\n  loadScene(sceneIndex) {\n    const extensions = this.extensions\n    const sceneDef = this.json.scenes[sceneIndex]\n    const parser = this\n\n    // Loader returns Group, not Scene.\n    // See: https://github.com/mrdoob/three.js/issues/18342#issuecomment-578981172\n    const scene = new Group()\n    if (sceneDef.name) scene.name = parser.createUniqueName(sceneDef.name)\n\n    assignExtrasToUserData(scene, sceneDef)\n\n    if (sceneDef.extensions) addUnknownExtensionsToUserData(extensions, scene, sceneDef)\n\n    const nodeIds = sceneDef.nodes || []\n\n    const pending = []\n\n    for (let i = 0, il = nodeIds.length; i < il; i++) {\n      pending.push(parser.getDependency('node', nodeIds[i]))\n    }\n\n    return Promise.all(pending).then(function (nodes) {\n      for (let i = 0, il = nodes.length; i < il; i++) {\n        scene.add(nodes[i])\n      }\n\n      // Removes dangling associations, associations that reference a node that\n      // didn't make it into the scene.\n      const reduceAssociations = (node) => {\n        const reducedAssociations = new Map()\n\n        for (const [key, value] of parser.associations) {\n          if (key instanceof Material || key instanceof Texture) {\n            reducedAssociations.set(key, value)\n          }\n        }\n\n        node.traverse((node) => {\n          const mappings = parser.associations.get(node)\n\n          if (mappings != null) {\n            reducedAssociations.set(node, mappings)\n          }\n        })\n\n        return reducedAssociations\n      }\n\n      parser.associations = reduceAssociations(scene)\n\n      return scene\n    })\n  }\n\n  _createAnimationTracks(node, inputAccessor, outputAccessor, sampler, target) {\n    const tracks = []\n\n    const targetName = node.name ? node.name : node.uuid\n    const targetNames = []\n\n    if (PATH_PROPERTIES[target.path] === PATH_PROPERTIES.weights) {\n      node.traverse(function (object) {\n        if (object.morphTargetInfluences) {\n          targetNames.push(object.name ? object.name : object.uuid)\n        }\n      })\n    } else {\n      targetNames.push(targetName)\n    }\n\n    let TypedKeyframeTrack\n\n    switch (PATH_PROPERTIES[target.path]) {\n      case PATH_PROPERTIES.weights:\n        TypedKeyframeTrack = NumberKeyframeTrack\n        break\n\n      case PATH_PROPERTIES.rotation:\n        TypedKeyframeTrack = QuaternionKeyframeTrack\n        break\n\n      case PATH_PROPERTIES.position:\n      case PATH_PROPERTIES.scale:\n        TypedKeyframeTrack = VectorKeyframeTrack\n        break\n\n      default:\n        switch (outputAccessor.itemSize) {\n          case 1:\n            TypedKeyframeTrack = NumberKeyframeTrack\n            break\n          case 2:\n          case 3:\n          default:\n            TypedKeyframeTrack = VectorKeyframeTrack\n            break\n        }\n\n        break\n    }\n\n    const interpolation = sampler.interpolation !== undefined ? INTERPOLATION[sampler.interpolation] : InterpolateLinear\n\n    const outputArray = this._getArrayFromAccessor(outputAccessor)\n\n    for (let j = 0, jl = targetNames.length; j < jl; j++) {\n      const track = new TypedKeyframeTrack(\n        targetNames[j] + '.' + PATH_PROPERTIES[target.path],\n        inputAccessor.array,\n        outputArray,\n        interpolation,\n      )\n\n      // Override interpolation with custom factory method.\n      if (sampler.interpolation === 'CUBICSPLINE') {\n        this._createCubicSplineTrackInterpolant(track)\n      }\n\n      tracks.push(track)\n    }\n\n    return tracks\n  }\n\n  _getArrayFromAccessor(accessor) {\n    let outputArray = accessor.array\n\n    if (accessor.normalized) {\n      const scale = getNormalizedComponentScale(outputArray.constructor)\n      const scaled = new Float32Array(outputArray.length)\n\n      for (let j = 0, jl = outputArray.length; j < jl; j++) {\n        scaled[j] = outputArray[j] * scale\n      }\n\n      outputArray = scaled\n    }\n\n    return outputArray\n  }\n\n  _createCubicSplineTrackInterpolant(track) {\n    track.createInterpolant = function InterpolantFactoryMethodGLTFCubicSpline(result) {\n      // A CUBICSPLINE keyframe in glTF has three output values for each input value,\n      // representing inTangent, splineVertex, and outTangent. As a result, track.getValueSize()\n      // must be divided by three to get the interpolant's sampleSize argument.\n\n      const interpolantType =\n        this instanceof QuaternionKeyframeTrack ? GLTFCubicSplineQuaternionInterpolant : GLTFCubicSplineInterpolant\n\n      return new interpolantType(this.times, this.values, this.getValueSize() / 3, result)\n    }\n\n    // Mark as CUBICSPLINE. `track.getInterpolation()` doesn't support custom interpolants.\n    track.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline = true\n  }\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n */\nfunction computeBounds(geometry, primitiveDef, parser) {\n  const attributes = primitiveDef.attributes\n\n  const box = new Box3()\n\n  if (attributes.POSITION !== undefined) {\n    const accessor = parser.json.accessors[attributes.POSITION]\n\n    const min = accessor.min\n    const max = accessor.max\n\n    // glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n    if (min !== undefined && max !== undefined) {\n      box.set(new Vector3(min[0], min[1], min[2]), new Vector3(max[0], max[1], max[2]))\n\n      if (accessor.normalized) {\n        const boxScale = getNormalizedComponentScale(WEBGL_COMPONENT_TYPES[accessor.componentType])\n        box.min.multiplyScalar(boxScale)\n        box.max.multiplyScalar(boxScale)\n      }\n    } else {\n      console.warn('THREE.GLTFLoader: Missing min/max properties for accessor POSITION.')\n\n      return\n    }\n  } else {\n    return\n  }\n\n  const targets = primitiveDef.targets\n\n  if (targets !== undefined) {\n    const maxDisplacement = new Vector3()\n    const vector = new Vector3()\n\n    for (let i = 0, il = targets.length; i < il; i++) {\n      const target = targets[i]\n\n      if (target.POSITION !== undefined) {\n        const accessor = parser.json.accessors[target.POSITION]\n        const min = accessor.min\n        const max = accessor.max\n\n        // glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n        if (min !== undefined && max !== undefined) {\n          // we need to get max of absolute components because target weight is [-1,1]\n          vector.setX(Math.max(Math.abs(min[0]), Math.abs(max[0])))\n          vector.setY(Math.max(Math.abs(min[1]), Math.abs(max[1])))\n          vector.setZ(Math.max(Math.abs(min[2]), Math.abs(max[2])))\n\n          if (accessor.normalized) {\n            const boxScale = getNormalizedComponentScale(WEBGL_COMPONENT_TYPES[accessor.componentType])\n            vector.multiplyScalar(boxScale)\n          }\n\n          // Note: this assumes that the sum of all weights is at most 1. This isn't quite correct - it's more conservative\n          // to assume that each target can have a max weight of 1. However, for some use cases - notably, when morph targets\n          // are used to implement key-frame animations and as such only two are active at a time - this results in very large\n          // boxes. So for now we make a box that's sometimes a touch too small but is hopefully mostly of reasonable size.\n          maxDisplacement.max(vector)\n        } else {\n          console.warn('THREE.GLTFLoader: Missing min/max properties for accessor POSITION.')\n        }\n      }\n    }\n\n    // As per comment above this box isn't conservative, but has a reasonable size for a very large number of morph targets.\n    box.expandByVector(maxDisplacement)\n  }\n\n  geometry.boundingBox = box\n\n  const sphere = new Sphere()\n\n  box.getCenter(sphere.center)\n  sphere.radius = box.min.distanceTo(box.max) / 2\n\n  geometry.boundingSphere = sphere\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addPrimitiveAttributes(geometry, primitiveDef, parser) {\n  const attributes = primitiveDef.attributes\n\n  const pending = []\n\n  function assignAttributeAccessor(accessorIndex, attributeName) {\n    return parser.getDependency('accessor', accessorIndex).then(function (accessor) {\n      geometry.setAttribute(attributeName, accessor)\n    })\n  }\n\n  for (const gltfAttributeName in attributes) {\n    const threeAttributeName = ATTRIBUTES[gltfAttributeName] || gltfAttributeName.toLowerCase()\n\n    // Skip attributes already provided by e.g. Draco extension.\n    if (threeAttributeName in geometry.attributes) continue\n\n    pending.push(assignAttributeAccessor(attributes[gltfAttributeName], threeAttributeName))\n  }\n\n  if (primitiveDef.indices !== undefined && !geometry.index) {\n    const accessor = parser.getDependency('accessor', primitiveDef.indices).then(function (accessor) {\n      geometry.setIndex(accessor)\n    })\n\n    pending.push(accessor)\n  }\n\n  assignExtrasToUserData(geometry, primitiveDef)\n\n  computeBounds(geometry, primitiveDef, parser)\n\n  return Promise.all(pending).then(function () {\n    return primitiveDef.targets !== undefined ? addMorphTargets(geometry, primitiveDef.targets, parser) : geometry\n  })\n}\n\nexport { GLTFLoader }\n"], "names": ["self", "res", "sourceURI", "node", "accessor"], "mappings": ";;;;AAoEA,MAAM,iBAAiB;AACvB,MAAM,uBAAuB;AAC7B,MAAM,eAAe;AACrB,MAAM,iBAAiB;AAEvB,MAAM,mBAAmB,OAAO;AAAA,EAC9B,YAAY,SAAS;AACnB,UAAM,OAAO;AAEb,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AAEtB,SAAK,kBAAkB,CAAE;AAEzB,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,gCAAgC,MAAM;AAAA,IACvD,CAAK;AAED,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,iCAAiC,MAAM;AAAA,IACxD,CAAK;AAED,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,2BAA2B,MAAM;AAAA,IAClD,CAAK;AAED,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,yBAAyB,MAAM;AAAA,IAChD,CAAK;AAED,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,yBAAyB,MAAM;AAAA,IAChD,CAAK;AAED,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,4BAA4B,MAAM;AAAA,IACnD,CAAK;AAED,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,mCAAmC,MAAM;AAAA,IAC1D,CAAK;AAED,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,6BAA6B,MAAM;AAAA,IACpD,CAAK;AAED,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,0BAA0B,MAAM;AAAA,IACjD,CAAK;AAED,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,uCAAuC,MAAM;AAAA,IAC9D,CAAK;AAED,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,+BAA+B,MAAM;AAAA,IACtD,CAAK;AAED,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,kCAAkC,MAAM;AAAA,IACzD,CAAK;AAED,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,iCAAiC,MAAM;AAAA,IACxD,CAAK;AAED,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,2BAA2B,MAAM;AAAA,IAClD,CAAK;AAED,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,oBAAoB,MAAM;AAAA,IAC3C,CAAK;AAED,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,uBAAuB,MAAM;AAAA,IAC9C,CAAK;AAED,SAAK,SAAS,SAAU,QAAQ;AAC9B,aAAO,IAAI,sBAAsB,MAAM;AAAA,IAC7C,CAAK;AAAA,EACF;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AAEd,QAAI;AAEJ,QAAI,KAAK,iBAAiB,IAAI;AAC5B,qBAAe,KAAK;AAAA,IAC1B,WAAe,KAAK,SAAS,IAAI;AAM3B,YAAM,cAAc,YAAY,eAAe,GAAG;AAClD,qBAAe,YAAY,WAAW,aAAa,KAAK,IAAI;AAAA,IAClE,OAAW;AACL,qBAAe,YAAY,eAAe,GAAG;AAAA,IAC9C;AAKD,SAAK,QAAQ,UAAU,GAAG;AAE1B,UAAM,WAAW,SAAU,GAAG;AAC5B,UAAI,SAAS;AACX,gBAAQ,CAAC;AAAA,MACjB,OAAa;AACL,gBAAQ,MAAM,CAAC;AAAA,MAChB;AAED,YAAM,QAAQ,UAAU,GAAG;AAC3B,YAAM,QAAQ,QAAQ,GAAG;AAAA,IAC1B;AAED,UAAM,SAAS,IAAI,WAAW,KAAK,OAAO;AAE1C,WAAO,QAAQ,KAAK,IAAI;AACxB,WAAO,gBAAgB,aAAa;AACpC,WAAO,iBAAiB,KAAK,aAAa;AAC1C,WAAO,mBAAmB,KAAK,eAAe;AAE9C,WAAO;AAAA,MACL;AAAA,MACA,SAAU,MAAM;AACd,YAAI;AACF,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA,SAAU,MAAM;AACd,qBAAO,IAAI;AAEX,oBAAM,QAAQ,QAAQ,GAAG;AAAA,YAC1B;AAAA,YACD;AAAA,UACD;AAAA,QACF,SAAQ,GAAP;AACA,mBAAS,CAAC;AAAA,QACX;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,eAAe,aAAa;AAC1B,SAAK,cAAc;AACnB,WAAO;AAAA,EACR;AAAA,EAED,eAAe;AACb,UAAM,IAAI,MAAM,kGAAkG;AAAA,EACnH;AAAA,EAED,cAAc,YAAY;AACxB,SAAK,aAAa;AAClB,WAAO;AAAA,EACR;AAAA,EAED,kBAAkB,gBAAgB;AAChC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACR;AAAA,EAED,SAAS,UAAU;AACjB,QAAI,KAAK,gBAAgB,QAAQ,QAAQ,MAAM,IAAI;AACjD,WAAK,gBAAgB,KAAK,QAAQ;AAAA,IACnC;AAED,WAAO;AAAA,EACR;AAAA,EAED,WAAW,UAAU;AACnB,QAAI,KAAK,gBAAgB,QAAQ,QAAQ,MAAM,IAAI;AACjD,WAAK,gBAAgB,OAAO,KAAK,gBAAgB,QAAQ,QAAQ,GAAG,CAAC;AAAA,IACtE;AAED,WAAO;AAAA,EACR;AAAA,EAED,MAAM,MAAM,MAAM,QAAQ,SAAS;AACjC,QAAI;AACJ,UAAM,aAAa,CAAE;AACrB,UAAM,UAAU,CAAE;AAElB,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,KAAK,MAAM,IAAI;AAAA,IAC5B,WAAe,gBAAgB,aAAa;AACtC,YAAM,QAAQ,WAAW,IAAI,WAAW,KAAK,MAAM,GAAG,CAAC,CAAC,CAAC;AAEzD,UAAI,UAAU,+BAA+B;AAC3C,YAAI;AACF,qBAAW,WAAW,eAAe,IAAI,IAAI,oBAAoB,IAAI;AAAA,QACtE,SAAQ,OAAP;AACA,cAAI;AAAS,oBAAQ,KAAK;AAC1B;AAAA,QACD;AAED,eAAO,KAAK,MAAM,WAAW,WAAW,eAAe,EAAE,OAAO;AAAA,MACxE,OAAa;AACL,eAAO,KAAK,MAAM,WAAW,IAAI,WAAW,IAAI,CAAC,CAAC;AAAA,MACnD;AAAA,IACP,OAAW;AACL,aAAO;AAAA,IACR;AAED,QAAI,KAAK,UAAU,UAAa,KAAK,MAAM,QAAQ,CAAC,IAAI,GAAG;AACzD,UAAI;AAAS,gBAAQ,IAAI,MAAM,yEAAyE,CAAC;AACzG;AAAA,IACD;AAED,UAAM,SAAS,IAAI,WAAW,MAAM;AAAA,MAClC,MAAM,QAAQ,KAAK,gBAAgB;AAAA,MACnC,aAAa,KAAK;AAAA,MAClB,eAAe,KAAK;AAAA,MACpB,SAAS,KAAK;AAAA,MACd,YAAY,KAAK;AAAA,MACjB,gBAAgB,KAAK;AAAA,IAC3B,CAAK;AAED,WAAO,WAAW,iBAAiB,KAAK,aAAa;AAErD,aAAS,IAAI,GAAG,IAAI,KAAK,gBAAgB,QAAQ,KAAK;AACpD,YAAM,SAAS,KAAK,gBAAgB,CAAC,EAAE,MAAM;AAE7C,UAAI,CAAC,OAAO;AAAM,gBAAQ,MAAM,sDAAsD;AAEtF,cAAQ,OAAO,IAAI,IAAI;AAMvB,iBAAW,OAAO,IAAI,IAAI;AAAA,IAC3B;AAED,QAAI,KAAK,gBAAgB;AACvB,eAAS,IAAI,GAAG,IAAI,KAAK,eAAe,QAAQ,EAAE,GAAG;AACnD,cAAM,gBAAgB,KAAK,eAAe,CAAC;AAC3C,cAAM,qBAAqB,KAAK,sBAAsB,CAAE;AAExD,gBAAQ,eAAa;AAAA,UACnB,KAAK,WAAW;AACd,uBAAW,aAAa,IAAI,IAAI,4BAA6B;AAC7D;AAAA,UAEF,KAAK,WAAW;AACd,uBAAW,aAAa,IAAI,IAAI,kCAAkC,MAAM,KAAK,WAAW;AACxF;AAAA,UAEF,KAAK,WAAW;AACd,uBAAW,aAAa,IAAI,IAAI,8BAA+B;AAC/D;AAAA,UAEF,KAAK,WAAW;AACd,uBAAW,aAAa,IAAI,IAAI,8BAA+B;AAC/D;AAAA,UAEF;AACE,gBAAI,mBAAmB,QAAQ,aAAa,KAAK,KAAK,QAAQ,aAAa,MAAM,QAAW;AAC1F,sBAAQ,KAAK,0CAA0C,gBAAgB,IAAI;AAAA,YAC5E;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAED,WAAO,cAAc,UAAU;AAC/B,WAAO,WAAW,OAAO;AACzB,WAAO,MAAM,QAAQ,OAAO;AAAA,EAC7B;AAAA,EAED,WAAW,MAAM,MAAM;AACrB,UAAM,QAAQ;AAEd,WAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,YAAM,MAAM,MAAM,MAAM,SAAS,MAAM;AAAA,IAC7C,CAAK;AAAA,EACF;AACH;AAIA,SAAS,eAAe;AACtB,MAAI,UAAU,CAAE;AAEhB,SAAO;AAAA,IACL,KAAK,SAAU,KAAK;AAClB,aAAO,QAAQ,GAAG;AAAA,IACnB;AAAA,IAED,KAAK,SAAU,KAAK,QAAQ;AAC1B,cAAQ,GAAG,IAAI;AAAA,IAChB;AAAA,IAED,QAAQ,SAAU,KAAK;AACrB,aAAO,QAAQ,GAAG;AAAA,IACnB;AAAA,IAED,WAAW,WAAY;AACrB,gBAAU,CAAE;AAAA,IACb;AAAA,EACF;AACH;AAMA,MAAM,aAAa;AAAA,EACjB,iBAAiB;AAAA,EACjB,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,iCAAiC;AAAA,EACjC,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAC3B;AAOA,MAAM,oBAAoB;AAAA,EACxB,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAGvB,SAAK,QAAQ,EAAE,MAAM,CAAA,GAAI,MAAM,CAAA,EAAI;AAAA,EACpC;AAAA,EAED,YAAY;AACV,UAAM,SAAS,KAAK;AACpB,UAAM,WAAW,KAAK,OAAO,KAAK,SAAS,CAAE;AAE7C,aAAS,YAAY,GAAG,aAAa,SAAS,QAAQ,YAAY,YAAY,aAAa;AACzF,YAAM,UAAU,SAAS,SAAS;AAElC,UAAI,QAAQ,cAAc,QAAQ,WAAW,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,IAAI,EAAE,UAAU,QAAW;AAC5G,eAAO,YAAY,KAAK,OAAO,QAAQ,WAAW,KAAK,IAAI,EAAE,KAAK;AAAA,MACnE;AAAA,IACF;AAAA,EACF;AAAA,EAED,WAAW,YAAY;AACrB,UAAM,SAAS,KAAK;AACpB,UAAM,WAAW,WAAW;AAC5B,QAAI,aAAa,OAAO,MAAM,IAAI,QAAQ;AAE1C,QAAI;AAAY,aAAO;AAEvB,UAAM,OAAO,OAAO;AACpB,UAAM,aAAc,KAAK,cAAc,KAAK,WAAW,KAAK,IAAI,KAAM,CAAE;AACxE,UAAM,YAAY,WAAW,UAAU,CAAE;AACzC,UAAM,WAAW,UAAU,UAAU;AACrC,QAAI;AAEJ,UAAM,QAAQ,IAAI,MAAM,QAAQ;AAEhC,QAAI,SAAS,UAAU;AACrB,YAAM,OAAO,SAAS,MAAM,CAAC,GAAG,SAAS,MAAM,CAAC,GAAG,SAAS,MAAM,CAAC,GAAG,oBAAoB;AAE5F,UAAM,QAAQ,SAAS,UAAU,SAAY,SAAS,QAAQ;AAE9D,YAAQ,SAAS,MAAI;AAAA,MACnB,KAAK;AACH,oBAAY,IAAI,iBAAiB,KAAK;AACtC,kBAAU,OAAO,SAAS,IAAI,GAAG,GAAG,EAAE;AACtC,kBAAU,IAAI,UAAU,MAAM;AAC9B;AAAA,MAEF,KAAK;AACH,oBAAY,IAAI,WAAW,KAAK;AAChC,kBAAU,WAAW;AACrB;AAAA,MAEF,KAAK;AACH,oBAAY,IAAI,UAAU,KAAK;AAC/B,kBAAU,WAAW;AAErB,iBAAS,OAAO,SAAS,QAAQ,CAAE;AACnC,iBAAS,KAAK,iBAAiB,SAAS,KAAK,mBAAmB,SAAY,SAAS,KAAK,iBAAiB;AAC3G,iBAAS,KAAK,iBACZ,SAAS,KAAK,mBAAmB,SAAY,SAAS,KAAK,iBAAiB,KAAK,KAAK;AACxF,kBAAU,QAAQ,SAAS,KAAK;AAChC,kBAAU,WAAW,IAAM,SAAS,KAAK,iBAAiB,SAAS,KAAK;AACxE,kBAAU,OAAO,SAAS,IAAI,GAAG,GAAG,EAAE;AACtC,kBAAU,IAAI,UAAU,MAAM;AAC9B;AAAA,MAEF;AACE,cAAM,IAAI,MAAM,8CAA8C,SAAS,IAAI;AAAA,IAC9E;AAID,cAAU,SAAS,IAAI,GAAG,GAAG,CAAC;AAE9B,cAAU,QAAQ;AAElB,2BAAuB,WAAW,QAAQ;AAE1C,QAAI,SAAS,cAAc;AAAW,gBAAU,YAAY,SAAS;AAErE,cAAU,OAAO,OAAO,iBAAiB,SAAS,QAAQ,WAAW,UAAU;AAE/E,iBAAa,QAAQ,QAAQ,SAAS;AAEtC,WAAO,MAAM,IAAI,UAAU,UAAU;AAErC,WAAO;AAAA,EACR;AAAA,EAED,cAAc,MAAM,OAAO;AACzB,QAAI,SAAS;AAAS;AAEtB,WAAO,KAAK,WAAW,KAAK;AAAA,EAC7B;AAAA,EAED,qBAAqB,WAAW;AAC9B,UAAMA,QAAO;AACb,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,OAAO;AACpB,UAAM,UAAU,KAAK,MAAM,SAAS;AACpC,UAAM,WAAY,QAAQ,cAAc,QAAQ,WAAW,KAAK,IAAI,KAAM,CAAE;AAC5E,UAAM,aAAa,SAAS;AAE5B,QAAI,eAAe;AAAW,aAAO;AAErC,WAAO,KAAK,WAAW,UAAU,EAAE,KAAK,SAAU,OAAO;AACvD,aAAO,OAAO,YAAYA,MAAK,OAAO,YAAY,KAAK;AAAA,IAC7D,CAAK;AAAA,EACF;AACH;AAOA,MAAM,4BAA4B;AAAA,EAChC,cAAc;AACZ,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA,EAED,kBAAkB;AAChB,WAAO;AAAA,EACR;AAAA,EAED,aAAa,gBAAgB,aAAa,QAAQ;AAChD,UAAM,UAAU,CAAE;AAElB,mBAAe,QAAQ,IAAI,MAAM,GAAK,GAAK,CAAG;AAC9C,mBAAe,UAAU;AAEzB,UAAM,oBAAoB,YAAY;AAEtC,QAAI,mBAAmB;AACrB,UAAI,MAAM,QAAQ,kBAAkB,eAAe,GAAG;AACpD,cAAM,QAAQ,kBAAkB;AAEhC,uBAAe,MAAM,OAAO,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,oBAAoB;AAC9E,uBAAe,UAAU,MAAM,CAAC;AAAA,MACjC;AAED,UAAI,kBAAkB,qBAAqB,QAAW;AACpD,gBAAQ,KAAK,OAAO,cAAc,gBAAgB,OAAO,kBAAkB,kBAAkB,cAAc,CAAC;AAAA,MAC7G;AAAA,IACF;AAED,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC3B;AACH;AAOA,MAAM,uCAAuC;AAAA,EAC3C,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA,EAED,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAS;AAAA,IACzB;AAED,UAAM,mBAAmB,YAAY,WAAW,KAAK,IAAI,EAAE;AAE3D,QAAI,qBAAqB,QAAW;AAClC,qBAAe,oBAAoB;AAAA,IACpC;AAED,WAAO,QAAQ,QAAS;AAAA,EACzB;AACH;AAOA,MAAM,gCAAgC;AAAA,EACpC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA,EAED,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI;AAAG,aAAO;AAE1E,WAAO;AAAA,EACR;AAAA,EAED,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAS;AAAA,IACzB;AAED,UAAM,UAAU,CAAE;AAElB,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAElD,QAAI,UAAU,oBAAoB,QAAW;AAC3C,qBAAe,YAAY,UAAU;AAAA,IACtC;AAED,QAAI,UAAU,qBAAqB,QAAW;AAC5C,cAAQ,KAAK,OAAO,cAAc,gBAAgB,gBAAgB,UAAU,gBAAgB,CAAC;AAAA,IAC9F;AAED,QAAI,UAAU,6BAA6B,QAAW;AACpD,qBAAe,qBAAqB,UAAU;AAAA,IAC/C;AAED,QAAI,UAAU,8BAA8B,QAAW;AACrD,cAAQ,KAAK,OAAO,cAAc,gBAAgB,yBAAyB,UAAU,yBAAyB,CAAC;AAAA,IAChH;AAED,QAAI,UAAU,2BAA2B,QAAW;AAClD,cAAQ,KAAK,OAAO,cAAc,gBAAgB,sBAAsB,UAAU,sBAAsB,CAAC;AAEzG,UAAI,UAAU,uBAAuB,UAAU,QAAW;AACxD,cAAM,QAAQ,UAAU,uBAAuB;AAE/C,uBAAe,uBAAuB,IAAI,QAAQ,OAAO,KAAK;AAAA,MAC/D;AAAA,IACF;AAED,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC3B;AACH;AAOA,MAAM,iCAAiC;AAAA,EACrC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA,EAED,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI;AAAG,aAAO;AAE1E,WAAO;AAAA,EACR;AAAA,EAED,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAS;AAAA,IACzB;AAED,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAElD,mBAAe,aAAa,UAAU,eAAe,SAAY,UAAU,aAAa;AAExF,WAAO,QAAQ,QAAS;AAAA,EACzB;AACH;AAOA,MAAM,kCAAkC;AAAA,EACtC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA,EAED,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI;AAAG,aAAO;AAE1E,WAAO;AAAA,EACR;AAAA,EAED,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAS;AAAA,IACzB;AAED,UAAM,UAAU,CAAE;AAElB,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAElD,QAAI,UAAU,sBAAsB,QAAW;AAC7C,qBAAe,cAAc,UAAU;AAAA,IACxC;AAED,QAAI,UAAU,uBAAuB,QAAW;AAC9C,cAAQ,KAAK,OAAO,cAAc,gBAAgB,kBAAkB,UAAU,kBAAkB,CAAC;AAAA,IAClG;AAED,QAAI,UAAU,mBAAmB,QAAW;AAC1C,qBAAe,iBAAiB,UAAU;AAAA,IAC3C;AAED,QAAI,eAAe,8BAA8B,QAAW;AAC1D,qBAAe,4BAA4B,CAAC,KAAK,GAAG;AAAA,IACrD;AAED,QAAI,UAAU,gCAAgC,QAAW;AACvD,qBAAe,0BAA0B,CAAC,IAAI,UAAU;AAAA,IACzD;AAED,QAAI,UAAU,gCAAgC,QAAW;AACvD,qBAAe,0BAA0B,CAAC,IAAI,UAAU;AAAA,IACzD;AAED,QAAI,UAAU,gCAAgC,QAAW;AACvD,cAAQ;AAAA,QACN,OAAO,cAAc,gBAAgB,2BAA2B,UAAU,2BAA2B;AAAA,MACtG;AAAA,IACF;AAED,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC3B;AACH;AAOA,MAAM,4BAA4B;AAAA,EAChC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA,EAED,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI;AAAG,aAAO;AAE1E,WAAO;AAAA,EACR;AAAA,EAED,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAS;AAAA,IACzB;AAED,UAAM,UAAU,CAAE;AAElB,mBAAe,aAAa,IAAI,MAAM,GAAG,GAAG,CAAC;AAC7C,mBAAe,iBAAiB;AAChC,mBAAe,QAAQ;AAEvB,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAElD,QAAI,UAAU,qBAAqB,QAAW;AAC5C,YAAM,cAAc,UAAU;AAC9B,qBAAe,WAAW,OAAO,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,oBAAoB;AAAA,IACtG;AAED,QAAI,UAAU,yBAAyB,QAAW;AAChD,qBAAe,iBAAiB,UAAU;AAAA,IAC3C;AAED,QAAI,UAAU,sBAAsB,QAAW;AAC7C,cAAQ,KAAK,OAAO,cAAc,gBAAgB,iBAAiB,UAAU,mBAAmB,cAAc,CAAC;AAAA,IAChH;AAED,QAAI,UAAU,0BAA0B,QAAW;AACjD,cAAQ,KAAK,OAAO,cAAc,gBAAgB,qBAAqB,UAAU,qBAAqB,CAAC;AAAA,IACxG;AAED,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC3B;AACH;AAQA,MAAM,mCAAmC;AAAA,EACvC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA,EAED,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI;AAAG,aAAO;AAE1E,WAAO;AAAA,EACR;AAAA,EAED,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAS;AAAA,IACzB;AAED,UAAM,UAAU,CAAE;AAElB,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAElD,QAAI,UAAU,uBAAuB,QAAW;AAC9C,qBAAe,eAAe,UAAU;AAAA,IACzC;AAED,QAAI,UAAU,wBAAwB,QAAW;AAC/C,cAAQ,KAAK,OAAO,cAAc,gBAAgB,mBAAmB,UAAU,mBAAmB,CAAC;AAAA,IACpG;AAED,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC3B;AACH;AAOA,MAAM,6BAA6B;AAAA,EACjC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA,EAED,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI;AAAG,aAAO;AAE1E,WAAO;AAAA,EACR;AAAA,EAED,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAS;AAAA,IACzB;AAED,UAAM,UAAU,CAAE;AAElB,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAElD,mBAAe,YAAY,UAAU,oBAAoB,SAAY,UAAU,kBAAkB;AAEjG,QAAI,UAAU,qBAAqB,QAAW;AAC5C,cAAQ,KAAK,OAAO,cAAc,gBAAgB,gBAAgB,UAAU,gBAAgB,CAAC;AAAA,IAC9F;AAED,mBAAe,sBAAsB,UAAU,uBAAuB;AAEtE,UAAM,aAAa,UAAU,oBAAoB,CAAC,GAAG,GAAG,CAAC;AACzD,mBAAe,mBAAmB,IAAI,MAAK,EAAG;AAAA,MAC5C,WAAW,CAAC;AAAA,MACZ,WAAW,CAAC;AAAA,MACZ,WAAW,CAAC;AAAA,MACZ;AAAA,IACD;AAED,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC3B;AACH;AAOA,MAAM,0BAA0B;AAAA,EAC9B,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA,EAED,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI;AAAG,aAAO;AAE1E,WAAO;AAAA,EACR;AAAA,EAED,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAS;AAAA,IACzB;AAED,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAElD,mBAAe,MAAM,UAAU,QAAQ,SAAY,UAAU,MAAM;AAEnE,WAAO,QAAQ,QAAS;AAAA,EACzB;AACH;AAOA,MAAM,+BAA+B;AAAA,EACnC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA,EAED,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI;AAAG,aAAO;AAE1E,WAAO;AAAA,EACR;AAAA,EAED,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAS;AAAA,IACzB;AAED,UAAM,UAAU,CAAE;AAElB,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAElD,mBAAe,oBAAoB,UAAU,mBAAmB,SAAY,UAAU,iBAAiB;AAEvG,QAAI,UAAU,oBAAoB,QAAW;AAC3C,cAAQ,KAAK,OAAO,cAAc,gBAAgB,wBAAwB,UAAU,eAAe,CAAC;AAAA,IACrG;AAED,UAAM,aAAa,UAAU,uBAAuB,CAAC,GAAG,GAAG,CAAC;AAC5D,mBAAe,gBAAgB,IAAI,MAAK,EAAG,OAAO,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,oBAAoB;AAEnH,QAAI,UAAU,yBAAyB,QAAW;AAChD,cAAQ;AAAA,QACN,OAAO,cAAc,gBAAgB,oBAAoB,UAAU,sBAAsB,cAAc;AAAA,MACxG;AAAA,IACF;AAED,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC3B;AACH;AAOA,MAAM,2BAA2B;AAAA,EAC/B,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA,EAED,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI;AAAG,aAAO;AAE1E,WAAO;AAAA,EACR;AAAA,EAED,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAS;AAAA,IACzB;AAED,UAAM,UAAU,CAAE;AAElB,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAElD,mBAAe,YAAY,UAAU,eAAe,SAAY,UAAU,aAAa;AAEvF,QAAI,UAAU,gBAAgB,QAAW;AACvC,cAAQ,KAAK,OAAO,cAAc,gBAAgB,WAAW,UAAU,WAAW,CAAC;AAAA,IACpF;AAED,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC3B;AACH;AAOA,MAAM,iCAAiC;AAAA,EACrC,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA,EAED,gBAAgB,eAAe;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI;AAAG,aAAO;AAE1E,WAAO;AAAA,EACR;AAAA,EAED,qBAAqB,eAAe,gBAAgB;AAClD,UAAM,SAAS,KAAK;AACpB,UAAM,cAAc,OAAO,KAAK,UAAU,aAAa;AAEvD,QAAI,CAAC,YAAY,cAAc,CAAC,YAAY,WAAW,KAAK,IAAI,GAAG;AACjE,aAAO,QAAQ,QAAS;AAAA,IACzB;AAED,UAAM,UAAU,CAAE;AAElB,UAAM,YAAY,YAAY,WAAW,KAAK,IAAI;AAElD,QAAI,UAAU,uBAAuB,QAAW;AAC9C,qBAAe,aAAa,UAAU;AAAA,IACvC;AAED,QAAI,UAAU,uBAAuB,QAAW;AAC9C,qBAAe,qBAAqB,UAAU;AAAA,IAC/C;AAED,QAAI,UAAU,sBAAsB,QAAW;AAC7C,cAAQ,KAAK,OAAO,cAAc,gBAAgB,iBAAiB,UAAU,iBAAiB,CAAC;AAAA,IAChG;AAED,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC3B;AACH;AAOA,MAAM,2BAA2B;AAAA,EAC/B,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA,EAED,YAAY,cAAc;AACxB,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,OAAO;AAEpB,UAAM,aAAa,KAAK,SAAS,YAAY;AAE7C,QAAI,CAAC,WAAW,cAAc,CAAC,WAAW,WAAW,KAAK,IAAI,GAAG;AAC/D,aAAO;AAAA,IACR;AAED,UAAM,YAAY,WAAW,WAAW,KAAK,IAAI;AACjD,UAAM,SAAS,OAAO,QAAQ;AAE9B,QAAI,CAAC,QAAQ;AACX,UAAI,KAAK,sBAAsB,KAAK,mBAAmB,QAAQ,KAAK,IAAI,KAAK,GAAG;AAC9E,cAAM,IAAI,MAAM,6EAA6E;AAAA,MACrG,OAAa;AAEL,eAAO;AAAA,MACR;AAAA,IACF;AAED,WAAO,OAAO,iBAAiB,cAAc,UAAU,QAAQ,MAAM;AAAA,EACtE;AACH;AAOA,MAAM,yBAAyB;AAAA,EAC7B,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AACvB,SAAK,cAAc;AAAA,EACpB;AAAA,EAED,YAAY,cAAc;AACxB,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,OAAO;AAEpB,UAAM,aAAa,KAAK,SAAS,YAAY;AAE7C,QAAI,CAAC,WAAW,cAAc,CAAC,WAAW,WAAW,IAAI,GAAG;AAC1D,aAAO;AAAA,IACR;AAED,UAAM,YAAY,WAAW,WAAW,IAAI;AAC5C,UAAM,SAAS,KAAK,OAAO,UAAU,MAAM;AAE3C,QAAI,SAAS,OAAO;AACpB,QAAI,OAAO,KAAK;AACd,YAAM,UAAU,OAAO,QAAQ,QAAQ,WAAW,OAAO,GAAG;AAC5D,UAAI,YAAY;AAAM,iBAAS;AAAA,IAChC;AAED,WAAO,KAAK,cAAa,EAAG,KAAK,SAAU,aAAa;AACtD,UAAI;AAAa,eAAO,OAAO,iBAAiB,cAAc,UAAU,QAAQ,MAAM;AAEtF,UAAI,KAAK,sBAAsB,KAAK,mBAAmB,QAAQ,IAAI,KAAK,GAAG;AACzE,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC5E;AAGD,aAAO,OAAO,YAAY,YAAY;AAAA,IAC5C,CAAK;AAAA,EACF;AAAA,EAED,gBAAgB;AACd,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,cAAc,IAAI,QAAQ,SAAU,SAAS;AAChD,cAAM,QAAQ,IAAI,MAAO;AAIzB,cAAM,MAAM;AAEZ,cAAM,SAAS,MAAM,UAAU,WAAY;AACzC,kBAAQ,MAAM,WAAW,CAAC;AAAA,QAC3B;AAAA,MACT,CAAO;AAAA,IACF;AAED,WAAO,KAAK;AAAA,EACb;AACH;AAOA,MAAM,yBAAyB;AAAA,EAC7B,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,WAAW;AACvB,SAAK,cAAc;AAAA,EACpB;AAAA,EAED,YAAY,cAAc;AACxB,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS,KAAK;AACpB,UAAM,OAAO,OAAO;AAEpB,UAAM,aAAa,KAAK,SAAS,YAAY;AAE7C,QAAI,CAAC,WAAW,cAAc,CAAC,WAAW,WAAW,IAAI,GAAG;AAC1D,aAAO;AAAA,IACR;AAED,UAAM,YAAY,WAAW,WAAW,IAAI;AAC5C,UAAM,SAAS,KAAK,OAAO,UAAU,MAAM;AAE3C,QAAI,SAAS,OAAO;AACpB,QAAI,OAAO,KAAK;AACd,YAAM,UAAU,OAAO,QAAQ,QAAQ,WAAW,OAAO,GAAG;AAC5D,UAAI,YAAY;AAAM,iBAAS;AAAA,IAChC;AAED,WAAO,KAAK,cAAa,EAAG,KAAK,SAAU,aAAa;AACtD,UAAI;AAAa,eAAO,OAAO,iBAAiB,cAAc,UAAU,QAAQ,MAAM;AAEtF,UAAI,KAAK,sBAAsB,KAAK,mBAAmB,QAAQ,IAAI,KAAK,GAAG;AACzE,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC5E;AAGD,aAAO,OAAO,YAAY,YAAY;AAAA,IAC5C,CAAK;AAAA,EACF;AAAA,EAED,gBAAgB;AACd,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,cAAc,IAAI,QAAQ,SAAU,SAAS;AAChD,cAAM,QAAQ,IAAI,MAAO;AAGzB,cAAM,MACJ;AACF,cAAM,SAAS,MAAM,UAAU,WAAY;AACzC,kBAAQ,MAAM,WAAW,CAAC;AAAA,QAC3B;AAAA,MACT,CAAO;AAAA,IACF;AAED,WAAO,KAAK;AAAA,EACb;AACH;AAOA,MAAM,uBAAuB;AAAA,EAC3B,YAAY,QAAQ;AAClB,SAAK,OAAO,WAAW;AACvB,SAAK,SAAS;AAAA,EACf;AAAA,EAED,eAAe,OAAO;AACpB,UAAM,OAAO,KAAK,OAAO;AACzB,UAAM,aAAa,KAAK,YAAY,KAAK;AAEzC,QAAI,WAAW,cAAc,WAAW,WAAW,KAAK,IAAI,GAAG;AAC7D,YAAM,eAAe,WAAW,WAAW,KAAK,IAAI;AAEpD,YAAM,SAAS,KAAK,OAAO,cAAc,UAAU,aAAa,MAAM;AACtE,YAAM,UAAU,KAAK,OAAO,QAAQ;AAEpC,UAAI,CAAC,WAAW,CAAC,QAAQ,WAAW;AAClC,YAAI,KAAK,sBAAsB,KAAK,mBAAmB,QAAQ,KAAK,IAAI,KAAK,GAAG;AAC9E,gBAAM,IAAI,MAAM,oFAAoF;AAAA,QAC9G,OAAe;AAEL,iBAAO;AAAA,QACR;AAAA,MACF;AAED,aAAO,OAAO,KAAK,SAAU,KAAK;AAChC,cAAM,aAAa,aAAa,cAAc;AAC9C,cAAM,aAAa,aAAa,cAAc;AAE9C,cAAM,QAAQ,aAAa;AAC3B,cAAM,SAAS,aAAa;AAE5B,cAAM,SAAS,IAAI,WAAW,KAAK,YAAY,UAAU;AAEzD,YAAI,QAAQ,uBAAuB;AACjC,iBAAO,QACJ,sBAAsB,OAAO,QAAQ,QAAQ,aAAa,MAAM,aAAa,MAAM,EACnF,KAAK,SAAUC,MAAK;AACnB,mBAAOA,KAAI;AAAA,UACzB,CAAa;AAAA,QACb,OAAe;AAEL,iBAAO,QAAQ,MAAM,KAAK,WAAY;AACpC,kBAAM,SAAS,IAAI,YAAY,QAAQ,MAAM;AAC7C,oBAAQ;AAAA,cACN,IAAI,WAAW,MAAM;AAAA,cACrB;AAAA,cACA;AAAA,cACA;AAAA,cACA,aAAa;AAAA,cACb,aAAa;AAAA,YACd;AACD,mBAAO;AAAA,UACnB,CAAW;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACP,OAAW;AACL,aAAO;AAAA,IACR;AAAA,EACF;AACH;AAQA,MAAM,sBAAsB;AAAA,EAC1B,YAAY,QAAQ;AAClB,SAAK,OAAO,WAAW;AACvB,SAAK,SAAS;AAAA,EACf;AAAA,EAED,eAAe,WAAW;AACxB,UAAM,OAAO,KAAK,OAAO;AACzB,UAAM,UAAU,KAAK,MAAM,SAAS;AAEpC,QAAI,CAAC,QAAQ,cAAc,CAAC,QAAQ,WAAW,KAAK,IAAI,KAAK,QAAQ,SAAS,QAAW;AACvF,aAAO;AAAA,IACR;AAED,UAAM,UAAU,KAAK,OAAO,QAAQ,IAAI;AAIxC,eAAW,aAAa,QAAQ,YAAY;AAC1C,UACE,UAAU,SAAS,gBAAgB,aACnC,UAAU,SAAS,gBAAgB,kBACnC,UAAU,SAAS,gBAAgB,gBACnC,UAAU,SAAS,QACnB;AACA,eAAO;AAAA,MACR;AAAA,IACF;AAED,UAAM,eAAe,QAAQ,WAAW,KAAK,IAAI;AACjD,UAAM,gBAAgB,aAAa;AAInC,UAAM,UAAU,CAAE;AAClB,UAAM,aAAa,CAAE;AAErB,eAAW,OAAO,eAAe;AAC/B,cAAQ;AAAA,QACN,KAAK,OAAO,cAAc,YAAY,cAAc,GAAG,CAAC,EAAE,KAAK,CAAC,aAAa;AAC3E,qBAAW,GAAG,IAAI;AAClB,iBAAO,WAAW,GAAG;AAAA,QAC/B,CAAS;AAAA,MACF;AAAA,IACF;AAED,QAAI,QAAQ,SAAS,GAAG;AACtB,aAAO;AAAA,IACR;AAED,YAAQ,KAAK,KAAK,OAAO,eAAe,SAAS,CAAC;AAElD,WAAO,QAAQ,IAAI,OAAO,EAAE,KAAK,CAAC,YAAY;AAC5C,YAAM,aAAa,QAAQ,IAAK;AAChC,YAAM,SAAS,WAAW,UAAU,WAAW,WAAW,CAAC,UAAU;AACrE,YAAM,QAAQ,QAAQ,CAAC,EAAE;AACzB,YAAM,kBAAkB,CAAE;AAE1B,iBAAW,QAAQ,QAAQ;AAEzB,cAAM,IAAI,IAAI,QAAS;AACvB,cAAM,IAAI,IAAI,QAAS;AACvB,cAAM,IAAI,IAAI,WAAY;AAC1B,cAAM,IAAI,IAAI,QAAQ,GAAG,GAAG,CAAC;AAE7B,cAAM,gBAAgB,IAAI,cAAc,KAAK,UAAU,KAAK,UAAU,KAAK;AAE3E,iBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,cAAI,WAAW,aAAa;AAC1B,cAAE,oBAAoB,WAAW,aAAa,CAAC;AAAA,UAChD;AAED,cAAI,WAAW,UAAU;AACvB,cAAE,oBAAoB,WAAW,UAAU,CAAC;AAAA,UAC7C;AAED,cAAI,WAAW,OAAO;AACpB,cAAE,oBAAoB,WAAW,OAAO,CAAC;AAAA,UAC1C;AAED,wBAAc,YAAY,GAAG,EAAE,QAAQ,GAAG,GAAG,CAAC,CAAC;AAAA,QAChD;AAGD,mBAAW,iBAAiB,YAAY;AACtC,cAAI,kBAAkB,YAAY;AAChC,kBAAM,OAAO,WAAW,aAAa;AACrC,0BAAc,gBAAgB,IAAI,yBAAyB,KAAK,OAAO,KAAK,UAAU,KAAK,UAAU;AAAA,UACjH,WAAqB,kBAAkB,iBAAiB,kBAAkB,cAAc,kBAAkB,SAAS;AACvG,iBAAK,SAAS,aAAa,eAAe,WAAW,aAAa,CAAC;AAAA,UACpE;AAAA,QACF;AAGD,iBAAS,UAAU,KAAK,KAAK,eAAe,IAAI;AAEhD,aAAK,OAAO,oBAAoB,aAAa;AAE7C,wBAAgB,KAAK,aAAa;AAAA,MACnC;AAED,UAAI,WAAW,SAAS;AACtB,mBAAW,MAAO;AAElB,mBAAW,IAAI,GAAG,eAAe;AAEjC,eAAO;AAAA,MACR;AAED,aAAO,gBAAgB,CAAC;AAAA,IAC9B,CAAK;AAAA,EACF;AACH;AAGA,MAAM,gCAAgC;AACtC,MAAM,iCAAiC;AACvC,MAAM,+BAA+B,EAAE,MAAM,YAAY,KAAK,QAAY;AAE1E,MAAM,oBAAoB;AAAA,EACxB,YAAY,MAAM;AAChB,SAAK,OAAO,WAAW;AACvB,SAAK,UAAU;AACf,SAAK,OAAO;AAEZ,UAAM,aAAa,IAAI,SAAS,MAAM,GAAG,8BAA8B;AAEvE,SAAK,SAAS;AAAA,MACZ,OAAO,WAAW,IAAI,WAAW,KAAK,MAAM,GAAG,CAAC,CAAC,CAAC;AAAA,MAClD,SAAS,WAAW,UAAU,GAAG,IAAI;AAAA,MACrC,QAAQ,WAAW,UAAU,GAAG,IAAI;AAAA,IACrC;AAED,QAAI,KAAK,OAAO,UAAU,+BAA+B;AACvD,YAAM,IAAI,MAAM,mDAAmD;AAAA,IACpE,WAAU,KAAK,OAAO,UAAU,GAAK;AACpC,YAAM,IAAI,MAAM,gDAAgD;AAAA,IACjE;AAED,UAAM,sBAAsB,KAAK,OAAO,SAAS;AACjD,UAAM,YAAY,IAAI,SAAS,MAAM,8BAA8B;AACnE,QAAI,aAAa;AAEjB,WAAO,aAAa,qBAAqB;AACvC,YAAM,cAAc,UAAU,UAAU,YAAY,IAAI;AACxD,oBAAc;AAEd,YAAM,YAAY,UAAU,UAAU,YAAY,IAAI;AACtD,oBAAc;AAEd,UAAI,cAAc,6BAA6B,MAAM;AACnD,cAAM,eAAe,IAAI,WAAW,MAAM,iCAAiC,YAAY,WAAW;AAClG,aAAK,UAAU,WAAW,YAAY;AAAA,MAC9C,WAAiB,cAAc,6BAA6B,KAAK;AACzD,cAAM,aAAa,iCAAiC;AACpD,aAAK,OAAO,KAAK,MAAM,YAAY,aAAa,WAAW;AAAA,MAC5D;AAID,oBAAc;AAAA,IACf;AAED,QAAI,KAAK,YAAY,MAAM;AACzB,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC5D;AAAA,EACF;AACH;AAOA,MAAM,kCAAkC;AAAA,EACtC,YAAY,MAAM,aAAa;AAC7B,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,qDAAqD;AAAA,IACtE;AAED,SAAK,OAAO,WAAW;AACvB,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,YAAY,QAAS;AAAA,EAC3B;AAAA,EAED,gBAAgB,WAAW,QAAQ;AACjC,UAAM,OAAO,KAAK;AAClB,UAAM,cAAc,KAAK;AACzB,UAAM,kBAAkB,UAAU,WAAW,KAAK,IAAI,EAAE;AACxD,UAAM,mBAAmB,UAAU,WAAW,KAAK,IAAI,EAAE;AACzD,UAAM,oBAAoB,CAAE;AAC5B,UAAM,yBAAyB,CAAE;AACjC,UAAM,mBAAmB,CAAE;AAE3B,eAAW,iBAAiB,kBAAkB;AAC5C,YAAM,qBAAqB,WAAW,aAAa,KAAK,cAAc,YAAa;AAEnF,wBAAkB,kBAAkB,IAAI,iBAAiB,aAAa;AAAA,IACvE;AAED,eAAW,iBAAiB,UAAU,YAAY;AAChD,YAAM,qBAAqB,WAAW,aAAa,KAAK,cAAc,YAAa;AAEnF,UAAI,iBAAiB,aAAa,MAAM,QAAW;AACjD,cAAM,cAAc,KAAK,UAAU,UAAU,WAAW,aAAa,CAAC;AACtE,cAAM,gBAAgB,sBAAsB,YAAY,aAAa;AAErE,yBAAiB,kBAAkB,IAAI,cAAc;AACrD,+BAAuB,kBAAkB,IAAI,YAAY,eAAe;AAAA,MACzE;AAAA,IACF;AAED,WAAO,OAAO,cAAc,cAAc,eAAe,EAAE,KAAK,SAAU,YAAY;AACpF,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,oBAAY;AAAA,UACV;AAAA,UACA,SAAU,UAAU;AAClB,uBAAW,iBAAiB,SAAS,YAAY;AAC/C,oBAAM,YAAY,SAAS,WAAW,aAAa;AACnD,oBAAM,aAAa,uBAAuB,aAAa;AAEvD,kBAAI,eAAe;AAAW,0BAAU,aAAa;AAAA,YACtD;AAED,oBAAQ,QAAQ;AAAA,UACjB;AAAA,UACD;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AACH;AAOA,MAAM,8BAA8B;AAAA,EAClC,cAAc;AACZ,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA,EAED,cAAc,SAAS,WAAW;AAChC,SACG,UAAU,aAAa,UAAa,UAAU,aAAa,QAAQ,YACpE,UAAU,WAAW,UACrB,UAAU,aAAa,UACvB,UAAU,UAAU,QACpB;AAEA,aAAO;AAAA,IACR;AAED,cAAU,QAAQ,MAAO;AAEzB,QAAI,UAAU,aAAa,QAAW;AACpC,cAAQ,UAAU,UAAU;AAAA,IAC7B;AAED,QAAI,UAAU,WAAW,QAAW;AAClC,cAAQ,OAAO,UAAU,UAAU,MAAM;AAAA,IAC1C;AAED,QAAI,UAAU,aAAa,QAAW;AACpC,cAAQ,WAAW,UAAU;AAAA,IAC9B;AAED,QAAI,UAAU,UAAU,QAAW;AACjC,cAAQ,OAAO,UAAU,UAAU,KAAK;AAAA,IACzC;AAED,YAAQ,cAAc;AAEtB,WAAO;AAAA,EACR;AACH;AAOA,MAAM,8BAA8B;AAAA,EAClC,cAAc;AACZ,SAAK,OAAO,WAAW;AAAA,EACxB;AACH;AAQA,MAAM,mCAAmC,YAAY;AAAA,EACnD,YAAY,oBAAoB,cAAc,YAAY,cAAc;AACtE,UAAM,oBAAoB,cAAc,YAAY,YAAY;AAAA,EACjE;AAAA,EAED,iBAAiB,OAAO;AAItB,UAAM,SAAS,KAAK,cAClB,SAAS,KAAK,cACd,YAAY,KAAK,WACjB,SAAS,QAAQ,YAAY,IAAI;AAEnC,aAAS,IAAI,GAAG,MAAM,WAAW,KAAK;AACpC,aAAO,CAAC,IAAI,OAAO,SAAS,CAAC;AAAA,IAC9B;AAED,WAAO;AAAA,EACR;AAAA,EAED,aAAa,IAAI,IAAI,GAAG,IAAI;AAC1B,UAAM,SAAS,KAAK;AACpB,UAAM,SAAS,KAAK;AACpB,UAAM,SAAS,KAAK;AAEpB,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AAEzB,UAAM,KAAK,KAAK;AAEhB,UAAM,KAAK,IAAI,MAAM;AACrB,UAAM,KAAK,IAAI;AACf,UAAM,MAAM,KAAK;AAEjB,UAAM,UAAU,KAAK;AACrB,UAAM,UAAU,UAAU;AAE1B,UAAM,KAAK,KAAK,MAAM,IAAI;AAC1B,UAAM,KAAK,MAAM;AACjB,UAAM,KAAK,IAAI;AACf,UAAM,KAAK,KAAK,KAAK;AAIrB,aAAS,IAAI,GAAG,MAAM,QAAQ,KAAK;AACjC,YAAM,KAAK,OAAO,UAAU,IAAI,MAAM;AACtC,YAAM,KAAK,OAAO,UAAU,IAAI,OAAO,IAAI;AAC3C,YAAM,KAAK,OAAO,UAAU,IAAI,MAAM;AACtC,YAAM,KAAK,OAAO,UAAU,CAAC,IAAI;AAEjC,aAAO,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAAA,IAChD;AAED,WAAO;AAAA,EACR;AACH;AAEA,MAAM,KAAqB,oBAAI,WAAY;AAE3C,MAAM,6CAA6C,2BAA2B;AAAA,EAC5E,aAAa,IAAI,IAAI,GAAG,IAAI;AAC1B,UAAM,SAAS,MAAM,aAAa,IAAI,IAAI,GAAG,EAAE;AAE/C,OAAG,UAAU,MAAM,EAAE,UAAW,EAAC,QAAQ,MAAM;AAE/C,WAAO;AAAA,EACR;AACH;AAQA,MAAM,kBAAkB;AAAA,EACtB,OAAO;AAAA;AAAA,EAEP,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,eAAe;AAAA,EACf,gBAAgB;AAClB;AAEA,MAAM,wBAAwB;AAAA,EAC5B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACR;AAEA,MAAM,gBAAgB;AAAA,EACpB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACR;AAEA,MAAM,kBAAkB;AAAA,EACtB,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACT;AAEA,MAAM,mBAAmB;AAAA,EACvB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACR;AAEA,MAAM,aAAa;AAAA,EACjB,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA;AAAA;AAAA;AAAA,EAIT,GAAI,WAAW,MACX;AAAA,IACE,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,EACb,IACD;AAAA,IACE,YAAY;AAAA,IACZ,YAAY;AAAA,EACpB;AAAA,EAEE,SAAS;AAAA,EACT,WAAW;AAAA,EACX,UAAU;AACZ;AAEA,MAAM,kBAAkB;AAAA,EACtB,OAAO;AAAA,EACP,aAAa;AAAA,EACb,UAAU;AAAA,EACV,SAAS;AACX;AAEA,MAAM,gBAAgB;AAAA,EACpB,aAAa;AAAA;AAAA;AAAA,EAEb,QAAQ;AAAA,EACR,MAAM;AACR;AAEA,MAAM,cAAc;AAAA,EAClB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AACT;AAKA,SAAS,sBAAsB,OAAO;AACpC,MAAI,MAAM,iBAAiB,MAAM,QAAW;AAC1C,UAAM,iBAAiB,IAAI,IAAI,qBAAqB;AAAA,MAClD,OAAO;AAAA,MACP,UAAU;AAAA,MACV,WAAW;AAAA,MACX,WAAW;AAAA,MACX,aAAa;AAAA,MACb,WAAW;AAAA,MACX,MAAM;AAAA,IACZ,CAAK;AAAA,EACF;AAED,SAAO,MAAM,iBAAiB;AAChC;AAEA,SAAS,+BAA+B,iBAAiB,QAAQ,WAAW;AAG1E,aAAW,QAAQ,UAAU,YAAY;AACvC,QAAI,gBAAgB,IAAI,MAAM,QAAW;AACvC,aAAO,SAAS,iBAAiB,OAAO,SAAS,kBAAkB,CAAE;AACrE,aAAO,SAAS,eAAe,IAAI,IAAI,UAAU,WAAW,IAAI;AAAA,IACjE;AAAA,EACF;AACH;AAMA,SAAS,uBAAuB,QAAQ,SAAS;AAC/C,MAAI,QAAQ,WAAW,QAAW;AAChC,QAAI,OAAO,QAAQ,WAAW,UAAU;AACtC,aAAO,OAAO,OAAO,UAAU,QAAQ,MAAM;AAAA,IACnD,OAAW;AACL,cAAQ,KAAK,wDAAwD,QAAQ,MAAM;AAAA,IACpF;AAAA,EACF;AACH;AAUA,SAAS,gBAAgB,UAAU,SAAS,QAAQ;AAClD,MAAI,mBAAmB;AACvB,MAAI,iBAAiB;AACrB,MAAI,gBAAgB;AAEpB,WAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAChD,UAAM,SAAS,QAAQ,CAAC;AAExB,QAAI,OAAO,aAAa;AAAW,yBAAmB;AACtD,QAAI,OAAO,WAAW;AAAW,uBAAiB;AAClD,QAAI,OAAO,YAAY;AAAW,sBAAgB;AAElD,QAAI,oBAAoB,kBAAkB;AAAe;AAAA,EAC1D;AAED,MAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC;AAAe,WAAO,QAAQ,QAAQ,QAAQ;AAE3F,QAAM,2BAA2B,CAAE;AACnC,QAAM,yBAAyB,CAAE;AACjC,QAAM,wBAAwB,CAAE;AAEhC,WAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAChD,UAAM,SAAS,QAAQ,CAAC;AAExB,QAAI,kBAAkB;AACpB,YAAM,kBACJ,OAAO,aAAa,SAAY,OAAO,cAAc,YAAY,OAAO,QAAQ,IAAI,SAAS,WAAW;AAE1G,+BAAyB,KAAK,eAAe;AAAA,IAC9C;AAED,QAAI,gBAAgB;AAClB,YAAM,kBACJ,OAAO,WAAW,SAAY,OAAO,cAAc,YAAY,OAAO,MAAM,IAAI,SAAS,WAAW;AAEtG,6BAAuB,KAAK,eAAe;AAAA,IAC5C;AAED,QAAI,eAAe;AACjB,YAAM,kBACJ,OAAO,YAAY,SAAY,OAAO,cAAc,YAAY,OAAO,OAAO,IAAI,SAAS,WAAW;AAExG,4BAAsB,KAAK,eAAe;AAAA,IAC3C;AAAA,EACF;AAED,SAAO,QAAQ,IAAI;AAAA,IACjB,QAAQ,IAAI,wBAAwB;AAAA,IACpC,QAAQ,IAAI,sBAAsB;AAAA,IAClC,QAAQ,IAAI,qBAAqB;AAAA,EACrC,CAAG,EAAE,KAAK,SAAU,WAAW;AAC3B,UAAM,iBAAiB,UAAU,CAAC;AAClC,UAAM,eAAe,UAAU,CAAC;AAChC,UAAM,cAAc,UAAU,CAAC;AAE/B,QAAI;AAAkB,eAAS,gBAAgB,WAAW;AAC1D,QAAI;AAAgB,eAAS,gBAAgB,SAAS;AACtD,QAAI;AAAe,eAAS,gBAAgB,QAAQ;AACpD,aAAS,uBAAuB;AAEhC,WAAO;AAAA,EACX,CAAG;AACH;AAMA,SAAS,mBAAmB,MAAM,SAAS;AACzC,OAAK,mBAAoB;AAEzB,MAAI,QAAQ,YAAY,QAAW;AACjC,aAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,QAAQ,IAAI,IAAI,KAAK;AACxD,WAAK,sBAAsB,CAAC,IAAI,QAAQ,QAAQ,CAAC;AAAA,IAClD;AAAA,EACF;AAGD,MAAI,QAAQ,UAAU,MAAM,QAAQ,QAAQ,OAAO,WAAW,GAAG;AAC/D,UAAM,cAAc,QAAQ,OAAO;AAEnC,QAAI,KAAK,sBAAsB,WAAW,YAAY,QAAQ;AAC5D,WAAK,wBAAwB,CAAE;AAE/B,eAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AACpD,aAAK,sBAAsB,YAAY,CAAC,CAAC,IAAI;AAAA,MAC9C;AAAA,IACP,OAAW;AACL,cAAQ,KAAK,sEAAsE;AAAA,IACpF;AAAA,EACF;AACH;AAEA,SAAS,mBAAmB,cAAc;AACxC,MAAI;AAEJ,QAAM,iBAAiB,aAAa,cAAc,aAAa,WAAW,WAAW,0BAA0B;AAE/G,MAAI,gBAAgB;AAClB,kBACE,WACA,eAAe,aACf,MACA,eAAe,UACf,MACA,oBAAoB,eAAe,UAAU;AAAA,EACnD,OAAS;AACL,kBAAc,aAAa,UAAU,MAAM,oBAAoB,aAAa,UAAU,IAAI,MAAM,aAAa;AAAA,EAC9G;AAED,MAAI,aAAa,YAAY,QAAW;AACtC,aAAS,IAAI,GAAG,KAAK,aAAa,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAC7D,qBAAe,MAAM,oBAAoB,aAAa,QAAQ,CAAC,CAAC;AAAA,IACjE;AAAA,EACF;AAED,SAAO;AACT;AAEA,SAAS,oBAAoB,YAAY;AACvC,MAAI,gBAAgB;AAEpB,QAAM,OAAO,OAAO,KAAK,UAAU,EAAE,KAAM;AAE3C,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,KAAK;AAC7C,qBAAiB,KAAK,CAAC,IAAI,MAAM,WAAW,KAAK,CAAC,CAAC,IAAI;AAAA,EACxD;AAED,SAAO;AACT;AAEA,SAAS,4BAA4B,aAAa;AAIhD,UAAQ,aAAW;AAAA,IACjB,KAAK;AACH,aAAO,IAAI;AAAA,IAEb,KAAK;AACH,aAAO,IAAI;AAAA,IAEb,KAAK;AACH,aAAO,IAAI;AAAA,IAEb,KAAK;AACH,aAAO,IAAI;AAAA,IAEb;AACE,YAAM,IAAI,MAAM,mEAAmE;AAAA,EACtF;AACH;AAEA,SAAS,oBAAoB,KAAK;AAChC,MAAI,IAAI,OAAO,gBAAgB,IAAI,KAAK,IAAI,OAAO,oBAAoB,MAAM;AAAG,WAAO;AACvF,MAAI,IAAI,OAAO,eAAe,IAAI,KAAK,IAAI,OAAO,oBAAoB,MAAM;AAAG,WAAO;AAEtF,SAAO;AACT;AAEA,MAAM,kBAAkC,oBAAI,QAAS;AAIrD,MAAM,WAAW;AAAA,EACf,YAAY,OAAO,IAAI,UAAU,CAAA,GAAI;AACnC,SAAK,OAAO;AACZ,SAAK,aAAa,CAAE;AACpB,SAAK,UAAU,CAAE;AACjB,SAAK,UAAU;AAGf,SAAK,QAAQ,IAAI,aAAc;AAG/B,SAAK,eAAe,oBAAI,IAAK;AAG7B,SAAK,iBAAiB,CAAE;AAGxB,SAAK,YAAY,CAAE;AAGnB,SAAK,YAAY,EAAE,MAAM,CAAA,GAAI,MAAM,CAAA,EAAI;AACvC,SAAK,cAAc,EAAE,MAAM,CAAA,GAAI,MAAM,CAAA,EAAI;AACzC,SAAK,aAAa,EAAE,MAAM,CAAA,GAAI,MAAM,CAAA,EAAI;AAExC,SAAK,cAAc,CAAE;AACrB,SAAK,eAAe,CAAE;AAGtB,SAAK,gBAAgB,CAAE;AAKvB,QAAI,WAAW;AACf,QAAI,YAAY;AAChB,QAAI,iBAAiB;AAErB,QAAI,OAAO,cAAc,eAAe,OAAO,UAAU,cAAc,aAAa;AAClF,iBAAW,iCAAiC,KAAK,UAAU,SAAS,MAAM;AAC1E,kBAAY,UAAU,UAAU,QAAQ,SAAS,IAAI;AACrD,uBAAiB,YAAY,UAAU,UAAU,MAAM,qBAAqB,EAAE,CAAC,IAAI;AAAA,IACpF;AAED,QAAI,OAAO,sBAAsB,eAAe,YAAa,aAAa,iBAAiB,IAAK;AAC9F,WAAK,gBAAgB,IAAI,cAAc,KAAK,QAAQ,OAAO;AAAA,IACjE,OAAW;AACL,WAAK,gBAAgB,IAAI,kBAAkB,KAAK,QAAQ,OAAO;AAAA,IAChE;AAED,SAAK,cAAc,eAAe,KAAK,QAAQ,WAAW;AAC1D,SAAK,cAAc,iBAAiB,KAAK,QAAQ,aAAa;AAE9D,SAAK,aAAa,IAAI,WAAW,KAAK,QAAQ,OAAO;AACrD,SAAK,WAAW,gBAAgB,aAAa;AAE7C,QAAI,KAAK,QAAQ,gBAAgB,mBAAmB;AAClD,WAAK,WAAW,mBAAmB,IAAI;AAAA,IACxC;AAAA,EACF;AAAA,EAED,cAAc,YAAY;AACxB,SAAK,aAAa;AAAA,EACnB;AAAA,EAED,WAAW,SAAS;AAClB,SAAK,UAAU;AAAA,EAChB;AAAA,EAED,MAAM,QAAQ,SAAS;AACrB,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAClB,UAAM,aAAa,KAAK;AAGxB,SAAK,MAAM,UAAW;AACtB,SAAK,YAAY,CAAE;AAGnB,SAAK,WAAW,SAAU,KAAK;AAC7B,aAAO,IAAI,aAAa,IAAI,UAAW;AAAA,IAC7C,CAAK;AAED,YAAQ;AAAA,MACN,KAAK,WAAW,SAAU,KAAK;AAC7B,eAAO,IAAI,cAAc,IAAI,WAAY;AAAA,MACjD,CAAO;AAAA,IACF,EACE,KAAK,WAAY;AAChB,aAAO,QAAQ,IAAI;AAAA,QACjB,OAAO,gBAAgB,OAAO;AAAA,QAC9B,OAAO,gBAAgB,WAAW;AAAA,QAClC,OAAO,gBAAgB,QAAQ;AAAA,MACzC,CAAS;AAAA,IACT,CAAO,EACA,KAAK,SAAU,cAAc;AAC5B,YAAM,SAAS;AAAA,QACb,OAAO,aAAa,CAAC,EAAE,KAAK,SAAS,CAAC;AAAA,QACtC,QAAQ,aAAa,CAAC;AAAA,QACtB,YAAY,aAAa,CAAC;AAAA,QAC1B,SAAS,aAAa,CAAC;AAAA,QACvB,OAAO,KAAK;AAAA,QACZ;AAAA,QACA,UAAU,CAAE;AAAA,MACb;AAED,qCAA+B,YAAY,QAAQ,IAAI;AAEvD,6BAAuB,QAAQ,IAAI;AAEnC,aAAO,QAAQ;AAAA,QACb,OAAO,WAAW,SAAU,KAAK;AAC/B,iBAAO,IAAI,aAAa,IAAI,UAAU,MAAM;AAAA,QACxD,CAAW;AAAA,MACF,EAAC,KAAK,WAAY;AACjB,mBAAW,SAAS,OAAO,QAAQ;AACjC,gBAAM,kBAAmB;AAAA,QAC1B;AAED,eAAO,MAAM;AAAA,MACvB,CAAS;AAAA,IACT,CAAO,EACA,MAAM,OAAO;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAKD,YAAY;AACV,UAAM,WAAW,KAAK,KAAK,SAAS,CAAE;AACtC,UAAM,WAAW,KAAK,KAAK,SAAS,CAAE;AACtC,UAAM,WAAW,KAAK,KAAK,UAAU,CAAE;AAIvC,aAAS,YAAY,GAAG,aAAa,SAAS,QAAQ,YAAY,YAAY,aAAa;AACzF,YAAM,SAAS,SAAS,SAAS,EAAE;AAEnC,eAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AAC/C,iBAAS,OAAO,CAAC,CAAC,EAAE,SAAS;AAAA,MAC9B;AAAA,IACF;AAID,aAAS,YAAY,GAAG,aAAa,SAAS,QAAQ,YAAY,YAAY,aAAa;AACzF,YAAM,UAAU,SAAS,SAAS;AAElC,UAAI,QAAQ,SAAS,QAAW;AAC9B,aAAK,YAAY,KAAK,WAAW,QAAQ,IAAI;AAK7C,YAAI,QAAQ,SAAS,QAAW;AAC9B,mBAAS,QAAQ,IAAI,EAAE,gBAAgB;AAAA,QACxC;AAAA,MACF;AAED,UAAI,QAAQ,WAAW,QAAW;AAChC,aAAK,YAAY,KAAK,aAAa,QAAQ,MAAM;AAAA,MAClD;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWD,YAAY,OAAO,OAAO;AACxB,QAAI,UAAU;AAAW;AAEzB,QAAI,MAAM,KAAK,KAAK,MAAM,QAAW;AACnC,YAAM,KAAK,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI;AAAA,IACzC;AAED,UAAM,KAAK,KAAK;AAAA,EACjB;AAAA;AAAA,EAGD,YAAY,OAAO,OAAO,QAAQ;AAChC,QAAI,MAAM,KAAK,KAAK,KAAK;AAAG,aAAO;AAEnC,UAAM,MAAM,OAAO,MAAO;AAI1B,UAAM,iBAAiB,CAAC,UAAU,UAAU;AAC1C,YAAM,WAAW,KAAK,aAAa,IAAI,QAAQ;AAC/C,UAAI,YAAY,MAAM;AACpB,aAAK,aAAa,IAAI,OAAO,QAAQ;AAAA,MACtC;AAED,iBAAW,CAAC,GAAG,KAAK,KAAK,SAAS,SAAS,WAAW;AACpD,uBAAe,OAAO,MAAM,SAAS,CAAC,CAAC;AAAA,MACxC;AAAA,IACF;AAED,mBAAe,QAAQ,GAAG;AAE1B,QAAI,QAAQ,eAAe,MAAM,KAAK,KAAK;AAE3C,WAAO;AAAA,EACR;AAAA,EAED,WAAW,MAAM;AACf,UAAM,aAAa,OAAO,OAAO,KAAK,OAAO;AAC7C,eAAW,KAAK,IAAI;AAEpB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,YAAM,SAAS,KAAK,WAAW,CAAC,CAAC;AAEjC,UAAI;AAAQ,eAAO;AAAA,IACpB;AAED,WAAO;AAAA,EACR;AAAA,EAED,WAAW,MAAM;AACf,UAAM,aAAa,OAAO,OAAO,KAAK,OAAO;AAC7C,eAAW,QAAQ,IAAI;AAEvB,UAAM,UAAU,CAAE;AAElB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,YAAM,SAAS,KAAK,WAAW,CAAC,CAAC;AAEjC,UAAI;AAAQ,gBAAQ,KAAK,MAAM;AAAA,IAChC;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,cAAc,MAAM,OAAO;AACzB,UAAM,WAAW,OAAO,MAAM;AAC9B,QAAI,aAAa,KAAK,MAAM,IAAI,QAAQ;AAExC,QAAI,CAAC,YAAY;AACf,cAAQ,MAAI;AAAA,QACV,KAAK;AACH,uBAAa,KAAK,UAAU,KAAK;AACjC;AAAA,QAEF,KAAK;AACH,uBAAa,KAAK,WAAW,SAAU,KAAK;AAC1C,mBAAO,IAAI,YAAY,IAAI,SAAS,KAAK;AAAA,UACrD,CAAW;AACD;AAAA,QAEF,KAAK;AACH,uBAAa,KAAK,WAAW,SAAU,KAAK;AAC1C,mBAAO,IAAI,YAAY,IAAI,SAAS,KAAK;AAAA,UACrD,CAAW;AACD;AAAA,QAEF,KAAK;AACH,uBAAa,KAAK,aAAa,KAAK;AACpC;AAAA,QAEF,KAAK;AACH,uBAAa,KAAK,WAAW,SAAU,KAAK;AAC1C,mBAAO,IAAI,kBAAkB,IAAI,eAAe,KAAK;AAAA,UACjE,CAAW;AACD;AAAA,QAEF,KAAK;AACH,uBAAa,KAAK,WAAW,KAAK;AAClC;AAAA,QAEF,KAAK;AACH,uBAAa,KAAK,WAAW,SAAU,KAAK;AAC1C,mBAAO,IAAI,gBAAgB,IAAI,aAAa,KAAK;AAAA,UAC7D,CAAW;AACD;AAAA,QAEF,KAAK;AACH,uBAAa,KAAK,WAAW,SAAU,KAAK;AAC1C,mBAAO,IAAI,eAAe,IAAI,YAAY,KAAK;AAAA,UAC3D,CAAW;AACD;AAAA,QAEF,KAAK;AACH,uBAAa,KAAK,SAAS,KAAK;AAChC;AAAA,QAEF,KAAK;AACH,uBAAa,KAAK,WAAW,SAAU,KAAK;AAC1C,mBAAO,IAAI,iBAAiB,IAAI,cAAc,KAAK;AAAA,UAC/D,CAAW;AACD;AAAA,QAEF,KAAK;AACH,uBAAa,KAAK,WAAW,KAAK;AAClC;AAAA,QAEF;AACE,uBAAa,KAAK,WAAW,SAAU,KAAK;AAC1C,mBAAO,OAAO,QAAQ,IAAI,iBAAiB,IAAI,cAAc,MAAM,KAAK;AAAA,UACpF,CAAW;AAED,cAAI,CAAC,YAAY;AACf,kBAAM,IAAI,MAAM,mBAAmB,IAAI;AAAA,UACxC;AAED;AAAA,MACH;AAED,WAAK,MAAM,IAAI,UAAU,UAAU;AAAA,IACpC;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,gBAAgB,MAAM;AACpB,QAAI,eAAe,KAAK,MAAM,IAAI,IAAI;AAEtC,QAAI,CAAC,cAAc;AACjB,YAAM,SAAS;AACf,YAAM,OAAO,KAAK,KAAK,QAAQ,SAAS,SAAS,OAAO,IAAI,KAAK,CAAE;AAEnE,qBAAe,QAAQ;AAAA,QACrB,KAAK,IAAI,SAAU,KAAK,OAAO;AAC7B,iBAAO,OAAO,cAAc,MAAM,KAAK;AAAA,QACjD,CAAS;AAAA,MACF;AAED,WAAK,MAAM,IAAI,MAAM,YAAY;AAAA,IAClC;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,WAAW,aAAa;AACtB,UAAM,YAAY,KAAK,KAAK,QAAQ,WAAW;AAC/C,UAAM,SAAS,KAAK;AAEpB,QAAI,UAAU,QAAQ,UAAU,SAAS,eAAe;AACtD,YAAM,IAAI,MAAM,uBAAuB,UAAU,OAAO,gCAAgC;AAAA,IACzF;AAGD,QAAI,UAAU,QAAQ,UAAa,gBAAgB,GAAG;AACpD,aAAO,QAAQ,QAAQ,KAAK,WAAW,WAAW,eAAe,EAAE,IAAI;AAAA,IACxE;AAED,UAAM,UAAU,KAAK;AAErB,WAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,aAAO,KAAK,YAAY,WAAW,UAAU,KAAK,QAAQ,IAAI,GAAG,SAAS,QAAW,WAAY;AAC/F,eAAO,IAAI,MAAM,8CAA8C,UAAU,MAAM,IAAI,CAAC;AAAA,MAC5F,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,eAAe,iBAAiB;AAC9B,UAAM,gBAAgB,KAAK,KAAK,YAAY,eAAe;AAE3D,WAAO,KAAK,cAAc,UAAU,cAAc,MAAM,EAAE,KAAK,SAAU,QAAQ;AAC/E,YAAM,aAAa,cAAc,cAAc;AAC/C,YAAM,aAAa,cAAc,cAAc;AAC/C,aAAO,OAAO,MAAM,YAAY,aAAa,UAAU;AAAA,IAC7D,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,aAAa,eAAe;AAC1B,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAElB,UAAM,cAAc,KAAK,KAAK,UAAU,aAAa;AAErD,QAAI,YAAY,eAAe,UAAa,YAAY,WAAW,QAAW;AAC5E,YAAM,WAAW,iBAAiB,YAAY,IAAI;AAClD,YAAM,aAAa,sBAAsB,YAAY,aAAa;AAClE,YAAM,aAAa,YAAY,eAAe;AAE9C,YAAM,QAAQ,IAAI,WAAW,YAAY,QAAQ,QAAQ;AACzD,aAAO,QAAQ,QAAQ,IAAI,gBAAgB,OAAO,UAAU,UAAU,CAAC;AAAA,IACxE;AAED,UAAM,qBAAqB,CAAE;AAE7B,QAAI,YAAY,eAAe,QAAW;AACxC,yBAAmB,KAAK,KAAK,cAAc,cAAc,YAAY,UAAU,CAAC;AAAA,IACtF,OAAW;AACL,yBAAmB,KAAK,IAAI;AAAA,IAC7B;AAED,QAAI,YAAY,WAAW,QAAW;AACpC,yBAAmB,KAAK,KAAK,cAAc,cAAc,YAAY,OAAO,QAAQ,UAAU,CAAC;AAC/F,yBAAmB,KAAK,KAAK,cAAc,cAAc,YAAY,OAAO,OAAO,UAAU,CAAC;AAAA,IAC/F;AAED,WAAO,QAAQ,IAAI,kBAAkB,EAAE,KAAK,SAAU,aAAa;AACjE,YAAM,aAAa,YAAY,CAAC;AAEhC,YAAM,WAAW,iBAAiB,YAAY,IAAI;AAClD,YAAM,aAAa,sBAAsB,YAAY,aAAa;AAGlE,YAAM,eAAe,WAAW;AAChC,YAAM,YAAY,eAAe;AACjC,YAAM,aAAa,YAAY,cAAc;AAC7C,YAAM,aACJ,YAAY,eAAe,SAAY,KAAK,YAAY,YAAY,UAAU,EAAE,aAAa;AAC/F,YAAM,aAAa,YAAY,eAAe;AAC9C,UAAI,OAAO;AAGX,UAAI,cAAc,eAAe,WAAW;AAG1C,cAAM,UAAU,KAAK,MAAM,aAAa,UAAU;AAClD,cAAM,aACJ,uBACA,YAAY,aACZ,MACA,YAAY,gBACZ,MACA,UACA,MACA,YAAY;AACd,YAAI,KAAK,OAAO,MAAM,IAAI,UAAU;AAEpC,YAAI,CAAC,IAAI;AACP,kBAAQ,IAAI,WAAW,YAAY,UAAU,YAAa,YAAY,QAAQ,aAAc,YAAY;AAGxG,eAAK,IAAI,kBAAkB,OAAO,aAAa,YAAY;AAE3D,iBAAO,MAAM,IAAI,YAAY,EAAE;AAAA,QAChC;AAED,0BAAkB,IAAI;AAAA,UACpB;AAAA,UACA;AAAA,UACC,aAAa,aAAc;AAAA,UAC5B;AAAA,QACD;AAAA,MACT,OAAa;AACL,YAAI,eAAe,MAAM;AACvB,kBAAQ,IAAI,WAAW,YAAY,QAAQ,QAAQ;AAAA,QAC7D,OAAe;AACL,kBAAQ,IAAI,WAAW,YAAY,YAAY,YAAY,QAAQ,QAAQ;AAAA,QAC5E;AAED,0BAAkB,IAAI,gBAAgB,OAAO,UAAU,UAAU;AAAA,MAClE;AAGD,UAAI,YAAY,WAAW,QAAW;AACpC,cAAM,kBAAkB,iBAAiB;AACzC,cAAM,oBAAoB,sBAAsB,YAAY,OAAO,QAAQ,aAAa;AAExF,cAAM,oBAAoB,YAAY,OAAO,QAAQ,cAAc;AACnE,cAAM,mBAAmB,YAAY,OAAO,OAAO,cAAc;AAEjE,cAAM,gBAAgB,IAAI;AAAA,UACxB,YAAY,CAAC;AAAA,UACb;AAAA,UACA,YAAY,OAAO,QAAQ;AAAA,QAC5B;AACD,cAAM,eAAe,IAAI,WAAW,YAAY,CAAC,GAAG,kBAAkB,YAAY,OAAO,QAAQ,QAAQ;AAEzG,YAAI,eAAe,MAAM;AAEvB,4BAAkB,IAAI;AAAA,YACpB,gBAAgB,MAAM,MAAO;AAAA,YAC7B,gBAAgB;AAAA,YAChB,gBAAgB;AAAA,UACjB;AAAA,QACF;AAED,iBAAS,IAAI,GAAG,KAAK,cAAc,QAAQ,IAAI,IAAI,KAAK;AACtD,gBAAM,QAAQ,cAAc,CAAC;AAE7B,0BAAgB,KAAK,OAAO,aAAa,IAAI,QAAQ,CAAC;AACtD,cAAI,YAAY;AAAG,4BAAgB,KAAK,OAAO,aAAa,IAAI,WAAW,CAAC,CAAC;AAC7E,cAAI,YAAY;AAAG,4BAAgB,KAAK,OAAO,aAAa,IAAI,WAAW,CAAC,CAAC;AAC7E,cAAI,YAAY;AAAG,4BAAgB,KAAK,OAAO,aAAa,IAAI,WAAW,CAAC,CAAC;AAC7E,cAAI,YAAY;AAAG,kBAAM,IAAI,MAAM,mEAAmE;AAAA,QACvG;AAAA,MACF;AAED,aAAO;AAAA,IACb,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,YAAY,cAAc;AACxB,UAAM,OAAO,KAAK;AAClB,UAAM,UAAU,KAAK;AACrB,UAAM,aAAa,KAAK,SAAS,YAAY;AAC7C,UAAM,cAAc,WAAW;AAC/B,UAAM,YAAY,KAAK,OAAO,WAAW;AAEzC,QAAI,SAAS,KAAK;AAElB,QAAI,UAAU,KAAK;AACjB,YAAM,UAAU,QAAQ,QAAQ,WAAW,UAAU,GAAG;AACxD,UAAI,YAAY;AAAM,iBAAS;AAAA,IAChC;AAED,WAAO,KAAK,iBAAiB,cAAc,aAAa,MAAM;AAAA,EAC/D;AAAA,EAED,iBAAiB,cAAc,aAAa,QAAQ;AAClD,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAElB,UAAM,aAAa,KAAK,SAAS,YAAY;AAC7C,UAAM,YAAY,KAAK,OAAO,WAAW;AAEzC,UAAM,YAAY,UAAU,OAAO,UAAU,cAAc,MAAM,WAAW;AAE5E,QAAI,KAAK,aAAa,QAAQ,GAAG;AAE/B,aAAO,KAAK,aAAa,QAAQ;AAAA,IAClC;AAED,UAAM,UAAU,KAAK,gBAAgB,aAAa,MAAM,EACrD,KAAK,SAAU,SAAS;AACvB,cAAQ,QAAQ;AAEhB,cAAQ,OAAO,WAAW,QAAQ,UAAU,QAAQ;AAEpD,UACE,QAAQ,SAAS,MACjB,OAAO,UAAU,QAAQ,YACzB,UAAU,IAAI,WAAW,aAAa,MAAM,OAC5C;AACA,gBAAQ,OAAO,UAAU;AAAA,MAC1B;AAED,YAAM,WAAW,KAAK,YAAY,CAAE;AACpC,YAAM,UAAU,SAAS,WAAW,OAAO,KAAK,CAAE;AAElD,cAAQ,YAAY,cAAc,QAAQ,SAAS,KAAK;AACxD,cAAQ,YAAY,cAAc,QAAQ,SAAS,KAAK;AACxD,cAAQ,QAAQ,gBAAgB,QAAQ,KAAK,KAAK;AAClD,cAAQ,QAAQ,gBAAgB,QAAQ,KAAK,KAAK;AAElD,aAAO,aAAa,IAAI,SAAS,EAAE,UAAU,cAAc;AAE3D,aAAO;AAAA,IACf,CAAO,EACA,MAAM,WAAY;AACjB,aAAO;AAAA,IACf,CAAO;AAEH,SAAK,aAAa,QAAQ,IAAI;AAE9B,WAAO;AAAA,EACR;AAAA,EAED,gBAAgB,aAAa,QAAQ;AACnC,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAClB,UAAM,UAAU,KAAK;AAErB,QAAI,KAAK,YAAY,WAAW,MAAM,QAAW;AAC/C,aAAO,KAAK,YAAY,WAAW,EAAE,KAAK,CAAC,YAAY,QAAQ,OAAO;AAAA,IACvE;AAED,UAAM,YAAY,KAAK,OAAO,WAAW;AAEzC,UAAM,MAAM,KAAK,OAAO,KAAK;AAE7B,QAAI,YAAY,UAAU,OAAO;AACjC,QAAI,cAAc;AAElB,QAAI,UAAU,eAAe,QAAW;AAGtC,kBAAY,OAAO,cAAc,cAAc,UAAU,UAAU,EAAE,KAAK,SAAU,YAAY;AAC9F,sBAAc;AACd,cAAM,OAAO,IAAI,KAAK,CAAC,UAAU,GAAG,EAAE,MAAM,UAAU,UAAU;AAChE,oBAAY,IAAI,gBAAgB,IAAI;AACpC,eAAO;AAAA,MACf,CAAO;AAAA,IACP,WAAe,UAAU,QAAQ,QAAW;AACtC,YAAM,IAAI,MAAM,6BAA6B,cAAc,gCAAgC;AAAA,IAC5F;AAED,UAAM,UAAU,QAAQ,QAAQ,SAAS,EACtC,KAAK,SAAUC,YAAW;AACzB,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,YAAI,SAAS;AAEb,YAAI,OAAO,wBAAwB,MAAM;AACvC,mBAAS,SAAU,aAAa;AAC9B,kBAAM,UAAU,IAAI,QAAQ,WAAW;AACvC,oBAAQ,cAAc;AAEtB,oBAAQ,OAAO;AAAA,UAChB;AAAA,QACF;AAED,eAAO,KAAK,YAAY,WAAWA,YAAW,QAAQ,IAAI,GAAG,QAAQ,QAAW,MAAM;AAAA,MAChG,CAAS;AAAA,IACT,CAAO,EACA,KAAK,SAAU,SAAS;AAGvB,UAAI,gBAAgB,MAAM;AACxB,YAAI,gBAAgB,SAAS;AAAA,MAC9B;AAED,6BAAuB,SAAS,SAAS;AAEzC,cAAQ,SAAS,WAAW,UAAU,YAAY,oBAAoB,UAAU,GAAG;AAEnF,aAAO;AAAA,IACf,CAAO,EACA,MAAM,SAAU,OAAO;AACtB,cAAQ,MAAM,2CAA2C,SAAS;AAClE,YAAM;AAAA,IACd,CAAO;AAEH,SAAK,YAAY,WAAW,IAAI;AAChC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,cAAc,gBAAgB,SAAS,QAAQ,YAAY;AACzD,UAAM,SAAS;AAEf,WAAO,KAAK,cAAc,WAAW,OAAO,KAAK,EAAE,KAAK,SAAU,SAAS;AACzE,UAAI,CAAC;AAAS,eAAO;AAErB,UAAI,OAAO,aAAa,UAAa,OAAO,WAAW,GAAG;AACxD,kBAAU,QAAQ,MAAO;AACzB,gBAAQ,UAAU,OAAO;AAAA,MAC1B;AAED,UAAI,OAAO,WAAW,WAAW,qBAAqB,GAAG;AACvD,cAAM,YACJ,OAAO,eAAe,SAAY,OAAO,WAAW,WAAW,qBAAqB,IAAI;AAE1F,YAAI,WAAW;AACb,gBAAM,gBAAgB,OAAO,aAAa,IAAI,OAAO;AACrD,oBAAU,OAAO,WAAW,WAAW,qBAAqB,EAAE,cAAc,SAAS,SAAS;AAC9F,iBAAO,aAAa,IAAI,SAAS,aAAa;AAAA,QAC/C;AAAA,MACF;AAED,UAAI,eAAe,QAAW;AAE5B,YAAI,OAAO,eAAe;AACxB,uBAAa,eAAe,eAAe,iBAAiB;AAG9D,YAAI,gBAAgB;AAAS,kBAAQ,aAAa;AAAA;AAC7C,kBAAQ,WAAW,eAAe,iBAAiB,eAAe;AAAA,MACxE;AAED,qBAAe,OAAO,IAAI;AAE1B,aAAO;AAAA,IACb,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,oBAAoB,MAAM;AACxB,UAAM,WAAW,KAAK;AACtB,QAAI,WAAW,KAAK;AAEpB,UAAM,wBAAwB,SAAS,WAAW,YAAY;AAC9D,UAAM,kBAAkB,SAAS,WAAW,UAAU;AACtD,UAAM,iBAAiB,SAAS,WAAW,WAAW;AAEtD,QAAI,KAAK,UAAU;AACjB,YAAM,WAAW,oBAAoB,SAAS;AAE9C,UAAI,iBAAiB,KAAK,MAAM,IAAI,QAAQ;AAE5C,UAAI,CAAC,gBAAgB;AACnB,yBAAiB,IAAI,eAAgB;AACrC,iBAAS,UAAU,KAAK,KAAK,gBAAgB,QAAQ;AACrD,uBAAe,MAAM,KAAK,SAAS,KAAK;AACxC,uBAAe,MAAM,SAAS;AAC9B,uBAAe,kBAAkB;AAEjC,aAAK,MAAM,IAAI,UAAU,cAAc;AAAA,MACxC;AAED,iBAAW;AAAA,IACjB,WAAe,KAAK,QAAQ;AACtB,YAAM,WAAW,uBAAuB,SAAS;AAEjD,UAAI,eAAe,KAAK,MAAM,IAAI,QAAQ;AAE1C,UAAI,CAAC,cAAc;AACjB,uBAAe,IAAI,kBAAmB;AACtC,iBAAS,UAAU,KAAK,KAAK,cAAc,QAAQ;AACnD,qBAAa,MAAM,KAAK,SAAS,KAAK;AACtC,qBAAa,MAAM,SAAS;AAE5B,aAAK,MAAM,IAAI,UAAU,YAAY;AAAA,MACtC;AAED,iBAAW;AAAA,IACZ;AAGD,QAAI,yBAAyB,mBAAmB,gBAAgB;AAC9D,UAAI,WAAW,oBAAoB,SAAS,OAAO;AAEnD,UAAI;AAAuB,oBAAY;AACvC,UAAI;AAAiB,oBAAY;AACjC,UAAI;AAAgB,oBAAY;AAEhC,UAAI,iBAAiB,KAAK,MAAM,IAAI,QAAQ;AAE5C,UAAI,CAAC,gBAAgB;AACnB,yBAAiB,SAAS,MAAO;AAEjC,YAAI;AAAiB,yBAAe,eAAe;AACnD,YAAI;AAAgB,yBAAe,cAAc;AAEjD,YAAI,uBAAuB;AAEzB,cAAI,eAAe;AAAa,2BAAe,YAAY,KAAK;AAChE,cAAI,eAAe;AAAsB,2BAAe,qBAAqB,KAAK;AAAA,QACnF;AAED,aAAK,MAAM,IAAI,UAAU,cAAc;AAEvC,aAAK,aAAa,IAAI,gBAAgB,KAAK,aAAa,IAAI,QAAQ,CAAC;AAAA,MACtE;AAED,iBAAW;AAAA,IACZ;AAED,SAAK,WAAW;AAAA,EACjB;AAAA,EAED,kBAAqC;AACnC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,aAAa,eAAe;AAC1B,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAClB,UAAM,aAAa,KAAK;AACxB,UAAM,cAAc,KAAK,UAAU,aAAa;AAEhD,QAAI;AACJ,UAAM,iBAAiB,CAAE;AACzB,UAAM,qBAAqB,YAAY,cAAc,CAAE;AAEvD,UAAM,UAAU,CAAE;AAElB,QAAI,mBAAmB,WAAW,mBAAmB,GAAG;AACtD,YAAM,eAAe,WAAW,WAAW,mBAAmB;AAC9D,qBAAe,aAAa,gBAAiB;AAC7C,cAAQ,KAAK,aAAa,aAAa,gBAAgB,aAAa,MAAM,CAAC;AAAA,IACjF,OAAW;AAIL,YAAM,oBAAoB,YAAY,wBAAwB,CAAE;AAEhE,qBAAe,QAAQ,IAAI,MAAM,GAAK,GAAK,CAAG;AAC9C,qBAAe,UAAU;AAEzB,UAAI,MAAM,QAAQ,kBAAkB,eAAe,GAAG;AACpD,cAAM,QAAQ,kBAAkB;AAEhC,uBAAe,MAAM,OAAO,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,oBAAoB;AAC9E,uBAAe,UAAU,MAAM,CAAC;AAAA,MACjC;AAED,UAAI,kBAAkB,qBAAqB,QAAW;AACpD,gBAAQ,KAAK,OAAO,cAAc,gBAAgB,OAAO,kBAAkB,kBAAkB,cAAc,CAAC;AAAA,MAC7G;AAED,qBAAe,YAAY,kBAAkB,mBAAmB,SAAY,kBAAkB,iBAAiB;AAC/G,qBAAe,YACb,kBAAkB,oBAAoB,SAAY,kBAAkB,kBAAkB;AAExF,UAAI,kBAAkB,6BAA6B,QAAW;AAC5D,gBAAQ,KAAK,OAAO,cAAc,gBAAgB,gBAAgB,kBAAkB,wBAAwB,CAAC;AAC7G,gBAAQ,KAAK,OAAO,cAAc,gBAAgB,gBAAgB,kBAAkB,wBAAwB,CAAC;AAAA,MAC9G;AAED,qBAAe,KAAK,WAAW,SAAU,KAAK;AAC5C,eAAO,IAAI,mBAAmB,IAAI,gBAAgB,aAAa;AAAA,MACvE,CAAO;AAED,cAAQ;AAAA,QACN,QAAQ;AAAA,UACN,KAAK,WAAW,SAAU,KAAK;AAC7B,mBAAO,IAAI,wBAAwB,IAAI,qBAAqB,eAAe,cAAc;AAAA,UACrG,CAAW;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAED,QAAI,YAAY,gBAAgB,MAAM;AACpC,qBAAe,OAAO;AAAA,IACvB;AAED,UAAM,YAAY,YAAY,aAAa,YAAY;AAEvD,QAAI,cAAc,YAAY,OAAO;AACnC,qBAAe,cAAc;AAG7B,qBAAe,aAAa;AAAA,IAClC,OAAW;AACL,qBAAe,cAAc;AAE7B,UAAI,cAAc,YAAY,MAAM;AAClC,uBAAe,YAAY,YAAY,gBAAgB,SAAY,YAAY,cAAc;AAAA,MAC9F;AAAA,IACF;AAED,QAAI,YAAY,kBAAkB,UAAa,iBAAiB,mBAAmB;AACjF,cAAQ,KAAK,OAAO,cAAc,gBAAgB,aAAa,YAAY,aAAa,CAAC;AAEzF,qBAAe,cAAc,IAAI,QAAQ,GAAG,CAAC;AAE7C,UAAI,YAAY,cAAc,UAAU,QAAW;AACjD,cAAM,QAAQ,YAAY,cAAc;AAExC,uBAAe,YAAY,IAAI,OAAO,KAAK;AAAA,MAC5C;AAAA,IACF;AAED,QAAI,YAAY,qBAAqB,UAAa,iBAAiB,mBAAmB;AACpF,cAAQ,KAAK,OAAO,cAAc,gBAAgB,SAAS,YAAY,gBAAgB,CAAC;AAExF,UAAI,YAAY,iBAAiB,aAAa,QAAW;AACvD,uBAAe,iBAAiB,YAAY,iBAAiB;AAAA,MAC9D;AAAA,IACF;AAED,QAAI,YAAY,mBAAmB,UAAa,iBAAiB,mBAAmB;AAClF,YAAM,iBAAiB,YAAY;AACnC,qBAAe,WAAW,IAAI,MAAK,EAAG;AAAA,QACpC,eAAe,CAAC;AAAA,QAChB,eAAe,CAAC;AAAA,QAChB,eAAe,CAAC;AAAA,QAChB;AAAA,MACD;AAAA,IACF;AAED,QAAI,YAAY,oBAAoB,UAAa,iBAAiB,mBAAmB;AACnF,cAAQ,KAAK,OAAO,cAAc,gBAAgB,eAAe,YAAY,iBAAiB,cAAc,CAAC;AAAA,IAC9G;AAED,WAAO,QAAQ,IAAI,OAAO,EAAE,KAAK,WAAY;AAC3C,YAAM,WAAW,IAAI,aAAa,cAAc;AAEhD,UAAI,YAAY;AAAM,iBAAS,OAAO,YAAY;AAElD,6BAAuB,UAAU,WAAW;AAE5C,aAAO,aAAa,IAAI,UAAU,EAAE,WAAW,eAAe;AAE9D,UAAI,YAAY;AAAY,uCAA+B,YAAY,UAAU,WAAW;AAE5F,aAAO;AAAA,IACb,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,iBAAiB,cAAc;AAC7B,UAAM,gBAAgB,gBAAgB,iBAAiB,gBAAgB,EAAE;AAEzE,QAAI,iBAAiB,KAAK,eAAe;AACvC,aAAO,gBAAgB,MAAM,EAAE,KAAK,cAAc,aAAa;AAAA,IACrE,OAAW;AACL,WAAK,cAAc,aAAa,IAAI;AAEpC,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,eAAe,YAAY;AACzB,UAAM,SAAS;AACf,UAAM,aAAa,KAAK;AACxB,UAAM,QAAQ,KAAK;AAEnB,aAAS,qBAAqB,WAAW;AACvC,aAAO,WAAW,WAAW,0BAA0B,EACpD,gBAAgB,WAAW,MAAM,EACjC,KAAK,SAAU,UAAU;AACxB,eAAO,uBAAuB,UAAU,WAAW,MAAM;AAAA,MACnE,CAAS;AAAA,IACJ;AAED,UAAM,UAAU,CAAE;AAElB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAK;AACnD,YAAM,YAAY,WAAW,CAAC;AAC9B,YAAM,WAAW,mBAAmB,SAAS;AAG7C,YAAM,SAAS,MAAM,QAAQ;AAE7B,UAAI,QAAQ;AAEV,gBAAQ,KAAK,OAAO,OAAO;AAAA,MACnC,OAAa;AACL,YAAI;AAEJ,YAAI,UAAU,cAAc,UAAU,WAAW,WAAW,0BAA0B,GAAG;AAEvF,4BAAkB,qBAAqB,SAAS;AAAA,QAC1D,OAAe;AAEL,4BAAkB,uBAAuB,IAAI,eAAc,GAAI,WAAW,MAAM;AAAA,QACjF;AAGD,cAAM,QAAQ,IAAI,EAAE,WAAsB,SAAS,gBAAiB;AAEpE,gBAAQ,KAAK,eAAe;AAAA,MAC7B;AAAA,IACF;AAED,WAAO,QAAQ,IAAI,OAAO;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,SAAS,WAAW;AAClB,UAAM,SAAS;AACf,UAAM,OAAO,KAAK;AAClB,UAAM,aAAa,KAAK;AAExB,UAAM,UAAU,KAAK,OAAO,SAAS;AACrC,UAAM,aAAa,QAAQ;AAE3B,UAAM,UAAU,CAAE;AAElB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAK;AACnD,YAAM,WACJ,WAAW,CAAC,EAAE,aAAa,SACvB,sBAAsB,KAAK,KAAK,IAChC,KAAK,cAAc,YAAY,WAAW,CAAC,EAAE,QAAQ;AAE3D,cAAQ,KAAK,QAAQ;AAAA,IACtB;AAED,YAAQ,KAAK,OAAO,eAAe,UAAU,CAAC;AAE9C,WAAO,QAAQ,IAAI,OAAO,EAAE,KAAK,SAAU,SAAS;AAClD,YAAM,YAAY,QAAQ,MAAM,GAAG,QAAQ,SAAS,CAAC;AACrD,YAAM,aAAa,QAAQ,QAAQ,SAAS,CAAC;AAE7C,YAAM,SAAS,CAAE;AAEjB,eAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAK;AACnD,cAAM,WAAW,WAAW,CAAC;AAC7B,cAAM,YAAY,WAAW,CAAC;AAI9B,YAAI;AAEJ,cAAM,WAAW,UAAU,CAAC;AAE5B,YACE,UAAU,SAAS,gBAAgB,aACnC,UAAU,SAAS,gBAAgB,kBACnC,UAAU,SAAS,gBAAgB,gBACnC,UAAU,SAAS,QACnB;AAEA,iBAAO,QAAQ,kBAAkB,OAAO,IAAI,YAAY,UAAU,QAAQ,IAAI,IAAI,KAAK,UAAU,QAAQ;AAEzG,cAAI,KAAK,kBAAkB,MAAM;AAE/B,iBAAK,qBAAsB;AAAA,UAC5B;AAED,cAAI,UAAU,SAAS,gBAAgB,gBAAgB;AACrD,iBAAK,WAAW,oBAAoB,KAAK,UAAU,qBAAqB;AAAA,UACzE,WAAU,UAAU,SAAS,gBAAgB,cAAc;AAC1D,iBAAK,WAAW,oBAAoB,KAAK,UAAU,mBAAmB;AAAA,UACvE;AAAA,QACF,WAAU,UAAU,SAAS,gBAAgB,OAAO;AACnD,iBAAO,IAAI,aAAa,UAAU,QAAQ;AAAA,QAC3C,WAAU,UAAU,SAAS,gBAAgB,YAAY;AACxD,iBAAO,IAAI,KAAK,UAAU,QAAQ;AAAA,QACnC,WAAU,UAAU,SAAS,gBAAgB,WAAW;AACvD,iBAAO,IAAI,SAAS,UAAU,QAAQ;AAAA,QACvC,WAAU,UAAU,SAAS,gBAAgB,QAAQ;AACpD,iBAAO,IAAI,OAAO,UAAU,QAAQ;AAAA,QAC9C,OAAe;AACL,gBAAM,IAAI,MAAM,mDAAmD,UAAU,IAAI;AAAA,QAClF;AAED,YAAI,OAAO,KAAK,KAAK,SAAS,eAAe,EAAE,SAAS,GAAG;AACzD,6BAAmB,MAAM,OAAO;AAAA,QACjC;AAED,aAAK,OAAO,OAAO,iBAAiB,QAAQ,QAAQ,UAAU,SAAS;AAEvE,+BAAuB,MAAM,OAAO;AAEpC,YAAI,UAAU;AAAY,yCAA+B,YAAY,MAAM,SAAS;AAEpF,eAAO,oBAAoB,IAAI;AAE/B,eAAO,KAAK,IAAI;AAAA,MACjB;AAED,eAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AAC/C,eAAO,aAAa,IAAI,OAAO,CAAC,GAAG;AAAA,UACjC,QAAQ;AAAA,UACR,YAAY;AAAA,QACtB,CAAS;AAAA,MACF;AAED,UAAI,OAAO,WAAW,GAAG;AACvB,YAAI,QAAQ;AAAY,yCAA+B,YAAY,OAAO,CAAC,GAAG,OAAO;AAErF,eAAO,OAAO,CAAC;AAAA,MAChB;AAED,YAAM,QAAQ,IAAI,MAAO;AAEzB,UAAI,QAAQ;AAAY,uCAA+B,YAAY,OAAO,OAAO;AAEjF,aAAO,aAAa,IAAI,OAAO,EAAE,QAAQ,WAAW;AAEpD,eAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AAC/C,cAAM,IAAI,OAAO,CAAC,CAAC;AAAA,MACpB;AAED,aAAO;AAAA,IACb,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,WAAW,aAAa;AACtB,QAAI;AACJ,UAAM,YAAY,KAAK,KAAK,QAAQ,WAAW;AAC/C,UAAM,SAAS,UAAU,UAAU,IAAI;AAEvC,QAAI,CAAC,QAAQ;AACX,cAAQ,KAAK,8CAA8C;AAC3D;AAAA,IACD;AAED,QAAI,UAAU,SAAS,eAAe;AACpC,eAAS,IAAI;AAAA,QACX,UAAU,SAAS,OAAO,IAAI;AAAA,QAC9B,OAAO,eAAe;AAAA,QACtB,OAAO,SAAS;AAAA,QAChB,OAAO,QAAQ;AAAA,MAChB;AAAA,IACP,WAAe,UAAU,SAAS,gBAAgB;AAC5C,eAAS,IAAI,mBAAmB,CAAC,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,CAAC,OAAO,MAAM,OAAO,OAAO,OAAO,IAAI;AAAA,IAChH;AAED,QAAI,UAAU;AAAM,aAAO,OAAO,KAAK,iBAAiB,UAAU,IAAI;AAEtE,2BAAuB,QAAQ,SAAS;AAExC,WAAO,QAAQ,QAAQ,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,SAAS,WAAW;AAClB,UAAM,UAAU,KAAK,KAAK,MAAM,SAAS;AAEzC,UAAM,UAAU,CAAE;AAElB,aAAS,IAAI,GAAG,KAAK,QAAQ,OAAO,QAAQ,IAAI,IAAI,KAAK;AACvD,cAAQ,KAAK,KAAK,iBAAiB,QAAQ,OAAO,CAAC,CAAC,CAAC;AAAA,IACtD;AAED,QAAI,QAAQ,wBAAwB,QAAW;AAC7C,cAAQ,KAAK,KAAK,cAAc,YAAY,QAAQ,mBAAmB,CAAC;AAAA,IAC9E,OAAW;AACL,cAAQ,KAAK,IAAI;AAAA,IAClB;AAED,WAAO,QAAQ,IAAI,OAAO,EAAE,KAAK,SAAU,SAAS;AAClD,YAAM,sBAAsB,QAAQ,IAAK;AACzC,YAAM,aAAa;AAKnB,YAAM,QAAQ,CAAE;AAChB,YAAM,eAAe,CAAE;AAEvB,eAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAK;AACnD,cAAM,YAAY,WAAW,CAAC;AAE9B,YAAI,WAAW;AACb,gBAAM,KAAK,SAAS;AAEpB,gBAAM,MAAM,IAAI,QAAS;AAEzB,cAAI,wBAAwB,MAAM;AAChC,gBAAI,UAAU,oBAAoB,OAAO,IAAI,EAAE;AAAA,UAChD;AAED,uBAAa,KAAK,GAAG;AAAA,QAC/B,OAAe;AACL,kBAAQ,KAAK,oDAAoD,QAAQ,OAAO,CAAC,CAAC;AAAA,QACnF;AAAA,MACF;AAED,aAAO,IAAI,SAAS,OAAO,YAAY;AAAA,IAC7C,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,cAAc,gBAAgB;AAC5B,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS;AAEf,UAAM,eAAe,KAAK,WAAW,cAAc;AACnD,UAAM,gBAAgB,aAAa,OAAO,aAAa,OAAO,eAAe;AAE7E,UAAM,eAAe,CAAE;AACvB,UAAM,wBAAwB,CAAE;AAChC,UAAM,yBAAyB,CAAE;AACjC,UAAM,kBAAkB,CAAE;AAC1B,UAAM,iBAAiB,CAAE;AAEzB,aAAS,IAAI,GAAG,KAAK,aAAa,SAAS,QAAQ,IAAI,IAAI,KAAK;AAC9D,YAAM,UAAU,aAAa,SAAS,CAAC;AACvC,YAAM,UAAU,aAAa,SAAS,QAAQ,OAAO;AACrD,YAAM,SAAS,QAAQ;AACvB,YAAM,OAAO,OAAO;AACpB,YAAM,QAAQ,aAAa,eAAe,SAAY,aAAa,WAAW,QAAQ,KAAK,IAAI,QAAQ;AACvG,YAAM,SAAS,aAAa,eAAe,SAAY,aAAa,WAAW,QAAQ,MAAM,IAAI,QAAQ;AAEzG,UAAI,OAAO,SAAS;AAAW;AAE/B,mBAAa,KAAK,KAAK,cAAc,QAAQ,IAAI,CAAC;AAClD,4BAAsB,KAAK,KAAK,cAAc,YAAY,KAAK,CAAC;AAChE,6BAAuB,KAAK,KAAK,cAAc,YAAY,MAAM,CAAC;AAClE,sBAAgB,KAAK,OAAO;AAC5B,qBAAe,KAAK,MAAM;AAAA,IAC3B;AAED,WAAO,QAAQ,IAAI;AAAA,MACjB,QAAQ,IAAI,YAAY;AAAA,MACxB,QAAQ,IAAI,qBAAqB;AAAA,MACjC,QAAQ,IAAI,sBAAsB;AAAA,MAClC,QAAQ,IAAI,eAAe;AAAA,MAC3B,QAAQ,IAAI,cAAc;AAAA,IAChC,CAAK,EAAE,KAAK,SAAU,cAAc;AAC9B,YAAM,QAAQ,aAAa,CAAC;AAC5B,YAAM,iBAAiB,aAAa,CAAC;AACrC,YAAM,kBAAkB,aAAa,CAAC;AACtC,YAAM,WAAW,aAAa,CAAC;AAC/B,YAAM,UAAU,aAAa,CAAC;AAE9B,YAAM,SAAS,CAAE;AAEjB,eAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AAC9C,cAAM,OAAO,MAAM,CAAC;AACpB,cAAM,gBAAgB,eAAe,CAAC;AACtC,cAAM,iBAAiB,gBAAgB,CAAC;AACxC,cAAM,UAAU,SAAS,CAAC;AAC1B,cAAM,SAAS,QAAQ,CAAC;AAExB,YAAI,SAAS;AAAW;AAExB,YAAI,KAAK,cAAc;AACrB,eAAK,aAAc;AAAA,QACpB;AAED,cAAM,gBAAgB,OAAO,uBAAuB,MAAM,eAAe,gBAAgB,SAAS,MAAM;AAExG,YAAI,eAAe;AACjB,mBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,mBAAO,KAAK,cAAc,CAAC,CAAC;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAED,aAAO,IAAI,cAAc,eAAe,QAAW,MAAM;AAAA,IAC/D,CAAK;AAAA,EACF;AAAA,EAED,eAAe,WAAW;AACxB,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS;AACf,UAAM,UAAU,KAAK,MAAM,SAAS;AAEpC,QAAI,QAAQ,SAAS;AAAW,aAAO;AAEvC,WAAO,OAAO,cAAc,QAAQ,QAAQ,IAAI,EAAE,KAAK,SAAU,MAAM;AACrE,YAAM,OAAO,OAAO,YAAY,OAAO,WAAW,QAAQ,MAAM,IAAI;AAGpE,UAAI,QAAQ,YAAY,QAAW;AACjC,aAAK,SAAS,SAAU,GAAG;AACzB,cAAI,CAAC,EAAE;AAAQ;AAEf,mBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,QAAQ,IAAI,IAAI,KAAK;AACxD,cAAE,sBAAsB,CAAC,IAAI,QAAQ,QAAQ,CAAC;AAAA,UAC/C;AAAA,QACX,CAAS;AAAA,MACF;AAED,aAAO;AAAA,IACb,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,SAAS,WAAW;AAClB,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS;AAEf,UAAM,UAAU,KAAK,MAAM,SAAS;AAEpC,UAAM,cAAc,OAAO,iBAAiB,SAAS;AAErD,UAAM,eAAe,CAAE;AACvB,UAAM,cAAc,QAAQ,YAAY,CAAE;AAE1C,aAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AACpD,mBAAa,KAAK,OAAO,cAAc,QAAQ,YAAY,CAAC,CAAC,CAAC;AAAA,IAC/D;AAED,UAAM,kBACJ,QAAQ,SAAS,SAAY,QAAQ,QAAQ,IAAI,IAAI,OAAO,cAAc,QAAQ,QAAQ,IAAI;AAEhG,WAAO,QAAQ,IAAI,CAAC,aAAa,QAAQ,IAAI,YAAY,GAAG,eAAe,CAAC,EAAE,KAAK,SAAU,SAAS;AACpG,YAAM,OAAO,QAAQ,CAAC;AACtB,YAAM,WAAW,QAAQ,CAAC;AAC1B,YAAM,WAAW,QAAQ,CAAC;AAE1B,UAAI,aAAa,MAAM;AAGrB,aAAK,SAAS,SAAU,MAAM;AAC5B,cAAI,CAAC,KAAK;AAAe;AAEzB,eAAK,KAAK,UAAU,eAAe;AAAA,QAC7C,CAAS;AAAA,MACF;AAED,eAAS,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,KAAK;AACjD,aAAK,IAAI,SAAS,CAAC,CAAC;AAAA,MACrB;AAED,aAAO;AAAA,IACb,CAAK;AAAA,EACF;AAAA;AAAA;AAAA,EAID,iBAAiB,WAAW;AAC1B,UAAM,OAAO,KAAK;AAClB,UAAM,aAAa,KAAK;AACxB,UAAM,SAAS;AAKf,QAAI,KAAK,UAAU,SAAS,MAAM,QAAW;AAC3C,aAAO,KAAK,UAAU,SAAS;AAAA,IAChC;AAED,UAAM,UAAU,KAAK,MAAM,SAAS;AAGpC,UAAM,WAAW,QAAQ,OAAO,OAAO,iBAAiB,QAAQ,IAAI,IAAI;AAExE,UAAM,UAAU,CAAE;AAElB,UAAM,cAAc,OAAO,WAAW,SAAU,KAAK;AACnD,aAAO,IAAI,kBAAkB,IAAI,eAAe,SAAS;AAAA,IAC/D,CAAK;AAED,QAAI,aAAa;AACf,cAAQ,KAAK,WAAW;AAAA,IACzB;AAED,QAAI,QAAQ,WAAW,QAAW;AAChC,cAAQ;AAAA,QACN,OAAO,cAAc,UAAU,QAAQ,MAAM,EAAE,KAAK,SAAU,QAAQ;AACpE,iBAAO,OAAO,YAAY,OAAO,aAAa,QAAQ,QAAQ,MAAM;AAAA,QAC9E,CAAS;AAAA,MACF;AAAA,IACF;AAED,WACG,WAAW,SAAU,KAAK;AACzB,aAAO,IAAI,wBAAwB,IAAI,qBAAqB,SAAS;AAAA,IAC7E,CAAO,EACA,QAAQ,SAAU,SAAS;AAC1B,cAAQ,KAAK,OAAO;AAAA,IAC5B,CAAO;AAEH,SAAK,UAAU,SAAS,IAAI,QAAQ,IAAI,OAAO,EAAE,KAAK,SAAU,SAAS;AACvE,UAAI;AAGJ,UAAI,QAAQ,WAAW,MAAM;AAC3B,eAAO,IAAI,KAAM;AAAA,MACzB,WAAiB,QAAQ,SAAS,GAAG;AAC7B,eAAO,IAAI,MAAO;AAAA,MAC1B,WAAiB,QAAQ,WAAW,GAAG;AAC/B,eAAO,QAAQ,CAAC;AAAA,MACxB,OAAa;AACL,eAAO,IAAI,SAAU;AAAA,MACtB;AAED,UAAI,SAAS,QAAQ,CAAC,GAAG;AACvB,iBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAChD,eAAK,IAAI,QAAQ,CAAC,CAAC;AAAA,QACpB;AAAA,MACF;AAED,UAAI,QAAQ,MAAM;AAChB,aAAK,SAAS,OAAO,QAAQ;AAC7B,aAAK,OAAO;AAAA,MACb;AAED,6BAAuB,MAAM,OAAO;AAEpC,UAAI,QAAQ;AAAY,uCAA+B,YAAY,MAAM,OAAO;AAEhF,UAAI,QAAQ,WAAW,QAAW;AAChC,cAAM,SAAS,IAAI,QAAS;AAC5B,eAAO,UAAU,QAAQ,MAAM;AAC/B,aAAK,aAAa,MAAM;AAAA,MAChC,OAAa;AACL,YAAI,QAAQ,gBAAgB,QAAW;AACrC,eAAK,SAAS,UAAU,QAAQ,WAAW;AAAA,QAC5C;AAED,YAAI,QAAQ,aAAa,QAAW;AAClC,eAAK,WAAW,UAAU,QAAQ,QAAQ;AAAA,QAC3C;AAED,YAAI,QAAQ,UAAU,QAAW;AAC/B,eAAK,MAAM,UAAU,QAAQ,KAAK;AAAA,QACnC;AAAA,MACF;AAED,UAAI,CAAC,OAAO,aAAa,IAAI,IAAI,GAAG;AAClC,eAAO,aAAa,IAAI,MAAM,CAAA,CAAE;AAAA,MACjC;AAED,aAAO,aAAa,IAAI,IAAI,EAAE,QAAQ;AAEtC,aAAO;AAAA,IACb,CAAK;AAED,WAAO,KAAK,UAAU,SAAS;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,UAAU,YAAY;AACpB,UAAM,aAAa,KAAK;AACxB,UAAM,WAAW,KAAK,KAAK,OAAO,UAAU;AAC5C,UAAM,SAAS;AAIf,UAAM,QAAQ,IAAI,MAAO;AACzB,QAAI,SAAS;AAAM,YAAM,OAAO,OAAO,iBAAiB,SAAS,IAAI;AAErE,2BAAuB,OAAO,QAAQ;AAEtC,QAAI,SAAS;AAAY,qCAA+B,YAAY,OAAO,QAAQ;AAEnF,UAAM,UAAU,SAAS,SAAS,CAAE;AAEpC,UAAM,UAAU,CAAE;AAElB,aAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAChD,cAAQ,KAAK,OAAO,cAAc,QAAQ,QAAQ,CAAC,CAAC,CAAC;AAAA,IACtD;AAED,WAAO,QAAQ,IAAI,OAAO,EAAE,KAAK,SAAU,OAAO;AAChD,eAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AAC9C,cAAM,IAAI,MAAM,CAAC,CAAC;AAAA,MACnB;AAID,YAAM,qBAAqB,CAAC,SAAS;AACnC,cAAM,sBAAsB,oBAAI,IAAK;AAErC,mBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,cAAc;AAC9C,cAAI,eAAe,YAAY,eAAe,SAAS;AACrD,gCAAoB,IAAI,KAAK,KAAK;AAAA,UACnC;AAAA,QACF;AAED,aAAK,SAAS,CAACC,UAAS;AACtB,gBAAM,WAAW,OAAO,aAAa,IAAIA,KAAI;AAE7C,cAAI,YAAY,MAAM;AACpB,gCAAoB,IAAIA,OAAM,QAAQ;AAAA,UACvC;AAAA,QACX,CAAS;AAED,eAAO;AAAA,MACR;AAED,aAAO,eAAe,mBAAmB,KAAK;AAE9C,aAAO;AAAA,IACb,CAAK;AAAA,EACF;AAAA,EAED,uBAAuB,MAAM,eAAe,gBAAgB,SAAS,QAAQ;AAC3E,UAAM,SAAS,CAAE;AAEjB,UAAM,aAAa,KAAK,OAAO,KAAK,OAAO,KAAK;AAChD,UAAM,cAAc,CAAE;AAEtB,QAAI,gBAAgB,OAAO,IAAI,MAAM,gBAAgB,SAAS;AAC5D,WAAK,SAAS,SAAU,QAAQ;AAC9B,YAAI,OAAO,uBAAuB;AAChC,sBAAY,KAAK,OAAO,OAAO,OAAO,OAAO,OAAO,IAAI;AAAA,QACzD;AAAA,MACT,CAAO;AAAA,IACP,OAAW;AACL,kBAAY,KAAK,UAAU;AAAA,IAC5B;AAED,QAAI;AAEJ,YAAQ,gBAAgB,OAAO,IAAI,GAAC;AAAA,MAClC,KAAK,gBAAgB;AACnB,6BAAqB;AACrB;AAAA,MAEF,KAAK,gBAAgB;AACnB,6BAAqB;AACrB;AAAA,MAEF,KAAK,gBAAgB;AAAA,MACrB,KAAK,gBAAgB;AACnB,6BAAqB;AACrB;AAAA,MAEF;AACE,gBAAQ,eAAe,UAAQ;AAAA,UAC7B,KAAK;AACH,iCAAqB;AACrB;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AACE,iCAAqB;AACrB;AAAA,QACH;AAED;AAAA,IACH;AAED,UAAM,gBAAgB,QAAQ,kBAAkB,SAAY,cAAc,QAAQ,aAAa,IAAI;AAEnG,UAAM,cAAc,KAAK,sBAAsB,cAAc;AAE7D,aAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AACpD,YAAM,QAAQ,IAAI;AAAA,QAChB,YAAY,CAAC,IAAI,MAAM,gBAAgB,OAAO,IAAI;AAAA,QAClD,cAAc;AAAA,QACd;AAAA,QACA;AAAA,MACD;AAGD,UAAI,QAAQ,kBAAkB,eAAe;AAC3C,aAAK,mCAAmC,KAAK;AAAA,MAC9C;AAED,aAAO,KAAK,KAAK;AAAA,IAClB;AAED,WAAO;AAAA,EACR;AAAA,EAED,sBAAsB,UAAU;AAC9B,QAAI,cAAc,SAAS;AAE3B,QAAI,SAAS,YAAY;AACvB,YAAM,QAAQ,4BAA4B,YAAY,WAAW;AACjE,YAAM,SAAS,IAAI,aAAa,YAAY,MAAM;AAElD,eAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AACpD,eAAO,CAAC,IAAI,YAAY,CAAC,IAAI;AAAA,MAC9B;AAED,oBAAc;AAAA,IACf;AAED,WAAO;AAAA,EACR;AAAA,EAED,mCAAmC,OAAO;AACxC,UAAM,oBAAoB,SAAS,wCAAwC,QAAQ;AAKjF,YAAM,kBACJ,gBAAgB,0BAA0B,uCAAuC;AAEnF,aAAO,IAAI,gBAAgB,KAAK,OAAO,KAAK,QAAQ,KAAK,aAAY,IAAK,GAAG,MAAM;AAAA,IACpF;AAGD,UAAM,kBAAkB,4CAA4C;AAAA,EACrE;AACH;AAOA,SAAS,cAAc,UAAU,cAAc,QAAQ;AACrD,QAAM,aAAa,aAAa;AAEhC,QAAM,MAAM,IAAI,KAAM;AAEtB,MAAI,WAAW,aAAa,QAAW;AACrC,UAAM,WAAW,OAAO,KAAK,UAAU,WAAW,QAAQ;AAE1D,UAAM,MAAM,SAAS;AACrB,UAAM,MAAM,SAAS;AAIrB,QAAI,QAAQ,UAAa,QAAQ,QAAW;AAC1C,UAAI,IAAI,IAAI,QAAQ,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,QAAQ,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAEhF,UAAI,SAAS,YAAY;AACvB,cAAM,WAAW,4BAA4B,sBAAsB,SAAS,aAAa,CAAC;AAC1F,YAAI,IAAI,eAAe,QAAQ;AAC/B,YAAI,IAAI,eAAe,QAAQ;AAAA,MAChC;AAAA,IACP,OAAW;AACL,cAAQ,KAAK,qEAAqE;AAElF;AAAA,IACD;AAAA,EACL,OAAS;AACL;AAAA,EACD;AAED,QAAM,UAAU,aAAa;AAE7B,MAAI,YAAY,QAAW;AACzB,UAAM,kBAAkB,IAAI,QAAS;AACrC,UAAM,SAAS,IAAI,QAAS;AAE5B,aAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAChD,YAAM,SAAS,QAAQ,CAAC;AAExB,UAAI,OAAO,aAAa,QAAW;AACjC,cAAM,WAAW,OAAO,KAAK,UAAU,OAAO,QAAQ;AACtD,cAAM,MAAM,SAAS;AACrB,cAAM,MAAM,SAAS;AAIrB,YAAI,QAAQ,UAAa,QAAQ,QAAW;AAE1C,iBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACxD,iBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACxD,iBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AAExD,cAAI,SAAS,YAAY;AACvB,kBAAM,WAAW,4BAA4B,sBAAsB,SAAS,aAAa,CAAC;AAC1F,mBAAO,eAAe,QAAQ;AAAA,UAC/B;AAMD,0BAAgB,IAAI,MAAM;AAAA,QACpC,OAAe;AACL,kBAAQ,KAAK,qEAAqE;AAAA,QACnF;AAAA,MACF;AAAA,IACF;AAGD,QAAI,eAAe,eAAe;AAAA,EACnC;AAED,WAAS,cAAc;AAEvB,QAAM,SAAS,IAAI,OAAQ;AAE3B,MAAI,UAAU,OAAO,MAAM;AAC3B,SAAO,SAAS,IAAI,IAAI,WAAW,IAAI,GAAG,IAAI;AAE9C,WAAS,iBAAiB;AAC5B;AAQA,SAAS,uBAAuB,UAAU,cAAc,QAAQ;AAC9D,QAAM,aAAa,aAAa;AAEhC,QAAM,UAAU,CAAE;AAElB,WAAS,wBAAwB,eAAe,eAAe;AAC7D,WAAO,OAAO,cAAc,YAAY,aAAa,EAAE,KAAK,SAAU,UAAU;AAC9E,eAAS,aAAa,eAAe,QAAQ;AAAA,IACnD,CAAK;AAAA,EACF;AAED,aAAW,qBAAqB,YAAY;AAC1C,UAAM,qBAAqB,WAAW,iBAAiB,KAAK,kBAAkB,YAAa;AAG3F,QAAI,sBAAsB,SAAS;AAAY;AAE/C,YAAQ,KAAK,wBAAwB,WAAW,iBAAiB,GAAG,kBAAkB,CAAC;AAAA,EACxF;AAED,MAAI,aAAa,YAAY,UAAa,CAAC,SAAS,OAAO;AACzD,UAAM,WAAW,OAAO,cAAc,YAAY,aAAa,OAAO,EAAE,KAAK,SAAUC,WAAU;AAC/F,eAAS,SAASA,SAAQ;AAAA,IAChC,CAAK;AAED,YAAQ,KAAK,QAAQ;AAAA,EACtB;AAED,yBAAuB,UAAU,YAAY;AAE7C,gBAAc,UAAU,cAAc,MAAM;AAE5C,SAAO,QAAQ,IAAI,OAAO,EAAE,KAAK,WAAY;AAC3C,WAAO,aAAa,YAAY,SAAY,gBAAgB,UAAU,aAAa,SAAS,MAAM,IAAI;AAAA,EAC1G,CAAG;AACH;"}