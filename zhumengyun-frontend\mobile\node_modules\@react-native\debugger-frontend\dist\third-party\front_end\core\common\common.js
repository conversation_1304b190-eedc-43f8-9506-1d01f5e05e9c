import*as t from"../root/root.js";import*as e from"../platform/platform.js";export{UIString}from"../platform/platform.js";import*as r from"../i18n/i18n.js";var s=Object.freeze({__proto__:null});const n=[];var i=Object.freeze({__proto__:null,registerAppProvider:function(t){n.push(t)},getRegisteredAppProviders:function(){return n.filter((e=>t.Runtime.Runtime.isDescriptorEnabled({experiment:void 0,condition:e.condition}))).sort(((t,e)=>(t.order||0)-(e.order||0)))}});const a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=new Uint8Array(123);for(let t=0;t<64;++t)o[a.charCodeAt(t)]=t;var l=Object.freeze({__proto__:null,BASE64_CHARS:a,BASE64_CODES:o,decode:function(t){let e=3*t.length/4>>>0;61===t.charCodeAt(t.length-2)?e-=2:61===t.charCodeAt(t.length-1)&&(e-=1);const r=new Uint8Array(e);for(let e=0,s=0;e<t.length;e+=4){const n=o[t.charCodeAt(e+0)],i=o[t.charCodeAt(e+1)],a=o[t.charCodeAt(e+2)],l=o[t.charCodeAt(e+3)];r[s++]=n<<2|i>>4,r[s++]=(15&i)<<4|a>>2,r[s++]=(3&a)<<6|63&l}return r.buffer},encode:function(t){return new Promise(((e,r)=>{const s=new FileReader;s.onerror=()=>r(new Error("failed to convert to base64")),s.onload=()=>{const t=s.result,[,r]=t.split(",",2);e(r)},s.readAsDataURL(new Blob([t]))}))}});var h=Object.freeze({__proto__:null,CharacterIdMap:class{#t;#e;#r;constructor(){this.#t=new Map,this.#e=new Map,this.#r=33}toChar(t){let e=this.#t.get(t);if(!e){if(this.#r>=65535)throw new Error("CharacterIdMap ran out of capacity!");e=String.fromCharCode(this.#r++),this.#t.set(t,e),this.#e.set(e,t)}return e}fromChar(t){const e=this.#e.get(t);return void 0===e?null:e}}});const c=.9642,u=.8251;class g{values=[0,0,0];constructor(t){t&&(this.values=t)}}class d{values=[[0,0,0],[0,0,0],[0,0,0]];constructor(t){t&&(this.values=t)}multiply(t){const e=new g;for(let r=0;r<3;++r)e.values[r]=this.values[r][0]*t.values[0]+this.values[r][1]*t.values[1]+this.values[r][2]*t.values[2];return e}}class p{g;a;b;c;d;e;f;constructor(t,e,r=0,s=0,n=0,i=0,a=0){this.g=t,this.a=e,this.b=r,this.c=s,this.d=n,this.e=i,this.f=a}eval(t){const e=t<0?-1:1,r=t*e;return r<this.d?e*(this.c*r+this.f):e*(Math.pow(this.a*r+this.b,this.g)+this.e)}}const m={sRGB:new p(2.4,1/1.055,.055/1.055,1/12.92,.04045,0,0),sRGB_INVERSE:new p(.416667,1.13728,-0,12.92,.0031308,-.0549698,-0),proPhotoRGB:new p(1.8,1),proPhotoRGB_INVERSE:new p(.555556,1,-0,0,0,0,0),k2Dot2:new p(2.2,1),k2Dot2_INVERSE:new p(.454545,1),rec2020:new p(2.22222,.909672,.0903276,.222222,.0812429,0,0),rec2020_INVERSE:new p(.45,1.23439,-0,4.5,.018054,-.0993195,-0)},y={sRGB:new d([[.436065674,.385147095,.143066406],[.222488403,.716873169,.06060791],[.013916016,.097076416,.714096069]]),sRGB_INVERSE:new d([[3.134112151374599,-1.6173924597114966,-.4906334036481285],[-.9787872938826594,1.9162795854799963,.0334547139520088],[.07198304248352326,-.2289858493321844,1.4053851325241447]]),displayP3:new d([[.515102,.291965,.157153],[.241182,.692236,.0665819],[-.00104941,.0418818,.784378]]),displayP3_INVERSE:new d([[2.404045155982687,-.9898986932663839,-.3976317191366333],[-.8422283799266768,1.7988505115115485,.016048170293157416],[.04818705979712955,-.09737385156228891,1.2735066448052303]]),adobeRGB:new d([[.60974,.20528,.14919],[.31111,.62567,.06322],[.01947,.06087,.74457]]),adobeRGB_INVERSE:new d([[1.9625385510109137,-.6106892546501431,-.3413827467482388],[-.9787580455521,1.9161624707082339,.03341676594241408],[.028696263137883395,-.1406807819331586,1.349252109991369]]),rec2020:new d([[.673459,.165661,.1251],[.279033,.675338,.0456288],[-.00193139,.0299794,.797162]]),rec2020_INVERSE:new d([[1.647275201661012,-.3936024771460771,-.23598028884792507],[-.6826176165196962,1.647617775014935,.01281626807852422],[.029662725298529837,-.06291668721366285,1.2533964313435522]]),xyz:new d([[1,0,0],[0,1,0],[0,0,1]])};function f(t){return t*(Math.PI/180)}function w(t,e,r,s){return[t.eval(e),t.eval(r),t.eval(s)]}const b=new d([[.9999999984505198,.39633779217376786,.2158037580607588],[1.0000000088817609,-.10556134232365635,-.06385417477170591],[1.0000000546724108,-.08948418209496575,-1.2914855378640917]]),S=new d([[.2104542553,.7936177849999999,-.0040720468],[1.9779984951000003,-2.4285922049999997,.4505937099000001],[.025904037099999982,.7827717662,-.8086757660000001]]),x=new d([[.8190224432164319,.3619062562801221,-.12887378261216414],[.0329836671980271,.9292868468965546,.03614466816999844],[.048177199566046255,.26423952494422764,.6335478258136937]]),v=new d([[1.226879873374156,-.5578149965554814,.2813910501772159],[-.040575762624313734,1.1122868293970596,-.07171106666151703],[-.07637294974672144,-.4214933239627915,1.586924024427242]]),T=new d([[.7976700747153241,.13519395152800417,.03135596341127167],[.28803902352472205,.7118744007923554,8661179538844252e-20],[2.739876695467402e-7,-14405226518969991e-22,.825211112593861]]),R=new d([[1.3459533710138858,-.25561367037652133,-.051116041522131374],[-.544600415668951,1.5081687311475767,.020535163968720935],[-13975622054109725e-22,2717590904589903e-21,1.2118111696814942]]),z=new d([[1.0478573189120088,.022907374491829943,-.050162247377152525],[.029570500050499514,.9904755577034089,-.017061518194840468],[-.00924047197558879,.015052921526981566,.7519708530777581]]),I=new d([[.9555366447632887,-.02306009252137888,.06321844147263304],[-.028315378228764922,1.009951351591575,.021026001591792402],[.012308773293784308,-.02050053471777469,1.3301947294775631]]),P=new d([[3.2408089365140573,-1.5375788839307314,-.4985609572551541],[-.9692732213205414,1.876110235238969,.041560501141251774],[.05567030990267439,-.2040007921971802,1.0571046720577026]]);class A{static labToXyzd50(t,e,r){let s=(t+16)/116,n=s+e/500,i=s-r/200;function a(t){return t<=24/116?108/841*(t-16/116):t*t*t}return n=a(n)*c,s=1*a(s),i=a(i)*u,[n,s,i]}static xyzd50ToLab(t,e,r){function s(t){return t<=.008856451679035631?841/108*t+16/116:Math.pow(t,1/3)}t=s(t/c);return[116*(e=s(e/1))-16,500*(t-e),200*(e-(r=s(r/u)))]}static oklabToXyzd65(t,e,r){const s=new g([t,e,r]),n=b.multiply(s);n.values[0]=n.values[0]*n.values[0]*n.values[0],n.values[1]=n.values[1]*n.values[1]*n.values[1],n.values[2]=n.values[2]*n.values[2]*n.values[2];return v.multiply(n).values}static xyzd65ToOklab(t,e,r){const s=new g([t,e,r]),n=x.multiply(s);n.values[0]=Math.pow(n.values[0],1/3),n.values[1]=Math.pow(n.values[1],1/3),n.values[2]=Math.pow(n.values[2],1/3);const i=S.multiply(n);return[i.values[0],i.values[1],i.values[2]]}static lchToLab(t,e,r){return void 0===r?[t,0,0]:[t,e*Math.cos(f(r)),e*Math.sin(f(r))]}static labToLch(t,e,r){return[t,Math.sqrt(e*e+r*r),(s=Math.atan2(r,e),s*(180/Math.PI))];var s}static displayP3ToXyzd50(t,e,r){const[s,n,i]=w(m.sRGB,t,e,r),a=new g([s,n,i]);return y.displayP3.multiply(a).values}static xyzd50ToDisplayP3(t,e,r){const s=new g([t,e,r]),n=y.displayP3_INVERSE.multiply(s);return w(m.sRGB_INVERSE,n.values[0],n.values[1],n.values[2])}static proPhotoToXyzd50(t,e,r){const[s,n,i]=w(m.proPhotoRGB,t,e,r),a=new g([s,n,i]);return T.multiply(a).values}static xyzd50ToProPhoto(t,e,r){const s=new g([t,e,r]),n=R.multiply(s);return w(m.proPhotoRGB_INVERSE,n.values[0],n.values[1],n.values[2])}static adobeRGBToXyzd50(t,e,r){const[s,n,i]=w(m.k2Dot2,t,e,r),a=new g([s,n,i]);return y.adobeRGB.multiply(a).values}static xyzd50ToAdobeRGB(t,e,r){const s=new g([t,e,r]),n=y.adobeRGB_INVERSE.multiply(s);return w(m.k2Dot2_INVERSE,n.values[0],n.values[1],n.values[2])}static rec2020ToXyzd50(t,e,r){const[s,n,i]=w(m.rec2020,t,e,r),a=new g([s,n,i]);return y.rec2020.multiply(a).values}static xyzd50ToRec2020(t,e,r){const s=new g([t,e,r]),n=y.rec2020_INVERSE.multiply(s);return w(m.rec2020_INVERSE,n.values[0],n.values[1],n.values[2])}static xyzd50ToD65(t,e,r){const s=new g([t,e,r]);return I.multiply(s).values}static xyzd65ToD50(t,e,r){const s=new g([t,e,r]);return z.multiply(s).values}static xyzd65TosRGBLinear(t,e,r){const s=new g([t,e,r]);return P.multiply(s).values}static xyzd50TosRGBLinear(t,e,r){const s=new g([t,e,r]);return y.sRGB_INVERSE.multiply(s).values}static srgbLinearToXyzd50(t,e,r){const s=new g([t,e,r]);return y.sRGB.multiply(s).values}static srgbToXyzd50(t,e,r){const[s,n,i]=w(m.sRGB,t,e,r),a=new g([s,n,i]);return y.sRGB.multiply(a).values}static xyzd50ToSrgb(t,e,r){const s=new g([t,e,r]),n=y.sRGB_INVERSE.multiply(s);return w(m.sRGB_INVERSE,n.values[0],n.values[1],n.values[2])}static oklchToXyzd50(t,e,r){const[s,n,i]=A.lchToLab(t,e,r),[a,o,l]=A.oklabToXyzd65(s,n,i);return A.xyzd65ToD50(a,o,l)}static xyzd50ToOklch(t,e,r){const[s,n,i]=A.xyzd50ToD65(t,e,r),[a,o,l]=A.xyzd65ToOklab(s,n,i);return A.labToLch(a,o,l)}}var E=Object.freeze({__proto__:null,ColorConverter:A});function k(t,e){const r=t[3];return[(1-r)*e[0]+r*t[0],(1-r)*e[1]+r*t[1],(1-r)*e[2]+r*t[2],r+e[3]*(1-r)]}function C([t,e,r]){const s=Math.max(t,e,r),n=Math.min(t,e,r),i=s-n;let a;return a=n===s?0:t===s?(1/6*(e-r)/i+1)%1:e===s?1/6*(r-t)/i+1/3:1/6*(t-e)/i+2/3,a}function L(t){const[e,r,s]=_([...t,void 0]);return[e,r,s]}function _([t,e,r,s]){const n=Math.max(t,e,r),i=Math.min(t,e,r),a=n-i,o=n+i,l=.5*o;let h;return h=0===l||1===l?0:l<=.5?a/o:a/(2-o),[C([t,e,r]),h,l,s]}function O(t){const[e,r,s]=B([...t,void 0]);return[e,r,s]}function B([t,e,r,s]){const n=C([t,e,r]),i=Math.max(t,e,r);return[n,Math.min(t,e,r),1-i,s]}function N([t,e,r]){return.2126*(t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4))+.7152*(e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4))+.0722*(r<=.04045?r/12.92:Math.pow((r+.055)/1.055,2.4))}const V=2.4,G=.56,M=.57,W=.65,X=.62,F=.022,D=1.414,j=1.14,U=1.14,H=.027,$=.1,q=5e-4;function Z([t,e,r]){return.2126729*Math.pow(t,V)+.7151522*Math.pow(e,V)+.072175*Math.pow(r,V)}function Y(t,e){return J(Z(k(t,e)),Z(e))}function K(t){return t>F?t:t+Math.pow(F-t,D)}function J(t,e){if(t=K(t),e=K(e),Math.abs(t-e)<q)return 0;let r=0;return e>t?(r=(Math.pow(e,G)-Math.pow(t,M))*j,r=r<$?0:r-H):(r=(Math.pow(e,W)-Math.pow(t,X))*U,r=r>-$?0:r+H),100*r}function Q(t,e,r){function s(){return r?Math.pow(Math.abs(Math.pow(t,W)-(-e-H)/U),1/X):Math.pow(Math.abs(Math.pow(t,G)-(e+H)/j),1/M)}t=K(t),e/=100;let n=s();return(n<0||n>1)&&(r=!r,n=s()),n}const tt=[[12,-1,-1,-1,-1,100,90,80,-1,-1],[14,-1,-1,-1,100,90,80,60,60,-1],[16,-1,-1,100,90,80,60,55,50,50],[18,-1,-1,90,80,60,55,50,40,40],[24,-1,100,80,60,55,50,40,38,35],[30,-1,90,70,55,50,40,38,35,40],[36,-1,80,60,50,40,38,35,30,25],[48,100,70,55,40,38,35,30,25,20],[60,90,60,50,38,35,30,25,20,20],[72,80,55,40,35,30,25,20,20,20],[96,70,50,35,30,25,20,20,20,20],[120,60,40,30,25,20,20,20,20,20]];function et(t,e){const r=72*parseFloat(t.replace("px",""))/96;return(isNaN(Number(e))?["bold","bolder"].includes(e):Number(e)>=600)?r>=14:r>=18}tt.reverse();const rt={aa:3,aaa:4.5},st={aa:4.5,aaa:7};var nt=Object.freeze({__proto__:null,blendColors:k,rgbToHsl:L,rgbaToHsla:_,rgbToHwb:O,rgbaToHwba:B,luminance:N,contrastRatio:function(t,e){const r=N(k(t,e)),s=N(e);return(Math.max(r,s)+.05)/(Math.min(r,s)+.05)},luminanceAPCA:Z,contrastRatioAPCA:Y,contrastRatioByLuminanceAPCA:J,desiredLuminanceAPCA:Q,getAPCAThreshold:function(t,e){const r=parseFloat(t.replace("px","")),s=parseFloat(e);for(const[t,...e]of tt)if(r>=t)for(const[t,r]of[900,800,700,600,500,400,300,200,100].entries())if(s>=r){const r=e[e.length-1-t];return-1===r?null:r}return null},isLargeFont:et,getContrastThreshold:function(t,e){return et(t,e)?rt:st}});function it(t){return(t%360+360)%360}function at(t){const e=t.replace(/(deg|g?rad|turn)$/,"");if(isNaN(e)||t.match(/\s+(deg|g?rad|turn)/))return null;const r=parseFloat(e);return t.includes("turn")?360*r:t.includes("grad")?9*r/10:t.includes("rad")?180*r/Math.PI:r}function ot(t){switch(t){case"srgb":return"srgb";case"srgb-linear":return"srgb-linear";case"display-p3":return"display-p3";case"a98-rgb":return"a98-rgb";case"prophoto-rgb":return"prophoto-rgb";case"rec2020":return"rec2020";case"xyz":return"xyz";case"xyz-d50":return"xyz-d50";case"xyz-d65":return"xyz-d65"}return null}function lt(t,e){const r=Math.sign(t),s=Math.abs(t),[n,i]=e;return r*(s*(i-n)/100+n)}function ht(t,{min:e,max:r}){return null===t||(void 0!==e&&(t=Math.max(t,e)),void 0!==r&&(t=Math.min(t,r))),t}function ct(t,e){if(!t.endsWith("%"))return null;const r=parseFloat(t.substr(0,t.length-1));return isNaN(r)?null:lt(r,e)}function ut(t){const e=parseFloat(t);return isNaN(e)?null:e}function gt(t){return void 0===t?null:ht(ct(t,[0,1])??ut(t),{min:0,max:1})}function dt(t,e=[0,1]){if(isNaN(t.replace("%","")))return null;const r=parseFloat(t);return-1!==t.indexOf("%")?t.indexOf("%")!==t.length-1?null:lt(r,e):r}function pt(t){const e=dt(t);return null===e?null:-1!==t.indexOf("%")?e:e/255}function mt(t){const e=t.replace(/(deg|g?rad|turn)$/,"");if(isNaN(e)||t.match(/\s+(deg|g?rad|turn)/))return null;const r=parseFloat(e);return-1!==t.indexOf("turn")?r%1:-1!==t.indexOf("grad")?r/400%1:-1!==t.indexOf("rad")?r/(2*Math.PI)%1:r/360%1}function yt(t){if(t.indexOf("%")!==t.length-1||isNaN(t.replace("%","")))return null;return parseFloat(t)/100}function ft(t){const e=t[0];let r=t[1];const s=t[2];function n(t,e,r){return r<0?r+=1:r>1&&(r-=1),6*r<1?t+(e-t)*r*6:2*r<1?e:3*r<2?t+(e-t)*(2/3-r)*6:t}let i;r<0&&(r=0),i=s<=.5?s*(1+r):s+r-s*r;const a=2*s-i,o=e,l=e-1/3;return[n(a,i,e+1/3),n(a,i,o),n(a,i,l),t[3]]}function wt(t){return ft(function(t){const e=t[0];let r=t[1];const s=t[2],n=(2-r)*s;return 0===s||0===r?r=0:r*=s/(n<1?n:2-n),[e,r,n/2,t[3]]}(t))}function bt(t,e,r){function s(){return r?(t+.05)*e-.05:(t+.05)/e-.05}let n=s();return(n<0||n>1)&&(r=!r,n=s()),n}function St(t,e,r,s,n){let i=t[r],a=1,o=n(t)-s,l=Math.sign(o);for(let e=100;e;e--){if(Math.abs(o)<2e-4)return t[r]=i,i;const e=Math.sign(o);if(e!==l)a/=2,l=e;else if(i<0||i>1)return null;i+=a*(2===r?-o:o),t[r]=i,o=n(t)-s}return null}function xt(t,e,r=.01){if(Array.isArray(t)&&Array.isArray(e)){if(t.length!==e.length)return!1;for(const r in t)if(!xt(t[r],e[r]))return!1;return!0}return!Array.isArray(t)&&!Array.isArray(e)&&(null===t||null===e?t===e:Math.abs(t-e)<r)}function vt(t,e,r=.01){return t-e<=r}class Tt{l;a;b;alpha;#s;#n;static#i={hex:t=>new Ot(t.#a(!1),"hex"),hexa:t=>new Ot(t.#a(!0),"hexa"),rgb:t=>new Ot(t.#a(!1),"rgb"),rgba:t=>new Ot(t.#a(!0),"rgba"),hsl:t=>new At(...L(t.#a(!1)),t.alpha),hsla:t=>new At(...L(t.#a(!1)),t.alpha),hwb:t=>new Et(...O(t.#a(!1)),t.alpha),hwba:t=>new Et(...O(t.#a(!1)),t.alpha),lch:t=>new Rt(...A.labToLch(t.l,t.a,t.b),t.alpha),oklch:t=>new It(...A.xyzd50ToOklch(...t.#o()),t.alpha),lab:t=>t,oklab:t=>new zt(...A.xyzd65ToOklab(...A.xyzd50ToD65(...t.#o())),t.alpha),srgb:t=>new Pt("srgb",...A.xyzd50ToSrgb(...t.#o()),t.alpha),"srgb-linear":t=>new Pt("srgb-linear",...A.xyzd50TosRGBLinear(...t.#o()),t.alpha),"display-p3":t=>new Pt("display-p3",...A.xyzd50ToDisplayP3(...t.#o()),t.alpha),"a98-rgb":t=>new Pt("a98-rgb",...A.xyzd50ToAdobeRGB(...t.#o()),t.alpha),"prophoto-rgb":t=>new Pt("prophoto-rgb",...A.xyzd50ToProPhoto(...t.#o()),t.alpha),rec2020:t=>new Pt("rec2020",...A.xyzd50ToRec2020(...t.#o()),t.alpha),xyz:t=>new Pt("xyz",...A.xyzd50ToD65(...t.#o()),t.alpha),"xyz-d50":t=>new Pt("xyz-d50",...t.#o(),t.alpha),"xyz-d65":t=>new Pt("xyz-d65",...A.xyzd50ToD65(...t.#o()),t.alpha)};#o(){return A.labToXyzd50(this.l,this.a,this.b)}#a(t=!0){const e=A.xyzd50ToSrgb(...this.#o());return t?[...e,this.alpha??void 0]:e}constructor(t,e,r,s,n){this.#n=[t,e,r],this.l=ht(t,{min:0,max:100}),(xt(this.l,0,1)||xt(this.l,100,1))&&(e=r=0),this.a=e,this.b=r,this.alpha=ht(s,{min:0,max:1}),this.#s=n}is(t){return t===this.format()}as(t){return Tt.#i[t](this)}asLegacyColor(){return this.as("rgba")}equal(t){const e=t.as("lab");return xt(e.l,this.l,1)&&xt(e.a,this.a)&&xt(e.b,this.b)&&xt(e.alpha,this.alpha)}format(){return"lab"}setAlpha(t){return new Tt(this.l,this.a,this.b,t,void 0)}asString(t){return t?this.as(t).asString():this.#l(this.l,this.a,this.b)}#l(t,r,s){const n=null===this.alpha||xt(this.alpha,1)?"":` / ${e.StringUtilities.stringifyWithPrecision(this.alpha)}`;return`lab(${e.StringUtilities.stringifyWithPrecision(t,0)} ${e.StringUtilities.stringifyWithPrecision(r)} ${e.StringUtilities.stringifyWithPrecision(s)}${n})`}getAuthoredText(){return this.#s??null}getRawParameters(){return[...this.#n]}getAsRawString(t){return t?this.as(t).getAsRawString():this.#l(...this.#n)}isGamutClipped(){return!1}static fromSpec(t,e){const r=ct(t[0],[0,100])??ut(t[0]);if(null===r)return null;const s=ct(t[1],[0,125])??ut(t[1]);if(null===s)return null;const n=ct(t[2],[0,125])??ut(t[2]);if(null===n)return null;const i=gt(t[3]);return new Tt(r,s,n,i,e)}}class Rt{#n;l;c;h;alpha;#s;static#i={hex:t=>new Ot(t.#a(!1),"hex"),hexa:t=>new Ot(t.#a(!0),"hexa"),rgb:t=>new Ot(t.#a(!1),"rgb"),rgba:t=>new Ot(t.#a(!0),"rgba"),hsl:t=>new At(...L(t.#a(!1)),t.alpha),hsla:t=>new At(...L(t.#a(!1)),t.alpha),hwb:t=>new Et(...O(t.#a(!1)),t.alpha),hwba:t=>new Et(...O(t.#a(!1)),t.alpha),lch:t=>t,oklch:t=>new It(...A.xyzd50ToOklch(...t.#o()),t.alpha),lab:t=>new Tt(...A.lchToLab(t.l,t.c,t.h),t.alpha),oklab:t=>new zt(...A.xyzd65ToOklab(...A.xyzd50ToD65(...t.#o())),t.alpha),srgb:t=>new Pt("srgb",...A.xyzd50ToSrgb(...t.#o()),t.alpha),"srgb-linear":t=>new Pt("srgb-linear",...A.xyzd50TosRGBLinear(...t.#o()),t.alpha),"display-p3":t=>new Pt("display-p3",...A.xyzd50ToDisplayP3(...t.#o()),t.alpha),"a98-rgb":t=>new Pt("a98-rgb",...A.xyzd50ToAdobeRGB(...t.#o()),t.alpha),"prophoto-rgb":t=>new Pt("prophoto-rgb",...A.xyzd50ToProPhoto(...t.#o()),t.alpha),rec2020:t=>new Pt("rec2020",...A.xyzd50ToRec2020(...t.#o()),t.alpha),xyz:t=>new Pt("xyz",...A.xyzd50ToD65(...t.#o()),t.alpha),"xyz-d50":t=>new Pt("xyz-d50",...t.#o(),t.alpha),"xyz-d65":t=>new Pt("xyz-d65",...A.xyzd50ToD65(...t.#o()),t.alpha)};#o(){return A.labToXyzd50(...A.lchToLab(this.l,this.c,this.h))}#a(t=!0){const e=A.xyzd50ToSrgb(...this.#o());return t?[...e,this.alpha??void 0]:e}constructor(t,e,r,s,n){this.#n=[t,e,r],this.l=ht(t,{min:0,max:100}),e=xt(this.l,0,1)||xt(this.l,100,1)?0:e,this.c=ht(e,{min:0}),r=xt(e,0)?0:r,this.h=it(r),this.alpha=ht(s,{min:0,max:1}),this.#s=n}asLegacyColor(){return this.as("rgba")}is(t){return t===this.format()}as(t){return Rt.#i[t](this)}equal(t){const e=t.as("lch");return xt(e.l,this.l,1)&&xt(e.c,this.c)&&xt(e.h,this.h)&&xt(e.alpha,this.alpha)}format(){return"lch"}setAlpha(t){return new Rt(this.l,this.c,this.h,t)}asString(t){return t?this.as(t).asString():this.#l(this.l,this.c,this.h)}#l(t,r,s){const n=null===this.alpha||xt(this.alpha,1)?"":` / ${e.StringUtilities.stringifyWithPrecision(this.alpha)}`;return`lch(${e.StringUtilities.stringifyWithPrecision(t,0)} ${e.StringUtilities.stringifyWithPrecision(r)} ${e.StringUtilities.stringifyWithPrecision(s)}${n})`}getAuthoredText(){return this.#s??null}getRawParameters(){return[...this.#n]}getAsRawString(t){return t?this.as(t).getAsRawString():this.#l(...this.#n)}isGamutClipped(){return!1}isHuePowerless(){return xt(this.c,0)}static fromSpec(t,e){const r=ct(t[0],[0,100])??ut(t[0]);if(null===r)return null;const s=ct(t[1],[0,150])??ut(t[1]);if(null===s)return null;const n=at(t[2]);if(null===n)return null;const i=gt(t[3]);return new Rt(r,s,n,i,e)}}class zt{#n;l;a;b;alpha;#s;static#i={hex:t=>new Ot(t.#a(!1),"hex"),hexa:t=>new Ot(t.#a(!0),"hexa"),rgb:t=>new Ot(t.#a(!1),"rgb"),rgba:t=>new Ot(t.#a(!0),"rgba"),hsl:t=>new At(...L(t.#a(!1)),t.alpha),hsla:t=>new At(...L(t.#a(!1)),t.alpha),hwb:t=>new Et(...O(t.#a(!1)),t.alpha),hwba:t=>new Et(...O(t.#a(!1)),t.alpha),lch:t=>new Rt(...A.labToLch(...A.xyzd50ToLab(...t.#o())),t.alpha),oklch:t=>new It(...A.xyzd50ToOklch(...t.#o()),t.alpha),lab:t=>new Tt(...A.xyzd50ToLab(...t.#o()),t.alpha),oklab:t=>t,srgb:t=>new Pt("srgb",...A.xyzd50ToSrgb(...t.#o()),t.alpha),"srgb-linear":t=>new Pt("srgb-linear",...A.xyzd50TosRGBLinear(...t.#o()),t.alpha),"display-p3":t=>new Pt("display-p3",...A.xyzd50ToDisplayP3(...t.#o()),t.alpha),"a98-rgb":t=>new Pt("a98-rgb",...A.xyzd50ToAdobeRGB(...t.#o()),t.alpha),"prophoto-rgb":t=>new Pt("prophoto-rgb",...A.xyzd50ToProPhoto(...t.#o()),t.alpha),rec2020:t=>new Pt("rec2020",...A.xyzd50ToRec2020(...t.#o()),t.alpha),xyz:t=>new Pt("xyz",...A.xyzd50ToD65(...t.#o()),t.alpha),"xyz-d50":t=>new Pt("xyz-d50",...t.#o(),t.alpha),"xyz-d65":t=>new Pt("xyz-d65",...A.xyzd50ToD65(...t.#o()),t.alpha)};#o(){return A.xyzd65ToD50(...A.oklabToXyzd65(this.l,this.a,this.b))}#a(t=!0){const e=A.xyzd50ToSrgb(...this.#o());return t?[...e,this.alpha??void 0]:e}constructor(t,e,r,s,n){this.#n=[t,e,r],this.l=ht(t,{min:0,max:1}),(xt(this.l,0)||xt(this.l,1))&&(e=r=0),this.a=e,this.b=r,this.alpha=ht(s,{min:0,max:1}),this.#s=n}asLegacyColor(){return this.as("rgba")}is(t){return t===this.format()}as(t){return zt.#i[t](this)}equal(t){const e=t.as("oklab");return xt(e.l,this.l)&&xt(e.a,this.a)&&xt(e.b,this.b)&&xt(e.alpha,this.alpha)}format(){return"oklab"}setAlpha(t){return new zt(this.l,this.a,this.b,t)}asString(t){return t?this.as(t).asString():this.#l(this.l,this.a,this.b)}#l(t,r,s){const n=null===this.alpha||xt(this.alpha,1)?"":` / ${e.StringUtilities.stringifyWithPrecision(this.alpha)}`;return`oklab(${e.StringUtilities.stringifyWithPrecision(t)} ${e.StringUtilities.stringifyWithPrecision(r)} ${e.StringUtilities.stringifyWithPrecision(s)}${n})`}getAuthoredText(){return this.#s??null}getRawParameters(){return[...this.#n]}getAsRawString(t){return t?this.as(t).getAsRawString():this.#l(...this.#n)}isGamutClipped(){return!1}static fromSpec(t,e){const r=ct(t[0],[0,1])??ut(t[0]);if(null===r)return null;const s=ct(t[1],[0,.4])??ut(t[1]);if(null===s)return null;const n=ct(t[2],[0,.4])??ut(t[2]);if(null===n)return null;const i=gt(t[3]);return new zt(r,s,n,i,e)}}class It{#n;l;c;h;alpha;#s;static#i={hex:t=>new Ot(t.#a(!1),"hex"),hexa:t=>new Ot(t.#a(!0),"hexa"),rgb:t=>new Ot(t.#a(!1),"rgb"),rgba:t=>new Ot(t.#a(!0),"rgba"),hsl:t=>new At(...L(t.#a(!1)),t.alpha),hsla:t=>new At(...L(t.#a(!1)),t.alpha),hwb:t=>new Et(...O(t.#a(!1)),t.alpha),hwba:t=>new Et(...O(t.#a(!1)),t.alpha),lch:t=>new Rt(...A.labToLch(...A.xyzd50ToLab(...t.#o())),t.alpha),oklch:t=>t,lab:t=>new Tt(...A.xyzd50ToLab(...t.#o()),t.alpha),oklab:t=>new zt(...A.xyzd65ToOklab(...A.xyzd50ToD65(...t.#o())),t.alpha),srgb:t=>new Pt("srgb",...A.xyzd50ToSrgb(...t.#o()),t.alpha),"srgb-linear":t=>new Pt("srgb-linear",...A.xyzd50TosRGBLinear(...t.#o()),t.alpha),"display-p3":t=>new Pt("display-p3",...A.xyzd50ToDisplayP3(...t.#o()),t.alpha),"a98-rgb":t=>new Pt("a98-rgb",...A.xyzd50ToAdobeRGB(...t.#o()),t.alpha),"prophoto-rgb":t=>new Pt("prophoto-rgb",...A.xyzd50ToProPhoto(...t.#o()),t.alpha),rec2020:t=>new Pt("rec2020",...A.xyzd50ToRec2020(...t.#o()),t.alpha),xyz:t=>new Pt("xyz",...A.xyzd50ToD65(...t.#o()),t.alpha),"xyz-d50":t=>new Pt("xyz-d50",...t.#o(),t.alpha),"xyz-d65":t=>new Pt("xyz-d65",...A.xyzd50ToD65(...t.#o()),t.alpha)};#o(){return A.oklchToXyzd50(this.l,this.c,this.h)}#a(t=!0){const e=A.xyzd50ToSrgb(...this.#o());return t?[...e,this.alpha??void 0]:e}constructor(t,e,r,s,n){this.#n=[t,e,r],this.l=ht(t,{min:0,max:1}),e=xt(this.l,0)||xt(this.l,1)?0:e,this.c=ht(e,{min:0}),r=xt(e,0)?0:r,this.h=it(r),this.alpha=ht(s,{min:0,max:1}),this.#s=n}asLegacyColor(){return this.as("rgba")}is(t){return t===this.format()}as(t){return It.#i[t](this)}equal(t){const e=t.as("oklch");return xt(e.l,this.l)&&xt(e.c,this.c)&&xt(e.h,this.h)&&xt(e.alpha,this.alpha)}format(){return"oklch"}setAlpha(t){return new It(this.l,this.c,this.h,t)}asString(t){return t?this.as(t).asString():this.#l(this.l,this.c,this.h)}#l(t,r,s){const n=null===this.alpha||xt(this.alpha,1)?"":` / ${e.StringUtilities.stringifyWithPrecision(this.alpha)}`;return`oklch(${e.StringUtilities.stringifyWithPrecision(t)} ${e.StringUtilities.stringifyWithPrecision(r)} ${e.StringUtilities.stringifyWithPrecision(s)}${n})`}getAuthoredText(){return this.#s??null}getRawParameters(){return[...this.#n]}getAsRawString(t){return t?this.as(t).getAsRawString():this.#l(...this.#n)}isGamutClipped(){return!1}static fromSpec(t,e){const r=ct(t[0],[0,1])??ut(t[0]);if(null===r)return null;const s=ct(t[1],[0,.4])??ut(t[1]);if(null===s)return null;const n=at(t[2]);if(null===n)return null;const i=gt(t[3]);return new It(r,s,n,i,e)}}class Pt{#n;p0;p1;p2;alpha;colorSpace;#s;static#i={hex:t=>new Ot(t.#a(!1),"hex"),hexa:t=>new Ot(t.#a(!0),"hexa"),rgb:t=>new Ot(t.#a(!1),"rgb"),rgba:t=>new Ot(t.#a(!0),"rgba"),hsl:t=>new At(...L(t.#a(!1)),t.alpha),hsla:t=>new At(...L(t.#a(!1)),t.alpha),hwb:t=>new Et(...O(t.#a(!1)),t.alpha),hwba:t=>new Et(...O(t.#a(!1)),t.alpha),lch:t=>new Rt(...A.labToLch(...A.xyzd50ToLab(...t.#o())),t.alpha),oklch:t=>new It(...A.xyzd50ToOklch(...t.#o()),t.alpha),lab:t=>new Tt(...A.xyzd50ToLab(...t.#o()),t.alpha),oklab:t=>new zt(...A.xyzd65ToOklab(...A.xyzd50ToD65(...t.#o())),t.alpha),srgb:t=>new Pt("srgb",...A.xyzd50ToSrgb(...t.#o()),t.alpha),"srgb-linear":t=>new Pt("srgb-linear",...A.xyzd50TosRGBLinear(...t.#o()),t.alpha),"display-p3":t=>new Pt("display-p3",...A.xyzd50ToDisplayP3(...t.#o()),t.alpha),"a98-rgb":t=>new Pt("a98-rgb",...A.xyzd50ToAdobeRGB(...t.#o()),t.alpha),"prophoto-rgb":t=>new Pt("prophoto-rgb",...A.xyzd50ToProPhoto(...t.#o()),t.alpha),rec2020:t=>new Pt("rec2020",...A.xyzd50ToRec2020(...t.#o()),t.alpha),xyz:t=>new Pt("xyz",...A.xyzd50ToD65(...t.#o()),t.alpha),"xyz-d50":t=>new Pt("xyz-d50",...t.#o(),t.alpha),"xyz-d65":t=>new Pt("xyz-d65",...A.xyzd50ToD65(...t.#o()),t.alpha)};#o(){const[t,e,r]=this.#n;switch(this.colorSpace){case"srgb":return A.srgbToXyzd50(t,e,r);case"srgb-linear":return A.srgbLinearToXyzd50(t,e,r);case"display-p3":return A.displayP3ToXyzd50(t,e,r);case"a98-rgb":return A.adobeRGBToXyzd50(t,e,r);case"prophoto-rgb":return A.proPhotoToXyzd50(t,e,r);case"rec2020":return A.rec2020ToXyzd50(t,e,r);case"xyz-d50":return[t,e,r];case"xyz":case"xyz-d65":return A.xyzd65ToD50(t,e,r)}throw new Error("Invalid color space")}#a(t=!0){const[e,r,s]=this.#n,n="srgb"===this.colorSpace?[e,r,s]:[...A.xyzd50ToSrgb(...this.#o())];return t?[...n,this.alpha??void 0]:n}constructor(t,e,r,s,n,i){this.#n=[e,r,s],this.colorSpace=t,this.#s=i,"xyz-d50"!==this.colorSpace&&"xyz-d65"!==this.colorSpace&&"xyz"!==this.colorSpace&&(e=ht(e,{min:0,max:1}),r=ht(r,{min:0,max:1}),s=ht(s,{min:0,max:1})),this.p0=e,this.p1=r,this.p2=s,this.alpha=ht(n,{min:0,max:1})}asLegacyColor(){return this.as("rgba")}is(t){return t===this.format()}as(t){return this.colorSpace===t?this:Pt.#i[t](this)}equal(t){const e=t.as(this.colorSpace);return xt(this.p0,e.p0)&&xt(this.p1,e.p1)&&xt(this.p2,e.p2)&&xt(this.alpha,e.alpha)}format(){return this.colorSpace}setAlpha(t){return new Pt(this.colorSpace,this.p0,this.p1,this.p2,t)}asString(t){return t?this.as(t).asString():this.#l(this.p0,this.p1,this.p2)}#l(t,r,s){const n=null===this.alpha||xt(this.alpha,1)?"":` / ${e.StringUtilities.stringifyWithPrecision(this.alpha)}`;return`color(${this.colorSpace} ${e.StringUtilities.stringifyWithPrecision(t)} ${e.StringUtilities.stringifyWithPrecision(r)} ${e.StringUtilities.stringifyWithPrecision(s)}${n})`}getAuthoredText(){return this.#s??null}getRawParameters(){return[...this.#n]}getAsRawString(t){return t?this.as(t).getAsRawString():this.#l(...this.#n)}isGamutClipped(){return"xyz-d50"!==this.colorSpace&&"xyz-d65"!==this.colorSpace&&"xyz"!==this.colorSpace&&!xt(this.#n,[this.p0,this.p1,this.p2])}static fromSpec(t,e){const[r,s]=e.split("/",2),n=r.trim().split(/\s+/),[i,...a]=n,o=ot(i);if(!o)return null;if(0===a.length&&void 0===s)return new Pt(o,0,0,0,null,t);if(0===a.length&&void 0!==s&&s.trim().split(/\s+/).length>1)return null;if(a.length>3)return null;const l=a.map((t=>"none"===t?"0":t)).map((t=>dt(t,[0,1])));if(l.includes(null))return null;const h=s?dt(s,[0,1])??1:1,c=[l[0]??0,l[1]??0,l[2]??0,h];return new Pt(o,...c,t)}}class At{h;s;l;alpha;#n;#s;static#i={hex:t=>new Ot(t.#a(!1),"hex"),hexa:t=>new Ot(t.#a(!0),"hexa"),rgb:t=>new Ot(t.#a(!1),"rgb"),rgba:t=>new Ot(t.#a(!0),"rgba"),hsl:t=>t,hsla:t=>t,hwb:t=>new Et(...O(t.#a(!1)),t.alpha),hwba:t=>new Et(...O(t.#a(!1)),t.alpha),lch:t=>new Rt(...A.labToLch(...A.xyzd50ToLab(...t.#o())),t.alpha),oklch:t=>new It(...A.xyzd50ToOklch(...t.#o()),t.alpha),lab:t=>new Tt(...A.xyzd50ToLab(...t.#o()),t.alpha),oklab:t=>new zt(...A.xyzd65ToOklab(...A.xyzd50ToD65(...t.#o())),t.alpha),srgb:t=>new Pt("srgb",...A.xyzd50ToSrgb(...t.#o()),t.alpha),"srgb-linear":t=>new Pt("srgb-linear",...A.xyzd50TosRGBLinear(...t.#o()),t.alpha),"display-p3":t=>new Pt("display-p3",...A.xyzd50ToDisplayP3(...t.#o()),t.alpha),"a98-rgb":t=>new Pt("a98-rgb",...A.xyzd50ToAdobeRGB(...t.#o()),t.alpha),"prophoto-rgb":t=>new Pt("prophoto-rgb",...A.xyzd50ToProPhoto(...t.#o()),t.alpha),rec2020:t=>new Pt("rec2020",...A.xyzd50ToRec2020(...t.#o()),t.alpha),xyz:t=>new Pt("xyz",...A.xyzd50ToD65(...t.#o()),t.alpha),"xyz-d50":t=>new Pt("xyz-d50",...t.#o(),t.alpha),"xyz-d65":t=>new Pt("xyz-d65",...A.xyzd50ToD65(...t.#o()),t.alpha)};#a(t=!0){const e=ft([this.h,this.s,this.l,0]);return t?[e[0],e[1],e[2],this.alpha??void 0]:[e[0],e[1],e[2]]}#o(){const t=this.#a(!1);return A.srgbToXyzd50(t[0],t[1],t[2])}constructor(t,e,r,s,n){this.#n=[t,e,r],this.l=ht(r,{min:0,max:1}),e=xt(this.l,0)||xt(this.l,1)?0:e,this.s=ht(e,{min:0,max:1}),t=xt(this.s,0)?0:t,this.h=it(360*t)/360,this.alpha=ht(s??null,{min:0,max:1}),this.#s=n}equal(t){const e=t.as("hsl");return xt(this.h,e.h)&&xt(this.s,e.s)&&xt(this.l,e.l)&&xt(this.alpha,e.alpha)}asString(t){return t?this.as(t).asString():this.#l(this.h,this.s,this.l)}#l(t,r,s){const n=e.StringUtilities.sprintf("hsl(%sdeg %s% %s%",e.StringUtilities.stringifyWithPrecision(360*t),e.StringUtilities.stringifyWithPrecision(100*r),e.StringUtilities.stringifyWithPrecision(100*s));return null!==this.alpha&&1!==this.alpha?n+e.StringUtilities.sprintf(" / %s%)",e.StringUtilities.stringifyWithPrecision(100*this.alpha)):n+")"}setAlpha(t){return new At(this.h,this.s,this.l,t)}format(){return null===this.alpha||1===this.alpha?"hsl":"hsla"}is(t){return t===this.format()}as(t){return t===this.format()?this:At.#i[t](this)}asLegacyColor(){return this.as("rgba")}getAuthoredText(){return this.#s??null}getRawParameters(){return[...this.#n]}getAsRawString(t){return t?this.as(t).getAsRawString():this.#l(...this.#n)}isGamutClipped(){return!vt(this.#n[1],1)||!vt(0,this.#n[1])}static fromSpec(t,e){const r=mt(t[0]);if(null===r)return null;const s=yt(t[1]);if(null===s)return null;const n=yt(t[2]);if(null===n)return null;const i=gt(t[3]);return new At(r,s,n,i,e)}hsva(){const t=this.s*(this.l<.5?this.l:1-this.l);return[this.h,0!==t?2*t/(this.l+t):0,this.l+t,this.alpha??1]}canonicalHSLA(){return[Math.round(360*this.h),Math.round(100*this.s),Math.round(100*this.l),this.alpha??1]}}class Et{h;w;b;alpha;#n;#s;static#i={hex:t=>new Ot(t.#a(!1),"hex"),hexa:t=>new Ot(t.#a(!0),"hexa"),rgb:t=>new Ot(t.#a(!1),"rgb"),rgba:t=>new Ot(t.#a(!0),"rgba"),hsl:t=>new At(...L(t.#a(!1)),t.alpha),hsla:t=>new At(...L(t.#a(!1)),t.alpha),hwb:t=>t,hwba:t=>t,lch:t=>new Rt(...A.labToLch(...A.xyzd50ToLab(...t.#o())),t.alpha),oklch:t=>new It(...A.xyzd50ToOklch(...t.#o()),t.alpha),lab:t=>new Tt(...A.xyzd50ToLab(...t.#o()),t.alpha),oklab:t=>new zt(...A.xyzd65ToOklab(...A.xyzd50ToD65(...t.#o())),t.alpha),srgb:t=>new Pt("srgb",...A.xyzd50ToSrgb(...t.#o()),t.alpha),"srgb-linear":t=>new Pt("srgb-linear",...A.xyzd50TosRGBLinear(...t.#o()),t.alpha),"display-p3":t=>new Pt("display-p3",...A.xyzd50ToDisplayP3(...t.#o()),t.alpha),"a98-rgb":t=>new Pt("a98-rgb",...A.xyzd50ToAdobeRGB(...t.#o()),t.alpha),"prophoto-rgb":t=>new Pt("prophoto-rgb",...A.xyzd50ToProPhoto(...t.#o()),t.alpha),rec2020:t=>new Pt("rec2020",...A.xyzd50ToRec2020(...t.#o()),t.alpha),xyz:t=>new Pt("xyz",...A.xyzd50ToD65(...t.#o()),t.alpha),"xyz-d50":t=>new Pt("xyz-d50",...t.#o(),t.alpha),"xyz-d65":t=>new Pt("xyz-d65",...A.xyzd50ToD65(...t.#o()),t.alpha)};#a(t=!0){const e=function(t){const e=t[0],r=t[1],s=t[2],n=r/(r+s);let i=[n,n,n,t[3]];if(r+s<1){i=ft([e,1,.5,t[3]]);for(let t=0;t<3;++t)i[t]+=r-(r+s)*i[t]}return i}([this.h,this.w,this.b,0]);return t?[e[0],e[1],e[2],this.alpha??void 0]:[e[0],e[1],e[2]]}#o(){const t=this.#a(!1);return A.srgbToXyzd50(t[0],t[1],t[2])}constructor(t,e,r,s,n){if(this.#n=[t,e,r],this.w=ht(e,{min:0,max:1}),this.b=ht(r,{min:0,max:1}),t=vt(1,this.w+this.b)?0:t,this.h=it(360*t)/360,this.alpha=ht(s,{min:0,max:1}),vt(1,this.w+this.b)){const t=this.w/this.b;this.b=1/(1+t),this.w=1-this.b}this.#s=n}equal(t){const e=t.as("hwb");return xt(this.h,e.h)&&xt(this.w,e.w)&&xt(this.b,e.b)&&xt(this.alpha,e.alpha)}asString(t){return t?this.as(t).asString():this.#l(this.h,this.w,this.b)}#l(t,r,s){const n=e.StringUtilities.sprintf("hwb(%sdeg %s% %s%",e.StringUtilities.stringifyWithPrecision(360*t),e.StringUtilities.stringifyWithPrecision(100*r),e.StringUtilities.stringifyWithPrecision(100*s));return null!==this.alpha&&1!==this.alpha?n+e.StringUtilities.sprintf(" / %s%)",e.StringUtilities.stringifyWithPrecision(100*this.alpha)):n+")"}setAlpha(t){return new Et(this.h,this.w,this.b,t,this.#s)}format(){return null===this.alpha||xt(this.alpha,1)?"hwb":"hwba"}is(t){return t===this.format()}as(t){return t===this.format()?this:Et.#i[t](this)}asLegacyColor(){return this.as("rgba")}getAuthoredText(){return this.#s??null}canonicalHWBA(){return[Math.round(360*this.h),Math.round(100*this.w),Math.round(100*this.b),this.alpha??1]}getRawParameters(){return[...this.#n]}getAsRawString(t){return t?this.as(t).getAsRawString():this.#l(...this.#n)}isGamutClipped(){return!(vt(this.#n[1],1)&&vt(0,this.#n[1])&&vt(this.#n[2],1)&&vt(0,this.#n[2]))}static fromSpec(t,e){const r=mt(t[0]);if(null===r)return null;const s=yt(t[1]);if(null===s)return null;const n=yt(t[2]);if(null===n)return null;const i=gt(t[3]);return new Et(r,s,n,i,e)}}function kt(t){return Math.round(255*t)}class Ct{color;constructor(t){this.color=t}get alpha(){return this.color.alpha}equal(t){return this.color.equal(t)}setAlpha(t){return this.color.setAlpha(t)}format(){return 1!==(this.alpha??1)?"hexa":"hex"}as(t){return this.color.as(t)}is(t){return this.color.is(t)}asLegacyColor(){return this.color.asLegacyColor()}getAuthoredText(){return this.color.getAuthoredText()}getRawParameters(){return this.color.getRawParameters()}isGamutClipped(){return this.color.isGamutClipped()}asString(t){if(t)return this.as(t).asString();const[e,r,s]=this.color.rgba();return this.stringify(e,r,s)}getAsRawString(t){if(t)return this.as(t).getAsRawString();const[e,r,s]=this.getRawParameters();return this.stringify(e,r,s)}}class Lt extends Ct{setAlpha(t){return new Lt(this.color.setAlpha(t))}asString(t){return t&&t!==this.format()?super.as(t).asString():super.asString()}stringify(t,r,s){function n(t){return(Math.round(255*t)/17).toString(16)}return this.color.hasAlpha()?e.StringUtilities.sprintf("#%s%s%s%s",n(t),n(r),n(s),n(this.alpha??1)).toLowerCase():e.StringUtilities.sprintf("#%s%s%s",n(t),n(r),n(s)).toLowerCase()}}class _t extends Ct{nickname;constructor(t,e){super(e),this.nickname=t}static fromName(t,e){const r=t.toLowerCase(),s=Nt.get(r);return void 0!==s?new _t(r,Ot.fromRGBA(s,e)):null}stringify(){return this.nickname}getAsRawString(t){return this.color.getAsRawString(t)}}class Ot{#n;#h;#s;#c;static#i={hex:t=>new Ot(t.#h,"hex"),hexa:t=>new Ot(t.#h,"hexa"),rgb:t=>new Ot(t.#h,"rgb"),rgba:t=>new Ot(t.#h,"rgba"),hsl:t=>new At(...L([t.#h[0],t.#h[1],t.#h[2]]),t.alpha),hsla:t=>new At(...L([t.#h[0],t.#h[1],t.#h[2]]),t.alpha),hwb:t=>new Et(...O([t.#h[0],t.#h[1],t.#h[2]]),t.alpha),hwba:t=>new Et(...O([t.#h[0],t.#h[1],t.#h[2]]),t.alpha),lch:t=>new Rt(...A.labToLch(...A.xyzd50ToLab(...t.#o())),t.alpha),oklch:t=>new It(...A.xyzd50ToOklch(...t.#o()),t.alpha),lab:t=>new Tt(...A.xyzd50ToLab(...t.#o()),t.alpha),oklab:t=>new zt(...A.xyzd65ToOklab(...A.xyzd50ToD65(...t.#o())),t.alpha),srgb:t=>new Pt("srgb",...A.xyzd50ToSrgb(...t.#o()),t.alpha),"srgb-linear":t=>new Pt("srgb-linear",...A.xyzd50TosRGBLinear(...t.#o()),t.alpha),"display-p3":t=>new Pt("display-p3",...A.xyzd50ToDisplayP3(...t.#o()),t.alpha),"a98-rgb":t=>new Pt("a98-rgb",...A.xyzd50ToAdobeRGB(...t.#o()),t.alpha),"prophoto-rgb":t=>new Pt("prophoto-rgb",...A.xyzd50ToProPhoto(...t.#o()),t.alpha),rec2020:t=>new Pt("rec2020",...A.xyzd50ToRec2020(...t.#o()),t.alpha),xyz:t=>new Pt("xyz",...A.xyzd50ToD65(...t.#o()),t.alpha),"xyz-d50":t=>new Pt("xyz-d50",...t.#o(),t.alpha),"xyz-d65":t=>new Pt("xyz-d65",...A.xyzd50ToD65(...t.#o()),t.alpha)};#o(){const[t,e,r]=this.#h;return A.srgbToXyzd50(t,e,r)}get alpha(){switch(this.format()){case"hexa":case"rgba":return this.#h[3];default:return null}}asLegacyColor(){return this}nickname(){const t=Vt.get(String(this.canonicalRGBA()));return t?new _t(t,this):null}shortHex(){for(let t=0;t<4;++t){if(Math.round(255*this.#h[t])%17)return null}return new Lt(this)}constructor(t,e,r){this.#s=r||null,this.#c=e,this.#n=[t[0],t[1],t[2]],this.#h=[ht(t[0],{min:0,max:1}),ht(t[1],{min:0,max:1}),ht(t[2],{min:0,max:1}),ht(t[3]??1,{min:0,max:1})]}static fromHex(t,e){const r=4===(t=t.toLowerCase()).length||8===t.length?"hexa":"hex",s=t.length<=4;s&&(t=t.charAt(0)+t.charAt(0)+t.charAt(1)+t.charAt(1)+t.charAt(2)+t.charAt(2)+t.charAt(3)+t.charAt(3));const n=parseInt(t.substring(0,2),16),i=parseInt(t.substring(2,4),16),a=parseInt(t.substring(4,6),16);let o=1;8===t.length&&(o=parseInt(t.substring(6,8),16)/255);const l=new Ot([n/255,i/255,a/255,o],r,e);return s?new Lt(l):l}static fromRGBAFunction(t,r,s,n,i){const a=[pt(t),pt(r),pt(s),n?(o=n,dt(o)):1];var o;return e.ArrayUtilities.arrayDoesNotContainNullOrUndefined(a)?new Ot(a,n?"rgba":"rgb",i):null}static fromRGBA(t,e){return new Ot([t[0]/255,t[1]/255,t[2]/255,t[3]],"rgba",e)}static fromHSVA(t){const e=wt(t);return new Ot(e,"rgba")}is(t){return t===this.format()}as(t){return t===this.format()?this:Ot.#i[t](this)}format(){return this.#c}hasAlpha(){return 1!==this.#h[3]}detectHEXFormat(){return this.hasAlpha()?"hexa":"hex"}asString(t){return t?this.as(t).asString():this.#l(t,this.#h[0],this.#h[1],this.#h[2])}#l(t,r,s,n){function i(t){const e=Math.round(255*t).toString(16);return 1===e.length?"0"+e:e}switch(t||(t=this.#c),t){case"rgb":case"rgba":{const t=e.StringUtilities.sprintf("rgb(%d %d %d",kt(r),kt(s),kt(n));return this.hasAlpha()?t+e.StringUtilities.sprintf(" / %d%)",Math.round(100*this.#h[3])):t+")"}case"hex":case"hexa":return this.hasAlpha()?e.StringUtilities.sprintf("#%s%s%s%s",i(r),i(s),i(n),i(this.#h[3])).toLowerCase():e.StringUtilities.sprintf("#%s%s%s",i(r),i(s),i(n)).toLowerCase()}}getAuthoredText(){return this.#s??null}getRawParameters(){return[...this.#n]}getAsRawString(t){return t?this.as(t).getAsRawString():this.#l(t,...this.#n)}isGamutClipped(){return!xt(this.#n.map(kt),[this.#h[0],this.#h[1],this.#h[2]].map(kt),1)}rgba(){return[...this.#h]}canonicalRGBA(){const t=new Array(4);for(let e=0;e<3;++e)t[e]=Math.round(255*this.#h[e]);return t[3]=this.#h[3],t}toProtocolRGBA(){const t=this.canonicalRGBA(),e={r:t[0],g:t[1],b:t[2],a:void 0};return 1!==t[3]&&(e.a=t[3]),e}invert(){const t=[0,0,0,0];return t[0]=1-this.#h[0],t[1]=1-this.#h[1],t[2]=1-this.#h[2],t[3]=this.#h[3],new Ot(t,"rgba")}setAlpha(t){const e=[...this.#h];return e[3]=t,new Ot(e,"rgba")}blendWith(t){const e=k(t.#h,this.#h);return new Ot(e,"rgba")}blendWithAlpha(t){const e=[...this.#h];return e[3]*=t,new Ot(e,"rgba")}setFormat(t){this.#c=t}equal(t){const e=t.as(this.#c);return xt(kt(this.#h[0]),kt(e.#h[0]),1)&&xt(kt(this.#h[1]),kt(e.#h[1]),1)&&xt(kt(this.#h[2]),kt(e.#h[2]),1)&&xt(this.#h[3],e.#h[3])}}const Bt=[["aliceblue",[240,248,255]],["antiquewhite",[250,235,215]],["aqua",[0,255,255]],["aquamarine",[127,255,212]],["azure",[240,255,255]],["beige",[245,245,220]],["bisque",[255,228,196]],["black",[0,0,0]],["blanchedalmond",[255,235,205]],["blue",[0,0,255]],["blueviolet",[138,43,226]],["brown",[165,42,42]],["burlywood",[222,184,135]],["cadetblue",[95,158,160]],["chartreuse",[127,255,0]],["chocolate",[210,105,30]],["coral",[255,127,80]],["cornflowerblue",[100,149,237]],["cornsilk",[255,248,220]],["crimson",[237,20,61]],["cyan",[0,255,255]],["darkblue",[0,0,139]],["darkcyan",[0,139,139]],["darkgoldenrod",[184,134,11]],["darkgray",[169,169,169]],["darkgrey",[169,169,169]],["darkgreen",[0,100,0]],["darkkhaki",[189,183,107]],["darkmagenta",[139,0,139]],["darkolivegreen",[85,107,47]],["darkorange",[255,140,0]],["darkorchid",[153,50,204]],["darkred",[139,0,0]],["darksalmon",[233,150,122]],["darkseagreen",[143,188,143]],["darkslateblue",[72,61,139]],["darkslategray",[47,79,79]],["darkslategrey",[47,79,79]],["darkturquoise",[0,206,209]],["darkviolet",[148,0,211]],["deeppink",[255,20,147]],["deepskyblue",[0,191,255]],["dimgray",[105,105,105]],["dimgrey",[105,105,105]],["dodgerblue",[30,144,255]],["firebrick",[178,34,34]],["floralwhite",[255,250,240]],["forestgreen",[34,139,34]],["fuchsia",[255,0,255]],["gainsboro",[220,220,220]],["ghostwhite",[248,248,255]],["gold",[255,215,0]],["goldenrod",[218,165,32]],["gray",[128,128,128]],["grey",[128,128,128]],["green",[0,128,0]],["greenyellow",[173,255,47]],["honeydew",[240,255,240]],["hotpink",[255,105,180]],["indianred",[205,92,92]],["indigo",[75,0,130]],["ivory",[255,255,240]],["khaki",[240,230,140]],["lavender",[230,230,250]],["lavenderblush",[255,240,245]],["lawngreen",[124,252,0]],["lemonchiffon",[255,250,205]],["lightblue",[173,216,230]],["lightcoral",[240,128,128]],["lightcyan",[224,255,255]],["lightgoldenrodyellow",[250,250,210]],["lightgreen",[144,238,144]],["lightgray",[211,211,211]],["lightgrey",[211,211,211]],["lightpink",[255,182,193]],["lightsalmon",[255,160,122]],["lightseagreen",[32,178,170]],["lightskyblue",[135,206,250]],["lightslategray",[119,136,153]],["lightslategrey",[119,136,153]],["lightsteelblue",[176,196,222]],["lightyellow",[255,255,224]],["lime",[0,255,0]],["limegreen",[50,205,50]],["linen",[250,240,230]],["magenta",[255,0,255]],["maroon",[128,0,0]],["mediumaquamarine",[102,205,170]],["mediumblue",[0,0,205]],["mediumorchid",[186,85,211]],["mediumpurple",[147,112,219]],["mediumseagreen",[60,179,113]],["mediumslateblue",[123,104,238]],["mediumspringgreen",[0,250,154]],["mediumturquoise",[72,209,204]],["mediumvioletred",[199,21,133]],["midnightblue",[25,25,112]],["mintcream",[245,255,250]],["mistyrose",[255,228,225]],["moccasin",[255,228,181]],["navajowhite",[255,222,173]],["navy",[0,0,128]],["oldlace",[253,245,230]],["olive",[128,128,0]],["olivedrab",[107,142,35]],["orange",[255,165,0]],["orangered",[255,69,0]],["orchid",[218,112,214]],["palegoldenrod",[238,232,170]],["palegreen",[152,251,152]],["paleturquoise",[175,238,238]],["palevioletred",[219,112,147]],["papayawhip",[255,239,213]],["peachpuff",[255,218,185]],["peru",[205,133,63]],["pink",[255,192,203]],["plum",[221,160,221]],["powderblue",[176,224,230]],["purple",[128,0,128]],["rebeccapurple",[102,51,153]],["red",[255,0,0]],["rosybrown",[188,143,143]],["royalblue",[65,105,225]],["saddlebrown",[139,69,19]],["salmon",[250,128,114]],["sandybrown",[244,164,96]],["seagreen",[46,139,87]],["seashell",[255,245,238]],["sienna",[160,82,45]],["silver",[192,192,192]],["skyblue",[135,206,235]],["slateblue",[106,90,205]],["slategray",[112,128,144]],["slategrey",[112,128,144]],["snow",[255,250,250]],["springgreen",[0,255,127]],["steelblue",[70,130,180]],["tan",[210,180,140]],["teal",[0,128,128]],["thistle",[216,191,216]],["tomato",[255,99,71]],["turquoise",[64,224,208]],["violet",[238,130,238]],["wheat",[245,222,179]],["white",[255,255,255]],["whitesmoke",[245,245,245]],["yellow",[255,255,0]],["yellowgreen",[154,205,50]],["transparent",[0,0,0,0]]];console.assert(Bt.every((([t])=>t.toLowerCase()===t)),"All color nicknames must be lowercase.");const Nt=new Map(Bt),Vt=new Map(Bt.map((([t,[e,r,s,n=1]])=>[String([e,r,s,n]),t]))),Gt=[127,32,210],Mt={Content:Ot.fromRGBA([111,168,220,.66]),ContentLight:Ot.fromRGBA([111,168,220,.5]),ContentOutline:Ot.fromRGBA([9,83,148]),Padding:Ot.fromRGBA([147,196,125,.55]),PaddingLight:Ot.fromRGBA([147,196,125,.4]),Border:Ot.fromRGBA([255,229,153,.66]),BorderLight:Ot.fromRGBA([255,229,153,.5]),Margin:Ot.fromRGBA([246,178,107,.66]),MarginLight:Ot.fromRGBA([246,178,107,.5]),EventTarget:Ot.fromRGBA([255,196,196,.66]),Shape:Ot.fromRGBA([96,82,177,.8]),ShapeMargin:Ot.fromRGBA([96,82,127,.6]),CssGrid:Ot.fromRGBA([75,0,130,1]),LayoutLine:Ot.fromRGBA([...Gt,1]),GridBorder:Ot.fromRGBA([...Gt,1]),GapBackground:Ot.fromRGBA([...Gt,.3]),GapHatch:Ot.fromRGBA([...Gt,.8]),GridAreaBorder:Ot.fromRGBA([26,115,232,1])},Wt={ParentOutline:Ot.fromRGBA([224,90,183,1]),ChildOutline:Ot.fromRGBA([0,120,212,1])},Xt={Resizer:Ot.fromRGBA([222,225,230,1]),ResizerHandle:Ot.fromRGBA([166,166,166,1]),Mask:Ot.fromRGBA([248,249,249,1])};var Ft=Object.freeze({__proto__:null,getFormat:function(t){switch(t){case"hex":return"hex";case"hexa":return"hexa";case"rgb":return"rgb";case"rgba":return"rgba";case"hsl":return"hsl";case"hsla":return"hsla";case"hwb":return"hwb";case"hwba":return"hwba";case"lch":return"lch";case"oklch":return"oklch";case"lab":return"lab";case"oklab":return"oklab"}return ot(t)},parse:function(t){if(!t.match(/\s/)){const e=t.toLowerCase().match(/^(?:#([0-9a-f]{3,4}|[0-9a-f]{6}|[0-9a-f]{8})|(\w+))$/i);if(e)return e[1]?Ot.fromHex(e[1],t):e[2]?_t.fromName(e[2],t):null}const e=t.toLowerCase().match(/^\s*(?:(rgba?)|(hsla?)|(hwba?)|(lch)|(oklch)|(lab)|(oklab)|(color))\((.*)\)\s*$/);if(e){const r=Boolean(e[1]),s=Boolean(e[2]),n=Boolean(e[3]),i=Boolean(e[4]),a=Boolean(e[5]),o=Boolean(e[6]),l=Boolean(e[7]),h=Boolean(e[8]),c=e[9];if(h)return Pt.fromSpec(t,c);const u=function(t,{allowCommas:e,convertNoneToZero:r}){const s=t.trim();let n=[];e&&(n=s.split(/\s*,\s*/));if(!e||1===n.length)if(n=s.split(/\s+/),"/"===n[3]){if(n.splice(3,1),4!==n.length)return null}else if(n.length>2&&-1!==n[2].indexOf("/")||n.length>3&&-1!==n[3].indexOf("/")){const t=n.slice(2,4).join("");n=n.slice(0,2).concat(t.split(/\//)).concat(n.slice(4))}else if(n.length>=4)return null;if(3!==n.length&&4!==n.length||n.indexOf("")>-1)return null;if(r)return n.map((t=>"none"===t?"0":t));return n}(c,{allowCommas:r||s,convertNoneToZero:!(r||s||n)});if(!u)return null;const g=[u[0],u[1],u[2],u[3]];if(r)return Ot.fromRGBAFunction(u[0],u[1],u[2],u[3],t);if(s)return At.fromSpec(g,t);if(n)return Et.fromSpec(g,t);if(i)return Rt.fromSpec(g,t);if(a)return It.fromSpec(g,t);if(o)return Tt.fromSpec(g,t);if(l)return zt.fromSpec(g,t)}return null},parseHueNumeric:mt,hsl2rgb:ft,hsva2rgba:wt,rgb2hsv:function(t){const e=L(t),r=e[0];let s=e[1];const n=e[2];return s*=n<.5?n:1-n,[r,0!==s?2*s/(n+s):0,n+s]},desiredLuminance:bt,approachColorValue:St,findFgColorForContrast:function(t,e,r){const s=t.as("hsl").hsva(),n=e.rgba(),i=t=>N(k(Ot.fromHSVA(t).rgba(),n)),a=N(e.rgba()),o=bt(a,r,i(s)>a);return St(s,0,2,o,i)?Ot.fromHSVA(s):(s[2]=1,St(s,0,1,o,i)?Ot.fromHSVA(s):null)},findFgColorForContrastAPCA:function(t,e,r){const s=t.as("hsl").hsva(),n=(e.rgba(),t=>Z(Ot.fromHSVA(t).rgba())),i=Z(e.rgba()),a=Q(i,r,n(s)>=i);if(St(s,0,2,a,n)){const t=Ot.fromHSVA(s);if(Math.abs(Y(e.rgba(),t.rgba()))>=r)return t}if(s[2]=1,St(s,0,1,a,n)){const t=Ot.fromHSVA(s);if(Math.abs(Y(e.rgba(),t.rgba()))>=r)return t}return null},Lab:Tt,LCH:Rt,Oklab:zt,Oklch:It,ColorFunction:Pt,HSL:At,HWB:Et,ShortHex:Lt,Nickname:_t,Legacy:Ot,Regex:/((?:rgba?|hsla?|hwba?|lab|lch|oklab|oklch|color)\([^)]+\)|#[0-9a-fA-F]{8}|#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3,4}|\b[a-zA-Z]+\b(?!-))/g,ColorMixRegex:/color-mix\(.*,\s*(?<firstColor>.+)\s*,\s*(?<secondColor>.+)\s*\)/g,Nicknames:Nt,PageHighlight:Mt,SourceOrderHighlight:Wt,IsolationModeHighlight:Xt,Generator:class{#u;#g;#d;#p;#m;constructor(t,e,r,s){this.#u=t||{min:0,max:360,count:void 0},this.#g=e||67,this.#d=r||80,this.#p=s||1,this.#m=new Map}setColorForID(t,e){this.#m.set(t,e)}colorForID(t){let e=this.#m.get(t);return e||(e=this.generateColorForID(t),this.#m.set(t,e)),e}generateColorForID(t){const r=e.StringUtilities.hashCode(t),s=this.indexToValueInSpace(r,this.#u),n=this.indexToValueInSpace(r>>8,this.#g),i=this.indexToValueInSpace(r>>16,this.#d),a=this.indexToValueInSpace(r>>24,this.#p),o=`hsl(${s}deg ${n}% ${i}%`;return 1!==a?`${o} / ${Math.floor(100*a)}%)`:`${o})`}indexToValueInSpace(t,e){if("number"==typeof e)return e;const r=e.count||e.max-e.min;return t%=r,e.min+Math.floor(t/(r-1)*(e.max-e.min))}}});class Dt{listeners;addEventListener(t,e,r){this.listeners||(this.listeners=new Map);let s=this.listeners.get(t);return s||(s=new Set,this.listeners.set(t,s)),s.add({thisObject:r,listener:e}),{eventTarget:this,eventType:t,thisObject:r,listener:e}}once(t){return new Promise((e=>{const r=this.addEventListener(t,(s=>{this.removeEventListener(t,r.listener),e(s.data)}))}))}removeEventListener(t,e,r){const s=this.listeners?.get(t);if(s){for(const t of s)t.listener===e&&t.thisObject===r&&(t.disposed=!0,s.delete(t));s.size||this.listeners?.delete(t)}}hasEventListeners(t){return Boolean(this.listeners&&this.listeners.has(t))}dispatchEventToListeners(t,...[e]){const r=this.listeners?.get(t);if(!r)return;const s={data:e,source:this};for(const t of[...r])t.disposed||t.listener.call(t.thisObject,s)}}var jt=Object.freeze({__proto__:null,ObjectWrapper:Dt,eventMixin:function(t){return class extends t{#y=new Dt;addEventListener(t,e,r){return this.#y.addEventListener(t,e,r)}once(t){return this.#y.once(t)}removeEventListener(t,e,r){this.#y.removeEventListener(t,e,r)}hasEventListeners(t){return this.#y.hasEventListeners(t)}dispatchEventToListeners(t,...e){this.#y.dispatchEventToListeners(t,...e)}}}});const Ut={elementsPanel:"Elements panel",stylesSidebar:"styles sidebar",changesDrawer:"Changes drawer",issuesView:"Issues view",networkPanel:"Network panel",applicationPanel:"Application panel",sourcesPanel:"Sources panel",memoryInspectorPanel:"Memory inspector panel",developerResourcesPanel:"Developer Resources panel"},Ht=r.i18n.registerUIStrings("core/common/Revealer.ts",Ut),$t=r.i18n.getLazilyComputedLocalizedString.bind(void 0,Ht);let qt;class Zt{registeredRevealers=[];static instance(){return void 0===qt&&(qt=new Zt),qt}static removeInstance(){qt=void 0}register(t){this.registeredRevealers.push(t)}async reveal(t,e){const r=await Promise.all(this.getApplicableRegisteredRevealers(t).map((t=>t.loadRevealer())));if(r.length<1)throw new Error(`No revealers found for ${t}`);if(r.length>1)throw new Error(`Conflicting reveals found for ${t}`);return await r[0].reveal(t,e)}getApplicableRegisteredRevealers(t){return this.registeredRevealers.filter((e=>{for(const r of e.contextTypes())if(t instanceof r)return!0;return!1}))}}async function Yt(t,e=!1){await Zt.instance().reveal(t,e)}const Kt={DEVELOPER_RESOURCES_PANEL:$t(Ut.developerResourcesPanel),ELEMENTS_PANEL:$t(Ut.elementsPanel),STYLES_SIDEBAR:$t(Ut.stylesSidebar),CHANGES_DRAWER:$t(Ut.changesDrawer),ISSUES_VIEW:$t(Ut.issuesView),NETWORK_PANEL:$t(Ut.networkPanel),APPLICATION_PANEL:$t(Ut.applicationPanel),SOURCES_PANEL:$t(Ut.sourcesPanel),MEMORY_INSPECTOR_PANEL:$t(Ut.memoryInspectorPanel)};var Jt=Object.freeze({__proto__:null,RevealerRegistry:Zt,revealDestination:function(t){const e=Zt.instance().getApplicableRegisteredRevealers(t);for(const{destination:t}of e)if(t)return t();return null},registerRevealer:function(t){Zt.instance().register(t)},reveal:Yt,RevealerDestination:Kt});let Qt;class te extends Dt{#f;constructor(){super(),this.#f=[]}static instance(t){return Qt&&!t?.forceNew||(Qt=new te),Qt}static removeInstance(){Qt=void 0}addMessage(t,e,r,s){const n=new re(t,e||"info",Date.now(),r||!1,s);this.#f.push(n),this.dispatchEventToListeners("messageAdded",n)}log(t){this.addMessage(t,"info")}warn(t,e){this.addMessage(t,"warning",void 0,e)}error(t){this.addMessage(t,"error",!0)}messages(){return this.#f}show(){this.showPromise()}showPromise(){return Yt(this)}}var ee;!function(t){t.CSS="css",t.ConsoleAPI="console-api",t.IssuePanel="issue-panel",t.SelfXss="self-xss"}(ee||(ee={}));class re{text;level;timestamp;show;source;constructor(t,e,r,s,n){this.text=t,this.level=e,this.timestamp="number"==typeof r?r:Date.now(),this.show=s,n&&(this.source=n)}}var se=Object.freeze({__proto__:null,Console:te,get FrontendMessageSource(){return ee},Message:re});var ne=Object.freeze({__proto__:null,debounce:function(t,e){let r=0;return()=>{clearTimeout(r),r=window.setTimeout((()=>t()),e)}}});var ie=Object.freeze({__proto__:null,removeEventListeners:function(t){for(const e of t)e.eventTarget.removeEventListener(e.eventType,e.listener,e.thisObject);t.splice(0)},fireEvent:function(t,e={},r=window){const s=new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:e});r.dispatchEvent(s)}}),ae=Object.freeze({__proto__:null});const oe=Symbol("uninitialized"),le=Symbol("error");var he=Object.freeze({__proto__:null,lazy:function(t){let e=oe,r=null;return()=>{if(e===le)throw r;if(e!==oe)return e;try{return e=t(),e}catch(t){throw r=t,e=le,r}}}});const ce=[];function ue(t){return ce.filter((function(e){if(!e.contextTypes)return!0;for(const r of e.contextTypes())if(t instanceof r)return!0;return!1}))}var ge=Object.freeze({__proto__:null,Linkifier:class{static async linkify(t,e){if(!t)throw new Error("Can't linkify "+t);const r=ue(t)[0];if(!r)throw new Error("No linkifiers registered for object "+t);return(await r.loadLinkifier()).linkify(t,e)}},registerLinkifier:function(t){ce.push(t)},getApplicableRegisteredlinkifiers:ue});var de=Object.freeze({__proto__:null,Mutex:class{#w=!1;#b=[];acquire(){const t={resolved:!1};return this.#w?new Promise((e=>{this.#b.push((()=>e(this.#S.bind(this,t))))})):(this.#w=!0,Promise.resolve(this.#S.bind(this,t)))}#S(t){if(t.resolved)throw new Error("Cannot release more than once.");t.resolved=!0;const e=this.#b.shift();e?e():this.#w=!1}async run(t){const e=await this.acquire();try{return await t()}finally{e()}}}});function pe(t){if(-1===t.indexOf("..")&&-1===t.indexOf("."))return t;const e=("/"===t[0]?t.substring(1):t).split("/"),r=[];for(const t of e)"."!==t&&(".."===t?r.pop():r.push(t));let s=r.join("/");return"/"===t[0]&&s&&(s="/"+s),"/"===s[s.length-1]||"/"!==t[t.length-1]&&"."!==e[e.length-1]&&".."!==e[e.length-1]||(s+="/"),s}class me{isValid;url;scheme;user;host;port;path;queryParams;fragment;folderPathComponents;lastPathComponent;blobInnerScheme;#x;#v;constructor(t){this.isValid=!1,this.url=t,this.scheme="",this.user="",this.host="",this.port="",this.path="",this.queryParams="",this.fragment="",this.folderPathComponents="",this.lastPathComponent="";const e=this.url.startsWith("blob:"),r=(e?t.substring(5):t).match(me.urlRegex());if(r)this.isValid=!0,e?(this.blobInnerScheme=r[2].toLowerCase(),this.scheme="blob"):this.scheme=r[2].toLowerCase(),this.user=r[3]??"",this.host=r[4]??"",this.port=r[5]??"",this.path=r[6]??"/",this.queryParams=r[7]??"",this.fragment=r[8]??"";else{if(this.url.startsWith("data:"))return void(this.scheme="data");if(this.url.startsWith("blob:"))return void(this.scheme="blob");if("about:blank"===this.url)return void(this.scheme="about");this.path=this.url}const s=this.path.lastIndexOf("/",this.path.length-2);this.lastPathComponent=-1!==s?this.path.substring(s+1):this.path;const n=this.path.lastIndexOf("/");-1!==n&&(this.folderPathComponents=this.path.substring(0,n))}static fromString(t){const e=new me(t.toString());return e.isValid?e:null}static preEncodeSpecialCharactersInPath(t){for(const e of["%",";","#","?"," "])t=t.replaceAll(e,encodeURIComponent(e));return t}static rawPathToEncodedPathString(t){const e=me.preEncodeSpecialCharactersInPath(t);return t.startsWith("/")?new URL(e,"file:///").pathname:new URL("/"+e,"file:///").pathname.substr(1)}static encodedFromParentPathAndName(t,e){return me.concatenate(t,"/",me.preEncodeSpecialCharactersInPath(e))}static urlFromParentUrlAndName(t,e){return me.concatenate(t,"/",me.preEncodeSpecialCharactersInPath(e))}static encodedPathToRawPathString(t){return decodeURIComponent(t)}static rawPathToUrlString(t){let e=me.preEncodeSpecialCharactersInPath(t.replace(/\\/g,"/"));return e=e.replace(/\\/g,"/"),e.startsWith("file://")||(e=e.startsWith("/")?"file://"+e:"file:///"+e),new URL(e).toString()}static relativePathToUrlString(t,e){const r=me.preEncodeSpecialCharactersInPath(t.replace(/\\/g,"/"));return new URL(r,e).toString()}static urlToRawPathString(t,e){console.assert(t.startsWith("file://"),"This must be a file URL.");const r=decodeURIComponent(t);return e?r.substr(8).replace(/\//g,"\\"):r.substr(7)}static sliceUrlToEncodedPathString(t,e){return t.substring(e)}static substr(t,e,r){return t.substr(e,r)}static substring(t,e,r){return t.substring(e,r)}static prepend(t,e){return t+e}static concatenate(t,...e){return t.concat(...e)}static trim(t){return t.trim()}static slice(t,e,r){return t.slice(e,r)}static join(t,e){return t.join(e)}static split(t,e,r){return t.split(e,r)}static toLowerCase(t){return t.toLowerCase()}static isValidUrlString(t){return new me(t).isValid}static urlWithoutHash(t){const e=t.indexOf("#");return-1!==e?t.substr(0,e):t}static urlRegex(){if(me.urlRegexInstance)return me.urlRegexInstance;return me.urlRegexInstance=new RegExp("^("+/([A-Za-z][A-Za-z0-9+.-]*):\/\//.source+/(?:([A-Za-z0-9\-._~%!$&'()*+,;=:]*)@)?/.source+/((?:\[::\d?\])|(?:[^\s\/:]*))/.source+/(?::([\d]+))?/.source+")"+/(\/[^#?]*)?/.source+/(?:\?([^#]*))?/.source+/(?:#(.*))?/.source+"$"),me.urlRegexInstance}static extractPath(t){const e=this.fromString(t);return e?e.path:""}static extractOrigin(t){const r=this.fromString(t);return r?r.securityOrigin():e.DevToolsPath.EmptyUrlString}static extractExtension(t){const e=(t=me.urlWithoutHash(t)).indexOf("?");-1!==e&&(t=t.substr(0,e));const r=t.lastIndexOf("/");-1!==r&&(t=t.substr(r+1));const s=t.lastIndexOf(".");if(-1!==s){const e=(t=t.substr(s+1)).indexOf("%");return-1!==e?t.substr(0,e):t}return""}static extractName(t){let e=t.lastIndexOf("/");const r=-1!==e?t.substr(e+1):t;return e=r.indexOf("?"),e<0?r:r.substr(0,e)}static completeURL(t,e){const r=e.trim();if(r.startsWith("data:")||r.startsWith("blob:")||r.startsWith("javascript:")||r.startsWith("mailto:"))return e;const s=this.fromString(r);if(s&&s.scheme){return s.securityOrigin()+pe(s.path)+(s.queryParams&&`?${s.queryParams}`)+(s.fragment&&`#${s.fragment}`)}const n=this.fromString(t);if(!n)return null;if(n.isDataURL())return e;if(e.length>1&&"/"===e.charAt(0)&&"/"===e.charAt(1))return n.scheme+":"+e;const i=n.securityOrigin(),a=n.path,o=n.queryParams?"?"+n.queryParams:"";if(!e.length)return i+a+o;if("#"===e.charAt(0))return i+a+o+e;if("?"===e.charAt(0))return i+a+e;const l=e.match(/^[^#?]*/);if(!l||!e.length)throw new Error("Invalid href");let h=l[0];const c=e.substring(h.length);return"/"!==h.charAt(0)&&(h=n.folderPathComponents+"/"+h),i+pe(h)+c}static splitLineAndColumn(t){const e=t.match(me.urlRegex());let r="",s=t;e&&(r=e[1],s=t.substring(e[1].length));const n=/(?::(\d+))?(?::(\d+))?$/.exec(s);let i,a;if(console.assert(Boolean(n)),!n)return{url:t,lineNumber:0,columnNumber:0};"string"==typeof n[1]&&(i=parseInt(n[1],10),i=isNaN(i)?void 0:i-1),"string"==typeof n[2]&&(a=parseInt(n[2],10),a=isNaN(a)?void 0:a-1);let o=r+s.substring(0,s.length-n[0].length);if(void 0===n[1]&&void 0===n[2]){const t=/wasm-function\[\d+\]:0x([a-z0-9]+)$/g.exec(s);t&&"string"==typeof t[1]&&(o=me.removeWasmFunctionInfoFromURL(o),a=parseInt(t[1],16),a=isNaN(a)?void 0:a)}return{url:o,lineNumber:i,columnNumber:a}}static removeWasmFunctionInfoFromURL(t){const e=t.search(/:wasm-function\[\d+\]/);return-1===e?t:me.substring(t,0,e)}static beginsWithWindowsDriveLetter(t){return/^[A-Za-z]:/.test(t)}static beginsWithScheme(t){return/^[A-Za-z][A-Za-z0-9+.-]*:/.test(t)}static isRelativeURL(t){return!this.beginsWithScheme(t)||this.beginsWithWindowsDriveLetter(t)}get displayName(){return this.#x?this.#x:this.isDataURL()?this.dataURLDisplayName():this.isBlobURL()||this.isAboutBlank()?this.url:(this.#x=this.lastPathComponent,this.#x||(this.#x=(this.host||"")+"/"),"/"===this.#x&&(this.#x=this.url),this.#x)}dataURLDisplayName(){return this.#v?this.#v:this.isDataURL()?(this.#v=e.StringUtilities.trimEndWithMaxLength(this.url,20),this.#v):""}isAboutBlank(){return"about:blank"===this.url}isDataURL(){return"data"===this.scheme}isHttpOrHttps(){return"http"===this.scheme||"https"===this.scheme}isBlobURL(){return this.url.startsWith("blob:")}lastPathComponentWithFragment(){return this.lastPathComponent+(this.fragment?"#"+this.fragment:"")}domain(){return this.isDataURL()?"data:":this.host+(this.port?":"+this.port:"")}securityOrigin(){if(this.isDataURL())return"data:";return(this.isBlobURL()?this.blobInnerScheme:this.scheme)+"://"+this.domain()}urlWithoutScheme(){return this.scheme&&this.url.startsWith(this.scheme+"://")?this.url.substring(this.scheme.length+3):this.url}static urlRegexInstance=null}var ye=Object.freeze({__proto__:null,normalizePath:pe,schemeIs:function(t,e){try{return new URL(t).protocol===e}catch(t){return!1}},ParsedURL:me});class fe{#T;#R;#z;#I;constructor(t,e){this.#T=t,this.#R=e||1,this.#z=0,this.#I=0}isCanceled(){return this.#T.parent.isCanceled()}setTitle(t){this.#T.parent.setTitle(t)}done(){this.setWorked(this.#I),this.#T.childDone()}setTotalWork(t){this.#I=t,this.#T.update()}setWorked(t,e){this.#z=t,void 0!==e&&this.setTitle(e),this.#T.update()}incrementWorked(t){this.setWorked(this.#z+(t||1))}getWeight(){return this.#R}getWorked(){return this.#z}getTotalWork(){return this.#I}}var we=Object.freeze({__proto__:null,Progress:class{setTotalWork(t){}setTitle(t){}setWorked(t,e){}incrementWorked(t){}done(){}isCanceled(){return!1}},CompositeProgress:class{parent;#P;#A;constructor(t){this.parent=t,this.#P=[],this.#A=0,this.parent.setTotalWork(1),this.parent.setWorked(0)}childDone(){++this.#A===this.#P.length&&this.parent.done()}createSubProgress(t){const e=new fe(this,t);return this.#P.push(e),e}update(){let t=0,e=0;for(let r=0;r<this.#P.length;++r){const s=this.#P[r];s.getTotalWork()&&(e+=s.getWeight()*s.getWorked()/s.getTotalWork()),t+=s.getWeight()}this.parent.setWorked(e/t)}},SubProgress:fe,ProgressProxy:class{#E;#k;constructor(t,e){this.#E=t,this.#k=e}isCanceled(){return!!this.#E&&this.#E.isCanceled()}setTitle(t){this.#E&&this.#E.setTitle(t)}done(){this.#E&&this.#E.done(),this.#k&&this.#k()}setTotalWork(t){this.#E&&this.#E.setTotalWork(t)}setWorked(t,e){this.#E&&this.#E.setWorked(t,e)}incrementWorked(t){this.#E&&this.#E.incrementWorked(t)}}}),be=Object.freeze({__proto__:null});var Se=Object.freeze({__proto__:null,ResolverBase:class{#C=new Map;async waitFor(t){const e=this.getForId(t);return e||this.getOrCreatePromise(t)}tryGet(t,e){const r=this.getForId(t);if(!r){const r=()=>{};return this.getOrCreatePromise(t).catch(r).then((t=>{t&&e(t)})),null}return r}clear(){this.stopListening();for(const[t,{reject:e}]of this.#C.entries())e(new Error(`Object with ${t} never resolved.`));this.#C.clear()}getOrCreatePromise(t){const e=this.#C.get(t);if(e)return e.promise;let r=()=>{},s=()=>{};const n=new Promise(((t,e)=>{r=t,s=e}));return this.#C.set(t,{promise:n,resolve:r,reject:s}),this.startListening(),n}onResolve(t,e){const r=this.#C.get(t);this.#C.delete(t),0===this.#C.size&&this.stopListening(),r?.resolve(e)}}});const xe={fetchAndXHR:"`Fetch` and `XHR`",javascript:"JavaScript",js:"JS",css:"CSS",img:"Img",media:"Media",font:"Font",doc:"Doc",ws:"WS",webassembly:"WebAssembly",wasm:"Wasm",manifest:"Manifest",other:"Other",document:"Document",stylesheet:"Stylesheet",image:"Image",script:"Script",texttrack:"TextTrack",fetch:"Fetch",eventsource:"EventSource",websocket:"WebSocket",webtransport:"WebTransport",signedexchange:"SignedExchange",ping:"Ping",cspviolationreport:"CSPViolationReport",preflight:"Preflight",webbundle:"WebBundle"},ve=r.i18n.registerUIStrings("core/common/ResourceType.ts",xe),Te=r.i18n.getLazilyComputedLocalizedString.bind(void 0,ve);class Re{#L;#_;#O;#B;constructor(t,e,r,s){this.#L=t,this.#_=e,this.#O=r,this.#B=s}static fromMimeType(t){return t?t.startsWith("text/html")?Ae.Document:t.startsWith("text/css")?Ae.Stylesheet:t.startsWith("image/")?Ae.Image:t.startsWith("text/")?Ae.Script:t.includes("font")?Ae.Font:t.includes("script")?Ae.Script:t.includes("octet")?Ae.Other:t.includes("application")?Ae.Script:Ae.Other:Ae.Other}static fromMimeTypeOverride(t){return"application/manifest+json"===t?Ae.Manifest:"application/wasm"===t?Ae.Wasm:"application/webbundle"===t?Ae.WebBundle:null}static fromURL(t){return ke.get(me.extractExtension(t))||null}static fromName(t){for(const e in Ae){const r=Ae[e];if(r.name()===t)return r}return null}static mimeFromURL(t){const e=me.extractName(t);if(Ee.has(e))return Ee.get(e);let r=me.extractExtension(t).toLowerCase();return"html"===r&&e.endsWith(".component.html")&&(r="component.html"),Ce.get(r)}static mimeFromExtension(t){return Ce.get(t)}static simplifyContentType(t){return new RegExp("^application(.*json$|/json+.*)").test(t)?"application/json":t}static mediaTypeForMetrics(t,e,r){return"text/javascript"!==t?t:e?"text/javascript+sourcemapped":r?"text/javascript+minified":"text/javascript+plain"}name(){return this.#L}title(){return this.#_()}category(){return this.#O}isTextType(){return this.#B}isScript(){return"script"===this.#L||"sm-script"===this.#L}hasScripts(){return this.isScript()||this.isDocument()}isStyleSheet(){return"stylesheet"===this.#L||"sm-stylesheet"===this.#L}hasStyleSheets(){return this.isStyleSheet()||this.isDocument()}isDocument(){return"document"===this.#L}isDocumentOrScriptOrStyleSheet(){return this.isDocument()||this.isScript()||this.isStyleSheet()}isFont(){return"font"===this.#L}isImage(){return"image"===this.#L}isFromSourceMap(){return this.#L.startsWith("sm-")}isWebbundle(){return"webbundle"===this.#L}toString(){return this.#L}canonicalMimeType(){return this.isDocument()?"text/html":this.isScript()?"text/javascript":this.isStyleSheet()?"text/css":""}}class ze{title;shortTitle;constructor(t,e){this.title=t,this.shortTitle=e}static categoryByTitle(t){return Object.values(Ie).find((e=>e.title()===t))||null}}const Ie={XHR:new ze(Te(xe.fetchAndXHR),r.i18n.lockedLazyString("Fetch/XHR")),Document:new ze(Te(xe.document),Te(xe.doc)),Stylesheet:new ze(Te(xe.css),Te(xe.css)),Script:new ze(Te(xe.javascript),Te(xe.js)),Font:new ze(Te(xe.font),Te(xe.font)),Image:new ze(Te(xe.image),Te(xe.img)),Media:new ze(Te(xe.media),Te(xe.media)),Manifest:new ze(Te(xe.manifest),Te(xe.manifest)),WebSocket:new ze(Te(xe.websocket),Te(xe.ws)),Wasm:new ze(Te(xe.webassembly),Te(xe.wasm)),Other:new ze(Te(xe.other),Te(xe.other))},Pe={XHR:new ze(Te(xe.fetchAndXHR),r.i18n.lockedLazyString("Fetch/XHR")),Script:new ze(Te(xe.javascript),Te(xe.js)),Image:new ze(Te(xe.image),Te(xe.img)),Media:new ze(Te(xe.media),Te(xe.media)),Other:new ze(Te(xe.other),Te(xe.other))},Ae={Document:new Re("document",Te(xe.document),Ie.Document,!0),Stylesheet:new Re("stylesheet",Te(xe.stylesheet),Ie.Stylesheet,!0),Image:new Re("image",Te(xe.image),Ie.Image,!1),Media:new Re("media",Te(xe.media),Ie.Media,!1),Font:new Re("font",Te(xe.font),Ie.Font,!1),Script:new Re("script",Te(xe.script),Ie.Script,!0),TextTrack:new Re("texttrack",Te(xe.texttrack),Ie.Other,!0),XHR:new Re("xhr",r.i18n.lockedLazyString("XHR"),Ie.XHR,!0),Fetch:new Re("fetch",Te(xe.fetch),Ie.XHR,!0),Prefetch:new Re("prefetch",r.i18n.lockedLazyString("Prefetch"),Ie.Document,!0),EventSource:new Re("eventsource",Te(xe.eventsource),Ie.XHR,!0),WebSocket:new Re("websocket",Te(xe.websocket),Ie.WebSocket,!1),WebTransport:new Re("webtransport",Te(xe.webtransport),Ie.WebSocket,!1),Wasm:new Re("wasm",Te(xe.wasm),Ie.Wasm,!1),Manifest:new Re("manifest",Te(xe.manifest),Ie.Manifest,!0),SignedExchange:new Re("signed-exchange",Te(xe.signedexchange),Ie.Other,!1),Ping:new Re("ping",Te(xe.ping),Ie.Other,!1),CSPViolationReport:new Re("csp-violation-report",Te(xe.cspviolationreport),Ie.Other,!1),Other:new Re("other",Te(xe.other),Ie.Other,!1),Preflight:new Re("preflight",Te(xe.preflight),Ie.Other,!0),SourceMapScript:new Re("sm-script",Te(xe.script),Ie.Script,!0),SourceMapStyleSheet:new Re("sm-stylesheet",Te(xe.stylesheet),Ie.Stylesheet,!0),WebBundle:new Re("webbundle",Te(xe.webbundle),Ie.Other,!1)},Ee=new Map([["Cakefile","text/x-coffeescript"]]),ke=new Map([["js",Ae.Script],["mjs",Ae.Script],["css",Ae.Stylesheet],["xsl",Ae.Stylesheet],["avif",Ae.Image],["bmp",Ae.Image],["gif",Ae.Image],["ico",Ae.Image],["jpeg",Ae.Image],["jpg",Ae.Image],["jxl",Ae.Image],["png",Ae.Image],["svg",Ae.Image],["tif",Ae.Image],["tiff",Ae.Image],["vue",Ae.Document],["webmanifest",Ae.Manifest],["webp",Ae.Media],["otf",Ae.Font],["ttc",Ae.Font],["ttf",Ae.Font],["woff",Ae.Font],["woff2",Ae.Font],["wasm",Ae.Wasm]]),Ce=new Map([["js","text/javascript"],["mjs","text/javascript"],["css","text/css"],["html","text/html"],["htm","text/html"],["xml","application/xml"],["xsl","application/xml"],["wasm","application/wasm"],["webmanifest","application/manifest+json"],["asp","application/x-aspx"],["aspx","application/x-aspx"],["jsp","application/x-jsp"],["c","text/x-c++src"],["cc","text/x-c++src"],["cpp","text/x-c++src"],["h","text/x-c++src"],["m","text/x-c++src"],["mm","text/x-c++src"],["coffee","text/x-coffeescript"],["dart","application/vnd.dart"],["ts","text/typescript"],["tsx","text/typescript-jsx"],["json","application/json"],["gyp","application/json"],["gypi","application/json"],["map","application/json"],["cs","text/x-csharp"],["go","text/x-go"],["java","text/x-java"],["kt","text/x-kotlin"],["scala","text/x-scala"],["less","text/x-less"],["php","application/x-httpd-php"],["phtml","application/x-httpd-php"],["py","text/x-python"],["sh","text/x-sh"],["gss","text/x-gss"],["sass","text/x-sass"],["scss","text/x-scss"],["vtt","text/vtt"],["ls","text/x-livescript"],["md","text/markdown"],["cljs","text/x-clojure"],["cljc","text/x-clojure"],["cljx","text/x-clojure"],["styl","text/x-styl"],["jsx","text/jsx"],["avif","image/avif"],["bmp","image/bmp"],["gif","image/gif"],["ico","image/ico"],["jpeg","image/jpeg"],["jpg","image/jpeg"],["jxl","image/jxl"],["png","image/png"],["svg","image/svg+xml"],["tif","image/tif"],["tiff","image/tiff"],["webp","image/webp"],["otf","font/otf"],["ttc","font/collection"],["ttf","font/ttf"],["woff","font/woff"],["woff2","font/woff2"],["component.html","text/x.angular"],["svelte","text/x.svelte"],["vue","text/x.vue"]]);var Le=Object.freeze({__proto__:null,ResourceType:Re,ResourceCategory:ze,resourceCategories:Ie,resourceCategoriesReactNative:Pe,resourceTypes:Ae,resourceTypeByExtension:ke,mimeTypeByExtension:Ce});const _e=new Map;const Oe=[];var Be=Object.freeze({__proto__:null,registerLateInitializationRunnable:function(t){const{id:e,loadRunnable:r}=t;if(_e.has(e))throw new Error(`Duplicate late Initializable runnable id '${e}'`);_e.set(e,r)},maybeRemoveLateInitializationRunnable:function(t){return _e.delete(t)},lateInitializationRunnables:function(){return[..._e.values()]},registerEarlyInitializationRunnable:function(t){Oe.push(t)},earlyInitializationRunnables:function(){return Oe}});class Ne{begin;end;data;constructor(t,e,r){if(t>e)throw new Error("Invalid segment");this.begin=t,this.end=e,this.data=r}intersects(t){return this.begin<t.end&&t.begin<this.end}}var Ve=Object.freeze({__proto__:null,Segment:Ne,SegmentedRange:class{#N;#V;constructor(t){this.#N=[],this.#V=t}append(t){let r=e.ArrayUtilities.lowerBound(this.#N,t,((t,e)=>t.begin-e.begin)),s=r,n=null;if(r>0){const e=this.#N[r-1];n=this.tryMerge(e,t),n?(--r,t=n):this.#N[r-1].end>=t.begin&&(t.end<e.end&&this.#N.splice(r,0,new Ne(t.end,e.end,e.data)),e.end=t.begin)}for(;s<this.#N.length&&this.#N[s].end<=t.end;)++s;s<this.#N.length&&(n=this.tryMerge(t,this.#N[s]),n?(s++,t=n):t.intersects(this.#N[s])&&(this.#N[s].begin=t.end)),this.#N.splice(r,s-r,t)}appendRange(t){t.segments().forEach((t=>this.append(t)))}segments(){return this.#N}tryMerge(t,e){const r=this.#V&&this.#V(t,e);return r?(r.begin=t.begin,r.end=Math.max(t.end,e.end),r):null}}});const Ge={elements:"Elements",appearance:"Appearance",sources:"Sources",network:"Network",performance:"Performance",console:"Console",persistence:"Persistence",debugger:"Debugger",global:"Global",rendering:"Rendering",grid:"Grid",mobile:"Mobile",memory:"Memory",extension:"Extension",adorner:"Adorner",sync:"Sync"},Me=r.i18n.registerUIStrings("core/common/SettingRegistration.ts",Ge),We=r.i18n.getLocalizedString.bind(void 0,Me);let Xe=[];const Fe=new Set;function De(t){const e=t.settingName;if(Fe.has(e))throw new Error(`Duplicate setting name '${e}'`);Fe.add(e),Xe.push(t)}function je(e){return Xe.filter((r=>t.Runtime.Runtime.isDescriptorEnabled({experiment:r.experiment,condition:r.condition},e)))}function Ue(t,e=!1){if(0===Xe.length||e){Xe=t,Fe.clear();for(const e of t){const t=e.settingName;if(Fe.has(t))throw new Error(`Duplicate setting name '${t}'`);Fe.add(t)}}}function He(){Xe=[],Fe.clear()}function $e(t){const e=Xe.findIndex((e=>e.settingName===t));return!(e<0||!Fe.delete(t))&&(Xe.splice(e,1),!0)}function qe(t){switch(t){case"ELEMENTS":return We(Ge.elements);case"APPEARANCE":return We(Ge.appearance);case"SOURCES":return We(Ge.sources);case"NETWORK":return We(Ge.network);case"PERFORMANCE":return We(Ge.performance);case"CONSOLE":case"EMULATION":return We(Ge.console);case"PERSISTENCE":return We(Ge.persistence);case"DEBUGGER":return We(Ge.debugger);case"GLOBAL":return We(Ge.global);case"RENDERING":return We(Ge.rendering);case"GRID":return We(Ge.grid);case"MOBILE":return We(Ge.mobile);case"MEMORY":return We(Ge.memory);case"EXTENSIONS":return We(Ge.extension);case"ADORNER":return We(Ge.adorner);case"":return r.i18n.lockedString("");case"SYNC":return We(Ge.sync)}}var Ze=Object.freeze({__proto__:null,registerSettingExtension:De,getRegisteredSettings:je,registerSettingsForTest:Ue,resetSettings:He,maybeRemoveSettingExtension:$e,getLocalizedSettingsCategory:qe});let Ye;class Ke{syncedStorage;globalStorage;localStorage;#G;settingNameSet;orderValuesBySettingCategory;#M;#W;moduleSettings;#X;constructor(e,r,s,n){this.syncedStorage=e,this.globalStorage=r,this.localStorage=s,this.#G=new Qe({}),this.settingNameSet=new Set,this.orderValuesBySettingCategory=new Map,this.#M=new Dt,this.#W=new Map,this.moduleSettings=new Map,this.#X=n;for(const e of this.getRegisteredSettings()){const{settingName:r,defaultValue:s,storageType:n}=e,i="regex"===e.settingType,a="function"==typeof s?s(this.#X):s,o=i&&"string"==typeof a?this.createRegExpSetting(r,a,void 0,n):this.createSetting(r,a,n);o.setTitleFunction(e.title),e.userActionCondition&&o.setRequiresUserAction(Boolean(t.Runtime.Runtime.queryParam(e.userActionCondition))),o.setRegistration(e),this.registerModuleSetting(o)}}getRegisteredSettings(){return je(this.#X)}static hasInstance(){return void 0!==Ye}static instance(t={forceNew:null,syncedStorage:null,globalStorage:null,localStorage:null}){const{forceNew:e,syncedStorage:r,globalStorage:s,localStorage:n,config:i}=t;if(!Ye||e){if(!r||!s||!n)throw new Error(`Unable to create settings: global and local storage must be provided: ${(new Error).stack}`);Ye=new Ke(r,s,n,i)}return Ye}static removeInstance(){Ye=void 0}getHostConfig(){return this.#X}registerModuleSetting(t){const e=t.name,r=t.category(),s=t.order();if(this.settingNameSet.has(e))throw new Error(`Duplicate Setting name '${e}'`);if(r&&s){const t=this.orderValuesBySettingCategory.get(r)||new Set;if(t.has(s))throw new Error(`Duplicate order value '${s}' for settings category '${r}'`);t.add(s),this.orderValuesBySettingCategory.set(r,t)}this.settingNameSet.add(e),this.moduleSettings.set(t.name,t)}static normalizeSettingName(t){return[nr.GLOBAL_VERSION_SETTING_NAME,nr.SYNCED_VERSION_SETTING_NAME,nr.LOCAL_VERSION_SETTING_NAME,"currentDockState","isUnderTest"].includes(t)?t:e.StringUtilities.toKebabCase(t)}moduleSetting(t){const e=this.moduleSettings.get(t);if(!e)throw new Error("No setting registered: "+t);return e}settingForTest(t){const e=this.#W.get(t);if(!e)throw new Error("No setting registered: "+t);return e}createSetting(t,e,r){const s=this.storageFromType(r);let n=this.#W.get(t);return n||(n=new rr(t,e,this.#M,s),this.#W.set(t,n)),n}createLocalSetting(t,e){return this.createSetting(t,e,"Local")}createRegExpSetting(t,e,r,s){return this.#W.get(t)||this.#W.set(t,new sr(t,e,this.#M,this.storageFromType(s),r)),this.#W.get(t)}clearAll(){this.globalStorage.removeAll(),this.syncedStorage.removeAll(),this.localStorage.removeAll(),(new nr).resetToCurrent()}storageFromType(t){switch(t){case"Local":return this.localStorage;case"Session":return this.#G;case"Global":return this.globalStorage;case"Synced":return this.syncedStorage}return this.globalStorage}getRegistry(){return this.#W}}const Je={register:()=>{},set:()=>{},get:()=>Promise.resolve(""),remove:()=>{},clear:()=>{}};class Qe{object;backingStore;storagePrefix;constructor(t,e=Je,r=""){this.object=t,this.backingStore=e,this.storagePrefix=r}register(t){t=this.storagePrefix+t,this.backingStore.register(t)}set(t,e){t=this.storagePrefix+t,this.object[t]=e,this.backingStore.set(t,e)}has(t){return(t=this.storagePrefix+t)in this.object}get(t){return t=this.storagePrefix+t,this.object[t]}async forceGet(t){const e=this.storagePrefix+t,r=await this.backingStore.get(e);return r&&r!==this.object[e]?this.set(t,r):r||this.remove(t),r}remove(t){t=this.storagePrefix+t,delete this.object[t],this.backingStore.remove(t)}removeAll(){this.object={},this.backingStore.clear()}keys(){return Object.keys(this.object)}dumpSizes(){te.instance().log("Ten largest settings: ");const t={__proto__:null};for(const e in this.object)t[e]=this.object[e].length;const e=Object.keys(t);e.sort((function(e,r){return t[r]-t[e]}));for(let r=0;r<10&&r<e.length;++r)te.instance().log("Setting: '"+e[r]+"', size: "+t[e[r]])}}function tr(t){const e=t.name,r=Ke.instance();r.getRegistry().delete(e),r.moduleSettings.delete(e),t.storage.remove(e)}class er{disabled;warning;experiment;constructor({deprecationNotice:e}){if(!e)throw new Error("Cannot create deprecation info for a non-deprecated setting");this.disabled=e.disabled,this.warning=e.warning(),this.experiment=e.experiment?t.Runtime.experiments.allConfigurableExperiments().find((t=>t.name===e.experiment)):void 0}}class rr{name;defaultValue;eventSupport;storage;#F;#_;#D=null;#j;#U;#H=JSON;#$;#q;#Z=null;constructor(t,e,r,s){this.name=t,this.defaultValue=e,this.eventSupport=r,this.storage=s,s.register(this.name)}setSerializer(t){this.#H=t}addChangeListener(t,e){return this.eventSupport.addEventListener(this.name,t,e)}removeChangeListener(t,e){this.eventSupport.removeEventListener(this.name,t,e)}title(){return this.#_?this.#_:this.#F?this.#F():""}setTitleFunction(t){t&&(this.#F=t)}setTitle(t){this.#_=t}setRequiresUserAction(t){this.#j=t}disabled(){if(this.#D?.disabledCondition){const{disabled:t}=this.#D.disabledCondition(Ke.instance().getHostConfig());if(t)return!0}return this.#q||!1}disabledReason(){if(this.#D?.disabledCondition){const t=this.#D.disabledCondition(Ke.instance().getHostConfig());if(t.disabled)return t.reason}}setDisabled(t){this.#q=t,this.eventSupport.dispatchEventToListeners(this.name)}get(){if(this.#j&&!this.#$)return this.defaultValue;if(void 0!==this.#U)return this.#U;if(this.#U=this.defaultValue,this.storage.has(this.name))try{this.#U=this.#H.parse(this.storage.get(this.name))}catch(t){this.storage.remove(this.name)}return this.#U}async forceGet(){const t=this.name,e=this.storage.get(t),r=await this.storage.forceGet(t);if(this.#U=this.defaultValue,r)try{this.#U=this.#H.parse(r)}catch(t){this.storage.remove(this.name)}return e!==r&&this.eventSupport.dispatchEventToListeners(this.name,this.#U),this.#U}set(t){this.#$=!0,this.#U=t;try{const e=this.#H.stringify(t);try{this.storage.set(this.name,e)}catch(t){this.printSettingsSavingError(t.message,this.name,e)}}catch(t){te.instance().error("Cannot stringify setting with name: "+this.name+", error: "+t.message)}this.eventSupport.dispatchEventToListeners(this.name,t)}setRegistration(e){this.#D=e;const{deprecationNotice:r}=e;if(r?.disabled){const e=r.experiment?t.Runtime.experiments.allConfigurableExperiments().find((t=>t.name===r.experiment)):void 0;e&&!e.isEnabled()||(this.set(this.defaultValue),this.setDisabled(!0))}}type(){return this.#D?this.#D.settingType:null}options(){return this.#D&&this.#D.options?this.#D.options.map((t=>{const{value:e,title:r,text:s,raw:n}=t;return{value:e,title:r(),text:"function"==typeof s?s():s,raw:n}})):[]}reloadRequired(){return this.#D&&this.#D.reloadRequired||null}category(){return this.#D&&this.#D.category||null}tags(){return this.#D&&this.#D.tags?this.#D.tags.map((t=>t())).join("\0"):null}order(){return this.#D&&this.#D.order||null}get deprecation(){return this.#D&&this.#D.deprecationNotice?(this.#Z||(this.#Z=new er(this.#D)),this.#Z):null}printSettingsSavingError(t,e,r){const s="Error saving setting with name: "+this.name+", value length: "+r.length+". Error: "+t;console.error(s),te.instance().error(s),this.storage.dumpSizes()}}class sr extends rr{#Y;#K;constructor(t,e,r,s,n){super(t,e?[{pattern:e}]:[],r,s),this.#Y=n}get(){const t=[],e=this.getAsArray();for(let r=0;r<e.length;++r){const s=e[r];s.pattern&&!s.disabled&&t.push(s.pattern)}return t.join("|")}getAsArray(){return super.get()}set(t){this.setAsArray([{pattern:t,disabled:!1}])}setAsArray(t){this.#K=void 0,super.set(t)}asRegExp(){if(void 0!==this.#K)return this.#K;this.#K=null;try{const t=this.get();t&&(this.#K=new RegExp(t,this.#Y||""))}catch(t){}return this.#K}}class nr{static GLOBAL_VERSION_SETTING_NAME="inspectorVersion";static SYNCED_VERSION_SETTING_NAME="syncedInspectorVersion";static LOCAL_VERSION_SETTING_NAME="localInspectorVersion";static CURRENT_VERSION=37;#J;#Q;#tt;constructor(){this.#J=Ke.instance().createSetting(nr.GLOBAL_VERSION_SETTING_NAME,nr.CURRENT_VERSION,"Global"),this.#Q=Ke.instance().createSetting(nr.SYNCED_VERSION_SETTING_NAME,nr.CURRENT_VERSION,"Synced"),this.#tt=Ke.instance().createSetting(nr.LOCAL_VERSION_SETTING_NAME,nr.CURRENT_VERSION,"Local")}resetToCurrent(){this.#J.set(nr.CURRENT_VERSION),this.#Q.set(nr.CURRENT_VERSION),this.#tt.set(nr.CURRENT_VERSION)}updateVersion(){const t=nr.CURRENT_VERSION,e=Math.min(this.#J.get(),this.#Q.get(),this.#tt.get()),r=this.methodsToRunToUpdateVersion(e,t);console.assert(void 0===this[`updateVersionFrom${t}To${t+1}`],"Unexpected migration method found. Increment CURRENT_VERSION or remove the method.");for(const t of r)this[t].call(this);this.resetToCurrent()}methodsToRunToUpdateVersion(t,e){const r=[];for(let s=t;s<e;++s)r.push("updateVersionFrom"+s+"To"+(s+1));return r}updateVersionFrom0To1(){this.clearBreakpointsWhenTooMany(Ke.instance().createLocalSetting("breakpoints",[]),5e5)}updateVersionFrom1To2(){Ke.instance().createSetting("previouslyViewedFiles",[]).set([])}updateVersionFrom2To3(){Ke.instance().createSetting("fileSystemMapping",{}).set({}),tr(Ke.instance().createSetting("fileMappingEntries",[]))}updateVersionFrom3To4(){const t=Ke.instance().createSetting("showHeaSnapshotObjectsHiddenProperties",!1);ir("showAdvancedHeapSnapshotProperties").set(t.get()),tr(t)}updateVersionFrom4To5(){const t={FileSystemViewSidebarWidth:"fileSystemViewSplitViewState",elementsSidebarWidth:"elementsPanelSplitViewState",StylesPaneSplitRatio:"stylesPaneSplitViewState",heapSnapshotRetainersViewSize:"heapSnapshotSplitViewState","InspectorView.splitView":"InspectorView.splitViewState","InspectorView.screencastSplitView":"InspectorView.screencastSplitViewState","Inspector.drawerSplitView":"Inspector.drawerSplitViewState",layerDetailsSplitView:"layerDetailsSplitViewState",networkSidebarWidth:"networkPanelSplitViewState",sourcesSidebarWidth:"sourcesPanelSplitViewState",scriptsPanelNavigatorSidebarWidth:"sourcesPanelNavigatorSplitViewState",sourcesPanelSplitSidebarRatio:"sourcesPanelDebuggerSidebarSplitViewState","timeline-details":"timelinePanelDetailsSplitViewState","timeline-split":"timelinePanelRecorsSplitViewState","timeline-view":"timelinePanelTimelineStackSplitViewState",auditsSidebarWidth:"auditsPanelSplitViewState",layersSidebarWidth:"layersPanelSplitViewState",profilesSidebarWidth:"profilesPanelSplitViewState",resourcesSidebarWidth:"resourcesPanelSplitViewState"},e={};for(const r in t){const s=t[r],n=r+"H";let i=null;const a=Ke.instance().createSetting(r,e);a.get()!==e&&(i=i||{},i.vertical={},i.vertical.size=a.get(),tr(a));const o=Ke.instance().createSetting(n,e);o.get()!==e&&(i=i||{},i.horizontal={},i.horizontal.size=o.get(),tr(o)),i&&Ke.instance().createSetting(s,{}).set(i)}}updateVersionFrom5To6(){const t={debuggerSidebarHidden:"sourcesPanelSplitViewState",navigatorHidden:"sourcesPanelNavigatorSplitViewState","WebInspector.Drawer.showOnLoad":"Inspector.drawerSplitViewState"};for(const e in t){const r=Ke.instance().createSetting(e,null);if(null===r.get()){tr(r);continue}const s=t[e],n="WebInspector.Drawer.showOnLoad"===e,i=r.get()!==n;tr(r);const a=i?"OnlyMain":"Both",o=Ke.instance().createSetting(s,{}),l=o.get()||{};l.vertical=l.vertical||{},l.vertical.showMode=a,l.horizontal=l.horizontal||{},l.horizontal.showMode=a,o.set(l)}}updateVersionFrom6To7(){const t={sourcesPanelNavigatorSplitViewState:"sourcesPanelNavigatorSplitViewState",elementsPanelSplitViewState:"elementsPanelSplitViewState",stylesPaneSplitViewState:"stylesPaneSplitViewState",sourcesPanelDebuggerSidebarSplitViewState:"sourcesPanelDebuggerSidebarSplitViewState"},e={};for(const r in t){const t=Ke.instance().createSetting(r,e),s=t.get();s!==e&&(s.vertical&&s.vertical.size&&s.vertical.size<1&&(s.vertical.size=0),s.horizontal&&s.horizontal.size&&s.horizontal.size<1&&(s.horizontal.size=0),t.set(s))}}updateVersionFrom7To8(){}updateVersionFrom8To9(){const t=["skipStackFramesPattern","workspaceFolderExcludePattern"];for(let e=0;e<t.length;++e){const r=Ke.instance().createSetting(t[e],"");let s=r.get();if(!s)return;"string"==typeof s&&(s=[s]);for(let t=0;t<s.length;++t)"string"==typeof s[t]&&(s[t]={pattern:s[t]});r.set(s)}}updateVersionFrom9To10(){if(window.localStorage)for(const t in window.localStorage)t.startsWith("revision-history")&&window.localStorage.removeItem(t)}updateVersionFrom10To11(){const t=Ke.instance().createSetting("customDevicePresets",void 0),e=t.get();if(!Array.isArray(e))return;const r=[];for(let t=0;t<e.length;++t){const s=e[t],n={};n.title=s.title,n.type="unknown",n["user-agent"]=s.userAgent,n.capabilities=[],s.touch&&n.capabilities.push("touch"),s.mobile&&n.capabilities.push("mobile"),n.screen={},n.screen.vertical={width:s.width,height:s.height},n.screen.horizontal={width:s.height,height:s.width},n.screen["device-pixel-ratio"]=s.deviceScaleFactor,n.modes=[],n["show-by-default"]=!0,n.show="Default",r.push(n)}r.length&&Ke.instance().createSetting("customEmulatedDeviceList",[]).set(r),tr(t)}updateVersionFrom11To12(){this.migrateSettingsFromLocalStorage()}updateVersionFrom12To13(){this.migrateSettingsFromLocalStorage(),tr(Ke.instance().createSetting("timelineOverviewMode",""))}updateVersionFrom13To14(){const t={throughput:-1,latency:0};Ke.instance().createSetting("networkConditions",t).set(t)}updateVersionFrom14To15(){const t=Ke.instance().createLocalSetting("workspaceExcludedFolders",{}),e=t.get(),r={};for(const t in e){r[t]=[];for(const s of e[t])r[t].push(s.path)}t.set(r)}updateVersionFrom15To16(){const t=Ke.instance().createSetting("InspectorView.panelOrder",{}),e=t.get();for(const t of Object.keys(e))e[t]=10*(e[t]+1);t.set(e)}updateVersionFrom16To17(){const t=Ke.instance().createSetting("networkConditionsCustomProfiles",[]),e=t.get(),r=[];if(Array.isArray(e))for(const t of e)"string"==typeof t.title&&"object"==typeof t.value&&"number"==typeof t.value.throughput&&"number"==typeof t.value.latency&&r.push({title:t.title,value:{download:t.value.throughput,upload:t.value.throughput,latency:t.value.latency}});t.set(r)}updateVersionFrom17To18(){const t=Ke.instance().createLocalSetting("workspaceExcludedFolders",{}),e=t.get(),r={};for(const t in e){let s=t.replace(/\\/g,"/");s.startsWith("file://")||(s=s.startsWith("/")?"file://"+s:"file:///"+s),r[s]=e[t]}t.set(r)}updateVersionFrom18To19(){const t=Ke.instance().createSetting("networkLogColumnsVisibility",{status:!0,type:!0,initiator:!0,size:!0,time:!0}),e=t.get();e.name=!0,e.timeline=!0;const r={};for(const t in e)e.hasOwnProperty(t)&&(r[t.toLowerCase()]={visible:e[t]});Ke.instance().createSetting("networkLogColumns",{}).set(r),tr(t)}updateVersionFrom19To20(){const t=Ke.instance().createSetting("InspectorView.panelOrder",{});Ke.instance().createSetting("panel-tabOrder",{}).set(t.get()),tr(t)}updateVersionFrom20To21(){const t=Ke.instance().createSetting("networkLogColumns",{}),e=t.get();delete e.timeline,delete e.waterfall,t.set(e)}updateVersionFrom21To22(){const t=Ke.instance().createLocalSetting("breakpoints",[]),e=t.get();for(const t of e)t.url=t.sourceFileId,delete t.sourceFileId;t.set(e)}updateVersionFrom22To23(){}updateVersionFrom23To24(){const t=Ke.instance().createSetting("searchInContentScripts",!1);Ke.instance().createSetting("searchInAnonymousAndContentScripts",!1).set(t.get()),tr(t)}updateVersionFrom24To25(){const t=Ke.instance().createSetting("networkLogColumns",{status:!0,type:!0,initiator:!0,size:!0,time:!0}),e=t.get();delete e.product,t.set(e)}updateVersionFrom25To26(){const t=Ke.instance().createSetting("messageURLFilters",{}),e=Object.keys(t.get()).map((t=>`-url:${t}`)).join(" ");if(e){const t=Ke.instance().createSetting("console.textFilter",""),r=t.get()?` ${t.get()}`:"";t.set(`${e}${r}`)}tr(t)}updateVersionFrom26To27(){function t(t,e,r){const s=Ke.instance().createSetting(t,{}),n=s.get();e in n&&(n[r]=n[e],delete n[e],s.set(n))}t("panel-tabOrder","audits2","audits"),t("panel-closeableTabs","audits2","audits"),function(t,e,r){const s=Ke.instance().createSetting(t,"");s.get()===e&&s.set(r)}("panel-selectedTab","audits2","audits")}updateVersionFrom27To28(){const t=Ke.instance().createSetting("uiTheme","systemPreferred");"default"===t.get()&&t.set("systemPreferred")}updateVersionFrom28To29(){function t(t,e,r){const s=Ke.instance().createSetting(t,{}),n=s.get();e in n&&(n[r]=n[e],delete n[e],s.set(n))}t("panel-tabOrder","audits","lighthouse"),t("panel-closeableTabs","audits","lighthouse"),function(t,e,r){const s=Ke.instance().createSetting(t,"");s.get()===e&&s.set(r)}("panel-selectedTab","audits","lighthouse")}updateVersionFrom29To30(){const t=Ke.instance().createSetting("closeableTabs",{}),e=Ke.instance().createSetting("panel-closeableTabs",{}),r=Ke.instance().createSetting("drawer-view-closeableTabs",{}),s=e.get(),n=e.get(),i=Object.assign(n,s);t.set(i),tr(e),tr(r)}updateVersionFrom30To31(){tr(Ke.instance().createSetting("recorder_recordings",[]))}updateVersionFrom31To32(){const t=Ke.instance().createLocalSetting("breakpoints",[]),e=t.get();for(const t of e)t.resourceTypeName="script";t.set(e)}updateVersionFrom32To33(){const t=Ke.instance().createLocalSetting("previouslyViewedFiles",[]);let e=t.get();e=e.filter((t=>"url"in t));for(const t of e)t.resourceTypeName="script";t.set(e)}updateVersionFrom33To34(){const t=Ke.instance().createLocalSetting("breakpoints",[]),e=t.get();for(const t of e){const e=t.condition.startsWith("/** DEVTOOLS_LOGPOINT */ console.log(")&&t.condition.endsWith(")");t.isLogpoint=e}t.set(e)}updateVersionFrom34To35(){const t=Ke.instance().createLocalSetting("breakpoints",[]),e=t.get();for(const t of e){const{condition:e,isLogpoint:r}=t;r&&(t.condition=e.slice(37,e.length-1))}t.set(e)}updateVersionFrom35To36(){Ke.instance().createSetting("showThirdPartyIssues",!0).set(!0)}updateVersionFrom36To37(){const t=t=>{for(const e of t.keys()){const r=Ke.normalizeSettingName(e);if(r!==e){const s=t.get(e);tr({name:e,storage:t}),t.set(r,s)}}};t(Ke.instance().globalStorage),t(Ke.instance().syncedStorage),t(Ke.instance().localStorage);for(const t of Ke.instance().globalStorage.keys()){if(t.startsWith("data-grid-")&&t.endsWith("-column-weights")||t.endsWith("-tab-order")||"views-location-override"===t||"closeable-tabs"===t){const r=Ke.instance().createSetting(t,{});r.set(e.StringUtilities.toKebabCaseKeys(r.get()))}if(t.endsWith("-selected-tab")){const r=Ke.instance().createSetting(t,"");r.set(e.StringUtilities.toKebabCase(r.get()))}}}migrateSettingsFromLocalStorage(){const t=new Set(["advancedSearchConfig","breakpoints","consoleHistory","domBreakpoints","eventListenerBreakpoints","fileSystemMapping","lastSelectedSourcesSidebarPaneTab","previouslyViewedFiles","savedURLs","watchExpressions","workspaceExcludedFolders","xhrBreakpoints"]);if(window.localStorage)for(const e in window.localStorage){if(t.has(e))continue;const r=window.localStorage[e];window.localStorage.removeItem(e),Ke.instance().globalStorage.set(e,r)}}clearBreakpointsWhenTooMany(t,e){t.get().length>e&&t.set([])}}function ir(t){return Ke.instance().moduleSetting(t)}var ar=Object.freeze({__proto__:null,Settings:Ke,NOOP_STORAGE:Je,SettingsStorage:Qe,Deprecation:er,Setting:rr,RegExpSetting:sr,VersionController:nr,moduleSetting:ir,settingForTest:function(t){return Ke.instance().settingForTest(t)},getLocalizedSettingsCategory:qe,maybeRemoveSettingExtension:$e,registerSettingExtension:De,registerSettingsForTest:Ue,resetSettings:He});var or=Object.freeze({__proto__:null,SimpleHistoryManager:class{#et;#rt;#st;#nt;constructor(t){this.#et=[],this.#rt=-1,this.#st=0,this.#nt=t}readOnlyLock(){++this.#st}releaseReadOnlyLock(){--this.#st}getPreviousValidIndex(){if(this.empty())return-1;let t=this.#rt-1;for(;t>=0&&!this.#et[t].valid();)--t;return t<0?-1:t}getNextValidIndex(){let t=this.#rt+1;for(;t<this.#et.length&&!this.#et[t].valid();)++t;return t>=this.#et.length?-1:t}readOnly(){return Boolean(this.#st)}filterOut(t){if(this.readOnly())return;const e=[];let r=0;for(let s=0;s<this.#et.length;++s)t(this.#et[s])?s<=this.#rt&&++r:e.push(this.#et[s]);this.#et=e,this.#rt=Math.max(0,this.#rt-r)}empty(){return!this.#et.length}active(){return this.empty()?null:this.#et[this.#rt]}push(t){this.readOnly()||(this.empty()||this.#et.splice(this.#rt+1),this.#et.push(t),this.#et.length>this.#nt&&this.#et.shift(),this.#rt=this.#et.length-1)}canRollback(){return this.getPreviousValidIndex()>=0}canRollover(){return this.getNextValidIndex()>=0}rollback(){const t=this.getPreviousValidIndex();return-1!==t&&(this.readOnlyLock(),this.#rt=t,this.#et[t].reveal(),this.releaseReadOnlyLock(),!0)}rollover(){const t=this.getNextValidIndex();return-1!==t&&(this.readOnlyLock(),this.#rt=t,this.#et[t].reveal(),this.releaseReadOnlyLock(),!0)}}});var lr=Object.freeze({__proto__:null,StringOutputStream:class{#it;constructor(){this.#it=""}async write(t){this.#it+=t}async close(){}data(){return this.#it}}});class hr{#at;#ot;#lt;#ht;#ct;#ut;#gt;constructor(t){this.#ot=0,this.#gt=t,this.clear()}static newStringTrie(){return new hr({empty:()=>"",append:(t,e)=>t+e,slice:(t,e,r)=>t.slice(e,r)})}static newArrayTrie(){return new hr({empty:()=>[],append:(t,e)=>t.concat([e]),slice:(t,e,r)=>t.slice(e,r)})}add(t){let e=this.#ot;++this.#ct[this.#ot];for(let r=0;r<t.length;++r){const s=t[r];let n=this.#lt[e].get(s);n||(this.#ut.length?n=this.#ut.pop():(n=this.#at++,this.#ht.push(!1),this.#ct.push(0),this.#lt.push(new Map)),this.#lt[e].set(s,n)),++this.#ct[n],e=n}this.#ht[e]=!0}remove(t){if(!this.has(t))return!1;let e=this.#ot;--this.#ct[this.#ot];for(let r=0;r<t.length;++r){const s=t[r],n=this.#lt[e].get(s);--this.#ct[n]||(this.#lt[e].delete(s),this.#ut.push(n)),e=n}return this.#ht[e]=!1,!0}has(t){let e=this.#ot;for(let r=0;r<t.length;++r)if(e=this.#lt[e].get(t[r]),!e)return!1;return this.#ht[e]}words(t){t=t??this.#gt.empty();let e=this.#ot;for(let r=0;r<t.length;++r)if(e=this.#lt[e].get(t[r]),!e)return[];const r=[];return this.dfs(e,t,r),r}dfs(t,e,r){this.#ht[t]&&r.push(e);const s=this.#lt[t];for(const[t,n]of s){const s=this.#gt.append(e,t);this.dfs(n,s,r)}}longestPrefix(t,e){let r=this.#ot,s=0;for(let n=0;n<t.length&&(r=this.#lt[r].get(t[n]),r);++n)e&&!this.#ht[r]||(s=n+1);return this.#gt.slice(t,0,s)}clear(){this.#at=1,this.#ot=0,this.#lt=[new Map],this.#ht=[!1],this.#ct=[0],this.#ut=[]}}var cr=Object.freeze({__proto__:null,Trie:hr});var ur=Object.freeze({__proto__:null,TextDictionary:class{words;index;constructor(){this.words=new Map,this.index=hr.newStringTrie()}addWord(t){let e=this.words.get(t)||0;++e,this.words.set(t,e),this.index.add(t)}removeWord(t){let e=this.words.get(t)||0;if(e){if(1===e)return this.words.delete(t),void this.index.remove(t);--e,this.words.set(t,e)}}wordsWithPrefix(t){return this.index.words(t)}hasWord(t){return this.words.has(t)}wordCount(t){return this.words.get(t)||0}reset(){this.words.clear(),this.index.clear()}}});var gr=Object.freeze({__proto__:null,Throttler:class{#dt;#pt;#mt;#yt;#ft;#wt;#bt;#St;constructor(t){this.#dt=t,this.#pt=!1,this.#mt=!1,this.#yt=null,this.#ft=0,this.#wt=new Promise((t=>{this.#bt=t}))}#xt(){this.#ft=this.getTime(),this.#pt=!1,this.#yt&&this.innerSchedule(!1),this.processCompletedForTests()}processCompletedForTests(){}get process(){return this.#yt}get processCompleted(){return this.#yt?this.#wt:null}onTimeout(){this.#St=void 0,this.#mt=!1,this.#pt=!0,Promise.resolve().then(this.#yt).catch(console.error.bind(console)).then(this.#xt.bind(this)).then(this.#bt),this.#wt=new Promise((t=>{this.#bt=t})),this.#yt=null}schedule(t,e="Default"){this.#yt=t;const r=Boolean(this.#St)||this.#pt,s=this.getTime()-this.#ft>this.#dt,n="AsSoonAsPossible"===e||"Default"===e&&!r&&s,i=n&&!this.#mt;return this.#mt=this.#mt||n,this.innerSchedule(i),this.#wt}innerSchedule(t){if(this.#pt)return;if(this.#St&&!t)return;this.#St&&this.clearTimeout(this.#St);const e=this.#mt?0:this.#dt;this.#St=this.setTimeout(this.onTimeout.bind(this),e)}clearTimeout(t){clearTimeout(t)}setTimeout(t,e){return window.setTimeout(t,e)}getTime(){return window.performance.now()}}});class dr{#vt;#Tt;constructor(t){this.#vt=new Promise((e=>{const r=new Worker(t,{type:"module"});r.onmessage=t=>{console.assert("workerReady"===t.data),r.onmessage=null,e(r)}}))}static fromURL(t){return new dr(t)}postMessage(t){this.#vt.then((e=>{this.#Tt||e.postMessage(t)}))}dispose(){this.#Tt=!0,this.#vt.then((t=>t.terminate()))}terminate(){this.dispose()}set onmessage(t){this.#vt.then((e=>{e.onmessage=t}))}set onerror(t){this.#vt.then((e=>{e.onerror=t}))}}var pr=Object.freeze({__proto__:null,WorkerWrapper:dr});export{s as App,i as AppProvider,l as Base64,h as CharacterIdMap,Ft as Color,E as ColorConverter,nt as ColorUtils,se as Console,ne as Debouncer,ie as EventTarget,ae as JavaScriptMetaData,he as Lazy,ge as Linkifier,de as Mutex,jt as ObjectWrapper,ye as ParsedURL,we as Progress,be as QueryParamHandler,Se as ResolverBase,Le as ResourceType,Jt as Revealer,Be as Runnable,Ve as SegmentedRange,Ze as SettingRegistration,ar as Settings,or as SimpleHistoryManager,lr as StringOutputStream,ur as TextDictionary,gr as Throttler,cr as Trie,pr as Worker};
