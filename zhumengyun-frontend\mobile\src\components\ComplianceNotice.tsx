'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface ComplianceNoticeProps {
  showOnMount?: boolean
  position?: 'top' | 'bottom'
  variant?: 'banner' | 'modal' | 'inline'
}

export default function ComplianceNotice({ 
  showOnMount = false, 
  position = 'bottom',
  variant = 'banner'
}: ComplianceNoticeProps) {
  const [isVisible, setIsVisible] = useState(showOnMount)
  const [hasAcknowledged, setHasAcknowledged] = useState(false)

  const handleAcknowledge = () => {
    setHasAcknowledged(true)
    setIsVisible(false)
    localStorage.setItem('compliance-acknowledged', 'true')
  }

  const handleShow = () => {
    setIsVisible(true)
  }

  // 检查是否已经确认过
  const checkAcknowledged = () => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('compliance-acknowledged') === 'true'
    }
    return false
  }

  if (variant === 'inline') {
    return (
      <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-xl p-4 mb-4">
        <div className="flex items-start space-x-3">
          <div className="text-yellow-400 text-lg flex-shrink-0">⚠️</div>
          <div className="flex-1">
            <h4 className="text-yellow-200 font-medium mb-2">重要合规声明</h4>
            <div className="text-yellow-100 text-sm space-y-1">
              <p>• 本平台严格遵守中华人民共和国相关法律法规</p>
              <p>• 所有"积分"均为平台内虚拟积分，不具有货币属性</p>
              <p>• 不进行任何形式的虚拟货币交易或ICO活动</p>
              <p>• 所有用户必须完成实名认证</p>
              <p>• 平台承担相应的法律责任和社会责任</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (variant === 'modal') {
    return (
      <>
        <button
          onClick={handleShow}
          className="text-xs text-gray-400 hover:text-gray-300 underline"
        >
          合规声明
        </button>

        <AnimatePresence>
          {isVisible && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4"
              onClick={() => setIsVisible(false)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-yellow-500/30"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="text-center mb-6">
                  <div className="text-4xl mb-4">⚖️</div>
                  <h3 className="text-xl font-bold text-white mb-2">合规声明</h3>
                  <p className="text-gray-400 text-sm">NextGen 2025平台合规运营承诺</p>
                </div>

                <div className="space-y-4 mb-6">
                  <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                    <h4 className="text-yellow-200 font-medium mb-2">法律合规</h4>
                    <div className="text-yellow-100 text-sm space-y-1">
                      <p>✓ 严格遵守《网络安全法》</p>
                      <p>✓ 符合《数据安全法》要求</p>
                      <p>✓ 遵循《个人信息保护法》</p>
                      <p>✓ 配合《反洗钱法》执行</p>
                    </div>
                  </div>

                  <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                    <h4 className="text-blue-200 font-medium mb-2">业务合规</h4>
                    <div className="text-blue-100 text-sm space-y-1">
                      <p>✓ 不涉及虚拟货币交易</p>
                      <p>✓ 不进行非法集资</p>
                      <p>✓ 资金银行存管</p>
                      <p>✓ 依法纳税</p>
                    </div>
                  </div>

                  <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                    <h4 className="text-green-200 font-medium mb-2">用户保护</h4>
                    <div className="text-green-100 text-sm space-y-1">
                      <p>✓ 严格实名制认证</p>
                      <p>✓ 保护用户隐私</p>
                      <p>✓ 资金安全保障</p>
                      <p>✓ 投诉处理机制</p>
                    </div>
                  </div>
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={() => setIsVisible(false)}
                    className="flex-1 py-3 bg-gray-700 rounded-xl text-white font-medium"
                  >
                    关闭
                  </button>
                  <button
                    onClick={handleAcknowledge}
                    className="flex-1 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl text-white font-medium"
                  >
                    我已知晓
                  </button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </>
    )
  }

  // Banner variant
  if (!checkAcknowledged() && !hasAcknowledged) {
    return (
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, y: position === 'top' ? -100 : 100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: position === 'top' ? -100 : 100 }}
            className={`fixed ${position === 'top' ? 'top-0' : 'bottom-0'} left-0 right-0 z-40 bg-gradient-to-r from-yellow-500/90 to-orange-500/90 backdrop-blur-lg border-t border-yellow-400/30 p-4`}
          >
            <div className="max-w-4xl mx-auto">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">⚖️</div>
                  <div>
                    <h4 className="text-white font-bold text-sm">合规运营声明</h4>
                    <p className="text-yellow-100 text-xs">
                      本平台严格遵守国家法律法规，所有积分为平台内虚拟积分，不具有货币属性
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleShow}
                    className="px-3 py-1 bg-white/20 rounded-lg text-white text-xs font-medium hover:bg-white/30 transition-colors"
                  >
                    详情
                  </button>
                  <button
                    onClick={handleAcknowledge}
                    className="px-3 py-1 bg-white rounded-lg text-yellow-600 text-xs font-medium hover:bg-gray-100 transition-colors"
                  >
                    我知道了
                  </button>
                  <button
                    onClick={() => setIsVisible(false)}
                    className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors"
                  >
                    ×
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    )
  }

  return null
}

// 合规信息常量
export const COMPLIANCE_INFO = {
  platform: {
    name: 'NextGen 2025',
    operator: 'NextGen科技有限公司',
    license: 'ICP备案号：京ICP备**********号',
    address: '北京市海淀区中关村软件园',
    phone: '************',
    email: '<EMAIL>'
  },
  legal: {
    terms: '/legal/terms',
    privacy: '/legal/privacy',
    compliance: '/legal/compliance',
    dispute: '/legal/dispute'
  },
  supervision: {
    authority: '国家互联网信息办公室',
    reportEmail: '<EMAIL>',
    reportPhone: '************'
  }
}

// 合规检查工具函数
export const complianceUtils = {
  // 检查用户是否已实名认证
  checkRealNameVerification: (userId: string): boolean => {
    // 这里应该调用实际的API检查
    return localStorage.getItem(`realname-${userId}`) === 'verified'
  },

  // 检查交易限额
  checkTransactionLimit: (amount: number, userLevel: string): boolean => {
    const limits = {
      basic: 1000,
      intermediate: 10000,
      advanced: 100000,
      enterprise: Infinity
    }
    return amount <= (limits[userLevel as keyof typeof limits] || 0)
  },

  // 记录合规日志
  logComplianceEvent: (event: string, userId: string, details: any) => {
    console.log('合规日志:', { event, userId, details, timestamp: new Date().toISOString() })
    // 这里应该发送到后端日志系统
  },

  // 生成合规报告
  generateComplianceReport: (userId: string, period: string) => {
    return {
      userId,
      period,
      transactions: [],
      verificationStatus: 'verified',
      riskLevel: 'low',
      generatedAt: new Date().toISOString()
    }
  }
}
