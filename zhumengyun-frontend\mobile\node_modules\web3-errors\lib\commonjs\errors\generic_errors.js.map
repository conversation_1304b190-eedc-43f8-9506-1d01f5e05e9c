{"version": 3, "file": "generic_errors.js", "sourceRoot": "", "sources": ["../../../src/errors/generic_errors.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;;AAEF,yCAAyC;AAEzC,sDAS2B;AAC3B,8DAAsD;AAEtD,MAAa,0BAA2B,SAAQ,kCAAa;IAG5D,YAA0B,GAAW,EAAS,QAAgB,EAAS,MAAc;QACpF,KAAK,CAAC,qCAAqC,MAAM,WAAW,GAAG,eAAe,QAAQ,IAAI,CAAC,CAAC;QADnE,QAAG,GAAH,GAAG,CAAQ;QAAS,aAAQ,GAAR,QAAQ,CAAQ;QAAS,WAAM,GAAN,MAAM,CAAQ;QAF9E,SAAI,GAAG,0BAAS,CAAC;IAIxB,CAAC;IAEM,MAAM;QACZ,uCACI,KAAK,CAAC,MAAM,EAAE,KACjB,GAAG,EAAE,IAAI,CAAC,GAAG,EACb,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,MAAM,EAAE,IAAI,CAAC,MAAM,IAClB;IACH,CAAC;CACD;AAfD,gEAeC;AAED,MAAa,wBAAyB,SAAQ,kCAAa;IAG1D,YAA0B,IAAa;QACtC,KAAK,CAAC,+BAA+B,OAAO,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QADxD,SAAI,GAAJ,IAAI,CAAS;QAFhC,SAAI,GAAG,0CAAyB,CAAC;IAIxC,CAAC;IAEM,MAAM;QACZ,uCACI,KAAK,CAAC,MAAM,EAAE,KACjB,IAAI,EAAE,IAAI,CAAC,IAAI,IACd;IACH,CAAC;CACD;AAbD,4DAaC;AAED,MAAa,cAAe,SAAQ,kCAAa;IAAjD;;QACQ,SAAI,GAAG,+BAAc,CAAC;IAC9B,CAAC;CAAA;AAFD,wCAEC;AAED,MAAa,yBAA0B,SAAQ,kCAAa;IAG3D;QACC,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAHxD,SAAI,GAAG,2CAA0B,CAAC;IAIzC,CAAC;CACD;AAND,8DAMC;AAED,MAAa,qBAAsB,SAAQ,kCAAa;IAAxD;;QACQ,SAAI,GAAG,sCAAqB,CAAC;IACrC,CAAC;CAAA;AAFD,sDAEC;AAED,MAAa,mBAAoB,SAAQ,kCAAa;IAAtD;;QACQ,SAAI,GAAG,oCAAmB,CAAC;IACnC,CAAC;CAAA;AAFD,kDAEC;AAED,MAAa,QAAS,SAAQ,kCAAa;IAI1C,YAAmB,OAAe,EAAE,KAAmD;QACtF,KAAK,CAAC,OAAO,CAAC,CAAC;QAJT,SAAI,GAAG,iCAAgB,CAAC;QAK9B,IAAI,CAAC,KAAK,GAAG,KAAK,aAAL,KAAK,cAAL,KAAK,GAAI,EAAE,CAAC;IAC1B,CAAC;CACD;AARD,4BAQC;AAED,MAAa,4BAA6B,SAAQ,kCAAa;IAG9D,YAAmB,eAAuB;QACzC,KAAK,CAAC,gCAAgC,eAAe,+BAA+B,CAAC,CAAC;QAHhF,SAAI,GAAG,8CAA6B,CAAC;IAI5C,CAAC;CACD;AAND,oEAMC"}