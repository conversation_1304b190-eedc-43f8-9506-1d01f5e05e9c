﻿'use client'

import React, { useState } from 'react'
import { 中文页面布局, 中文容器, 中文卡片, 中文按钮 } from '../../components/GitHubBottomNavigation'
import {
  User,
  Settings,
  Star,
  GitFork,
  Eye,
  Calendar,
  MapPin,
  Link,
  Mail,
  Phone,
  Edit,
  Share,
  MoreHorizontal,
  Award,
  TrendingUp,
  Users,
  Code
} from 'lucide-react'

interface 用户统计 {
  repositories: number
  followers: number
  following: number
  stars: number
  contributions: number
}

interface 项目 {
  id: number
  name: string
  description: string
  language: string
  stars: number
  forks: number
  updated: string
}

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState('overview')

  const userStats: 用户统计 = {
    repositories: 12,
    followers: 156,
    following: 89,
    stars: 342,
    contributions: 1247
  }

  const topProjects: 项目[] = [
    {
      id: 1,
      name: 'nextgen-2025-platform',
      description: 'AI原生数字生活操作系统',
      language: 'TypeScript',
      stars: 128,
      forks: 32,
      updated: '2小时前'
    },
    {
      id: 2,
      name: 'engineering-discovery',
      description: '工程发现与智慧建造平台',
      language: 'JavaScript',
      stars: 89,
      forks: 21,
      updated: '5小时前'
    },
    {
      id: 3,
      name: 'creator-economy-system',
      description: 'Web3创作者经济系统',
      language: 'Solidity',
      stars: 156,
      forks: 67,
      updated: '1天前'
    }
  ]

  const getLanguageColor = (language: string) => {
    const colors: { [key: string]: string } = {
      'TypeScript': '#3178c6',
      'JavaScript': '#f1e05a',
      'Solidity': '#aa6746',
      'Python': '#3572a5',
      'React': '#61dafb'
    }
    return colors[language] || '#8c959f'
  }

  return (
    <中文页面布局>
      <中文容器 className="py-6">
        {/* 用户信息卡片 */}
        <中文卡片 className="mb-4">
          <div className="flex items-start space-x-4">
            <div className="w-20 h-20 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white text-2xl font-bold">
              用
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between mb-2">
                <h1 className="text-xl font-bold text-[#24292f]">NextGen开发者</h1>
                <button className="text-[#656d76] hover:text-[#24292f]">
                  <MoreHorizontal className="w-5 h-5" />
                </button>
              </div>
              <p className="text-sm text-[#656d76] mb-3">
                AI原生数字生活操作系统架构师 | Web3建设者 | 开源贡献者
              </p>
              <div className="flex items-center space-x-4 text-xs text-[#656d76] mb-3">
                <div className="flex items-center space-x-1">
                  <MapPin className="w-3 h-3" />
                  <span>中国·杭州</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Calendar className="w-3 h-3" />
                  <span>加入于 2024年1月</span>
                </div>
              </div>
              <div className="flex space-x-2">
                <中文按钮 variant="primary" className="flex-1">
                  <Edit className="w-4 h-4 mr-2" />
                  编辑资料
                </中文按钮>
                <中文按钮 variant="secondary">
                  <Share className="w-4 h-4" />
                </中文按钮>
              </div>
            </div>
          </div>
        </中文卡片>

        {/* 统计数据 */}
        <div className="grid grid-cols-5 gap-2 mb-4">
          <中文卡片 className="text-center p-3">
            <Code className="w-5 h-5 text-[#0969da] mx-auto mb-1" />
            <div className="text-lg font-bold text-[#24292f]">{userStats.repositories}</div>
            <div className="text-xs text-[#656d76]">仓库</div>
          </中文卡片>
          <中文卡片 className="text-center p-3">
            <Users className="w-5 h-5 text-[#2ea043] mx-auto mb-1" />
            <div className="text-lg font-bold text-[#24292f]">{userStats.followers}</div>
            <div className="text-xs text-[#656d76]">关注者</div>
          </中文卡片>
          <中文卡片 className="text-center p-3">
            <User className="w-5 h-5 text-[#cf222e] mx-auto mb-1" />
            <div className="text-lg font-bold text-[#24292f]">{userStats.following}</div>
            <div className="text-xs text-[#656d76]">关注</div>
          </中文卡片>
          <中文卡片 className="text-center p-3">
            <Star className="w-5 h-5 text-[#9a6700] mx-auto mb-1" />
            <div className="text-lg font-bold text-[#24292f]">{userStats.stars}</div>
            <div className="text-xs text-[#656d76]">星标</div>
          </中文卡片>
          <中文卡片 className="text-center p-3">
            <TrendingUp className="w-5 h-5 text-[#8250df] mx-auto mb-1" />
            <div className="text-lg font-bold text-[#24292f]">{userStats.contributions}</div>
            <div className="text-xs text-[#656d76]">贡献</div>
          </中文卡片>
        </div>

        {/* 标签页 */}
        <中文卡片 className="mb-4">
          <div className="flex space-x-1">
            <button
              onClick={() => setActiveTab('overview')}
              className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'overview'
                  ? 'bg-[#0969da] text-white'
                  : 'text-[#656d76] hover:text-[#24292f]'
              }`}
            >
              概览
            </button>
            <button
              onClick={() => setActiveTab('projects')}
              className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'projects'
                  ? 'bg-[#0969da] text-white'
                  : 'text-[#656d76] hover:text-[#24292f]'
              }`}
            >
              项目
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'settings'
                  ? 'bg-[#0969da] text-white'
                  : 'text-[#656d76] hover:text-[#24292f]'
              }`}
            >
              设置
            </button>
          </div>
        </中文卡片>

        {/* 概览内容 */}
        {activeTab === 'overview' && (
          <div className="space-y-4">
            {/* 热门项目 */}
            <中文卡片>
              <h2 className="text-lg font-semibold text-[#24292f] mb-3">热门项目</h2>
              <div className="space-y-3">
                {topProjects.slice(0, 3).map((project) => (
                  <div key={project.id} className="border-b border-[#d0d7de] last:border-b-0 pb-3 last:pb-0">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-medium text-[#0969da]">{project.name}</h3>
                      <Star className="w-4 h-4 text-[#656d76]" />
                    </div>
                    <p className="text-sm text-[#656d76] mb-2 text-ellipsis-2">{project.description}</p>
                    <div className="flex items-center space-x-3 text-xs text-[#656d76]">
                      <div className="flex items-center space-x-1">
                        <div
                          className="w-2 h-2 rounded-full"
                          style={{ backgroundColor: getLanguageColor(project.language) }}
                        />
                        <span>{project.language}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-3 h-3" />
                        <span>{project.stars}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <GitFork className="w-3 h-3" />
                        <span>{project.forks}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </中文卡片>

            {/* 成就徽章 */}
            <中文卡片>
              <h2 className="text-lg font-semibold text-[#24292f] mb-3">成就徽章</h2>
              <div className="grid grid-cols-3 gap-3">
                <div className="text-center p-3 bg-[#f6f8fa] rounded-md">
                  <Award className="w-8 h-8 text-[#0969da] mx-auto mb-2" />
                  <div className="text-xs font-medium text-[#24292f]">早期采用者</div>
                </div>
                <div className="text-center p-3 bg-[#f6f8fa] rounded-md">
                  <Star className="w-8 h-8 text-[#2ea043] mx-auto mb-2" />
                  <div className="text-xs font-medium text-[#24292f]">明星开发者</div>
                </div>
                <div className="text-center p-3 bg-[#f6f8fa] rounded-md">
                  <TrendingUp className="w-8 h-8 text-[#cf222e] mx-auto mb-2" />
                  <div className="text-xs font-medium text-[#24292f]">活跃贡献者</div>
                </div>
              </div>
            </中文卡片>
          </div>
        )}

        {/* 项目内容 */}
        {activeTab === 'projects' && (
          <中文卡片>
            <h2 className="text-lg font-semibold text-[#24292f] mb-3">我的项目</h2>
            <div className="space-y-4">
              {topProjects.map((project) => (
                <div key={project.id} className="border-b border-[#d0d7de] last:border-b-0 pb-4 last:pb-0">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-medium text-[#0969da]">{project.name}</h3>
                    <button className="text-[#656d76] hover:text-[#24292f]">
                      <MoreHorizontal className="w-4 h-4" />
                    </button>
                  </div>
                  <p className="text-sm text-[#656d76] mb-2">{project.description}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 text-xs text-[#656d76]">
                      <div className="flex items-center space-x-1">
                        <div
                          className="w-2 h-2 rounded-full"
                          style={{ backgroundColor: getLanguageColor(project.language) }}
                        />
                        <span>{project.language}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-3 h-3" />
                        <span>{project.stars}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <GitFork className="w-3 h-3" />
                        <span>{project.forks}</span>
                      </div>
                    </div>
                    <span className="text-xs text-[#656d76]">更新于 {project.updated}</span>
                  </div>
                </div>
              ))}
            </div>
          </中文卡片>
        )}

        {/* 设置内容 */}
        {activeTab === 'settings' && (
          <div className="space-y-4">
            <中文卡片>
              <h2 className="text-lg font-semibold text-[#24292f] mb-3">账户设置</h2>
              <div className="space-y-3">
                <div className="flex items-center justify-between py-2">
                  <span className="text-sm text-[#24292f]">个人信息</span>
                  <button className="text-[#0969da] text-sm">编辑</button>
                </div>
                <div className="flex items-center justify-between py-2">
                  <span className="text-sm text-[#24292f]">隐私设置</span>
                  <button className="text-[#0969da] text-sm">管理</button>
                </div>
                <div className="flex items-center justify-between py-2">
                  <span className="text-sm text-[#24292f]">通知设置</span>
                  <button className="text-[#0969da] text-sm">配置</button>
                </div>
                <div className="flex items-center justify-between py-2">
                  <span className="text-sm text-[#24292f]">安全设置</span>
                  <button className="text-[#0969da] text-sm">查看</button>
                </div>
              </div>
            </中文卡片>

            <中文卡片>
              <h2 className="text-lg font-semibold text-[#24292f] mb-3">应用设置</h2>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-[#24292f]">深色模式</span>
                  <input type="checkbox" className="rounded" />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-[#24292f]">推送通知</span>
                  <input type="checkbox" className="rounded" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-[#24292f]">自动同步</span>
                  <input type="checkbox" className="rounded" defaultChecked />
                </div>
              </div>
            </中文卡片>

            <中文按钮 variant="secondary" className="w-full">
              退出登录
            </中文按钮>
          </div>
        )}
      </中文容器>
    </中文页面布局>
  )
}
