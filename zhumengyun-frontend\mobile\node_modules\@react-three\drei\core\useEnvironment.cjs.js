"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@react-three/fiber"),n=require("three"),r=require("three-stdlib"),t=require("@monogrid/gainmap-js"),s=require("../helpers/environment-assets.cjs.js"),o=require("../helpers/deprecated.cjs.js"),i=require("react");const p="https://raw.githack.com/pmndrs/drei-assets/456060a26bbeb8fdf79326f224b6d99b8bcce736/hdri/",a=e=>Array.isArray(e),l=["/px.png","/nx.png","/py.png","/ny.png","/pz.png","/nz.png"];function c({files:r=l,path:t="",preset:c,encoding:u,extensions:d}={}){let f=null,j=!1;c&&(g(c),r=s.presetsObj[c],t=p),j=a(r);const{extension:m,isCubemap:x}=b(r);if(f=h(m),!f)throw new Error("useEnvironment: Unrecognized file extension: "+r);const w=e.useThree((e=>e.gl));i.useLayoutEffect((()=>{"webp"!==m&&"jpg"!==m&&"jpeg"!==m||w.domElement.addEventListener("webglcontextlost",(function(){e.useLoader.clear(f,j?[r]:r)}),{once:!0})}),[r,w.domElement]);const E=e.useLoader(f,j?[r]:r,(e=>{"webp"!==m&&"jpg"!==m&&"jpeg"!==m||e.setRenderer(w),null==e.setPath||e.setPath(t),d&&d(e)}));let v=j?E[0]:E;var L;"jpg"!==m&&"jpeg"!==m&&"webp"!==m||(v=null==(L=v.renderTarget)?void 0:L.texture);return v.mapping=x?n.CubeReflectionMapping:n.EquirectangularReflectionMapping,"colorSpace"in v?v.colorSpace=(null!=u?u:x)?"srgb":"srgb-linear":v.encoding=(null!=u?u:x)?o.sRGBEncoding:o.LinearEncoding,v}const u={files:l,path:"",preset:void 0,extensions:void 0};c.preload=n=>{const r={...u,...n};let{files:t,path:o=""}=r;const{preset:i,extensions:l}=r;i&&(g(i),t=s.presetsObj[i],o=p);const{extension:c}=b(t);if("webp"===c||"jpg"===c||"jpeg"===c)throw new Error("useEnvironment: Preloading gainmaps is not supported");const d=h(c);if(!d)throw new Error("useEnvironment: Unrecognized file extension: "+t);e.useLoader.preload(d,a(t)?[t]:t,(e=>{null==e.setPath||e.setPath(o),l&&l(e)}))};const d={files:l,preset:void 0};function g(e){if(!(e in s.presetsObj))throw new Error("Preset must be one of: "+Object.keys(s.presetsObj).join(", "))}function b(e){var n;const r=a(e)&&6===e.length,t=a(e)&&3===e.length&&e.some((e=>e.endsWith("json"))),s=a(e)?e[0]:e;return{extension:r?"cube":t?"webp":s.startsWith("data:application/exr")?"exr":s.startsWith("data:application/hdr")?"hdr":s.startsWith("data:image/jpeg")?"jpg":null==(n=s.split(".").pop())||null==(n=n.split("?"))||null==(n=n.shift())?void 0:n.toLowerCase(),isCubemap:r,isGainmap:t}}function h(e){return"cube"===e?n.CubeTextureLoader:"hdr"===e?r.RGBELoader:"exr"===e?r.EXRLoader:"jpg"===e||"jpeg"===e?t.HDRJPGLoader:"webp"===e?t.GainMapLoader:null}c.clear=n=>{const r={...d,...n};let{files:t}=r;const{preset:o}=r;o&&(g(o),t=s.presetsObj[o]);const{extension:i}=b(t),p=h(i);if(!p)throw new Error("useEnvironment: Unrecognized file extension: "+t);e.useLoader.clear(p,a(t)?[t]:t)},exports.useEnvironment=c;
