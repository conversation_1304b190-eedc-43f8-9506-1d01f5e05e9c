"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("three"),r=require("react");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function c(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var i=n(e),l=c(t),u=c(r);const s=u.forwardRef((({id:e=1,colorWrite:t=!1,depthWrite:r=!1,...n},c)=>{const s=u.useRef(null),a=u.useMemo((()=>({colorWrite:t,depthWrite:r,stencilWrite:!0,stencilRef:e,stencilFunc:l.AlwaysStencilFunc,stencilFail:l.ReplaceStencilOp,stencilZFail:l.ReplaceStencilOp,stencilZPass:l.ReplaceStencilOp})),[e,t,r]);return u.useLayoutEffect((()=>{Object.assign(s.current.material,a)})),u.useImperativeHandle(c,(()=>s.current),[]),u.createElement("mesh",i.default({ref:s,renderOrder:-e},n))}));exports.Mask=s,exports.useMask=function(e,t=!1){return{stencilWrite:!0,stencilRef:e,stencilFunc:t?l.NotEqualStencilFunc:l.EqualStencilFunc,stencilFail:l.KeepStencilOp,stencilZFail:l.KeepStencilOp,stencilZPass:l.KeepStencilOp}};
