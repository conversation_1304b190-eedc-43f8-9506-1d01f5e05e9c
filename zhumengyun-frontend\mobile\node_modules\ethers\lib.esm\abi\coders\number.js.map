{"version": 3, "file": "number.js", "sourceRoot": "", "sources": ["../../../src.ts/abi/coders/number.ts"], "names": [], "mappings": "AAAA,OAAO,EACH,gBAAgB,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EACtD,MAAM,sBAAsB,CAAC;AAE9B,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AACpC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAOtD,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACvB,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACvB,MAAM,cAAc,GAAG,MAAM,CAAC,oEAAoE,CAAC,CAAC;AAEpG;;GAEG;AACH,MAAM,OAAO,WAAY,SAAQ,KAAK;IACzB,IAAI,CAAU;IACd,MAAM,CAAW;IAE1B,YAAY,IAAY,EAAE,MAAe,EAAE,SAAiB;QACxD,MAAM,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAA,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;QACrD,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAEpC,gBAAgB,CAAc,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;IACjG,CAAC;IAED,YAAY;QACR,OAAO,CAAC,CAAC;IACb,CAAC;IAED,MAAM,CAAC,MAAc,EAAE,MAA4B;QAC/C,IAAI,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAE5D,qCAAqC;QACrC,IAAI,YAAY,GAAG,IAAI,CAAC,cAAc,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC;QACtD,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACrD,IAAI,KAAK,GAAG,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE;gBAC5C,IAAI,CAAC,WAAW,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;aACnD;YACD,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC;SACvC;aAAM,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE;YAClE,IAAI,CAAC,WAAW,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;SACnD;QAED,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAED,MAAM,CAAC,MAAc;QACjB,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QAEpD,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;SAC1C;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;CACJ"}