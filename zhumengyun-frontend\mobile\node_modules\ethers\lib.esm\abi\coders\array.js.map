{"version": 3, "file": "array.js", "sourceRoot": "", "sources": ["../../../src.ts/abi/coders/array.ts"], "names": [], "mappings": "AAAA,OAAO,EACH,gBAAgB,EAAE,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,mBAAmB,EACzE,MAAM,sBAAsB,CAAC;AAE9B,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAEpC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AACtE,OAAO,EAAE,cAAc,EAAE,MAAM,gBAAgB,CAAC;AAIhD;;GAEG;AACH,MAAM,UAAU,IAAI,CAAC,MAAc,EAAE,MAA4B,EAAE,MAA8C;IAC7G,IAAI,WAAW,GAAe,EAAG,CAAC;IAElC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACxB,WAAW,GAAG,MAAM,CAAC;KAEvB;SAAM,IAAI,MAAM,IAAI,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;QAC9C,IAAI,MAAM,GAAkC,EAAG,CAAC;QAEhD,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YAC/B,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;YAC7B,MAAM,CAAC,IAAI,EAAE,uDAAuD,EAChE,kBAAkB,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;YAEhF,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,yDAAyD,EAC3E,kBAAkB,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;YAEhF,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;YAEpB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;KAEN;SAAM;QACH,cAAc,CAAC,KAAK,EAAE,qBAAqB,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;KACjE;IAED,cAAc,CAAC,MAAM,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,EAAE,6BAA6B,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAErG,IAAI,YAAY,GAAG,IAAI,MAAM,EAAE,CAAC;IAChC,IAAI,aAAa,GAAG,IAAI,MAAM,EAAE,CAAC;IAEjC,IAAI,WAAW,GAAwC,EAAE,CAAC;IAC1D,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;QAC5B,IAAI,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;QAE/B,IAAI,KAAK,CAAC,OAAO,EAAE;YACf,sDAAsD;YACtD,IAAI,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC;YAEzC,kDAAkD;YAClD,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YAEnC,0DAA0D;YAC1D,IAAI,UAAU,GAAG,YAAY,CAAC,mBAAmB,EAAE,CAAC;YACpD,WAAW,CAAC,IAAI,CAAC,CAAC,UAAkB,EAAE,EAAE;gBACpC,UAAU,CAAC,UAAU,GAAG,aAAa,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;SAEN;aAAM;YACH,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;SACrC;IACL,CAAC,CAAC,CAAC;IAEH,uEAAuE;IACvE,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE9D,IAAI,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;IAC/C,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;IAC7C,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,MAAc,EAAE,MAA4B;IAC/D,IAAI,MAAM,GAAe,EAAE,CAAC;IAC5B,IAAI,IAAI,GAAyB,EAAG,CAAC;IAErC,iCAAiC;IACjC,IAAI,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAErC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;QACrB,IAAI,KAAK,GAAQ,IAAI,CAAC;QAEtB,IAAI,KAAK,CAAC,OAAO,EAAE;YACf,IAAI,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YAChC,IAAI,YAAY,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAChD,IAAI;gBACA,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;aACtC;YAAC,OAAO,KAAU,EAAE;gBACjB,2BAA2B;gBAC3B,IAAI,OAAO,CAAC,KAAK,EAAE,gBAAgB,CAAC,EAAE;oBAClC,MAAM,KAAK,CAAC;iBACf;gBAED,KAAK,GAAG,KAAK,CAAC;gBACd,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;gBAC5B,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;gBAC7B,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;aAC3B;SAEJ;aAAM;YACH,IAAI;gBACA,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;aAChC;YAAC,OAAO,KAAU,EAAE;gBACjB,2BAA2B;gBAC3B,IAAI,OAAO,CAAC,KAAK,EAAE,gBAAgB,CAAC,EAAE;oBAClC,MAAM,KAAK,CAAC;iBACf;gBAED,KAAK,GAAG,KAAK,CAAC;gBACd,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;gBAC5B,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;gBAC7B,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;aAC3B;SACJ;QAED,IAAI,KAAK,IAAI,SAAS,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;SAClC;QAED,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC1C,CAAC;AAED;;GAEG;AACH,MAAM,OAAO,UAAW,SAAQ,KAAK;IACxB,KAAK,CAAS;IACd,MAAM,CAAU;IAEzB,YAAY,KAAY,EAAE,MAAc,EAAE,SAAiB;QACvD,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;QACnE,MAAM,OAAO,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;QACjD,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACzC,gBAAgB,CAAa,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED,YAAY;QACR,+EAA+E;QAC/E,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;QAE/C,MAAM,MAAM,GAAe,EAAE,CAAC;QAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC7B;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,MAAM,CAAC,MAAc,EAAE,MAA0B;QAC7C,MAAM,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAEjD,IAAG,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACtB,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;SACnD;QAED,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAExB,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;YACrB,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;SACnC;QAED,mBAAmB,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,aAAa,GAAG,CAAC,IAAI,CAAC,SAAS,CAAA,CAAC,CAAC,CAAC,GAAG,GAAE,IAAI,CAAC,SAAS,CAAC,CAAA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEtG,IAAI,MAAM,GAAiB,EAAG,CAAC;QAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAAE;QAEnE,OAAO,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACvC,CAAC;IAED,MAAM,CAAC,MAAc;QACjB,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QACxB,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,KAAK,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YAE3B,sDAAsD;YACtD,wDAAwD;YACxD,yDAAyD;YACzD,sDAAsD;YACtD,4DAA4D;YAC5D,MAAM,CAAC,KAAK,GAAG,QAAQ,IAAI,MAAM,CAAC,UAAU,EAAE,0BAA0B,EACpE,gBAAgB,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,GAAG,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;SACxG;QACD,IAAI,MAAM,GAAiB,EAAE,CAAC;QAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAAE,MAAM,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;SAAE;QAEhF,OAAO,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAClC,CAAC;CACJ"}