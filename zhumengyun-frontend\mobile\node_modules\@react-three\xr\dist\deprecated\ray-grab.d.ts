import { Group } from 'three';
/**
 * @deprecated use `DragControls` instead
 */
export declare const RayGrab: import("react").ForwardRefExoticComponent<Omit<{
    onHover?: ((event: {
        intersection: import("three").Intersection;
        intersections: Array<import("three").Intersection>;
        target: any;
    }) => void) | undefined;
    onBlur?: ((event: {
        intersection: import("three").Intersection;
        intersections: Array<import("three").Intersection>;
        target: any;
    }) => void) | undefined;
    onSelectStart?: ((event: {
        intersection: import("three").Intersection;
        intersections: Array<import("three").Intersection>;
        target: any;
    }) => void) | undefined;
    onSelectEnd?: ((event: {
        intersection: import("three").Intersection;
        intersections: Array<import("three").Intersection>;
        target: any;
    }) => void) | undefined;
    onSelect?: ((event: {
        intersection: import("three").Intersection;
        intersections: Array<import("three").Intersection>;
        target: any;
    }) => void) | undefined;
    onSqueezeStart?: ((event: {
        intersection: import("three").Intersection;
        intersections: Array<import("three").Intersection>;
        target: any;
    }) => void) | undefined;
    onSqueezeEnd?: ((event: {
        intersection: import("three").Intersection;
        intersections: Array<import("three").Intersection>;
        target: any;
    }) => void) | undefined;
    onSqueeze?: ((event: {
        intersection: import("three").Intersection;
        intersections: Array<import("three").Intersection>;
        target: any;
    }) => void) | undefined;
    onMove?: ((event: {
        intersection: import("three").Intersection;
        intersections: Array<import("three").Intersection>;
        target: any;
    }) => void) | undefined;
    children: React.ReactNode;
} & import("react").RefAttributes<Group<import("three").Object3DEventMap>>, "ref"> & import("react").RefAttributes<Group<import("three").Object3DEventMap>>>;
