/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_RENDER_STATE } from '../private.js';
export class XRRenderState {
    constructor(init = {}, oldState) {
        this[P_RENDER_STATE] = {
            depthNear: init.depthNear || (oldState === null || oldState === void 0 ? void 0 : oldState.depthNear) || 0.1,
            depthFar: init.depthFar || (oldState === null || oldState === void 0 ? void 0 : oldState.depthFar) || 1000.0,
            inlineVerticalFieldOfView: init.inlineVerticalFieldOfView ||
                (oldState === null || oldState === void 0 ? void 0 : oldState.inlineVerticalFieldOfView) ||
                null,
            baseLayer: init.baseLayer || (oldState === null || oldState === void 0 ? void 0 : oldState.baseLayer) || null,
        };
    }
    get depthNear() {
        return this[P_RENDER_STATE].depthNear;
    }
    get depthFar() {
        return this[P_RENDER_STATE].depthFar;
    }
    get inlineVerticalFieldOfView() {
        return this[P_RENDER_STATE].inlineVerticalFieldOfView;
    }
    get baseLayer() {
        return this[P_RENDER_STATE].baseLayer;
    }
}
//# sourceMappingURL=XRRenderState.js.map