/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
export var XRHandJoint;
(function (XRHandJoint) {
    XRHandJoint["Wrist"] = "wrist";
    XRHandJoint["ThumbMetacarpal"] = "thumb-metacarpal";
    XRHandJoint["ThumbPhalanxProximal"] = "thumb-phalanx-proximal";
    XRHandJoint["ThumbPhalanxDistal"] = "thumb-phalanx-distal";
    XRHandJoint["ThumbTip"] = "thumb-tip";
    XRHandJoint["IndexFingerMetacarpal"] = "index-finger-metacarpal";
    XRHandJoint["IndexFingerPhalanxProximal"] = "index-finger-phalanx-proximal";
    XRHandJoint["IndexFingerPhalanxIntermediate"] = "index-finger-phalanx-intermediate";
    XRHandJoint["IndexFingerPhalanxDistal"] = "index-finger-phalanx-distal";
    XRHandJoint["IndexFingerTip"] = "index-finger-tip";
    XRHandJoint["MiddleFingerMetacarpal"] = "middle-finger-metacarpal";
    XRHandJoint["MiddleFingerPhalanxProximal"] = "middle-finger-phalanx-proximal";
    XRHandJoint["MiddleFingerPhalanxIntermediate"] = "middle-finger-phalanx-intermediate";
    XRHandJoint["MiddleFingerPhalanxDistal"] = "middle-finger-phalanx-distal";
    XRHandJoint["MiddleFingerTip"] = "middle-finger-tip";
    XRHandJoint["RingFingerMetacarpal"] = "ring-finger-metacarpal";
    XRHandJoint["RingFingerPhalanxProximal"] = "ring-finger-phalanx-proximal";
    XRHandJoint["RingFingerPhalanxIntermediate"] = "ring-finger-phalanx-intermediate";
    XRHandJoint["RingFingerPhalanxDistal"] = "ring-finger-phalanx-distal";
    XRHandJoint["RingFingerTip"] = "ring-finger-tip";
    XRHandJoint["PinkyFingerMetacarpal"] = "pinky-finger-metacarpal";
    XRHandJoint["PinkyFingerPhalanxProximal"] = "pinky-finger-phalanx-proximal";
    XRHandJoint["PinkyFingerPhalanxIntermediate"] = "pinky-finger-phalanx-intermediate";
    XRHandJoint["PinkyFingerPhalanxDistal"] = "pinky-finger-phalanx-distal";
    XRHandJoint["PinkyFingerTip"] = "pinky-finger-tip";
})(XRHandJoint || (XRHandJoint = {}));
export class XRHand extends Map {
}
//# sourceMappingURL=XRHand.js.map