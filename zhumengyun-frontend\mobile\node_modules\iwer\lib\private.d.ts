/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
export declare const P_ACTION_PLAYER: unique symbol;
export declare const P_ACTION_RECORDER: unique symbol;
export declare const P_ANCHOR: unique symbol;
export declare const P_CONTROLLER: unique symbol;
export declare const P_DEVICE: unique symbol;
export declare const P_HAND_INPUT: unique symbol;
export declare const P_TRACKED_INPUT: unique symbol;
export declare const P_FRAME: unique symbol;
export declare const P_GAMEPAD: unique symbol;
export declare const P_SYSTEM: unique symbol;
export declare const P_INPUT_SOURCE: unique symbol;
export declare const P_WEBGL_LAYER: unique symbol;
export declare const P_MESH: unique symbol;
export declare const P_PLANE: unique symbol;
export declare const P_JOINT_POSE: unique symbol;
export declare const P_POSE: unique symbol;
export declare const P_VIEWER_POSE: unique symbol;
export declare const P_RIGID_TRANSFORM: unique symbol;
export declare const P_RENDER_STATE: unique symbol;
export declare const P_SESSION: unique symbol;
export declare const P_JOINT_SPACE: unique symbol;
export declare const P_REF_SPACE: unique symbol;
export declare const P_SPACE: unique symbol;
export declare const P_VIEW: unique symbol;
export declare const P_VIEWPORT: unique symbol;
export declare const P_RAY: unique symbol;
export declare const P_HIT_TEST: unique symbol;
//# sourceMappingURL=private.d.ts.map