import { Group, OrthographicCamera, PerspectiveCamera } from 'three';
import { PivotAxisScaleHandle } from './scale.js';
import { HandleOptions } from '../../store.js';
import { HandlesContext } from '../context.js';
import { HandlesProperties } from '../index.js';
import { PivotAxisRotationHandle } from './rotate.js';
import { AxisTranslateHandle } from '../translate/axis.js';
import { PlaneTranslateHandle } from '../translate/plane.js';
export declare class PivotHandlesHandles extends Group {
    private readonly context;
    size?: number | undefined;
    fixed?: boolean | undefined;
    readonly scaleX: PivotAxisScaleHandle;
    readonly scaleY: PivotAxisScaleHandle;
    readonly scaleZ: PivotAxisScaleHandle;
    readonly rotationX: PivotAxisRotationHandle;
    readonly rotationY: PivotAxisRotationHandle;
    readonly rotationZ: PivotAxisRotationHandle;
    readonly translationX: AxisTranslateHandle;
    readonly translationY: AxisTranslateHandle;
    readonly translationZ: AxisTranslateHandle;
    readonly translationXY: PlaneTranslateHandle;
    readonly translationYZ: PlaneTranslateHandle;
    readonly translationXZ: PlaneTranslateHandle;
    private xAxis;
    private yAxis;
    private zAxis;
    private xRotationAxis;
    private yRotationAxis;
    private zRotationAxis;
    constructor(context: HandlesContext, size?: number | undefined, fixed?: boolean | undefined);
    update(camera: PerspectiveCamera | OrthographicCamera): void;
    bind(translation?: HandlesProperties, rotation?: HandlesProperties, scale?: HandlesProperties): () => void;
}
export declare class PivotHandles extends Group {
    readonly handles: PivotHandlesHandles;
    private readonly context;
    constructor(getOptions?: () => HandleOptions<unknown>);
    update(time: number, camera: PerspectiveCamera | OrthographicCamera): void;
    bind(translation?: HandlesProperties, rotation?: HandlesProperties, scale?: HandlesProperties): () => void;
}
export * from './rotate.js';
export * from './scale.js';
