import { OrthographicCamera, PerspectiveCamera, Vector2, Vector3 } from 'three';
import type { ScreenCameraStateAndFunctions } from './camera.js';
import type { ScreenHandleStore } from './store.js';
export declare function average(target: Vector2, map: ScreenHandleStore['map'], key: 'currentScreenPosition' | 'initialScreenPosition'): void;
export declare function convertScreenSpaceMovementToGlobalPan(state: ScreenCameraStateAndFunctions, camera: PerspectiveCamera | OrthographicCamera, screenSpaceMovement: Vector2, target: Vector3, speed: number, space: 'screen' | 'xz'): void;
