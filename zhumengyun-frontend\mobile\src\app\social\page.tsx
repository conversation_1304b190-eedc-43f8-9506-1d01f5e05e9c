'use client'

import React, { useState } from 'react'
import { 中文页面布局, 中文容器, 中文卡片, 中文按钮 } from '../../components/GitHubBottomNavigation'
import { 
  MessageCircle, 
  Users, 
  Bell, 
  Search, 
  Heart,
  Star,
  User,
  Clock,
  CheckCircle,
  MoreHorizontal,
  Phone,
  Video,
  Send
} from 'lucide-react'

interface 消息 {
  id: number
  sender: {
    name: string
    avatar: string
    online: boolean
  }
  content: string
  time: string
  read: boolean
  type: 'text' | 'image' | 'file'
}

interface 通知 {
  id: number
  type: 'like' | 'comment' | 'follow' | 'mention'
  title: string
  description: string
  user: {
    name: string
    avatar: string
  }
  time: string
  read: boolean
}

export default function SocialPage() {
  const [activeTab, setActiveTab] = useState('messages')
  const [searchQuery, setSearchQuery] = useState('')

  const messages: 消息[] = [
    {
      id: 1,
      sender: {
        name: '张开发者',
        avatar: '👨‍💻',
        online: true
      },
      content: '你好！关于NextGen 2025项目的技术架构，我有一些想法想和你讨论。',
      time: '2分钟前',
      read: false,
      type: 'text'
    },
    {
      id: 2,
      sender: {
        name: '李工程师',
        avatar: '👷‍♂️',
        online: false
      },
      content: '工程发现模块的API文档已经更新，请查看最新版本。',
      time: '1小时前',
      read: false,
      type: 'text'
    },
    {
      id: 3,
      sender: {
        name: '王设计师',
        avatar: '🎨',
        online: true
      },
      content: '新的UI设计稿已经完成，需要你的反馈。',
      time: '3小时前',
      read: true,
      type: 'text'
    },
    {
      id: 4,
      sender: {
        name: '陈产品经理',
        avatar: '📊',
        online: false
      },
      content: '明天的产品评审会议改到下午3点，请准时参加。',
      time: '1天前',
      read: true,
      type: 'text'
    }
  ]

  const notifications: 通知[] = [
    {
      id: 1,
      type: 'like',
      title: '新的点赞',
      description: '张开发者 点赞了你的项目',
      user: {
        name: '张开发者',
        avatar: '👨‍💻'
      },
      time: '10分钟前',
      read: false
    },
    {
      id: 2,
      type: 'comment',
      title: '新的评论',
      description: '李工程师 评论了你的代码',
      user: {
        name: '李工程师',
        avatar: '👷‍♂️'
      },
      time: '30分钟前',
      read: false
    },
    {
      id: 3,
      type: 'follow',
      title: '新的关注者',
      description: '王设计师 开始关注你',
      user: {
        name: '王设计师',
        avatar: '🎨'
      },
      time: '2小时前',
      read: true
    },
    {
      id: 4,
      type: 'mention',
      title: '提到了你',
      description: '陈产品经理 在讨论中提到了你',
      user: {
        name: '陈产品经理',
        avatar: '📊'
      },
      time: '1天前',
      read: true
    }
  ]

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'like': return <Heart className="w-4 h-4 text-red-500" />
      case 'comment': return <MessageCircle className="w-4 h-4 text-blue-500" />
      case 'follow': return <User className="w-4 h-4 text-green-500" />
      case 'mention': return <Bell className="w-4 h-4 text-orange-500" />
      default: return <Bell className="w-4 h-4 text-gray-500" />
    }
  }

  return (
    <中文页面布局 title="消息">
      <中文容器 className="py-4 pb-20">
        {/* 标签页切换 */}
        <中文卡片 className="mb-4">
          <div className="flex space-x-1">
            <button
              onClick={() => setActiveTab('messages')}
              className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'messages'
                  ? 'bg-[#0969da] text-white'
                  : 'text-[#656d76] hover:text-[#24292f]'
              }`}
            >
              <MessageCircle className="w-4 h-4 inline mr-2" />
              消息
            </button>
            <button
              onClick={() => setActiveTab('notifications')}
              className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'notifications'
                  ? 'bg-[#0969da] text-white'
                  : 'text-[#656d76] hover:text-[#24292f]'
              }`}
            >
              <Bell className="w-4 h-4 inline mr-2" />
              通知
            </button>
          </div>
        </中文卡片>

        {/* 搜索栏 */}
        <中文卡片 className="mb-4">
          <div className="中文-搜索">
            <Search className="中文-搜索-图标 w-4 h-4" />
            <input
              type="text"
              placeholder={activeTab === 'messages' ? '搜索消息...' : '搜索通知...'}
              className="中文-搜索-输入框 w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </中文卡片>

        {/* 消息列表 */}
        {activeTab === 'messages' && (
          <中文卡片>
            <div className="space-y-4">
              {messages.map((message) => (
                <div key={message.id} className="flex items-start space-x-3 p-3 rounded-md hover:bg-[#f6f8fa] transition-colors">
                  <div className="relative">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white text-lg">
                      {message.sender.avatar}
                    </div>
                    {message.sender.online && (
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="text-sm font-medium text-[#24292f] truncate">
                        {message.sender.name}
                      </h3>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-[#656d76]">{message.time}</span>
                        {!message.read && (
                          <div className="w-2 h-2 bg-[#0969da] rounded-full"></div>
                        )}
                      </div>
                    </div>
                    <p className="text-sm text-[#656d76] text-ellipsis-2">{message.content}</p>
                  </div>
                  <button className="text-[#656d76] hover:text-[#24292f]">
                    <MoreHorizontal className="w-4 h-4" />
                  </button>
                </div>
              ))}
            </div>
          </中文卡片>
        )}

        {/* 通知列表 */}
        {activeTab === 'notifications' && (
          <中文卡片>
            <div className="space-y-4">
              {notifications.map((notification) => (
                <div key={notification.id} className="flex items-start space-x-3 p-3 rounded-md hover:bg-[#f6f8fa] transition-colors">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white text-lg">
                    {notification.user.avatar}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center space-x-2">
                        {getNotificationIcon(notification.type)}
                        <h3 className="text-sm font-medium text-[#24292f]">
                          {notification.title}
                        </h3>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-[#656d76]">{notification.time}</span>
                        {!notification.read && (
                          <div className="w-2 h-2 bg-[#0969da] rounded-full"></div>
                        )}
                      </div>
                    </div>
                    <p className="text-sm text-[#656d76]">{notification.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </中文卡片>
        )}

        {/* 快速操作 */}
        <div className="fixed bottom-20 right-4">
          <中文按钮 variant="primary" className="rounded-full w-12 h-12 p-0">
            <Send className="w-5 h-5" />
          </中文按钮>
        </div>
      </中文容器>
    </中文页面布局>
  )
}
