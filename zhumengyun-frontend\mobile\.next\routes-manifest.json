{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [{"source": "/api/:path*", "destination": "http://localhost:8080/api/:path*", "regex": "^\\/api(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$", "check": true}], "fallback": []}, "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true, "internal": true, "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))\\/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}, {"key": "X-UA-Compatible", "value": "IE=edge"}, {"key": "viewport", "value": "width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"}], "regex": "^(?:\\/(.*))(?:\\/)?$"}]}