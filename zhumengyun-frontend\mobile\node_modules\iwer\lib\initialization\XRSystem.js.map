{"version": 3, "file": "XRSystem.js", "sourceRoot": "", "sources": ["../../src/initialization/XRSystem.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAGH,OAAO,EACN,SAAS,GAGT,MAAM,yBAAyB,CAAC;AAEjC,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AASzC,MAAM,OAAO,QAAS,SAAQ,WAAW;IAQxC,YAAY,MAAgB;QAC3B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,QAAQ,CAAC,GAAG;YAChB,MAAM;YACN,YAAY,EAAE,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;gBACpD,uDAAuD;gBACvD,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE;oBACjC,MAAM,CACL,IAAI,YAAY,CACf,qCAAqC,EACrC,mBAAmB,CACnB,CACD,CAAC;oBACF,OAAO;iBACP;gBAED,wCAAwC;gBACxC,MAAM,EAAE,gBAAgB,GAAG,EAAE,EAAE,gBAAgB,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;gBACjE,MAAM,EAAE,iBAAiB,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;gBAEpD,+CAA+C;gBAC/C,MAAM,oBAAoB,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,EAAE,CAC/D,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,CACnC,CAAC;gBACF,IAAI,CAAC,oBAAoB,EAAE;oBAC1B,MAAM,CACL,IAAI,KAAK,CACR,gEAAgE,CAChE,CACD,CAAC;oBACF,OAAO;iBACP;gBAED,2CAA2C;gBAC3C,MAAM,yBAAyB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,CACrE,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,CACnC,CAAC;gBAEF,yEAAyE;gBACzE,MAAM,eAAe,GAAmB,KAAK,CAAC,IAAI,CACjD,IAAI,GAAG,CAAC;oBACP,GAAG,gBAAgB;oBACnB,GAAG,yBAAyB;oBAC5B,QAAQ;oBACR,OAAO;iBACP,CAAC,CACF,CAAC;gBAEF,gCAAgC;gBAChC,MAAM,OAAO,GAAG,IAAI,SAAS,CAC5B,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,EACrB,IAAI,EACJ,eAAe,CACf,CAAC;gBACF,IAAI,CAAC,QAAQ,CAAC,CAAC,aAAa,GAAG,OAAO,CAAC;gBAEvC,qDAAqD;gBACrD,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE;oBACpC,IAAI,CAAC,QAAQ,CAAC,CAAC,aAAa,GAAG,SAAS,CAAC;gBAC1C,CAAC,CAAC,CAAC;gBAEH,OAAO,CAAC,OAAO,CAAC,CAAC;YAClB,CAAC;SACD,CAAC;QACF,yDAAyD;IAC1D,CAAC;IAED,kBAAkB,CAAC,IAAmB;QACrC,OAAO,IAAI,OAAO,CAAU,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;YAChD,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACtB,OAAO,CAAC,IAAI,CAAC,CAAC;aACd;iBAAM;gBACN,qDAAqD;gBACrD,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;aACpE;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,cAAc,CACb,IAAmB,EACnB,UAAyB,EAAE;QAE3B,OAAO,IAAI,OAAO,CAAY,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACjD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;iBAC3B,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;gBACrB,IAAI,CAAC,WAAW,EAAE;oBACjB,MAAM,CACL,IAAI,YAAY,CACf,gDAAgD,EAChD,mBAAmB,CACnB,CACD,CAAC;oBACF,OAAO;iBACP;gBAED,MAAM,kBAAkB,GAAG;oBAC1B,OAAO;oBACP,MAAM;oBACN,IAAI;oBACJ,OAAO;iBACP,CAAC;gBAEF,IAAI,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;YACjD,CAAC,CAAC;iBACD,KAAK,CAAC,MAAM,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,YAAY,CACX,IAAmB,EACnB,UAAyB,EAAE;QAE3B,OAAO,IAAI,OAAO,CAAY,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACjD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;iBAC3B,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;gBACrB,IAAI,CAAC,WAAW,EAAE;oBACjB,MAAM,CACL,IAAI,YAAY,CACf,gDAAgD,EAChD,mBAAmB,CACnB,CACD,CAAC;oBACF,OAAO;iBACP;gBAED,IAAI,CAAC,QAAQ,CAAC,CAAC,oBAAoB,GAAG;oBACrC,OAAO;oBACP,MAAM;oBACN,IAAI;oBACJ,OAAO;iBACP,CAAC;YACH,CAAC,CAAC;iBACD,KAAK,CAAC,MAAM,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACJ,CAAC;CACD"}