{"version": 3, "file": "format_transaction.js", "sourceRoot": "", "sources": ["../../../src/utils/format_transaction.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AAEF,OAAO,EAA2B,qBAAqB,EAAc,MAAM,YAAY,CAAC;AACxF,OAAO,EAAE,SAAS,EAAyB,MAAM,gBAAgB,CAAC;AAClE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AAClE,OAAO,EAAE,4BAA4B,EAAE,MAAM,aAAa,CAAC;AAE3D,OAAO,EAAE,qBAAqB,EAAE,MAAM,eAAe,CAAC;AAGtD,MAAM,UAAU,iBAAiB,CAIhC,WAA4B,EAC5B,eAA6B,qBAAqC,EAClE,UAGI;IACH,iBAAiB,EAAE,qBAAqB;IACxC,gBAAgB,EAAE,KAAK;CACvB;;IAED,IAAI,oBAAoB,GAAG,SAAS,CAAC,EAAE,EAAE,WAAsC,CAAgB,CAAC;IAChG,IAAI,CAAC,SAAS,CAAC,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,MAAM,CAAC,EAAE,CAAC;QACrC,oBAAoB,CAAC,MAAM,qBAAQ,WAAW,CAAC,MAAM,CAAE,CAAC;QACxD,IAAI,CAAC,SAAS,CAAC,MAAA,WAAW,CAAC,MAAM,0CAAE,WAAW,CAAC;YAC9C,oBAAoB,CAAC,MAAM,CAAC,WAAW,qBAAQ,WAAW,CAAC,MAAM,CAAC,WAAW,CAAE,CAAC;IAClF,CAAC;IACD,oBAAoB,GAAG,MAAM,CAC5B,MAAA,OAAO,CAAC,iBAAiB,mCAAI,qBAAqB,EAClD,oBAAoB,EACpB,YAAY,CACZ,CAAC;IACF,IACC,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC;QACrC,CAAC,SAAS,CAAC,oBAAoB,CAAC,KAAK,CAAC;QACtC,sEAAsE;QACtE,4EAA4E;QAC5E,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAEtE,MAAM,IAAI,4BAA4B,CAAC;YACtC,IAAI,EAAE,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAC3C,KAAK,EAAE,UAAU,CAAC,oBAAoB,CAAC,KAAK,CAAC;SAC7C,CAAC,CAAC;IAEJ,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3C,oBAAoB,CAAC,KAAK,GAAG,oBAAoB,CAAC,IAAI,CAAC;QACxD,CAAC;aAAM,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE,CAAC;YACnD,oBAAoB,CAAC,IAAI,GAAG,oBAAoB,CAAC,KAAK,CAAC;QACxD,CAAC;IACF,CAAC;IAED,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC/C,oBAAoB,CAAC,GAAG,GAAG,oBAAoB,CAAC,QAAQ,CAAC;QACzD,OAAO,oBAAoB,CAAC,QAAQ,CAAC;IACtC,CAAC;IAED,OAAO,oBAAiE,CAAC;AAC1E,CAAC"}