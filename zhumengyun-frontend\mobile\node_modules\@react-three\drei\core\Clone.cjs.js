"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("three"),r=require("react"),n=require("three-stdlib");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var o=a(e),c=i(t),l=i(r);const s=l.forwardRef((({isChild:e=!1,object:t,children:r,deep:a,castShadow:i,receiveShadow:d,inject:u,keys:f,...y},m)=>{const h={keys:f,deep:a,inject:u,castShadow:i,receiveShadow:d};if(t=l.useMemo((()=>{if(!1===e&&!Array.isArray(t)){let e=!1;if(t.traverse((t=>{t.isSkinnedMesh&&(e=!0)})),e)return n.SkeletonUtils.clone(t)}return t}),[t,e]),Array.isArray(t))return l.createElement("group",o.default({},y,{ref:m}),t.map((e=>l.createElement(s,o.default({key:e.uuid,object:e},h)))),r);const{children:p,...b}=function(e,{keys:t=["near","far","color","distance","decay","penumbra","angle","intensity","skeleton","visible","castShadow","receiveShadow","morphTargetDictionary","morphTargetInfluences","name","geometry","material","position","rotation","scale","up","userData","bindMode","bindMatrix","bindMatrixInverse","skeleton"],deep:r,inject:n,castShadow:a,receiveShadow:i}){let o={};for(const r of t)o[r]=e[r];return r&&(o.geometry&&"materialsOnly"!==r&&(o.geometry=o.geometry.clone()),o.material&&"geometriesOnly"!==r&&(o.material=o.material.clone())),n&&(o="function"==typeof n?{...o,children:n(e)}:l.isValidElement(n)?{...o,children:n}:{...o,...n}),e instanceof c.Mesh&&(a&&(o.castShadow=!0),i&&(o.receiveShadow=!0)),o}(t,h),j=t.type[0].toLowerCase()+t.type.slice(1);return l.createElement(j,o.default({},b,y,{ref:m}),t.children.map((e=>"Bone"===e.type?l.createElement("primitive",o.default({key:e.uuid,object:e},h)):l.createElement(s,o.default({key:e.uuid,object:e},h,{isChild:!0})))),r,p)}));exports.Clone=s;
