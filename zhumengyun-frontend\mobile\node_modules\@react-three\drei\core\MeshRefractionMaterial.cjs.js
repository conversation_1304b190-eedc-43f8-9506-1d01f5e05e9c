"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("@react-three/fiber"),n=require("three-mesh-bvh"),a=require("../materials/MeshRefractionMaterial.cjs.js");function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}require("three"),require("./shaderMaterial.cjs.js"),require("../helpers/constants.cjs.js");var o=i(e),s=u(r);exports.MeshRefractionMaterial=function({aberrationStrength:e=0,fastChroma:i=!0,envMap:u,...c}){t.extend({MeshRefractionMaterial:a.MeshRefractionMaterial});const l=r.useRef(),{size:f}=t.useThree(),h=r.useMemo((()=>{var r,t;const n={},a=(o=u)&&o.isCubeTexture;var o;const s=(null!==(r=a?null==(t=u.image[0])?void 0:t.width:u.image.width)&&void 0!==r?r:1024)/4,c=Math.floor(Math.log2(s)),l=Math.pow(2,c),f=3*Math.max(l,112),h=4*l;return a&&(n.ENVMAP_TYPE_CUBEM=""),n.CUBEUV_TEXEL_WIDTH=""+1/f,n.CUBEUV_TEXEL_HEIGHT=""+1/h,n.CUBEUV_MAX_MIP=`${c}.0`,e>0&&(n.CHROMATIC_ABERRATIONS=""),i&&(n.FAST_CHROMA=""),n}),[e,i]);return r.useLayoutEffect((()=>{var e;const r=null==(e=l.current)||null==(e=e.__r3f)||null==(e=e.parent)?void 0:e.geometry;r&&(l.current.bvh=new n.MeshBVHUniformStruct,l.current.bvh.updateFrom(new n.MeshBVH(r.clone().toNonIndexed(),{strategy:n.SAH})))}),[]),t.useFrame((({camera:e})=>{l.current.viewMatrixInverse=e.matrixWorld,l.current.projectionMatrixInverse=e.projectionMatrixInverse})),s.createElement("meshRefractionMaterial",o.default({key:JSON.stringify(h),defines:h,ref:l,resolution:[f.width,f.height],aberrationStrength:e,envMap:u},c))};
