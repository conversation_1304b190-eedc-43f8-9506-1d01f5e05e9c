/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<883b15015fe6ee9b3147215d7507e3cd>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Lists/VirtualizedSectionList.js
 */

import VirtualizedLists from "@react-native/virtualized-lists";
type VirtualizedSectionListType = typeof VirtualizedLists.VirtualizedSectionList;
declare const VirtualizedSectionList: VirtualizedSectionListType;
export type { SectionBase, ScrollToLocationParamsType, VirtualizedSectionListProps } from "@react-native/virtualized-lists";
declare const $$VirtualizedSectionList: typeof VirtualizedSectionList;
declare type $$VirtualizedSectionList = typeof $$VirtualizedSectionList;
export default $$VirtualizedSectionList;
