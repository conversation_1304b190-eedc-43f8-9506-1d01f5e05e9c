{"version": 3, "file": "wallet.js", "sourceRoot": "", "sources": ["../../src/wallet.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;;;;;;;;;;AAEF,OAAO,EAAE,cAAc,EAAmC,MAAM,YAAY,CAAC;AAC7E,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAK3C;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,OAAO,MAEX,SAAQ,cAAiB;IAF3B;;QAGkB,gBAAW,GAAG,IAAI,GAAG,EAAkB,CAAC;QACxC,oBAAe,GAAG,eAAe,CAAC;IAsYpD,CAAC;IApYA;;;;OAIG;IACI,MAAM,CAAC,UAAU;QACvB,IAAI,OAA+B,CAAC;QAEpC,IAAI,CAAC;YACJ,OAAO,GAAG,MAAM,CAAC,YAAY,CAAC;YAC9B,MAAM,CAAC,GAAG,kBAAkB,CAAC;YAC7B,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtB,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAEtB,OAAO,OAAO,CAAC;QAChB,CAAC;QAAC,OAAO,CAAU,EAAE,CAAC;YACrB,OAAQ,CAAkB;gBACzB,4BAA4B;gBAC5B,CAAE,CAAkB,CAAC,IAAI,KAAK,EAAE;oBAC/B,UAAU;oBACT,CAAkB,CAAC,IAAI,KAAK,IAAI;oBACjC,yDAAyD;oBACzD,4BAA4B;oBAC3B,CAAkB,CAAC,IAAI,KAAK,oBAAoB;oBACjD,UAAU;oBACT,CAAkB,CAAC,IAAI,KAAK,4BAA4B,CAAC;gBAC3D,0EAA0E;gBAC1E,CAAC,SAAS,CAAC,OAAO,CAAC;gBACnB,OAAO,CAAC,MAAM,KAAK,CAAC;gBACpB,CAAC,CAAC,OAAO;gBACT,CAAC,CAAC,SAAS,CAAC;QACd,CAAC;IACF,CAAC;IACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmCG;IAEI,MAAM,CAAC,gBAAwB;QACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9C,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACI,GAAG,CAAC,OAAmB;;QAC7B,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC;QACrE,CAAC;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QACxB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,YAAY,EAAE,CAAC;YAClB,OAAO,CAAC,IAAI,CAAC,WAAW,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;YACzE,KAAK,GAAG,MAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,mCAAI,KAAK,CAAC;QACtE,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC;QAC3D,IAAI,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;QAEtB,OAAO,IAAI,CAAC;IACb,CAAC;IACD;;;;;OAKG;IAEI,GAAG,CAAC,cAA+B;QACzC,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE,CAAC;YACxC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC;YAEjE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;YAED,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC;IAC7B,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACI,MAAM,CAAC,cAA+B;QAC5C,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE,CAAC;YACxC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC;YACjE,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gBACtB,OAAO,KAAK,CAAC;YACd,CAAC;YACD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC;YACtD,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAEtB,OAAO,IAAI,CAAC;QACb,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;YAC/B,OAAO,IAAI,CAAC;QACb,CAAC;QAED,OAAO,KAAK,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACI,KAAK;QACX,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAEzB,yCAAyC;QACzC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAEhB,OAAO,IAAI,CAAC;IACb,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACU,OAAO,CACnB,QAAgB,EAChB,OAA6C;;YAE7C,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAO,OAAU,EAAE,EAAE,gDAAC,OAAA,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA,GAAA,CAAC,CAAC,CAAC;QACxF,CAAC;KAAA;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAyEG;IACU,OAAO,CACnB,gBAA4B,EAC5B,QAAgB,EAChB,OAA6C;;YAE7C,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAChC,gBAAgB,CAAC,GAAG,CAAC,CAAO,MAAgB,EAAE,EAAE,gDAC/C,OAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA,GAAA,CACxD,CACD,CAAC;YACF,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;gBAC3B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACf,CAAC;YACD,OAAO,IAAI,CAAC;QACb,CAAC;KAAA;IAED;;;;;;;;;;;OAWG;IACU,IAAI,CAAC,QAAgB,EAAE,OAAgB;;YACnD,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;YAEpC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YACjD,CAAC;YAED,OAAO,CAAC,OAAO,CACd,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,IAAI,CAAC,eAAe,EAC/B,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAC5C,CAAC;YAEF,OAAO,IAAI,CAAC;QACb,CAAC;KAAA;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACU,IAAI,CAAC,QAAgB,EAAE,OAAgB;;YACnD,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;YAEpC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YACjD,CAAC;YAED,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,IAAI,CAAC,eAAe,CAAC,CAAC;YAElE,IAAI,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,CAAC,OAAO,CAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAgB,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;YAC1E,CAAC;YAED,OAAO,IAAI,CAAC;QACb,CAAC;KAAA;CACD"}