import { Object3D } from 'three';
import { GLTF, GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
export type XRHandLoaderOptions = {
    /**
     * where to load the hand models from
     * @default 'https://cdn.jsdelivr.net/npm/@webxr-input-profiles/assets@1.0/dist/profiles/'
     */
    baseAssetPath?: string;
    /**
     * profile id that is used if no matching profile id is found
     * @default 'generic-hand'
     */
    defaultXRHandProfileId?: string;
};
export declare function getXRHandAssetPath(handedness: XRHandedness, options: XRHandLoaderOptions | undefined): string;
export declare function loadXRHandModel(assetPath: string, loader?: GLTFLoader): Promise<Object3D<import("three").Object3DEventMap>>;
export declare function cloneXRHandGltf({ scene }: GLTF): Object3D<import("three").Object3DEventMap>;
export type XRHandModelOptions = {
    colorWrite?: boolean;
    renderOrder?: number;
};
export declare function configureXRHandModel(model: Object3D, options?: XRHandModelOptions): void;
