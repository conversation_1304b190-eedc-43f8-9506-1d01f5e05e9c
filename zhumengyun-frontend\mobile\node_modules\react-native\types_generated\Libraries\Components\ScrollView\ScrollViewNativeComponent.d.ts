/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<77dc33a0a7d5472f1911008d8afcef95>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Components/ScrollView/ScrollViewNativeComponent.js
 */

import type { HostComponent } from "../../../src/private/types/HostComponent";
import type { PartialViewConfig } from "../../Renderer/shims/ReactNativeTypes";
import type { ScrollViewNativeProps as Props } from "./ScrollViewNativeComponentType";
export declare const __INTERNAL_VIEW_CONFIG: PartialViewConfig;
export declare type __INTERNAL_VIEW_CONFIG = typeof __INTERNAL_VIEW_CONFIG;
declare const ScrollViewNativeComponent: HostComponent<Props>;
declare const $$ScrollViewNativeComponent: typeof ScrollViewNativeComponent;
declare type $$ScrollViewNativeComponent = typeof $$ScrollViewNativeComponent;
export default $$ScrollViewNativeComponent;
