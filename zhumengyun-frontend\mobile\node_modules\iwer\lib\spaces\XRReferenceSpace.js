/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var _a;
import { P_REF_SPACE } from '../private.js';
import { XRSpace } from './XRSpace.js';
export var XRReferenceSpaceType;
(function (XRReferenceSpaceType) {
    XRReferenceSpaceType["Viewer"] = "viewer";
    XRReferenceSpaceType["Local"] = "local";
    XRReferenceSpaceType["LocalFloor"] = "local-floor";
    XRReferenceSpaceType["BoundedFloor"] = "bounded-floor";
    XRReferenceSpaceType["Unbounded"] = "unbounded";
})(XRReferenceSpaceType || (XRReferenceSpaceType = {}));
export class XRReferenceSpace extends XRSpace {
    constructor(type, parentSpace, offsetMatrix) {
        super(parentSpace, offsetMatrix);
        this[_a] = {
            type: null,
            onreset: () => { },
        };
        this[P_REF_SPACE].type = type;
    }
    get onreset() {
        var _b;
        return (_b = this[P_REF_SPACE].onreset) !== null && _b !== void 0 ? _b : (() => { });
    }
    set onreset(callback) {
        if (this[P_REF_SPACE].onreset) {
            this.removeEventListener('reset', this[P_REF_SPACE].onreset);
        }
        this[P_REF_SPACE].onreset = callback;
        if (callback) {
            this.addEventListener('reset', callback);
        }
    }
    // Create a new XRReferenceSpace with an offset from the current space
    getOffsetReferenceSpace(originOffset) {
        // Create a new XRReferenceSpace with the originOffset as its offsetMatrix
        // The new space's parent is set to 'this' (the current XRReferenceSpace)
        return new XRReferenceSpace(this[P_REF_SPACE].type, this, originOffset);
    }
}
_a = P_REF_SPACE;
//# sourceMappingURL=XRReferenceSpace.js.map