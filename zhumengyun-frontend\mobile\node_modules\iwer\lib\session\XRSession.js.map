{"version": 3, "file": "XRSession.js", "sourceRoot": "", "sources": ["../../src/session/XRSession.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAc,MAAM,EAAE,MAAM,qBAAqB,CAAC;AACzD,OAAO,EAAe,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EACN,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,UAAU,EACV,MAAM,EACN,OAAO,EACP,SAAS,EACT,OAAO,EACP,aAAa,GACb,MAAM,eAAe,CAAC;AAEvB,OAAO,EAAY,WAAW,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AAC9E,OAAO,EAEN,eAAe,EACf,eAAe,GACf,MAAM,yBAAyB,CAAC;AAEjC,OAAO,EACN,yBAAyB,GAEzB,MAAM,wCAAwC,CAAC;AAChD,OAAO,EACN,gBAAgB,EAChB,oBAAoB,GACpB,MAAM,+BAA+B,CAAC;AACvC,OAAO,EAAE,aAAa,EAAqB,MAAM,oBAAoB,CAAC;AACtE,OAAO,EACN,cAAc,GAEd,MAAM,6BAA6B,CAAC;AACrC,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAE7D,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAC3C,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAElD,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAWjC,MAAM,CAAN,IAAY,sBAIX;AAJD,WAAY,sBAAsB;IACjC,2CAAiB,CAAA;IACjB,oDAA0B,CAAA;IAC1B,+CAAqB,CAAA;AACtB,CAAC,EAJW,sBAAsB,KAAtB,sBAAsB,QAIjC;AAED,MAAM,CAAN,IAAY,iBAGX;AAHD,WAAY,iBAAiB;IAC5B,iDAA4B,CAAA;IAC5B,+CAA0B,CAAA;AAC3B,CAAC,EAHW,iBAAiB,KAAjB,iBAAiB,QAG5B;AAaD,MAAM,OAAO,SAAU,SAAQ,WAAW;IAiEzC,YACC,MAAgB,EAChB,IAAmB,EACnB,eAAyB;QAEzB,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,SAAS,CAAC,GAAG;YACjB,MAAM;YACN,IAAI;YACJ,WAAW,EAAE,IAAI,aAAa,EAAE;YAChC,kBAAkB,EAAE,IAAI;YACxB,eAAe,EAAE,eAAe;YAChC,yBAAyB,EAAE,KAAK;YAChC,KAAK,EAAE,KAAK;YACZ,kBAAkB,EAAE;gBACnB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE;gBAC3B,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE;gBAC5B,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE;aAC3B;YACD,mBAAmB,EAAE,CAAC,GAAU,EAAE,EAAE;gBACnC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAChD,CAAC;YACD,yBAAyB,EAAE,CAAC,kBAAwC,EAAE,EAAE;gBACvE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;oBAClE,OAAO,KAAK,CAAC;iBACb;gBACD,QAAQ,kBAAkB,EAAE;oBAC3B,KAAK,oBAAoB,CAAC,MAAM;wBAC/B,OAAO,IAAI,CAAC;oBACb,KAAK,oBAAoB,CAAC,KAAK,CAAC;oBAChC,KAAK,oBAAoB,CAAC,UAAU,CAAC;oBACrC,KAAK,oBAAoB,CAAC,YAAY,CAAC;oBACvC,KAAK,oBAAoB,CAAC,SAAS;wBAClC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,IAAI,QAAQ,CAAC;iBACzC;YACF,CAAC;YACD,WAAW,EAAE,CAAC;YACd,cAAc,EAAE,EAAE;YAClB,qBAAqB,EAAE,IAAI;YAC3B,aAAa,EAAE,GAAG,EAAE;gBACnB,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE;oBAC1B,OAAO;iBACP;gBAED,IAAI,CAAC,SAAS,CAAC,CAAC,iBAAiB,GAAG,UAAU,CAAC,qBAAqB,CACnE,IAAI,CAAC,SAAS,CAAC,CAAC,aAAa,CAC7B,CAAC;gBAEF,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,IAAI,IAAI,EAAE;oBAC/C,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,CAAC;oBACjE,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,GAAG,IAAI,CAAC;oBAC1C,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAC9C,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,SAAS,CACrC,CAAC;iBACF;gBAED,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC;gBACxD,IAAI,SAAS,KAAK,IAAI,EAAE;oBACvB,OAAO;iBACP;gBACD,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;gBAClC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBAE9B;;;;;;;;;;;;;;;;;;;;;;;;;;;mBA2BG;gBACH,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,IAAI,QAAQ,EAAE;oBACrC,MAAM,iBAAiB,GAAG,OAAO,CAAC,YAAY,CAC7C,OAAO,CAAC,iBAAiB,CACzB,CAAC;oBACF,MAAM,iBAAiB,GAAG,OAAO,CAAC,YAAY,CAC7C,OAAO,CAAC,iBAAiB,CACzB,CAAC;oBACF,MAAM,mBAAmB,GAAG,OAAO,CAAC,YAAY,CAC/C,OAAO,CAAC,mBAAmB,CAC3B,CAAC;oBACF,OAAO,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;oBACvC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBACtB,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;oBAC1B,OAAO,CAAC,KAAK,CACZ,OAAO,CAAC,gBAAgB;wBACvB,OAAO,CAAC,gBAAgB;wBACxB,OAAO,CAAC,kBAAkB,CAC3B,CAAC;oBACF,OAAO,CAAC,UAAU,CACjB,iBAAiB,CAAC,CAAC,CAAC,EACpB,iBAAiB,CAAC,CAAC,CAAC,EACpB,iBAAiB,CAAC,CAAC,CAAC,EACpB,iBAAiB,CAAC,CAAC,CAAC,CACpB,CAAC;oBACF,OAAO,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;oBACtC,OAAO,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;iBAC1C;gBAED,gCAAgC;gBAChC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC;gBAC5D,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;gBACjC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;oBACtC,MAAM,MAAM,GACX,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;wBAC5D,MAAM,CAAC;oBACR,IAAI,CAAC,WAAW,CACf,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,EAC9C,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,IAAI,EAC3B,MAAM,EACN,SAAS,EACT,QAAQ,CACR,CAAC;oBACF,IAAI,CAAC,IAAI,CACR,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,KAAK,CAAC,EAC/C,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,CAC9C,CAAC;iBACF;qBAAM;oBACN,MAAM,MAAM,GAAG,KAAK,GAAG,MAAM,CAAC;oBAC9B,IAAI,CAAC,WAAW,CACf,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,EAC9C,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,yBAA0B,EACtD,MAAM,EACN,SAAS,EACT,QAAQ,CACR,CAAC;iBACF;gBAED,MAAM,KAAK,GAAG,IAAI,OAAO,CACxB,IAAI,EACJ,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAC3B,IAAI,EACJ,IAAI,EACJ,WAAW,CAAC,GAAG,EAAE,CACjB,CAAC;gBAEF,MAAM,IAAI,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;gBAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC;gBACrD,IAAI,KAAK,EAAE;oBACV,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;iBACnB;gBAED,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,cAAc,EAAE;oBAC5C,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC;oBACjD,IAAI,GAAG,EAAE;wBACR,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;qBACjB;iBACD;gBAED,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;oBACxD,IAAI,CAAC,SAAS,CAAC,CAAC,oBAAoB,EAAE,CAAC;iBACvC;gBACD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;oBAChE,IAAI,CAAC,SAAS,CAAC,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;iBAC3C;gBACD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;oBAC/D,IAAI,CAAC,SAAS,CAAC,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;iBAC3C;gBACD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;oBACzD,IAAI,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;iBAC7C;gBAED,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACrD,IAAI,CAAC,SAAS,CAAC,CAAC,wBAAwB,EAAE,CAAC;gBAE3C;;;;;mBAKG;gBACH,gFAAgF;gBAChF,iEAAiE;gBACjE,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,qBAAqB;oBACvD,IAAI,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,CAAC;gBACjC,uEAAuE;gBACvE,IAAI,CAAC,SAAS,CAAC,CAAC,cAAc,GAAG,EAAE,CAAC;gBACpC,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;gBACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC1C,IAAI;wBACH,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;4BAC5B,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;yBACvC;qBACD;oBAAC,OAAO,GAAG,EAAE;wBACb,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;qBACnB;iBACD;gBACD,IAAI,CAAC,SAAS,CAAC,CAAC,qBAAqB,GAAG,IAAI,CAAC;gBAE7C,yCAAyC;gBACzC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;YAC/B,CAAC;YACD,gBAAgB,EAAE,MAAM,CAAC,wBAAwB;YACjD,eAAe,EAAE,EAAE;YACnB,gBAAgB,EAAE,EAAE;YACpB,kBAAkB,EAAE,EAAE;YACtB,wBAAwB,EAAE,GAAG,EAAE;gBAC9B,MAAM,cAAc,GACnB,IAAI,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;gBAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,CAAC;gBACtD,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAC5D,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,WAAW,CAAC,IAAI,IAAI,cAAc,CACpD,CAAC;gBAEF,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;gBACtE,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;gBAExE,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,GAAG,UAAU,CAAC;gBAEhD,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC3C,IAAI,CAAC,aAAa,CACjB,IAAI,yBAAyB,CAAC,oBAAoB,EAAE;wBACnD,OAAO,EAAE,IAAI;wBACb,KAAK;wBACL,OAAO;qBACP,CAAC,CACF,CAAC;iBACF;YACF,CAAC;YACD,cAAc,EAAE,IAAI,WAAW,EAAE;YACjC,iBAAiB,EAAE,IAAI,GAAG,EAAE;YAC5B,UAAU,EAAE,IAAI,GAAG,EAAE;YACrB,mBAAmB,EAAE,IAAI,WAAW,EAAE;YACtC,oBAAoB,EAAE,GAAG,EAAE;gBAC1B,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;oBACxD,IAAI,CAAC,SAAS,CAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;oBAC5C,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;wBAC7D,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE;4BAC7B,IAAI,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;4BAC9C,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gCAC3C,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC;gCAC3D,MAAM,CACL,IAAI,YAAY,CACf,6BAA6B,EAC7B,mBAAmB,CACnB,CACD,CAAC;6BACF;yBACD;6BAAM;4BACN,IAAI,CAAC,SAAS,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;4BAChD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gCAC3C,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC;gCAC5D,OAAO,CAAC,MAAM,CAAC,CAAC;gCAChB,IAAI,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;6BAC1C;yBACD;oBACF,CAAC,CAAC,CAAC;iBACH;YACF,CAAC;YACD,aAAa,EAAE,IAAI,GAAG,EAAE;YACxB,mBAAmB,EAAE,CAAC,KAAc,EAAE,EAAE;gBACvC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC;gBACjD,IAAI,CAAC,GAAG,EAAE;oBACT,OAAO;iBACP;gBACD,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;gBACvE,aAAa,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBAC/B,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;wBAClC,IAAI,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;qBAC5C;gBACF,CAAC,CAAC,CAAC;gBACH,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBACnC,IAAI,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBACvD,IAAI,CAAC,OAAO,EAAE;wBACb,MAAM,UAAU,GAAG,IAAI,OAAO,CAC7B,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAC5C,KAAK,CAAC,SAAS,CAAC,MAAM,CACtB,CAAC;wBACF,OAAO,GAAG,IAAI,OAAO,CACpB,KAAK,EACL,UAAU,EACV,KAAK,CAAC,OAAO,EACb,KAAK,CAAC,aAAa,CACnB,CAAC;wBACF,IAAI,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;qBAClD;oBACD,OAAO,CAAC,OAAO,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,oBAAoB,CAAC;oBAC9D,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;oBAC/B,KAAK,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC5C,CAAC,CAAC,CAAC;YACJ,CAAC;YACD,aAAa,EAAE,IAAI,GAAG,EAAE;YACxB,mBAAmB,EAAE,CAAC,KAAc,EAAE,EAAE;gBACvC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC;gBACjD,IAAI,CAAC,GAAG,EAAE;oBACT,OAAO;iBACP;gBACD,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;gBACvE,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBAC9B,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;wBACjC,IAAI,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;qBAC3C;gBACF,CAAC,CAAC,CAAC;gBACH,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBAClC,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBACrD,IAAI,CAAC,MAAM,EAAE;wBACZ,MAAM,SAAS,GAAG,IAAI,OAAO,CAC5B,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAC5C,IAAI,CAAC,SAAS,CAAC,MAAM,CACrB,CAAC;wBACF,MAAM,GAAG,IAAI,MAAM,CAClB,IAAI,EACJ,SAAS,EACT,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,aAAa,CAClB,CAAC;wBACF,IAAI,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;qBAChD;oBACD,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,oBAAoB,CAAC;oBAC5D,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;oBAC7B,KAAK,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC;YACJ,CAAC;YACD,cAAc,EAAE,IAAI,GAAG,EAAE;YACzB,qBAAqB,EAAE,CAAC,KAAK,EAAE,EAAE;gBAChC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC;gBACjD,IAAI,CAAC,GAAG;oBAAE,OAAO;gBACjB,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC;gBACjE,IAAI,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;oBACxD,MAAM,WAAW,GAAG,aAAa,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC;oBACpD,MAAM,kBAAkB,GACvB,YAAY,CAAC,2BAA2B,CAAC,WAAW,CAAC,CAAC;oBACvD,MAAM,cAAc,GAAG,aAAa,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC;oBAClE,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;oBACtC,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,kBAAkB,EAAE,cAAc,CAAC,CAAC;oBACnE,MAAM,cAAc,GAAsB,EAAE,CAAC;oBAC7C,GAAG,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;wBAC7D,MAAM,WAAW,GAAG,IAAI,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;wBACrD,MAAM,aAAa,GAAG,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;wBAC9D,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;oBACpC,CAAC,CAAC,CAAC;oBACH,KAAK,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;gBACrE,CAAC,CAAC,CAAC;YACJ,CAAC;YACD,KAAK,EAAE,IAAI;YACX,oBAAoB,EAAE,IAAI;YAC1B,QAAQ,EAAE,IAAI;YACd,aAAa,EAAE,IAAI;YACnB,WAAW,EAAE,IAAI;YACjB,SAAS,EAAE,IAAI;YACf,cAAc,EAAE,IAAI;YACpB,YAAY,EAAE,IAAI;YAClB,kBAAkB,EAAE,IAAI;YACxB,iBAAiB,EAAE,IAAI;SACvB,CAAC;QAEF,aAAa,CAAC,mCAAmC,CAAC,IAAI,CAAC,CAAC;QAExD,sBAAsB;QACtB,IAAI,CAAC,SAAS,CAAC,CAAC,aAAa,EAAE,CAAC;IACjC,CAAC;IAED,IAAI,eAAe;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC;IAC/C,CAAC;IAED,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,gBAAgB,CAAC;IACzC,CAAC;IAED,IAAI,mBAAmB;QACtB,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;IACrE,CAAC;IAED,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC;IACpC,CAAC;IAED,IAAI,YAAY;QACf,4BAA4B;QAC5B,IAAI,CAAC,SAAS,CAAC,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;YAChE,IAAI,CAAC,SAAS,CAAC,CAAC,gBAAgB,CAAC,IAAI,CACpC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,CACrC,CAAC;SACF;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,gBAAgB,CAAC;IACzC,CAAC;IAED,IAAI,eAAe;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC;IACxC,CAAC;IAED,IAAI,yBAAyB;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,yBAAyB,CAAC;IAClD,CAAC;IAED,IAAI,oBAAoB;;QACvB,OAAO,CACN,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,qBAAqB,CACrD,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CACpB,mCAAI,sBAAsB,CAAC,MAAM,CAClC,CAAC;IACH,CAAC;IAED,IAAI,eAAe;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC;IACzD,CAAC;IAED,iBAAiB,CAAC,QAA2B,EAAE;;QAC9C,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE;YAC1B,MAAM,IAAI,YAAY,CACrB,8BAA8B,EAC9B,mBAAmB,CACnB,CAAC;SACF;QAED,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,OAAO,KAAK,IAAI,EAAE;YACvE,MAAM,IAAI,YAAY,CACrB,iDAAiD,EACjD,mBAAmB,CACnB,CAAC;SACF;QAED,IACC,KAAK,CAAC,yBAAyB,IAAI,IAAI;YACvC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,QAAQ,EAChC;YACD,MAAM,IAAI,YAAY,CACrB,oEAAoE,EACpE,mBAAmB,CACnB,CAAC;SACF;QAED,MAAM,iBAAiB,GAAsB;YAC5C,SAAS,EACR,KAAK,CAAC,SAAS;iBACf,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,0CAAE,SAAS,CAAA;gBAC7C,SAAS;YACV,QAAQ,EACP,KAAK,CAAC,QAAQ;iBACd,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,0CAAE,QAAQ,CAAA;gBAC5C,SAAS;YACV,SAAS,EACR,KAAK,CAAC,SAAS;iBACf,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,0CAAE,SAAS,CAAA;gBAC7C,SAAS;YACV,yBAAyB,EACxB,KAAK,CAAC,yBAAyB;iBAC/B,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,0CAAE,yBAAyB,CAAA;gBAC7D,SAAS;SACV,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,GAAG,IAAI,aAAa,CACrD,iBAAiB,EACjB,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,CAC3B,CAAC;IACH,CAAC;IAED,uEAAuE;IACvE,oDAAoD;IACpD,KAAK,CAAC,qBAAqB,CAAC,IAAY;QACvC,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC5C,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE;gBAC1B,MAAM,CACL,IAAI,YAAY,CAAC,8BAA8B,EAAE,mBAAmB,CAAC,CACrE,CAAC;aACF;iBAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBACtE,MAAM,CACL,IAAI,YAAY,CACf,qCAAqC,EACrC,mBAAmB,CACnB,CACD,CAAC;aACF;iBAAM;gBACN,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,gBAAgB,KAAK,IAAI,EAAE;oBAC9C,OAAO,CAAC,GAAG,CACV,oFAAoF,CACpF,CAAC;iBACF;qBAAM;oBACN,IAAI,CAAC,SAAS,CAAC,CAAC,gBAAgB,GAAG,IAAI,CAAC;oBACxC,IAAI,CAAC,aAAa,CACjB,IAAI,cAAc,CAAC,iBAAiB,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CACxD,CAAC;oBACF,OAAO,CAAC,GAAG,CAAC,iCAAiC,IAAI,EAAE,CAAC,CAAC;iBACrD;gBACD,OAAO,EAAE,CAAC;aACV;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,qBAAqB,CAC1B,IAA0B;QAE1B,OAAO,IAAI,OAAO,CAAmB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACxD,IACC,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK;gBACrB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,yBAAyB,CAAC,IAAI,CAAC,EAC/C;gBACD,MAAM,CACL,IAAI,YAAY,CACf,sDAAsD,EACtD,mBAAmB,CACnB,CACD,CAAC;gBACF,OAAO;aACP;YACD,IAAI,cAAgC,CAAC;YACrC,QAAQ,IAAI,EAAE;gBACb,KAAK,oBAAoB,CAAC,MAAM;oBAC/B,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC;oBACpD,MAAM;gBACP,KAAK,oBAAoB,CAAC,KAAK;oBAC9B,kFAAkF;oBAClF,cAAc,GAAG,IAAI,gBAAgB,CACpC,IAAI,EACJ,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAC5C,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,YAAY,CACxD,CAAC;oBACF,MAAM;gBACP,KAAK,oBAAoB,CAAC,UAAU,CAAC;gBACrC,KAAK,oBAAoB,CAAC,YAAY,CAAC;gBACvC,KAAK,oBAAoB,CAAC,SAAS;oBAClC,iDAAiD;oBACjD,cAAc,GAAG,IAAI,gBAAgB,CACpC,IAAI,EACJ,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,CAC5C,CAAC;oBACF,MAAM;aACP;YACD,IAAI,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACrD,OAAO,CAAC,cAAc,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,qBAAqB,CAAC,QAAgC;QACrD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE;YAC1B,OAAO,CAAC,CAAC;SACT;QACD,MAAM,WAAW,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC;QAClD,IAAI,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC;YACnC,MAAM,EAAE,WAAW;YACnB,QAAQ;YACR,SAAS,EAAE,KAAK;SAChB,CAAC,CAAC;QACH,OAAO,WAAW,CAAC;IACpB,CAAC;IAED,oBAAoB,CAAC,MAAc;QAClC,sDAAsD;QACtD,IAAI,SAAS,GAA0B,IAAI,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC;QACtE,IAAI,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;QACjE,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACf,SAAS,CAAC,KAAK,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC;YAClC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC3B;QACD,6EAA6E;QAC7E,iDAAiD;QACjD,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC;QAClD,IAAI,SAAS,EAAE;YACd,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;YAC7D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;gBACf,SAAS,CAAC,KAAK,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC;gBAClC,iFAAiF;aACjF;SACD;IACF,CAAC;IAED,KAAK,CAAC,GAAG;QACR,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACtC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,iBAAiB,KAAK,IAAI,EAAE;gBACxE,MAAM,CACL,IAAI,YAAY,CAAC,8BAA8B,EAAE,mBAAmB,CAAC,CACrE,CAAC;aACF;iBAAM;gBACN,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,iBAAkB,CAAC,CAAC;gBACpE,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,EAAE,CAAC;gBAChD,IAAI,CAAC,aAAa,CAAC,IAAI,cAAc,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;gBACjE,OAAO,EAAE,CAAC;aACV;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,UAAU;IACV,IAAI,iBAAiB;QACpB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED,uBAAuB,CAAC,IAAY;QACnC,OAAO,IAAI,OAAO,CAAW,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAChD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACjD,MAAM,CACL,IAAI,YAAY,CACf,+BAA+B,IAAI,aAAa,EAChD,mBAAmB,CACnB,CACD,CAAC;aACF;iBAAM,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE;gBACjC,MAAM,CACL,IAAI,YAAY,CAAC,8BAA8B,EAAE,mBAAmB,CAAC,CACrE,CAAC;aACF;iBAAM;gBACN,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;gBAC5D,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;oBAC3C,MAAM,CACL,IAAI,YAAY,CACf,0EAA0E,IAAI,GAAG,EACjF,mBAAmB,CACnB,CACD,CAAC;iBACF;qBAAM;oBACN,IAAI,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBAC3C,IAAI,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;iBAC5D;aACD;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,sBAAsB,CAAC,IAAY;QAClC,OAAO,IAAI,OAAO,CAAY,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACjD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACjD,MAAM,CACL,IAAI,YAAY,CACf,+BAA+B,IAAI,aAAa,EAChD,mBAAmB,CACnB,CACD,CAAC;aACF;iBAAM;gBACN,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;gBAC5D,IAAI,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC/C,MAAM,CAAC,MAAM,EAAE,CAAC;gBAChB,OAAO,CAAC,SAAS,CAAC,CAAC;aACnB;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,oBAAoB,CAAC,OAA6B;QACjD,OAAO,IAAI,OAAO,CAAkB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACvD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;gBAC1D,MAAM,CACL,IAAI,YAAY,CACf,8DAA8D,EAC9D,mBAAmB,CACnB,CACD,CAAC;aACF;iBAAM,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE;gBACjC,MAAM,CACL,IAAI,YAAY,CAAC,8BAA8B,EAAE,mBAAmB,CAAC,CACrE,CAAC;aACF;iBAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE;gBACjD,MAAM,CACL,IAAI,YAAY,CACf,+DAA+D,EAC/D,gBAAgB,CAChB,CACD,CAAC;aACF;iBAAM;gBACN,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAC3D,IAAI,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;gBACpD,OAAO,CAAC,eAAe,CAAC,CAAC;aACzB;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,SAAS;IACT,IAAI,KAAK;;QACR,OAAO,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,mCAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,KAAK,CAAC,QAA+B;QACxC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,KAAsB,CAAC,CAAC;SACxE;QACD,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,GAAG,QAAQ,CAAC;QACjC,IAAI,QAAQ,EAAE;YACb,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAyB,CAAC,CAAC;SACxD;IACF,CAAC;IAED,IAAI,oBAAoB;;QACvB,OAAO,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,oBAAoB,mCAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED,IAAI,oBAAoB,CAAC,QAA0C;QAClE,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,oBAAoB,EAAE;YACzC,IAAI,CAAC,mBAAmB,CACvB,oBAAoB,EACpB,IAAI,CAAC,SAAS,CAAC,CAAC,oBAAqC,CACrD,CAAC;SACF;QACD,IAAI,CAAC,SAAS,CAAC,CAAC,oBAAoB,GAAG,QAAQ,CAAC;QAChD,IAAI,QAAQ,EAAE;YACb,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,QAAyB,CAAC,CAAC;SACvE;IACF,CAAC;IAED,IAAI,QAAQ;;QACX,OAAO,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,mCAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED,IAAI,QAAQ,CAAC,QAAmC;QAC/C,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE;YAC7B,IAAI,CAAC,mBAAmB,CACvB,QAAQ,EACR,IAAI,CAAC,SAAS,CAAC,CAAC,QAAyB,CACzC,CAAC;SACF;QACD,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACpC,IAAI,QAAQ,EAAE;YACb,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAyB,CAAC,CAAC;SAC3D;IACF,CAAC;IAED,IAAI,aAAa;;QAChB,OAAO,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,aAAa,mCAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,aAAa,CAAC,QAAmC;QACpD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,aAAa,EAAE;YAClC,IAAI,CAAC,mBAAmB,CACvB,aAAa,EACb,IAAI,CAAC,SAAS,CAAC,CAAC,aAA8B,CAC9C,CAAC;SACF;QACD,IAAI,CAAC,SAAS,CAAC,CAAC,aAAa,GAAG,QAAQ,CAAC;QACzC,IAAI,QAAQ,EAAE;YACb,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,QAAyB,CAAC,CAAC;SAChE;IACF,CAAC;IAED,IAAI,WAAW;;QACd,OAAO,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,mCAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,IAAI,WAAW,CAAC,QAAmC;QAClD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE;YAChC,IAAI,CAAC,mBAAmB,CACvB,WAAW,EACX,IAAI,CAAC,SAAS,CAAC,CAAC,WAA4B,CAC5C,CAAC;SACF;QACD,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,GAAG,QAAQ,CAAC;QACvC,IAAI,QAAQ,EAAE;YACb,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAyB,CAAC,CAAC;SAC9D;IACF,CAAC;IAED,IAAI,SAAS;;QACZ,OAAO,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,mCAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAChD,CAAC;IAED,IAAI,SAAS,CAAC,QAAmC;QAChD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE;YAC9B,IAAI,CAAC,mBAAmB,CACvB,SAAS,EACT,IAAI,CAAC,SAAS,CAAC,CAAC,SAA0B,CAC1C,CAAC;SACF;QACD,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,GAAG,QAAQ,CAAC;QACrC,IAAI,QAAQ,EAAE;YACb,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAyB,CAAC,CAAC;SAC5D;IACF,CAAC;IAED,IAAI,cAAc;;QACjB,OAAO,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,cAAc,mCAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IACrD,CAAC;IAED,IAAI,cAAc,CAAC,QAAmC;QACrD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE;YACnC,IAAI,CAAC,mBAAmB,CACvB,cAAc,EACd,IAAI,CAAC,SAAS,CAAC,CAAC,cAA+B,CAC/C,CAAC;SACF;QACD,IAAI,CAAC,SAAS,CAAC,CAAC,cAAc,GAAG,QAAQ,CAAC;QAC1C,IAAI,QAAQ,EAAE;YACb,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,QAAyB,CAAC,CAAC;SACjE;IACF,CAAC;IAED,IAAI,YAAY;;QACf,OAAO,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,YAAY,mCAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IACnD,CAAC;IAED,IAAI,YAAY,CAAC,QAAmC;QACnD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,YAAY,EAAE;YACjC,IAAI,CAAC,mBAAmB,CACvB,YAAY,EACZ,IAAI,CAAC,SAAS,CAAC,CAAC,YAA6B,CAC7C,CAAC;SACF;QACD,IAAI,CAAC,SAAS,CAAC,CAAC,YAAY,GAAG,QAAQ,CAAC;QACxC,IAAI,QAAQ,EAAE;YACb,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,QAAyB,CAAC,CAAC;SAC/D;IACF,CAAC;IAED,IAAI,kBAAkB;;QACrB,OAAO,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,mCAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,IAAI,kBAAkB,CAAC,QAA+B;QACrD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE;YACvC,IAAI,CAAC,mBAAmB,CACvB,kBAAkB,EAClB,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAmC,CACnD,CAAC;SACF;QACD,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,GAAG,QAAQ,CAAC;QAC9C,IAAI,QAAQ,EAAE;YACb,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,QAAyB,CAAC,CAAC;SACrE;IACF,CAAC;IAED,IAAI,iBAAiB;;QACpB,OAAO,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,iBAAiB,mCAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IACxD,CAAC;IAED,IAAI,iBAAiB,CAAC,QAA+B;QACpD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,iBAAiB,EAAE;YACtC,IAAI,CAAC,mBAAmB,CACvB,iBAAiB,EACjB,IAAI,CAAC,SAAS,CAAC,CAAC,iBAAkC,CAClD,CAAC;SACF;QACD,IAAI,CAAC,SAAS,CAAC,CAAC,iBAAiB,GAAG,QAAQ,CAAC;QAC7C,IAAI,QAAQ,EAAE;YACb,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,QAAyB,CAAC,CAAC;SACpE;IACF,CAAC;CACD"}