import { Camera, ColorRepresentation } from 'three';
import { Axis } from '../../state.js';
import { HandlesContext } from '../context.js';
import { HandlesProperties } from '../index.js';
import { RegisteredHandle } from '../registered.js';
export declare class AxisRotateHandle extends RegisteredHandle {
    private readonly direction;
    constructor(context: HandlesContext, axis: Axis, tagPrefix?: string);
    update(camera: Camera): void;
    bind(defaultColor: ColorRepresentation, config?: HandlesProperties): (() => void) | undefined;
}
