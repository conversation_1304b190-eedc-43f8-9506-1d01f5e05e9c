'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import ComplianceNotice from '@/components/ComplianceNotice'

export default function LegalPage() {
  const [activeTab, setActiveTab] = useState<'terms' | 'privacy' | 'compliance'>('terms')

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 text-white">
      {/* 头部 */}
      <div className="sticky top-0 z-20 bg-black/20 backdrop-blur-lg border-b border-white/10">
        <div className="px-4 py-4">
          <h1 className="text-xl font-bold text-center">法律条款</h1>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="px-4 py-4">
        <div className="flex space-x-1 bg-white/10 rounded-lg p-1">
          {[
            { key: 'terms', label: '用户协议', icon: '📋' },
            { key: 'privacy', label: '隐私政策', icon: '🔒' },
            { key: 'compliance', label: '合规声明', icon: '⚖️' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.key
                  ? 'bg-blue-500 text-white'
                  : 'text-white/70 hover:text-white'
              }`}
            >
              <span>{tab.icon}</span>
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* 内容区域 */}
      <div className="px-4 pb-8">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20"
        >
          {activeTab === 'terms' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-center mb-6">NextGen 2025 用户服务协议</h2>
              
              <div className="space-y-4">
                <section>
                  <h3 className="text-lg font-semibold mb-3 text-blue-300">第一条 协议的接受</h3>
                  <p className="text-gray-300 leading-relaxed">
                    欢迎使用NextGen 2025平台！本协议是您与NextGen科技有限公司（以下简称"我们"或"平台"）之间关于使用NextGen 2025平台服务的法律协议。
                    通过注册、访问或使用我们的服务，您表示同意受本协议的约束。
                  </p>
                </section>

                <section>
                  <h3 className="text-lg font-semibold mb-3 text-blue-300">第二条 服务说明</h3>
                  <div className="text-gray-300 leading-relaxed space-y-2">
                    <p>NextGen 2025是一个合规的数字创作者服务平台，提供以下服务：</p>
                    <ul className="list-disc list-inside space-y-1 ml-4">
                      <li>数字内容创作和分享服务</li>
                      <li>专业技能服务交易平台</li>
                      <li>平台内积分奖励系统</li>
                      <li>数字资产展示和交易服务</li>
                      <li>创作者社区和协作功能</li>
                    </ul>
                  </div>
                </section>

                <section>
                  <h3 className="text-lg font-semibold mb-3 text-blue-300">第三条 用户义务</h3>
                  <div className="text-gray-300 leading-relaxed space-y-2">
                    <p>作为平台用户，您需要遵守以下义务：</p>
                    <ul className="list-disc list-inside space-y-1 ml-4">
                      <li>提供真实、准确的个人信息并完成实名认证</li>
                      <li>遵守中华人民共和国相关法律法规</li>
                      <li>不得发布违法、有害、虚假的内容</li>
                      <li>尊重他人知识产权和合法权益</li>
                      <li>不得进行任何形式的虚拟货币交易</li>
                      <li>配合平台的合规管理和监督</li>
                    </ul>
                  </div>
                </section>

                <section>
                  <h3 className="text-lg font-semibold mb-3 text-blue-300">第四条 积分系统说明</h3>
                  <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                    <p className="text-yellow-200 font-medium mb-2">重要说明：</p>
                    <div className="text-yellow-100 text-sm space-y-1">
                      <p>• 平台积分仅为虚拟积分，不具有货币属性</p>
                      <p>• 积分仅可用于平台内服务消费，不可提现或转换为法定货币</p>
                      <p>• 平台有权根据运营需要调整积分规则</p>
                      <p>• 禁止任何形式的积分买卖或转让</p>
                    </div>
                  </div>
                </section>

                <section>
                  <h3 className="text-lg font-semibold mb-3 text-blue-300">第五条 知识产权</h3>
                  <p className="text-gray-300 leading-relaxed">
                    用户在平台上发布的原创内容，其知识产权归用户所有。平台仅获得为提供服务所必需的使用权。
                    用户保证其发布的内容不侵犯第三方的知识产权，如有侵权，用户承担全部法律责任。
                  </p>
                </section>

                <section>
                  <h3 className="text-lg font-semibold mb-3 text-blue-300">第六条 免责声明</h3>
                  <p className="text-gray-300 leading-relaxed">
                    平台仅作为信息发布和交易撮合的中介平台，不对用户间的交易承担担保责任。
                    用户应自行判断交易风险，平台不承担因用户决策失误造成的损失。
                  </p>
                </section>

                <section>
                  <h3 className="text-lg font-semibold mb-3 text-blue-300">第七条 协议修改</h3>
                  <p className="text-gray-300 leading-relaxed">
                    我们有权根据法律法规变化或业务发展需要修改本协议。修改后的协议将在平台上公布，
                    继续使用服务即表示您接受修改后的协议。
                  </p>
                </section>
              </div>
            </div>
          )}

          {activeTab === 'privacy' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-center mb-6">隐私保护政策</h2>
              
              <div className="space-y-4">
                <section>
                  <h3 className="text-lg font-semibold mb-3 text-green-300">信息收集</h3>
                  <div className="text-gray-300 leading-relaxed space-y-2">
                    <p>我们可能收集以下类型的信息：</p>
                    <ul className="list-disc list-inside space-y-1 ml-4">
                      <li>注册信息：姓名、手机号、邮箱等</li>
                      <li>身份认证信息：身份证号、银行卡信息等</li>
                      <li>使用信息：浏览记录、操作日志等</li>
                      <li>设备信息：设备型号、操作系统等</li>
                    </ul>
                  </div>
                </section>

                <section>
                  <h3 className="text-lg font-semibold mb-3 text-green-300">信息使用</h3>
                  <div className="text-gray-300 leading-relaxed space-y-2">
                    <p>我们使用收集的信息用于：</p>
                    <ul className="list-disc list-inside space-y-1 ml-4">
                      <li>提供和改进我们的服务</li>
                      <li>进行身份验证和安全保护</li>
                      <li>个性化内容推荐</li>
                      <li>合规监管和风险控制</li>
                      <li>客户服务和技术支持</li>
                    </ul>
                  </div>
                </section>

                <section>
                  <h3 className="text-lg font-semibold mb-3 text-green-300">信息保护</h3>
                  <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                    <p className="text-green-200 font-medium mb-2">我们承诺：</p>
                    <div className="text-green-100 text-sm space-y-1">
                      <p>• 采用行业标准的安全措施保护您的信息</p>
                      <p>• 不会向第三方出售您的个人信息</p>
                      <p>• 严格限制员工访问个人信息的权限</p>
                      <p>• 定期进行安全审计和漏洞检测</p>
                    </div>
                  </div>
                </section>

                <section>
                  <h3 className="text-lg font-semibold mb-3 text-green-300">您的权利</h3>
                  <div className="text-gray-300 leading-relaxed space-y-2">
                    <p>根据《个人信息保护法》，您享有以下权利：</p>
                    <ul className="list-disc list-inside space-y-1 ml-4">
                      <li>知情权：了解个人信息处理情况</li>
                      <li>决定权：同意或拒绝个人信息处理</li>
                      <li>查询权：查询个人信息处理情况</li>
                      <li>更正权：更正不准确的个人信息</li>
                      <li>删除权：要求删除个人信息</li>
                    </ul>
                  </div>
                </section>
              </div>
            </div>
          )}

          {activeTab === 'compliance' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-center mb-6">合规运营声明</h2>
              
              <ComplianceNotice variant="inline" />
              
              <div className="space-y-4">
                <section>
                  <h3 className="text-lg font-semibold mb-3 text-yellow-300">监管合规</h3>
                  <div className="text-gray-300 leading-relaxed space-y-2">
                    <p>我们严格遵守以下法律法规：</p>
                    <ul className="list-disc list-inside space-y-1 ml-4">
                      <li>《网络安全法》- 保障网络安全和数据保护</li>
                      <li>《数据安全法》- 规范数据处理活动</li>
                      <li>《个人信息保护法》- 保护个人信息权益</li>
                      <li>《电子商务法》- 规范电子商务活动</li>
                      <li>《反洗钱法》- 预防洗钱和恐怖主义融资</li>
                    </ul>
                  </div>
                </section>

                <section>
                  <h3 className="text-lg font-semibold mb-3 text-yellow-300">业务合规</h3>
                  <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                    <p className="text-red-200 font-medium mb-2">明确声明：</p>
                    <div className="text-red-100 text-sm space-y-1">
                      <p>• 本平台不涉及任何虚拟货币交易</p>
                      <p>• 不进行ICO、IEO等代币发行活动</p>
                      <p>• 不提供虚拟货币兑换服务</p>
                      <p>• 所有交易均使用人民币结算</p>
                      <p>• 严格执行实名制认证</p>
                    </div>
                  </div>
                </section>

                <section>
                  <h3 className="text-lg font-semibold mb-3 text-yellow-300">监督举报</h3>
                  <div className="text-gray-300 leading-relaxed">
                    <p className="mb-2">如发现平台存在违法违规行为，欢迎举报：</p>
                    <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                      <div className="space-y-2 text-sm">
                        <p><strong>举报邮箱：</strong> <EMAIL></p>
                        <p><strong>举报电话：</strong> 400-123-4567</p>
                        <p><strong>监管部门：</strong> 国家互联网信息办公室</p>
                        <p><strong>平台地址：</strong> 北京市海淀区中关村软件园</p>
                      </div>
                    </div>
                  </div>
                </section>

                <section>
                  <h3 className="text-lg font-semibold mb-3 text-yellow-300">定期审查</h3>
                  <p className="text-gray-300 leading-relaxed">
                    我们定期进行合规审查，确保平台运营符合最新的法律法规要求。
                    同时接受监管部门的检查和指导，持续改进合规管理体系。
                  </p>
                </section>
              </div>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  )
}
