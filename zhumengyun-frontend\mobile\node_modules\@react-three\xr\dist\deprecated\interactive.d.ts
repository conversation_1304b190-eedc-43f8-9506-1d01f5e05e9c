import { Group } from 'three';
import { useInteraction } from './hooks.js';
type XRInteractionHandler = Parameters<typeof useInteraction>[2];
/**
 * @deprecated Just use `<group onClick... />`
 */
export declare const Interactive: import("react").ForwardRefExoticComponent<{
    onHover?: XRInteractionHandler;
    onBlur?: XRInteractionHandler;
    onSelectStart?: XRInteractionHandler;
    onSelectEnd?: XRInteractionHandler;
    onSelect?: XRInteractionHandler;
    onSqueezeStart?: XRInteractionHandler;
    onSqueezeEnd?: XRInteractionHandler;
    onSqueeze?: XRInteractionHandler;
    onMove?: XRInteractionHandler;
    children: React.ReactNode;
} & import("react").RefAttributes<Group<import("three").Object3DEventMap>>>;
export {};
