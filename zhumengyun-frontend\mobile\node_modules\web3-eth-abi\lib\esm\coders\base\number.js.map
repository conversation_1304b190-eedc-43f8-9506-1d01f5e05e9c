{"version": 3, "file": "number.js", "sourceRoot": "", "sources": ["../../../../src/coders/base/number.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AAEF,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AAEvC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAC/C,OAAO,EAAE,KAAK,EAAE,MAAM,gBAAgB,CAAC;AAEvC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAElD,sCAAsC;AACtC,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;AAEtC,SAAS,kBAAkB,CAAC,KAAa,EAAE,UAAU,GAAG,SAAS;IAChE,IAAI,QAAQ,CAAC;IACb,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;QACf,QAAQ,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACxC,CAAC;SAAM,CAAC;QACP,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;IACD,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;IAC7C,OAAO,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;AACxC,CAAC;AAED,SAAS,kBAAkB,CAAC,KAAiB,EAAE,GAAW;IACzD,MAAM,QAAQ,GAAG,KAAK,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IACpD,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;IAChC,IAAI,MAAM,IAAI,GAAG;QAAE,OAAO,MAAM,CAAC;IACjC,OAAO,MAAM,GAAG,IAAI,CAAC;AACtB,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,KAAmB,EAAE,KAAc;IAC/D,IAAI,KAAK,CAAC;IACV,IAAI,CAAC;QACJ,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACZ,MAAM,IAAI,QAAQ,CAAC,oCAAoC,EAAE;YACxD,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,KAAK,CAAC,IAAI;SAChB,CAAC,CAAC;IACJ,CAAC;IACD,MAAM,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC3C,IAAI,CAAC,KAAK,EAAE,CAAC;QACZ,MAAM,IAAI,QAAQ,CAAC,+CAA+C,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3F,CAAC;IACD,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,IAAI,QAAQ,CAAC,oDAAoD,EAAE;YACxE,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;SAC7B,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,IAAI,QAAQ,CAAC,uDAAuD,EAAE;YAC3E,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;SAC7B,CAAC,CAAC;IACJ,CAAC;IACD,OAAO;QACN,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,kBAAkB,CAAC,KAAK,CAAC;KAClC,CAAC;AACH,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,KAAmB,EAAE,KAAiB;IAClE,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;QAC9B,MAAM,IAAI,QAAQ,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3F,CAAC;IACD,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;IAC/C,MAAM,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC3C,IAAI,CAAC,KAAK,EAAE,CAAC;QACZ,MAAM,IAAI,QAAQ,CAAC,+CAA+C,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3F,CAAC;IACD,MAAM,YAAY,GAAG,kBAAkB,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;IAE9D,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;QAC9B,MAAM,IAAI,QAAQ,CAAC,mDAAmD,EAAE;YACvE,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,KAAK,EAAE,YAAY;YACnB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;SAC7B,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;QAC9B,MAAM,IAAI,QAAQ,CAAC,sDAAsD,EAAE;YAC1E,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,KAAK,EAAE,YAAY;YACnB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;SAC7B,CAAC,CAAC;IACJ,CAAC;IACD,OAAO;QACN,MAAM,EAAE,YAAY;QACpB,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC;QAClC,QAAQ,EAAE,SAAS;KACnB,CAAC;AACH,CAAC"}