{"version": 3, "file": "XRHitTest.js", "sourceRoot": "", "sources": ["../../src/hittest/XRHitTest.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAItD,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AASrE,MAAM,OAAO,eAAe;IAO3B,YAAY,OAAkB,EAAE,OAA6B;;QAC5D,IAAI,CAAC,UAAU,CAAC,GAAG;YAClB,OAAO;YACP,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,SAAS,EAAE,MAAA,OAAO,CAAC,SAAS,mCAAI,IAAI,KAAK,EAAE;SAC3C,CAAC;IACH,CAAC;IAED,MAAM;QACL,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACjE,CAAC;CACD;AAED,MAAM,OAAO,eAAe;IAM3B,YAAY,KAAc,EAAE,WAAoB;QAC/C,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;IAC3C,CAAC;IAED,OAAO,CAAC,SAAkB;QACzB,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,CACpC,IAAI,CAAC,UAAU,CAAC,CAAC,WAAW,EAC5B,SAAS,CACT,CAAC;IACH,CAAC;IAED,YAAY;QACX,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CACzC,IAAI,gBAAgB,EAAE,EACtB,IAAI,CAAC,UAAU,CAAC,CAAC,WAAW,CAC5B,CAAC;IACH,CAAC;CACD"}