{"version": 3, "file": "XRRigidTransform.js", "sourceRoot": "", "sources": ["../../src/primitives/XRRigidTransform.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAE7C,OAAO,EAAE,gBAAgB,EAAE,MAAM,8BAA8B,CAAC;AAChE,OAAO,EAAE,iBAAiB,EAAE,MAAM,eAAe,CAAC;AAElD,MAAM,OAAO,gBAAgB;IAQ5B,YAAY,QAAuB,EAAE,WAA0B;QAC9D,iBAAiB;QACjB,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACjD,MAAM,kBAAkB,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAEzC,IAAI,CAAC,iBAAiB,CAAC,GAAG;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE;YACrB,QAAQ,EAAE,QAAQ;gBACjB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAE,EAAE,QAAQ,CAAC,CAAE,EAAE,QAAQ,CAAC,CAAE,CAAC;gBACxD,CAAC,CAAC,eAAe;YAClB,WAAW,EAAE,WAAW;gBACvB,CAAC,CAAC,IAAI,CAAC,SAAS,CACd,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,UAAU,CACd,WAAW,CAAC,CAAE,EACd,WAAW,CAAC,CAAE,EACd,WAAW,CAAC,CAAE,EACd,WAAW,CAAC,CAAE,CACd,CACA;gBACH,CAAC,CAAC,kBAAkB;YACrB,OAAO,EAAE,IAAI;SACb,CAAC;QAEF,IAAI,CAAC,YAAY,EAAE,CAAC;IACrB,CAAC;IAEO,YAAY;QACnB,IAAI,CAAC,uBAAuB,CAC3B,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,EAC9B,IAAI,CAAC,iBAAiB,CAAC,CAAC,WAAW,EACnC,IAAI,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAChC,CAAC;IACH,CAAC;IAED,IAAI,MAAM;QACT,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAsB,CAAC;IACvD,CAAC;IAED,IAAI,QAAQ;QACX,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC;QAC7C,OAAO,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxD,CAAC;IAED,IAAI,WAAW;QACd,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,WAAW,CAAC;QAChD,OAAO,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED,IAAI,OAAO;QACV,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,EAAE;YACrC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,EAAE;gBAC5D,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;aAC7C;YAED,+DAA+D;YAC/D,IAAI,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAChC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAE5C,IAAI,cAAc,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YACnC,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;YAE5C,kDAAkD;YAClD,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,GAAG,IAAI,gBAAgB,CACrD,IAAI,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EACvE,IAAI,gBAAgB,CACnB,cAAc,CAAC,CAAC,CAAC,EACjB,cAAc,CAAC,CAAC,CAAC,EACjB,cAAc,CAAC,CAAC,CAAC,EACjB,cAAc,CAAC,CAAC,CAAC,CACjB,CACD,CAAC;YAEF,0DAA0D;YAC1D,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC;SAClE;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC;IACxC,CAAC;CACD"}