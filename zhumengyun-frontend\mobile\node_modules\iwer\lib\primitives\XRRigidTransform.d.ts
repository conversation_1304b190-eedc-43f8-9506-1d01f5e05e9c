/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { mat4, quat, vec3 } from 'gl-matrix';
import { P_RIGID_TRANSFORM } from '../private.js';
export declare class XRRigidTransform {
    [P_RIGID_TRANSFORM]: {
        matrix: mat4;
        position: vec3;
        orientation: quat;
        inverse: XRRigidTransform | null;
    };
    constructor(position?: DOMPointInit, orientation?: DOMPointInit);
    private updateMatrix;
    get matrix(): Float32Array;
    get position(): DOMPointReadOnly;
    get orientation(): DOMPointReadOnly;
    get inverse(): XRRigidTransform;
}
//# sourceMappingURL=XRRigidTransform.d.ts.map