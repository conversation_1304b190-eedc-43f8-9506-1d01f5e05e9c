import*as e from"../../core/i18n/i18n.js";import*as n from"../../ui/legacy/legacy.js";const t={webauthn:"WebAuthn",showWebauthn:"Show WebAuthn"},a=e.i18n.registerUIStrings("panels/webauthn/webauthn-meta.ts",t),i=e.i18n.getLazilyComputedLocalizedString.bind(void 0,a);let o;n.ViewManager.registerViewExtension({location:"drawer-view",id:"webauthn-pane",title:i(t.webauthn),commandPrompt:i(t.showWebauthn),order:100,persistence:"closeable",loadView:async()=>new((await async function(){return o||(o=await import("./webauthn.js")),o}()).WebauthnPane.WebauthnPaneImpl)});
