{"version": 3, "file": "fetch.d.ts", "sourceRoot": "", "sources": ["../../src.ts/utils/fetch.ts"], "names": [], "mappings": "AA2BA;;GAEG;AACH,MAAM,MAAM,cAAc,GAAG;IACzB,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,MAAM,CAAC;IACtB,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAChC,IAAI,EAAE,IAAI,GAAG,UAAU,CAAA;CAC1B,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,mBAAmB,GAAG;IAC9B,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,YAAY,CAAC,EAAE,MAAM,CAAC;CACzB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,kBAAkB,GAAG,CAAC,GAAG,EAAE,YAAY,KAAK,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9E;;GAEG;AACH,MAAM,MAAM,gBAAgB,GAAG,CAAC,GAAG,EAAE,YAAY,EAAE,IAAI,EAAE,aAAa,KAAK,OAAO,CAAC,aAAa,CAAC,CAAC;AAElG;;GAEG;AACH,MAAM,MAAM,cAAc,GAAG,CAAC,GAAG,EAAE,YAAY,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;AAE3G;;GAEG;AACH,MAAM,MAAM,gBAAgB,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,iBAAiB,KAAK,OAAO,CAAC,YAAY,GAAG,aAAa,CAAC,CAAC;AAElH;;;;;GAKG;AACH,MAAM,MAAM,eAAe,GAAG,CAAC,GAAG,EAAE,YAAY,EAAE,MAAM,CAAC,EAAE,iBAAiB,KAAK,OAAO,CAAC,cAAc,CAAC,CAAC;AAqDzG;;GAEG;AACH,qBAAa,iBAAiB;;gBAId,OAAO,EAAE,YAAY;IAejC,WAAW,CAAC,QAAQ,EAAE,MAAM,IAAI,GAAG,IAAI;IAOvC,IAAI,SAAS,IAAI,OAAO,CAA4B;IAEpD,WAAW,IAAI,IAAI;CAGtB;AASD;;;;;;;;;;;;;GAaG;AACH,qBAAa,YAAa,YAAW,QAAQ,CAAC,CAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAE,CAAC;;IAuBzE;;OAEG;IACH,IAAI,GAAG,IAAI,MAAM,CAAsB;IACvC,IAAI,GAAG,CAAC,GAAG,EAAE,MAAM,EAElB;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,IAAI,IAAI,IAAI,IAAI,GAAG,UAAU,CAG5B;IACD,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,EAgBrE;IAED;;OAEG;IACH,OAAO,IAAI,IAAI,IAAI,CAAC,YAAY,GAAG;QAAE,IAAI,EAAE,UAAU,CAAA;KAAE,CAAC;IAIxD;;;;OAIG;IACH,IAAI,MAAM,IAAI,MAAM,CAInB;IACD,IAAI,MAAM,CAAC,MAAM,EAAE,IAAI,GAAG,MAAM,EAG/B;IAED;;;;;;;;OAQG;IACH,IAAI,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAiBpC;IAED;;OAEG;IACH,SAAS,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM;IAI9B;;;OAGG;IACH,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;IAIpD;;OAEG;IACH,YAAY,IAAI,IAAI;IAIpB,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAE,CAAC;IAiB7D;;;;OAIG;IACH,IAAI,WAAW,IAAI,IAAI,GAAG,MAAM,CAE/B;IAED;;OAEG;IACH,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI;IAKxD;;;OAGG;IACH,IAAI,SAAS,IAAI,OAAO,CAEvB;IACD,IAAI,SAAS,CAAC,KAAK,EAAE,OAAO,EAE3B;IAED;;;OAGG;IACH,IAAI,2BAA2B,IAAI,OAAO,CAEzC;IACD,IAAI,2BAA2B,CAAC,KAAK,EAAE,OAAO,EAE7C;IAED;;;OAGG;IACH,IAAI,OAAO,IAAI,MAAM,CAA0B;IAC/C,IAAI,OAAO,CAAC,OAAO,EAAE,MAAM,EAG1B;IAED;;;;;;OAMG;IACH,IAAI,aAAa,IAAI,IAAI,GAAG,kBAAkB,CAE7C;IACD,IAAI,aAAa,CAAC,SAAS,EAAE,IAAI,GAAG,kBAAkB,EAErD;IAED;;;;;;;;;OASG;IACH,IAAI,WAAW,IAAI,IAAI,GAAG,gBAAgB,CAEzC;IACD,IAAI,WAAW,CAAC,OAAO,EAAE,IAAI,GAAG,gBAAgB,EAE/C;IAED;;OAEG;IACH,IAAI,SAAS,IAAI,IAAI,GAAG,cAAc,CAErC;IACD,IAAI,SAAS,CAAC,KAAK,EAAE,IAAI,GAAG,cAAc,EAEzC;IAED;;;;;;;;;;;;;;OAcG;IACH,IAAI,UAAU,IAAI,eAAe,CAEhC;IACD,IAAI,UAAU,CAAC,KAAK,EAAE,IAAI,GAAG,eAAe,EAE3C;IAED;;;;;OAKG;gBACS,GAAG,EAAE,MAAM;IAiBvB,QAAQ,IAAI,MAAM;IAIlB;;;OAGG;IACH,iBAAiB,CAAC,MAAM,EAAE,mBAAmB,GAAG,IAAI;IAqGpD;;OAEG;IACH,IAAI,IAAI,OAAO,CAAC,aAAa,CAAC;IAM9B;;;OAGG;IACH,MAAM,IAAI,IAAI;IAOd;;;OAGG;IACH,QAAQ,CAAC,QAAQ,EAAE,MAAM,GAAG,YAAY;IA8BxC;;OAEG;IACH,KAAK,IAAI,YAAY;IAgCrB;;;OAGG;IACH,MAAM,CAAC,UAAU,IAAI,IAAI;IAIzB;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,GAAG,gBAAgB;IAI1D;;;;;;;OAOG;IACH,MAAM,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,GAAG,IAAI;IASpE;;;;;;;OAOG;IACH,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,eAAe,GAAG,IAAI;IAKpD;;;;;;;;;;OAUG;IACH,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,eAAe;IAIvE;;;;;;;;OAQG;IACH,MAAM,CAAC,iBAAiB,IAAI,gBAAgB;IAI5C;;;;;;OAMG;IACH,MAAM,CAAC,qBAAqB,CAAC,OAAO,EAAE,MAAM,GAAG,gBAAgB;CAGlE;AAQD;;GAEG;AACH,qBAAa,aAAc,YAAW,QAAQ,CAAC,CAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAE,CAAC;;IAS1E,QAAQ,IAAI,MAAM;IAIlB;;OAEG;IACH,IAAI,UAAU,IAAI,MAAM,CAA6B;IAErD;;OAEG;IACH,IAAI,aAAa,IAAI,MAAM,CAAgC;IAE3D;;OAEG;IACH,IAAI,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAA8C;IAEnF;;OAEG;IACH,IAAI,IAAI,IAAI,IAAI,GAAG,QAAQ,CAAC,UAAU,CAAC,CAEtC;IAED;;;;;OAKG;IACH,IAAI,QAAQ,IAAI,MAAM,CAQrB;IAED;;;;;OAKG;IACH,IAAI,QAAQ,IAAI,GAAG,CAQlB;IAED,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAE,CAAC;gBAiBjD,UAAU,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,GAAG,UAAU,EAAE,OAAO,CAAC,EAAE,YAAY;IAajJ;;;;OAIG;IACH,eAAe,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,aAAa;IAc/D;;;;OAIG;IACH,kBAAkB,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,KAAK;IAc3D;;OAEG;IACH,SAAS,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM;IAI9B;;OAEG;IACH,OAAO,IAAI,IAAI,IAAI,CAAC,aAAa,GAAG;QAAE,IAAI,EAAE,UAAU,CAAA;KAAE,CAAC;IAIzD;;OAEG;IACH,IAAI,OAAO,IAAI,IAAI,GAAG,YAAY,CAA0B;IAE5D;;OAEG;IACH,EAAE,IAAI,OAAO;IAIb;;OAEG;IACH,QAAQ,IAAI,IAAI;CAsBnB"}