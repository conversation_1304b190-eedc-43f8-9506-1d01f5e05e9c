"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("suspend-react"),u=require("../core/VideoTexture.cjs.js");function c(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var u=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,u.get?u:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}require("three"),require("@react-three/fiber"),require("hls.js");var n=c(e),a=i(r);const o=r.forwardRef((({constraints:e={audio:!1,video:{facingMode:"user"}},...c},i)=>{const o=t.suspend((()=>navigator.mediaDevices.getUserMedia(e)),[]);return r.useEffect((()=>()=>{null==o||o.getTracks().forEach((e=>e.stop())),t.clear([])}),[o]),a.createElement(u.VideoTexture,n.default({ref:i},c,{src:o}))}));exports.WebcamVideoTexture=o;
