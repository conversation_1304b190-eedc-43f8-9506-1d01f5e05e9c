/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { GlobalSpace } from '../spaces/XRSpace.js';
import { P_HAND_INPUT } from '../private.js';
import { XRHandJoint } from '../input/XRHand.js';
import { XRHandedness } from '../input/XRInputSource.js';
import { mat4 } from 'gl-matrix';
import { XRFrame } from '../frameloop/XRFrame.js';
import { XRTrackedInput } from './XRTrackedInput.js';
export interface HandPose {
    jointTransforms: {
        [joint in XRHandJoint]: {
            offsetMatrix: mat4;
            radius: number;
        };
    };
    gripOffsetMatrix?: mat4;
}
export interface XRHandInputConfig {
    profileId: string;
    fallbackProfileIds: string[];
    poses: {
        default: HandPose;
        pinch: HandPose;
        [poseId: string]: HandPose;
    };
}
export declare const oculusHandConfig: XRHandInputConfig;
export declare class XRHandInput extends XRTrackedInput {
    [P_HAND_INPUT]: {
        poseId: string;
        poses: {
            default: HandPose;
            pinch: HandPose;
            [poseId: string]: HandPose;
        };
    };
    constructor(handInputConfig: XRHandInputConfig, handedness: XRHandedness, globalSpace: GlobalSpace);
    get poseId(): string;
    set poseId(poseId: string);
    updateHandPose(): void;
    get pinchValue(): number;
    updatePinchValue(value: number): void;
    onFrameStart(frame: XRFrame): void;
}
//# sourceMappingURL=XRHandInput.d.ts.map