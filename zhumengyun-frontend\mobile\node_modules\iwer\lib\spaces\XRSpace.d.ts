/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { mat4, quat, vec3 } from 'gl-matrix';
import { P_SPACE } from '../private.js';
export declare class XRSpace extends EventTarget {
    [P_SPACE]: {
        parentSpace: XRSpace | undefined;
        offsetMatrix: mat4;
        emulated: boolean;
    };
    constructor(parentSpace?: XRSpace, offsetMatrix?: mat4);
}
export declare class GlobalSpace extends XRSpace {
    constructor();
}
export declare class XRSpaceUtils {
    static updateOffsetPosition(space: XRSpace, position: vec3): void;
    static updateOffsetQuaternion(space: XRSpace, quaternion: quat): void;
    static updateOffsetMatrix(space: XRSpace, matrix: mat4): void;
    static calculateGlobalOffsetMatrix(space: XRSpace, globalOffset?: mat4): mat4;
}
//# sourceMappingURL=XRSpace.d.ts.map