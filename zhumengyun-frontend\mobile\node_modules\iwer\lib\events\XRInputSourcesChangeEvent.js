/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
export class XRInputSourcesChangeEvent extends Event {
    constructor(type, eventInitDict) {
        super(type, eventInitDict);
        if (!eventInitDict.session) {
            throw new Error('XRInputSourcesChangeEventInit.session is required');
        }
        if (!eventInitDict.added) {
            throw new Error('XRInputSourcesChangeEventInit.added is required');
        }
        if (!eventInitDict.removed) {
            throw new Error('XRInputSourcesChangeEventInit.removed is required');
        }
        this.session = eventInitDict.session;
        this.added = eventInitDict.added;
        this.removed = eventInitDict.removed;
    }
}
//# sourceMappingURL=XRInputSourcesChangeEvent.js.map