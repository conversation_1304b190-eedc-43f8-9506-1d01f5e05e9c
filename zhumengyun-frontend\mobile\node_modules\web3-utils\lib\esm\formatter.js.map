{"version": 3, "file": "formatter.js", "sourceRoot": "", "sources": ["../../src/formatter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AACF,OAAO,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC7C,OAAO,EAGN,SAAS,EACT,UAAU,EAEV,qBAAqB,GACrB,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAc,KAAK,EAAyB,MAAM,gBAAgB,CAAC;AAC/F,OAAO,EAAE,iBAAiB,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AACvF,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AACzC,OAAO,EAAE,OAAO,EAAE,MAAM,0BAA0B,CAAC;AACnD,OAAO,EAAE,YAAY,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AAEjE,MAAM,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AAEhC,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,UAAmB,EAA4B,EAAE,CAC7E,OAAO,UAAU,KAAK,QAAQ;IAC9B,CAAC,SAAS,CAAC,UAAU,CAAC;IACtB,QAAQ,IAAI,UAAU;IACtB,OAAO,IAAI,UAAU,CAAC;AAEvB;;;;;;;;;GASG;AACH,MAAM,oBAAoB,GAAG,CAC5B,MAAkB,EAClB,QAAkB,EAClB,YAAgC,EAAE,EACT,EAAE;IAC3B,IAAI,MAAM,GAAe,kBAAK,MAAM,CAAgB,CAAC;IACrD,IAAI,gBAAoC,CAAC;IAEzC,KAAK,MAAM,QAAQ,IAAI,QAAQ,EAAE,CAAC;QACjC,IAAI,MAAM,CAAC,KAAK,IAAI,gBAAgB,EAAE,CAAC;YACtC,MAAM,eAAe,GAAG,gBAAgB,CAAC;YACzC,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,eAAe,CAAC,CAAC;YAChE,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,gBAAgB,EAAE,CAAC;gBAC1C,+GAA+G;gBAC/G,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC;QACF,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACzC,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACvB,MAAM,GAAI,MAAM,CAAC,UAAyC,CAAC,QAAQ,CAAC,CAAC;QACtE,CAAC;aAAM,IAAI,MAAM,CAAC,KAAK,IAAK,MAAM,CAAC,KAAoB,CAAC,UAAU,EAAE,CAAC;YACpE,MAAM,IAAI,GAAI,MAAM,CAAC,KAAoB,CAAC,UAAwC,CAAC;YAEnF,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzB,CAAC;aAAM,IAAI,MAAM,CAAC,KAAK,IAAI,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YACnD,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC;QACvB,CAAC;aAAM,IAAI,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YACxD,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,MAAM,IAAI,QAAQ;YAAE,gBAAgB,GAAG,QAAQ,CAAC;IACrD,CAAC;IAED,OAAO,MAAM,CAAC;AACf,CAAC,CAAC;AACF;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAAC,KAAc,EAAE,OAAe,EAAE,MAAkB,EAAE,EAAE;IACzF,IAAI,CAAC;QACJ,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;QAC1D,IAAI,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,MAAM,EAAE,CAAC;YAC/C,QAAQ,MAAM,CAAC,MAAM,EAAE,CAAC;gBACvB,KAAK,UAAU,CAAC,MAAM;oBACrB,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;gBAChC,KAAK,UAAU,CAAC,GAAG;oBAClB,OAAO,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;gBACrC,KAAK,UAAU,CAAC,GAAG;oBAClB,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACnC,KAAK,UAAU,CAAC,MAAM;oBACrB,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACxB;oBACC,MAAM,IAAI,cAAc,CAAC,mBAAmB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACvE,CAAC;QACF,CAAC;QACD,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YAC1B,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,EAAE,CAAC;gBAClB,IAAI,OAAO,KAAK,KAAK,QAAQ;oBAAE,WAAW,GAAG,OAAO,CAAC,KAAK,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;qBACzE,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC9B,WAAW,GAAG,gBAAgB,CAC7B,IAAI,UAAU,CAAC,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,EAC3C,KAAK,CACL,CAAC;gBACH,CAAC;YACF,CAAC;iBAAM,CAAC;gBACP,WAAW,GAAG,KAAK,CAAC;YACrB,CAAC;YACD,QAAQ,MAAM,CAAC,KAAK,EAAE,CAAC;gBACtB,KAAK,SAAS,CAAC,GAAG;oBACjB,OAAO,UAAU,CAAC,iBAAiB,CAAC,WAAoB,CAAC,CAAC,CAAC;gBAC5D,KAAK,SAAS,CAAC,UAAU;oBACxB,OAAO,iBAAiB,CAAC,WAAoB,CAAC,CAAC;gBAChD;oBACC,MAAM,IAAI,cAAc,CAAC,mBAAmB,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACtE,CAAC;QACF,CAAC;QAED,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC3B,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,mEAAmE;QACnE,yDAAyD;QACzD,OAAO,KAAK,CAAC;IACd,CAAC;IAED,OAAO,KAAK,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,YAAY,GAAG,CAAC,EACrB,KAAK,EACL,UAAU,EACV,MAAM,EACN,MAAM,EACN,GAAG,EACH,QAAQ,EACR,MAAM,EACN,SAAS,GAAG,EAAE,GAUd,EAAE,EAAE;;IACJ,uBAAuB;IACvB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1B,IAAI,WAAW,GAAG,UAAU,CAAC;QAE7B,wDAAwD;QACxD,kEAAkE;QAClE,2BAA2B;QAC3B,wDAAwD;QACxD,IAAI,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,KAAK,MAAK,SAAS,EAAE,CAAC;YACrC,0CAA0C;YAC1C,qDAAqD;YACrD,iEAAiE;YACjE,qEAAqE;YACrE,+BAA+B;YAC/B,yGAAyG;YACzG,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,eAA2B,EAAE,KAAa,EAAE,EAAE;;gBACvE,IACC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,KAAK,CAAC;oBACjC,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ;wBAC7B,CAAA,MAAC,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,KAAoB,0CAAE,IAAI,MAAK,QAAQ,CAAC;wBAC1D,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ;4BAC5B,CAAA,MAAC,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,KAAoB,0CAAE,IAAI,MAAK,QAAQ,CAAC,CAAC,EAC5D,CAAC;oBACF,WAAW,GAAG,eAAe,CAAC;oBAC9B,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;gBAC9B,CAAC;YACF,CAAC,CAAC,CAAC;QACJ,CAAC;QAED,IAAI,SAAS,CAAC,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,KAAK,CAAC,EAAE,CAAC;YACnC,uDAAuD;YACvD,6CAA6C;YAC7C,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;YACnB,QAAQ,CAAC,GAAG,EAAE,CAAC;YAEf,OAAO,IAAI,CAAC;QACb,CAAC;QAED,6CAA6C;QAC7C,IAAI,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YACzE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1C,6CAA6C;gBAC5C,MAAM,CAAC,GAAG,CAAe,CAAC,CAAC,CAAC,GAAG,kBAAkB,CACjD,KAAK,CAAC,CAAC,CAAC;gBACR,iEAAiE;gBACjE,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,KAAK,0CAAE,MAAM,EAC1B,MAAM,CACN,CAAC;YACH,CAAC;YAED,QAAQ,CAAC,GAAG,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACb,CAAC;QAED,yCAAyC;QACzC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,KAAK,CAAC,IAAI,CAAA,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,KAAK,0CAAE,IAAI,MAAK,QAAQ,EAAE,CAAC;YACjF,KAAK,MAAM,SAAS,IAAI,KAAK,EAAE,CAAC;gBAC/B,gDAAgD;gBAChD,OAAO,CACN,SAAgD,EAChD,MAAM,EACN,QAAQ,EACR,MAAM,EACN,SAAS,CACT,CAAC;YACH,CAAC;YAED,QAAQ,CAAC,GAAG,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACb,CAAC;QAED,iCAAiC;QACjC,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,KAAK,CAAC,EAAE,CAAC;YACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1C,6CAA6C;gBAC5C,MAAM,CAAC,GAAG,CAAe,CAAC,CAAC,CAAC,GAAG,kBAAkB,CACjD,KAAK,CAAC,CAAC,CAAC,EACR,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAgB,EACrC,MAAM,CACN,CAAC;YACH,CAAC;YAED,QAAQ,CAAC,GAAG,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACb,CAAC;IACF,CAAC;IACD,OAAO,KAAK,CAAC;AACd,CAAC,CAAC;AAEF;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,OAAO,GAAG,CACtB,IAAmD,EACnD,MAAkB,EAClB,QAAkB,EAClB,MAAkB,EAClB,YAAgC,EAAE,EACjC,EAAE;;IACH,yBAAyB;IACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QAC7C,OAAO,kBAAkB,CAAC,IAAI,EAAE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAgB,EAAE,MAAM,CAAC,CAAC;IACnE,CAAC;IAED,MAAM,MAAM,GAAG,IAA+B,CAAC;IAC/C,kDAAkD;IAClD,IACC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;QACrB,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,MAAK,OAAO;QACxB,CAAA,MAAC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAoB,0CAAE,IAAI,MAAK,QAAQ,EAC/C,CAAC;QACF,YAAY,CAAC;YACZ,KAAK,EAAE,MAAM;YACb,UAAU,EAAE,MAAM;YAClB,MAAM;YACN,MAAM;YACN,GAAG,EAAE,EAAE;YACP,QAAQ;YACR,MAAM;YACN,SAAS;SACT,CAAC,CAAC;IACJ,CAAC;SAAM,CAAC;QACP,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACnD,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACnB,IAAI,UAAU,GAAG,oBAAoB,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;YAEnE,6BAA6B;YAC7B,IAAI,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC3B,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;gBACnB,QAAQ,CAAC,GAAG,EAAE,CAAC;gBAEf,SAAS;YACV,CAAC;YAED,yCAAyC;YACzC,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACrB,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;gBACpD,QAAQ,CAAC,GAAG,EAAE,CAAC;gBACf,SAAS;YACV,CAAC;YAED,uBAAuB;YACvB,IACC,YAAY,CAAC;gBACZ,KAAK;gBACL,UAAU;gBACV,MAAM;gBACN,MAAM;gBACN,GAAG;gBACH,QAAQ;gBACR,MAAM;gBACN,SAAS;aACT,CAAC,EACD,CAAC;gBACF,SAAS;YACV,CAAC;YAED,0CAA0C;YAC1C,qDAAqD;YACrD,kFAAkF;YAClF,oEAAoE;YACpE,yGAAyG;YACzG,IAAI,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,MAAM,MAAK,SAAS,IAAI,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,KAAK,MAAK,SAAS,EAAE,CAAC;gBACzE,KAAK,MAAM,CAAC,MAAM,EAAE,eAAe,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;oBACpE,IAAI,CAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM,MAAK,SAAS,EAAE,CAAC;wBAC3C,UAAU,GAAG,eAAe,CAAC;wBAC7B,MAAM;oBACP,CAAC;gBACF,CAAC;YACF,CAAC;YAED,MAAM,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,KAAK,EAAE,UAAU,CAAC,MAAgB,EAAE,MAAM,CAAC,CAAC;YAE7E,QAAQ,CAAC,GAAG,EAAE,CAAC;QAChB,CAAC;IACF,CAAC;IAED,OAAO,MAAM,CAAC;AACf,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAM,CAAC,MAAM,MAAM,GAAG,CAIrB,MAA0C,EAC1C,IAAc,EACd,eAA2B,qBAAmC,EAC3B,EAAE;IACrC,IAAI,WAA0D,CAAC;IAE/D,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACpB,WAAW,GAAG,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QAChC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;IACzB,CAAC;SAAM,CAAC;QACP,WAAW,GAAG,IAAI,CAAC;IACpB,CAAC;IAED,mEAAmE;IACnE,MAAM,UAAU,GAAe,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;IAE5F,IAAI,CAAC,UAAU,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QACvE,MAAM,IAAI,cAAc,CAAC,oCAAoC,CAAC,CAAC;IAChE,CAAC;IAED,OAAO,OAAO,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,EAAE,YAAY,CAGvD,CAAC;AACH,CAAC,CAAC"}