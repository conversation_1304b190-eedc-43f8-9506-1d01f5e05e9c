import*as e from"../../../core/common/common.js";import*as t from"../../../core/platform/platform.js";import*as i from"../../../core/sdk/sdk.js";import*as n from"../models/models.js";import*as a from"../util/util.js";const r="captureSelectors";class s extends Event{static eventName="selectorpicked";data;constructor(e){super(s.eventName,{bubbles:!0,composed:!0}),this.data=e}}class o extends Event{static eventName="requestselectorattribute";send;constructor(e){super(o.eventName,{bubbles:!0,composed:!0}),this.send=e}}class c{static get#e(){return i.TargetManager.TargetManager.instance()}#t;#i;#n=new e.Mutex.Mutex;active=!1;constructor(e){this.#t=e}start=()=>this.#n.run((async()=>{this.active||(this.active=!0,this.#i=await new Promise(((e,t)=>{const i=setTimeout(t,1e3);this.#t.dispatchEvent(new o((t=>{clearTimeout(i),e(t)})))})),c.#e.observeTargets(this),this.#t.requestUpdate())}));stop=()=>this.#n.run((async()=>{this.active&&(this.active=!1,c.#e.unobserveTargets(this),c.#e.targets().map(this.targetRemoved.bind(this)),this.#i=void 0,this.#t.requestUpdate())}));toggle=()=>this.active?this.stop():this.start();#a=new Map;targetAdded(t){if(t.type()!==i.Target.Type.Frame)return;let n=this.#a.get(t);n||(n=new e.Mutex.Mutex,this.#a.set(t,n)),n.run((async()=>{await this.#r(t),await this.#s(t)}))}targetRemoved(e){const t=this.#a.get(e);t&&t.run((async()=>{try{await this.#o(e),await this.#c(e)}catch{}}))}#d=e=>{if(e.data.name!==r)return;const t=e.data.executionContextId,a=i.TargetManager.TargetManager.instance().targets(),o=n.SDKUtils.findTargetByExecutionContext(a,t),c=n.SDKUtils.findFrameIdByExecutionContext(a,t);if(!o||!c)throw new Error(`No execution context found for the binding call + ${JSON.stringify(e.data)}`);const d=o.model(i.ResourceTreeModel.ResourceTreeModel);if(!d)throw new Error(`ResourceTreeModel instance is missing for the target: ${o.id()}`);const l=d.frameForId(c);if(!l)throw new Error("Frame is not found");this.#t.dispatchEvent(new s({...JSON.parse(e.data.payload),...n.SDKUtils.getTargetFrameContext(o,l)})),this.stop()};#l=new Map;async#s(e){const t=`${await a.InjectedScript.get()};DevToolsRecorder.startSelectorPicker({getAccessibleName, getAccessibleRole}, ${JSON.stringify(this.#i?this.#i:void 0)}, ${a.isDebugBuild})`,[{identifier:i}]=await Promise.all([e.pageAgent().invoke_addScriptToEvaluateOnNewDocument({source:t,worldName:a.DEVTOOLS_RECORDER_WORLD_NAME,includeCommandLineAPI:!0}),n.SDKUtils.evaluateInAllFrames(a.DEVTOOLS_RECORDER_WORLD_NAME,e,t)]);this.#l.set(e,i)}async#o(e){const i=this.#l.get(e);t.assertNotNullOrUndefined(i),this.#l.delete(e),await e.pageAgent().invoke_removeScriptToEvaluateOnNewDocument({identifier:i});await n.SDKUtils.evaluateInAllFrames(a.DEVTOOLS_RECORDER_WORLD_NAME,e,"DevToolsRecorder.stopSelectorPicker()")}async#r(e){const n=e.model(i.RuntimeModel.RuntimeModel);t.assertNotNullOrUndefined(n),n.addEventListener(i.RuntimeModel.Events.BindingCalled,this.#d),await n.addBinding({name:r,executionContextName:a.DEVTOOLS_RECORDER_WORLD_NAME})}async#c(e){await e.runtimeAgent().invoke_removeBinding({name:r});const n=e.model(i.RuntimeModel.RuntimeModel);t.assertNotNullOrUndefined(n),n.removeEventListener(i.RuntimeModel.Events.BindingCalled,this.#d)}}var d=Object.freeze({__proto__:null,SelectorPickedEvent:s,RequestSelectorAttributeEvent:o,SelectorPicker:c});export{d as SelectorPicker};
