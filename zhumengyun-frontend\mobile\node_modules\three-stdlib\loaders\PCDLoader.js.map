{"version": 3, "file": "PCDLoader.js", "sources": ["../../src/loaders/PCDLoader.js"], "sourcesContent": ["import { BufferGeometry, FileLoader, Float32BufferAttribute, Loader, LoaderUtils, Points, PointsMaterial } from 'three'\nimport { decodeText } from '../_polyfill/LoaderUtils'\n\nclass PCDLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.littleEndian = true\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (data) {\n        try {\n          onLoad(scope.parse(data, url))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(data, url) {\n    // from https://gitlab.com/taketwo/three-pcd-loader/blob/master/decompress-lzf.js\n\n    function decompressLZF(inData, outLength) {\n      const inLength = inData.length\n      const outData = new Uint8Array(outLength)\n      let inPtr = 0\n      let outPtr = 0\n      let ctrl\n      let len\n      let ref\n      do {\n        ctrl = inData[inPtr++]\n        if (ctrl < 1 << 5) {\n          ctrl++\n          if (outPtr + ctrl > outLength) throw new Error('Output buffer is not large enough')\n          if (inPtr + ctrl > inLength) throw new Error('Invalid compressed data')\n          do {\n            outData[outPtr++] = inData[inPtr++]\n          } while (--ctrl)\n        } else {\n          len = ctrl >> 5\n          ref = outPtr - ((ctrl & 0x1f) << 8) - 1\n          if (inPtr >= inLength) throw new Error('Invalid compressed data')\n          if (len === 7) {\n            len += inData[inPtr++]\n            if (inPtr >= inLength) throw new Error('Invalid compressed data')\n          }\n\n          ref -= inData[inPtr++]\n          if (outPtr + len + 2 > outLength) throw new Error('Output buffer is not large enough')\n          if (ref < 0) throw new Error('Invalid compressed data')\n          if (ref >= outPtr) throw new Error('Invalid compressed data')\n          do {\n            outData[outPtr++] = outData[ref++]\n          } while (--len + 2)\n        }\n      } while (inPtr < inLength)\n\n      return outData\n    }\n\n    function parseHeader(data) {\n      const PCDheader = {}\n      const result1 = data.search(/[\\r\\n]DATA\\s(\\S*)\\s/i)\n      const result2 = /[\\r\\n]DATA\\s(\\S*)\\s/i.exec(data.substr(result1 - 1))\n\n      PCDheader.data = result2[1]\n      PCDheader.headerLen = result2[0].length + result1\n      PCDheader.str = data.substr(0, PCDheader.headerLen)\n\n      // remove comments\n\n      PCDheader.str = PCDheader.str.replace(/\\#.*/gi, '')\n\n      // parse\n\n      PCDheader.version = /VERSION (.*)/i.exec(PCDheader.str)\n      PCDheader.fields = /FIELDS (.*)/i.exec(PCDheader.str)\n      PCDheader.size = /SIZE (.*)/i.exec(PCDheader.str)\n      PCDheader.type = /TYPE (.*)/i.exec(PCDheader.str)\n      PCDheader.count = /COUNT (.*)/i.exec(PCDheader.str)\n      PCDheader.width = /WIDTH (.*)/i.exec(PCDheader.str)\n      PCDheader.height = /HEIGHT (.*)/i.exec(PCDheader.str)\n      PCDheader.viewpoint = /VIEWPOINT (.*)/i.exec(PCDheader.str)\n      PCDheader.points = /POINTS (.*)/i.exec(PCDheader.str)\n\n      // evaluate\n\n      if (PCDheader.version !== null) PCDheader.version = parseFloat(PCDheader.version[1])\n\n      if (PCDheader.fields !== null) PCDheader.fields = PCDheader.fields[1].split(' ')\n\n      if (PCDheader.type !== null) PCDheader.type = PCDheader.type[1].split(' ')\n\n      if (PCDheader.width !== null) PCDheader.width = parseInt(PCDheader.width[1])\n\n      if (PCDheader.height !== null) PCDheader.height = parseInt(PCDheader.height[1])\n\n      if (PCDheader.viewpoint !== null) PCDheader.viewpoint = PCDheader.viewpoint[1]\n\n      if (PCDheader.points !== null) PCDheader.points = parseInt(PCDheader.points[1], 10)\n\n      if (PCDheader.points === null) PCDheader.points = PCDheader.width * PCDheader.height\n\n      if (PCDheader.size !== null) {\n        PCDheader.size = PCDheader.size[1].split(' ').map(function (x) {\n          return parseInt(x, 10)\n        })\n      }\n\n      if (PCDheader.count !== null) {\n        PCDheader.count = PCDheader.count[1].split(' ').map(function (x) {\n          return parseInt(x, 10)\n        })\n      } else {\n        PCDheader.count = []\n\n        for (let i = 0, l = PCDheader.fields.length; i < l; i++) {\n          PCDheader.count.push(1)\n        }\n      }\n\n      PCDheader.offset = {}\n\n      let sizeSum = 0\n\n      for (let i = 0, l = PCDheader.fields.length; i < l; i++) {\n        if (PCDheader.data === 'ascii') {\n          PCDheader.offset[PCDheader.fields[i]] = i\n        } else {\n          PCDheader.offset[PCDheader.fields[i]] = sizeSum\n          sizeSum += PCDheader.size[i] * PCDheader.count[i]\n        }\n      }\n\n      // for binary only\n\n      PCDheader.rowSize = sizeSum\n\n      return PCDheader\n    }\n\n    const textData = decodeText(new Uint8Array(data))\n\n    // parse header (always ascii format)\n\n    const PCDheader = parseHeader(textData)\n\n    // parse data\n\n    const position = []\n    const normal = []\n    const color = []\n\n    // ascii\n\n    if (PCDheader.data === 'ascii') {\n      const offset = PCDheader.offset\n      const pcdData = textData.substr(PCDheader.headerLen)\n      const lines = pcdData.split('\\n')\n\n      for (let i = 0, l = lines.length; i < l; i++) {\n        if (lines[i] === '') continue\n\n        const line = lines[i].split(' ')\n\n        if (offset.x !== undefined) {\n          position.push(parseFloat(line[offset.x]))\n          position.push(parseFloat(line[offset.y]))\n          position.push(parseFloat(line[offset.z]))\n        }\n\n        if (offset.rgb !== undefined) {\n          const rgb = parseFloat(line[offset.rgb])\n          const r = (rgb >> 16) & 0x0000ff\n          const g = (rgb >> 8) & 0x0000ff\n          const b = (rgb >> 0) & 0x0000ff\n          color.push(r / 255, g / 255, b / 255)\n        }\n\n        if (offset.normal_x !== undefined) {\n          normal.push(parseFloat(line[offset.normal_x]))\n          normal.push(parseFloat(line[offset.normal_y]))\n          normal.push(parseFloat(line[offset.normal_z]))\n        }\n      }\n    }\n\n    // binary-compressed\n\n    // normally data in PCD files are organized as array of structures: XYZRGBXYZRGB\n    // binary compressed PCD files organize their data as structure of arrays: XXYYZZRGBRGB\n    // that requires a totally different parsing approach compared to non-compressed data\n\n    if (PCDheader.data === 'binary_compressed') {\n      const sizes = new Uint32Array(data.slice(PCDheader.headerLen, PCDheader.headerLen + 8))\n      const compressedSize = sizes[0]\n      const decompressedSize = sizes[1]\n      const decompressed = decompressLZF(\n        new Uint8Array(data, PCDheader.headerLen + 8, compressedSize),\n        decompressedSize,\n      )\n      const dataview = new DataView(decompressed.buffer)\n\n      const offset = PCDheader.offset\n\n      for (let i = 0; i < PCDheader.points; i++) {\n        if (offset.x !== undefined) {\n          position.push(dataview.getFloat32(PCDheader.points * offset.x + PCDheader.size[0] * i, this.littleEndian))\n          position.push(dataview.getFloat32(PCDheader.points * offset.y + PCDheader.size[1] * i, this.littleEndian))\n          position.push(dataview.getFloat32(PCDheader.points * offset.z + PCDheader.size[2] * i, this.littleEndian))\n        }\n\n        if (offset.rgb !== undefined) {\n          color.push(dataview.getUint8(PCDheader.points * offset.rgb + PCDheader.size[3] * i + 2) / 255.0)\n          color.push(dataview.getUint8(PCDheader.points * offset.rgb + PCDheader.size[3] * i + 1) / 255.0)\n          color.push(dataview.getUint8(PCDheader.points * offset.rgb + PCDheader.size[3] * i + 0) / 255.0)\n        }\n\n        if (offset.normal_x !== undefined) {\n          normal.push(\n            dataview.getFloat32(PCDheader.points * offset.normal_x + PCDheader.size[4] * i, this.littleEndian),\n          )\n          normal.push(\n            dataview.getFloat32(PCDheader.points * offset.normal_y + PCDheader.size[5] * i, this.littleEndian),\n          )\n          normal.push(\n            dataview.getFloat32(PCDheader.points * offset.normal_z + PCDheader.size[6] * i, this.littleEndian),\n          )\n        }\n      }\n    }\n\n    // binary\n\n    if (PCDheader.data === 'binary') {\n      const dataview = new DataView(data, PCDheader.headerLen)\n      const offset = PCDheader.offset\n\n      for (let i = 0, row = 0; i < PCDheader.points; i++, row += PCDheader.rowSize) {\n        if (offset.x !== undefined) {\n          position.push(dataview.getFloat32(row + offset.x, this.littleEndian))\n          position.push(dataview.getFloat32(row + offset.y, this.littleEndian))\n          position.push(dataview.getFloat32(row + offset.z, this.littleEndian))\n        }\n\n        if (offset.rgb !== undefined) {\n          color.push(dataview.getUint8(row + offset.rgb + 2) / 255.0)\n          color.push(dataview.getUint8(row + offset.rgb + 1) / 255.0)\n          color.push(dataview.getUint8(row + offset.rgb + 0) / 255.0)\n        }\n\n        if (offset.normal_x !== undefined) {\n          normal.push(dataview.getFloat32(row + offset.normal_x, this.littleEndian))\n          normal.push(dataview.getFloat32(row + offset.normal_y, this.littleEndian))\n          normal.push(dataview.getFloat32(row + offset.normal_z, this.littleEndian))\n        }\n      }\n    }\n\n    // build geometry\n\n    const geometry = new BufferGeometry()\n\n    if (position.length > 0) geometry.setAttribute('position', new Float32BufferAttribute(position, 3))\n    if (normal.length > 0) geometry.setAttribute('normal', new Float32BufferAttribute(normal, 3))\n    if (color.length > 0) geometry.setAttribute('color', new Float32BufferAttribute(color, 3))\n\n    geometry.computeBoundingSphere()\n\n    // build material\n\n    const material = new PointsMaterial({ size: 0.005 })\n\n    if (color.length > 0) {\n      material.vertexColors = true\n    } else {\n      material.color.setHex(Math.random() * 0xffffff)\n    }\n\n    // build point cloud\n\n    const mesh = new Points(geometry, material)\n    let name = url.split('').reverse().join('')\n    name = /([^\\/]*)/.exec(name)\n    name = name[1].split('').reverse().join('')\n    mesh.name = name\n\n    return mesh\n  }\n}\n\nexport { PCDLoader }\n"], "names": ["data", "PCD<PERSON>er"], "mappings": ";;AAGA,MAAM,kBAAkB,OAAO;AAAA,EAC7B,YAAY,SAAS;AACnB,UAAM,OAAO;AAEb,SAAK,eAAe;AAAA,EACrB;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AAEd,UAAM,SAAS,IAAI,WAAW,MAAM,OAAO;AAC3C,WAAO,QAAQ,MAAM,IAAI;AACzB,WAAO,gBAAgB,aAAa;AACpC,WAAO,iBAAiB,MAAM,aAAa;AAC3C,WAAO,mBAAmB,MAAM,eAAe;AAC/C,WAAO;AAAA,MACL;AAAA,MACA,SAAU,MAAM;AACd,YAAI;AACF,iBAAO,MAAM,MAAM,MAAM,GAAG,CAAC;AAAA,QAC9B,SAAQ,GAAP;AACA,cAAI,SAAS;AACX,oBAAQ,CAAC;AAAA,UACrB,OAAiB;AACL,oBAAQ,MAAM,CAAC;AAAA,UAChB;AAED,gBAAM,QAAQ,UAAU,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,MAAM,MAAM,KAAK;AAGf,aAAS,cAAc,QAAQ,WAAW;AACxC,YAAM,WAAW,OAAO;AACxB,YAAM,UAAU,IAAI,WAAW,SAAS;AACxC,UAAI,QAAQ;AACZ,UAAI,SAAS;AACb,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,SAAG;AACD,eAAO,OAAO,OAAO;AACrB,YAAI,OAAO,KAAK,GAAG;AACjB;AACA,cAAI,SAAS,OAAO;AAAW,kBAAM,IAAI,MAAM,mCAAmC;AAClF,cAAI,QAAQ,OAAO;AAAU,kBAAM,IAAI,MAAM,yBAAyB;AACtE,aAAG;AACD,oBAAQ,QAAQ,IAAI,OAAO,OAAO;AAAA,UACnC,SAAQ,EAAE;AAAA,QACrB,OAAe;AACL,gBAAM,QAAQ;AACd,gBAAM,WAAW,OAAO,OAAS,KAAK;AACtC,cAAI,SAAS;AAAU,kBAAM,IAAI,MAAM,yBAAyB;AAChE,cAAI,QAAQ,GAAG;AACb,mBAAO,OAAO,OAAO;AACrB,gBAAI,SAAS;AAAU,oBAAM,IAAI,MAAM,yBAAyB;AAAA,UACjE;AAED,iBAAO,OAAO,OAAO;AACrB,cAAI,SAAS,MAAM,IAAI;AAAW,kBAAM,IAAI,MAAM,mCAAmC;AACrF,cAAI,MAAM;AAAG,kBAAM,IAAI,MAAM,yBAAyB;AACtD,cAAI,OAAO;AAAQ,kBAAM,IAAI,MAAM,yBAAyB;AAC5D,aAAG;AACD,oBAAQ,QAAQ,IAAI,QAAQ,KAAK;AAAA,UAC7C,SAAmB,EAAE,MAAM;AAAA,QAClB;AAAA,MACT,SAAe,QAAQ;AAEjB,aAAO;AAAA,IACR;AAED,aAAS,YAAYA,OAAM;AACzB,YAAMC,aAAY,CAAE;AACpB,YAAM,UAAUD,MAAK,OAAO,sBAAsB;AAClD,YAAM,UAAU,uBAAuB,KAAKA,MAAK,OAAO,UAAU,CAAC,CAAC;AAEpE,MAAAC,WAAU,OAAO,QAAQ,CAAC;AAC1B,MAAAA,WAAU,YAAY,QAAQ,CAAC,EAAE,SAAS;AAC1C,MAAAA,WAAU,MAAMD,MAAK,OAAO,GAAGC,WAAU,SAAS;AAIlD,MAAAA,WAAU,MAAMA,WAAU,IAAI,QAAQ,UAAU,EAAE;AAIlD,MAAAA,WAAU,UAAU,gBAAgB,KAAKA,WAAU,GAAG;AACtD,MAAAA,WAAU,SAAS,eAAe,KAAKA,WAAU,GAAG;AACpD,MAAAA,WAAU,OAAO,aAAa,KAAKA,WAAU,GAAG;AAChD,MAAAA,WAAU,OAAO,aAAa,KAAKA,WAAU,GAAG;AAChD,MAAAA,WAAU,QAAQ,cAAc,KAAKA,WAAU,GAAG;AAClD,MAAAA,WAAU,QAAQ,cAAc,KAAKA,WAAU,GAAG;AAClD,MAAAA,WAAU,SAAS,eAAe,KAAKA,WAAU,GAAG;AACpD,MAAAA,WAAU,YAAY,kBAAkB,KAAKA,WAAU,GAAG;AAC1D,MAAAA,WAAU,SAAS,eAAe,KAAKA,WAAU,GAAG;AAIpD,UAAIA,WAAU,YAAY;AAAM,QAAAA,WAAU,UAAU,WAAWA,WAAU,QAAQ,CAAC,CAAC;AAEnF,UAAIA,WAAU,WAAW;AAAM,QAAAA,WAAU,SAASA,WAAU,OAAO,CAAC,EAAE,MAAM,GAAG;AAE/E,UAAIA,WAAU,SAAS;AAAM,QAAAA,WAAU,OAAOA,WAAU,KAAK,CAAC,EAAE,MAAM,GAAG;AAEzE,UAAIA,WAAU,UAAU;AAAM,QAAAA,WAAU,QAAQ,SAASA,WAAU,MAAM,CAAC,CAAC;AAE3E,UAAIA,WAAU,WAAW;AAAM,QAAAA,WAAU,SAAS,SAASA,WAAU,OAAO,CAAC,CAAC;AAE9E,UAAIA,WAAU,cAAc;AAAM,QAAAA,WAAU,YAAYA,WAAU,UAAU,CAAC;AAE7E,UAAIA,WAAU,WAAW;AAAM,QAAAA,WAAU,SAAS,SAASA,WAAU,OAAO,CAAC,GAAG,EAAE;AAElF,UAAIA,WAAU,WAAW;AAAM,QAAAA,WAAU,SAASA,WAAU,QAAQA,WAAU;AAE9E,UAAIA,WAAU,SAAS,MAAM;AAC3B,QAAAA,WAAU,OAAOA,WAAU,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI,SAAU,GAAG;AAC7D,iBAAO,SAAS,GAAG,EAAE;AAAA,QAC/B,CAAS;AAAA,MACF;AAED,UAAIA,WAAU,UAAU,MAAM;AAC5B,QAAAA,WAAU,QAAQA,WAAU,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI,SAAU,GAAG;AAC/D,iBAAO,SAAS,GAAG,EAAE;AAAA,QAC/B,CAAS;AAAA,MACT,OAAa;AACL,QAAAA,WAAU,QAAQ,CAAE;AAEpB,iBAAS,IAAI,GAAG,IAAIA,WAAU,OAAO,QAAQ,IAAI,GAAG,KAAK;AACvD,UAAAA,WAAU,MAAM,KAAK,CAAC;AAAA,QACvB;AAAA,MACF;AAED,MAAAA,WAAU,SAAS,CAAE;AAErB,UAAI,UAAU;AAEd,eAAS,IAAI,GAAG,IAAIA,WAAU,OAAO,QAAQ,IAAI,GAAG,KAAK;AACvD,YAAIA,WAAU,SAAS,SAAS;AAC9B,UAAAA,WAAU,OAAOA,WAAU,OAAO,CAAC,CAAC,IAAI;AAAA,QAClD,OAAe;AACL,UAAAA,WAAU,OAAOA,WAAU,OAAO,CAAC,CAAC,IAAI;AACxC,qBAAWA,WAAU,KAAK,CAAC,IAAIA,WAAU,MAAM,CAAC;AAAA,QACjD;AAAA,MACF;AAID,MAAAA,WAAU,UAAU;AAEpB,aAAOA;AAAA,IACR;AAED,UAAM,WAAW,WAAW,IAAI,WAAW,IAAI,CAAC;AAIhD,UAAM,YAAY,YAAY,QAAQ;AAItC,UAAM,WAAW,CAAE;AACnB,UAAM,SAAS,CAAE;AACjB,UAAM,QAAQ,CAAE;AAIhB,QAAI,UAAU,SAAS,SAAS;AAC9B,YAAM,SAAS,UAAU;AACzB,YAAM,UAAU,SAAS,OAAO,UAAU,SAAS;AACnD,YAAM,QAAQ,QAAQ,MAAM,IAAI;AAEhC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,YAAI,MAAM,CAAC,MAAM;AAAI;AAErB,cAAM,OAAO,MAAM,CAAC,EAAE,MAAM,GAAG;AAE/B,YAAI,OAAO,MAAM,QAAW;AAC1B,mBAAS,KAAK,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC;AACxC,mBAAS,KAAK,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC;AACxC,mBAAS,KAAK,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC;AAAA,QACzC;AAED,YAAI,OAAO,QAAQ,QAAW;AAC5B,gBAAM,MAAM,WAAW,KAAK,OAAO,GAAG,CAAC;AACvC,gBAAM,IAAK,OAAO,KAAM;AACxB,gBAAM,IAAK,OAAO,IAAK;AACvB,gBAAM,IAAK,OAAO,IAAK;AACvB,gBAAM,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,QACrC;AAED,YAAI,OAAO,aAAa,QAAW;AACjC,iBAAO,KAAK,WAAW,KAAK,OAAO,QAAQ,CAAC,CAAC;AAC7C,iBAAO,KAAK,WAAW,KAAK,OAAO,QAAQ,CAAC,CAAC;AAC7C,iBAAO,KAAK,WAAW,KAAK,OAAO,QAAQ,CAAC,CAAC;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAQD,QAAI,UAAU,SAAS,qBAAqB;AAC1C,YAAM,QAAQ,IAAI,YAAY,KAAK,MAAM,UAAU,WAAW,UAAU,YAAY,CAAC,CAAC;AACtF,YAAM,iBAAiB,MAAM,CAAC;AAC9B,YAAM,mBAAmB,MAAM,CAAC;AAChC,YAAM,eAAe;AAAA,QACnB,IAAI,WAAW,MAAM,UAAU,YAAY,GAAG,cAAc;AAAA,QAC5D;AAAA,MACD;AACD,YAAM,WAAW,IAAI,SAAS,aAAa,MAAM;AAEjD,YAAM,SAAS,UAAU;AAEzB,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,YAAI,OAAO,MAAM,QAAW;AAC1B,mBAAS,KAAK,SAAS,WAAW,UAAU,SAAS,OAAO,IAAI,UAAU,KAAK,CAAC,IAAI,GAAG,KAAK,YAAY,CAAC;AACzG,mBAAS,KAAK,SAAS,WAAW,UAAU,SAAS,OAAO,IAAI,UAAU,KAAK,CAAC,IAAI,GAAG,KAAK,YAAY,CAAC;AACzG,mBAAS,KAAK,SAAS,WAAW,UAAU,SAAS,OAAO,IAAI,UAAU,KAAK,CAAC,IAAI,GAAG,KAAK,YAAY,CAAC;AAAA,QAC1G;AAED,YAAI,OAAO,QAAQ,QAAW;AAC5B,gBAAM,KAAK,SAAS,SAAS,UAAU,SAAS,OAAO,MAAM,UAAU,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,GAAK;AAC/F,gBAAM,KAAK,SAAS,SAAS,UAAU,SAAS,OAAO,MAAM,UAAU,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,GAAK;AAC/F,gBAAM,KAAK,SAAS,SAAS,UAAU,SAAS,OAAO,MAAM,UAAU,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,GAAK;AAAA,QAChG;AAED,YAAI,OAAO,aAAa,QAAW;AACjC,iBAAO;AAAA,YACL,SAAS,WAAW,UAAU,SAAS,OAAO,WAAW,UAAU,KAAK,CAAC,IAAI,GAAG,KAAK,YAAY;AAAA,UAClG;AACD,iBAAO;AAAA,YACL,SAAS,WAAW,UAAU,SAAS,OAAO,WAAW,UAAU,KAAK,CAAC,IAAI,GAAG,KAAK,YAAY;AAAA,UAClG;AACD,iBAAO;AAAA,YACL,SAAS,WAAW,UAAU,SAAS,OAAO,WAAW,UAAU,KAAK,CAAC,IAAI,GAAG,KAAK,YAAY;AAAA,UAClG;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAID,QAAI,UAAU,SAAS,UAAU;AAC/B,YAAM,WAAW,IAAI,SAAS,MAAM,UAAU,SAAS;AACvD,YAAM,SAAS,UAAU;AAEzB,eAAS,IAAI,GAAG,MAAM,GAAG,IAAI,UAAU,QAAQ,KAAK,OAAO,UAAU,SAAS;AAC5E,YAAI,OAAO,MAAM,QAAW;AAC1B,mBAAS,KAAK,SAAS,WAAW,MAAM,OAAO,GAAG,KAAK,YAAY,CAAC;AACpE,mBAAS,KAAK,SAAS,WAAW,MAAM,OAAO,GAAG,KAAK,YAAY,CAAC;AACpE,mBAAS,KAAK,SAAS,WAAW,MAAM,OAAO,GAAG,KAAK,YAAY,CAAC;AAAA,QACrE;AAED,YAAI,OAAO,QAAQ,QAAW;AAC5B,gBAAM,KAAK,SAAS,SAAS,MAAM,OAAO,MAAM,CAAC,IAAI,GAAK;AAC1D,gBAAM,KAAK,SAAS,SAAS,MAAM,OAAO,MAAM,CAAC,IAAI,GAAK;AAC1D,gBAAM,KAAK,SAAS,SAAS,MAAM,OAAO,MAAM,CAAC,IAAI,GAAK;AAAA,QAC3D;AAED,YAAI,OAAO,aAAa,QAAW;AACjC,iBAAO,KAAK,SAAS,WAAW,MAAM,OAAO,UAAU,KAAK,YAAY,CAAC;AACzE,iBAAO,KAAK,SAAS,WAAW,MAAM,OAAO,UAAU,KAAK,YAAY,CAAC;AACzE,iBAAO,KAAK,SAAS,WAAW,MAAM,OAAO,UAAU,KAAK,YAAY,CAAC;AAAA,QAC1E;AAAA,MACF;AAAA,IACF;AAID,UAAM,WAAW,IAAI,eAAgB;AAErC,QAAI,SAAS,SAAS;AAAG,eAAS,aAAa,YAAY,IAAI,uBAAuB,UAAU,CAAC,CAAC;AAClG,QAAI,OAAO,SAAS;AAAG,eAAS,aAAa,UAAU,IAAI,uBAAuB,QAAQ,CAAC,CAAC;AAC5F,QAAI,MAAM,SAAS;AAAG,eAAS,aAAa,SAAS,IAAI,uBAAuB,OAAO,CAAC,CAAC;AAEzF,aAAS,sBAAuB;AAIhC,UAAM,WAAW,IAAI,eAAe,EAAE,MAAM,KAAK,CAAE;AAEnD,QAAI,MAAM,SAAS,GAAG;AACpB,eAAS,eAAe;AAAA,IAC9B,OAAW;AACL,eAAS,MAAM,OAAO,KAAK,OAAM,IAAK,QAAQ;AAAA,IAC/C;AAID,UAAM,OAAO,IAAI,OAAO,UAAU,QAAQ;AAC1C,QAAI,OAAO,IAAI,MAAM,EAAE,EAAE,QAAS,EAAC,KAAK,EAAE;AAC1C,WAAO,WAAW,KAAK,IAAI;AAC3B,WAAO,KAAK,CAAC,EAAE,MAAM,EAAE,EAAE,QAAO,EAAG,KAAK,EAAE;AAC1C,SAAK,OAAO;AAEZ,WAAO;AAAA,EACR;AACH;"}