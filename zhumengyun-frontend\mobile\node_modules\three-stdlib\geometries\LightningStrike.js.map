{"version": 3, "file": "LightningStrike.js", "sources": ["../../src/geometries/LightningStrike.js"], "sourcesContent": ["import {\n  BufferGeometry,\n  DynamicDrawUsage,\n  Float32BufferAttribute,\n  MathUtils,\n  Uint32BufferAttribute,\n  Vector3,\n} from 'three'\nimport { SimplexNoise } from '../math/SimplexNoise'\n\n/**\n * @fileoverview LightningStrike object for creating lightning strikes and voltaic arcs.\n *\n *\n * Usage\n *\n * var myRay = new LightningStrike( paramsObject );\n * var myRayMesh = new THREE.Mesh( myRay, myMaterial );\n * scene.add( myRayMesh );\n * ...\n * myRay.update( currentTime );\n *\n * The \"currentTime\" can vary its rate, go forwards, backwards or even jump, but it cannot be negative.\n *\n * You should normally leave the ray position to (0, 0, 0). You should control it by changing the sourceOffset and destOffset parameters.\n *\n *\n * LightningStrike parameters\n *\n * The paramsObject can contain any of the following parameters.\n *\n * Legend:\n * 'LightningStrike' (also called 'ray'): An independent voltaic arc with its ramifications and defined with a set of parameters.\n * 'Subray': A ramification of the ray. It is not a LightningStrike object.\n * 'Segment': A linear segment piece of a subray.\n * 'Leaf segment': A ray segment which cannot be smaller.\n *\n *\n * The following parameters can be changed any time and if they vary smoothly, the ray form will also change smoothly:\n *\n * @param {Vector3} sourceOffset The point where the ray starts.\n *\n * @param {Vector3} destOffset The point where the ray ends.\n *\n * @param {double} timeScale The rate at wich the ray form changes in time. Default: 1\n *\n * @param {double} roughness From 0 to 1. The higher the value, the more wrinkled is the ray. Default: 0.9\n *\n * @param {double} straightness From 0 to 1. The higher the value, the more straight will be a subray path. Default: 0.7\n *\n * @param {Vector3} up0 Ray 'up' direction at the ray starting point. Must be normalized. It should be perpendicular to the ray forward direction but it doesn't matter much.\n *\n * @param {Vector3} up1 Like the up0 parameter but at the end of the ray. Must be normalized.\n *\n * @param {double} radius0 Radius of the main ray trunk at the start point. Default: 1\n *\n * @param {double} radius1 Radius of the main ray trunk at the end point. Default: 1\n *\n * @param {double} radius0Factor The radius0 of a subray is this factor times the radius0 of its parent subray. Default: 0.5\n *\n * @param {double} radius1Factor The radius1 of a subray is this factor times the radius1 of its parent subray. Default: 0.2\n *\n * @param {minRadius} Minimum value a subray radius0 or radius1 can get. Default: 0.1\n *\n *\n * The following parameters should not be changed after lightning creation. They can be changed but the ray will change its form abruptly:\n *\n * @param {boolean} isEternal If true the ray never extinguishes. Otherwise its life is controlled by the 'birthTime' and 'deathTime' parameters. Default: true if any of those two parameters is undefined.\n *\n * @param {double} birthTime The time at which the ray starts its life and begins propagating. Only if isEternal is false. Default: None.\n *\n * @param {double} deathTime The time at which the ray ends vanishing and its life. Only if isEternal is false. Default: None.\n *\n * @param {double} propagationTimeFactor From 0 to 1. Lifetime factor at which the ray ends propagating and enters the steady phase. For example, 0.1 means it is propagating 1/10 of its lifetime. Default: 0.1\n *\n * @param {double} vanishingTimeFactor From 0 to 1. Lifetime factor at which the ray ends the steady phase and begins vanishing. For example, 0.9 means it is vanishing 1/10 of its lifetime. Default: 0.9\n *\n * @param {double} subrayPeriod Subrays cycle periodically. This is their time period. Default: 4\n *\n * @param {double} subrayDutyCycle From 0 to 1. This is the fraction of time a subray is active. Default: 0.6\n *\n *\n * These parameters cannot change after lightning creation:\n *\n * @param {integer} maxIterations: Greater than 0. The number of ray's leaf segments is 2**maxIterations. Default: 9\n *\n * @param {boolean} isStatic Set to true only for rays which won't change over time and are not attached to moving objects (Rare case). It is used to set the vertex buffers non-dynamic. You can omit calling update() for these rays.\n *\n * @param {integer} ramification Greater than 0. Maximum number of child subrays a subray can have. Default: 5\n *\n * @param {integer} maxSubrayRecursion Greater than 0. Maximum level of recursion (subray descendant generations). Default: 3\n *\n * @param {double} recursionProbability From 0 to 1. The lower the value, the less chance each new generation of subrays has to generate new subrays. Default: 0.6\n *\n * @param {boolean} generateUVs If true, the ray geometry will have uv coordinates generated. u runs along the ray, and v across its perimeter. Default: false.\n *\n * @param {Object} randomGenerator Set here your random number generator which will seed the SimplexNoise and other decisions during ray tree creation.\n * It can be used to generate repeatable rays. For that, set also the noiseSeed parameter, and each ray created with that generator and seed pair will be identical in time.\n * The randomGenerator parameter should be an object with a random() function similar to Math.random, but seedable.\n * It must have also a getSeed() method, which returns the current seed, and a setSeed( seed ) method, which accepts as seed a fractional number from 0 to 1, as well as any other number.\n * The default value is an internal generator for some uses and Math.random for others (It is non-repeatable even if noiseSeed is supplied)\n *\n * @param {double} noiseSeed Seed used to make repeatable rays (see the randomGenerator)\n *\n * @param {function} onDecideSubrayCreation Set this to change the callback which decides subray creation. You can look at the default callback in the code (createDefaultSubrayCreationCallbacks)for more info.\n *\n * @param {function} onSubrayCreation This is another callback, more simple than the previous one. It can be used to adapt the form of subrays or other parameters once a subray has been created and initialized. It is used in the examples to adapt subrays to a sphere or to a plane.\n *\n *\n */\n\nconst LightningStrike = /* @__PURE__ */ (() => {\n  class LightningStrike extends BufferGeometry {\n    // Ray states\n    static RAY_INITIALIZED = 0\n    static RAY_UNBORN = 1\n    static RAY_PROPAGATING = 2\n    static RAY_STEADY = 3\n    static RAY_VANISHING = 4\n    static RAY_EXTINGUISHED = 5\n\n    static COS30DEG = Math.cos((30 * Math.PI) / 180)\n    static SIN30DEG = Math.sin((30 * Math.PI) / 180)\n\n    constructor(rayParameters = {}) {\n      super()\n\n      this.isLightningStrike = true\n\n      this.type = 'LightningStrike'\n\n      // Set parameters, and set undefined parameters to default values\n      this.init(LightningStrike.copyParameters(rayParameters, rayParameters))\n\n      // Creates and populates the mesh\n      this.createMesh()\n    }\n\n    static createRandomGenerator() {\n      const numSeeds = 2053\n      const seeds = []\n\n      for (let i = 0; i < numSeeds; i++) {\n        seeds.push(Math.random())\n      }\n\n      const generator = {\n        currentSeed: 0,\n\n        random: function () {\n          const value = seeds[generator.currentSeed]\n\n          generator.currentSeed = (generator.currentSeed + 1) % numSeeds\n\n          return value\n        },\n\n        getSeed: function () {\n          return generator.currentSeed / numSeeds\n        },\n\n        setSeed: function (seed) {\n          generator.currentSeed = Math.floor(seed * numSeeds) % numSeeds\n        },\n      }\n\n      return generator\n    }\n\n    static copyParameters(dest = {}, source = {}) {\n      const vecCopy = function (v) {\n        if (source === dest) {\n          return v\n        } else {\n          return v.clone()\n        }\n      }\n\n      ;(dest.sourceOffset = source.sourceOffset !== undefined ? vecCopy(source.sourceOffset) : new Vector3(0, 100, 0)),\n        (dest.destOffset = source.destOffset !== undefined ? vecCopy(source.destOffset) : new Vector3(0, 0, 0)),\n        (dest.timeScale = source.timeScale !== undefined ? source.timeScale : 1),\n        (dest.roughness = source.roughness !== undefined ? source.roughness : 0.9),\n        (dest.straightness = source.straightness !== undefined ? source.straightness : 0.7),\n        (dest.up0 = source.up0 !== undefined ? vecCopy(source.up0) : new Vector3(0, 0, 1))\n      ;(dest.up1 = source.up1 !== undefined ? vecCopy(source.up1) : new Vector3(0, 0, 1)),\n        (dest.radius0 = source.radius0 !== undefined ? source.radius0 : 1),\n        (dest.radius1 = source.radius1 !== undefined ? source.radius1 : 1),\n        (dest.radius0Factor = source.radius0Factor !== undefined ? source.radius0Factor : 0.5),\n        (dest.radius1Factor = source.radius1Factor !== undefined ? source.radius1Factor : 0.2),\n        (dest.minRadius = source.minRadius !== undefined ? source.minRadius : 0.2),\n        // These parameters should not be changed after lightning creation. They can be changed but the ray will change its form abruptly:\n\n        (dest.isEternal =\n          source.isEternal !== undefined\n            ? source.isEternal\n            : source.birthTime === undefined || source.deathTime === undefined),\n        (dest.birthTime = source.birthTime),\n        (dest.deathTime = source.deathTime),\n        (dest.propagationTimeFactor = source.propagationTimeFactor !== undefined ? source.propagationTimeFactor : 0.1),\n        (dest.vanishingTimeFactor = source.vanishingTimeFactor !== undefined ? source.vanishingTimeFactor : 0.9),\n        (dest.subrayPeriod = source.subrayPeriod !== undefined ? source.subrayPeriod : 4),\n        (dest.subrayDutyCycle = source.subrayDutyCycle !== undefined ? source.subrayDutyCycle : 0.6)\n\n      // These parameters cannot change after lightning creation:\n\n      dest.maxIterations = source.maxIterations !== undefined ? source.maxIterations : 9\n      dest.isStatic = source.isStatic !== undefined ? source.isStatic : false\n      dest.ramification = source.ramification !== undefined ? source.ramification : 5\n      dest.maxSubrayRecursion = source.maxSubrayRecursion !== undefined ? source.maxSubrayRecursion : 3\n      dest.recursionProbability = source.recursionProbability !== undefined ? source.recursionProbability : 0.6\n      dest.generateUVs = source.generateUVs !== undefined ? source.generateUVs : false\n      ;(dest.randomGenerator = source.randomGenerator),\n        (dest.noiseSeed = source.noiseSeed),\n        (dest.onDecideSubrayCreation = source.onDecideSubrayCreation),\n        (dest.onSubrayCreation = source.onSubrayCreation)\n\n      return dest\n    }\n\n    update(time) {\n      if (this.isStatic) return\n\n      if (\n        this.rayParameters.isEternal ||\n        (this.rayParameters.birthTime <= time && time <= this.rayParameters.deathTime)\n      ) {\n        this.updateMesh(time)\n\n        if (time < this.subrays[0].endPropagationTime) {\n          this.state = LightningStrike.RAY_PROPAGATING\n        } else if (time > this.subrays[0].beginVanishingTime) {\n          this.state = LightningStrike.RAY_VANISHING\n        } else {\n          this.state = LightningStrike.RAY_STEADY\n        }\n\n        this.visible = true\n      } else {\n        this.visible = false\n\n        if (time < this.rayParameters.birthTime) {\n          this.state = LightningStrike.RAY_UNBORN\n        } else {\n          this.state = LightningStrike.RAY_EXTINGUISHED\n        }\n      }\n    }\n\n    init(rayParameters) {\n      // Init all the state from the parameters\n\n      this.rayParameters = rayParameters\n\n      // These parameters cannot change after lightning creation:\n\n      this.maxIterations = rayParameters.maxIterations !== undefined ? Math.floor(rayParameters.maxIterations) : 9\n      rayParameters.maxIterations = this.maxIterations\n      this.isStatic = rayParameters.isStatic !== undefined ? rayParameters.isStatic : false\n      rayParameters.isStatic = this.isStatic\n      this.ramification = rayParameters.ramification !== undefined ? Math.floor(rayParameters.ramification) : 5\n      rayParameters.ramification = this.ramification\n      this.maxSubrayRecursion =\n        rayParameters.maxSubrayRecursion !== undefined ? Math.floor(rayParameters.maxSubrayRecursion) : 3\n      rayParameters.maxSubrayRecursion = this.maxSubrayRecursion\n      this.recursionProbability =\n        rayParameters.recursionProbability !== undefined ? rayParameters.recursionProbability : 0.6\n      rayParameters.recursionProbability = this.recursionProbability\n      this.generateUVs = rayParameters.generateUVs !== undefined ? rayParameters.generateUVs : false\n      rayParameters.generateUVs = this.generateUVs\n\n      // Random generator\n      if (rayParameters.randomGenerator !== undefined) {\n        this.randomGenerator = rayParameters.randomGenerator\n        this.seedGenerator = rayParameters.randomGenerator\n\n        if (rayParameters.noiseSeed !== undefined) {\n          this.seedGenerator.setSeed(rayParameters.noiseSeed)\n        }\n      } else {\n        this.randomGenerator = LightningStrike.createRandomGenerator()\n        this.seedGenerator = Math\n      }\n\n      // Ray creation callbacks\n      if (rayParameters.onDecideSubrayCreation !== undefined) {\n        this.onDecideSubrayCreation = rayParameters.onDecideSubrayCreation\n      } else {\n        this.createDefaultSubrayCreationCallbacks()\n\n        if (rayParameters.onSubrayCreation !== undefined) {\n          this.onSubrayCreation = rayParameters.onSubrayCreation\n        }\n      }\n\n      // Internal state\n\n      this.state = LightningStrike.RAY_INITIALIZED\n\n      this.maxSubrays = Math.ceil(1 + Math.pow(this.ramification, Math.max(0, this.maxSubrayRecursion - 1)))\n      rayParameters.maxSubrays = this.maxSubrays\n\n      this.maxRaySegments = 2 * (1 << this.maxIterations)\n\n      this.subrays = []\n\n      for (let i = 0; i < this.maxSubrays; i++) {\n        this.subrays.push(this.createSubray())\n      }\n\n      this.raySegments = []\n\n      for (let i = 0; i < this.maxRaySegments; i++) {\n        this.raySegments.push(this.createSegment())\n      }\n\n      this.time = 0\n      this.timeFraction = 0\n      this.currentSegmentCallback = null\n      this.currentCreateTriangleVertices = this.generateUVs\n        ? this.createTriangleVerticesWithUVs\n        : this.createTriangleVerticesWithoutUVs\n      this.numSubrays = 0\n      this.currentSubray = null\n      this.currentSegmentIndex = 0\n      this.isInitialSegment = false\n      this.subrayProbability = 0\n\n      this.currentVertex = 0\n      this.currentIndex = 0\n      this.currentCoordinate = 0\n      this.currentUVCoordinate = 0\n      this.vertices = null\n      this.uvs = null\n      this.indices = null\n      this.positionAttribute = null\n      this.uvsAttribute = null\n\n      this.simplexX = new SimplexNoise(this.seedGenerator)\n      this.simplexY = new SimplexNoise(this.seedGenerator)\n      this.simplexZ = new SimplexNoise(this.seedGenerator)\n\n      // Temp vectors\n      this.forwards = new Vector3()\n      this.forwardsFill = new Vector3()\n      this.side = new Vector3()\n      this.down = new Vector3()\n      this.middlePos = new Vector3()\n      this.middleLinPos = new Vector3()\n      this.newPos = new Vector3()\n      this.vPos = new Vector3()\n      this.cross1 = new Vector3()\n    }\n\n    createMesh() {\n      const maxDrawableSegmentsPerSubRay = 1 << this.maxIterations\n\n      const maxVerts = 3 * (maxDrawableSegmentsPerSubRay + 1) * this.maxSubrays\n      const maxIndices = 18 * maxDrawableSegmentsPerSubRay * this.maxSubrays\n\n      this.vertices = new Float32Array(maxVerts * 3)\n      this.indices = new Uint32Array(maxIndices)\n\n      if (this.generateUVs) {\n        this.uvs = new Float32Array(maxVerts * 2)\n      }\n\n      // Populate the mesh\n      this.fillMesh(0)\n\n      this.setIndex(new Uint32BufferAttribute(this.indices, 1))\n\n      this.positionAttribute = new Float32BufferAttribute(this.vertices, 3)\n      this.setAttribute('position', this.positionAttribute)\n\n      if (this.generateUVs) {\n        this.uvsAttribute = new Float32BufferAttribute(new Float32Array(this.uvs), 2)\n        this.setAttribute('uv', this.uvsAttribute)\n      }\n\n      if (!this.isStatic) {\n        this.index.usage = DynamicDrawUsage\n        this.positionAttribute.usage = DynamicDrawUsage\n\n        if (this.generateUVs) {\n          this.uvsAttribute.usage = DynamicDrawUsage\n        }\n      }\n\n      // Store buffers for later modification\n      this.vertices = this.positionAttribute.array\n      this.indices = this.index.array\n\n      if (this.generateUVs) {\n        this.uvs = this.uvsAttribute.array\n      }\n    }\n\n    updateMesh(time) {\n      this.fillMesh(time)\n\n      this.drawRange.count = this.currentIndex\n\n      this.index.needsUpdate = true\n\n      this.positionAttribute.needsUpdate = true\n\n      if (this.generateUVs) {\n        this.uvsAttribute.needsUpdate = true\n      }\n    }\n\n    fillMesh(time) {\n      const scope = this\n\n      this.currentVertex = 0\n      this.currentIndex = 0\n      this.currentCoordinate = 0\n      this.currentUVCoordinate = 0\n\n      this.fractalRay(time, function fillVertices(segment) {\n        const subray = scope.currentSubray\n\n        if (time < subray.birthTime) {\n          //&& ( ! this.rayParameters.isEternal || scope.currentSubray.recursion > 0 ) ) {\n\n          return\n        } else if (this.rayParameters.isEternal && scope.currentSubray.recursion == 0) {\n          // Eternal rays don't propagate nor vanish, but its subrays do\n\n          scope.createPrism(segment)\n\n          scope.onDecideSubrayCreation(segment, scope)\n        } else if (time < subray.endPropagationTime) {\n          if (scope.timeFraction >= segment.fraction0 * subray.propagationTimeFactor) {\n            // Ray propagation has arrived to this segment\n\n            scope.createPrism(segment)\n\n            scope.onDecideSubrayCreation(segment, scope)\n          }\n        } else if (time < subray.beginVanishingTime) {\n          // Ray is steady (nor propagating nor vanishing)\n\n          scope.createPrism(segment)\n\n          scope.onDecideSubrayCreation(segment, scope)\n        } else {\n          if (scope.timeFraction <= subray.vanishingTimeFactor + segment.fraction1 * (1 - subray.vanishingTimeFactor)) {\n            // Segment has not yet vanished\n\n            scope.createPrism(segment)\n          }\n\n          scope.onDecideSubrayCreation(segment, scope)\n        }\n      })\n    }\n\n    addNewSubray(/*rayParameters*/) {\n      return this.subrays[this.numSubrays++]\n    }\n\n    initSubray(subray, rayParameters) {\n      subray.pos0.copy(rayParameters.sourceOffset)\n      subray.pos1.copy(rayParameters.destOffset)\n      subray.up0.copy(rayParameters.up0)\n      subray.up1.copy(rayParameters.up1)\n      subray.radius0 = rayParameters.radius0\n      subray.radius1 = rayParameters.radius1\n      subray.birthTime = rayParameters.birthTime\n      subray.deathTime = rayParameters.deathTime\n      subray.timeScale = rayParameters.timeScale\n      subray.roughness = rayParameters.roughness\n      subray.straightness = rayParameters.straightness\n      subray.propagationTimeFactor = rayParameters.propagationTimeFactor\n      subray.vanishingTimeFactor = rayParameters.vanishingTimeFactor\n\n      subray.maxIterations = this.maxIterations\n      subray.seed = rayParameters.noiseSeed !== undefined ? rayParameters.noiseSeed : 0\n      subray.recursion = 0\n    }\n\n    fractalRay(time, segmentCallback) {\n      this.time = time\n      this.currentSegmentCallback = segmentCallback\n      this.numSubrays = 0\n\n      // Add the top level subray\n      this.initSubray(this.addNewSubray(), this.rayParameters)\n\n      // Process all subrays that are being generated until consuming all of them\n      for (let subrayIndex = 0; subrayIndex < this.numSubrays; subrayIndex++) {\n        const subray = this.subrays[subrayIndex]\n        this.currentSubray = subray\n\n        this.randomGenerator.setSeed(subray.seed)\n\n        subray.endPropagationTime = MathUtils.lerp(subray.birthTime, subray.deathTime, subray.propagationTimeFactor)\n        subray.beginVanishingTime = MathUtils.lerp(subray.deathTime, subray.birthTime, 1 - subray.vanishingTimeFactor)\n\n        const random1 = this.randomGenerator.random\n        subray.linPos0.set(random1(), random1(), random1()).multiplyScalar(1000)\n        subray.linPos1.set(random1(), random1(), random1()).multiplyScalar(1000)\n\n        this.timeFraction = (time - subray.birthTime) / (subray.deathTime - subray.birthTime)\n\n        this.currentSegmentIndex = 0\n        this.isInitialSegment = true\n\n        const segment = this.getNewSegment()\n        segment.iteration = 0\n        segment.pos0.copy(subray.pos0)\n        segment.pos1.copy(subray.pos1)\n        segment.linPos0.copy(subray.linPos0)\n        segment.linPos1.copy(subray.linPos1)\n        segment.up0.copy(subray.up0)\n        segment.up1.copy(subray.up1)\n        segment.radius0 = subray.radius0\n        segment.radius1 = subray.radius1\n        segment.fraction0 = 0\n        segment.fraction1 = 1\n        segment.positionVariationFactor = 1 - subray.straightness\n\n        this.subrayProbability =\n          (this.ramification * Math.pow(this.recursionProbability, subray.recursion)) / (1 << subray.maxIterations)\n\n        this.fractalRayRecursive(segment)\n      }\n\n      this.currentSegmentCallback = null\n      this.currentSubray = null\n    }\n\n    fractalRayRecursive(segment) {\n      // Leave recursion condition\n      if (segment.iteration >= this.currentSubray.maxIterations) {\n        this.currentSegmentCallback(segment)\n\n        return\n      }\n\n      // Interpolation\n      this.forwards.subVectors(segment.pos1, segment.pos0)\n      let lForwards = this.forwards.length()\n\n      if (lForwards < 0.000001) {\n        this.forwards.set(0, 0, 0.01)\n        lForwards = this.forwards.length()\n      }\n\n      const middleRadius = (segment.radius0 + segment.radius1) * 0.5\n      const middleFraction = (segment.fraction0 + segment.fraction1) * 0.5\n\n      const timeDimension = this.time * this.currentSubray.timeScale * Math.pow(2, segment.iteration)\n\n      this.middlePos.lerpVectors(segment.pos0, segment.pos1, 0.5)\n      this.middleLinPos.lerpVectors(segment.linPos0, segment.linPos1, 0.5)\n      const p = this.middleLinPos\n\n      // Noise\n      this.newPos.set(\n        this.simplexX.noise4d(p.x, p.y, p.z, timeDimension),\n        this.simplexY.noise4d(p.x, p.y, p.z, timeDimension),\n        this.simplexZ.noise4d(p.x, p.y, p.z, timeDimension),\n      )\n\n      this.newPos.multiplyScalar(segment.positionVariationFactor * lForwards)\n      this.newPos.add(this.middlePos)\n\n      // Recursion\n\n      const newSegment1 = this.getNewSegment()\n      newSegment1.pos0.copy(segment.pos0)\n      newSegment1.pos1.copy(this.newPos)\n      newSegment1.linPos0.copy(segment.linPos0)\n      newSegment1.linPos1.copy(this.middleLinPos)\n      newSegment1.up0.copy(segment.up0)\n      newSegment1.up1.copy(segment.up1)\n      newSegment1.radius0 = segment.radius0\n      newSegment1.radius1 = middleRadius\n      newSegment1.fraction0 = segment.fraction0\n      newSegment1.fraction1 = middleFraction\n      newSegment1.positionVariationFactor = segment.positionVariationFactor * this.currentSubray.roughness\n      newSegment1.iteration = segment.iteration + 1\n\n      const newSegment2 = this.getNewSegment()\n      newSegment2.pos0.copy(this.newPos)\n      newSegment2.pos1.copy(segment.pos1)\n      newSegment2.linPos0.copy(this.middleLinPos)\n      newSegment2.linPos1.copy(segment.linPos1)\n      this.cross1.crossVectors(segment.up0, this.forwards.normalize())\n      newSegment2.up0.crossVectors(this.forwards, this.cross1).normalize()\n      newSegment2.up1.copy(segment.up1)\n      newSegment2.radius0 = middleRadius\n      newSegment2.radius1 = segment.radius1\n      newSegment2.fraction0 = middleFraction\n      newSegment2.fraction1 = segment.fraction1\n      newSegment2.positionVariationFactor = segment.positionVariationFactor * this.currentSubray.roughness\n      newSegment2.iteration = segment.iteration + 1\n\n      this.fractalRayRecursive(newSegment1)\n\n      this.fractalRayRecursive(newSegment2)\n    }\n\n    createPrism(segment) {\n      // Creates one triangular prism and its vertices at the segment\n\n      this.forwardsFill.subVectors(segment.pos1, segment.pos0).normalize()\n\n      if (this.isInitialSegment) {\n        this.currentCreateTriangleVertices(segment.pos0, segment.up0, this.forwardsFill, segment.radius0, 0)\n\n        this.isInitialSegment = false\n      }\n\n      this.currentCreateTriangleVertices(\n        segment.pos1,\n        segment.up0,\n        this.forwardsFill,\n        segment.radius1,\n        segment.fraction1,\n      )\n\n      this.createPrismFaces()\n    }\n\n    createTriangleVerticesWithoutUVs(pos, up, forwards, radius) {\n      // Create an equilateral triangle (only vertices)\n\n      this.side.crossVectors(up, forwards).multiplyScalar(radius * LightningStrike.COS30DEG)\n      this.down.copy(up).multiplyScalar(-radius * LightningStrike.SIN30DEG)\n\n      const p = this.vPos\n      const v = this.vertices\n\n      p.copy(pos).sub(this.side).add(this.down)\n\n      v[this.currentCoordinate++] = p.x\n      v[this.currentCoordinate++] = p.y\n      v[this.currentCoordinate++] = p.z\n\n      p.copy(pos).add(this.side).add(this.down)\n\n      v[this.currentCoordinate++] = p.x\n      v[this.currentCoordinate++] = p.y\n      v[this.currentCoordinate++] = p.z\n\n      p.copy(up).multiplyScalar(radius).add(pos)\n\n      v[this.currentCoordinate++] = p.x\n      v[this.currentCoordinate++] = p.y\n      v[this.currentCoordinate++] = p.z\n\n      this.currentVertex += 3\n    }\n\n    createTriangleVerticesWithUVs(pos, up, forwards, radius, u) {\n      // Create an equilateral triangle (only vertices)\n\n      this.side.crossVectors(up, forwards).multiplyScalar(radius * LightningStrike.COS30DEG)\n      this.down.copy(up).multiplyScalar(-radius * LightningStrike.SIN30DEG)\n\n      const p = this.vPos\n      const v = this.vertices\n      const uv = this.uvs\n\n      p.copy(pos).sub(this.side).add(this.down)\n\n      v[this.currentCoordinate++] = p.x\n      v[this.currentCoordinate++] = p.y\n      v[this.currentCoordinate++] = p.z\n\n      uv[this.currentUVCoordinate++] = u\n      uv[this.currentUVCoordinate++] = 0\n\n      p.copy(pos).add(this.side).add(this.down)\n\n      v[this.currentCoordinate++] = p.x\n      v[this.currentCoordinate++] = p.y\n      v[this.currentCoordinate++] = p.z\n\n      uv[this.currentUVCoordinate++] = u\n      uv[this.currentUVCoordinate++] = 0.5\n\n      p.copy(up).multiplyScalar(radius).add(pos)\n\n      v[this.currentCoordinate++] = p.x\n      v[this.currentCoordinate++] = p.y\n      v[this.currentCoordinate++] = p.z\n\n      uv[this.currentUVCoordinate++] = u\n      uv[this.currentUVCoordinate++] = 1\n\n      this.currentVertex += 3\n    }\n\n    createPrismFaces(vertex /*, index*/) {\n      const indices = this.indices\n      vertex = this.currentVertex - 6\n\n      indices[this.currentIndex++] = vertex + 1\n      indices[this.currentIndex++] = vertex + 2\n      indices[this.currentIndex++] = vertex + 5\n      indices[this.currentIndex++] = vertex + 1\n      indices[this.currentIndex++] = vertex + 5\n      indices[this.currentIndex++] = vertex + 4\n      indices[this.currentIndex++] = vertex + 0\n      indices[this.currentIndex++] = vertex + 1\n      indices[this.currentIndex++] = vertex + 4\n      indices[this.currentIndex++] = vertex + 0\n      indices[this.currentIndex++] = vertex + 4\n      indices[this.currentIndex++] = vertex + 3\n      indices[this.currentIndex++] = vertex + 2\n      indices[this.currentIndex++] = vertex + 0\n      indices[this.currentIndex++] = vertex + 3\n      indices[this.currentIndex++] = vertex + 2\n      indices[this.currentIndex++] = vertex + 3\n      indices[this.currentIndex++] = vertex + 5\n    }\n\n    createDefaultSubrayCreationCallbacks() {\n      const random1 = this.randomGenerator.random\n\n      this.onDecideSubrayCreation = function (segment, lightningStrike) {\n        // Decide subrays creation at parent (sub)ray segment\n\n        const subray = lightningStrike.currentSubray\n\n        const period = lightningStrike.rayParameters.subrayPeriod\n        const dutyCycle = lightningStrike.rayParameters.subrayDutyCycle\n\n        const phase0 =\n          lightningStrike.rayParameters.isEternal && subray.recursion == 0\n            ? -random1() * period\n            : MathUtils.lerp(subray.birthTime, subray.endPropagationTime, segment.fraction0) - random1() * period\n\n        const phase = lightningStrike.time - phase0\n        const currentCycle = Math.floor(phase / period)\n\n        const childSubraySeed = random1() * (currentCycle + 1)\n\n        const isActive = phase % period <= dutyCycle * period\n\n        let probability = 0\n\n        if (isActive) {\n          probability = lightningStrike.subrayProbability\n          // Distribution test: probability *= segment.fraction0 > 0.5 && segment.fraction0 < 0.9 ? 1 / 0.4 : 0;\n        }\n\n        if (\n          subray.recursion < lightningStrike.maxSubrayRecursion &&\n          lightningStrike.numSubrays < lightningStrike.maxSubrays &&\n          random1() < probability\n        ) {\n          const childSubray = lightningStrike.addNewSubray()\n\n          const parentSeed = lightningStrike.randomGenerator.getSeed()\n          childSubray.seed = childSubraySeed\n          lightningStrike.randomGenerator.setSeed(childSubraySeed)\n\n          childSubray.recursion = subray.recursion + 1\n          childSubray.maxIterations = Math.max(1, subray.maxIterations - 1)\n\n          childSubray.linPos0.set(random1(), random1(), random1()).multiplyScalar(1000)\n          childSubray.linPos1.set(random1(), random1(), random1()).multiplyScalar(1000)\n          childSubray.up0.copy(subray.up0)\n          childSubray.up1.copy(subray.up1)\n          childSubray.radius0 = segment.radius0 * lightningStrike.rayParameters.radius0Factor\n          childSubray.radius1 = Math.min(\n            lightningStrike.rayParameters.minRadius,\n            segment.radius1 * lightningStrike.rayParameters.radius1Factor,\n          )\n\n          childSubray.birthTime = phase0 + currentCycle * period\n          childSubray.deathTime = childSubray.birthTime + period * dutyCycle\n\n          if (!lightningStrike.rayParameters.isEternal && subray.recursion == 0) {\n            childSubray.birthTime = Math.max(childSubray.birthTime, subray.birthTime)\n            childSubray.deathTime = Math.min(childSubray.deathTime, subray.deathTime)\n          }\n\n          childSubray.timeScale = subray.timeScale * 2\n          childSubray.roughness = subray.roughness\n          childSubray.straightness = subray.straightness\n          childSubray.propagationTimeFactor = subray.propagationTimeFactor\n          childSubray.vanishingTimeFactor = subray.vanishingTimeFactor\n\n          lightningStrike.onSubrayCreation(segment, subray, childSubray, lightningStrike)\n\n          lightningStrike.randomGenerator.setSeed(parentSeed)\n        }\n      }\n\n      const vec1Pos = new Vector3()\n      const vec2Forward = new Vector3()\n      const vec3Side = new Vector3()\n      const vec4Up = new Vector3()\n\n      this.onSubrayCreation = function (segment, parentSubray, childSubray, lightningStrike) {\n        // Decide childSubray origin and destination positions (pos0 and pos1) and possibly other properties of childSubray\n\n        // Just use the default cone position generator\n        lightningStrike.subrayCylinderPosition(segment, parentSubray, childSubray, 0.5, 0.6, 0.2)\n      }\n\n      this.subrayConePosition = function (\n        segment,\n        parentSubray,\n        childSubray,\n        heightFactor,\n        sideWidthFactor,\n        minSideWidthFactor,\n      ) {\n        // Sets childSubray pos0 and pos1 in a cone\n\n        childSubray.pos0.copy(segment.pos0)\n\n        vec1Pos.subVectors(parentSubray.pos1, parentSubray.pos0)\n        vec2Forward.copy(vec1Pos).normalize()\n        vec1Pos.multiplyScalar(segment.fraction0 + (1 - segment.fraction0) * (random1() * heightFactor))\n        const length = vec1Pos.length()\n        vec3Side.crossVectors(parentSubray.up0, vec2Forward)\n        const angle = 2 * Math.PI * random1()\n        vec3Side.multiplyScalar(Math.cos(angle))\n        vec4Up.copy(parentSubray.up0).multiplyScalar(Math.sin(angle))\n\n        childSubray.pos1\n          .copy(vec3Side)\n          .add(vec4Up)\n          .multiplyScalar(length * sideWidthFactor * (minSideWidthFactor + random1() * (1 - minSideWidthFactor)))\n          .add(vec1Pos)\n          .add(parentSubray.pos0)\n      }\n\n      this.subrayCylinderPosition = function (\n        segment,\n        parentSubray,\n        childSubray,\n        heightFactor,\n        sideWidthFactor,\n        minSideWidthFactor,\n      ) {\n        // Sets childSubray pos0 and pos1 in a cylinder\n\n        childSubray.pos0.copy(segment.pos0)\n\n        vec1Pos.subVectors(parentSubray.pos1, parentSubray.pos0)\n        vec2Forward.copy(vec1Pos).normalize()\n        vec1Pos.multiplyScalar(segment.fraction0 + (1 - segment.fraction0) * ((2 * random1() - 1) * heightFactor))\n        const length = vec1Pos.length()\n        vec3Side.crossVectors(parentSubray.up0, vec2Forward)\n        const angle = 2 * Math.PI * random1()\n        vec3Side.multiplyScalar(Math.cos(angle))\n        vec4Up.copy(parentSubray.up0).multiplyScalar(Math.sin(angle))\n\n        childSubray.pos1\n          .copy(vec3Side)\n          .add(vec4Up)\n          .multiplyScalar(length * sideWidthFactor * (minSideWidthFactor + random1() * (1 - minSideWidthFactor)))\n          .add(vec1Pos)\n          .add(parentSubray.pos0)\n      }\n    }\n\n    createSubray() {\n      return {\n        seed: 0,\n        maxIterations: 0,\n        recursion: 0,\n        pos0: new Vector3(),\n        pos1: new Vector3(),\n        linPos0: new Vector3(),\n        linPos1: new Vector3(),\n        up0: new Vector3(),\n        up1: new Vector3(),\n        radius0: 0,\n        radius1: 0,\n        birthTime: 0,\n        deathTime: 0,\n        timeScale: 0,\n        roughness: 0,\n        straightness: 0,\n        propagationTimeFactor: 0,\n        vanishingTimeFactor: 0,\n        endPropagationTime: 0,\n        beginVanishingTime: 0,\n      }\n    }\n\n    createSegment() {\n      return {\n        iteration: 0,\n        pos0: new Vector3(),\n        pos1: new Vector3(),\n        linPos0: new Vector3(),\n        linPos1: new Vector3(),\n        up0: new Vector3(),\n        up1: new Vector3(),\n        radius0: 0,\n        radius1: 0,\n        fraction0: 0,\n        fraction1: 0,\n        positionVariationFactor: 0,\n      }\n    }\n\n    getNewSegment() {\n      return this.raySegments[this.currentSegmentIndex++]\n    }\n\n    copy(source) {\n      super.copy(source)\n\n      this.init(LightningStrike.copyParameters({}, source.rayParameters))\n\n      return this\n    }\n\n    clone() {\n      return new this.constructor(LightningStrike.copyParameters({}, this.rayParameters))\n    }\n  }\n\n  return LightningStrike\n})()\n\nexport { LightningStrike }\n"], "names": ["LightningStrike"], "mappings": ";;;;;;;;AA+GK,MAAC,kBAAmC,uBAAM;AAC7C,QAAM,mBAAN,cAA8B,eAAe;AAAA,IAY3C,YAAY,gBAAgB,IAAI;AAC9B,YAAO;AAEP,WAAK,oBAAoB;AAEzB,WAAK,OAAO;AAGZ,WAAK,KAAK,iBAAgB,eAAe,eAAe,aAAa,CAAC;AAGtE,WAAK,WAAY;AAAA,IAClB;AAAA,IAED,OAAO,wBAAwB;AAC7B,YAAM,WAAW;AACjB,YAAM,QAAQ,CAAE;AAEhB,eAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,cAAM,KAAK,KAAK,QAAQ;AAAA,MACzB;AAED,YAAM,YAAY;AAAA,QAChB,aAAa;AAAA,QAEb,QAAQ,WAAY;AAClB,gBAAM,QAAQ,MAAM,UAAU,WAAW;AAEzC,oBAAU,eAAe,UAAU,cAAc,KAAK;AAEtD,iBAAO;AAAA,QACR;AAAA,QAED,SAAS,WAAY;AACnB,iBAAO,UAAU,cAAc;AAAA,QAChC;AAAA,QAED,SAAS,SAAU,MAAM;AACvB,oBAAU,cAAc,KAAK,MAAM,OAAO,QAAQ,IAAI;AAAA,QACvD;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAAA,IAED,OAAO,eAAe,OAAO,IAAI,SAAS,CAAA,GAAI;AAC5C,YAAM,UAAU,SAAU,GAAG;AAC3B,YAAI,WAAW,MAAM;AACnB,iBAAO;AAAA,QACjB,OAAe;AACL,iBAAO,EAAE,MAAO;AAAA,QACjB;AAAA,MACF;AAEA,MAAC,KAAK,eAAe,OAAO,iBAAiB,SAAY,QAAQ,OAAO,YAAY,IAAI,IAAI,QAAQ,GAAG,KAAK,CAAC,GAC3G,KAAK,aAAa,OAAO,eAAe,SAAY,QAAQ,OAAO,UAAU,IAAI,IAAI,QAAQ,GAAG,GAAG,CAAC,GACpG,KAAK,YAAY,OAAO,cAAc,SAAY,OAAO,YAAY,GACrE,KAAK,YAAY,OAAO,cAAc,SAAY,OAAO,YAAY,KACrE,KAAK,eAAe,OAAO,iBAAiB,SAAY,OAAO,eAAe,KAC9E,KAAK,MAAM,OAAO,QAAQ,SAAY,QAAQ,OAAO,GAAG,IAAI,IAAI,QAAQ,GAAG,GAAG,CAAC;AACjF,MAAC,KAAK,MAAM,OAAO,QAAQ,SAAY,QAAQ,OAAO,GAAG,IAAI,IAAI,QAAQ,GAAG,GAAG,CAAC,GAC9E,KAAK,UAAU,OAAO,YAAY,SAAY,OAAO,UAAU,GAC/D,KAAK,UAAU,OAAO,YAAY,SAAY,OAAO,UAAU,GAC/D,KAAK,gBAAgB,OAAO,kBAAkB,SAAY,OAAO,gBAAgB,KACjF,KAAK,gBAAgB,OAAO,kBAAkB,SAAY,OAAO,gBAAgB,KACjF,KAAK,YAAY,OAAO,cAAc,SAAY,OAAO,YAAY;AAAA,MAGrE,KAAK,YACJ,OAAO,cAAc,SACjB,OAAO,YACP,OAAO,cAAc,UAAa,OAAO,cAAc,QAC5D,KAAK,YAAY,OAAO,WACxB,KAAK,YAAY,OAAO,WACxB,KAAK,wBAAwB,OAAO,0BAA0B,SAAY,OAAO,wBAAwB,KACzG,KAAK,sBAAsB,OAAO,wBAAwB,SAAY,OAAO,sBAAsB,KACnG,KAAK,eAAe,OAAO,iBAAiB,SAAY,OAAO,eAAe,GAC9E,KAAK,kBAAkB,OAAO,oBAAoB,SAAY,OAAO,kBAAkB;AAI1F,WAAK,gBAAgB,OAAO,kBAAkB,SAAY,OAAO,gBAAgB;AACjF,WAAK,WAAW,OAAO,aAAa,SAAY,OAAO,WAAW;AAClE,WAAK,eAAe,OAAO,iBAAiB,SAAY,OAAO,eAAe;AAC9E,WAAK,qBAAqB,OAAO,uBAAuB,SAAY,OAAO,qBAAqB;AAChG,WAAK,uBAAuB,OAAO,yBAAyB,SAAY,OAAO,uBAAuB;AACtG,WAAK,cAAc,OAAO,gBAAgB,SAAY,OAAO,cAAc;AAC1E,MAAC,KAAK,kBAAkB,OAAO,iBAC7B,KAAK,YAAY,OAAO,WACxB,KAAK,yBAAyB,OAAO,wBACrC,KAAK,mBAAmB,OAAO;AAElC,aAAO;AAAA,IACR;AAAA,IAED,OAAO,MAAM;AACX,UAAI,KAAK;AAAU;AAEnB,UACE,KAAK,cAAc,aAClB,KAAK,cAAc,aAAa,QAAQ,QAAQ,KAAK,cAAc,WACpE;AACA,aAAK,WAAW,IAAI;AAEpB,YAAI,OAAO,KAAK,QAAQ,CAAC,EAAE,oBAAoB;AAC7C,eAAK,QAAQ,iBAAgB;AAAA,QACvC,WAAmB,OAAO,KAAK,QAAQ,CAAC,EAAE,oBAAoB;AACpD,eAAK,QAAQ,iBAAgB;AAAA,QACvC,OAAe;AACL,eAAK,QAAQ,iBAAgB;AAAA,QAC9B;AAED,aAAK,UAAU;AAAA,MACvB,OAAa;AACL,aAAK,UAAU;AAEf,YAAI,OAAO,KAAK,cAAc,WAAW;AACvC,eAAK,QAAQ,iBAAgB;AAAA,QACvC,OAAe;AACL,eAAK,QAAQ,iBAAgB;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAAA,IAED,KAAK,eAAe;AAGlB,WAAK,gBAAgB;AAIrB,WAAK,gBAAgB,cAAc,kBAAkB,SAAY,KAAK,MAAM,cAAc,aAAa,IAAI;AAC3G,oBAAc,gBAAgB,KAAK;AACnC,WAAK,WAAW,cAAc,aAAa,SAAY,cAAc,WAAW;AAChF,oBAAc,WAAW,KAAK;AAC9B,WAAK,eAAe,cAAc,iBAAiB,SAAY,KAAK,MAAM,cAAc,YAAY,IAAI;AACxG,oBAAc,eAAe,KAAK;AAClC,WAAK,qBACH,cAAc,uBAAuB,SAAY,KAAK,MAAM,cAAc,kBAAkB,IAAI;AAClG,oBAAc,qBAAqB,KAAK;AACxC,WAAK,uBACH,cAAc,yBAAyB,SAAY,cAAc,uBAAuB;AAC1F,oBAAc,uBAAuB,KAAK;AAC1C,WAAK,cAAc,cAAc,gBAAgB,SAAY,cAAc,cAAc;AACzF,oBAAc,cAAc,KAAK;AAGjC,UAAI,cAAc,oBAAoB,QAAW;AAC/C,aAAK,kBAAkB,cAAc;AACrC,aAAK,gBAAgB,cAAc;AAEnC,YAAI,cAAc,cAAc,QAAW;AACzC,eAAK,cAAc,QAAQ,cAAc,SAAS;AAAA,QACnD;AAAA,MACT,OAAa;AACL,aAAK,kBAAkB,iBAAgB,sBAAuB;AAC9D,aAAK,gBAAgB;AAAA,MACtB;AAGD,UAAI,cAAc,2BAA2B,QAAW;AACtD,aAAK,yBAAyB,cAAc;AAAA,MACpD,OAAa;AACL,aAAK,qCAAsC;AAE3C,YAAI,cAAc,qBAAqB,QAAW;AAChD,eAAK,mBAAmB,cAAc;AAAA,QACvC;AAAA,MACF;AAID,WAAK,QAAQ,iBAAgB;AAE7B,WAAK,aAAa,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,cAAc,KAAK,IAAI,GAAG,KAAK,qBAAqB,CAAC,CAAC,CAAC;AACrG,oBAAc,aAAa,KAAK;AAEhC,WAAK,iBAAiB,KAAK,KAAK,KAAK;AAErC,WAAK,UAAU,CAAE;AAEjB,eAAS,IAAI,GAAG,IAAI,KAAK,YAAY,KAAK;AACxC,aAAK,QAAQ,KAAK,KAAK,aAAY,CAAE;AAAA,MACtC;AAED,WAAK,cAAc,CAAE;AAErB,eAAS,IAAI,GAAG,IAAI,KAAK,gBAAgB,KAAK;AAC5C,aAAK,YAAY,KAAK,KAAK,cAAa,CAAE;AAAA,MAC3C;AAED,WAAK,OAAO;AACZ,WAAK,eAAe;AACpB,WAAK,yBAAyB;AAC9B,WAAK,gCAAgC,KAAK,cACtC,KAAK,gCACL,KAAK;AACT,WAAK,aAAa;AAClB,WAAK,gBAAgB;AACrB,WAAK,sBAAsB;AAC3B,WAAK,mBAAmB;AACxB,WAAK,oBAAoB;AAEzB,WAAK,gBAAgB;AACrB,WAAK,eAAe;AACpB,WAAK,oBAAoB;AACzB,WAAK,sBAAsB;AAC3B,WAAK,WAAW;AAChB,WAAK,MAAM;AACX,WAAK,UAAU;AACf,WAAK,oBAAoB;AACzB,WAAK,eAAe;AAEpB,WAAK,WAAW,IAAI,aAAa,KAAK,aAAa;AACnD,WAAK,WAAW,IAAI,aAAa,KAAK,aAAa;AACnD,WAAK,WAAW,IAAI,aAAa,KAAK,aAAa;AAGnD,WAAK,WAAW,IAAI,QAAS;AAC7B,WAAK,eAAe,IAAI,QAAS;AACjC,WAAK,OAAO,IAAI,QAAS;AACzB,WAAK,OAAO,IAAI,QAAS;AACzB,WAAK,YAAY,IAAI,QAAS;AAC9B,WAAK,eAAe,IAAI,QAAS;AACjC,WAAK,SAAS,IAAI,QAAS;AAC3B,WAAK,OAAO,IAAI,QAAS;AACzB,WAAK,SAAS,IAAI,QAAS;AAAA,IAC5B;AAAA,IAED,aAAa;AACX,YAAM,+BAA+B,KAAK,KAAK;AAE/C,YAAM,WAAW,KAAK,+BAA+B,KAAK,KAAK;AAC/D,YAAM,aAAa,KAAK,+BAA+B,KAAK;AAE5D,WAAK,WAAW,IAAI,aAAa,WAAW,CAAC;AAC7C,WAAK,UAAU,IAAI,YAAY,UAAU;AAEzC,UAAI,KAAK,aAAa;AACpB,aAAK,MAAM,IAAI,aAAa,WAAW,CAAC;AAAA,MACzC;AAGD,WAAK,SAAS,CAAC;AAEf,WAAK,SAAS,IAAI,sBAAsB,KAAK,SAAS,CAAC,CAAC;AAExD,WAAK,oBAAoB,IAAI,uBAAuB,KAAK,UAAU,CAAC;AACpE,WAAK,aAAa,YAAY,KAAK,iBAAiB;AAEpD,UAAI,KAAK,aAAa;AACpB,aAAK,eAAe,IAAI,uBAAuB,IAAI,aAAa,KAAK,GAAG,GAAG,CAAC;AAC5E,aAAK,aAAa,MAAM,KAAK,YAAY;AAAA,MAC1C;AAED,UAAI,CAAC,KAAK,UAAU;AAClB,aAAK,MAAM,QAAQ;AACnB,aAAK,kBAAkB,QAAQ;AAE/B,YAAI,KAAK,aAAa;AACpB,eAAK,aAAa,QAAQ;AAAA,QAC3B;AAAA,MACF;AAGD,WAAK,WAAW,KAAK,kBAAkB;AACvC,WAAK,UAAU,KAAK,MAAM;AAE1B,UAAI,KAAK,aAAa;AACpB,aAAK,MAAM,KAAK,aAAa;AAAA,MAC9B;AAAA,IACF;AAAA,IAED,WAAW,MAAM;AACf,WAAK,SAAS,IAAI;AAElB,WAAK,UAAU,QAAQ,KAAK;AAE5B,WAAK,MAAM,cAAc;AAEzB,WAAK,kBAAkB,cAAc;AAErC,UAAI,KAAK,aAAa;AACpB,aAAK,aAAa,cAAc;AAAA,MACjC;AAAA,IACF;AAAA,IAED,SAAS,MAAM;AACb,YAAM,QAAQ;AAEd,WAAK,gBAAgB;AACrB,WAAK,eAAe;AACpB,WAAK,oBAAoB;AACzB,WAAK,sBAAsB;AAE3B,WAAK,WAAW,MAAM,SAAS,aAAa,SAAS;AACnD,cAAM,SAAS,MAAM;AAErB,YAAI,OAAO,OAAO,WAAW;AAG3B;AAAA,QACV,WAAmB,KAAK,cAAc,aAAa,MAAM,cAAc,aAAa,GAAG;AAG7E,gBAAM,YAAY,OAAO;AAEzB,gBAAM,uBAAuB,SAAS,KAAK;AAAA,QACrD,WAAmB,OAAO,OAAO,oBAAoB;AAC3C,cAAI,MAAM,gBAAgB,QAAQ,YAAY,OAAO,uBAAuB;AAG1E,kBAAM,YAAY,OAAO;AAEzB,kBAAM,uBAAuB,SAAS,KAAK;AAAA,UAC5C;AAAA,QACX,WAAmB,OAAO,OAAO,oBAAoB;AAG3C,gBAAM,YAAY,OAAO;AAEzB,gBAAM,uBAAuB,SAAS,KAAK;AAAA,QACrD,OAAe;AACL,cAAI,MAAM,gBAAgB,OAAO,sBAAsB,QAAQ,aAAa,IAAI,OAAO,sBAAsB;AAG3G,kBAAM,YAAY,OAAO;AAAA,UAC1B;AAED,gBAAM,uBAAuB,SAAS,KAAK;AAAA,QAC5C;AAAA,MACT,CAAO;AAAA,IACF;AAAA,IAED,eAAgC;AAC9B,aAAO,KAAK,QAAQ,KAAK,YAAY;AAAA,IACtC;AAAA,IAED,WAAW,QAAQ,eAAe;AAChC,aAAO,KAAK,KAAK,cAAc,YAAY;AAC3C,aAAO,KAAK,KAAK,cAAc,UAAU;AACzC,aAAO,IAAI,KAAK,cAAc,GAAG;AACjC,aAAO,IAAI,KAAK,cAAc,GAAG;AACjC,aAAO,UAAU,cAAc;AAC/B,aAAO,UAAU,cAAc;AAC/B,aAAO,YAAY,cAAc;AACjC,aAAO,YAAY,cAAc;AACjC,aAAO,YAAY,cAAc;AACjC,aAAO,YAAY,cAAc;AACjC,aAAO,eAAe,cAAc;AACpC,aAAO,wBAAwB,cAAc;AAC7C,aAAO,sBAAsB,cAAc;AAE3C,aAAO,gBAAgB,KAAK;AAC5B,aAAO,OAAO,cAAc,cAAc,SAAY,cAAc,YAAY;AAChF,aAAO,YAAY;AAAA,IACpB;AAAA,IAED,WAAW,MAAM,iBAAiB;AAChC,WAAK,OAAO;AACZ,WAAK,yBAAyB;AAC9B,WAAK,aAAa;AAGlB,WAAK,WAAW,KAAK,aAAY,GAAI,KAAK,aAAa;AAGvD,eAAS,cAAc,GAAG,cAAc,KAAK,YAAY,eAAe;AACtE,cAAM,SAAS,KAAK,QAAQ,WAAW;AACvC,aAAK,gBAAgB;AAErB,aAAK,gBAAgB,QAAQ,OAAO,IAAI;AAExC,eAAO,qBAAqB,UAAU,KAAK,OAAO,WAAW,OAAO,WAAW,OAAO,qBAAqB;AAC3G,eAAO,qBAAqB,UAAU,KAAK,OAAO,WAAW,OAAO,WAAW,IAAI,OAAO,mBAAmB;AAE7G,cAAM,UAAU,KAAK,gBAAgB;AACrC,eAAO,QAAQ,IAAI,QAAS,GAAE,QAAS,GAAE,QAAS,CAAA,EAAE,eAAe,GAAI;AACvE,eAAO,QAAQ,IAAI,QAAS,GAAE,QAAS,GAAE,QAAS,CAAA,EAAE,eAAe,GAAI;AAEvE,aAAK,gBAAgB,OAAO,OAAO,cAAc,OAAO,YAAY,OAAO;AAE3E,aAAK,sBAAsB;AAC3B,aAAK,mBAAmB;AAExB,cAAM,UAAU,KAAK,cAAe;AACpC,gBAAQ,YAAY;AACpB,gBAAQ,KAAK,KAAK,OAAO,IAAI;AAC7B,gBAAQ,KAAK,KAAK,OAAO,IAAI;AAC7B,gBAAQ,QAAQ,KAAK,OAAO,OAAO;AACnC,gBAAQ,QAAQ,KAAK,OAAO,OAAO;AACnC,gBAAQ,IAAI,KAAK,OAAO,GAAG;AAC3B,gBAAQ,IAAI,KAAK,OAAO,GAAG;AAC3B,gBAAQ,UAAU,OAAO;AACzB,gBAAQ,UAAU,OAAO;AACzB,gBAAQ,YAAY;AACpB,gBAAQ,YAAY;AACpB,gBAAQ,0BAA0B,IAAI,OAAO;AAE7C,aAAK,oBACF,KAAK,eAAe,KAAK,IAAI,KAAK,sBAAsB,OAAO,SAAS,KAAM,KAAK,OAAO;AAE7F,aAAK,oBAAoB,OAAO;AAAA,MACjC;AAED,WAAK,yBAAyB;AAC9B,WAAK,gBAAgB;AAAA,IACtB;AAAA,IAED,oBAAoB,SAAS;AAE3B,UAAI,QAAQ,aAAa,KAAK,cAAc,eAAe;AACzD,aAAK,uBAAuB,OAAO;AAEnC;AAAA,MACD;AAGD,WAAK,SAAS,WAAW,QAAQ,MAAM,QAAQ,IAAI;AACnD,UAAI,YAAY,KAAK,SAAS,OAAQ;AAEtC,UAAI,YAAY,MAAU;AACxB,aAAK,SAAS,IAAI,GAAG,GAAG,IAAI;AAC5B,oBAAY,KAAK,SAAS,OAAQ;AAAA,MACnC;AAED,YAAM,gBAAgB,QAAQ,UAAU,QAAQ,WAAW;AAC3D,YAAM,kBAAkB,QAAQ,YAAY,QAAQ,aAAa;AAEjE,YAAM,gBAAgB,KAAK,OAAO,KAAK,cAAc,YAAY,KAAK,IAAI,GAAG,QAAQ,SAAS;AAE9F,WAAK,UAAU,YAAY,QAAQ,MAAM,QAAQ,MAAM,GAAG;AAC1D,WAAK,aAAa,YAAY,QAAQ,SAAS,QAAQ,SAAS,GAAG;AACnE,YAAM,IAAI,KAAK;AAGf,WAAK,OAAO;AAAA,QACV,KAAK,SAAS,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,aAAa;AAAA,QAClD,KAAK,SAAS,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,aAAa;AAAA,QAClD,KAAK,SAAS,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,aAAa;AAAA,MACnD;AAED,WAAK,OAAO,eAAe,QAAQ,0BAA0B,SAAS;AACtE,WAAK,OAAO,IAAI,KAAK,SAAS;AAI9B,YAAM,cAAc,KAAK,cAAe;AACxC,kBAAY,KAAK,KAAK,QAAQ,IAAI;AAClC,kBAAY,KAAK,KAAK,KAAK,MAAM;AACjC,kBAAY,QAAQ,KAAK,QAAQ,OAAO;AACxC,kBAAY,QAAQ,KAAK,KAAK,YAAY;AAC1C,kBAAY,IAAI,KAAK,QAAQ,GAAG;AAChC,kBAAY,IAAI,KAAK,QAAQ,GAAG;AAChC,kBAAY,UAAU,QAAQ;AAC9B,kBAAY,UAAU;AACtB,kBAAY,YAAY,QAAQ;AAChC,kBAAY,YAAY;AACxB,kBAAY,0BAA0B,QAAQ,0BAA0B,KAAK,cAAc;AAC3F,kBAAY,YAAY,QAAQ,YAAY;AAE5C,YAAM,cAAc,KAAK,cAAe;AACxC,kBAAY,KAAK,KAAK,KAAK,MAAM;AACjC,kBAAY,KAAK,KAAK,QAAQ,IAAI;AAClC,kBAAY,QAAQ,KAAK,KAAK,YAAY;AAC1C,kBAAY,QAAQ,KAAK,QAAQ,OAAO;AACxC,WAAK,OAAO,aAAa,QAAQ,KAAK,KAAK,SAAS,WAAW;AAC/D,kBAAY,IAAI,aAAa,KAAK,UAAU,KAAK,MAAM,EAAE,UAAW;AACpE,kBAAY,IAAI,KAAK,QAAQ,GAAG;AAChC,kBAAY,UAAU;AACtB,kBAAY,UAAU,QAAQ;AAC9B,kBAAY,YAAY;AACxB,kBAAY,YAAY,QAAQ;AAChC,kBAAY,0BAA0B,QAAQ,0BAA0B,KAAK,cAAc;AAC3F,kBAAY,YAAY,QAAQ,YAAY;AAE5C,WAAK,oBAAoB,WAAW;AAEpC,WAAK,oBAAoB,WAAW;AAAA,IACrC;AAAA,IAED,YAAY,SAAS;AAGnB,WAAK,aAAa,WAAW,QAAQ,MAAM,QAAQ,IAAI,EAAE,UAAW;AAEpE,UAAI,KAAK,kBAAkB;AACzB,aAAK,8BAA8B,QAAQ,MAAM,QAAQ,KAAK,KAAK,cAAc,QAAQ,SAAS,CAAC;AAEnG,aAAK,mBAAmB;AAAA,MACzB;AAED,WAAK;AAAA,QACH,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,QAAQ;AAAA,MACT;AAED,WAAK,iBAAkB;AAAA,IACxB;AAAA,IAED,iCAAiC,KAAK,IAAI,UAAU,QAAQ;AAG1D,WAAK,KAAK,aAAa,IAAI,QAAQ,EAAE,eAAe,SAAS,iBAAgB,QAAQ;AACrF,WAAK,KAAK,KAAK,EAAE,EAAE,eAAe,CAAC,SAAS,iBAAgB,QAAQ;AAEpE,YAAM,IAAI,KAAK;AACf,YAAM,IAAI,KAAK;AAEf,QAAE,KAAK,GAAG,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI;AAExC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAChC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAChC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAEhC,QAAE,KAAK,GAAG,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI;AAExC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAChC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAChC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAEhC,QAAE,KAAK,EAAE,EAAE,eAAe,MAAM,EAAE,IAAI,GAAG;AAEzC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAChC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAChC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAEhC,WAAK,iBAAiB;AAAA,IACvB;AAAA,IAED,8BAA8B,KAAK,IAAI,UAAU,QAAQ,GAAG;AAG1D,WAAK,KAAK,aAAa,IAAI,QAAQ,EAAE,eAAe,SAAS,iBAAgB,QAAQ;AACrF,WAAK,KAAK,KAAK,EAAE,EAAE,eAAe,CAAC,SAAS,iBAAgB,QAAQ;AAEpE,YAAM,IAAI,KAAK;AACf,YAAM,IAAI,KAAK;AACf,YAAM,KAAK,KAAK;AAEhB,QAAE,KAAK,GAAG,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI;AAExC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAChC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAChC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAEhC,SAAG,KAAK,qBAAqB,IAAI;AACjC,SAAG,KAAK,qBAAqB,IAAI;AAEjC,QAAE,KAAK,GAAG,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI;AAExC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAChC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAChC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAEhC,SAAG,KAAK,qBAAqB,IAAI;AACjC,SAAG,KAAK,qBAAqB,IAAI;AAEjC,QAAE,KAAK,EAAE,EAAE,eAAe,MAAM,EAAE,IAAI,GAAG;AAEzC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAChC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAChC,QAAE,KAAK,mBAAmB,IAAI,EAAE;AAEhC,SAAG,KAAK,qBAAqB,IAAI;AACjC,SAAG,KAAK,qBAAqB,IAAI;AAEjC,WAAK,iBAAiB;AAAA,IACvB;AAAA,IAED,iBAAiB,QAAoB;AACnC,YAAM,UAAU,KAAK;AACrB,eAAS,KAAK,gBAAgB;AAE9B,cAAQ,KAAK,cAAc,IAAI,SAAS;AACxC,cAAQ,KAAK,cAAc,IAAI,SAAS;AACxC,cAAQ,KAAK,cAAc,IAAI,SAAS;AACxC,cAAQ,KAAK,cAAc,IAAI,SAAS;AACxC,cAAQ,KAAK,cAAc,IAAI,SAAS;AACxC,cAAQ,KAAK,cAAc,IAAI,SAAS;AACxC,cAAQ,KAAK,cAAc,IAAI,SAAS;AACxC,cAAQ,KAAK,cAAc,IAAI,SAAS;AACxC,cAAQ,KAAK,cAAc,IAAI,SAAS;AACxC,cAAQ,KAAK,cAAc,IAAI,SAAS;AACxC,cAAQ,KAAK,cAAc,IAAI,SAAS;AACxC,cAAQ,KAAK,cAAc,IAAI,SAAS;AACxC,cAAQ,KAAK,cAAc,IAAI,SAAS;AACxC,cAAQ,KAAK,cAAc,IAAI,SAAS;AACxC,cAAQ,KAAK,cAAc,IAAI,SAAS;AACxC,cAAQ,KAAK,cAAc,IAAI,SAAS;AACxC,cAAQ,KAAK,cAAc,IAAI,SAAS;AACxC,cAAQ,KAAK,cAAc,IAAI,SAAS;AAAA,IACzC;AAAA,IAED,uCAAuC;AACrC,YAAM,UAAU,KAAK,gBAAgB;AAErC,WAAK,yBAAyB,SAAU,SAAS,iBAAiB;AAGhE,cAAM,SAAS,gBAAgB;AAE/B,cAAM,SAAS,gBAAgB,cAAc;AAC7C,cAAM,YAAY,gBAAgB,cAAc;AAEhD,cAAM,SACJ,gBAAgB,cAAc,aAAa,OAAO,aAAa,IAC3D,CAAC,QAAS,IAAG,SACb,UAAU,KAAK,OAAO,WAAW,OAAO,oBAAoB,QAAQ,SAAS,IAAI,QAAO,IAAK;AAEnG,cAAM,QAAQ,gBAAgB,OAAO;AACrC,cAAM,eAAe,KAAK,MAAM,QAAQ,MAAM;AAE9C,cAAM,kBAAkB,aAAa,eAAe;AAEpD,cAAM,WAAW,QAAQ,UAAU,YAAY;AAE/C,YAAI,cAAc;AAElB,YAAI,UAAU;AACZ,wBAAc,gBAAgB;AAAA,QAE/B;AAED,YACE,OAAO,YAAY,gBAAgB,sBACnC,gBAAgB,aAAa,gBAAgB,cAC7C,QAAS,IAAG,aACZ;AACA,gBAAM,cAAc,gBAAgB,aAAc;AAElD,gBAAM,aAAa,gBAAgB,gBAAgB,QAAS;AAC5D,sBAAY,OAAO;AACnB,0BAAgB,gBAAgB,QAAQ,eAAe;AAEvD,sBAAY,YAAY,OAAO,YAAY;AAC3C,sBAAY,gBAAgB,KAAK,IAAI,GAAG,OAAO,gBAAgB,CAAC;AAEhE,sBAAY,QAAQ,IAAI,QAAS,GAAE,QAAS,GAAE,QAAS,CAAA,EAAE,eAAe,GAAI;AAC5E,sBAAY,QAAQ,IAAI,QAAS,GAAE,QAAS,GAAE,QAAS,CAAA,EAAE,eAAe,GAAI;AAC5E,sBAAY,IAAI,KAAK,OAAO,GAAG;AAC/B,sBAAY,IAAI,KAAK,OAAO,GAAG;AAC/B,sBAAY,UAAU,QAAQ,UAAU,gBAAgB,cAAc;AACtE,sBAAY,UAAU,KAAK;AAAA,YACzB,gBAAgB,cAAc;AAAA,YAC9B,QAAQ,UAAU,gBAAgB,cAAc;AAAA,UACjD;AAED,sBAAY,YAAY,SAAS,eAAe;AAChD,sBAAY,YAAY,YAAY,YAAY,SAAS;AAEzD,cAAI,CAAC,gBAAgB,cAAc,aAAa,OAAO,aAAa,GAAG;AACrE,wBAAY,YAAY,KAAK,IAAI,YAAY,WAAW,OAAO,SAAS;AACxE,wBAAY,YAAY,KAAK,IAAI,YAAY,WAAW,OAAO,SAAS;AAAA,UACzE;AAED,sBAAY,YAAY,OAAO,YAAY;AAC3C,sBAAY,YAAY,OAAO;AAC/B,sBAAY,eAAe,OAAO;AAClC,sBAAY,wBAAwB,OAAO;AAC3C,sBAAY,sBAAsB,OAAO;AAEzC,0BAAgB,iBAAiB,SAAS,QAAQ,aAAa,eAAe;AAE9E,0BAAgB,gBAAgB,QAAQ,UAAU;AAAA,QACnD;AAAA,MACF;AAED,YAAM,UAAU,IAAI,QAAS;AAC7B,YAAM,cAAc,IAAI,QAAS;AACjC,YAAM,WAAW,IAAI,QAAS;AAC9B,YAAM,SAAS,IAAI,QAAS;AAE5B,WAAK,mBAAmB,SAAU,SAAS,cAAc,aAAa,iBAAiB;AAIrF,wBAAgB,uBAAuB,SAAS,cAAc,aAAa,KAAK,KAAK,GAAG;AAAA,MACzF;AAED,WAAK,qBAAqB,SACxB,SACA,cACA,aACA,cACA,iBACA,oBACA;AAGA,oBAAY,KAAK,KAAK,QAAQ,IAAI;AAElC,gBAAQ,WAAW,aAAa,MAAM,aAAa,IAAI;AACvD,oBAAY,KAAK,OAAO,EAAE,UAAW;AACrC,gBAAQ,eAAe,QAAQ,aAAa,IAAI,QAAQ,cAAc,YAAY,aAAa;AAC/F,cAAM,SAAS,QAAQ,OAAQ;AAC/B,iBAAS,aAAa,aAAa,KAAK,WAAW;AACnD,cAAM,QAAQ,IAAI,KAAK,KAAK,QAAS;AACrC,iBAAS,eAAe,KAAK,IAAI,KAAK,CAAC;AACvC,eAAO,KAAK,aAAa,GAAG,EAAE,eAAe,KAAK,IAAI,KAAK,CAAC;AAE5D,oBAAY,KACT,KAAK,QAAQ,EACb,IAAI,MAAM,EACV,eAAe,SAAS,mBAAmB,qBAAqB,QAAO,KAAM,IAAI,oBAAoB,EACrG,IAAI,OAAO,EACX,IAAI,aAAa,IAAI;AAAA,MACzB;AAED,WAAK,yBAAyB,SAC5B,SACA,cACA,aACA,cACA,iBACA,oBACA;AAGA,oBAAY,KAAK,KAAK,QAAQ,IAAI;AAElC,gBAAQ,WAAW,aAAa,MAAM,aAAa,IAAI;AACvD,oBAAY,KAAK,OAAO,EAAE,UAAW;AACrC,gBAAQ,eAAe,QAAQ,aAAa,IAAI,QAAQ,eAAe,IAAI,QAAS,IAAG,KAAK,aAAa;AACzG,cAAM,SAAS,QAAQ,OAAQ;AAC/B,iBAAS,aAAa,aAAa,KAAK,WAAW;AACnD,cAAM,QAAQ,IAAI,KAAK,KAAK,QAAS;AACrC,iBAAS,eAAe,KAAK,IAAI,KAAK,CAAC;AACvC,eAAO,KAAK,aAAa,GAAG,EAAE,eAAe,KAAK,IAAI,KAAK,CAAC;AAE5D,oBAAY,KACT,KAAK,QAAQ,EACb,IAAI,MAAM,EACV,eAAe,SAAS,mBAAmB,qBAAqB,QAAO,KAAM,IAAI,oBAAoB,EACrG,IAAI,OAAO,EACX,IAAI,aAAa,IAAI;AAAA,MACzB;AAAA,IACF;AAAA,IAED,eAAe;AACb,aAAO;AAAA,QACL,MAAM;AAAA,QACN,eAAe;AAAA,QACf,WAAW;AAAA,QACX,MAAM,IAAI,QAAS;AAAA,QACnB,MAAM,IAAI,QAAS;AAAA,QACnB,SAAS,IAAI,QAAS;AAAA,QACtB,SAAS,IAAI,QAAS;AAAA,QACtB,KAAK,IAAI,QAAS;AAAA,QAClB,KAAK,IAAI,QAAS;AAAA,QAClB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,QACX,cAAc;AAAA,QACd,uBAAuB;AAAA,QACvB,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,MACrB;AAAA,IACF;AAAA,IAED,gBAAgB;AACd,aAAO;AAAA,QACL,WAAW;AAAA,QACX,MAAM,IAAI,QAAS;AAAA,QACnB,MAAM,IAAI,QAAS;AAAA,QACnB,SAAS,IAAI,QAAS;AAAA,QACtB,SAAS,IAAI,QAAS;AAAA,QACtB,KAAK,IAAI,QAAS;AAAA,QAClB,KAAK,IAAI,QAAS;AAAA,QAClB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,WAAW;AAAA,QACX,WAAW;AAAA,QACX,yBAAyB;AAAA,MAC1B;AAAA,IACF;AAAA,IAED,gBAAgB;AACd,aAAO,KAAK,YAAY,KAAK,qBAAqB;AAAA,IACnD;AAAA,IAED,KAAK,QAAQ;AACX,YAAM,KAAK,MAAM;AAEjB,WAAK,KAAK,iBAAgB,eAAe,CAAA,GAAI,OAAO,aAAa,CAAC;AAElE,aAAO;AAAA,IACR;AAAA,IAED,QAAQ;AACN,aAAO,IAAI,KAAK,YAAY,iBAAgB,eAAe,IAAI,KAAK,aAAa,CAAC;AAAA,IACnF;AAAA,EACF;AA3yBD,MAAMA,mBAAN;AAEE;AAAA,gBAFIA,kBAEG,mBAAkB;AACzB,gBAHIA,kBAGG,cAAa;AACpB,gBAJIA,kBAIG,mBAAkB;AACzB,gBALIA,kBAKG,cAAa;AACpB,gBANIA,kBAMG,iBAAgB;AACvB,gBAPIA,kBAOG,oBAAmB;AAE1B,gBATIA,kBASG,YAAW,KAAK,IAAK,KAAK,KAAK,KAAM,GAAG;AAC/C,gBAVIA,kBAUG,YAAW,KAAK,IAAK,KAAK,KAAK,KAAM,GAAG;AAmyBjD,SAAOA;AACT,GAAC;"}