/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
/**
 * Enum for semantic labels.
 * For more details, see the {@link https://github.com/immersive-web/semantic-labels | Semantic Labels Documentation}.
 */
export declare enum XRSemanticLabels {
    Desk = "desk",
    Couch = "couch",
    Floor = "floor",
    Ceiling = "ceiling",
    Wall = "wall",
    Door = "door",
    Window = "window",
    Table = "table",
    Shelf = "shelf",
    Bed = "bed",
    Screen = "screen",
    Lamp = "lamp",
    Plant = "plant",
    WallArt = "wall art",
    GlobalMesh = "global mesh",
    Other = "other"
}
//# sourceMappingURL=labels.d.ts.map