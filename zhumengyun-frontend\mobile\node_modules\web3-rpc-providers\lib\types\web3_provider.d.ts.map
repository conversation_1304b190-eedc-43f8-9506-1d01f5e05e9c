{"version": 3, "file": "web3_provider.d.ts", "sourceRoot": "", "sources": ["../../src/web3_provider.ts"], "names": [], "mappings": "AAiBA,OAAqB,EAAE,mBAAmB,EAAE,MAAM,qBAAqB,CAAC;AAExE,OAAO,EACN,eAAe,EACf,aAAa,EACb,mBAAmB,EACnB,eAAe,EACf,gBAAgB,EAChB,aAAa,EACb,cAAc,EACd,iBAAiB,EACjB,WAAW,EACX,gBAAgB,EAChB,gCAAgC,EAChC,yBAAyB,EACzB,gCAAgC,EAChC,kBAAkB,EAClB,yBAAyB,EACzB,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAC7C,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,YAAY,CAAC;AAW/D,8BAAsB,oBAAoB,CACzC,GAAG,SAAS,WAAW,GAAG,eAAe,CACxC,SAAQ,eAAe;IACjB,QAAQ,EAAG,gBAAgB,CAAC;IACnC,SAAgB,SAAS,EAAE,SAAS,CAAC;aAErB,SAAS,CACxB,OAAO,EAAE,OAAO,EAChB,SAAS,EAAE,SAAS,EACpB,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,MAAM,GACV,MAAM;gBAGR,OAAO,EAAE,OAAO,EAChB,SAAS,EAAE,SAAS,EACpB,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,MAAM,EACZ,qBAAqB,CAAC,EAAE,mBAAmB,GAAG,aAAa;IAoC/C,OAAO,CACnB,MAAM,SAAS,aAAa,CAAC,GAAG,CAAC,EACjC,UAAU,GAAG,iBAAiB,CAAC,GAAG,EAAE,MAAM,CAAC,EAE3C,OAAO,EAAE,cAAc,CAAC,eAAe,EAAE,MAAM,CAAC,EAChD,cAAc,CAAC,EAAE,WAAW,GAC1B,OAAO,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;IAW1C,SAAS,IAAI,kBAAkB;IAG/B,qBAAqB,IAAI,OAAO;IAGhC,IAAI,CACV,IAAI,EAAE,YAAY,EAClB,QAAQ,EAAE,gCAAgC,CAAC,gBAAgB,CAAC,GAC1D,IAAI;IACA,IAAI,CAAC,CAAC,GAAG,aAAa,EAC5B,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,gCAAgC,CAAC,eAAe,CAAC,GAAG,yBAAyB,CAAC,CAAC,CAAC,GACxF,IAAI;IACA,IAAI,CACV,IAAI,EAAE,SAAS,EACf,QAAQ,EAAE,gCAAgC,CAAC,mBAAmB,CAAC,GAC7D,IAAI;IACA,IAAI,CAAC,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,gCAAgC,CAAC,MAAM,CAAC,GAAG,IAAI;IACpF,IAAI,CACV,IAAI,EAAE,iBAAiB,EACvB,QAAQ,EAAE,gCAAgC,CAAC,MAAM,EAAE,CAAC,GAClD,IAAI;IAYA,kBAAkB,CAAC,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAGxC,OAAO,IAAI,IAAI;IAGf,UAAU,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI;IAGxE,KAAK,IAAI,IAAI;IAIb,EAAE,CACR,IAAI,EAAE,YAAY,EAClB,QAAQ,EAAE,gCAAgC,CAAC,gBAAgB,CAAC,GAC1D,IAAI;IACA,EAAE,CAAC,CAAC,GAAG,aAAa,EAC1B,IAAI,EAAE,MAAM,EACZ,QAAQ,EACL,gCAAgC,CAAC,eAAe,CAAC,GACjD,gCAAgC,CAAC,CAAC,CAAC,GACpC,IAAI;IACA,EAAE,CAAC,CAAC,GAAG,aAAa,EAC1B,IAAI,EAAE,MAAM,EACZ,QAAQ,EACL,gCAAgC,CAAC,eAAe,CAAC,GACjD,gCAAgC,CAAC,CAAC,CAAC,GACpC,IAAI;IACA,EAAE,CACR,IAAI,EAAE,SAAS,EACf,QAAQ,EAAE,gCAAgC,CAAC,mBAAmB,CAAC,GAC7D,IAAI;IACA,EAAE,CAAC,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,gCAAgC,CAAC,MAAM,CAAC,GAAG,IAAI;IAClF,EAAE,CAAC,IAAI,EAAE,iBAAiB,EAAE,QAAQ,EAAE,gCAAgC,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI;IAWvF,cAAc,CACpB,IAAI,EAAE,YAAY,EAClB,QAAQ,EAAE,gCAAgC,CAAC,gBAAgB,CAAC,GAC1D,IAAI;IACA,cAAc,CAAC,CAAC,GAAG,aAAa,EACtC,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,gCAAgC,CAAC,eAAe,CAAC,GAAG,yBAAyB,CAAC,CAAC,CAAC,GACxF,IAAI;IACA,cAAc,CACpB,IAAI,EAAE,SAAS,EACf,QAAQ,EAAE,gCAAgC,CAAC,mBAAmB,CAAC,GAC7D,IAAI;IACA,cAAc,CACpB,IAAI,EAAE,cAAc,EACpB,QAAQ,EAAE,gCAAgC,CAAC,MAAM,CAAC,GAChD,IAAI;IACA,cAAc,CACpB,IAAI,EAAE,iBAAiB,EACvB,QAAQ,EAAE,gCAAgC,CAAC,MAAM,EAAE,CAAC,GAClD,IAAI;CAUP"}