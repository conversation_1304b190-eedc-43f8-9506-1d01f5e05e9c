import { <PERSON><PERSON>er<PERSON><PERSON><PERSON>, Mesh, MeshBasicMaterial, WebGLRenderer } from 'three';
import { XRLayerOptions, XRLayerProperties, XRLayerSrc } from '../layer.js';
import { XRStore } from '../store.js';
export declare class XRLayer extends Mesh<BufferGeometry, MeshBasicMaterial> {
    private readonly store;
    private readonly options;
    private properties;
    private layerRenderOrder;
    private layerEntry?;
    private cleanup?;
    private cleanupSubscription?;
    constructor(store: XRStore<any>, renderer: WebGLRenderer, options: XRLayerOptions & {
        src: XRLayerSrc;
    }, properties?: XRLayerProperties, layerRenderOrder?: number);
    setLayerRenderOrder(layerRenderOrder: number): void;
    setProperties(properties?: XRLayerProperties): void;
    destroy(): void;
    onBeforeRender(): void;
}
