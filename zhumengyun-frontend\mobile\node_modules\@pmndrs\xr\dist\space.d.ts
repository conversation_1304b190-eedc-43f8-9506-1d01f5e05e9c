import { Matrix4, Object3D } from 'three';
export declare function createGetXRSpaceMatrix(space: XRSpace, referenceSpace: XRSpace | (() => XRSpace | undefined)): (target: Matrix4, frame: XR<PERSON>rame | undefined) => boolean;
export declare function getSpaceFromAncestors(object: Object3D, origin: Object3D | undefined, originReferenceSpace: XRReferenceSpace, targetOffsetMatrix?: Matrix4): XRSpace;
export declare function getSpaceFromAncestors(object: Object3D, origin?: Object3D, originReferenceSpace?: XRReferenceSpace, targetOffsetMatrix?: Matrix4): XRSpace | undefined;
