import*as t from"../../core/i18n/i18n.js";import*as e from"../../core/sdk/sdk.js";import*as n from"../../core/common/common.js";import*as i from"../../core/host/host.js";import*as o from"../../core/platform/platform.js";import*as r from"../../ui/components/icon_button/icon_button.js";import*as s from"../../ui/legacy/legacy.js";import*as a from"../../ui/visual_logging/visual_logging.js";const l={noThrottling:"No throttling",noInternetConnectivity:"No internet connectivity",lowendMobile:"Low-end mobile",slowGXCpuSlowdown:"Slow 3G & 6x CPU slowdown",midtierMobile:"Mid-tier mobile",fastGXCpuSlowdown:"Fast 3G & 4x CPU slowdown",custom:"Custom",checkNetworkAndPerformancePanels:"Check Network and Performance panels"},c=t.i18n.registerUIStrings("panels/mobile_throttling/ThrottlingPresets.ts",l),d=t.i18n.getLocalizedString.bind(void 0,c);class g{static getNoThrottlingConditions(){return{title:"function"==typeof e.NetworkManager.NoThrottlingConditions.title?e.NetworkManager.NoThrottlingConditions.title():e.NetworkManager.NoThrottlingConditions.title,description:d(l.noThrottling),network:e.NetworkManager.NoThrottlingConditions,cpuThrottlingRate:e.CPUThrottlingManager.CPUThrottlingRates.NoThrottling,jslogContext:"no-throttling"}}static getOfflineConditions(){return{title:"function"==typeof e.NetworkManager.OfflineConditions.title?e.NetworkManager.OfflineConditions.title():e.NetworkManager.OfflineConditions.title,description:d(l.noInternetConnectivity),network:e.NetworkManager.OfflineConditions,cpuThrottlingRate:e.CPUThrottlingManager.CPUThrottlingRates.NoThrottling,jslogContext:"offline"}}static getLowEndMobileConditions(){return{title:d(l.lowendMobile),description:d(l.slowGXCpuSlowdown),network:e.NetworkManager.Slow3GConditions,cpuThrottlingRate:e.CPUThrottlingManager.CPUThrottlingRates.LowEndMobile,jslogContext:"low-end-mobile"}}static getMidTierMobileConditions(){return{title:d(l.midtierMobile),description:d(l.fastGXCpuSlowdown),network:e.NetworkManager.Slow4GConditions,cpuThrottlingRate:e.CPUThrottlingManager.CPUThrottlingRates.MidTierMobile,jslogContext:"mid-tier-mobile"}}static getCustomConditions(){return{title:d(l.custom),description:d(l.checkNetworkAndPerformancePanels),jslogContext:"custom"}}static getMobilePresets(){return[g.getMidTierMobileConditions(),g.getLowEndMobileConditions(),g.getCustomConditions()]}static getAdvancedMobilePresets(){return[g.getOfflineConditions()]}static networkPresets=[e.NetworkManager.Fast4GConditions,e.NetworkManager.Slow4GConditions,e.NetworkManager.Slow3GConditions,e.NetworkManager.OfflineConditions];static cpuThrottlingPresets=[e.CPUThrottlingManager.CPUThrottlingRates.NoThrottling,e.CPUThrottlingManager.CPUThrottlingRates.MidTierMobile,e.CPUThrottlingManager.CPUThrottlingRates.LowEndMobile,e.CPUThrottlingManager.CPUThrottlingRates.ExtraSlow]}globalThis.MobileThrottling=globalThis.MobileThrottling||{},globalThis.MobileThrottling.networkPresets=g.networkPresets;var h=Object.freeze({__proto__:null,ThrottlingPresets:g});const u={disabled:"Disabled",presets:"Presets",custom:"Custom"},p=t.i18n.registerUIStrings("panels/mobile_throttling/NetworkThrottlingSelector.ts",u),C=t.i18n.getLocalizedString.bind(void 0,p);class w{populateCallback;selectCallback;customNetworkConditionsSetting;options;constructor(t,n,i){this.populateCallback=t,this.selectCallback=n,this.customNetworkConditionsSetting=i,this.customNetworkConditionsSetting.addChangeListener(this.populateOptions,this),e.NetworkManager.MultitargetNetworkManager.instance().addEventListener("ConditionsChanged",(()=>{this.networkConditionsChanged()}),this),this.populateOptions()}revealAndUpdate(){n.Revealer.reveal(this.customNetworkConditionsSetting),this.networkConditionsChanged()}optionSelected(t){e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(t)}populateOptions(){const t={title:C(u.disabled),items:[e.NetworkManager.NoThrottlingConditions]},n={title:C(u.presets),items:g.networkPresets},i={title:C(u.custom),items:this.customNetworkConditionsSetting.get()};if(this.options=this.populateCallback([t,n,i]),!this.networkConditionsChanged())for(let t=this.options.length-1;t>=0;t--)if(this.options[t]){this.optionSelected(this.options[t]);break}}networkConditionsChanged(){const t=e.NetworkManager.MultitargetNetworkManager.instance().networkConditions();for(let n=0;n<this.options.length;++n){const i=this.options[n];if(i&&e.NetworkManager.networkConditionsEqual(t,i))return this.selectCallback(n),!0}return!1}}var k=Object.freeze({__proto__:null,NetworkThrottlingSelector:w});const b={sS:"{PH1}: {PH2}",add:"Add…",addS:"Add {PH1}",offline:"Offline",forceDisconnectedFromNetwork:"Force disconnected from network",throttling:"Throttling",cpuThrottlingIsEnabled:"CPU throttling is enabled",cpuThrottling:"CPU throttling",noThrottling:"No throttling",dSlowdown:"{PH1}× slowdown",excessConcurrency:"Exceeding the default value may degrade system performance.",resetConcurrency:"Reset to the default value",hardwareConcurrency:"Hardware concurrency",hardwareConcurrencySettingTooltip:"Override the value reported by navigator.hardwareConcurrency on the page",hardwareConcurrencyIsEnabled:"Hardware concurrency override is enabled"},m=t.i18n.registerUIStrings("panels/mobile_throttling/ThrottlingManager.ts",b),v=t.i18n.getLocalizedString.bind(void 0,m);let f;class T{cpuThrottlingControls;cpuThrottlingRates;customNetworkConditionsSetting;currentNetworkThrottlingConditionsSetting;lastNetworkThrottlingConditions;cpuThrottlingManager;#t=!1;get hardwareConcurrencyOverrideEnabled(){return this.#t}constructor(){this.cpuThrottlingManager=e.CPUThrottlingManager.CPUThrottlingManager.instance(),this.cpuThrottlingControls=new Set,this.cpuThrottlingRates=g.cpuThrottlingPresets,this.customNetworkConditionsSetting=n.Settings.Settings.instance().moduleSetting("custom-network-conditions"),this.currentNetworkThrottlingConditionsSetting=n.Settings.Settings.instance().createSetting("preferred-network-condition",e.NetworkManager.NoThrottlingConditions),this.currentNetworkThrottlingConditionsSetting.setSerializer(new e.NetworkManager.ConditionsSerializer),e.NetworkManager.MultitargetNetworkManager.instance().addEventListener("ConditionsChanged",(()=>{this.lastNetworkThrottlingConditions=this.currentNetworkThrottlingConditionsSetting.get(),this.currentNetworkThrottlingConditionsSetting.set(e.NetworkManager.MultitargetNetworkManager.instance().networkConditions())})),this.isDirty()&&e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(this.currentNetworkThrottlingConditionsSetting.get())}static instance(t={forceNew:null}){const{forceNew:e}=t;return f&&!e||(f=new T),f}decorateSelectWithNetworkThrottling(t){let e=[];const n=new w((function(n){t.removeChildren(),e=[];for(let i=0;i<n.length;++i){const r=n[i],l=t.createChild("optgroup");l.label=r.title;for(const t of r.items){const n="function"==typeof t.title?t.title():t.title,i=new Option(n,n);s.ARIAUtils.setLabel(i,v(b.sS,{PH1:r.title,PH2:n})),i.setAttribute("jslog",`${a.item(o.StringUtilities.toKebabCase(t.i18nTitleKey||n)).track({click:!0})}`),l.appendChild(i),e.push(t)}if(i===n.length-1){const t=new Option(v(b.add),v(b.add));s.ARIAUtils.setLabel(t,v(b.addS,{PH1:r.title})),t.setAttribute("jslog",`${a.action("add").track({click:!0})}`),l.appendChild(t),e.push(null)}}return e}),(function(e){t.selectedIndex!==e&&(t.selectedIndex=e)}),this.customNetworkConditionsSetting);return t.setAttribute("jslog",`${a.dropDown().track({change:!0}).context(this.currentNetworkThrottlingConditionsSetting.name)}`),t.addEventListener("change",(function(){if(t.selectedIndex===t.options.length-1)n.revealAndUpdate();else{const i=e[t.selectedIndex];i&&n.optionSelected(i)}}),!1),n}createOfflineToolbarCheckbox(){const t=new s.Toolbar.ToolbarCheckbox(v(b.offline),v(b.forceDisconnectedFromNetwork),function(){if(t.checked())e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(e.NetworkManager.OfflineConditions);else{const t=this.lastNetworkThrottlingConditions.download||this.lastNetworkThrottlingConditions.upload?this.lastNetworkThrottlingConditions:e.NetworkManager.NoThrottlingConditions;e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(t)}}.bind(this));return t.element.setAttribute("jslog",`${a.toggle("disconnect-from-network").track({click:!0})}`),e.NetworkManager.MultitargetNetworkManager.instance().addEventListener("ConditionsChanged",(function(){t.setChecked(e.NetworkManager.MultitargetNetworkManager.instance().isOffline())})),t.setChecked(e.NetworkManager.MultitargetNetworkManager.instance().isOffline()),t}createMobileThrottlingButton(){const t=new s.Toolbar.ToolbarMenuButton((function(t){for(let o=0;o<e.length;++o){const r=e[o];r&&(r.title===g.getCustomConditions().title&&r.description===g.getCustomConditions().description||t.defaultSection().appendCheckboxItem(r.title,i.optionSelected.bind(i,r),{checked:n===o,jslogContext:r.jslogContext}))}}),void 0,void 0,"mobile-throttling");t.setTitle(v(b.throttling)),t.setDarkText();let e=[],n=-1;const i=new y((function(t){e=[];for(const n of t){for(const t of n.items)e.push(t);e.push(null)}return e}),(function(i){n=i;const o=e[i];o&&(t.setText(o.title),t.setTitle(`${o.title} ${o.description}`))}));return t}updatePanelIcon(){const t=[];this.cpuThrottlingManager.cpuThrottlingRate()!==e.CPUThrottlingManager.CPUThrottlingRates.NoThrottling&&t.push(v(b.cpuThrottlingIsEnabled)),this.hardwareConcurrencyOverrideEnabled&&t.push(v(b.hardwareConcurrencyIsEnabled)),s.InspectorView.InspectorView.instance().setPanelWarnings("timeline",t)}setCPUThrottlingRate(t){this.cpuThrottlingManager.setCPUThrottlingRate(t),t!==e.CPUThrottlingManager.CPUThrottlingRates.NoThrottling&&i.userMetrics.actionTaken(i.UserMetrics.Action.CpuThrottlingEnabled);const n=this.cpuThrottlingRates.indexOf(t);for(const t of this.cpuThrottlingControls)t.setSelectedIndex(n);this.updatePanelIcon()}createCPUThrottlingSelector(){const t=new s.Toolbar.ToolbarComboBox((t=>this.setCPUThrottlingRate(this.cpuThrottlingRates[t.target.selectedIndex])),v(b.cpuThrottling),"","cpu-throttling-selector");this.cpuThrottlingControls.add(t);const e=this.cpuThrottlingManager.cpuThrottlingRate();for(let n=0;n<this.cpuThrottlingRates.length;++n){const i=this.cpuThrottlingRates[n],o=1===i?v(b.noThrottling):v(b.dSlowdown,{PH1:i}),r=1===i?"cpu-no-throttling":`cpu-throttled-${i}`,s=t.createOption(o,r);t.addOption(s),e===i&&t.setSelectedIndex(n)}return t}createHardwareConcurrencySelector(){const t=new s.Toolbar.ToolbarItem(s.UIUtils.createInput("devtools-text-input","number","hardware-concurrency-selector"));t.setTitle(v(b.hardwareConcurrencySettingTooltip));const e=t.element;e.min="1",t.setEnabled(!1);const n=new s.Toolbar.ToolbarCheckbox(v(b.hardwareConcurrency),v(b.hardwareConcurrencySettingTooltip),void 0,"hardware-concurrency-toggle"),i=new s.Toolbar.ToolbarButton("Reset concurrency","undo",void 0,"hardware-concurrency-reset");i.setTitle(v(b.resetConcurrency));const o=new r.Icon.Icon;o.data={iconName:"warning-filled",color:"var(--icon-warning)",width:"14px",height:"14px"};const a=new s.Toolbar.ToolbarItem(o);return a.setTitle(v(b.excessConcurrency)),n.inputElement.disabled=!0,i.element.classList.add("timeline-concurrency-hidden"),a.element.classList.add("timeline-concurrency-hidden"),this.cpuThrottlingManager.getHardwareConcurrency().then((o=>{if(void 0===o)return;const r=t=>{t>=1&&this.cpuThrottlingManager.setHardwareConcurrency(t),t>o?a.element.classList.remove("timeline-concurrency-hidden"):a.element.classList.add("timeline-concurrency-hidden"),t===o?i.element.classList.add("timeline-concurrency-hidden"):i.element.classList.remove("timeline-concurrency-hidden")};e.value=`${o}`,e.oninput=()=>r(Number(e.value)),n.inputElement.disabled=!1,n.inputElement.addEventListener("change",(()=>{this.#t=n.checked(),this.updatePanelIcon(),t.setEnabled(this.hardwareConcurrencyOverrideEnabled),r(this.hardwareConcurrencyOverrideEnabled?Number(e.value):o)})),i.addEventListener("Click",(()=>{e.value=`${o}`,r(o)}))})),{input:t,reset:i,warning:a,toggle:n}}setHardwareConcurrency(t){this.cpuThrottlingManager.setHardwareConcurrency(t)}isDirty(){const t=e.NetworkManager.MultitargetNetworkManager.instance().networkConditions(),n=this.currentNetworkThrottlingConditionsSetting.get();return!e.NetworkManager.networkConditionsEqual(t,n)}}function M(){return T.instance()}var N=Object.freeze({__proto__:null,ThrottlingManager:T,ActionDelegate:class{handleAction(t,n){return"network-conditions.network-online"===n?(e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(e.NetworkManager.NoThrottlingConditions),!0):"network-conditions.network-low-end-mobile"===n?(e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(e.NetworkManager.Slow3GConditions),!0):"network-conditions.network-mid-tier-mobile"===n?(e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(e.NetworkManager.Slow4GConditions),!0):"network-conditions.network-offline"===n&&(e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(e.NetworkManager.OfflineConditions),!0)}},throttlingManager:M});const x={disabled:"Disabled",presets:"Presets",advanced:"Advanced"},S=t.i18n.registerUIStrings("panels/mobile_throttling/MobileThrottlingSelector.ts",x),P=t.i18n.getLocalizedString.bind(void 0,S);class y{populateCallback;selectCallback;options;constructor(t,n){this.populateCallback=t,this.selectCallback=n,e.CPUThrottlingManager.CPUThrottlingManager.instance().addEventListener("RateChanged",this.conditionsChanged,this),e.NetworkManager.MultitargetNetworkManager.instance().addEventListener("ConditionsChanged",this.conditionsChanged,this),this.options=this.populateOptions(),this.conditionsChanged()}optionSelected(t){e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(t.network),M().setCPUThrottlingRate(t.cpuThrottlingRate)}populateOptions(){const t={title:P(x.disabled),items:[g.getNoThrottlingConditions()]},e={title:P(x.presets),items:g.getMobilePresets()},n={title:P(x.advanced),items:g.getAdvancedMobilePresets()};return this.populateCallback([t,e,n])}conditionsChanged(){const t=e.NetworkManager.MultitargetNetworkManager.instance().networkConditions(),n=e.CPUThrottlingManager.CPUThrottlingManager.instance().cpuThrottlingRate();for(let e=0;e<this.options.length;++e){const i=this.options[e];if(i&&"network"in i&&i.network===t&&i.cpuThrottlingRate===n)return void this.selectCallback(e)}const i=g.getCustomConditions();for(let t=0;t<this.options.length;++t){const e=this.options[t];if(e&&e.title===i.title&&e.description===i.description)return void this.selectCallback(t)}}}var I=Object.freeze({__proto__:null,MobileThrottlingSelector:y});const L={networkThrottlingIsEnabled:"Network throttling is enabled",requestsMayBeOverridden:"Requests may be overridden locally, see the Sources panel",requestsMayBeBlocked:"Requests may be blocked, see the Network request blocking panel",acceptedEncodingOverrideSet:"The set of accepted `Content-Encoding` headers has been modified by DevTools, see the Network conditions panel"},U=t.i18n.registerUIStrings("panels/mobile_throttling/NetworkPanelIndicator.ts",L),R=t.i18n.getLocalizedString.bind(void 0,U);var A=Object.freeze({__proto__:null,NetworkPanelIndicator:class{constructor(){if(!s.InspectorView.InspectorView.instance().hasPanel("network"))return;const t=e.NetworkManager.MultitargetNetworkManager.instance();function i(){const n=[];t.isThrottling()&&n.push(R(L.networkThrottlingIsEnabled)),e.NetworkManager.MultitargetNetworkManager.instance().isIntercepting()&&n.push(R(L.requestsMayBeOverridden)),t.isBlocking()&&n.push(R(L.requestsMayBeBlocked)),t.isAcceptedEncodingOverrideSet()&&n.push(R(L.acceptedEncodingOverrideSet)),s.InspectorView.InspectorView.instance().setPanelWarnings("network",n)}t.addEventListener("ConditionsChanged",i),t.addEventListener("BlockedPatternsChanged",i),t.addEventListener("InterceptorsChanged",i),t.addEventListener("AcceptedEncodingsChanged",i),n.Settings.Settings.instance().moduleSetting("cache-disabled").addChangeListener(i,this),i()}}});const E=new CSSStyleSheet;E.replaceSync(':host{overflow:hidden}.header{padding:0 0 6px;border-bottom:1px solid var(--sys-color-divider);font-size:18px;font-weight:normal;flex:none}.add-conditions-button{flex:none;margin:10px 5px;min-width:140px;align-self:flex-start}.conditions-list{min-width:640px;flex:auto}.conditions-list-item{padding:3px 5px;height:30px;display:flex;align-items:center;position:relative;flex:auto 1 1}.conditions-list-text{flex:0 0 80px;user-select:none;color:var(--sys-color-on-surface);text-align:center;position:relative;& > input{scroll-margin-left:5px}}.conditions-list-text:last-child{flex-basis:100px;text-align:left}.conditions-list-title{text-align:start;display:flex;flex:auto;align-items:flex-start}.conditions-list-title-text{flex:auto}.conditions-list-separator{flex:0 0 1px;background-color:var(--sys-color-divider);height:30px;margin:0 4px}.conditions-list-separator-invisible{visibility:hidden;height:100%!important}.conditions-edit-row{flex:none;display:flex;flex-direction:row;margin:6px 5px}.conditions-edit-row input{&[type="checkbox"]{display:block;margin:auto;top:6px}&:not([type="checkbox"]){width:100%;text-align:inherit}}.conditions-edit-optional{position:absolute;bottom:-20px;right:0;color:var(--sys-color-state-disabled)}.editor-buttons{margin-top:10px}\n/*# sourceURL=throttlingSettingsTab.css */\n');const O={networkThrottlingProfiles:"Network Throttling Profiles",addCustomProfile:"Add custom profile...",dms:"{PH1} `ms`",profileName:"Profile Name",download:"Download",upload:"Upload",latency:"Latency",packetLoss:"Packet Loss",percent:"percent",packetQueueLength:"Packet Queue Length",packetReordering:"Packet Reordering",packet:"packet",optional:"optional",profileNameCharactersLengthMust:"Profile Name characters length must be between 1 to {PH1} inclusive",sMustBeANumberBetweenSkbsToSkbs:"{PH1} must be a number between {PH2} `kbit/s` to {PH3} `kbit/s` inclusive",latencyMustBeAnIntegerBetweenSms:"Latency must be an integer between {PH1} `ms` to {PH2} `ms` inclusive",packetLossMustBeAnIntegerBetweenSpct:"Packet Loss must be a number between {PH1} `%` to {PH2} `%` inclusive",packetQueueLengthMustBeAnIntegerGreaterOrEqualToZero:"Packet Queue Length must be greater or equal to 0",dskbits:"{PH1} `kbit/s`",fsmbits:"{PH1} `Mbit/s`",on:"On",off:"Off"},H=t.i18n.registerUIStrings("panels/mobile_throttling/ThrottlingSettingsTab.ts",O),_=t.i18n.getLocalizedString.bind(void 0,H);class B extends s.Widget.VBox{list;customSetting;editor;constructor(){super(!0),this.element.setAttribute("jslog",`${a.pane("throttling-conditions")}`);const t=this.contentElement.createChild("div","header");t.textContent=_(O.networkThrottlingProfiles),s.ARIAUtils.markAsHeading(t,1);const e=s.UIUtils.createTextButton(_(O.addCustomProfile),this.addButtonClicked.bind(this),{className:"add-conditions-button",jslogContext:"network.add-conditions"});this.contentElement.appendChild(e),this.list=new s.ListWidget.ListWidget(this),this.list.element.classList.add("conditions-list"),this.list.show(this.contentElement),this.customSetting=n.Settings.Settings.instance().moduleSetting("custom-network-conditions"),this.customSetting.addChangeListener(this.conditionsUpdated,this),this.setDefaultFocusedElement(e)}wasShown(){super.wasShown(),this.list.registerCSSFiles([E]),this.registerCSSFiles([E]),this.conditionsUpdated()}conditionsUpdated(){this.list.clear();const t=this.customSetting.get();for(let e=0;e<t.length;++e)this.list.appendItem(t[e],!0);this.list.appendSeparator()}addButtonClicked(){this.list.addNewItem(this.customSetting.get().length,{title:()=>"",download:-1,upload:-1,latency:0,packetLoss:0,packetReordering:!1})}renderItem(t,e){const n=document.createElement("div");n.classList.add("conditions-list-item");const i=n.createChild("div","conditions-list-text conditions-list-title").createChild("div","conditions-list-title-text"),o=this.retrieveOptionsTitle(t);return i.textContent=o,s.Tooltip.Tooltip.install(i,o),n.createChild("div","conditions-list-separator"),n.createChild("div","conditions-list-text").textContent=j(t.download),n.createChild("div","conditions-list-separator"),n.createChild("div","conditions-list-text").textContent=j(t.upload),n.createChild("div","conditions-list-separator"),n.createChild("div","conditions-list-text").textContent=_(O.dms,{PH1:t.latency}),n.createChild("div","conditions-list-separator"),n.createChild("div","conditions-list-text").textContent=function(t){if(t<0)return"";return String(t)+"%"}(t.packetLoss??0),n.createChild("div","conditions-list-separator"),n.createChild("div","conditions-list-text").textContent=String(t.packetQueueLength??0),n.createChild("div","conditions-list-separator"),n.createChild("div","conditions-list-text").textContent=t.packetReordering?_(O.on):_(O.off),n}removeItemRequested(t,e){const n=this.customSetting.get();n.splice(e,1),this.customSetting.set(n)}retrieveOptionsTitle(t){return"function"==typeof t.title?t.title():t.title}commitEdit(t,e,n){t.title=e.control("title").value.trim();const i=e.control("download").value.trim();t.download=i?125*parseInt(i,10):-1;const o=e.control("upload").value.trim();t.upload=o?125*parseInt(o,10):-1;const r=e.control("latency").value.trim();t.latency=r?parseInt(r,10):0;const s=e.control("packetLoss").value.trim();t.packetLoss=s?parseFloat(s):0;const a=e.control("packetQueueLength").value.trim();t.packetQueueLength=a?parseFloat(a):0;const l=e.control("packetReordering").checked;t.packetReordering=l;const c=this.customSetting.get();n&&c.push(t),this.customSetting.set(c)}beginEdit(t){const e=this.createEditor();return e.control("title").value=this.retrieveOptionsTitle(t),e.control("download").value=t.download<=0?"":String(t.download/125),e.control("upload").value=t.upload<=0?"":String(t.upload/125),e.control("latency").value=t.latency?String(t.latency):"",e.control("packetLoss").value=t.packetLoss?String(t.packetLoss):"",e.control("packetQueueLength").value=t.packetQueueLength?String(t.packetQueueLength):"",e.control("packetReordering").checked=t.packetReordering??!1,e}createEditor(){if(this.editor)return this.editor;const e=new s.ListWidget.Editor;this.editor=e;const n=e.contentElement(),i=n.createChild("div","conditions-edit-row"),o=i.createChild("div","conditions-list-text conditions-list-title"),r=_(O.profileName);o.createChild("div","conditions-list-title-text").textContent=r,i.createChild("div","conditions-list-separator conditions-list-separator-invisible");const a=i.createChild("div","conditions-list-text"),l=_(O.download);a.createChild("div","conditions-list-title-text").textContent=l,i.createChild("div","conditions-list-separator conditions-list-separator-invisible");const c=i.createChild("div","conditions-list-text").createChild("div","conditions-list-title-text"),d=_(O.upload);c.textContent=d,i.createChild("div","conditions-list-separator conditions-list-separator-invisible");const g=i.createChild("div","conditions-list-text"),h=_(O.latency);g.createChild("div","conditions-list-title-text").textContent=h,i.createChild("div","conditions-list-separator conditions-list-separator-invisible");const u=i.createChild("div","conditions-list-text"),p=_(O.packetLoss);u.createChild("div","conditions-list-title-text").textContent=p,i.createChild("div","conditions-list-separator conditions-list-separator-invisible");const C=i.createChild("div","conditions-list-text"),w=_(O.packetQueueLength);C.createChild("div","conditions-list-title-text").textContent=w,i.createChild("div","conditions-list-separator conditions-list-separator-invisible");const k=i.createChild("div","conditions-list-text"),b=_(O.packetReordering);k.createChild("div","conditions-list-title-text").textContent=b;const m=n.createChild("div","conditions-edit-row"),v=e.createInput("title","text","",(function(t,e,n){const i=n.value.trim(),o=i.length>0&&i.length<=49;if(!o){return{valid:o,errorMessage:_(O.profileNameCharactersLengthMust,{PH1:49})}}return{valid:o,errorMessage:void 0}}));s.ARIAUtils.setLabel(v,r),m.createChild("div","conditions-list-text conditions-list-title").appendChild(v),m.createChild("div","conditions-list-separator conditions-list-separator-invisible");let f=m.createChild("div","conditions-list-text");const T=e.createInput("download","text",t.i18n.lockedString("kbit/s"),L);f.appendChild(T),s.ARIAUtils.setLabel(T,l);const M=f.createChild("div","conditions-edit-optional"),N=_(O.optional);M.textContent=N,s.ARIAUtils.setDescription(T,N),m.createChild("div","conditions-list-separator conditions-list-separator-invisible"),f=m.createChild("div","conditions-list-text");const x=e.createInput("upload","text",t.i18n.lockedString("kbit/s"),L);s.ARIAUtils.setLabel(x,d),f.appendChild(x);f.createChild("div","conditions-edit-optional").textContent=N,s.ARIAUtils.setDescription(x,N),m.createChild("div","conditions-list-separator conditions-list-separator-invisible"),f=m.createChild("div","conditions-list-text");const S=e.createInput("latency","text",t.i18n.lockedString("ms"),(function(t,e,n){const i=1e6,o=n.value.trim(),r=Number(o),s=Number.isInteger(r)&&r>=0&&r<=i;if(!s){return{valid:s,errorMessage:_(O.latencyMustBeAnIntegerBetweenSms,{PH1:0,PH2:i})}}return{valid:s,errorMessage:void 0}}));s.ARIAUtils.setLabel(S,h),f.appendChild(S);f.createChild("div","conditions-edit-optional").textContent=N,s.ARIAUtils.setDescription(S,N),m.createChild("div","conditions-list-separator conditions-list-separator-invisible"),f=m.createChild("div","conditions-list-text");const P=e.createInput("packetLoss","text",t.i18n.lockedString("percent"),(function(t,e,n){const i=n.value.trim(),o=Number(i),r=o>=0&&o<=100;if(!r){return{valid:r,errorMessage:_(O.packetLossMustBeAnIntegerBetweenSpct,{PH1:0,PH2:100})}}return{valid:r,errorMessage:void 0}}));s.ARIAUtils.setLabel(P,p),f.appendChild(P);f.createChild("div","conditions-edit-optional").textContent=N,s.ARIAUtils.setDescription(P,N),m.createChild("div","conditions-list-separator conditions-list-separator-invisible"),f=m.createChild("div","conditions-list-text");const y=e.createInput("packetQueueLength","text",_(O.packet),(function(t,e,n){const i=n.value.trim(),o=Number(i)>=0;if(!o){return{valid:o,errorMessage:_(O.packetQueueLengthMustBeAnIntegerGreaterOrEqualToZero)}}return{valid:o,errorMessage:void 0}}));s.ARIAUtils.setLabel(y,w),f.appendChild(y);f.createChild("div","conditions-edit-optional").textContent=N,s.ARIAUtils.setDescription(y,N),m.createChild("div","conditions-list-separator conditions-list-separator-invisible"),f=m.createChild("div","conditions-list-text");const I=e.createInput("packetReordering","checkbox",_(O.percent),(function(t,e,n){return{valid:!0,errorMessage:void 0}}));return s.ARIAUtils.setLabel(I,p),f.appendChild(I),e;function L(t,e,n){const i=1e7,o=n.value.trim(),r=Number(o),s=n.getAttribute("aria-label"),a=!Number.isNaN(r)&&r>=0&&r<=i;if(!a){return{valid:a,errorMessage:_(O.sMustBeANumberBetweenSkbsToSkbs,{PH1:String(s),PH2:0,PH3:i})}}return{valid:a,errorMessage:void 0}}}}function j(t){if(t<0)return"";const e=t/125;if(e<1e3)return _(O.dskbits,{PH1:e});if(e<1e4){const t=(e/1e3).toFixed(1);return _(O.fsmbits,{PH1:t})}return _(O.fsmbits,{PH1:e/1e3|0})}var D=Object.freeze({__proto__:null,ThrottlingSettingsTab:B});export{I as MobileThrottlingSelector,A as NetworkPanelIndicator,k as NetworkThrottlingSelector,N as ThrottlingManager,h as ThrottlingPresets,D as ThrottlingSettingsTab};
