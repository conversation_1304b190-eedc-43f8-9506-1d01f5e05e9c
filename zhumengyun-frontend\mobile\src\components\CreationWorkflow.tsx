'use client'

import { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface CreationProject {
  id: string
  name: string
  type: 'design' | 'video' | 'audio' | 'architecture' | 'text'
  stage: 'inspiration' | 'planning' | 'creation' | 'editing' | 'review' | 'publishing'
  progress: number
  collaborators: string[]
  aiAssistants: string[]
  inspirationSources: InspirationItem[]
  assets: ProjectAsset[]
  versions: ProjectVersion[]
  publishingTargets: string[]
  analytics?: ProjectAnalytics
}

interface InspirationItem {
  id: string
  type: 'image' | 'video' | 'text' | 'audio' | 'reference'
  title: string
  source: string
  url: string
  tags: string[]
  aiGenerated: boolean
}

interface ProjectAsset {
  id: string
  name: string
  type: 'image' | 'video' | 'audio' | 'document' | '3d_model'
  url: string
  size: number
  createdAt: Date
  aiEnhanced: boolean
}

interface ProjectVersion {
  id: string
  version: string
  description: string
  createdAt: Date
  createdBy: string
  changes: string[]
  previewUrl?: string
}

interface ProjectAnalytics {
  views: number
  likes: number
  shares: number
  comments: number
  engagement: number
  reachScore: number
  suggestions: string[]
}

interface CreationWorkflowProps {
  project?: CreationProject
  onProjectUpdate?: (project: CreationProject) => void
  onStageChange?: (stage: string) => void
}

export default function CreationWorkflow({ 
  project, 
  onProjectUpdate, 
  onStageChange 
}: CreationWorkflowProps) {
  const [currentProject, setCurrentProject] = useState<CreationProject | null>(project || null)
  const [activeStage, setActiveStage] = useState<string>('inspiration')
  const [showAIAssistant, setShowAIAssistant] = useState(false)
  const [collaborationMode, setCollaborationMode] = useState(false)

  const workflowStages = [
    {
      id: 'inspiration',
      name: '灵感收集',
      icon: '💡',
      description: '收集创作灵感和参考资料',
      color: 'from-yellow-500 to-orange-500'
    },
    {
      id: 'planning',
      name: '创作规划',
      icon: '📋',
      description: '制定创作计划和时间安排',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      id: 'creation',
      name: 'AI辅助创作',
      icon: '🎨',
      description: '使用AI工具进行创作',
      color: 'from-purple-500 to-pink-500'
    },
    {
      id: 'editing',
      name: '协作编辑',
      icon: '✏️',
      description: '多人实时协作编辑',
      color: 'from-green-500 to-teal-500'
    },
    {
      id: 'review',
      name: '审核优化',
      icon: '🔍',
      description: '内容审核和AI优化建议',
      color: 'from-indigo-500 to-purple-500'
    },
    {
      id: 'publishing',
      name: '发布推广',
      icon: '🚀',
      description: '多平台发布和数据分析',
      color: 'from-red-500 to-pink-500'
    }
  ]

  const aiTools = [
    {
      id: 'image-generator',
      name: 'AI图像生成',
      icon: '🖼️',
      description: '基于文本描述生成高质量图像',
      model: 'DALL-E 3'
    },
    {
      id: 'video-editor',
      name: 'AI视频剪辑',
      icon: '🎬',
      description: '智能视频剪辑和特效添加',
      model: 'CapCut AI'
    },
    {
      id: 'text-writer',
      name: 'AI文案创作',
      icon: '✍️',
      description: '智能文案生成和优化',
      model: 'GPT-4'
    },
    {
      id: 'audio-composer',
      name: 'AI音乐制作',
      icon: '🎵',
      description: 'AI音乐创作和音效制作',
      model: 'MusicLM'
    },
    {
      id: 'architecture-designer',
      name: 'AI建筑设计',
      icon: '🏗️',
      description: 'AI辅助建筑设计和优化',
      model: 'GPT-4 + CAD'
    }
  ]

  const handleStageChange = (stageId: string) => {
    setActiveStage(stageId)
    onStageChange?.(stageId)
  }

  const handleAIToolUse = (toolId: string) => {
    setShowAIAssistant(true)
    // 这里可以集成具体的AI工具逻辑
    console.log('Using AI tool:', toolId)
  }

  const handleCollaboration = () => {
    setCollaborationMode(!collaborationMode)
    // 这里可以集成实时协作逻辑
  }

  const renderStageContent = () => {
    switch (activeStage) {
      case 'inspiration':
        return <InspirationStage project={currentProject} onUpdate={setCurrentProject} />
      case 'planning':
        return <PlanningStage project={currentProject} onUpdate={setCurrentProject} />
      case 'creation':
        return <CreationStage project={currentProject} aiTools={aiTools} onAIToolUse={handleAIToolUse} />
      case 'editing':
        return <EditingStage project={currentProject} collaborationMode={collaborationMode} onCollaboration={handleCollaboration} />
      case 'review':
        return <ReviewStage project={currentProject} onUpdate={setCurrentProject} />
      case 'publishing':
        return <PublishingStage project={currentProject} onUpdate={setCurrentProject} />
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      {/* 工作流程导航 */}
      <div className="bg-gray-800/50 rounded-xl p-4">
        <h3 className="text-white font-bold mb-4 flex items-center space-x-2">
          <span>🔄</span>
          <span>创作工作流程</span>
        </h3>
        
        <div className="grid grid-cols-3 gap-2">
          {workflowStages.map((stage, index) => (
            <button
              key={stage.id}
              onClick={() => handleStageChange(stage.id)}
              className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                activeStage === stage.id
                  ? 'border-blue-500 bg-blue-500/20'
                  : 'border-gray-600 bg-gray-800/50 hover:border-gray-500'
              }`}
            >
              <div className="text-lg mb-1">{stage.icon}</div>
              <div className="text-xs font-medium text-white">{stage.name}</div>
              {currentProject && (
                <div className="mt-1">
                  <div className="w-full bg-gray-700 rounded-full h-1">
                    <div 
                      className={`bg-gradient-to-r ${stage.color} h-1 rounded-full transition-all duration-300`}
                      style={{ width: `${index <= workflowStages.findIndex(s => s.id === activeStage) ? 100 : 0}%` }}
                    />
                  </div>
                </div>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* 当前阶段内容 */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeStage}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          {renderStageContent()}
        </motion.div>
      </AnimatePresence>

      {/* AI助手浮窗 */}
      {showAIAssistant && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4"
        >
          <div className="bg-gray-900 rounded-2xl p-6 max-w-md w-full">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-white font-bold">AI创作助手</h3>
              <button
                onClick={() => setShowAIAssistant(false)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>
            <div className="space-y-4">
              <div className="bg-blue-500/20 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-blue-400">🤖</span>
                  <span className="text-blue-400 font-medium">AI建议</span>
                </div>
                <p className="text-gray-300 text-sm">
                  基于您的创作类型，建议使用图像生成工具创建初始概念图，然后使用视频编辑工具制作动态展示。
                </p>
              </div>
              <div className="grid grid-cols-2 gap-2">
                {aiTools.slice(0, 4).map((tool) => (
                  <button
                    key={tool.id}
                    onClick={() => handleAIToolUse(tool.id)}
                    className="p-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    <div className="text-lg mb-1">{tool.icon}</div>
                    <div className="text-xs text-white font-medium">{tool.name}</div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  )
}

// 灵感收集阶段组件
function InspirationStage({ project, onUpdate }: any) {
  const [inspirationItems, setInspirationItems] = useState<InspirationItem[]>([])

  const addInspiration = (item: InspirationItem) => {
    setInspirationItems(prev => [...prev, item])
  }

  return (
    <div className="bg-gray-800/50 rounded-xl p-4">
      <h4 className="text-white font-bold mb-4 flex items-center space-x-2">
        <span>💡</span>
        <span>灵感收集</span>
      </h4>
      
      <div className="grid grid-cols-2 gap-3 mb-4">
        <button className="p-4 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-lg">
          <div className="text-2xl mb-2">🔍</div>
          <div className="text-sm font-medium text-white">AI灵感搜索</div>
          <div className="text-xs text-gray-400">智能推荐相关内容</div>
        </button>
        
        <button className="p-4 bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-lg">
          <div className="text-2xl mb-2">📸</div>
          <div className="text-sm font-medium text-white">拍照收集</div>
          <div className="text-xs text-gray-400">拍摄灵感素材</div>
        </button>
        
        <button className="p-4 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border border-blue-500/30 rounded-lg">
          <div className="text-2xl mb-2">🌐</div>
          <div className="text-sm font-medium text-white">网络收藏</div>
          <div className="text-xs text-gray-400">保存网页内容</div>
        </button>
        
        <button className="p-4 bg-gradient-to-r from-green-500/20 to-teal-500/20 border border-green-500/30 rounded-lg">
          <div className="text-2xl mb-2">🎨</div>
          <div className="text-sm font-medium text-white">手绘草图</div>
          <div className="text-xs text-gray-400">创建手绘概念</div>
        </button>
      </div>

      <div className="space-y-3">
        <h5 className="text-gray-400 text-sm font-medium">最近收集的灵感</h5>
        {inspirationItems.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-2">💭</div>
            <p className="text-sm">还没有收集灵感，开始您的创作之旅吧！</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-3">
            {inspirationItems.map((item) => (
              <div key={item.id} className="bg-gray-700 rounded-lg p-3">
                <div className="text-sm font-medium text-white mb-1">{item.title}</div>
                <div className="text-xs text-gray-400">{item.source}</div>
                {item.aiGenerated && (
                  <div className="mt-2">
                    <span className="text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded">AI生成</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

// 其他阶段组件的简化实现
function PlanningStage({ project, onUpdate }: any) {
  return (
    <div className="bg-gray-800/50 rounded-xl p-4">
      <h4 className="text-white font-bold mb-4">📋 创作规划</h4>
      <p className="text-gray-400 text-sm">制定详细的创作计划和时间安排...</p>
    </div>
  )
}

function CreationStage({ project, aiTools, onAIToolUse }: any) {
  return (
    <div className="bg-gray-800/50 rounded-xl p-4">
      <h4 className="text-white font-bold mb-4">🎨 AI辅助创作</h4>
      <div className="text-center py-8">
        <div className="text-4xl mb-4">🎨</div>
        <h3 className="text-lg font-bold text-white mb-2">AI创作工具</h3>
        <p className="text-gray-400 text-sm mb-4">
          使用下方的AI工具列表开始您的创作之旅
        </p>
        <button
          onClick={() => onAIToolUse('all')}
          className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-lg font-medium"
        >
          查看AI工具
        </button>
      </div>
    </div>
  )
}

function EditingStage({ project, collaborationMode, onCollaboration }: any) {
  return (
    <div className="bg-gray-800/50 rounded-xl p-4">
      <h4 className="text-white font-bold mb-4">✏️ 协作编辑</h4>
      <button
        onClick={onCollaboration}
        className={`w-full p-3 rounded-lg transition-colors ${
          collaborationMode 
            ? 'bg-green-500/20 text-green-400 border border-green-500/30'
            : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
        }`}
      >
        {collaborationMode ? '🟢 协作模式已开启' : '⚪ 开启协作模式'}
      </button>
    </div>
  )
}

function ReviewStage({ project, onUpdate }: any) {
  return (
    <div className="bg-gray-800/50 rounded-xl p-4">
      <h4 className="text-white font-bold mb-4">🔍 审核优化</h4>
      <p className="text-gray-400 text-sm">AI智能审核和优化建议...</p>
    </div>
  )
}

function PublishingStage({ project, onUpdate }: any) {
  return (
    <div className="bg-gray-800/50 rounded-xl p-4">
      <h4 className="text-white font-bold mb-4">🚀 发布推广</h4>
      <p className="text-gray-400 text-sm">多平台发布和数据分析...</p>
    </div>
  )
}
