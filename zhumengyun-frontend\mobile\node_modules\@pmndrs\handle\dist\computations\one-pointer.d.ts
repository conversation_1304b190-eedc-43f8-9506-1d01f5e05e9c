import { <PERSON>uler, Matrix4, Quaternion, Vector3 } from 'three';
import { HandleTransformState } from '../state.js';
import { HandleOptions, HandleTransformOptions } from '../store.js';
export type OnePointerHandlePointerData = {
    initialPointerWorldPoint: Vector3;
    initialPointerWorldDirection: Vector3 | undefined;
    initialPointerWorldQuaternion: Quaternion;
    pointerWorldPoint: Vector3;
    pointerWorldOrigin: Vector3;
    pointerWorldDirection: Vector3 | undefined;
    pointerWorldQuaternion: Quaternion;
};
export type OnePointerHandleStoreData = {
    initialTargetPosition: Vector3;
    initialTargetQuaternion: Quaternion;
    initialTargetRotation: Euler;
    initialTargetScale: Vector3;
    initialTargetParentWorldMatrix: Matrix4 | undefined;
};
export declare function computeOnePointerHandleTransformState(time: number, pointerData: OnePointerHandlePointerData, storeData: OnePointerHandleStoreData, targetParentWorldMatrix: Matrix4 | undefined, options: HandleOptions<any> & {
    translate?: HandleTransformOptions;
}): HandleTransformState;
