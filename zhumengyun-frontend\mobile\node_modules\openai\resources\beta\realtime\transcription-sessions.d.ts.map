{"version": 3, "file": "transcription-sessions.d.ts", "sourceRoot": "", "sources": ["../../../src/resources/beta/realtime/transcription-sessions.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,KAAK,IAAI,MAAM,eAAe,CAAC;AAEtC,qBAAa,qBAAsB,SAAQ,WAAW;IACpD;;;;;;;;;;;;;;OAcG;IACH,MAAM,CACJ,IAAI,EAAE,gCAAgC,EACtC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC;CAOzC;AAED;;;;;;GAMG;AACH,MAAM,WAAW,oBAAoB;IACnC;;;OAGG;IACH,aAAa,EAAE,oBAAoB,CAAC,YAAY,CAAC;IAEjD;;OAEG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAE5B;;OAEG;IACH,yBAAyB,CAAC,EAAE,oBAAoB,CAAC,uBAAuB,CAAC;IAEzE;;;OAGG;IACH,UAAU,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;IAErC;;;;OAIG;IACH,cAAc,CAAC,EAAE,oBAAoB,CAAC,aAAa,CAAC;CACrD;AAED,yBAAiB,oBAAoB,CAAC;IACpC;;;OAGG;IACH,UAAiB,YAAY;QAC3B;;;WAGG;QACH,UAAU,EAAE,MAAM,CAAC;QAEnB;;;;WAIG;QACH,KAAK,EAAE,MAAM,CAAC;KACf;IAED;;OAEG;IACH,UAAiB,uBAAuB;QACtC;;;;WAIG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAC;QAElB;;;WAGG;QACH,KAAK,CAAC,EAAE,mBAAmB,GAAG,wBAAwB,GAAG,WAAW,CAAC;QAErE;;;;;WAKG;QACH,MAAM,CAAC,EAAE,MAAM,CAAC;KACjB;IAED;;;;OAIG;IACH,UAAiB,aAAa;QAC5B;;;WAGG;QACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAE3B;;;;WAIG;QACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;QAE7B;;;;WAIG;QACH,SAAS,CAAC,EAAE,MAAM,CAAC;QAEnB;;WAEG;QACH,IAAI,CAAC,EAAE,MAAM,CAAC;KACf;CACF;AAED,MAAM,WAAW,gCAAgC;IAC/C;;OAEG;IACH,aAAa,CAAC,EAAE,gCAAgC,CAAC,YAAY,CAAC;IAE9D;;;;OAIG;IACH,OAAO,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAExB;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,OAAO,GAAG,WAAW,GAAG,WAAW,CAAC;IAEzD;;;;;;OAMG;IACH,2BAA2B,CAAC,EAAE,gCAAgC,CAAC,wBAAwB,CAAC;IAExF;;;;OAIG;IACH,yBAAyB,CAAC,EAAE,gCAAgC,CAAC,uBAAuB,CAAC;IAErF;;;OAGG;IACH,UAAU,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;IAErC;;;;;;;;;;;OAWG;IACH,cAAc,CAAC,EAAE,gCAAgC,CAAC,aAAa,CAAC;CACjE;AAED,yBAAiB,gCAAgC,CAAC;IAChD;;OAEG;IACH,UAAiB,YAAY;QAC3B;;WAEG;QACH,UAAU,CAAC,EAAE,YAAY,CAAC,SAAS,CAAC;KACrC;IAED,UAAiB,YAAY,CAAC;QAC5B;;WAEG;QACH,UAAiB,SAAS;YACxB;;;eAGG;YACH,MAAM,CAAC,EAAE,YAAY,CAAC;YAEtB;;;eAGG;YACH,OAAO,CAAC,EAAE,MAAM,CAAC;SAClB;KACF;IAED;;;;;;OAMG;IACH,UAAiB,wBAAwB;QACvC;;;;WAIG;QACH,IAAI,CAAC,EAAE,YAAY,GAAG,WAAW,CAAC;KACnC;IAED;;;;OAIG;IACH,UAAiB,uBAAuB;QACtC;;;;WAIG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAC;QAElB;;;WAGG;QACH,KAAK,CAAC,EAAE,mBAAmB,GAAG,wBAAwB,GAAG,WAAW,CAAC;QAErE;;;;;;WAMG;QACH,MAAM,CAAC,EAAE,MAAM,CAAC;KACjB;IAED;;;;;;;;;;;OAWG;IACH,UAAiB,aAAa;QAC5B;;;WAGG;QACH,eAAe,CAAC,EAAE,OAAO,CAAC;QAE1B;;;;WAIG;QACH,SAAS,CAAC,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC;QAE/C;;;;WAIG;QACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;QAE7B;;;WAGG;QACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAE3B;;;;WAIG;QACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;QAE7B;;;;WAIG;QACH,SAAS,CAAC,EAAE,MAAM,CAAC;QAEnB;;WAEG;QACH,IAAI,CAAC,EAAE,YAAY,GAAG,cAAc,CAAC;KACtC;CACF;AAED,MAAM,CAAC,OAAO,WAAW,qBAAqB,CAAC;IAC7C,OAAO,EACL,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,gCAAgC,IAAI,gCAAgC,GAC1E,CAAC;CACH"}