{"version": 3, "file": "ActionRecorder.js", "sourceRoot": "", "sources": ["../../src/action/ActionRecorder.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAE7C,OAAO,EAAE,iBAAiB,EAAE,MAAM,eAAe,CAAC;AA6ClD,MAAM,QAAQ,GAAG,CAAC,GAAgB,EAAE,EAAE;IACrC,MAAM,GAAG,GAAa,EAAE,CAAC;IACzB,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;QACnB,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IACH,OAAO,GAAG,CAAC;AACZ,CAAC,CAAC;AAEF,MAAM,OAAO,cAAc;IAW1B,YAAY,OAAkB,EAAE,QAA0B;QACzD,IAAI,CAAC,iBAAiB,CAAC,GAAG;YACzB,OAAO;YACP,QAAQ;YACR,QAAQ,EAAE,IAAI,GAAG,EAAE;YACnB,SAAS,EAAE,IAAI,GAAG,EAAE;YACpB,gBAAgB,EAAE,EAAE;YACpB,UAAU,EAAE,IAAI,YAAY,CAAC,EAAE,CAAC;YAChC,eAAe,EAAE,IAAI,YAAY,CAAC,EAAE,GAAG,EAAE,CAAC;SAC1C,CAAC;IACH,CAAC;IAED,WAAW,CAAC,KAAc;;QACzB,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QACpC,MAAM,YAAY,GAAG,MAAA,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,0CACvE,SAAS,CAAC,MAAM,CAAC;QACpB,IAAI,CAAC,YAAY;YAAE,OAAO;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,YAAY,CAAC,CAAC;QAClE,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,YAAY,CAAC,CAAC;QACjE,MAAM,WAAW,GAAgB;YAChC,SAAS;YACT,QAAQ;YACR,UAAU;YACV,WAAW,EAAE,EAAE;SACf,CAAC;QACF,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE;;YACpE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;gBACvD,MAAM,MAAM,GAAgB;oBAC3B,UAAU,EAAE,WAAW,CAAC,UAAU;oBAClC,aAAa,EAAE,WAAW,CAAC,aAAa;oBACxC,QAAQ,EAAE,WAAW,CAAC,QAAQ;oBAC9B,OAAO,EAAE,WAAW,CAAC,SAAS,IAAI,IAAI;oBACtC,OAAO,EAAE,WAAW,CAAC,IAAI,IAAI,IAAI;oBACjC,UAAU,EAAE,WAAW,CAAC,OAAO,IAAI,IAAI;iBACvC,CAAC;gBACF,IAAI,MAAM,CAAC,OAAO,EAAE;oBACnB,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAChE,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,SAAS,CACpC,CAAC;iBACF;gBACD,IAAI,MAAM,CAAC,UAAU,EAAE;oBACtB,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC,OAAQ,CAAC,OAAO,CAAC;oBAC9C,MAAM,CAAC,UAAU,GAAG,WAAW,CAAC,OAAQ,CAAC,OAAO,CAAC,MAAM,CAAC;oBACxD,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC,OAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;iBAClD;gBACD,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACpD,IAAI,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;gBACzD,IAAI,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;aACrD;YACD,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAE,CAAC;YACjE,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC;YAC7D,MAAM,eAAe,GAAG,MAAA,KAAK,CAAC,OAAO,CACpC,WAAW,CAAC,cAAc,EAC1B,IAAI,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAChC,0CAAE,SAAS,CAAC,MAAM,CAAC;YACpB,IAAI,eAAe,EAAE;gBACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAC5C,IAAI,CAAC,MAAM,EAAE,EACb,eAAe,CACf,CAAC;gBACF,MAAM,mBAAmB,GAAG,IAAI,CAAC,WAAW,CAC3C,IAAI,CAAC,MAAM,EAAE,EACb,eAAe,CACf,CAAC;gBAEF,MAAM,UAAU,GAAe;oBAC9B,KAAK;oBACL,kBAAkB,EAAE;wBACnB,QAAQ,EAAE,iBAAiB;wBAC3B,UAAU,EAAE,mBAAmB;qBAC/B;iBACD,CAAC;gBAEF,IAAI,MAAM,CAAC,OAAO,EAAE;oBACnB,MAAM,UAAU,GAAG,MAAA,KAAK,CAAC,OAAO,CAC/B,WAAW,CAAC,SAAU,EACtB,IAAI,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAChC,0CAAE,SAAS,CAAC,MAAM,CAAC;oBACpB,IAAI,UAAU,EAAE;wBACf,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,UAAU,CAAC,CAAC;wBAChE,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,UAAU,CAAC,CAAC;wBAC/D,UAAU,CAAC,aAAa,GAAG;4BAC1B,QAAQ;4BACR,UAAU;yBACV,CAAC;qBACF;iBACD;gBAED,IAAI,MAAM,CAAC,OAAO,EAAE;oBACnB,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC3D,IAAI,QAAQ,GAAG,IAAI,CAAC;oBAEpB,aAAa;oBACb,QAAQ,KAAR,QAAQ,GAAK,KAAK,CAAC,SAAS,CAC3B,WAAW,EACX,WAAW,CAAC,cAAc,EAC1B,IAAI,CAAC,iBAAiB,CAAC,CAAC,eAAe,CACvC,EAAC;oBAEF,aAAa;oBACb,QAAQ,KAAR,QAAQ,GAAK,KAAK,CAAC,cAAc,CAChC,WAAW,EACX,IAAI,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAClC,EAAC;oBAEF,IAAI,QAAQ,EAAE;wBACb,MAAM,IAAI,GAMN,EAAS,CAAC;wBACd,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,EAAE,EAAE,MAAM,EAAE,EAAE;4BAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,eAAe,CAAC,KAAK,CAChE,MAAM,GAAG,EAAE,EACX,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CACjB,CAAC;4BACF,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;4BAC1D,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,CAAC,CAAC;4BACjE,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,CAAC,CAAC;4BAChE,MAAM,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,SAAwB,CAAC;4BAC/D,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC;yBACnD;wBACD,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;qBACvB;iBACD;gBAED,IAAI,MAAM,CAAC,UAAU,EAAE;oBACtB,MAAM,OAAO,GAAG;wBACf,OAAO,EAAE,WAAW,CAAC,OAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CACpD,MAAM;4BACL,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC;4BAChE,CAAC,CAAC,IAAI,CACP;wBACD,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,OAAQ,CAAC,IAAI,CAAC;qBAC3C,CAAC;oBACF,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;iBAC7B;gBAED,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aACzC;QACF,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,iBAAiB,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAC5C,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CACrC,CAAC;IACH,CAAC;IAED,mBAAmB,CAAC,EAAe;QAClC,MAAM,GAAG,GAAU;YAClB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,GAAG,EAAE,CAAC,GAAG,EAAE;YAClC,GAAG,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC;YACxB,GAAG,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC;SAC1B,CAAC;QACF,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACrC,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;YAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC;YAC7D,MAAM,QAAQ,GAAU;gBACvB,KAAK;gBACL,GAAG,QAAQ,CAAC,UAAU,CAAC,kBAAkB,CAAC,QAAQ,CAAC;gBACnD,GAAG,QAAQ,CAAC,UAAU,CAAC,kBAAkB,CAAC,UAAU,CAAC;aACrD,CAAC;YACF,IAAI,MAAM,CAAC,OAAO,EAAE;gBACnB,QAAQ,CAAC,IAAI,CAAC;oBACb,GAAG,QAAQ,CAAC,UAAU,CAAC,aAAc,CAAC,QAAQ,CAAC;oBAC/C,GAAG,QAAQ,CAAC,UAAU,CAAC,aAAc,CAAC,UAAU,CAAC;iBACjD,CAAC,CAAC;aACH;YACD,IAAI,MAAM,CAAC,OAAO,EAAE;gBACnB,MAAM,OAAO,GAAa,EAAE,CAAC;gBAC7B,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,IAAK,CAAC,CAAC,OAAO,CACtC,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE,EAAE;oBACpC,OAAO,CAAC,IAAI,CACX,GAAG,QAAQ,CAAC,QAAQ,CAAC,EACrB,GAAG,QAAQ,CAAC,UAAU,CAAC,EACvB,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAC7B,CAAC;gBACH,CAAC,CACD,CAAC;gBACF,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACvB;YACD,IAAI,MAAM,CAAC,UAAU,EAAE;gBACtB,QAAQ,CAAC,IAAI,CAAC;oBACb,GAAG,UAAU,CAAC,OAAQ,CAAC,OAAO;oBAC9B,GAAG,UAAU,CAAC,OAAQ,CAAC,IAAI;iBAC3B,CAAC,CAAC;aACH;YACD,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,CAAC;IACZ,CAAC;IAED,GAAG;QACF,MAAM,GAAG,GAAG;YACX,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YAC/D,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,gBAAgB;SAChD,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;IAClC,CAAC;CACD"}