{"version": 3, "file": "BasicShader.js", "sources": ["../../src/shaders/BasicShader.ts"], "sourcesContent": ["/**\n * Simple test shader\n */\n\nimport type { IShader } from './types'\n\nexport type BasicShaderUniforms = {}\n\nexport interface IBasicShader extends IShader<BasicShaderUniforms> {}\n\nexport const BasicShader: IBasicShader = {\n  uniforms: {},\n\n  vertexShader: /* glsl */ `\n    void main() {\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    void main() {\n\n      gl_FragColor = vec4( 1.0, 0.0, 0.0, 0.5 );\n\n    }\n  `,\n}\n"], "names": [], "mappings": "AAUO,MAAM,cAA4B;AAAA,EACvC,UAAU,CAAC;AAAA,EAEX;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAO7B;"}