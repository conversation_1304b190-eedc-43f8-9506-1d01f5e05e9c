/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_WEBGL_LAYER } from '../private.js';
import { XRSession } from '../session/XRSession.js';
import { XRView } from '../views/XRView.js';
export declare class XRLayer extends EventTarget {
}
type LayerInit = {
    antialias?: boolean;
    depth?: boolean;
    stencil?: boolean;
    alpha?: boolean;
    ignoreDepthValues?: boolean;
    framebufferScaleFactor?: number;
};
export declare class XRWebGLLayer extends XRLayer {
    [P_WEBGL_LAYER]: {
        session: XRSession;
        context: WebGLRenderingContext | WebGL2RenderingContext;
        antialias: boolean;
    };
    constructor(session: XRSession, context: WebGLRenderingContext | WebGL2RenderingContext, layerInit?: LayerInit);
    get context(): WebGLRenderingContext | WebGL2RenderingContext;
    get antialias(): boolean;
    get ignoreDepthValues(): boolean;
    get framebuffer(): null;
    get framebufferWidth(): number;
    get framebufferHeight(): number;
    getViewport(view: XRView): import("../index.js").XRViewport;
    static getNativeFramebufferScaleFactor(session: XRSession): number;
}
export {};
//# sourceMappingURL=XRWebGLLayer.d.ts.map