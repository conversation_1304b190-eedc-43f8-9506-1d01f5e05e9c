import*as i from"../../core/i18n/i18n.js";import*as e from"../../ui/legacy/legacy.js";const a={media:"Media",video:"video",showMedia:"Show Media"},o=i.i18n.registerUIStrings("panels/media/media-meta.ts",a),n=i.i18n.getLazilyComputedLocalizedString.bind(void 0,o);let t;e.ViewManager.registerViewExtension({location:"panel",id:"medias",title:n(a.media),commandPrompt:n(a.showMedia),persistence:"closeable",order:100,loadView:async()=>new((await async function(){return t||(t=await import("./media.js")),t}()).MainView.MainView),tags:[n(a.media),n(a.video)]});
