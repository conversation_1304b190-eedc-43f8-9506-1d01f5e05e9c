"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("suspend-react");function r(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var a=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,a.get?a:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var a=r(e);const s=e.createContext({}),n={basePath:"https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.17/wasm",options:{baseOptions:{modelAssetPath:"https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task",delegate:"GPU"},runningMode:"VIDEO",outputFaceBlendshapes:!0,outputFacialTransformationMatrixes:!0}},o=e.forwardRef((({basePath:o=n.basePath,options:i=n.options,children:c},u)=>{const l=JSON.stringify(i),d=t.suspend((async()=>{const{FilesetResolver:e,FaceLandmarker:t}=await Promise.resolve().then((function(){return r(require("@mediapipe/tasks-vision"))})),a=await e.forVisionTasks(o);return t.createFromOptions(a,i)}),[o,l]);return e.useEffect((()=>()=>{null==d||d.close(),t.clear([o,l])}),[d,o,l]),e.useImperativeHandle(u,(()=>d),[d]),a.createElement(s.Provider,{value:d},c)}));exports.FaceLandmarker=o,exports.FaceLandmarkerDefaults=n,exports.useFaceLandmarker=function(){return e.useContext(s)};
