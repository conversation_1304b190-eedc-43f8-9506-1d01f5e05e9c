import { Group, OrthographicCamera, PerspectiveCamera, TorusGeometry } from 'three';
import { HandlesContext } from '../context.js';
import { HandlesProperties } from '../index.js';
export declare function createCircleGeometry(radius: number, arc: number): TorusGeometry;
export declare class RotateHandles extends Group {
    private readonly context;
    fixed?: boolean | undefined;
    size?: number | undefined;
    private readonly rotationX;
    private readonly rotationY;
    private readonly rotationZ;
    private readonly free;
    private readonly screen;
    constructor(context: HandlesContext, fixed?: boolean | undefined, size?: number | undefined);
    update(camera: PerspectiveCamera | OrthographicCamera): void;
    bind(options?: HandlesProperties): () => void;
}
