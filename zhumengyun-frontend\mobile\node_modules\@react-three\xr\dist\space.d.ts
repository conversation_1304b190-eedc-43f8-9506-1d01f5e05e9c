import { RootState } from '@react-three/fiber';
import { ReactNode } from 'react';
import { Group, Object3D } from 'three';
/**
 * Component that puts its children in the provided XRSpace (or reference space type)
 *
 * @param props
 * #### `space` - [XRSpaceType](https://developer.mozilla.org/en-US/docs/Web/API/XRSpace) | [XRSpaceType](https://pmndrs.github.io/xr/docs/api/space.XRSpaceType)
 * #### `children` - [ReactNode](https://reactjs.org/docs/introducing-jsx.html#react-jsx)
 * @function
 */
export declare const XRSpace: import("react").ForwardRefExoticComponent<{
    space: XRSpace | XRSpaceType;
    children?: ReactNode;
} & import("react").RefAttributes<Object3D<import("three").Object3DEventMap>>>;
/**
 * A combined type of all XRSpace types
 * @see [XRReferenceSpaceType](https://developer.mozilla.org/en-US/docs/Web/API/XRReferenceSpaceType)
 * @see [XRHandJointSpaceType](https://immersive-web.github.io/webxr-hand-input/#xrhand-interface)
 */
export type XRSpaceType = XRReferenceSpaceType | XRInputSourceSpaceType | XRHandJointSpaceType | XRBodyJointSpaceType;
export type XRInputSourceSpaceType = 'grip-space' | 'target-ray-space';
export type XRHandJointSpaceType = XRHandJoint;
export type XRBodyJointSpaceType = XRBodyJoint;
/**
 * Hook for retrieving XR space from the context
 */
export declare function useXRSpace(): XRSpace;
export declare function useXRSpace(type: XRReferenceSpaceType): XRReferenceSpace | undefined;
export declare function useXRSpace(type: XRSpaceType): XRSpace | undefined;
/**
 * Hook that returns a function to compute a matrix that contains the transformation of the provided xr space
 */
export declare function useGetXRSpaceMatrix(space: XRSpace | undefined): ((target: import("three").Matrix4, frame: XRFrame | undefined) => boolean) | undefined;
/**
 * Hook that applies the transformation of the provided xr space to the provided object reference
 *
 * @param onFrame Optional callback that gets executed after the matrix of the reference object was updated
 * @param ref.current A react ref object that points to the object that the XRSpaceMatrix should be applied to
 * @requires matrixAutoUpdate to be disabled for the referenced object
 */
export declare function useApplyXRSpaceMatrix(ref: {
    current?: Group | null;
}, space: XRSpace | undefined, onFrame?: (state: RootState, delta: number, frame: XRFrame | undefined) => void): void;
