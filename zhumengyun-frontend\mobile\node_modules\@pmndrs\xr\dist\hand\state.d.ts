import { WebXRManager } from 'three';
import { XRHandLoaderOptions } from './model.js';
import type { XRHandState } from '../input.js';
export type XRHandInputSource = XRInputSource & {
    hand: XRHand;
};
export declare function isXRHandInputSource(inputSource: XRInputSource): inputSource is XRHandInputSource;
export declare function createXRHandState(id: string, inputSource: XRInputSource, options: XRHandLoaderOptions | undefined, events: ReadonlyArray<XRInputSourceEvent>, isPrimary: boolean): XRHandState;
export declare function updateXRHandState({ inputSource, pose }: XRHandState, frame: XRFrame | undefined, manager: WebXRManager): void;
