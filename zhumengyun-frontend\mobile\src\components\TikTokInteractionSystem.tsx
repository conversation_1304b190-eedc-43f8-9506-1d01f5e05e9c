'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface TikTokInteractionSystemProps {
  onSwipeUp: () => void
  onSwipeDown: () => void
  onSwipeLeft: () => void
  onSwipeRight: () => void
  onDoubleTap: (x: number, y: number) => void
  onLongPress: () => void
  onPullToRefresh: () => void
  onBottomSwipeUp: () => void
  children: React.ReactNode
}

interface LikeAnimation {
  id: string
  x: number
  y: number
}

export default function TikTokInteractionSystem({
  onSwipeUp,
  onSwipeDown,
  onSwipeLeft,
  onSwipeRight,
  onDoubleTap,
  onLongPress,
  onPullToRefresh,
  onBottomSwipeUp,
  children
}: TikTokInteractionSystemProps) {
  const [likeAnimations, setLikeAnimations] = useState<LikeAnimation[]>([])
  const [isLongPressing, setIsLongPressing] = useState(false)
  const [pullDistance, setPullDistance] = useState(0)
  const [showQuickMenu, setShowQuickMenu] = useState(false)
  
  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null)
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null)
  const lastTapRef = useRef<number>(0)

  // 触觉反馈
  const vibrate = useCallback((pattern: number | number[]) => {
    if ('vibrate' in navigator) {
      navigator.vibrate(pattern)
    }
  }, [])

  // 双击检测
  const handleTap = useCallback((x: number, y: number) => {
    const now = Date.now()
    const timeDiff = now - lastTapRef.current
    
    if (timeDiff < 300) {
      // 双击
      vibrate(50) // 轻微震动
      onDoubleTap(x, y)
      
      // 添加点赞动画
      const newAnimation: LikeAnimation = {
        id: Math.random().toString(36).substr(2, 9),
        x,
        y
      }
      setLikeAnimations(prev => [...prev, newAnimation])
      
      // 3秒后移除动画
      setTimeout(() => {
        setLikeAnimations(prev => prev.filter(anim => anim.id !== newAnimation.id))
      }, 3000)
    }
    
    lastTapRef.current = now
  }, [onDoubleTap, vibrate])

  // 长按检测
  const startLongPress = useCallback(() => {
    longPressTimerRef.current = setTimeout(() => {
      setIsLongPressing(true)
      vibrate([50, 50, 50]) // 三次短震动
      onLongPress()
    }, 500)
  }, [onLongPress, vibrate])

  const cancelLongPress = useCallback(() => {
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current)
      longPressTimerRef.current = null
    }
    setIsLongPressing(false)
  }, [])

  // 触摸开始
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0]
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    }
    
    startLongPress()
  }, [startLongPress])

  // 触摸移动
  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!touchStartRef.current) return
    
    const touch = e.touches[0]
    const deltaX = touch.clientX - touchStartRef.current.x
    const deltaY = touch.clientY - touchStartRef.current.y
    
    // 如果移动距离超过阈值，取消长按
    if (Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10) {
      cancelLongPress()
    }
    
    // 顶部下拉刷新
    if (touchStartRef.current.y < 100 && deltaY > 0) {
      setPullDistance(Math.min(deltaY, 100))
    }
  }, [cancelLongPress])

  // 触摸结束
  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    if (!touchStartRef.current) return
    
    const touch = e.changedTouches[0]
    const deltaX = touch.clientX - touchStartRef.current.x
    const deltaY = touch.clientY - touchStartRef.current.y
    const timeDiff = Date.now() - touchStartRef.current.time
    
    cancelLongPress()
    
    // 判断手势类型
    const isSwipe = Math.abs(deltaX) > 50 || Math.abs(deltaY) > 50
    const isQuickSwipe = timeDiff < 300
    
    if (isSwipe) {
      vibrate(30) // 滑动反馈
      
      if (Math.abs(deltaY) > Math.abs(deltaX)) {
        // 垂直滑动
        if (deltaY > 50) {
          onSwipeDown()
        } else if (deltaY < -50) {
          onSwipeUp()
        }
      } else {
        // 水平滑动
        if (deltaX > 50) {
          onSwipeRight()
        } else if (deltaX < -50) {
          onSwipeLeft()
        }
      }
    } else if (timeDiff < 300) {
      // 单击/双击
      handleTap(touch.clientX, touch.clientY)
    }
    
    // 下拉刷新
    if (pullDistance > 60) {
      vibrate(100)
      onPullToRefresh()
    }
    setPullDistance(0)
    
    // 底部上滑菜单
    if (touchStartRef.current.y > window.innerHeight - 100 && deltaY < -50) {
      setShowQuickMenu(true)
      vibrate(50)
      onBottomSwipeUp()
    }
    
    touchStartRef.current = null
  }, [cancelLongPress, handleTap, onSwipeUp, onSwipeDown, onSwipeLeft, onSwipeRight, onPullToRefresh, onBottomSwipeUp, pullDistance, vibrate])

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowUp':
          onSwipeDown()
          break
        case 'ArrowDown':
          onSwipeUp()
          break
        case 'ArrowLeft':
          onSwipeLeft()
          break
        case 'ArrowRight':
          onSwipeRight()
          break
        case ' ':
          e.preventDefault()
          onLongPress()
          break
        case 'r':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault()
            onPullToRefresh()
          }
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [onSwipeUp, onSwipeDown, onSwipeLeft, onSwipeRight, onLongPress, onPullToRefresh])

  return (
    <div 
      className="relative w-full h-full overflow-hidden"
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* 主要内容 */}
      <div className={`transition-transform duration-200 ${isLongPressing ? 'scale-95' : ''}`}>
        {children}
      </div>

      {/* 下拉刷新指示器 */}
      <AnimatePresence>
        {pullDistance > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="absolute top-0 left-0 right-0 z-50 flex justify-center pt-4"
          >
            <div className="bg-black/80 backdrop-blur-sm rounded-full px-4 py-2 flex items-center space-x-2">
              <div className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full ${pullDistance > 60 ? 'animate-spin' : ''}`} />
              <span className="text-white text-sm">
                {pullDistance > 60 ? '释放刷新' : '下拉刷新'}
              </span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 点赞动画 */}
      <AnimatePresence>
        {likeAnimations.map((animation) => (
          <motion.div
            key={animation.id}
            initial={{ 
              opacity: 1, 
              scale: 0,
              x: animation.x - 25,
              y: animation.y - 25
            }}
            animate={{ 
              opacity: 0, 
              scale: 1.5,
              y: animation.y - 100
            }}
            exit={{ opacity: 0 }}
            transition={{ duration: 2, ease: "easeOut" }}
            className="absolute z-50 pointer-events-none"
          >
            <div className="text-4xl">❤️</div>
          </motion.div>
        ))}
      </AnimatePresence>

      {/* 快捷菜单 */}
      <AnimatePresence>
        {showQuickMenu && (
          <motion.div
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 100 }}
            className="absolute bottom-20 left-0 right-0 z-50 px-4"
          >
            <div className="bg-black/90 backdrop-blur-lg rounded-2xl p-4">
              <div className="flex justify-around">
                <button className="flex flex-col items-center space-y-2 p-3 rounded-xl bg-white/10">
                  <span className="text-2xl">🎵</span>
                  <span className="text-white text-xs">音乐</span>
                </button>
                <button className="flex flex-col items-center space-y-2 p-3 rounded-xl bg-white/10">
                  <span className="text-2xl">📱</span>
                  <span className="text-white text-xs">分享</span>
                </button>
                <button className="flex flex-col items-center space-y-2 p-3 rounded-xl bg-white/10">
                  <span className="text-2xl">⭐</span>
                  <span className="text-white text-xs">收藏</span>
                </button>
                <button 
                  onClick={() => setShowQuickMenu(false)}
                  className="flex flex-col items-center space-y-2 p-3 rounded-xl bg-white/10"
                >
                  <span className="text-2xl">✕</span>
                  <span className="text-white text-xs">关闭</span>
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 长按提示 */}
      <AnimatePresence>
        {isLongPressing && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute inset-0 z-40 flex items-center justify-center bg-black/50"
          >
            <div className="bg-white/20 backdrop-blur-lg rounded-2xl p-6 text-center">
              <div className="text-4xl mb-2">⏸️</div>
              <div className="text-white text-lg font-medium">已暂停</div>
              <div className="text-white/70 text-sm mt-1">松开继续播放</div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
