"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),n=require("three"),a=require("@react-three/fiber");function t(e){if(e&&e.__esModule)return e;var n=Object.create(null);return e&&Object.keys(e).forEach((function(a){if("default"!==a){var t=Object.getOwnPropertyDescriptor(e,a);Object.defineProperty(n,a,t.get?t:{enumerable:!0,get:function(){return e[a]}})}})),n.default=e,Object.freeze(n)}var r=t(e),o=t(n);function l(e,n,a){n.traverse((n=>{n.material&&(e.properties.remove(n.material),null==n.material.dispose||n.material.dispose())})),e.info.programs.length=0,e.compile(n,a)}exports.SoftShadows=function({focus:e=0,samples:n=10,size:t=25}){const s=a.useThree((e=>e.gl)),i=a.useThree((e=>e.scene)),u=a.useThree((e=>e.camera));return r.useEffect((()=>{const a=o.ShaderChunk.shadowmap_pars_fragment;return o.ShaderChunk.shadowmap_pars_fragment=o.ShaderChunk.shadowmap_pars_fragment.replace("#ifdef USE_SHADOWMAP","#ifdef USE_SHADOWMAP\n"+(({focus:e=0,size:n=25,samples:a=10}={})=>`\n#define PENUMBRA_FILTER_SIZE float(${n})\n#define RGB_NOISE_FUNCTION(uv) (randRGB(uv))\nvec3 randRGB(vec2 uv) {\n  return vec3(\n    fract(sin(dot(uv, vec2(12.75613, 38.12123))) * 13234.76575),\n    fract(sin(dot(uv, vec2(19.45531, 58.46547))) * 43678.23431),\n    fract(sin(dot(uv, vec2(23.67817, 78.23121))) * 93567.23423)\n  );\n}\n\nvec3 lowPassRandRGB(vec2 uv) {\n  // 3x3 convolution (average)\n  // can be implemented as separable with an extra buffer for a total of 6 samples instead of 9\n  vec3 result = vec3(0);\n  result += RGB_NOISE_FUNCTION(uv + vec2(-1.0, -1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(-1.0,  0.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(-1.0, +1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2( 0.0, -1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2( 0.0,  0.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2( 0.0, +1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(+1.0, -1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(+1.0,  0.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(+1.0, +1.0));\n  result *= 0.111111111; // 1.0 / 9.0\n  return result;\n}\nvec3 highPassRandRGB(vec2 uv) {\n  // by subtracting the low-pass signal from the original signal, we're being left with the high-pass signal\n  // hp(x) = x - lp(x)\n  return RGB_NOISE_FUNCTION(uv) - lowPassRandRGB(uv) + 0.5;\n}\n\n\nvec2 vogelDiskSample(int sampleIndex, int sampleCount, float angle) {\n  const float goldenAngle = 2.399963f; // radians\n  float r = sqrt(float(sampleIndex) + 0.5f) / sqrt(float(sampleCount));\n  float theta = float(sampleIndex) * goldenAngle + angle;\n  float sine = sin(theta);\n  float cosine = cos(theta);\n  return vec2(cosine, sine) * r;\n}\nfloat penumbraSize( const in float zReceiver, const in float zBlocker ) { // Parallel plane estimation\n  return (zReceiver - zBlocker) / zBlocker;\n}\nfloat findBlocker(sampler2D shadowMap, vec2 uv, float compare, float angle) {\n  float texelSize = 1.0 / float(textureSize(shadowMap, 0).x);\n  float blockerDepthSum = float(${e});\n  float blockers = 0.0;\n\n  int j = 0;\n  vec2 offset = vec2(0.);\n  float depth = 0.;\n\n  #pragma unroll_loop_start\n  for(int i = 0; i < ${a}; i ++) {\n    offset = (vogelDiskSample(j, ${a}, angle) * texelSize) * 2.0 * PENUMBRA_FILTER_SIZE;\n    depth = unpackRGBAToDepth( texture2D( shadowMap, uv + offset));\n    if (depth < compare) {\n      blockerDepthSum += depth;\n      blockers++;\n    }\n    j++;\n  }\n  #pragma unroll_loop_end\n\n  if (blockers > 0.0) {\n    return blockerDepthSum / blockers;\n  }\n  return -1.0;\n}\n\n        \nfloat vogelFilter(sampler2D shadowMap, vec2 uv, float zReceiver, float filterRadius, float angle) {\n  float texelSize = 1.0 / float(textureSize(shadowMap, 0).x);\n  float shadow = 0.0f;\n  int j = 0;\n  vec2 vogelSample = vec2(0.0);\n  vec2 offset = vec2(0.0);\n  #pragma unroll_loop_start\n  for (int i = 0; i < ${a}; i++) {\n    vogelSample = vogelDiskSample(j, ${a}, angle) * texelSize;\n    offset = vogelSample * (1.0 + filterRadius * float(${n}));\n    shadow += step( zReceiver, unpackRGBAToDepth( texture2D( shadowMap, uv + offset ) ) );\n    j++;\n  }\n  #pragma unroll_loop_end\n  return shadow * 1.0 / ${a}.0;\n}\n\nfloat PCSS (sampler2D shadowMap, vec4 coords) {\n  vec2 uv = coords.xy;\n  float zReceiver = coords.z; // Assumed to be eye-space z in this code\n  float angle = highPassRandRGB(gl_FragCoord.xy).r * PI2;\n  float avgBlockerDepth = findBlocker(shadowMap, uv, zReceiver, angle);\n  if (avgBlockerDepth == -1.0) {\n    return 1.0;\n  }\n  float penumbraRatio = penumbraSize(zReceiver, avgBlockerDepth);\n  return vogelFilter(shadowMap, uv, zReceiver, 1.25 * penumbraRatio, angle);\n}`)({size:t,samples:n,focus:e})).replace("#if defined( SHADOWMAP_TYPE_PCF )","\nreturn PCSS(shadowMap, shadowCoord);\n#if defined( SHADOWMAP_TYPE_PCF )"),l(s,i,u),()=>{o.ShaderChunk.shadowmap_pars_fragment=a,l(s,i,u)}}),[e,t,n]),null};
