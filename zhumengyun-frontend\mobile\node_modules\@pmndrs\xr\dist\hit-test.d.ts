import { Matrix4, Object3D } from 'three';
import { XRStore } from './internals.js';
export type GetWorldMatrixFromXRHitTest = (target: Matrix4, result: XRHitTestResult) => boolean;
export declare function createXRHitTestSource(store: XRStore<any>, session: XRSession, relativeTo: Object3D | XRSpace | XRReferenceSpaceType, trackableType?: XRHitTestTrackableType | Array<XRHitTestTrackableType>): Promise<{
    source: XRHitTestSource;
    getWorldMatrix: (target: Matrix4, result: XRHitTestResult) => boolean;
} | undefined>;
export declare function requestXRHitTest(store: XRStore<any>, relativeTo: Object3D | XRSpace | XRReferenceSpaceType, trackableType?: XRHitTestTrackableType | Array<XRHitTestTrackableType>): Promise<{
    results: XRHitTestResult[];
    getWorldMatrix: (target: Matrix4, result: XRHitTestResult) => boolean;
} | undefined>;
