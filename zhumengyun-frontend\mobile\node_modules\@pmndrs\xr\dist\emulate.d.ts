import { XRDev<PERSON> } from 'iwer';
import { Euler, Quaternion, Vector3, Vector3Tuple, Vector4Tuple } from 'three';
import type { XRDeviceOptions } from 'iwer/lib/device/XRDevice';
declare const configurations: {
    metaQuest3: import("iwer").XRDeviceConfig;
    metaQuest2: import("iwer").XRDeviceConfig;
    metaQuestPro: import("iwer").XRDeviceConfig;
    oculusQuest1: import("iwer").XRDeviceConfig;
};
export type EmulatorType = keyof typeof configurations;
export type EmulatorTransformationOptions = {
    position?: Vector3 | Vector3Tuple;
    rotation?: Euler | Vector3Tuple;
    quaternion?: Quaternion | Vector4Tuple;
};
export type DefaultSyntheticEnvironment = 'meeting_room' | 'living_room' | 'music_room' | 'office_large' | 'office_small';
export type EmulatorOptions = EmulatorType | ({
    type?: EmulatorType;
    primaryInputMode?: XRDevice['primaryInputMode'];
    headset?: EmulatorTransformationOptions;
    inject?: boolean | {
        hostname: string;
    };
    controller?: Partial<Record<XRHandedness, EmulatorTransformationOptions>>;
    hand?: Partial<Record<XRHandedness, EmulatorTransformationOptions>>;
    syntheticEnvironment?: null | boolean | JSON | (string & {}) | DefaultSyntheticEnvironment;
} & Partial<Pick<XRDeviceOptions, 'ipd' | 'fovy' | 'stereoEnabled' | 'canvasContainer'>>);
export declare function emulate(options: EmulatorOptions): XRDevice;
export {};
