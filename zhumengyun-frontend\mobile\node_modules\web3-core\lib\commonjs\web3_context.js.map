{"version": 3, "file": "web3_context.js", "sourceRoot": "", "sources": ["../../src/web3_context.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;EAeE;AACF,gDAAgD;AAChD,6CAA2D;AAa3D,2CAAuC;AACvC,yDAAwE;AACxE,yCAAiD;AAGjD,mEAA2D;AAC3D,2CAA2C;AAC3C,qDAAkF;AAClF,uEAA+D;AAE/D,iFAAyE;AAmDzE,MAAa,WAMX,SAAQ,2BAAU;IASnB,YACC,iBAG8C;;QAE9C,KAAK,EAAE,CAAC;QAZO,cAAS,GAAG,4CAAkB,CAAC,SAAS,CAAC;QAcxD,uGAAuG;QACvG,IACC,IAAA,sBAAS,EAAC,iBAAiB,CAAC;YAC5B,CAAC,OAAO,iBAAiB,KAAK,QAAQ,IAAI,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;YAC1E,IAAA,8BAAmB,EAAC,iBAA4C,CAAC,EAChE,CAAC;YACF,IAAI,CAAC,eAAe,GAAG,IAAI,4CAAkB,CAC5C,iBAAiE,CACjE,CAAC;YACF,IAAI,CAAC,oBAAoB,GAAG,IAAI,sDAAuB,CACtD,IAAI,CAAC,eAAe,EACpB,EAAoB,CACpB,CAAC;YAEF,OAAO;QACR,CAAC;QAED,MAAM,EACL,MAAM,EACN,QAAQ,EACR,cAAc,EACd,mBAAmB,EACnB,uBAAuB,EACvB,eAAe,EACf,MAAM,EACN,wBAAwB,GACxB,GAAG,iBAAgE,CAAC;QAErE,IAAI,CAAC,SAAS,CAAC,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,EAAE,CAAC,CAAC;QAE7B,IAAI,CAAC,eAAe;YACnB,cAAc,aAAd,cAAc,cAAd,cAAc,GACd,IAAI,4CAAkB,CACrB,QAAQ,EACR,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,0BAA0B,0CAAE,uCAAuC,EAC3E,wBAAwB,CACxB,CAAC;QAEH,IAAI,mBAAmB,EAAE,CAAC;YACzB,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;QACjD,CAAC;aAAM,CAAC;YACP,IAAI,CAAC,oBAAoB,GAAG,IAAI,sDAAuB,CACtD,IAAI,CAAC,cAAc,EACnB,uBAAuB,aAAvB,uBAAuB,cAAvB,uBAAuB,GAAK,EAAqB,CACjD,CAAC;QACH,CAAC;QAED,IAAI,eAAe,EAAE,CAAC;YACrB,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACzC,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACvB,CAAC;IACF,CAAC;IAED,IAAW,cAAc;QACxB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,mBAAmB;QAC7B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IAClC,CAAC;IAED,IAAW,MAAM;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC;IACrB,CAAC;IAED,IAAW,eAAe;QACzB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC9B,CAAC;IAED,8DAA8D;IACvD,MAAM,CAAC,iBAAiB,CAE9B,GAAG,IAAgC;QAEnC,OAAO,IAAI,IAAI,CAAC,GAAI,IAAI,CAAC,OAAO,EAAiC,CAAC,CAAC;IACpE,CAAC;IAEM,gBAAgB;;QACtB,OAAO;YACN,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,uBAAuB,EAAE,MAAA,IAAI,CAAC,mBAAmB,0CAAE,uBAAuB;YAC1E,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,eAAe,EAAE,IAAI,CAAC,eAAe;SACrC,CAAC;IACH,CAAC;IAED;;;;OAIG;IACI,GAAG,CACT,UAAyC,EACzC,GAAG,IAAa;QAEhB,MAAM,eAAe,GAAM,IAAI,UAAU,CACxC,GAAI,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAA2C,CAChF,CAAC;QAEF,IAAI,CAAC,EAAE,CAAC,gCAAe,CAAC,aAAa,EAAE,KAAK,CAAC,EAAE;YAC9C,mEAAmE;YACnE,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,6HAA6H;QAC7H,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC;QAExC,OAAO,eAAe,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,IAAI,CAAwB,aAAgB;QAClD,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,aAAa,CAAC,cAAc,CAAC;QACpD,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;QACvC,mEAAmE;QACnE,IAAI,CAAC,oBAAoB,GAAG,aAAa,CAAC,mBAAmB,CAAC;QAC9D,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC,MAAM,CAAC;QACpC,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,gBAAgB,CAAC;QAEvD,aAAa,CAAC,EAAE,CAAC,gCAAe,CAAC,aAAa,EAAE,KAAK,CAAC,EAAE;YACvD,mEAAmE;YACnE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,gDAAgD;IACzC,cAAc,CAAC,MAAsB;QAC3C,6HAA6H;QAC7H,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,SAAS;YAC7C,MAAM,IAAI,0CAA4B,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QAEhE,MAAM,aAAa,GAAG;YACrB,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,MAAM;SAChC,CAAC;QACF,aAAa,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;IACpC,CAAC;IAED;;;;;;;;;;;;;OAaG;IAEH,IAAW,QAAQ;QAClB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC7B,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IAEH,IAAW,QAAQ,CAAC,QAAsD;QACzE,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,IAAW,eAAe;QACzB,OAAO,IAAI,CAAC,cAAc,CAAC,QAAiC,CAAC;IAC9D,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,IAAW,eAAe,CAAC,QAAsD;QAChF,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACH,kDAAkD;IAClD,IAAW,aAAa;QACvB,OAAO,WAAW,CAAC,aAAa,CAAC;IAClC,CAAC;IACD;;;;;OAKG;IACI,WAAW,CAAC,QAA2C;QAC7D,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,OAAO,IAAI,CAAC;IACb,CAAC;IAEM,2BAA2B,CAAC,wBAAuD;QACzF,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,wBAAwB,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACtB,OAAO,wCAAgB,CAAC,IAAI,CAC3B,SAAS,EACT,IAAI,CAAC,eAAgD,CACrD,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,SAA0B;;QACvC,6HAA6H;QAC7H,IAAI,SAAS,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;YAClD,6HAA6H;YAC7H,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;QAE/B,MAAA,SAAS,CAAC,OAAO,0CAAE,OAAO,CAAC,OAAO,CAAC,EAAE;YACpC,MAAM,MAAM,GAAG,CAAO,GAAG,WAAsB,EAAE,EAAE;gBAClD,OAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;oBACxB,MAAM,EAAE,OAAO,CAAC,IAAI;oBACpB,MAAM,EAAE,WAAW;iBACnB,CAAC,CAAA;cAAA,CAAC;YAEJ,IAAI,SAAS,CAAC,QAAQ;gBACrB,6HAA6H;gBAC7H,sEAAsE;gBACtE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;YACjD,6HAA6H;;gBACxH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;QAClC,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACb,CAAC;;AA5TF,kCA6TC;AAtTuB,qBAAS,GAAG,4CAAkB,CAAC,SAAS,AAA/B,CAAgC;AAwTjE;;;;;;;;;;;;;;;;GAgBG;AACH,MAAsB,cAEpB,SAAQ,WAAgB;IAGzB,kDAAkD;IACxC,0BAA0B,CACnC,IAAa,EACb,OAAuB;QAEvB,sCAAkB,CAAC,uBAAuB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;CACD;AAZD,wCAYC;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,MAAsB,iBAAqD,SAAQ,cAElF;CAAG;AAFJ,8CAEI"}