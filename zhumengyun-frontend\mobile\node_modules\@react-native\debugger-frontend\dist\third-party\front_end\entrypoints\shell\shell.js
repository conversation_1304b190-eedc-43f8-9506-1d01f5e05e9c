import"../../Images/Images.js";import"../../core/dom_extension/dom_extension.js";import*as e from"../../core/common/common.js";import*as t from"../../core/host/host.js";import*as o from"../../core/i18n/i18n.js";import*as i from"../../core/root/root.js";import*as a from"../../core/sdk/sdk.js";import*as n from"../../models/breakpoints/breakpoints.js";import*as s from"../../models/workspace/workspace.js";import*as r from"../../ui/legacy/components/object_ui/object_ui.js";import*as l from"../../ui/legacy/components/quick_open/quick_open.js";import*as c from"../../ui/legacy/legacy.js";import*as g from"../../ui/legacy/components/utils/utils.js";import*as d from"../../panels/console/console.js";import"../main/main.js";navigator.userAgent.includes("Chrome")||alert("DevTools is only supported in Chrome, Edge and other Blink-based browsers.\n\nOpen this page in a compatible browser to continue.");const u={showSources:"Show Sources",sources:"Sources",showWorkspace:"Show Workspace",workspace:"Workspace",showSnippets:"Show Snippets",snippets:"Snippets",showSearch:"Show Search",search:"Search",showQuickSource:"Show Quick source",quickSource:"Quick source",showThreads:"Show Threads",threads:"Threads",showScope:"Show Scope",scope:"Scope",showWatch:"Show Watch",watch:"Watch",showBreakpoints:"Show Breakpoints",breakpoints:"Breakpoints",pauseScriptExecution:"Pause script execution",resumeScriptExecution:"Resume script execution",stepOverNextFunctionCall:"Step over next function call",stepIntoNextFunctionCall:"Step into next function call",step:"Step",stepOutOfCurrentFunction:"Step out of current function",runSnippet:"Run snippet",deactivateBreakpoints:"Deactivate breakpoints",activateBreakpoints:"Activate breakpoints",addSelectedTextToWatches:"Add selected text to watches",evaluateSelectedTextInConsole:"Evaluate selected text in console",switchFile:"Switch file",rename:"Rename",closeAll:"Close All",jumpToPreviousEditingLocation:"Jump to previous editing location",jumpToNextEditingLocation:"Jump to next editing location",closeTheActiveTab:"Close the active tab",goToLine:"Go to line",goToAFunctionDeclarationruleSet:"Go to a function declaration/rule set",toggleBreakpoint:"Toggle breakpoint",toggleBreakpointEnabled:"Toggle breakpoint enabled",toggleBreakpointInputWindow:"Toggle breakpoint input window",save:"Save",saveAll:"Save all",createNewSnippet:"Create new snippet",addFolderToWorkspace:"Add folder to workspace",addFolder:"Add folder",previousCallFrame:"Previous call frame",nextCallFrame:"Next call frame",incrementCssUnitBy:"Increment CSS unit by {PH1}",decrementCssUnitBy:"Decrement CSS unit by {PH1}",searchInAnonymousAndContent:"Search in anonymous and content scripts",doNotSearchInAnonymousAndContent:"Do not search in anonymous and content scripts",automaticallyRevealFilesIn:"Automatically reveal files in sidebar",doNotAutomaticallyRevealFilesIn:"Do not automatically reveal files in sidebar",javaScriptSourceMaps:"JavaScript source maps",enableJavaScriptSourceMaps:"Enable JavaScript source maps",disableJavaScriptSourceMaps:"Disable JavaScript source maps",tabMovesFocus:"Tab moves focus",enableTabMovesFocus:"Enable tab moves focus",disableTabMovesFocus:"Disable tab moves focus",detectIndentation:"Detect indentation",doNotDetectIndentation:"Do not detect indentation",automaticallyPrettyPrintMinifiedSources:"Automatically pretty print minified sources",doNotAutomaticallyPrettyPrintMinifiedSources:"Do not automatically pretty print minified sources",autocompletion:"Autocompletion",enableAutocompletion:"Enable autocompletion",disableAutocompletion:"Disable autocompletion",bracketClosing:"Auto closing brackets",enableBracketClosing:"Enable auto closing brackets",disableBracketClosing:"Disable auto closing brackets",bracketMatching:"Bracket matching",enableBracketMatching:"Enable bracket matching",disableBracketMatching:"Disable bracket matching",codeFolding:"Code folding",enableCodeFolding:"Enable code folding",disableCodeFolding:"Disable code folding",showWhitespaceCharacters:"Show whitespace characters:",doNotShowWhitespaceCharacters:"Do not show whitespace characters",none:"None",showAllWhitespaceCharacters:"Show all whitespace characters",all:"All",showTrailingWhitespaceCharacters:"Show trailing whitespace characters",trailing:"Trailing",displayVariableValuesInlineWhile:"Display variable values inline while debugging",doNotDisplayVariableValuesInline:"Do not display variable values inline while debugging",cssSourceMaps:"CSS source maps",enableCssSourceMaps:"Enable CSS source maps",disableCssSourceMaps:"Disable CSS source maps",allowScrollingPastEndOfFile:"Allow scrolling past end of file",disallowScrollingPastEndOfFile:"Disallow scrolling past end of file",wasmAutoStepping:"When debugging Wasm with debug information, do not pause on wasm bytecode if possible",enableWasmAutoStepping:"Enable Wasm auto-stepping",disableWasmAutoStepping:"Disable Wasm auto-stepping",goTo:"Go to",line:"Line",symbol:"Symbol",open:"Open",file:"File",disableAutoFocusOnDebuggerPaused:"Do not focus Sources panel when triggering a breakpoint",enableAutoFocusOnDebuggerPaused:"Focus Sources panel when triggering a breakpoint",revealActiveFileInSidebar:"Reveal active file in navigator sidebar",toggleNavigatorSidebar:"Toggle navigator sidebar",toggleDebuggerSidebar:"Toggle debugger sidebar",nextEditorTab:"Next editor",previousEditorTab:"Previous editor"},p=o.i18n.registerUIStrings("panels/sources/sources-meta.ts",u),m=o.i18n.getLazilyComputedLocalizedString.bind(void 0,p);let S,y,w;async function h(){return S||(S=await import("../../panels/sources/sources.js")),S}async function v(){return y||(y=await import("../../panels/sources/components/components.js")),y}function b(e){return void 0===S?[]:e(S)}c.ViewManager.registerViewExtension({location:"panel",id:"sources",commandPrompt:m(u.showSources),title:m(u.sources),order:30,loadView:async()=>(await h()).SourcesPanel.SourcesPanel.instance()}),c.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-files",commandPrompt:m(u.showWorkspace),title:m(u.workspace),order:3,persistence:"permanent",loadView:async()=>new((await h()).SourcesNavigator.FilesNavigatorView),condition:i.Runtime.conditions.notSourcesHideAddFolder}),c.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-snippets",commandPrompt:m(u.showSnippets),title:m(u.snippets),order:6,persistence:"permanent",loadView:async()=>new((await h()).SourcesNavigator.SnippetsNavigatorView)}),c.ViewManager.registerViewExtension({location:"drawer-view",id:"sources.search-sources-tab",commandPrompt:m(u.showSearch),title:m(u.search),order:7,persistence:"closeable",loadView:async()=>new((await h()).SearchSourcesView.SearchSourcesView)}),c.ViewManager.registerViewExtension({location:"drawer-view",id:"sources.quick",commandPrompt:m(u.showQuickSource),title:m(u.quickSource),persistence:"closeable",order:1e3,loadView:async()=>new((await h()).SourcesPanel.QuickSourceView)}),c.ViewManager.registerViewExtension({id:"sources.threads",commandPrompt:m(u.showThreads),title:m(u.threads),persistence:"permanent",loadView:async()=>new((await h()).ThreadsSidebarPane.ThreadsSidebarPane)}),c.ViewManager.registerViewExtension({id:"sources.scope-chain",commandPrompt:m(u.showScope),title:m(u.scope),persistence:"permanent",loadView:async()=>(await h()).ScopeChainSidebarPane.ScopeChainSidebarPane.instance()}),c.ViewManager.registerViewExtension({id:"sources.watch",commandPrompt:m(u.showWatch),title:m(u.watch),persistence:"permanent",loadView:async()=>(await h()).WatchExpressionsSidebarPane.WatchExpressionsSidebarPane.instance(),hasToolbar:!0}),c.ViewManager.registerViewExtension({id:"sources.js-breakpoints",commandPrompt:m(u.showBreakpoints),title:m(u.breakpoints),persistence:"permanent",loadView:async()=>(await v()).BreakpointsView.BreakpointsView.instance().wrapper}),c.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"debugger.toggle-pause",iconClass:"pause",toggleable:!0,toggledIconClass:"resume",loadActionDelegate:async()=>new((await h()).SourcesPanel.RevealingActionDelegate),contextTypes:()=>b((e=>[e.SourcesView.SourcesView,c.ShortcutRegistry.ForwardedShortcut])),options:[{value:!0,title:m(u.pauseScriptExecution)},{value:!1,title:m(u.resumeScriptExecution)}],bindings:[{shortcut:"F8",keybindSets:["devToolsDefault"]},{platform:"windows,linux",shortcut:"Ctrl+\\"},{shortcut:"F5",keybindSets:["vsCode"]},{shortcut:"Shift+F5",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+\\"}]}),c.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"debugger.step-over",loadActionDelegate:async()=>new((await h()).SourcesPanel.ActionDelegate),title:m(u.stepOverNextFunctionCall),iconClass:"step-over",contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"F10",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+'"},{platform:"mac",shortcut:"Meta+'"}]}),c.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"debugger.step-into",loadActionDelegate:async()=>new((await h()).SourcesPanel.ActionDelegate),title:m(u.stepIntoNextFunctionCall),iconClass:"step-into",contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"F11",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+;"},{platform:"mac",shortcut:"Meta+;"}]}),c.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"debugger.step",loadActionDelegate:async()=>new((await h()).SourcesPanel.ActionDelegate),title:m(u.step),iconClass:"step",contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"F9",keybindSets:["devToolsDefault"]}]}),c.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"debugger.step-out",loadActionDelegate:async()=>new((await h()).SourcesPanel.ActionDelegate),title:m(u.stepOutOfCurrentFunction),iconClass:"step-out",contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"Shift+F11",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Shift+Ctrl+;"},{platform:"mac",shortcut:"Shift+Meta+;"}]}),c.ActionRegistration.registerActionExtension({actionId:"debugger.run-snippet",category:"DEBUGGER",loadActionDelegate:async()=>new((await h()).SourcesPanel.ActionDelegate),title:m(u.runSnippet),iconClass:"play",contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Enter"},{platform:"mac",shortcut:"Meta+Enter"}]}),c.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"debugger.toggle-breakpoints-active",iconClass:"breakpoint-crossed",toggledIconClass:"breakpoint-crossed-filled",toggleable:!0,loadActionDelegate:async()=>new((await h()).SourcesPanel.ActionDelegate),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),options:[{value:!0,title:m(u.deactivateBreakpoints)},{value:!1,title:m(u.activateBreakpoints)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+F8"},{platform:"mac",shortcut:"Meta+F8"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.add-to-watch",loadActionDelegate:async()=>(await h()).WatchExpressionsSidebarPane.WatchExpressionsSidebarPane.instance(),category:"DEBUGGER",title:m(u.addSelectedTextToWatches),contextTypes:()=>b((e=>[e.UISourceCodeFrame.UISourceCodeFrame])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+A"},{platform:"mac",shortcut:"Meta+Shift+A"}]}),c.ActionRegistration.registerActionExtension({actionId:"debugger.evaluate-selection",category:"DEBUGGER",loadActionDelegate:async()=>new((await h()).SourcesPanel.ActionDelegate),title:m(u.evaluateSelectedTextInConsole),contextTypes:()=>b((e=>[e.UISourceCodeFrame.UISourceCodeFrame])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+E"},{platform:"mac",shortcut:"Meta+Shift+E"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.switch-file",category:"SOURCES",title:m(u.switchFile),loadActionDelegate:async()=>new((await h()).SourcesView.SwitchFileActionDelegate),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Alt+O"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.rename",category:"SOURCES",title:m(u.rename),bindings:[{platform:"windows,linux",shortcut:"F2"},{platform:"mac",shortcut:"Enter"}]}),c.ActionRegistration.registerActionExtension({category:"SOURCES",actionId:"sources.close-all",loadActionDelegate:async()=>new((await h()).SourcesView.ActionDelegate),title:m(u.closeAll)}),c.ActionRegistration.registerActionExtension({actionId:"sources.jump-to-previous-location",category:"SOURCES",title:m(u.jumpToPreviousEditingLocation),loadActionDelegate:async()=>new((await h()).SourcesView.ActionDelegate),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Alt+Minus"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.jump-to-next-location",category:"SOURCES",title:m(u.jumpToNextEditingLocation),loadActionDelegate:async()=>new((await h()).SourcesView.ActionDelegate),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Alt+Plus"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.close-editor-tab",category:"SOURCES",title:m(u.closeTheActiveTab),loadActionDelegate:async()=>new((await h()).SourcesView.ActionDelegate),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Alt+w"},{shortcut:"Ctrl+W",keybindSets:["vsCode"]},{platform:"windows",shortcut:"Ctrl+F4",keybindSets:["vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.next-editor-tab",category:"SOURCES",title:m(u.nextEditorTab),loadActionDelegate:async()=>new((await h()).SourcesView.ActionDelegate),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+PageDown",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+PageDown",keybindSets:["devToolsDefault","vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.previous-editor-tab",category:"SOURCES",title:m(u.previousEditorTab),loadActionDelegate:async()=>new((await h()).SourcesView.ActionDelegate),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+PageUp",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+PageUp",keybindSets:["devToolsDefault","vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.go-to-line",category:"SOURCES",title:m(u.goToLine),loadActionDelegate:async()=>new((await h()).SourcesView.ActionDelegate),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Ctrl+g",keybindSets:["devToolsDefault","vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.go-to-member",category:"SOURCES",title:m(u.goToAFunctionDeclarationruleSet),loadActionDelegate:async()=>new((await h()).SourcesView.ActionDelegate),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+o",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+Shift+o",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+T",keybindSets:["vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+T",keybindSets:["vsCode"]},{shortcut:"F12",keybindSets:["vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"debugger.toggle-breakpoint",category:"DEBUGGER",title:m(u.toggleBreakpoint),bindings:[{platform:"windows,linux",shortcut:"Ctrl+b",keybindSets:["devToolsDefault"]},{platform:"mac",shortcut:"Meta+b",keybindSets:["devToolsDefault"]},{shortcut:"F9",keybindSets:["vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"debugger.toggle-breakpoint-enabled",category:"DEBUGGER",title:m(u.toggleBreakpointEnabled),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+b"},{platform:"mac",shortcut:"Meta+Shift+b"}]}),c.ActionRegistration.registerActionExtension({actionId:"debugger.breakpoint-input-window",category:"DEBUGGER",title:m(u.toggleBreakpointInputWindow),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Alt+b"},{platform:"mac",shortcut:"Meta+Alt+b"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.save",category:"SOURCES",title:m(u.save),loadActionDelegate:async()=>new((await h()).SourcesView.ActionDelegate),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+s",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+s",keybindSets:["devToolsDefault","vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.save-all",category:"SOURCES",title:m(u.saveAll),loadActionDelegate:async()=>new((await h()).SourcesView.ActionDelegate),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+s"},{platform:"mac",shortcut:"Meta+Alt+s"},{platform:"windows,linux",shortcut:"Ctrl+K S",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+Alt+S",keybindSets:["vsCode"]}]}),c.ActionRegistration.registerActionExtension({category:"SOURCES",actionId:"sources.create-snippet",loadActionDelegate:async()=>new((await h()).SourcesNavigator.ActionDelegate),title:m(u.createNewSnippet)}),t.InspectorFrontendHost.InspectorFrontendHostInstance.isHostedMode()||c.ActionRegistration.registerActionExtension({category:"SOURCES",actionId:"sources.add-folder-to-workspace",loadActionDelegate:async()=>new((await h()).SourcesNavigator.ActionDelegate),iconClass:"plus",title:m(u.addFolderToWorkspace)}),c.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"debugger.previous-call-frame",loadActionDelegate:async()=>new((await h()).CallStackSidebarPane.ActionDelegate),title:m(u.previousCallFrame),contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"Ctrl+,"}]}),c.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"debugger.next-call-frame",loadActionDelegate:async()=>new((await h()).CallStackSidebarPane.ActionDelegate),title:m(u.nextCallFrame),contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"Ctrl+."}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.search",title:m(u.search),loadActionDelegate:async()=>new((await h()).SearchSourcesView.ActionDelegate),category:"SOURCES",bindings:[{platform:"mac",shortcut:"Meta+Alt+F",keybindSets:["devToolsDefault"]},{platform:"windows,linux",shortcut:"Ctrl+Shift+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+Shift+J",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+Shift+F",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+Shift+J",keybindSets:["vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.increment-css",category:"SOURCES",title:m(u.incrementCssUnitBy,{PH1:1}),bindings:[{shortcut:"Alt+Up"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.increment-css-by-ten",title:m(u.incrementCssUnitBy,{PH1:10}),category:"SOURCES",bindings:[{shortcut:"Alt+PageUp"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.decrement-css",category:"SOURCES",title:m(u.decrementCssUnitBy,{PH1:1}),bindings:[{shortcut:"Alt+Down"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.decrement-css-by-ten",category:"SOURCES",title:m(u.decrementCssUnitBy,{PH1:10}),bindings:[{shortcut:"Alt+PageDown"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.reveal-in-navigator-sidebar",category:"SOURCES",title:m(u.revealActiveFileInSidebar),loadActionDelegate:async()=>new((await h()).SourcesPanel.ActionDelegate),contextTypes:()=>b((e=>[e.SourcesView.SourcesView]))}),c.ActionRegistration.registerActionExtension({actionId:"sources.toggle-navigator-sidebar",category:"SOURCES",title:m(u.toggleNavigatorSidebar),loadActionDelegate:async()=>new((await h()).SourcesPanel.ActionDelegate),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+y",keybindSets:["devToolsDefault"]},{platform:"mac",shortcut:"Meta+Shift+y",keybindSets:["devToolsDefault"]},{platform:"windows,linux",shortcut:"Ctrl+b",keybindSets:["vsCode"]},{platform:"windows,linux",shortcut:"Meta+b",keybindSets:["vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.toggle-debugger-sidebar",category:"SOURCES",title:m(u.toggleDebuggerSidebar),loadActionDelegate:async()=>new((await h()).SourcesPanel.ActionDelegate),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+h"},{platform:"mac",shortcut:"Meta+Shift+h"}]}),e.Settings.registerSettingExtension({settingName:"navigator-group-by-folder",settingType:"boolean",defaultValue:!0}),e.Settings.registerSettingExtension({settingName:"navigator-group-by-authored",settingType:"boolean",defaultValue:!1}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:m(u.searchInAnonymousAndContent),settingName:"search-in-anonymous-and-content-scripts",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:m(u.searchInAnonymousAndContent)},{value:!1,title:m(u.doNotSearchInAnonymousAndContent)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:m(u.automaticallyRevealFilesIn),settingName:"auto-reveal-in-navigator",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:m(u.automaticallyRevealFilesIn)},{value:!1,title:m(u.doNotAutomaticallyRevealFilesIn)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:m(u.javaScriptSourceMaps),settingName:"js-source-maps-enabled",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:m(u.enableJavaScriptSourceMaps)},{value:!1,title:m(u.disableJavaScriptSourceMaps)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:m(u.tabMovesFocus),settingName:"text-editor-tab-moves-focus",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:m(u.enableTabMovesFocus)},{value:!1,title:m(u.disableTabMovesFocus)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:m(u.detectIndentation),settingName:"text-editor-auto-detect-indent",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:m(u.detectIndentation)},{value:!1,title:m(u.doNotDetectIndentation)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:m(u.autocompletion),settingName:"text-editor-autocompletion",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:m(u.enableAutocompletion)},{value:!1,title:m(u.disableAutocompletion)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:m(u.bracketClosing),settingName:"text-editor-bracket-closing",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:m(u.enableBracketClosing)},{value:!1,title:m(u.disableBracketClosing)}]}),e.Settings.registerSettingExtension({category:"SOURCES",title:m(u.bracketMatching),settingName:"text-editor-bracket-matching",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:m(u.enableBracketMatching)},{value:!1,title:m(u.disableBracketMatching)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:m(u.codeFolding),settingName:"text-editor-code-folding",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:m(u.enableCodeFolding)},{value:!1,title:m(u.disableCodeFolding)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:m(u.showWhitespaceCharacters),settingName:"show-whitespaces-in-editor",settingType:"enum",defaultValue:"original",options:[{title:m(u.doNotShowWhitespaceCharacters),text:m(u.none),value:"none"},{title:m(u.showAllWhitespaceCharacters),text:m(u.all),value:"all"},{title:m(u.showTrailingWhitespaceCharacters),text:m(u.trailing),value:"trailing"}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:m(u.displayVariableValuesInlineWhile),settingName:"inline-variable-values",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:m(u.displayVariableValuesInlineWhile)},{value:!1,title:m(u.doNotDisplayVariableValuesInline)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:m(u.enableAutoFocusOnDebuggerPaused),settingName:"auto-focus-on-debugger-paused-enabled",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:m(u.enableAutoFocusOnDebuggerPaused)},{value:!1,title:m(u.disableAutoFocusOnDebuggerPaused)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:m(u.automaticallyPrettyPrintMinifiedSources),settingName:"auto-pretty-print-minified",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:m(u.automaticallyPrettyPrintMinifiedSources)},{value:!1,title:m(u.doNotAutomaticallyPrettyPrintMinifiedSources)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:m(u.cssSourceMaps),settingName:"css-source-maps-enabled",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:m(u.enableCssSourceMaps)},{value:!1,title:m(u.disableCssSourceMaps)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:m(u.allowScrollingPastEndOfFile),settingName:"allow-scroll-past-eof",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:m(u.allowScrollingPastEndOfFile)},{value:!1,title:m(u.disallowScrollingPastEndOfFile)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Local",title:m(u.wasmAutoStepping),settingName:"wasm-auto-stepping",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:m(u.enableWasmAutoStepping)},{value:!1,title:m(u.disableWasmAutoStepping)}]}),c.ViewManager.registerLocationResolver({name:"navigator-view",category:"SOURCES",loadResolver:async()=>(await h()).SourcesPanel.SourcesPanel.instance()}),c.ViewManager.registerLocationResolver({name:"sources.sidebar-top",category:"SOURCES",loadResolver:async()=>(await h()).SourcesPanel.SourcesPanel.instance()}),c.ViewManager.registerLocationResolver({name:"sources.sidebar-bottom",category:"SOURCES",loadResolver:async()=>(await h()).SourcesPanel.SourcesPanel.instance()}),c.ViewManager.registerLocationResolver({name:"sources.sidebar-tabs",category:"SOURCES",loadResolver:async()=>(await h()).SourcesPanel.SourcesPanel.instance()}),c.ContextMenu.registerProvider({contextTypes:()=>[s.UISourceCode.UISourceCode,s.UISourceCode.UILocation,a.RemoteObject.RemoteObject,a.NetworkRequest.NetworkRequest,...b((e=>[e.UISourceCodeFrame.UISourceCodeFrame]))],loadProvider:async()=>(await h()).SourcesPanel.SourcesPanel.instance(),experiment:void 0}),c.ContextMenu.registerProvider({loadProvider:async()=>(await h()).WatchExpressionsSidebarPane.WatchExpressionsSidebarPane.instance(),contextTypes:()=>[r.ObjectPropertiesSection.ObjectPropertyTreeElement,...b((e=>[e.UISourceCodeFrame.UISourceCodeFrame]))],experiment:void 0}),e.Revealer.registerRevealer({contextTypes:()=>[s.UISourceCode.UILocation],destination:e.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>new((await h()).SourcesPanel.UILocationRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[s.UISourceCode.UILocationRange],destination:e.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>new((await h()).SourcesPanel.UILocationRangeRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[a.DebuggerModel.Location],destination:e.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>new((await h()).SourcesPanel.DebuggerLocationRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[s.UISourceCode.UISourceCode],destination:e.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>new((await h()).SourcesPanel.UISourceCodeRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],destination:e.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>new((await h()).SourcesPanel.DebuggerPausedDetailsRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[n.BreakpointManager.BreakpointLocation],destination:e.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>new((await h()).DebuggerPlugin.BreakpointLocationRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>b((e=>[e.SearchSourcesView.SearchSources])),destination:void 0,loadRevealer:async()=>new((await h()).SearchSourcesView.Revealer)}),c.Toolbar.registerToolbarItem({actionId:"sources.add-folder-to-workspace",location:"files-navigator-toolbar",label:m(u.addFolder),showLabel:!0,loadItem:void 0,order:void 0,separator:void 0}),c.Context.registerListener({contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await v()).BreakpointsView.BreakpointsSidebarController.instance()}),c.Context.registerListener({contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await h()).CallStackSidebarPane.CallStackSidebarPane.instance()}),c.Context.registerListener({contextTypes:()=>[a.DebuggerModel.CallFrame],loadListener:async()=>(await h()).ScopeChainSidebarPane.ScopeChainSidebarPane.instance()}),c.ContextMenu.registerItem({location:"navigatorMenu/default",actionId:"quick-open.show",order:void 0}),c.ContextMenu.registerItem({location:"mainMenu/default",actionId:"sources.search",order:void 0}),l.FilteredListWidget.registerProvider({prefix:"@",iconName:"symbol",iconWidth:"20px",provider:async()=>new((await h()).OutlineQuickOpen.OutlineQuickOpen),titlePrefix:m(u.goTo),titleSuggestion:m(u.symbol)}),l.FilteredListWidget.registerProvider({prefix:":",iconName:"colon",iconWidth:"20px",provider:async()=>new((await h()).GoToLineQuickOpen.GoToLineQuickOpen),titlePrefix:m(u.goTo),titleSuggestion:m(u.line)}),l.FilteredListWidget.registerProvider({prefix:"",iconName:"document",iconWidth:"20px",provider:async()=>new((await h()).OpenFileQuickOpen.OpenFileQuickOpen),titlePrefix:m(u.open),titleSuggestion:m(u.file)});const f={memory:"Memory",liveHeapProfile:"Live Heap Profile",startRecordingHeapAllocations:"Start recording heap allocations",stopRecordingHeapAllocations:"Stop recording heap allocations",startRecordingHeapAllocationsAndReload:"Start recording heap allocations and reload the page",startStopRecording:"Start/stop recording",showMemory:"Show Memory",showLiveHeapProfile:"Show Live Heap Profile",clearAllProfiles:"Clear all profiles",saveProfile:"Save profile…",loadProfile:"Load profile…",deleteProfile:"Delete profile"},C=o.i18n.registerUIStrings("panels/profiler/profiler-meta.ts",f),x=o.i18n.getLazilyComputedLocalizedString.bind(void 0,C);async function E(){return w||(w=await import("../../panels/profiler/profiler.js")),w}function A(e){return void 0===w?[]:e(w)}c.ViewManager.registerViewExtension({location:"panel",id:"heap-profiler",commandPrompt:x(f.showMemory),title:x(f.memory),order:60,loadView:async()=>(await E()).HeapProfilerPanel.HeapProfilerPanel.instance(),experiment:"js-heap-profiler-enable"}),c.ViewManager.registerViewExtension({location:"drawer-view",id:"live-heap-profile",commandPrompt:x(f.showLiveHeapProfile),title:x(f.liveHeapProfile),persistence:"closeable",order:100,loadView:async()=>(await E()).LiveHeapProfileView.LiveHeapProfileView.instance(),experiment:"live-heap-profile"}),c.ActionRegistration.registerActionExtension({actionId:"live-heap-profile.toggle-recording",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,loadActionDelegate:async()=>new((await E()).LiveHeapProfileView.ActionDelegate),category:"MEMORY",experiment:"live-heap-profile",options:[{value:!0,title:x(f.startRecordingHeapAllocations)},{value:!1,title:x(f.stopRecordingHeapAllocations)}]}),c.ActionRegistration.registerActionExtension({actionId:"live-heap-profile.start-with-reload",iconClass:"refresh",loadActionDelegate:async()=>new((await E()).LiveHeapProfileView.ActionDelegate),category:"MEMORY",experiment:"live-heap-profile",title:x(f.startRecordingHeapAllocationsAndReload)}),c.ActionRegistration.registerActionExtension({actionId:"profiler.heap-toggle-recording",category:"MEMORY",iconClass:"record-start",title:x(f.startStopRecording),toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>A((e=>[e.HeapProfilerPanel.HeapProfilerPanel])),loadActionDelegate:async()=>(await E()).HeapProfilerPanel.HeapProfilerPanel.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),c.ActionRegistration.registerActionExtension({actionId:"profiler.clear-all",category:"MEMORY",iconClass:"clear",contextTypes:()=>A((e=>[e.ProfilesPanel.ProfilesPanel])),loadActionDelegate:async()=>new((await E()).ProfilesPanel.ActionDelegate),title:x(f.clearAllProfiles)}),c.ActionRegistration.registerActionExtension({actionId:"profiler.load-from-file",category:"MEMORY",iconClass:"import",contextTypes:()=>A((e=>[e.ProfilesPanel.ProfilesPanel])),loadActionDelegate:async()=>new((await E()).ProfilesPanel.ActionDelegate),title:x(f.loadProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+O"},{platform:"mac",shortcut:"Meta+O"}]}),c.ActionRegistration.registerActionExtension({actionId:"profiler.save-to-file",category:"MEMORY",iconClass:"download",contextTypes:()=>A((e=>[e.ProfileHeader.ProfileHeader])),loadActionDelegate:async()=>new((await E()).ProfilesPanel.ActionDelegate),title:x(f.saveProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+S"},{platform:"mac",shortcut:"Meta+S"}]}),c.ActionRegistration.registerActionExtension({actionId:"profiler.delete-profile",category:"MEMORY",iconClass:"download",contextTypes:()=>A((e=>[e.ProfileHeader.ProfileHeader])),loadActionDelegate:async()=>new((await E()).ProfilesPanel.ActionDelegate),title:x(f.deleteProfile)}),c.ContextMenu.registerProvider({contextTypes:()=>[a.RemoteObject.RemoteObject],loadProvider:async()=>(await E()).HeapProfilerPanel.HeapProfilerPanel.instance(),experiment:void 0}),c.ContextMenu.registerItem({location:"profilerMenu/default",actionId:"profiler.save-to-file",order:10}),c.ContextMenu.registerItem({location:"profilerMenu/default",actionId:"profiler.delete-profile",order:11});const T={console:"Console",showConsole:"Show Console",toggleConsole:"Toggle Console",clearConsole:"Clear console",clearConsoleHistory:"Clear console history",createLiveExpression:"Create live expression",hideNetworkMessages:"Hide network messages",showNetworkMessages:"Show network messages",selectedContextOnly:"Selected context only",onlyShowMessagesFromTheCurrent:"Only show messages from the current context (`top`, `iframe`, `worker`, extension)",showMessagesFromAllContexts:"Show messages from all contexts",logXmlhttprequests:"Log XMLHttpRequests",timestamps:"Timestamps",showTimestamps:"Show timestamps",hideTimestamps:"Hide timestamps",autocompleteFromHistory:"Autocomplete from history",doNotAutocompleteFromHistory:"Do not autocomplete from history",autocompleteOnEnter:"Accept autocomplete suggestion on Enter",doNotAutocompleteOnEnter:"Do not accept autocomplete suggestion on Enter",groupSimilarMessagesInConsole:"Group similar messages in console",doNotGroupSimilarMessagesIn:"Do not group similar messages in console",showCorsErrorsInConsole:"Show `CORS` errors in console",doNotShowCorsErrorsIn:"Do not show `CORS` errors in console",eagerEvaluation:"Eager evaluation",eagerlyEvaluateConsolePromptText:"Eagerly evaluate console prompt text",doNotEagerlyEvaluateConsole:"Do not eagerly evaluate console prompt text",evaluateTriggersUserActivation:"Treat code evaluation as user action",treatEvaluationAsUserActivation:"Treat evaluation as user activation",doNotTreatEvaluationAsUser:"Do not treat evaluation as user activation",expandConsoleTraceMessagesByDefault:"Automatically expand `console.trace()` messages",collapseConsoleTraceMessagesByDefault:"Do not automatically expand `console.trace()` messages"},R=o.i18n.registerUIStrings("panels/console/console-meta.ts",T),D=o.i18n.getLazilyComputedLocalizedString.bind(void 0,R);let P;async function k(){return P||(P=await import("../../panels/console/console.js")),P}c.ViewManager.registerViewExtension({location:"panel",id:"console",title:D(T.console),commandPrompt:D(T.showConsole),order:20,loadView:async()=>(await k()).ConsolePanel.ConsolePanel.instance()}),c.ViewManager.registerViewExtension({location:"drawer-view",id:"console-view",title:D(T.console),commandPrompt:D(T.showConsole),persistence:"permanent",order:0,loadView:async()=>(await k()).ConsolePanel.WrapperView.instance()}),c.ActionRegistration.registerActionExtension({actionId:"console.toggle",category:"CONSOLE",title:D(T.toggleConsole),loadActionDelegate:async()=>new((await k()).ConsoleView.ActionDelegate),bindings:[{shortcut:"Ctrl+`",keybindSets:["devToolsDefault","vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"console.clear",category:"CONSOLE",title:D(T.clearConsole),iconClass:"clear",loadActionDelegate:async()=>new((await k()).ConsoleView.ActionDelegate),contextTypes:()=>void 0===P?[]:(e=>[e.ConsoleView.ConsoleView])(P),bindings:[{shortcut:"Ctrl+L"},{shortcut:"Meta+K",platform:"mac"}]}),c.ActionRegistration.registerActionExtension({actionId:"console.clear.history",category:"CONSOLE",title:D(T.clearConsoleHistory),loadActionDelegate:async()=>new((await k()).ConsoleView.ActionDelegate)}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:D(T.hideNetworkMessages),settingName:"hide-network-messages",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:D(T.hideNetworkMessages)},{value:!1,title:D(T.showNetworkMessages)}]}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:D(T.selectedContextOnly),settingName:"selected-context-filter-enabled",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:D(T.onlyShowMessagesFromTheCurrent)},{value:!1,title:D(T.showMessagesFromAllContexts)}]}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:D(T.logXmlhttprequests),settingName:"monitoring-xhr-enabled",settingType:"boolean",defaultValue:!1}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:D(T.timestamps),settingName:"console-timestamps-enabled",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:D(T.showTimestamps)},{value:!1,title:D(T.hideTimestamps)}]}),e.Settings.registerSettingExtension({category:"CONSOLE",title:D(T.autocompleteFromHistory),settingName:"console-history-autocomplete",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:D(T.autocompleteFromHistory)},{value:!1,title:D(T.doNotAutocompleteFromHistory)}]}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:D(T.autocompleteOnEnter),settingName:"console-autocomplete-on-enter",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:D(T.autocompleteOnEnter)},{value:!1,title:D(T.doNotAutocompleteOnEnter)}]}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:D(T.groupSimilarMessagesInConsole),settingName:"console-group-similar",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:D(T.groupSimilarMessagesInConsole)},{value:!1,title:D(T.doNotGroupSimilarMessagesIn)}]}),e.Settings.registerSettingExtension({category:"CONSOLE",title:D(T.showCorsErrorsInConsole),settingName:"console-shows-cors-errors",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:D(T.showCorsErrorsInConsole)},{value:!1,title:D(T.doNotShowCorsErrorsIn)}]}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:D(T.evaluateTriggersUserActivation),settingName:"console-user-activation-eval",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:D(T.treatEvaluationAsUserActivation)},{value:!1,title:D(T.doNotTreatEvaluationAsUser)}]}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:D(T.expandConsoleTraceMessagesByDefault),settingName:"console-trace-expand",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:D(T.expandConsoleTraceMessagesByDefault)},{value:!1,title:D(T.collapseConsoleTraceMessagesByDefault)}]}),e.Revealer.registerRevealer({contextTypes:()=>[e.Console.Console],destination:void 0,loadRevealer:async()=>new((await k()).ConsolePanel.ConsoleRevealer)});const N={coverage:"Coverage",showCoverage:"Show Coverage",instrumentCoverage:"Instrument coverage",stopInstrumentingCoverageAndShow:"Stop instrumenting coverage and show results",startInstrumentingCoverageAnd:"Start instrumenting coverage and reload page",clearCoverage:"Clear coverage",exportCoverage:"Export coverage"},I=o.i18n.registerUIStrings("panels/coverage/coverage-meta.ts",N),V=o.i18n.getLazilyComputedLocalizedString.bind(void 0,I);let L,M;async function O(){return L||(L=await import("../../panels/coverage/coverage.js")),L}function F(e){return void 0===L?[]:e(L)}c.ViewManager.registerViewExtension({location:"drawer-view",id:"coverage",title:V(N.coverage),commandPrompt:V(N.showCoverage),persistence:"closeable",order:100,loadView:async()=>(await O()).CoverageView.CoverageView.instance()}),c.ActionRegistration.registerActionExtension({actionId:"coverage.toggle-recording",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,loadActionDelegate:async()=>new((await O()).CoverageView.ActionDelegate),category:"PERFORMANCE",options:[{value:!0,title:V(N.instrumentCoverage)},{value:!1,title:V(N.stopInstrumentingCoverageAndShow)}]}),c.ActionRegistration.registerActionExtension({actionId:"coverage.start-with-reload",iconClass:"refresh",loadActionDelegate:async()=>new((await O()).CoverageView.ActionDelegate),category:"PERFORMANCE",title:V(N.startInstrumentingCoverageAnd)}),c.ActionRegistration.registerActionExtension({actionId:"coverage.clear",iconClass:"clear",category:"PERFORMANCE",title:V(N.clearCoverage),loadActionDelegate:async()=>new((await O()).CoverageView.ActionDelegate),contextTypes:()=>F((e=>[e.CoverageView.CoverageView]))}),c.ActionRegistration.registerActionExtension({actionId:"coverage.export",iconClass:"download",category:"PERFORMANCE",title:V(N.exportCoverage),loadActionDelegate:async()=>new((await O()).CoverageView.ActionDelegate),contextTypes:()=>F((e=>[e.CoverageView.CoverageView]))});const U={changes:"Changes",showChanges:"Show Changes",revertAllChangesToCurrentFile:"Revert all changes to current file",copyAllChangesFromCurrentFile:"Copy all changes from current file"},G=o.i18n.registerUIStrings("panels/changes/changes-meta.ts",U),B=o.i18n.getLazilyComputedLocalizedString.bind(void 0,G);async function H(){return M||(M=await import("../../panels/changes/changes.js")),M}function W(e){return void 0===M?[]:e(M)}c.ViewManager.registerViewExtension({location:"drawer-view",id:"changes.changes",title:B(U.changes),commandPrompt:B(U.showChanges),persistence:"closeable",loadView:async()=>new((await H()).ChangesView.ChangesView)}),c.ActionRegistration.registerActionExtension({actionId:"changes.revert",category:"CHANGES",title:B(U.revertAllChangesToCurrentFile),iconClass:"undo",loadActionDelegate:async()=>new((await H()).ChangesView.ActionDelegate),contextTypes:()=>W((e=>[e.ChangesView.ChangesView]))}),c.ActionRegistration.registerActionExtension({actionId:"changes.copy",category:"CHANGES",title:B(U.copyAllChangesFromCurrentFile),iconClass:"copy",loadActionDelegate:async()=>new((await H()).ChangesView.ActionDelegate),contextTypes:()=>W((e=>[e.ChangesView.ChangesView]))});const z={memoryInspector:"Memory inspector",showMemoryInspector:"Show Memory inspector"},j=o.i18n.registerUIStrings("panels/linear_memory_inspector/linear_memory_inspector-meta.ts",z),q=o.i18n.getLazilyComputedLocalizedString.bind(void 0,j);let _;async function J(){return _||(_=await import("../../panels/linear_memory_inspector/linear_memory_inspector.js")),_}c.ViewManager.registerViewExtension({location:"drawer-view",id:"linear-memory-inspector",title:q(z.memoryInspector),commandPrompt:q(z.showMemoryInspector),order:100,persistence:"closeable",loadView:async()=>(await J()).LinearMemoryInspectorPane.LinearMemoryInspectorPane.instance()}),c.ContextMenu.registerProvider({loadProvider:async()=>(await J()).LinearMemoryInspectorController.LinearMemoryInspectorController.instance(),experiment:void 0,contextTypes:()=>[r.ObjectPropertiesSection.ObjectPropertyTreeElement]}),e.Revealer.registerRevealer({contextTypes:()=>[a.RemoteObject.LinearMemoryInspectable],destination:e.Revealer.RevealerDestination.MEMORY_INSPECTOR_PANEL,loadRevealer:async()=>(await J()).LinearMemoryInspectorController.LinearMemoryInspectorController.instance()});const Y={devices:"Devices",showDevices:"Show Devices"},Q=o.i18n.registerUIStrings("panels/settings/emulation/emulation-meta.ts",Y),K=o.i18n.getLazilyComputedLocalizedString.bind(void 0,Q);let Z;c.ViewManager.registerViewExtension({location:"settings-view",commandPrompt:K(Y.showDevices),title:K(Y.devices),order:30,loadView:async()=>new((await async function(){return Z||(Z=await import("../../panels/settings/emulation/emulation.js")),Z}()).DevicesSettingsTab.DevicesSettingsTab),id:"devices",settings:["standard-emulated-device-list","custom-emulated-device-list"],iconName:"devices"});const X={shortcuts:"Shortcuts",preferences:"Preferences",experiments:"Experiments",ignoreList:"Ignore List",showShortcuts:"Show Shortcuts",showPreferences:"Show Preferences",showExperiments:"Show Experiments",showIgnoreList:"Show Ignore List",settings:"Settings",documentation:"Documentation"},$=o.i18n.registerUIStrings("panels/settings/settings-meta.ts",X),ee=o.i18n.getLazilyComputedLocalizedString.bind(void 0,$);let te;async function oe(){return te||(te=await import("../../panels/settings/settings.js")),te}c.ViewManager.registerViewExtension({location:"settings-view",id:"preferences",title:ee(X.preferences),commandPrompt:ee(X.showPreferences),order:0,loadView:async()=>new((await oe()).SettingsScreen.GenericSettingsTab),iconName:"gear"}),c.ViewManager.registerViewExtension({location:"settings-view",id:"experiments",title:ee(X.experiments),commandPrompt:ee(X.showExperiments),order:3,experiment:"*",loadView:async()=>new((await oe()).SettingsScreen.ExperimentsSettingsTab),iconName:"experiment"}),c.ViewManager.registerViewExtension({location:"settings-view",id:"blackbox",title:ee(X.ignoreList),commandPrompt:ee(X.showIgnoreList),order:4,loadView:async()=>new((await oe()).FrameworkIgnoreListSettingsTab.FrameworkIgnoreListSettingsTab),iconName:"clear-list"}),c.ViewManager.registerViewExtension({location:"settings-view",id:"keybinds",title:ee(X.shortcuts),commandPrompt:ee(X.showShortcuts),order:100,loadView:async()=>new((await oe()).KeybindsSettingsTab.KeybindsSettingsTab),iconName:"keyboard"}),c.ActionRegistration.registerActionExtension({category:"SETTINGS",actionId:"settings.show",title:ee(X.settings),loadActionDelegate:async()=>new((await oe()).SettingsScreen.ActionDelegate),iconClass:"gear",bindings:[{shortcut:"F1",keybindSets:["devToolsDefault"]},{shortcut:"Shift+?"},{platform:"windows,linux",shortcut:"Ctrl+,",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+,",keybindSets:["vsCode"]}]}),c.ActionRegistration.registerActionExtension({category:"SETTINGS",actionId:"settings.documentation",title:ee(X.documentation),loadActionDelegate:async()=>new((await oe()).SettingsScreen.ActionDelegate)}),c.ActionRegistration.registerActionExtension({category:"SETTINGS",actionId:"settings.shortcuts",title:ee(X.showShortcuts),loadActionDelegate:async()=>new((await oe()).SettingsScreen.ActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+K Ctrl+S",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+K Meta+S",keybindSets:["vsCode"]}]}),c.ViewManager.registerLocationResolver({name:"settings-view",category:"SETTINGS",loadResolver:async()=>(await oe()).SettingsScreen.SettingsScreen.instance()}),e.Revealer.registerRevealer({contextTypes:()=>[e.Settings.Setting,i.Runtime.Experiment],destination:void 0,loadRevealer:async()=>new((await oe()).SettingsScreen.Revealer)}),c.ContextMenu.registerItem({location:"mainMenu/footer",actionId:"settings.shortcuts",order:void 0}),c.ContextMenu.registerItem({location:"mainMenuHelp/default",actionId:"settings.documentation",order:void 0});const ie={protocolMonitor:"Protocol monitor",showProtocolMonitor:"Show Protocol monitor"},ae=o.i18n.registerUIStrings("panels/protocol_monitor/protocol_monitor-meta.ts",ie),ne=o.i18n.getLazilyComputedLocalizedString.bind(void 0,ae);let se;c.ViewManager.registerViewExtension({location:"drawer-view",id:"protocol-monitor",title:ne(ie.protocolMonitor),commandPrompt:ne(ie.showProtocolMonitor),order:100,persistence:"closeable",loadView:async()=>new((await async function(){return se||(se=await import("../../panels/protocol_monitor/protocol_monitor.js")),se}()).ProtocolMonitor.ProtocolMonitorImpl),experiment:"protocol-monitor"});const re={workspace:"Workspace",showWorkspace:"Show Workspace settings",enableLocalOverrides:"Enable Local Overrides",interception:"interception",override:"override",network:"network",rewrite:"rewrite",request:"request",enableOverrideNetworkRequests:"Enable override network requests",disableOverrideNetworkRequests:"Disable override network requests"},le=o.i18n.registerUIStrings("models/persistence/persistence-meta.ts",re),ce=o.i18n.getLazilyComputedLocalizedString.bind(void 0,le);let ge;async function de(){return ge||(ge=await import("../../models/persistence/persistence.js")),ge}c.ViewManager.registerViewExtension({location:"settings-view",id:"workspace",title:ce(re.workspace),commandPrompt:ce(re.showWorkspace),order:1,loadView:async()=>new((await de()).WorkspaceSettingsTab.WorkspaceSettingsTab),iconName:"folder"}),e.Settings.registerSettingExtension({category:"PERSISTENCE",title:ce(re.enableLocalOverrides),settingName:"persistence-network-overrides-enabled",settingType:"boolean",defaultValue:!1,tags:[ce(re.interception),ce(re.override),ce(re.network),ce(re.rewrite),ce(re.request)],options:[{value:!0,title:ce(re.enableOverrideNetworkRequests)},{value:!1,title:ce(re.disableOverrideNetworkRequests)}]}),c.ContextMenu.registerProvider({contextTypes:()=>[s.UISourceCode.UISourceCode,a.Resource.Resource,a.NetworkRequest.NetworkRequest],loadProvider:async()=>new((await de()).PersistenceActions.ContextMenuProvider),experiment:void 0});const ue={preserveLog:"Preserve log",preserve:"preserve",clear:"clear",reset:"reset",preserveLogOnPageReload:"Preserve log on page reload / navigation",doNotPreserveLogOnPageReload:"Do not preserve log on page reload / navigation",recordNetworkLog:"Record network log"},pe=o.i18n.registerUIStrings("models/logs/logs-meta.ts",ue),me=o.i18n.getLazilyComputedLocalizedString.bind(void 0,pe);e.Settings.registerSettingExtension({category:"NETWORK",title:me(ue.preserveLog),settingName:"network-log.preserve-log",settingType:"boolean",defaultValue:!1,tags:[me(ue.preserve),me(ue.clear),me(ue.reset)],options:[{value:!0,title:me(ue.preserveLogOnPageReload)},{value:!1,title:me(ue.doNotPreserveLogOnPageReload)}]}),e.Settings.registerSettingExtension({category:"NETWORK",title:me(ue.recordNetworkLog),settingName:"network-log.record-log",settingType:"boolean",defaultValue:!0,storageType:"Session"});const Se={focusDebuggee:"Focus page",toggleDrawer:"Toggle drawer",nextPanel:"Next panel",previousPanel:"Previous panel",reloadDevtools:"Reload DevTools",restoreLastDockPosition:"Restore last dock position",zoomIn:"Zoom in",zoomOut:"Zoom out",resetZoomLevel:"Reset zoom level",searchInPanel:"Search in panel",cancelSearch:"Cancel search",findNextResult:"Find next result",findPreviousResult:"Find previous result",theme:"Theme:",switchToBrowserPreferredColor:"Switch to browser's preferred color theme",browserPreference:"Browser preference",switchToLightTheme:"Switch to light theme",lightCapital:"Light",switchToDarkTheme:"Switch to dark theme",darkCapital:"Dark",darkLower:"dark",lightLower:"light",panelLayout:"Panel layout:",useHorizontalPanelLayout:"Use horizontal panel layout",horizontal:"horizontal",useVerticalPanelLayout:"Use vertical panel layout",vertical:"vertical",useAutomaticPanelLayout:"Use automatic panel layout",auto:"auto",enableCtrlShortcutToSwitchPanels:"Enable Ctrl + 1-9 shortcut to switch panels",enableShortcutToSwitchPanels:"Enable ⌘ + 1-9 shortcut to switch panels",right:"Right",dockToRight:"Dock to right",bottom:"Bottom",dockToBottom:"Dock to bottom",left:"Left",dockToLeft:"Dock to left",undocked:"Undocked",undockIntoSeparateWindow:"Undock into separate window",devtoolsDefault:"DevTools (Default)",language:"Language:",browserLanguage:"Browser UI language",enableSync:"Enable settings sync",searchAsYouTypeSetting:"Search as you type",searchAsYouTypeCommand:"Enable search as you type",searchOnEnterCommand:"Disable search as you type (press Enter to search)"},ye=o.i18n.registerUIStrings("entrypoints/main/main-meta.ts",Se),we=o.i18n.getLazilyComputedLocalizedString.bind(void 0,ye);let he,ve;async function be(){return he||(he=await import("../main/main.js")),he}function fe(){return!t.InspectorFrontendHost.InspectorFrontendHostInstance.isHostedMode()}function Ce(e){return()=>o.i18n.getLocalizedLanguageRegion(e,o.DevToolsLocale.DevToolsLocale.instance())}c.ActionRegistration.registerActionExtension({category:"DRAWER",actionId:"inspector-main.focus-debuggee",loadActionDelegate:async()=>new((await async function(){return ve||(ve=await import("../inspector_main/inspector_main.js")),ve}()).InspectorMain.FocusDebuggeeActionDelegate),order:100,title:we(Se.focusDebuggee)}),c.ActionRegistration.registerActionExtension({category:"DRAWER",actionId:"main.toggle-drawer",loadActionDelegate:async()=>new c.InspectorView.ActionDelegate,order:101,title:we(Se.toggleDrawer),bindings:[{shortcut:"Esc"}]}),c.ActionRegistration.registerActionExtension({actionId:"main.next-tab",category:"GLOBAL",title:we(Se.nextPanel),loadActionDelegate:async()=>new c.InspectorView.ActionDelegate,bindings:[{platform:"windows,linux",shortcut:"Ctrl+]"},{platform:"mac",shortcut:"Meta+]"}]}),c.ActionRegistration.registerActionExtension({actionId:"main.previous-tab",category:"GLOBAL",title:we(Se.previousPanel),loadActionDelegate:async()=>new c.InspectorView.ActionDelegate,bindings:[{platform:"windows,linux",shortcut:"Ctrl+["},{platform:"mac",shortcut:"Meta+["}]}),c.ActionRegistration.registerActionExtension({actionId:"main.debug-reload",category:"GLOBAL",title:we(Se.reloadDevtools),loadActionDelegate:async()=>new((await be()).MainImpl.ReloadActionDelegate),bindings:[{shortcut:"Alt+R"}]}),c.ActionRegistration.registerActionExtension({category:"GLOBAL",title:we(Se.restoreLastDockPosition),actionId:"main.toggle-dock",loadActionDelegate:async()=>new c.DockController.ToggleDockActionDelegate,bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+D"},{platform:"mac",shortcut:"Meta+Shift+D"}]}),c.ActionRegistration.registerActionExtension({actionId:"main.zoom-in",category:"GLOBAL",title:we(Se.zoomIn),loadActionDelegate:async()=>new((await be()).MainImpl.ZoomActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Plus",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+Shift+Plus"},{platform:"windows,linux",shortcut:"Ctrl+NumpadPlus"},{platform:"windows,linux",shortcut:"Ctrl+Shift+NumpadPlus"},{platform:"mac",shortcut:"Meta+Plus",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+Shift+Plus"},{platform:"mac",shortcut:"Meta+NumpadPlus"},{platform:"mac",shortcut:"Meta+Shift+NumpadPlus"}],condition:fe}),c.ActionRegistration.registerActionExtension({actionId:"main.zoom-out",category:"GLOBAL",title:we(Se.zoomOut),loadActionDelegate:async()=>new((await be()).MainImpl.ZoomActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Minus",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+Shift+Minus"},{platform:"windows,linux",shortcut:"Ctrl+NumpadMinus"},{platform:"windows,linux",shortcut:"Ctrl+Shift+NumpadMinus"},{platform:"mac",shortcut:"Meta+Minus",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+Shift+Minus"},{platform:"mac",shortcut:"Meta+NumpadMinus"},{platform:"mac",shortcut:"Meta+Shift+NumpadMinus"}],condition:fe}),c.ActionRegistration.registerActionExtension({actionId:"main.zoom-reset",category:"GLOBAL",title:we(Se.resetZoomLevel),loadActionDelegate:async()=>new((await be()).MainImpl.ZoomActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+0"},{platform:"windows,linux",shortcut:"Ctrl+Numpad0"},{platform:"mac",shortcut:"Meta+Numpad0"},{platform:"mac",shortcut:"Meta+0"}],condition:fe}),c.ActionRegistration.registerActionExtension({actionId:"main.search-in-panel.find",category:"GLOBAL",title:we(Se.searchInPanel),loadActionDelegate:async()=>new((await be()).MainImpl.SearchActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"F3"}]}),c.ActionRegistration.registerActionExtension({actionId:"main.search-in-panel.cancel",category:"GLOBAL",title:we(Se.cancelSearch),loadActionDelegate:async()=>new((await be()).MainImpl.SearchActionDelegate),order:10,bindings:[{shortcut:"Esc"}]}),c.ActionRegistration.registerActionExtension({actionId:"main.search-in-panel.find-next",category:"GLOBAL",title:we(Se.findNextResult),loadActionDelegate:async()=>new((await be()).MainImpl.SearchActionDelegate),bindings:[{platform:"mac",shortcut:"Meta+G",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+G"},{platform:"windows,linux",shortcut:"F3",keybindSets:["devToolsDefault","vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"main.search-in-panel.find-previous",category:"GLOBAL",title:we(Se.findPreviousResult),loadActionDelegate:async()=>new((await be()).MainImpl.SearchActionDelegate),bindings:[{platform:"mac",shortcut:"Meta+Shift+G",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+Shift+G"},{platform:"windows,linux",shortcut:"Shift+F3",keybindSets:["devToolsDefault","vsCode"]}]}),e.Settings.registerSettingExtension({category:"APPEARANCE",storageType:"Synced",title:we(Se.theme),settingName:"ui-theme",settingType:"enum",defaultValue:"systemPreferred",reloadRequired:!1,options:[{title:we(Se.switchToBrowserPreferredColor),text:we(Se.browserPreference),value:"systemPreferred"},{title:we(Se.switchToLightTheme),text:we(Se.lightCapital),value:"default"},{title:we(Se.switchToDarkTheme),text:we(Se.darkCapital),value:"dark"}],tags:[we(Se.darkLower),we(Se.lightLower)]}),e.Settings.registerSettingExtension({category:"APPEARANCE",storageType:"Synced",title:we(Se.panelLayout),settingName:"sidebar-position",settingType:"enum",defaultValue:"auto",options:[{title:we(Se.useHorizontalPanelLayout),text:we(Se.horizontal),value:"bottom"},{title:we(Se.useVerticalPanelLayout),text:we(Se.vertical),value:"right"},{title:we(Se.useAutomaticPanelLayout),text:we(Se.auto),value:"auto"}]}),e.Settings.registerSettingExtension({category:"APPEARANCE",storageType:"Synced",settingName:"language",settingType:"enum",title:we(Se.language),defaultValue:"en-US",options:[{value:"browserLanguage",title:we(Se.browserLanguage),text:we(Se.browserLanguage)},...o.i18n.getAllSupportedDevToolsLocales().sort().map((e=>{return{value:t=e,title:Ce(t),text:Ce(t)};var t}))],reloadRequired:!0}),e.Settings.registerSettingExtension({category:"APPEARANCE",storageType:"Synced",title:"mac"===t.Platform.platform()?we(Se.enableShortcutToSwitchPanels):we(Se.enableCtrlShortcutToSwitchPanels),settingName:"shortcut-panel-switch",settingType:"boolean",defaultValue:!1}),e.Settings.registerSettingExtension({category:"GLOBAL",settingName:"currentDockState",settingType:"enum",defaultValue:"right",options:[{value:"right",text:we(Se.right),title:we(Se.dockToRight)},{value:"bottom",text:we(Se.bottom),title:we(Se.dockToBottom)},{value:"left",text:we(Se.left),title:we(Se.dockToLeft)},{value:"undocked",text:we(Se.undocked),title:we(Se.undockIntoSeparateWindow)}]}),e.Settings.registerSettingExtension({storageType:"Synced",settingName:"active-keybind-set",settingType:"enum",defaultValue:"devToolsDefault",options:[{value:"devToolsDefault",title:we(Se.devtoolsDefault),text:we(Se.devtoolsDefault)},{value:"vsCode",title:o.i18n.lockedLazyString("Visual Studio Code"),text:o.i18n.lockedLazyString("Visual Studio Code")}]}),e.Settings.registerSettingExtension({category:"SYNC",settingName:"sync-preferences",settingType:"boolean",title:we(Se.enableSync),defaultValue:!1,reloadRequired:!0}),e.Settings.registerSettingExtension({storageType:"Synced",settingName:"user-shortcuts",settingType:"array",defaultValue:[]}),e.Settings.registerSettingExtension({category:"GLOBAL",storageType:"Local",title:we(Se.searchAsYouTypeSetting),settingName:"search-as-you-type",settingType:"boolean",order:3,defaultValue:!0,options:[{value:!0,title:we(Se.searchAsYouTypeCommand)},{value:!1,title:we(Se.searchOnEnterCommand)}]}),c.ViewManager.registerLocationResolver({name:"drawer-view",category:"DRAWER",loadResolver:async()=>c.InspectorView.InspectorView.instance()}),c.ViewManager.registerLocationResolver({name:"drawer-sidebar",category:"DRAWER_SIDEBAR",loadResolver:async()=>c.InspectorView.InspectorView.instance()}),c.ViewManager.registerLocationResolver({name:"panel",category:"PANEL",loadResolver:async()=>c.InspectorView.InspectorView.instance()}),c.ContextMenu.registerProvider({contextTypes:()=>[s.UISourceCode.UISourceCode,a.Resource.Resource,a.NetworkRequest.NetworkRequest],loadProvider:async()=>new g.Linkifier.ContentProviderContextMenuProvider,experiment:void 0}),c.ContextMenu.registerProvider({contextTypes:()=>[Node],loadProvider:async()=>new c.XLink.ContextMenuProvider,experiment:void 0}),c.ContextMenu.registerProvider({contextTypes:()=>[Node],loadProvider:async()=>new g.Linkifier.LinkContextMenuProvider,experiment:void 0}),c.Toolbar.registerToolbarItem({separator:!0,location:"main-toolbar-left",order:100}),c.Toolbar.registerToolbarItem({separator:!0,order:97,location:"main-toolbar-right"}),c.Toolbar.registerToolbarItem({loadItem:async()=>(await be()).MainImpl.SettingsButtonProvider.instance(),order:99,location:"main-toolbar-right"}),c.Toolbar.registerToolbarItem({loadItem:async()=>(await be()).MainImpl.MainMenuItem.instance(),order:100,location:"main-toolbar-right"}),c.Toolbar.registerToolbarItem({loadItem:async()=>c.DockController.CloseButtonProvider.instance(),order:101,location:"main-toolbar-right"}),e.AppProvider.registerAppProvider({loadAppProvider:async()=>(await be()).SimpleApp.SimpleAppProvider.instance(),order:10});const xe={flamechartMouseWheelAction:"Flamechart mouse wheel action:",scroll:"Scroll",zoom:"Zoom",liveMemoryAllocationAnnotations:"Live memory allocation annotations",showLiveMemoryAllocation:"Show live memory allocation annotations",hideLiveMemoryAllocation:"Hide live memory allocation annotations",collectGarbage:"Collect garbage"},Ee=o.i18n.registerUIStrings("ui/legacy/components/perf_ui/perf_ui-meta.ts",xe),Ae=o.i18n.getLazilyComputedLocalizedString.bind(void 0,Ee);let Te;c.ActionRegistration.registerActionExtension({actionId:"components.collect-garbage",category:"PERFORMANCE",title:Ae(xe.collectGarbage),iconClass:"mop",loadActionDelegate:async()=>new((await async function(){return Te||(Te=await import("../../ui/legacy/components/perf_ui/perf_ui.js")),Te}()).GCActionDelegate.GCActionDelegate)}),e.Settings.registerSettingExtension({category:"PERFORMANCE",storageType:"Synced",title:Ae(xe.flamechartMouseWheelAction),settingName:"flamechart-mouse-wheel-action",settingType:"enum",defaultValue:"zoom",options:[{title:Ae(xe.scroll),text:Ae(xe.scroll),value:"scroll"},{title:Ae(xe.zoom),text:Ae(xe.zoom),value:"zoom"}]}),e.Settings.registerSettingExtension({category:"MEMORY",experiment:"live-heap-profile",title:Ae(xe.liveMemoryAllocationAnnotations),settingName:"memory-live-heap-profile",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:Ae(xe.showLiveMemoryAllocation)},{value:!1,title:Ae(xe.hideLiveMemoryAllocation)}]});const Re={openFile:"Open file",runCommand:"Run command"},De=o.i18n.registerUIStrings("ui/legacy/components/quick_open/quick_open-meta.ts",Re),Pe=o.i18n.getLazilyComputedLocalizedString.bind(void 0,De);let ke;async function Ne(){return ke||(ke=await import("../../ui/legacy/components/quick_open/quick_open.js")),ke}c.ActionRegistration.registerActionExtension({actionId:"quick-open.show-command-menu",category:"GLOBAL",title:Pe(Re.runCommand),loadActionDelegate:async()=>new((await Ne()).CommandMenu.ShowActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+P",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+Shift+P",keybindSets:["devToolsDefault","vsCode"]},{shortcut:"F1",keybindSets:["vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"quick-open.show",category:"GLOBAL",title:Pe(Re.openFile),loadActionDelegate:async()=>new((await Ne()).QuickOpen.ShowActionDelegate),order:100,bindings:[{platform:"mac",shortcut:"Meta+P",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+O",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+P",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+O",keybindSets:["devToolsDefault","vsCode"]}]}),c.ContextMenu.registerItem({location:"mainMenu/default",actionId:"quick-open.show-command-menu",order:void 0}),c.ContextMenu.registerItem({location:"mainMenu/default",actionId:"quick-open.show",order:void 0});const Ie={preserveLogUponNavigation:"Preserve log upon navigation",doNotPreserveLogUponNavigation:"Do not preserve log upon navigation",pauseOnExceptions:"Pause on exceptions",doNotPauseOnExceptions:"Do not pause on exceptions",disableJavascript:"Disable JavaScript",enableJavascript:"Enable JavaScript",disableAsyncStackTraces:"Disable async stack traces",doNotCaptureAsyncStackTraces:"Do not capture async stack traces",captureAsyncStackTraces:"Capture async stack traces",showRulersOnHover:"Show rulers on hover",doNotShowRulersOnHover:"Do not show rulers on hover",showAreaNames:"Show area names",showGridNamedAreas:"Show grid named areas",doNotShowGridNamedAreas:"Do not show grid named areas",showTrackSizes:"Show track sizes",showGridTrackSizes:"Show grid track sizes",doNotShowGridTrackSizes:"Do not show grid track sizes",extendGridLines:"Extend grid lines",doNotExtendGridLines:"Do not extend grid lines",showLineLabels:"Show line labels",hideLineLabels:"Hide line labels",showLineNumbers:"Show line numbers",showLineNames:"Show line names",showPaintFlashingRectangles:"Show paint flashing rectangles",hidePaintFlashingRectangles:"Hide paint flashing rectangles",showLayoutShiftRegions:"Show layout shift regions",hideLayoutShiftRegions:"Hide layout shift regions",highlightAdFrames:"Highlight ad frames",doNotHighlightAdFrames:"Do not highlight ad frames",showLayerBorders:"Show layer borders",hideLayerBorders:"Hide layer borders",showCoreWebVitalsOverlay:"Show Core Web Vitals overlay",hideCoreWebVitalsOverlay:"Hide Core Web Vitals overlay",showFramesPerSecondFpsMeter:"Show frames per second (FPS) meter",hideFramesPerSecondFpsMeter:"Hide frames per second (FPS) meter",showScrollPerformanceBottlenecks:"Show scroll performance bottlenecks",hideScrollPerformanceBottlenecks:"Hide scroll performance bottlenecks",emulateAFocusedPage:"Emulate a focused page",doNotEmulateAFocusedPage:"Do not emulate a focused page",doNotEmulateCssMediaType:"Do not emulate CSS media type",noEmulation:"No emulation",emulateCssPrintMediaType:"Emulate CSS print media type",print:"print",emulateCssScreenMediaType:"Emulate CSS screen media type",screen:"screen",query:"query",emulateCssMediaType:"Emulate CSS media type",doNotEmulateCss:"Do not emulate CSS {PH1}",emulateCss:"Emulate CSS {PH1}",emulateCssMediaFeature:"Emulate CSS media feature {PH1}",doNotEmulateAnyVisionDeficiency:"Do not emulate any vision deficiency",emulateBlurredVision:"Emulate blurred vision",emulateReducedContrast:"Emulate reduced contrast",blurredVision:"Blurred vision",reducedContrast:"Reduced contrast",emulateProtanopia:"Emulate protanopia (no red)",protanopia:"Protanopia (no red)",emulateDeuteranopia:"Emulate deuteranopia (no green)",deuteranopia:"Deuteranopia (no green)",emulateTritanopia:"Emulate tritanopia (no blue)",tritanopia:"Tritanopia (no blue)",emulateAchromatopsia:"Emulate achromatopsia (no color)",achromatopsia:"Achromatopsia (no color)",emulateVisionDeficiencies:"Emulate vision deficiencies",disableLocalFonts:"Disable local fonts",enableLocalFonts:"Enable local fonts",disableAvifFormat:"Disable `AVIF` format",enableAvifFormat:"Enable `AVIF` format",disableWebpFormat:"Disable `WebP` format",enableWebpFormat:"Enable `WebP` format",customFormatters:"Custom formatters",networkRequestBlocking:"Network request blocking",enableNetworkRequestBlocking:"Enable network request blocking",disableNetworkRequestBlocking:"Disable network request blocking",enableCache:"Enable cache",disableCache:"Disable cache (while DevTools is open)",emulateAutoDarkMode:"Emulate auto dark mode",enableRemoteFileLoading:"Allow `DevTools` to load resources, such as source maps, from remote file paths. Disabled by default for security reasons."},Ve=o.i18n.registerUIStrings("core/sdk/sdk-meta.ts",Ie),Le=o.i18n.getLazilyComputedLocalizedString.bind(void 0,Ve);e.Settings.registerSettingExtension({storageType:"Synced",settingName:"skip-stack-frames-pattern",settingType:"regex",defaultValue:""}),e.Settings.registerSettingExtension({storageType:"Synced",settingName:"skip-content-scripts",settingType:"boolean",defaultValue:!0}),e.Settings.registerSettingExtension({storageType:"Synced",settingName:"automatically-ignore-list-known-third-party-scripts",settingType:"boolean",defaultValue:!0}),e.Settings.registerSettingExtension({storageType:"Synced",settingName:"enable-ignore-listing",settingType:"boolean",defaultValue:!0}),e.Settings.registerSettingExtension({category:"CONSOLE",storageType:"Synced",title:Le(Ie.preserveLogUponNavigation),settingName:"preserve-console-log",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:Le(Ie.preserveLogUponNavigation)},{value:!1,title:Le(Ie.doNotPreserveLogUponNavigation)}]}),e.Settings.registerSettingExtension({category:"DEBUGGER",settingName:"pause-on-exception-enabled",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:Le(Ie.pauseOnExceptions)},{value:!1,title:Le(Ie.doNotPauseOnExceptions)}]}),e.Settings.registerSettingExtension({settingName:"pause-on-caught-exception",settingType:"boolean",defaultValue:!1}),e.Settings.registerSettingExtension({settingName:"pause-on-uncaught-exception",settingType:"boolean",defaultValue:!1}),e.Settings.registerSettingExtension({category:"DEBUGGER",title:Le(Ie.disableJavascript),settingName:"java-script-disabled",settingType:"boolean",storageType:"Session",order:1,defaultValue:!1,options:[{value:!0,title:Le(Ie.disableJavascript)},{value:!1,title:Le(Ie.enableJavascript)}]}),e.Settings.registerSettingExtension({category:"DEBUGGER",title:Le(Ie.disableAsyncStackTraces),settingName:"disable-async-stack-traces",settingType:"boolean",defaultValue:!1,order:2,options:[{value:!0,title:Le(Ie.doNotCaptureAsyncStackTraces)},{value:!1,title:Le(Ie.captureAsyncStackTraces)}]}),e.Settings.registerSettingExtension({category:"DEBUGGER",settingName:"breakpoints-active",settingType:"boolean",storageType:"Session",defaultValue:!0}),e.Settings.registerSettingExtension({category:"ELEMENTS",storageType:"Synced",title:Le(Ie.showRulersOnHover),settingName:"show-metrics-rulers",settingType:"boolean",options:[{value:!0,title:Le(Ie.showRulersOnHover)},{value:!1,title:Le(Ie.doNotShowRulersOnHover)}],defaultValue:!1}),e.Settings.registerSettingExtension({category:"GRID",storageType:"Synced",title:Le(Ie.showAreaNames),settingName:"show-grid-areas",settingType:"boolean",options:[{value:!0,title:Le(Ie.showGridNamedAreas)},{value:!1,title:Le(Ie.doNotShowGridNamedAreas)}],defaultValue:!1}),e.Settings.registerSettingExtension({category:"GRID",storageType:"Synced",title:Le(Ie.showTrackSizes),settingName:"show-grid-track-sizes",settingType:"boolean",options:[{value:!0,title:Le(Ie.showGridTrackSizes)},{value:!1,title:Le(Ie.doNotShowGridTrackSizes)}],defaultValue:!1}),e.Settings.registerSettingExtension({category:"GRID",storageType:"Synced",title:Le(Ie.extendGridLines),settingName:"extend-grid-lines",settingType:"boolean",options:[{value:!0,title:Le(Ie.extendGridLines)},{value:!1,title:Le(Ie.doNotExtendGridLines)}],defaultValue:!1}),e.Settings.registerSettingExtension({category:"GRID",storageType:"Synced",title:Le(Ie.showLineLabels),settingName:"show-grid-line-labels",settingType:"enum",options:[{title:Le(Ie.hideLineLabels),text:Le(Ie.hideLineLabels),value:"none"},{title:Le(Ie.showLineNumbers),text:Le(Ie.showLineNumbers),value:"lineNumbers"},{title:Le(Ie.showLineNames),text:Le(Ie.showLineNames),value:"lineNames"}],defaultValue:"lineNumbers"}),e.Settings.registerSettingExtension({category:"RENDERING",settingName:"show-paint-rects",settingType:"boolean",storageType:"Session",options:[{value:!0,title:Le(Ie.showPaintFlashingRectangles)},{value:!1,title:Le(Ie.hidePaintFlashingRectangles)}],defaultValue:!1}),e.Settings.registerSettingExtension({category:"RENDERING",settingName:"show-layout-shift-regions",settingType:"boolean",storageType:"Session",options:[{value:!0,title:Le(Ie.showLayoutShiftRegions)},{value:!1,title:Le(Ie.hideLayoutShiftRegions)}],defaultValue:!1}),e.Settings.registerSettingExtension({category:"RENDERING",settingName:"show-ad-highlights",settingType:"boolean",storageType:"Session",options:[{value:!0,title:Le(Ie.highlightAdFrames)},{value:!1,title:Le(Ie.doNotHighlightAdFrames)}],defaultValue:!1}),e.Settings.registerSettingExtension({category:"RENDERING",settingName:"show-debug-borders",settingType:"boolean",storageType:"Session",options:[{value:!0,title:Le(Ie.showLayerBorders)},{value:!1,title:Le(Ie.hideLayerBorders)}],defaultValue:!1}),e.Settings.registerSettingExtension({category:"RENDERING",settingName:"show-web-vitals",settingType:"boolean",storageType:"Session",options:[{value:!0,title:Le(Ie.showCoreWebVitalsOverlay)},{value:!1,title:Le(Ie.hideCoreWebVitalsOverlay)}],defaultValue:!1}),e.Settings.registerSettingExtension({category:"RENDERING",settingName:"show-fps-counter",settingType:"boolean",storageType:"Session",options:[{value:!0,title:Le(Ie.showFramesPerSecondFpsMeter)},{value:!1,title:Le(Ie.hideFramesPerSecondFpsMeter)}],defaultValue:!1}),e.Settings.registerSettingExtension({category:"RENDERING",settingName:"show-scroll-bottleneck-rects",settingType:"boolean",storageType:"Session",options:[{value:!0,title:Le(Ie.showScrollPerformanceBottlenecks)},{value:!1,title:Le(Ie.hideScrollPerformanceBottlenecks)}],defaultValue:!1}),e.Settings.registerSettingExtension({category:"RENDERING",title:Le(Ie.emulateAFocusedPage),settingName:"emulate-page-focus",settingType:"boolean",storageType:"Local",defaultValue:!1,options:[{value:!0,title:Le(Ie.emulateAFocusedPage)},{value:!1,title:Le(Ie.doNotEmulateAFocusedPage)}]}),e.Settings.registerSettingExtension({category:"RENDERING",settingName:"emulated-css-media",settingType:"enum",storageType:"Session",defaultValue:"",options:[{title:Le(Ie.doNotEmulateCssMediaType),text:Le(Ie.noEmulation),value:""},{title:Le(Ie.emulateCssPrintMediaType),text:Le(Ie.print),value:"print"},{title:Le(Ie.emulateCssScreenMediaType),text:Le(Ie.screen),value:"screen"}],tags:[Le(Ie.query)],title:Le(Ie.emulateCssMediaType)}),e.Settings.registerSettingExtension({category:"RENDERING",settingName:"emulated-css-media-feature-prefers-color-scheme",settingType:"enum",storageType:"Session",defaultValue:"",options:[{title:Le(Ie.doNotEmulateCss,{PH1:"prefers-color-scheme"}),text:Le(Ie.noEmulation),value:""},{title:Le(Ie.emulateCss,{PH1:"prefers-color-scheme: light"}),text:o.i18n.lockedLazyString("prefers-color-scheme: light"),value:"light"},{title:Le(Ie.emulateCss,{PH1:"prefers-color-scheme: dark"}),text:o.i18n.lockedLazyString("prefers-color-scheme: dark"),value:"dark"}],tags:[Le(Ie.query)],title:Le(Ie.emulateCssMediaFeature,{PH1:"prefers-color-scheme"})}),e.Settings.registerSettingExtension({category:"RENDERING",settingName:"emulated-css-media-feature-forced-colors",settingType:"enum",storageType:"Session",defaultValue:"",options:[{title:Le(Ie.doNotEmulateCss,{PH1:"forced-colors"}),text:Le(Ie.noEmulation),value:""},{title:Le(Ie.emulateCss,{PH1:"forced-colors: active"}),text:o.i18n.lockedLazyString("forced-colors: active"),value:"active"},{title:Le(Ie.emulateCss,{PH1:"forced-colors: none"}),text:o.i18n.lockedLazyString("forced-colors: none"),value:"none"}],tags:[Le(Ie.query)],title:Le(Ie.emulateCssMediaFeature,{PH1:"forced-colors"})}),e.Settings.registerSettingExtension({category:"RENDERING",settingName:"emulated-css-media-feature-prefers-reduced-motion",settingType:"enum",storageType:"Session",defaultValue:"",options:[{title:Le(Ie.doNotEmulateCss,{PH1:"prefers-reduced-motion"}),text:Le(Ie.noEmulation),value:""},{title:Le(Ie.emulateCss,{PH1:"prefers-reduced-motion: reduce"}),text:o.i18n.lockedLazyString("prefers-reduced-motion: reduce"),value:"reduce"}],tags:[Le(Ie.query)],title:Le(Ie.emulateCssMediaFeature,{PH1:"prefers-reduced-motion"})}),e.Settings.registerSettingExtension({settingName:"emulated-css-media-feature-prefers-contrast",settingType:"enum",storageType:"Session",defaultValue:"",options:[{title:Le(Ie.doNotEmulateCss,{PH1:"prefers-contrast"}),text:Le(Ie.noEmulation),value:""},{title:Le(Ie.emulateCss,{PH1:"prefers-contrast: more"}),text:o.i18n.lockedLazyString("prefers-contrast: more"),value:"more"},{title:Le(Ie.emulateCss,{PH1:"prefers-contrast: less"}),text:o.i18n.lockedLazyString("prefers-contrast: less"),value:"less"},{title:Le(Ie.emulateCss,{PH1:"prefers-contrast: custom"}),text:o.i18n.lockedLazyString("prefers-contrast: custom"),value:"custom"}],tags:[Le(Ie.query)],title:Le(Ie.emulateCssMediaFeature,{PH1:"prefers-contrast"})}),e.Settings.registerSettingExtension({settingName:"emulated-css-media-feature-prefers-reduced-data",settingType:"enum",storageType:"Session",defaultValue:"",options:[{title:Le(Ie.doNotEmulateCss,{PH1:"prefers-reduced-data"}),text:Le(Ie.noEmulation),value:""},{title:Le(Ie.emulateCss,{PH1:"prefers-reduced-data: reduce"}),text:o.i18n.lockedLazyString("prefers-reduced-data: reduce"),value:"reduce"}],title:Le(Ie.emulateCssMediaFeature,{PH1:"prefers-reduced-data"})}),e.Settings.registerSettingExtension({settingName:"emulated-css-media-feature-prefers-reduced-transparency",settingType:"enum",storageType:"Session",defaultValue:"",options:[{title:Le(Ie.doNotEmulateCss,{PH1:"prefers-reduced-transparency"}),text:Le(Ie.noEmulation),value:""},{title:Le(Ie.emulateCss,{PH1:"prefers-reduced-transparency: reduce"}),text:o.i18n.lockedLazyString("prefers-reduced-transparency: reduce"),value:"reduce"}],title:Le(Ie.emulateCssMediaFeature,{PH1:"prefers-reduced-transparency"})}),e.Settings.registerSettingExtension({settingName:"emulated-css-media-feature-color-gamut",settingType:"enum",storageType:"Session",defaultValue:"",options:[{title:Le(Ie.doNotEmulateCss,{PH1:"color-gamut"}),text:Le(Ie.noEmulation),value:""},{title:Le(Ie.emulateCss,{PH1:"color-gamut: srgb"}),text:o.i18n.lockedLazyString("color-gamut: srgb"),value:"srgb"},{title:Le(Ie.emulateCss,{PH1:"color-gamut: p3"}),text:o.i18n.lockedLazyString("color-gamut: p3"),value:"p3"},{title:Le(Ie.emulateCss,{PH1:"color-gamut: rec2020"}),text:o.i18n.lockedLazyString("color-gamut: rec2020"),value:"rec2020"}],title:Le(Ie.emulateCssMediaFeature,{PH1:"color-gamut"})}),e.Settings.registerSettingExtension({category:"RENDERING",settingName:"emulated-vision-deficiency",settingType:"enum",storageType:"Session",defaultValue:"none",options:[{title:Le(Ie.doNotEmulateAnyVisionDeficiency),text:Le(Ie.noEmulation),value:"none"},{title:Le(Ie.emulateBlurredVision),text:Le(Ie.blurredVision),value:"blurredVision"},{title:Le(Ie.emulateReducedContrast),text:Le(Ie.reducedContrast),value:"reducedContrast"},{title:Le(Ie.emulateProtanopia),text:Le(Ie.protanopia),value:"protanopia"},{title:Le(Ie.emulateDeuteranopia),text:Le(Ie.deuteranopia),value:"deuteranopia"},{title:Le(Ie.emulateTritanopia),text:Le(Ie.tritanopia),value:"tritanopia"},{title:Le(Ie.emulateAchromatopsia),text:Le(Ie.achromatopsia),value:"achromatopsia"}],tags:[Le(Ie.query)],title:Le(Ie.emulateVisionDeficiencies)}),e.Settings.registerSettingExtension({category:"RENDERING",settingName:"local-fonts-disabled",settingType:"boolean",storageType:"Session",options:[{value:!0,title:Le(Ie.disableLocalFonts)},{value:!1,title:Le(Ie.enableLocalFonts)}],defaultValue:!1}),e.Settings.registerSettingExtension({category:"RENDERING",settingName:"avif-format-disabled",settingType:"boolean",storageType:"Session",options:[{value:!0,title:Le(Ie.disableAvifFormat)},{value:!1,title:Le(Ie.enableAvifFormat)}],defaultValue:!1}),e.Settings.registerSettingExtension({category:"RENDERING",settingName:"webp-format-disabled",settingType:"boolean",storageType:"Session",options:[{value:!0,title:Le(Ie.disableWebpFormat)},{value:!1,title:Le(Ie.enableWebpFormat)}],defaultValue:!1}),e.Settings.registerSettingExtension({category:"CONSOLE",title:Le(Ie.customFormatters),settingName:"custom-formatters",settingType:"boolean",defaultValue:!1}),e.Settings.registerSettingExtension({category:"NETWORK",title:Le(Ie.networkRequestBlocking),settingName:"request-blocking-enabled",settingType:"boolean",storageType:"Session",defaultValue:!1,options:[{value:!0,title:Le(Ie.enableNetworkRequestBlocking)},{value:!1,title:Le(Ie.disableNetworkRequestBlocking)}]}),e.Settings.registerSettingExtension({category:"NETWORK",title:Le(Ie.disableCache),settingName:"cache-disabled",settingType:"boolean",order:0,defaultValue:!1,userActionCondition:"hasOtherClients",options:[{value:!0,title:Le(Ie.disableCache)},{value:!1,title:Le(Ie.enableCache)}]}),e.Settings.registerSettingExtension({category:"RENDERING",title:Le(Ie.emulateAutoDarkMode),settingName:"emulate-auto-dark-mode",settingType:"boolean",storageType:"Session",defaultValue:!1}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:Le(Ie.enableRemoteFileLoading),settingName:"network.enable-remote-file-loading",settingType:"boolean",defaultValue:!1});const Me={defaultIndentation:"Default indentation:",setIndentationToSpaces:"Set indentation to 2 spaces",Spaces:"2 spaces",setIndentationToFSpaces:"Set indentation to 4 spaces",fSpaces:"4 spaces",setIndentationToESpaces:"Set indentation to 8 spaces",eSpaces:"8 spaces",setIndentationToTabCharacter:"Set indentation to tab character",tabCharacter:"Tab character"},Oe=o.i18n.registerUIStrings("ui/legacy/components/source_frame/source_frame-meta.ts",Me),Fe=o.i18n.getLazilyComputedLocalizedString.bind(void 0,Oe);let Ue,Ge;e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:Fe(Me.defaultIndentation),settingName:"text-editor-indent",settingType:"enum",defaultValue:"    ",options:[{title:Fe(Me.setIndentationToSpaces),text:Fe(Me.Spaces),value:"  "},{title:Fe(Me.setIndentationToFSpaces),text:Fe(Me.fSpaces),value:"    "},{title:Fe(Me.setIndentationToESpaces),text:Fe(Me.eSpaces),value:"        "},{title:Fe(Me.setIndentationToTabCharacter),text:Fe(Me.tabCharacter),value:"\t"}]}),c.Toolbar.registerToolbarItem({loadItem:async()=>(await async function(){return Ue||(Ue=await import("../../panels/console_counters/console_counters.js")),Ue}()).WarningErrorCounter.WarningErrorCounter.instance(),order:1,location:"main-toolbar-right"}),c.UIUtils.registerRenderer({contextTypes:()=>[a.RemoteObject.RemoteObject],loadRenderer:async()=>(await async function(){return Ge||(Ge=await import("../../ui/legacy/components/object_ui/object_ui.js")),Ge}()).ObjectPropertiesSection.Renderer.instance()});const Be={explainThisError:"Understand this error",explainThisWarning:"Understand this warning",explainThisMessage:"Understand this message",enableConsoleInsights:"Understand console messages with AI",wrongLocale:"To use this feature, update your Language preference in DevTools Settings to English.",ageRestricted:"This feature is only available to users who are 18 years of age or older.",geoRestricted:"This feature is unavailable in your region.",policyRestricted:"Your organization turned off this feature. Contact your administrators for more information.",rolloutRestricted:"This feature is currently being rolled out. Stay tuned."},He=o.i18n.registerUIStrings("panels/explain/explain-meta.ts",Be),We=o.i18n.getLazilyComputedLocalizedString.bind(void 0,He),ze=o.i18n.getLocalizedString.bind(void 0,He),je="console-insights-enabled",qe=[{actionId:"explain.console-message.hover",title:We(Be.explainThisMessage),contextTypes:()=>[d.ConsoleViewMessage.ConsoleViewMessage]},{actionId:"explain.console-message.context.error",title:We(Be.explainThisError),contextTypes:()=>[]},{actionId:"explain.console-message.context.warning",title:We(Be.explainThisWarning),contextTypes:()=>[]},{actionId:"explain.console-message.context.other",title:We(Be.explainThisMessage),contextTypes:()=>[]}];function _e(){return!o.DevToolsLocale.DevToolsLocale.instance().locale.startsWith("en-")}function Je(e){return!0===e?.devToolsConsoleInsights?.blockedByAge}function Ye(e){return!0===e?.devToolsConsoleInsights?.blockedByRollout}function Qe(e){return!0===e?.devToolsConsoleInsights?.blockedByGeo}function Ke(e){return!0===e?.devToolsConsoleInsights?.blockedByEnterprisePolicy}function Ze(e){return!1===e?.devToolsConsoleInsights?.blockedByFeatureFlag}e.Settings.registerSettingExtension({category:"CONSOLE",settingName:je,settingType:"boolean",title:We(Be.enableConsoleInsights),defaultValue:e=>!function(e){return!0===e?.devToolsConsoleInsights?.optIn}(e),reloadRequired:!0,condition:e=>Ze(e),disabledCondition:e=>_e()?{disabled:!0,reason:ze(Be.wrongLocale)}:Je(e)?{disabled:!0,reason:ze(Be.ageRestricted)}:Qe(e)?{disabled:!0,reason:ze(Be.geoRestricted)}:Ke(e)?{disabled:!0,reason:ze(Be.policyRestricted)}:Ye(e)?{disabled:!0,reason:ze(Be.rolloutRestricted)}:{disabled:!1}});for(const e of qe)c.ActionRegistration.registerActionExtension({...e,setting:je,category:"CONSOLE",loadActionDelegate:async()=>new((await import("../../panels/explain/explain.js")).ActionDelegate),condition:e=>Ze(e)&&!Je(e)&&!Qe(e)&&!_e()&&!Ke(e)&&!Ye(e)});const Xe="Ask Freestyler",$e=o.i18n.lockedLazyString,et="freestyler-enabled";let tt;async function ot(){return tt||(tt=await import("../../panels/freestyler/freestyler.js")),tt}function it(e){return!0===e?.devToolsFreestylerDogfood?.enabled}c.ViewManager.registerViewExtension({location:"drawer-view",id:"freestyler",commandPrompt:$e("Show Freestyler"),title:$e("Freestyler"),order:10,persistence:"closeable",hasToolbar:!1,condition:it,loadView:async()=>(await ot()).FreestylerPanel.instance()}),e.Settings.registerSettingExtension({category:"GLOBAL",settingName:et,settingType:"boolean",title:$e("Enable Freestyler"),defaultValue:it,reloadRequired:!0,condition:it}),c.ActionRegistration.registerActionExtension({actionId:"freestyler.element-panel-context",contextTypes:()=>[],setting:et,category:"GLOBAL",title:$e(Xe),loadActionDelegate:async()=>new((await ot()).ActionDelegate),condition:it}),c.ActionRegistration.registerActionExtension({actionId:"freestyler.style-tab-context",contextTypes:()=>[],setting:et,category:"GLOBAL",title:$e(Xe),iconClass:"spark",loadActionDelegate:async()=>new((await ot()).ActionDelegate),condition:it});
