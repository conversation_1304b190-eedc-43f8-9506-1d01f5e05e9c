import { Pointer } from '@pmndrs/pointer-events';
export declare function bindXRInputSourceEvent(session: XRSession, inputSource: XRInputSource | 'all', event: 'select' | 'selectstart' | 'selectend' | 'squeeze' | 'squeezestart' | 'squeezeend', fn: (event: XRInputSourceEvent) => void): () => void;
export declare function bindPointerXRInputSourceEvent(pointer: Pointer, session: XRSession, inputSource: XRInputSource, event: 'select' | 'squeeze', missingEvents: ReadonlyArray<XRInputSourceEvent>, options?: {
    button?: number;
}): () => void;
