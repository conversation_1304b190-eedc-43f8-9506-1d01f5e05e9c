"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),a=require("@react-three/fiber"),n=require("../materials/BlurPass.cjs.js"),s=require("../materials/MeshReflectorMaterial.cjs.js");function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var a=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,a.get?a:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}require("../materials/ConvolutionMaterial.cjs.js"),require("../helpers/constants.cjs.js");var l=o(e),u=i(t);const c=u.forwardRef((({mixBlur:e=0,mixStrength:t=.5,resolution:o=256,blur:i=[0,0],args:c=[1,1],minDepthThreshold:d=.9,maxDepthThreshold:p=1,depthScale:m=0,depthToBlurRatioBias:h=.25,mirror:x=0,children:f,debug:M=0,distortion:b=1,mixContrast:w=1,distortionMap:y,...T},S)=>{a.extend({MeshReflectorMaterial:s.MeshReflectorMaterial}),u.useEffect((()=>{console.warn("Reflector has been deprecated and will be removed next major. Replace it with <MeshReflectorMaterial />!")}),[]);const g=a.useThree((({gl:e})=>e)),R=a.useThree((({camera:e})=>e)),j=a.useThree((({scene:e})=>e)),v=(i=Array.isArray(i)?i:[i,i])[0]+i[1]>0,B=u.useRef(null);u.useImperativeHandle(S,(()=>B.current),[]);const[D]=u.useState((()=>new r.Plane)),[P]=u.useState((()=>new r.Vector3)),[F]=u.useState((()=>new r.Vector3)),[V]=u.useState((()=>new r.Vector3)),[W]=u.useState((()=>new r.Matrix4)),[E]=u.useState((()=>new r.Vector3(0,0,-1))),[O]=u.useState((()=>new r.Vector4)),[q]=u.useState((()=>new r.Vector3)),[U]=u.useState((()=>new r.Vector3)),[C]=u.useState((()=>new r.Vector4)),[_]=u.useState((()=>new r.Matrix4)),[I]=u.useState((()=>new r.PerspectiveCamera)),L=u.useCallback((()=>{if(F.setFromMatrixPosition(B.current.matrixWorld),V.setFromMatrixPosition(R.matrixWorld),W.extractRotation(B.current.matrixWorld),P.set(0,0,1),P.applyMatrix4(W),q.subVectors(F,V),q.dot(P)>0)return;q.reflect(P).negate(),q.add(F),W.extractRotation(R.matrixWorld),E.set(0,0,-1),E.applyMatrix4(W),E.add(V),U.subVectors(F,E),U.reflect(P).negate(),U.add(F),I.position.copy(q),I.up.set(0,1,0),I.up.applyMatrix4(W),I.up.reflect(P),I.lookAt(U),I.far=R.far,I.updateMatrixWorld(),I.projectionMatrix.copy(R.projectionMatrix),_.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),_.multiply(I.projectionMatrix),_.multiply(I.matrixWorldInverse),_.multiply(B.current.matrixWorld),D.setFromNormalAndCoplanarPoint(P,F),D.applyMatrix4(I.matrixWorldInverse),O.set(D.normal.x,D.normal.y,D.normal.z,D.constant);const e=I.projectionMatrix;C.x=(Math.sign(O.x)+e.elements[8])/e.elements[0],C.y=(Math.sign(O.y)+e.elements[9])/e.elements[5],C.z=-1,C.w=(1+e.elements[10])/e.elements[14],O.multiplyScalar(2/O.dot(C)),e.elements[2]=O.x,e.elements[6]=O.y,e.elements[10]=O.z+1,e.elements[14]=O.w}),[]),[k,z,A,G]=u.useMemo((()=>{const a={type:r.HalfFloatType,minFilter:r.LinearFilter,magFilter:r.LinearFilter},s=new r.WebGLRenderTarget(o,o,a);s.depthBuffer=!0,s.depthTexture=new r.DepthTexture(o,o),s.depthTexture.format=r.DepthFormat,s.depthTexture.type=r.UnsignedShortType;const l=new r.WebGLRenderTarget(o,o,a);return[s,l,new n.BlurPass({gl:g,resolution:o,width:i[0],height:i[1],minDepthThreshold:d,maxDepthThreshold:p,depthScale:m,depthToBlurRatioBias:h}),{mirror:x,textureMatrix:_,mixBlur:e,tDiffuse:s.texture,tDepth:s.depthTexture,tDiffuseBlur:l.texture,hasBlur:v,mixStrength:t,minDepthThreshold:d,maxDepthThreshold:p,depthScale:m,depthToBlurRatioBias:h,transparent:!0,debug:M,distortion:b,distortionMap:y,mixContrast:w,"defines-USE_BLUR":v?"":void 0,"defines-USE_DEPTH":m>0?"":void 0,"defines-USE_DISTORTION":y?"":void 0}]}),[g,i,_,o,x,v,e,t,d,p,m,h,M,b,y,w]);return a.useFrame((()=>{if(null==B||!B.current)return;B.current.visible=!1;const e=g.xr.enabled,t=g.shadowMap.autoUpdate;L(),g.xr.enabled=!1,g.shadowMap.autoUpdate=!1,g.setRenderTarget(k),g.state.buffers.depth.setMask(!0),g.autoClear||g.clear(),g.render(j,I),v&&A.render(g,k,z),g.xr.enabled=e,g.shadowMap.autoUpdate=t,B.current.visible=!0,g.setRenderTarget(null)})),u.createElement("mesh",l.default({ref:B},T),u.createElement("planeGeometry",{args:c}),f?f("meshReflectorMaterial",G):u.createElement("meshReflectorMaterial",G))}));exports.Reflector=c;
