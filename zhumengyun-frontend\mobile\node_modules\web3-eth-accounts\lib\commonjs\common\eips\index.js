"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EIPs = void 0;
/*
This file is part of web3.js.

web3.js is free software: you can redistribute it and/or modify
it under the terms of the GNU Lesser General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

web3.js is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU Lesser General Public License for more details.

You should have received a copy of the GNU Lesser General Public License
along with web3.js.  If not, see <http://www.gnu.org/licenses/>.
*/
const _1153_js_1 = __importDefault(require("./1153.js"));
const _1559_js_1 = __importDefault(require("./1559.js"));
const _2315_js_1 = __importDefault(require("./2315.js"));
const _2537_js_1 = __importDefault(require("./2537.js"));
const _2565_js_1 = __importDefault(require("./2565.js"));
const _2718_js_1 = __importDefault(require("./2718.js"));
const _2929_js_1 = __importDefault(require("./2929.js"));
const _2930_js_1 = __importDefault(require("./2930.js"));
const _3198_js_1 = __importDefault(require("./3198.js"));
const _3529_js_1 = __importDefault(require("./3529.js"));
const _3540_js_1 = __importDefault(require("./3540.js"));
const _3541_js_1 = __importDefault(require("./3541.js"));
const _3554_js_1 = __importDefault(require("./3554.js"));
const _3607_js_1 = __importDefault(require("./3607.js"));
const _3651_js_1 = __importDefault(require("./3651.js"));
const _3670_js_1 = __importDefault(require("./3670.js"));
const _3675_js_1 = __importDefault(require("./3675.js"));
const _3855_js_1 = __importDefault(require("./3855.js"));
const _3860_js_1 = __importDefault(require("./3860.js"));
const _4345_js_1 = __importDefault(require("./4345.js"));
const _4399_js_1 = __importDefault(require("./4399.js"));
const _5133_js_1 = __importDefault(require("./5133.js"));
exports.EIPs = {
    1153: _1153_js_1.default,
    1559: _1559_js_1.default,
    2315: _2315_js_1.default,
    2537: _2537_js_1.default,
    2565: _2565_js_1.default,
    2718: _2718_js_1.default,
    2929: _2929_js_1.default,
    2930: _2930_js_1.default,
    3198: _3198_js_1.default,
    3529: _3529_js_1.default,
    3540: _3540_js_1.default,
    3541: _3541_js_1.default,
    3554: _3554_js_1.default,
    3607: _3607_js_1.default,
    3651: _3651_js_1.default,
    3670: _3670_js_1.default,
    3675: _3675_js_1.default,
    3855: _3855_js_1.default,
    3860: _3860_js_1.default,
    4345: _4345_js_1.default,
    4399: _4399_js_1.default,
    5133: _5133_js_1.default,
};
//# sourceMappingURL=index.js.map