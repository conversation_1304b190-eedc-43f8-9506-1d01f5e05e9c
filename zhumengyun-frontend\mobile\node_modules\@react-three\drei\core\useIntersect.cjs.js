"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("@react-three/fiber");function t(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var n=t(e);exports.useIntersect=function(e){const t=n.useRef(null),u=n.useRef(!1),c=n.useRef(!1),f=n.useRef(e);return n.useLayoutEffect((()=>{f.current=e}),[e]),n.useEffect((()=>{const e=t.current;if(e){const t=r.addEffect((()=>(u.current=!1,!0))),n=e.onBeforeRender;e.onBeforeRender=()=>u.current=!0;const o=r.addAfterEffect((()=>(u.current!==c.current&&(null==f.current||f.current(c.current=u.current)),!0)));return()=>{e.onBeforeRender=n,t(),o()}}}),[]),t};
