{"version": 3, "file": "XRController.js", "sourceRoot": "", "sources": ["../../src/device/XRController.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,OAAO,EAAiB,MAAM,uBAAuB,CAAC;AAC/D,OAAO,EAAe,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,eAAe,EAAE,MAAM,eAAe,CAAC;AACzE,OAAO,EAEN,aAAa,EACb,eAAe,GACf,MAAM,2BAA2B,CAAC;AAEnC,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AAerD,MAAM,OAAO,YAAa,SAAQ,cAAc;IAM/C,YACC,gBAAoC,EACpC,UAAwB,EACxB,WAAwB;QAExB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE;YACzC,MAAM,IAAI,YAAY,CAAC,0BAA0B,EAAE,mBAAmB,CAAC,CAAC;SACxE;QACD,MAAM,cAAc,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC;QAChD,MAAM,SAAS,GAAG,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAE,CAAC,gBAAgB;YACtE,CAAC,CAAC,IAAI,OAAO,CACX,cAAc,EACd,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAE,CAAC,gBAAgB,CACpD;YACH,CAAC,CAAC,SAAS,CAAC;QACb,MAAM,QAAQ,GAAG;YAChB,gBAAgB,CAAC,SAAS;YAC1B,GAAG,gBAAgB,CAAC,kBAAkB;SACtC,CAAC;QACF,MAAM,WAAW,GAAG,IAAI,aAAa,CACpC,UAAU,EACV,eAAe,CAAC,cAAc,EAC9B,QAAQ,EACR,cAAc,EACd,IAAI,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAE,CAAC,OAAO,CAAC,EACzD,SAAS,CACT,CAAC;QAEF,KAAK,CAAC,WAAW,CAAC,CAAC;QACnB,IAAI,CAAC,YAAY,CAAC,GAAG;YACpB,SAAS,EAAE,gBAAgB,CAAC,SAAS;YACrC,aAAa,EAAE,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAE,CAAC,OAAO;SAC3D,CAAC;IACH,CAAC;IAED,IAAI,aAAa;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,aAAa,CAAC;IACzC,CAAC;IAED,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC;IACrC,CAAC;IAED,iBAAiB,CAAC,EAAU,EAAE,KAAa;QAC1C,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;YAC3B,OAAO,CAAC,IAAI,CAAC,sBAAsB,KAAK,wBAAwB,EAAE,GAAG,CAAC,CAAC;YACvE,OAAO;SACP;QACD,MAAM,aAAa,GAClB,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,OAAQ,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACtE,IAAI,aAAa,EAAE;YAClB,IACC,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,QAAQ;gBAC1C,KAAK,IAAI,CAAC;gBACV,KAAK,IAAI,CAAC,EACT;gBACD,OAAO,CAAC,IAAI,CACX,oBAAoB,KAAK,+BAA+B,EAAE,GAAG,CAC7D,CAAC;gBACF,OAAO;aACP;YACD,aAAa,CAAC,SAAS,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC;SAC9C;aAAM;YACN,OAAO,CAAC,IAAI,CAAC,2CAA2C,EAAE,GAAG,CAAC,CAAC;SAC/D;IACF,CAAC;IAED,iBAAiB,CAAC,EAAU,EAAE,OAAgB;QAC7C,MAAM,aAAa,GAClB,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,OAAQ,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACtE,IAAI,aAAa,EAAE;YAClB,aAAa,CAAC,SAAS,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC;SAC3C;aAAM;YACN,OAAO,CAAC,IAAI,CAAC,2CAA2C,EAAE,GAAG,CAAC,CAAC;SAC/D;IACF,CAAC;IAED,UAAU,CAAC,EAAU,EAAE,IAAyB,EAAE,KAAa;QAC9D,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YAC5B,OAAO,CAAC,IAAI,CAAC,sBAAsB,KAAK,iBAAiB,EAAE,QAAQ,CAAC,CAAC;YACrE,OAAO;SACP;QACD,MAAM,QAAQ,GACb,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,OAAQ,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACnE,IAAI,QAAQ,EAAE;YACb,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACtB,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;aACnB;iBAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;gBAC7B,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;aACnB;SACD;aAAM;YACN,OAAO,CAAC,IAAI,CAAC,oCAAoC,EAAE,QAAQ,CAAC,CAAC;SAC7D;IACF,CAAC;IAED,UAAU,CAAC,EAAU,EAAE,CAAS,EAAE,CAAS;QAC1C,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;YACvC,OAAO,CAAC,IAAI,CACX,wBAAwB,CAAC,OAAO,CAAC,iBAAiB,EAAE,QAAQ,CAC5D,CAAC;YACF,OAAO;SACP;QACD,MAAM,QAAQ,GACb,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,OAAQ,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACnE,IAAI,QAAQ,EAAE;YACb,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;YACf,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;SACf;aAAM;YACN,OAAO,CAAC,IAAI,CAAC,oCAAoC,EAAE,QAAQ,CAAC,CAAC;SAC7D;IACF,CAAC;CACD"}