{"version": 3, "file": "rpc_error_messages.js", "sourceRoot": "", "sources": ["../../../src/errors/rpc_error_messages.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;;AAEF,sDAkB2B;AAE3B;;;;GAIG;AACU,QAAA,8BAA8B,GAAG,gDAAgD,CAAC;AAE/F,yDAAyD;AAC5C,QAAA,gBAAgB,GAEzB;IACH,2BAA2B;IAC3B,gEAAgE;IAChE,CAAC,qCAAoB,CAAC,EAAE;QACvB,OAAO,EAAE,aAAa;QACtB,WAAW,EAAE,cAAc;KAC3B;IACD,CAAC,wCAAuB,CAAC,EAAE;QAC1B,OAAO,EAAE,iBAAiB;QAC1B,WAAW,EAAE,qCAAqC;KAClD;IACD,CAAC,uCAAsB,CAAC,EAAE;QACzB,OAAO,EAAE,kBAAkB;QAC3B,WAAW,EAAE,wBAAwB;KACrC;IACD,CAAC,uCAAsB,CAAC,EAAE;QACzB,OAAO,EAAE,gBAAgB;QACzB,WAAW,EAAE,2BAA2B;KACxC;IACD,CAAC,uCAAsB,CAAC,EAAE;QACzB,OAAO,EAAE,gBAAgB;QACzB,WAAW,EAAE,yBAAyB;KACtC;IAED,CAAC,sCAAqB,CAAC,EAAE;QACxB,OAAO,EAAE,eAAe;QACxB,WAAW,EAAE,+BAA+B;KAC5C;IACD,CAAC,yCAAwB,CAAC,EAAE;QAC3B,OAAO,EAAE,oBAAoB;QAC7B,WAAW,EAAE,8BAA8B;KAC3C;IACD,CAAC,6CAA4B,CAAC,EAAE;QAC/B,OAAO,EAAE,sBAAsB;QAC/B,WAAW,EAAE,kCAAkC;KAC/C;IACD,CAAC,6CAA4B,CAAC,EAAE;QAC/B,OAAO,EAAE,sBAAsB;QAC/B,WAAW,EAAE,6BAA6B;KAC1C;IACD,CAAC,2CAA0B,CAAC,EAAE;QAC7B,OAAO,EAAE,sBAAsB;QAC/B,WAAW,EAAE,2BAA2B;KACxC;IACD,CAAC,uCAAsB,CAAC,EAAE;QACzB,OAAO,EAAE,gBAAgB;QACzB,WAAW,EAAE,+BAA+B;KAC5C;IACD,CAAC,sCAAqB,CAAC,EAAE;QACxB,OAAO,EAAE,gCAAgC;QACzC,WAAW,EAAE,+CAA+C;KAC5D;IAED,WAAW;IACX,gFAAgF;IAChF,CAAC,6CAA4B,CAAC,EAAE;QAC/B,IAAI,EAAE,uBAAuB;QAC7B,OAAO,EAAE,gCAAgC;KACzC;IACD,CAAC,yCAAwB,CAAC,EAAE;QAC3B,IAAI,EAAE,cAAc;QACpB,OAAO,EAAE,0EAA0E;KACnF;IACD,CAAC,+CAA8B,CAAC,EAAE;QACjC,IAAI,EAAE,oBAAoB;QAC1B,OAAO,EAAE,qDAAqD;KAC9D;IACD,CAAC,yCAAwB,CAAC,EAAE;QAC3B,IAAI,EAAE,cAAc;QACpB,OAAO,EAAE,+CAA+C;KACxD;IACD,CAAC,+CAA8B,CAAC,EAAE;QACjC,IAAI,EAAE,oBAAoB;QAC1B,OAAO,EAAE,uDAAuD;KAChE;IAED,wBAAwB;IACxB,mEAAmE;IACnE,OAAO,EAAE;QACR,IAAI,EAAE,EAAE;QACR,OAAO,EAAE,WAAW;KACpB;IACD,IAAI,EAAE;QACL,IAAI,EAAE,gBAAgB;QACtB,OAAO,EAAE,6EAA6E;KACtF;IACD,IAAI,EAAE;QACL,IAAI,EAAE,YAAY;QAClB,OAAO,EACN,oJAAoJ;KACrJ;IACD,IAAI,EAAE;QACL,IAAI,EAAE,gBAAgB;QACtB,OAAO,EAAE,qEAAqE;KAC9E;IACD,IAAI,EAAE;QACL,IAAI,EAAE,kBAAkB;QACxB,OAAO,EACN,6JAA6J;KAC9J;IACD,IAAI,EAAE;QACL,IAAI,EAAE,UAAU;QAChB,OAAO,EAAE,qDAAqD;KAC9D;IACD,IAAI,EAAE;QACL,IAAI,EAAE,gBAAgB;QACtB,OAAO,EACN,oFAAoF;KACrF;IACD,IAAI,EAAE;QACL,IAAI,EAAE,kBAAkB;QACxB,OAAO,EACN,uIAAuI;KACxI;IACD,IAAI,EAAE;QACL,IAAI,EAAE,4BAA4B;QAClC,OAAO,EACN,0JAA0J;KAC3J;IACD,IAAI,EAAE;QACL,IAAI,EAAE,kBAAkB;QACxB,OAAO,EACN,mLAAmL;KACpL;IACD,IAAI,EAAE;QACL,IAAI,EAAE,iBAAiB;QACvB,OAAO,EACN,iGAAiG;KAClG;IACD,IAAI,EAAE;QACL,IAAI,EAAE,gBAAgB;QACtB,OAAO,EACN,oIAAoI;KACrI;IACD,IAAI,EAAE;QACL,IAAI,EAAE,gBAAgB;QACtB,OAAO,EACN,wIAAwI;KACzI;IACD,IAAI,EAAE;QACL,IAAI,EAAE,iBAAiB;QACvB,OAAO,EAAE,oEAAoE;KAC7E;IACD,IAAI,EAAE;QACL,IAAI,EAAE,iBAAiB;QACvB,OAAO,EACN,sIAAsI;KACvI;IACD,IAAI,EAAE;QACL,IAAI,EAAE,aAAa;QACnB,OAAO,EACN,iJAAiJ;KAClJ;IACD,IAAI,EAAE;QACL,IAAI,EAAE,eAAe;QACrB,OAAO,EACN,kJAAkJ;KACnJ;IACD,WAAW,EAAE;QACZ,IAAI,EAAE,EAAE;QACR,OAAO,EACN,6HAA6H;KAC9H;IACD,WAAW,EAAE;QACZ,IAAI,EAAE,EAAE;QACR,OAAO,EACN,qLAAqL;KACtL;IACD,WAAW,EAAE;QACZ,IAAI,EAAE,EAAE;QACR,OAAO,EACN,qMAAqM;KACtM;CACD,CAAC"}