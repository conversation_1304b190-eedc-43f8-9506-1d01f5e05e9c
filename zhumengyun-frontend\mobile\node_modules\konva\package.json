{"name": "konva", "version": "9.3.22", "description": "HTML5 2d canvas library.", "author": "<PERSON>", "files": ["README.md", "konva.js", "konva.min.js", "lib", "cmj"], "main": "./lib/index-node.js", "browser": "./lib/index.js", "typings": "./lib/index-types.d.ts", "scripts": {"start": "npm run test:watch", "compile": "npm run clean && npm run tsc && cp ./src/index-types.d.ts ./lib/index-types.d.ts && npm run rollup", "build": "npm run compile && cp ./src/index-types.d.ts ./lib && gulp build && node ./rename-imports.mjs", "test:import": "npm run build  && node ./test/import-test.cjs && node ./test/import-test.mjs", "test": "npm run test:browser && npm run test:node", "test:build": "PARCEL_WORKER_BACKEND=process parcel build ./test/unit-tests.html --dist-dir ./test-build --target none --public-url ./ --no-source-maps", "test:browser": "npm run test:build && mocha-headless-chrome -f ./test-build/unit-tests.html -a disable-web-security -a no-sandbox -a disable-setuid-sandbox", "test:watch": "rm -rf ./.parcel-cache && PARCEL_WORKERS=0  parcel serve ./test/unit-tests.html ./test/manual-tests.html ./test/sandbox.html ./test/text-paths.html ./test/bunnies.html", "test:node:compiled": "rm -rf ./.test-temp && mkdir ./.test-temp && (tsc -p ./test/tsconfig.json --outDir ./.test-temp || true) && mocha './.test-temp/test/unit/**/*.js' -r ./test/node-global-setup.mjs --exit && rm -rf ./.test-temp && npm run test:import", "test:node": "npm run test:node:compiled", "tsc": "tsc --removeComments", "rollup": "rollup -c --bundleConfigAsCjs", "clean": "rm -rf ./lib && rm -rf ./types && rm -rf ./cmj && rm -rf ./test-build", "watch": "rollup -c -w", "size": "size-limit"}, "targets": {"none": {}}, "funding": [{"type": "patreon", "url": "https://www.patreon.com/lavrton"}, {"type": "opencollective", "url": "https://opencollective.com/konva"}, {"type": "github", "url": "https://github.com/sponsors/lavrton"}], "size-limit": [{"limit": "45 KB", "path": "./lib/index.js"}, {"limit": "26 KB", "path": "./lib/Core.js"}, {"path": "./konva.min.js"}], "devDependencies": {"@parcel/transformer-image": "2.13.2", "@size-limit/preset-big-lib": "^11.1.6", "@types/mocha": "^10.0.10", "canvas": "^3.1.0", "chai": "5.1.2", "filehound": "^1.17.6", "gulp": "^5.0.0", "gulp-concat": "^2.6.1", "gulp-connect": "^5.7.0", "gulp-exec": "^5.0.0", "gulp-jsdoc3": "^3.0.0", "gulp-rename": "^2.0.0", "gulp-replace": "^1.1.4", "gulp-typescript": "^5.0.1", "gulp-uglify": "^3.0.2", "gulp-uglify-es": "^3.0.0", "gulp-util": "^3.0.8", "mocha": "10.2.0", "mocha-headless-chrome": "^4.0.0", "parcel": "2.13.3", "process": "^0.11.10", "rollup": "^4.31.0", "rollup-plugin-typescript2": "^0.36.0", "size-limit": "^11.1.6", "ts-mocha": "^10.0.0", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "keywords": ["canvas", "animations", "graphic", "html5"], "prettier": {"singleQuote": true}, "bugs": {"url": "https://github.com/konvajs/konva/issues"}, "homepage": "http://konvajs.org/", "readmeFilename": "README.md", "repository": {"type": "git", "url": "git://github.com/konvajs/konva.git"}, "license": "MIT"}