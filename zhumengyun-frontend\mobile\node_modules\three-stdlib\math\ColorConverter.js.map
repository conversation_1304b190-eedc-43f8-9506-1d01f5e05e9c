{"version": 3, "file": "ColorConverter.js", "sources": ["../../src/math/ColorConverter.js"], "sourcesContent": ["import { MathUtils } from 'three'\n\nconst _hsl = {}\n\nconst ColorConverter = {\n  setHSV(color, h, s, v) {\n    // https://gist.github.com/xpansive/1337890#file-index-js\n\n    h = MathUtils.euclideanModulo(h, 1)\n    s = MathUtils.clamp(s, 0, 1)\n    v = MathUtils.clamp(v, 0, 1)\n\n    return color.setHSL(h, (s * v) / ((h = (2 - s) * v) < 1 ? h : 2 - h), h * 0.5)\n  },\n\n  getHSV(color, target) {\n    color.getHSL(_hsl)\n\n    // based on https://gist.github.com/xpansive/1337890#file-index-js\n    _hsl.s *= _hsl.l < 0.5 ? _hsl.l : 1 - _hsl.l\n\n    target.h = _hsl.h\n    target.s = (2 * _hsl.s) / (_hsl.l + _hsl.s)\n    target.v = _hsl.l + _hsl.s\n\n    return target\n  },\n\n  // where c, m, y, k is between 0 and 1\n\n  setCMYK(color, c, m, y, k) {\n    const r = (1 - c) * (1 - k)\n    const g = (1 - m) * (1 - k)\n    const b = (1 - y) * (1 - k)\n\n    return color.setRGB(r, g, b)\n  },\n\n  getCMYK(color, target) {\n    const r = color.r\n    const g = color.g\n    const b = color.b\n\n    const k = 1 - Math.max(r, g, b)\n    const c = (1 - r - k) / (1 - k)\n    const m = (1 - g - k) / (1 - k)\n    const y = (1 - b - k) / (1 - k)\n\n    target.c = c\n    target.m = m\n    target.y = y\n    target.k = k\n\n    return target\n  },\n}\n\nexport { ColorConverter }\n"], "names": [], "mappings": ";AAEA,MAAM,OAAO,CAAE;AAEV,MAAC,iBAAiB;AAAA,EACrB,OAAO,OAAO,GAAG,GAAG,GAAG;AAGrB,QAAI,UAAU,gBAAgB,GAAG,CAAC;AAClC,QAAI,UAAU,MAAM,GAAG,GAAG,CAAC;AAC3B,QAAI,UAAU,MAAM,GAAG,GAAG,CAAC;AAE3B,WAAO,MAAM,OAAO,GAAI,IAAI,MAAO,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG;AAAA,EAC9E;AAAA,EAED,OAAO,OAAO,QAAQ;AACpB,UAAM,OAAO,IAAI;AAGjB,SAAK,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI,IAAI,KAAK;AAE3C,WAAO,IAAI,KAAK;AAChB,WAAO,IAAK,IAAI,KAAK,KAAM,KAAK,IAAI,KAAK;AACzC,WAAO,IAAI,KAAK,IAAI,KAAK;AAEzB,WAAO;AAAA,EACR;AAAA;AAAA,EAID,QAAQ,OAAO,GAAG,GAAG,GAAG,GAAG;AACzB,UAAM,KAAK,IAAI,MAAM,IAAI;AACzB,UAAM,KAAK,IAAI,MAAM,IAAI;AACzB,UAAM,KAAK,IAAI,MAAM,IAAI;AAEzB,WAAO,MAAM,OAAO,GAAG,GAAG,CAAC;AAAA,EAC5B;AAAA,EAED,QAAQ,OAAO,QAAQ;AACrB,UAAM,IAAI,MAAM;AAChB,UAAM,IAAI,MAAM;AAChB,UAAM,IAAI,MAAM;AAEhB,UAAM,IAAI,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC;AAC9B,UAAM,KAAK,IAAI,IAAI,MAAM,IAAI;AAC7B,UAAM,KAAK,IAAI,IAAI,MAAM,IAAI;AAC7B,UAAM,KAAK,IAAI,IAAI,MAAM,IAAI;AAE7B,WAAO,IAAI;AACX,WAAO,IAAI;AACX,WAAO,IAAI;AACX,WAAO,IAAI;AAEX,WAAO;AAAA,EACR;AACH;"}