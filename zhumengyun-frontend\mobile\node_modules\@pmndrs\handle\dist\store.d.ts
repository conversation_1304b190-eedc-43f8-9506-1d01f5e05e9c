import { <PERSON><PERSON><PERSON>, <PERSON>4, <PERSON>bject3<PERSON>, Object3DEventMap, Quaternion, Vector2Tuple, Vector3, Vector3Tuple } from 'three';
import { TranslateAsHandlePointerData, TranslateAsHandleStoreData } from './computations/index.js';
import { OnePointerHandlePointerData, OnePointerHandleStoreData } from './computations/one-pointer.js';
import { TwoPointerHandlePointerData, TwoPointerHandleStoreData } from './computations/two-pointer.js';
import { Axis, HandleState, HandleStateImpl } from './state.js';
import type { PointerEvent, PointerEventsMap } from '@pmndrs/pointer-events';
export type HandleOptions<T> = {
    /**
     * function that allows to modify and apply the state to the target
     * @default (state, target) => {target.position.copy(state.current.position);target.quaternion.copy(state.current.quaternion);target.scale.copy(state.current.scale);}
     */
    apply?: (state: HandleState<T>, target: Object3D) => T;
    /**
     * @default false
     * necassary if the relative space (e.g. when using the default relativeTo="local") changes often (e.g. every frame)
     */
    alwaysUpdate?: boolean;
    /**
     * @default true
     */
    multitouch?: boolean;
    /**
     * @default true
     */
    rotate?: HandleTransformOptions;
    /**
     * allows to configure whether rays from input devices should be projected onto the interaction space (3D plane or 3D Line).
     * @default true
     */
    projectRays?: boolean;
    /**
     * @default true
     */
    scale?: HandleTransformOptions & {
        /**
         * @default false
         */
        uniform?: boolean;
    };
    /**
     * @default true
     */
    stopPropagation?: boolean;
} & ({
    /**
     * @default true
     */
    translate?: HandleTransformOptions;
} | {
    /**
     * @default true
     */
    translate?: 'as-rotate' | 'as-scale' | 'as-rotate-and-scale';
});
export type HandleTransformOptions = {
    x?: boolean | Vector2Tuple;
    y?: boolean | Vector2Tuple;
    z?: boolean | Vector2Tuple;
} | boolean | Axis | Array<Vector3Tuple | Vector3>;
export declare class HandleStore<T> implements OnePointerHandleStoreData, TwoPointerHandleStoreData, TranslateAsHandleStoreData {
    private readonly target;
    readonly getOptions: () => HandleOptions<T>;
    protected outputState: HandleStateImpl<T>;
    protected latestMoveEvent: PointerEvent | undefined;
    readonly inputState: Map<number, OnePointerHandlePointerData & TwoPointerHandlePointerData & TranslateAsHandlePointerData>;
    readonly capturedObjects: Map<number, Object3D<Object3DEventMap>>;
    readonly initialTargetPosition: Vector3;
    readonly initialTargetQuaternion: Quaternion;
    readonly initialTargetRotation: Euler;
    readonly initialTargetScale: Vector3;
    initialTargetParentWorldMatrix: Matrix4 | undefined;
    prevTwoPointerDeltaRotation: Quaternion | undefined;
    prevTranslateAsDeltaRotation: Quaternion | undefined;
    prevAngle: number | undefined;
    readonly handlers: {
        onPointerDown: (event: PointerEvent) => void;
        onPointerMove: (event: PointerEvent) => void;
        onPointerUp: (event: PointerEvent) => void;
    };
    constructor(target: Object3D | {
        current?: Object3D | null;
    }, getOptions?: () => HandleOptions<T>);
    /**
     * @requires that the pointerId is in this.capturedSet
     */
    private firstOnPointer;
    private onPointerDown;
    private onPointerMove;
    cancel(): void;
    private onPointerUp;
    update(time: number): void;
    protected getTarget(): Object3D<Object3DEventMap> | null | undefined;
    private capturePointer;
    private releasePointer;
    private stopPropagation;
    protected apply(target: Object3D): T;
    getState(): HandleState<T> | undefined;
    save(): void;
    bind(handle: Object3D<PointerEventsMap & Object3DEventMap>): () => void;
    capture(pointerId: number, object: Object3D): () => void;
}
export declare function defaultApply(state: HandleState<unknown>, target: Object3D): any;
