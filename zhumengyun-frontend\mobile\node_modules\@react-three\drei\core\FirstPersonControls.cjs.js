"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("@react-three/fiber"),n=require("three-stdlib");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function o(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var s=u(e),c=o(r);const l=c.forwardRef((({domElement:e,makeDefault:r,...u},o)=>{const l=t.useThree((e=>e.camera)),a=t.useThree((e=>e.gl)),f=t.useThree((e=>e.events)),i=t.useThree((e=>e.get)),d=t.useThree((e=>e.set)),b=e||f.connected||a.domElement,[m]=c.useState((()=>new n.FirstPersonControls(l,b)));return c.useEffect((()=>{if(r){const e=i().controls;return d({controls:m}),()=>d({controls:e})}}),[r,m]),t.useFrame(((e,r)=>{m.update(r)}),-1),m?c.createElement("primitive",s.default({ref:o,object:m},u)):null}));exports.FirstPersonControls=l;
