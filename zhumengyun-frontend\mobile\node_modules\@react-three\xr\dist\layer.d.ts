import { XRLayerProperties as XRLayer<PERSON>ynamicProperties, XRLayerEntry, XRLayerOptions, XRLayerSrc } from '@pmndrs/xr';
import { RootState, ThreeElements } from '@react-three/fiber';
import { MutableRefObject, ReactNode } from 'react';
import { B<PERSON>erGeometry, Mesh, WebGLRenderTarget } from 'three';
import { StoreApi } from 'zustand';
export type XRLayerProperties = XRLayerOptions & XRLayerDynamicProperties & Omit<ThreeElements['mesh'], 'geometry' | 'ref'> & {
    renderPriority?: number;
    children?: ReactNode;
    pixelWidth?: number;
    pixelHeight?: number;
    dpr?: number;
    src?: Exclude<XRLayerSrc, WebGLRenderTarget>;
    customRender?: (target: WebGLRenderTarget, state: RootState, delta: number, frame: XRFrame | undefined) => void;
};
/**
 * Component for rendering high quality quad, cylinder, or equirect layers inside supported sessions. Also includes a fallback for non-supported sessions.
 *
 * @param props
 * #### `src` - Property for displaying images and videos onto the layer. For rendering dynamic content to the layer, leave the `src` empty and put the dynamic (3D) content into the children, so that the layer acts as a render target.
 * #### `shape` - Property to configure the shape of the layer ("quad", "cylinder", "equirect").
 * #### `layout` - Property to configure the layout of the display content for stereo content ("default", "mono", "stereo-left-right", "stereo-top-bottom").
 * #### `centralAngle` - Property to configure the central angle in case the layer shape is a "cylinder".
 * #### `centralHorizontalAngle` - Property to configure the central horizontal angle in case the layer shape is "equirect".
 * #### `upperVerticalAngle` - Property to configure the upper vertical angle in case the layer shape is "equirect".
 * #### `lowerVerticalAngle` - Property to configure the lower vertical angle in case the layer shape is "equirect".
 * #### `chromaticAberrationCorrection` - Property to configure whether chromatic abberration should be corrected by the layer.
 * #### `quality` - Property to configure for what type of content the layer should be optimized ("default", "text-optimized", "graphics-optimized").
 */
export declare function XRLayer({ src, pixelWidth, pixelHeight, dpr, renderPriority, children, customRender, ...props }: XRLayerProperties): import("react/jsx-runtime").JSX.Element | null;
export declare const XRLayerImplementation: import("react").ForwardRefExoticComponent<Omit<XRLayerProperties, "src"> & {
    src?: Exclude<XRLayerSrc, WebGLRenderTarget>;
    geometry?: BufferGeometry;
    pixelWidth: number;
    pixelHeight: number;
    dpr: number;
    renderTargetRef: MutableRefObject<WebGLRenderTarget | undefined | null>;
    layerEntryRef: MutableRefObject<XRLayerEntry | undefined | null>;
} & import("react").RefAttributes<Mesh<BufferGeometry<import("three").NormalBufferAttributes>, import("three").Material | import("three").Material[], import("three").Object3DEventMap>>>;
export declare const FallbackXRLayerImplementation: import("react").ForwardRefExoticComponent<Omit<XRLayerProperties, "src"> & {
    src?: Exclude<XRLayerSrc, WebGLRenderTarget>;
    geometry?: BufferGeometry;
    pixelWidth: number;
    pixelHeight: number;
    dpr: number;
    renderTargetRef: MutableRefObject<WebGLRenderTarget | undefined>;
} & import("react").RefAttributes<Mesh<BufferGeometry<import("three").NormalBufferAttributes>, import("three").Material | import("three").Material[], import("three").Object3DEventMap>>>;
export declare const privateKeys: string[];
export declare function useLayerStore(width: number, height: number, dpr: number): {
    (): RootState & {
        setPreviousState: (prevState: RootState) => void;
    };
    <U>(selector: (state: RootState & {
        setPreviousState: (prevState: RootState) => void;
    }) => U): U;
    <U>(selector: (state: RootState & {
        setPreviousState: (prevState: RootState) => void;
    }) => U, equalityFn: (a: U, b: U) => boolean): U;
} & StoreApi<RootState & {
    setPreviousState: (prevState: RootState) => void;
}> & {
    setState(state: Partial<RootState>): void;
};
