import { mat4 } from 'gl-matrix';
interface Button {
    id: string;
    type: 'binary' | 'analog';
    eventTrigger?: 'select' | 'squeeze';
}
interface Axis {
    id: string;
    type: 'x-axis' | 'y-axis';
}
export interface GamepadConfig {
    mapping: 'xr-standard' | '';
    buttons: (But<PERSON> | null)[];
    axes: (Axis | null)[];
}
export interface XRControllerConfig {
    profileId: string;
    fallbackProfileIds: string[];
    layout: {
        [handedness: string]: {
            gamepad: GamepadConfig;
            gripOffsetMatrix: mat4 | undefined;
            numHapticActuators: number;
        };
    };
}
export declare const ControllerConfigs: {
    [controllerId: string]: XRControllerConfig;
};
export {};
//# sourceMappingURL=controllers.d.ts.map