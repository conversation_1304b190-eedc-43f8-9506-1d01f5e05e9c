"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("@react-three/fiber"),r=require("stats.js"),n=require("../helpers/useEffectfulState.cjs.js");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function c(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var f=c(e),l=u(r);exports.Stats=function({showPanel:e=0,className:r,parent:u}){const c=n.useEffectfulState((()=>new l.default),[]);return f.useEffect((()=>{if(c){const n=u&&u.current||document.body;c.showPanel(e),null==n||n.appendChild(c.dom);const f=(null!=r?r:"").split(" ").filter((e=>e));f.length&&c.dom.classList.add(...f);const l=t.addEffect((()=>c.begin())),s=t.addAfterEffect((()=>c.end()));return()=>{f.length&&c.dom.classList.remove(...f),null==n||n.removeChild(c.dom),l(),s()}}}),[u,c,r,e]),null};
