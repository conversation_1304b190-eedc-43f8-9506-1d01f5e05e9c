"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("@react-three/fiber"),r=require("react"),n=require("three-stdlib");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function o(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var a=s(e),u=o(r);const c=u.forwardRef((({makeDefault:e,camera:r,regress:s,domElement:o,enableDamping:c=!0,keyEvents:i=!1,onChange:f,onStart:d,onEnd:l,...m},v)=>{const b=t.useThree((e=>e.invalidate)),E=t.useThree((e=>e.camera)),h=t.useThree((e=>e.gl)),p=t.useThree((e=>e.events)),g=t.useThree((e=>e.setEvents)),O=t.useThree((e=>e.set)),j=t.useThree((e=>e.get)),T=t.useThree((e=>e.performance)),y=r||E,L=o||p.connected||h.domElement,q=u.useMemo((()=>new n.OrbitControls(y)),[y]);return t.useFrame((()=>{q.enabled&&q.update()}),-1),u.useEffect((()=>(i&&q.connect(!0===i?L:i),q.connect(L),()=>{q.dispose()})),[i,L,s,q,b]),u.useEffect((()=>{const e=e=>{b(),s&&T.regress(),f&&f(e)},t=e=>{d&&d(e)},r=e=>{l&&l(e)};return q.addEventListener("change",e),q.addEventListener("start",t),q.addEventListener("end",r),()=>{q.removeEventListener("start",t),q.removeEventListener("end",r),q.removeEventListener("change",e)}}),[f,d,l,q,b,g]),u.useEffect((()=>{if(e){const e=j().controls;return O({controls:q}),()=>O({controls:e})}}),[e,q]),u.createElement("primitive",a.default({ref:v,object:q,enableDamping:c},m))}));exports.OrbitControls=c;
