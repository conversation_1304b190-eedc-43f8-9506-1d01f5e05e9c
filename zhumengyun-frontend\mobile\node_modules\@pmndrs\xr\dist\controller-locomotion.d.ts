import { Camera, Object3D, Vector3 } from 'three';
import { XRStore } from './internals.js';
export type XRControllerLocomotionTranslationOptions = {
    speed?: number;
} | boolean;
export type XRControllerLocomotionRotationOptions = ({
    deadZone?: number;
} & ({
    type?: 'snap';
    degrees?: number;
} | {
    type: 'smooth';
    speed?: number;
})) | boolean;
/**
 * Function for handling controller based locomotion in VR
 * @param target Either an `Object`, or a callback function. Recieves translation and rotation input (required).
 * @param translationOptions Options that control the translation of the user. Set to `false` to disable.
 * @param translationOptions.speed The speed at which the user moves.
 * @param rotationOptions Options that control the rotation of the user. Set to `false` to disable.
 * @param rotationOptions.deadZone How far the joystick must be pushed to trigger a turn.
 * @param rotationOptions.type Controls how rotation using the controller functions. Can be either 'smooth' or 'snap'.
 * @param rotationOptions.degrees If `type` is 'snap', this specifies the number of degrees to snap the user's view by.
 * @param rotationOptions.speed If `type` is 'smooth', this specifies the speed at which the user's view rotates.
 * @param translationControllerHand Specifies which hand will control the translation. Can be either 'left' or 'right'.
 */
export declare function createXRControllerLocomotionUpdate(): <T extends Array<any>>(target: Object3D | undefined | null | ((velocity: Vector3, rotationVelocityY: number, ...params: T) => void), store: XRStore<any>, camera: Camera, delta: number, translationOptions?: XRControllerLocomotionTranslationOptions, rotationOptions?: XRControllerLocomotionRotationOptions, translationControllerHand?: Exclude<XRHandedness, "none">, ...params: T) => void;
