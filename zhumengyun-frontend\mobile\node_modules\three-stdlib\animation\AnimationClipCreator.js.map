{"version": 3, "file": "AnimationClipCreator.js", "sources": ["../../src/animation/AnimationClipCreator.js"], "sourcesContent": ["import {\n  AnimationC<PERSON>,\n  BooleanKeyframeTrack,\n  ColorKeyframeTrack,\n  NumberKeyframeTrack,\n  Vector3,\n  VectorKeyframeTrack,\n} from 'three'\n\nconst AnimationClipCreator = {\n  CreateRotationAnimation(period, axis = 'x') {\n    const times = [0, period],\n      values = [0, 360]\n\n    const trackName = '.rotation[' + axis + ']'\n\n    const track = new NumberKeyframeTrack(trackName, times, values)\n\n    return new AnimationClip(null, period, [track])\n  },\n\n  CreateScaleAxisAnimation(period, axis = 'x') {\n    const times = [0, period],\n      values = [0, 1]\n\n    const trackName = '.scale[' + axis + ']'\n\n    const track = new NumberKeyframeTrack(trackName, times, values)\n\n    return new AnimationClip(null, period, [track])\n  },\n\n  CreateShakeAnimation(duration, shakeScale) {\n    const times = [],\n      values = [],\n      tmp = new Vector3()\n\n    for (let i = 0; i < duration * 10; i++) {\n      times.push(i / 10)\n\n      tmp\n        .set(Math.random() * 2.0 - 1.0, Math.random() * 2.0 - 1.0, Math.random() * 2.0 - 1.0)\n        .multiply(shakeScale)\n        .toArray(values, values.length)\n    }\n\n    const trackName = '.position'\n\n    const track = new VectorKeyframeTrack(trackName, times, values)\n\n    return new AnimationClip(null, duration, [track])\n  },\n\n  CreatePulsationAnimation(duration, pulseScale) {\n    const times = [],\n      values = [],\n      tmp = new Vector3()\n\n    for (let i = 0; i < duration * 10; i++) {\n      times.push(i / 10)\n\n      const scaleFactor = Math.random() * pulseScale\n      tmp.set(scaleFactor, scaleFactor, scaleFactor).toArray(values, values.length)\n    }\n\n    const trackName = '.scale'\n\n    const track = new VectorKeyframeTrack(trackName, times, values)\n\n    return new AnimationClip(null, duration, [track])\n  },\n\n  CreateVisibilityAnimation(duration) {\n    const times = [0, duration / 2, duration],\n      values = [true, false, true]\n\n    const trackName = '.visible'\n\n    const track = new BooleanKeyframeTrack(trackName, times, values)\n\n    return new AnimationClip(null, duration, [track])\n  },\n\n  CreateMaterialColorAnimation(duration, colors) {\n    const times = [],\n      values = [],\n      timeStep = duration / colors.length\n\n    for (let i = 0; i < colors.length; i++) {\n      times.push(i * timeStep)\n\n      const color = colors[i]\n      values.push(color.r, color.g, color.b)\n    }\n\n    const trackName = '.material.color'\n\n    const track = new ColorKeyframeTrack(trackName, times, values)\n\n    return new AnimationClip(null, duration, [track])\n  },\n}\n\nexport { AnimationClipCreator }\n"], "names": [], "mappings": ";AASK,MAAC,uBAAuB;AAAA,EAC3B,wBAAwB,QAAQ,OAAO,KAAK;AAC1C,UAAM,QAAQ,CAAC,GAAG,MAAM,GACtB,SAAS,CAAC,GAAG,GAAG;AAElB,UAAM,YAAY,eAAe,OAAO;AAExC,UAAM,QAAQ,IAAI,oBAAoB,WAAW,OAAO,MAAM;AAE9D,WAAO,IAAI,cAAc,MAAM,QAAQ,CAAC,KAAK,CAAC;AAAA,EAC/C;AAAA,EAED,yBAAyB,QAAQ,OAAO,KAAK;AAC3C,UAAM,QAAQ,CAAC,GAAG,MAAM,GACtB,SAAS,CAAC,GAAG,CAAC;AAEhB,UAAM,YAAY,YAAY,OAAO;AAErC,UAAM,QAAQ,IAAI,oBAAoB,WAAW,OAAO,MAAM;AAE9D,WAAO,IAAI,cAAc,MAAM,QAAQ,CAAC,KAAK,CAAC;AAAA,EAC/C;AAAA,EAED,qBAAqB,UAAU,YAAY;AACzC,UAAM,QAAQ,CAAE,GACd,SAAS,CAAE,GACX,MAAM,IAAI,QAAS;AAErB,aAAS,IAAI,GAAG,IAAI,WAAW,IAAI,KAAK;AACtC,YAAM,KAAK,IAAI,EAAE;AAEjB,UACG,IAAI,KAAK,OAAM,IAAK,IAAM,GAAK,KAAK,OAAQ,IAAG,IAAM,GAAK,KAAK,OAAM,IAAK,IAAM,CAAG,EACnF,SAAS,UAAU,EACnB,QAAQ,QAAQ,OAAO,MAAM;AAAA,IACjC;AAED,UAAM,YAAY;AAElB,UAAM,QAAQ,IAAI,oBAAoB,WAAW,OAAO,MAAM;AAE9D,WAAO,IAAI,cAAc,MAAM,UAAU,CAAC,KAAK,CAAC;AAAA,EACjD;AAAA,EAED,yBAAyB,UAAU,YAAY;AAC7C,UAAM,QAAQ,CAAE,GACd,SAAS,CAAE,GACX,MAAM,IAAI,QAAS;AAErB,aAAS,IAAI,GAAG,IAAI,WAAW,IAAI,KAAK;AACtC,YAAM,KAAK,IAAI,EAAE;AAEjB,YAAM,cAAc,KAAK,OAAM,IAAK;AACpC,UAAI,IAAI,aAAa,aAAa,WAAW,EAAE,QAAQ,QAAQ,OAAO,MAAM;AAAA,IAC7E;AAED,UAAM,YAAY;AAElB,UAAM,QAAQ,IAAI,oBAAoB,WAAW,OAAO,MAAM;AAE9D,WAAO,IAAI,cAAc,MAAM,UAAU,CAAC,KAAK,CAAC;AAAA,EACjD;AAAA,EAED,0BAA0B,UAAU;AAClC,UAAM,QAAQ,CAAC,GAAG,WAAW,GAAG,QAAQ,GACtC,SAAS,CAAC,MAAM,OAAO,IAAI;AAE7B,UAAM,YAAY;AAElB,UAAM,QAAQ,IAAI,qBAAqB,WAAW,OAAO,MAAM;AAE/D,WAAO,IAAI,cAAc,MAAM,UAAU,CAAC,KAAK,CAAC;AAAA,EACjD;AAAA,EAED,6BAA6B,UAAU,QAAQ;AAC7C,UAAM,QAAQ,CAAE,GACd,SAAS,CAAE,GACX,WAAW,WAAW,OAAO;AAE/B,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,KAAK,IAAI,QAAQ;AAEvB,YAAM,QAAQ,OAAO,CAAC;AACtB,aAAO,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AAAA,IACtC;AAED,UAAM,YAAY;AAElB,UAAM,QAAQ,IAAI,mBAAmB,WAAW,OAAO,MAAM;AAE7D,WAAO,IAAI,cAAc,MAAM,UAAU,CAAC,KAAK,CAAC;AAAA,EACjD;AACH;"}