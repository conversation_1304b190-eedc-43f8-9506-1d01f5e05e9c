{"version": 3, "file": "Lut.js", "sources": ["../../src/math/Lut.js"], "sourcesContent": ["import { Color, MathUtils } from 'three'\n\nclass Lut {\n  constructor(colormap, count = 32) {\n    this.isLut = true\n\n    this.lut = []\n    this.map = []\n    this.n = 0\n    this.minV = 0\n    this.maxV = 1\n\n    this.setColorMap(colormap, count)\n  }\n\n  set(value) {\n    if (value.isLut === true) {\n      this.copy(value)\n    }\n\n    return this\n  }\n\n  setMin(min) {\n    this.minV = min\n\n    return this\n  }\n\n  setMax(max) {\n    this.maxV = max\n\n    return this\n  }\n\n  setColorMap(colormap, count = 32) {\n    this.map = ColorMapKeywords[colormap] || ColorMapKeywords.rainbow\n    this.n = count\n\n    const step = 1.0 / this.n\n    const minColor = new Color()\n    const maxColor = new Color()\n\n    this.lut.length = 0\n\n    // sample at 0\n\n    this.lut.push(new Color(this.map[0][1]))\n\n    // sample at 1/n, ..., (n-1)/n\n\n    for (let i = 1; i < count; i++) {\n      const alpha = i * step\n\n      for (let j = 0; j < this.map.length - 1; j++) {\n        if (alpha > this.map[j][0] && alpha <= this.map[j + 1][0]) {\n          const min = this.map[j][0]\n          const max = this.map[j + 1][0]\n\n          minColor.setHex(this.map[j][1], 'srgb-linear')\n          maxColor.setHex(this.map[j + 1][1], 'srgb-linear')\n\n          const color = new Color().lerpColors(minColor, maxColor, (alpha - min) / (max - min))\n\n          this.lut.push(color)\n        }\n      }\n    }\n\n    // sample at 1\n\n    this.lut.push(new Color(this.map[this.map.length - 1][1]))\n\n    return this\n  }\n\n  copy(lut) {\n    this.lut = lut.lut\n    this.map = lut.map\n    this.n = lut.n\n    this.minV = lut.minV\n    this.maxV = lut.maxV\n\n    return this\n  }\n\n  getColor(alpha) {\n    alpha = MathUtils.clamp(alpha, this.minV, this.maxV)\n\n    alpha = (alpha - this.minV) / (this.maxV - this.minV)\n\n    const colorPosition = Math.round(alpha * this.n)\n\n    return this.lut[colorPosition]\n  }\n\n  addColorMap(name, arrayOfColors) {\n    ColorMapKeywords[name] = arrayOfColors\n\n    return this\n  }\n\n  createCanvas() {\n    const canvas = document.createElement('canvas')\n    canvas.width = 1\n    canvas.height = this.n\n\n    this.updateCanvas(canvas)\n\n    return canvas\n  }\n\n  updateCanvas(canvas) {\n    const ctx = canvas.getContext('2d', { alpha: false })\n\n    const imageData = ctx.getImageData(0, 0, 1, this.n)\n\n    const data = imageData.data\n\n    let k = 0\n\n    const step = 1.0 / this.n\n\n    const minColor = new Color()\n    const maxColor = new Color()\n    const finalColor = new Color()\n\n    for (let i = 1; i >= 0; i -= step) {\n      for (let j = this.map.length - 1; j >= 0; j--) {\n        if (i < this.map[j][0] && i >= this.map[j - 1][0]) {\n          const min = this.map[j - 1][0]\n          const max = this.map[j][0]\n\n          minColor.setHex(this.map[j - 1][1], 'srgb-linear')\n          maxColor.setHex(this.map[j][1], 'srgb-linear')\n\n          finalColor.lerpColors(minColor, maxColor, (i - min) / (max - min))\n\n          data[k * 4] = Math.round(finalColor.r * 255)\n          data[k * 4 + 1] = Math.round(finalColor.g * 255)\n          data[k * 4 + 2] = Math.round(finalColor.b * 255)\n          data[k * 4 + 3] = 255\n\n          k += 1\n        }\n      }\n    }\n\n    ctx.putImageData(imageData, 0, 0)\n\n    return canvas\n  }\n}\n\nconst ColorMapKeywords = {\n  rainbow: [\n    [0.0, 0x0000ff],\n    [0.2, 0x00ffff],\n    [0.5, 0x00ff00],\n    [0.8, 0xffff00],\n    [1.0, 0xff0000],\n  ],\n  cooltowarm: [\n    [0.0, 0x3c4ec2],\n    [0.2, 0x9bbcff],\n    [0.5, 0xdcdcdc],\n    [0.8, 0xf6a385],\n    [1.0, 0xb40426],\n  ],\n  blackbody: [\n    [0.0, 0x000000],\n    [0.2, 0x780000],\n    [0.5, 0xe63200],\n    [0.8, 0xffff00],\n    [1.0, 0xffffff],\n  ],\n  grayscale: [\n    [0.0, 0x000000],\n    [0.2, 0x404040],\n    [0.5, 0x7f7f80],\n    [0.8, 0xbfbfbf],\n    [1.0, 0xffffff],\n  ],\n}\n\nexport { Lut, ColorMapKeywords }\n"], "names": [], "mappings": ";AAEA,MAAM,IAAI;AAAA,EACR,YAAY,UAAU,QAAQ,IAAI;AAChC,SAAK,QAAQ;AAEb,SAAK,MAAM,CAAE;AACb,SAAK,MAAM,CAAE;AACb,SAAK,IAAI;AACT,SAAK,OAAO;AACZ,SAAK,OAAO;AAEZ,SAAK,YAAY,UAAU,KAAK;AAAA,EACjC;AAAA,EAED,IAAI,OAAO;AACT,QAAI,MAAM,UAAU,MAAM;AACxB,WAAK,KAAK,KAAK;AAAA,IAChB;AAED,WAAO;AAAA,EACR;AAAA,EAED,OAAO,KAAK;AACV,SAAK,OAAO;AAEZ,WAAO;AAAA,EACR;AAAA,EAED,OAAO,KAAK;AACV,SAAK,OAAO;AAEZ,WAAO;AAAA,EACR;AAAA,EAED,YAAY,UAAU,QAAQ,IAAI;AAChC,SAAK,MAAM,iBAAiB,QAAQ,KAAK,iBAAiB;AAC1D,SAAK,IAAI;AAET,UAAM,OAAO,IAAM,KAAK;AACxB,UAAM,WAAW,IAAI,MAAO;AAC5B,UAAM,WAAW,IAAI,MAAO;AAE5B,SAAK,IAAI,SAAS;AAIlB,SAAK,IAAI,KAAK,IAAI,MAAM,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAIvC,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,YAAM,QAAQ,IAAI;AAElB,eAAS,IAAI,GAAG,IAAI,KAAK,IAAI,SAAS,GAAG,KAAK;AAC5C,YAAI,QAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,KAAK,SAAS,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG;AACzD,gBAAM,MAAM,KAAK,IAAI,CAAC,EAAE,CAAC;AACzB,gBAAM,MAAM,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC;AAE7B,mBAAS,OAAO,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,aAAa;AAC7C,mBAAS,OAAO,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG,aAAa;AAEjD,gBAAM,QAAQ,IAAI,MAAO,EAAC,WAAW,UAAU,WAAW,QAAQ,QAAQ,MAAM,IAAI;AAEpF,eAAK,IAAI,KAAK,KAAK;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAID,SAAK,IAAI,KAAK,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;AAEzD,WAAO;AAAA,EACR;AAAA,EAED,KAAK,KAAK;AACR,SAAK,MAAM,IAAI;AACf,SAAK,MAAM,IAAI;AACf,SAAK,IAAI,IAAI;AACb,SAAK,OAAO,IAAI;AAChB,SAAK,OAAO,IAAI;AAEhB,WAAO;AAAA,EACR;AAAA,EAED,SAAS,OAAO;AACd,YAAQ,UAAU,MAAM,OAAO,KAAK,MAAM,KAAK,IAAI;AAEnD,aAAS,QAAQ,KAAK,SAAS,KAAK,OAAO,KAAK;AAEhD,UAAM,gBAAgB,KAAK,MAAM,QAAQ,KAAK,CAAC;AAE/C,WAAO,KAAK,IAAI,aAAa;AAAA,EAC9B;AAAA,EAED,YAAY,MAAM,eAAe;AAC/B,qBAAiB,IAAI,IAAI;AAEzB,WAAO;AAAA,EACR;AAAA,EAED,eAAe;AACb,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,WAAO,QAAQ;AACf,WAAO,SAAS,KAAK;AAErB,SAAK,aAAa,MAAM;AAExB,WAAO;AAAA,EACR;AAAA,EAED,aAAa,QAAQ;AACnB,UAAM,MAAM,OAAO,WAAW,MAAM,EAAE,OAAO,OAAO;AAEpD,UAAM,YAAY,IAAI,aAAa,GAAG,GAAG,GAAG,KAAK,CAAC;AAElD,UAAM,OAAO,UAAU;AAEvB,QAAI,IAAI;AAER,UAAM,OAAO,IAAM,KAAK;AAExB,UAAM,WAAW,IAAI,MAAO;AAC5B,UAAM,WAAW,IAAI,MAAO;AAC5B,UAAM,aAAa,IAAI,MAAO;AAE9B,aAAS,IAAI,GAAG,KAAK,GAAG,KAAK,MAAM;AACjC,eAAS,IAAI,KAAK,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,YAAI,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG;AACjD,gBAAM,MAAM,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC;AAC7B,gBAAM,MAAM,KAAK,IAAI,CAAC,EAAE,CAAC;AAEzB,mBAAS,OAAO,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG,aAAa;AACjD,mBAAS,OAAO,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,aAAa;AAE7C,qBAAW,WAAW,UAAU,WAAW,IAAI,QAAQ,MAAM,IAAI;AAEjE,eAAK,IAAI,CAAC,IAAI,KAAK,MAAM,WAAW,IAAI,GAAG;AAC3C,eAAK,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,WAAW,IAAI,GAAG;AAC/C,eAAK,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,WAAW,IAAI,GAAG;AAC/C,eAAK,IAAI,IAAI,CAAC,IAAI;AAElB,eAAK;AAAA,QACN;AAAA,MACF;AAAA,IACF;AAED,QAAI,aAAa,WAAW,GAAG,CAAC;AAEhC,WAAO;AAAA,EACR;AACH;AAEK,MAAC,mBAAmB;AAAA,EACvB,SAAS;AAAA,IACP,CAAC,GAAK,GAAQ;AAAA,IACd,CAAC,KAAK,KAAQ;AAAA,IACd,CAAC,KAAK,KAAQ;AAAA,IACd,CAAC,KAAK,QAAQ;AAAA,IACd,CAAC,GAAK,QAAQ;AAAA,EACf;AAAA,EACD,YAAY;AAAA,IACV,CAAC,GAAK,OAAQ;AAAA,IACd,CAAC,KAAK,QAAQ;AAAA,IACd,CAAC,KAAK,QAAQ;AAAA,IACd,CAAC,KAAK,QAAQ;AAAA,IACd,CAAC,GAAK,QAAQ;AAAA,EACf;AAAA,EACD,WAAW;AAAA,IACT,CAAC,GAAK,CAAQ;AAAA,IACd,CAAC,KAAK,OAAQ;AAAA,IACd,CAAC,KAAK,QAAQ;AAAA,IACd,CAAC,KAAK,QAAQ;AAAA,IACd,CAAC,GAAK,QAAQ;AAAA,EACf;AAAA,EACD,WAAW;AAAA,IACT,CAAC,GAAK,CAAQ;AAAA,IACd,CAAC,KAAK,OAAQ;AAAA,IACd,CAAC,KAAK,OAAQ;AAAA,IACd,CAAC,KAAK,QAAQ;AAAA,IACd,CAAC,GAAK,QAAQ;AAAA,EACf;AACH;"}