import { PointerEvent } from '@pmndrs/pointer-events';
import { XRControllerState, XRGazeState, XRHandState, XRScreenInputState, XRTransientPointerState } from '@pmndrs/xr';
import { RefObject } from 'react';
import { Group, Intersection, XRControllerEventType as ThreeXRControllerEventType } from 'three';
import { useXRSpace } from '../space.js';
declare const eventTranslations: {
    onBlur: string;
    onHover: string;
    onMove: string;
    onSelect: {
        type: string;
        filter: (e: PointerEvent<globalThis.PointerEvent>) => boolean;
    };
    onSelectEnd: {
        type: string;
        filter: (e: PointerEvent<globalThis.PointerEvent>) => boolean;
    };
    onSelectStart: {
        type: string;
        filter: (e: PointerEvent<globalThis.PointerEvent>) => boolean;
    };
    onSqueeze: {
        type: string;
        filter: (e: PointerEvent<globalThis.PointerEvent>) => boolean;
    };
    onSqueezeEnd: {
        type: string;
        filter: (e: PointerEvent<globalThis.PointerEvent>) => boolean;
    };
    onSqueezeStart: {
        type: string;
        filter: (e: PointerEvent<globalThis.PointerEvent>) => boolean;
    };
};
/**
 * @deprecated Use normal react-three/fiber event listeners instead (e.g. `<mesh onClick={...} />`)
 */
export declare function useInteraction(ref: RefObject<Group | null>, type: keyof typeof eventTranslations, handler?: (event: {
    intersection: Intersection;
    intersections: Array<Intersection>;
    target: any;
}) => void): void;
/**
 * @deprecated Implement custom listeners instead
 */
export declare function useXREvent(type: Exclude<ThreeXRControllerEventType, XRSessionEventType | 'connected' | 'disconnected'>, handler: (event: {
    type: Exclude<ThreeXRControllerEventType, XRSessionEventType | 'connected' | 'disconnected'>;
    data: XRInputSource;
}) => void, { handedness }?: {
    handedness?: XRHandedness;
}): void;
/**
 * Hook for getting the transient-pointer state
 *
 * @param handedness the handedness that the XRHandState should have
 * @deprecated use `useXRInputSourceState("transientPointer", "left")` instead
 */
export declare function useXRTransientPointerState(handedness: XRHandedness): XRTransientPointerState | undefined;
/**
 * Hook for getting the transient-pointer state inside the xr store config
 *
 * @deprecated use `useXRInputSourceStateContext("transientPointer")` instead
 */
export declare function useXRTransientPointerState(): XRTransientPointerState;
/**
 * Hook for getting the gaze state
 *
 * @deprecated use `useXRInputSourceStateContext("gaze")` instead
 */
export declare function useXRGazeState(): XRGazeState;
/**
 * Hook for getting the screen-input state
 *
 * @deprecated `useXRInputSourceStateContext("screenInput")` instead
 */
export declare function useXRScreenInputState(): XRScreenInputState;
/**
 * Hook for getting the XRHandState
 *
 * @param handedness the handedness that the XRHandState should have
 * @deprecated use `useXRInputSourceState("hand", "left")` instead
 */
export declare function useXRHandState(handedness: XRHandedness): XRHandState | undefined;
/**
 * Hook for getting the XRHandState
 *
 * @deprecated `useXRInputSourceStateContext("hand")` instead
 */
export declare function useXRHandState(): XRHandState;
/**
 * Hook for getting the XRControllerState
 *
 * @param handedness the handedness that the XRControllerState should have
 * @deprecated use `useXRInputSourceState("controller", "left")` instead
 */
export declare function useXRControllerState(handedness: XRHandedness): XRControllerState | undefined;
/**
 * Hook for getting the XRControllerState
 *
 * @deprecated `useXRInputSourceStateContext("controller")` instead
 */
export declare function useXRControllerState(): XRControllerState;
/**
 * @deprecated use `useXRSpace` instead
 */
export declare const useXRReferenceSpace: typeof useXRSpace;
export {};
