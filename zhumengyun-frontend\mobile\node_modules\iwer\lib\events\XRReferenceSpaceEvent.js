/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
export class XRReferenceSpaceEvent extends Event {
    constructor(type, eventInitDict) {
        super(type, eventInitDict);
        if (!eventInitDict.referenceSpace) {
            throw new Error('XRReferenceSpaceEventInit.referenceSpace is required');
        }
        this.referenceSpace = eventInitDict.referenceSpace;
        this.transform = eventInitDict.transform;
    }
}
//# sourceMappingURL=XRReferenceSpaceEvent.js.map