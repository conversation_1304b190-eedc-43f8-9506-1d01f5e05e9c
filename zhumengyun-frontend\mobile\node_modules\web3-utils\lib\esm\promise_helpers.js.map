{"version": 3, "file": "promise_helpers.js", "sourceRoot": "", "sources": ["../../src/promise_helpers.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;;;;;;;;;;AAEF,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAK3C;;;;GAIG;AACH,MAAM,UAAU,SAAS,CAAC,MAAe;IACxC,OAAO,CACN,CAAC,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,UAAU,CAAC;QAC5D,sEAAsE;QACtE,OAAQ,MAA4B,CAAC,IAAI,KAAK,UAAU,CACxD,CAAC;AACH,CAAC;AAcD;;;;;;GAMG;AACH,MAAM,UAAgB,eAAe,CACpC,SAAwC,EACxC,OAAe,EACf,KAAa;;QAEb,IAAI,SAA8B,CAAC;QACnC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;YACjC,SAAS,YAAY,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,EAAE;YACtD,IAAI,OAAO,CAAoB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAClD,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YACrF,CAAC,CAAC;SACF,CAAC,CAAC;QACH,IAAI,SAAS,EAAE,CAAC;YACf,YAAY,CAAC,SAAS,CAAC,CAAC;QACzB,CAAC;QACD,IAAI,MAAM,YAAY,KAAK,EAAE,CAAC;YAC7B,MAAM,MAAM,CAAC;QACd,CAAC;QACD,OAAO,MAAM,CAAC;IACf,CAAC;CAAA;AAED;;;;;GAKG;AACH,MAAM,UAAU,kCAAkC,CACjD,IAAsB,EACtB,QAAgB;IAEhB,IAAI,UAA6B,CAAC;IAClC,MAAM,SAAS,GAAG,IAAI,OAAO,CAAwB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACxE,UAAU,GAAG,WAAW,CACvB,CAAC,SAAS,oBAAoB;YAC7B,CAAC,GAAS,EAAE;gBACX,IAAI,CAAC;oBACJ,MAAM,GAAG,GAAG,MAAM,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAElD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;wBACrB,aAAa,CAAC,UAAU,CAAC,CAAC;wBAC1B,OAAO,CAAC,GAAuC,CAAC,CAAC;oBAClD,CAAC;gBACF,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBAChB,aAAa,CAAC,UAAU,CAAC,CAAC;oBAC1B,MAAM,CAAC,KAAK,CAAC,CAAC;gBACf,CAAC;YACF,CAAC,CAAA,CAAC,EAAa,CAAC;YAChB,OAAO,oBAAoB,CAAC;QAC7B,CAAC,CAAC,EAAE,EAAE,wCAAwC;QAC9C,QAAQ,CACR,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,SAAsD,EAAE,UAAW,CAAC,CAAC;AAC9E,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAgB,eAAe,CACpC,IAAsB,EACtB,QAAgB;;QAEhB,OAAO,kCAAkC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC;CAAA;AACD;;;;;;;;;;GAUG;AACH,MAAM,UAAU,eAAe,CAAC,OAAe,EAAE,KAAY;IAC5D,IAAI,SAA4B,CAAC;IACjC,MAAM,eAAe,GAAG,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;QACxD,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;YAC3B,MAAM,CAAC,KAAK,CAAC,CAAC;QACf,CAAC,EAAE,OAAO,CAAC,CAAC;IACb,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,SAAU,EAAE,eAAe,CAAC,CAAC;AACtC,CAAC;AACD;;;;;;GAMG;AACH,MAAM,UAAU,2BAA2B,CAC1C,IAAkC,EAClC,QAAgB;IAEhB,IAAI,UAA6B,CAAC;IAClC,MAAM,iBAAiB,GAAG,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;QAC1D,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE;YAC7B,CAAC,GAAS,EAAE;gBACX,MAAM,KAAK,GAAG,MAAM,IAAI,EAAE,CAAC;gBAC3B,IAAI,KAAK,EAAE,CAAC;oBACX,aAAa,CAAC,UAAU,CAAC,CAAC;oBAC1B,MAAM,CAAC,KAAK,CAAC,CAAC;gBACf,CAAC;YACF,CAAC,CAAA,CAAC,EAAa,CAAC;QACjB,CAAC,EAAE,QAAQ,CAAC,CAAC;IACd,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,UAAW,EAAE,iBAAiB,CAAC,CAAC;AACzC,CAAC"}