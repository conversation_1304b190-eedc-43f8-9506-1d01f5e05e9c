{"version": 3, "file": "InteractiveGroup.js", "sources": ["../../src/interactive/InteractiveGroup.js"], "sourcesContent": ["import { Group, Matrix4, Ray<PERSON>, Vector2 } from 'three'\n\nconst _pointer = /* @__PURE__ */ new Vector2()\nconst _event = { type: '', data: _pointer }\n\nclass InteractiveGroup extends Group {\n  constructor(renderer, camera) {\n    super()\n\n    const scope = this\n\n    const raycaster = new Raycaster()\n    const tempMatrix = new Matrix4()\n\n    // Pointer Events\n\n    const element = renderer.domElement\n\n    function onPointerEvent(event) {\n      event.stopPropagation()\n\n      _pointer.x = (event.clientX / element.clientWidth) * 2 - 1\n      _pointer.y = -(event.clientY / element.clientHeight) * 2 + 1\n\n      raycaster.setFromCamera(_pointer, camera)\n\n      const intersects = raycaster.intersectObjects(scope.children, false)\n\n      if (intersects.length > 0) {\n        const intersection = intersects[0]\n\n        const object = intersection.object\n        const uv = intersection.uv\n\n        _event.type = event.type\n        _event.data.set(uv.x, 1 - uv.y)\n\n        object.dispatchEvent(_event)\n      }\n    }\n\n    element.addEventListener('pointerdown', onPointerEvent)\n    element.addEventListener('pointerup', onPointerEvent)\n    element.addEventListener('pointermove', onPointerEvent)\n    element.addEventListener('mousedown', onPointerEvent)\n    element.addEventListener('mouseup', onPointerEvent)\n    element.addEventListener('mousemove', onPointerEvent)\n    element.addEventListener('click', onPointerEvent)\n\n    // WebXR Controller Events\n    // TODO: Dispatch pointerevents too\n\n    const events = {\n      move: 'mousemove',\n      select: 'click',\n      selectstart: 'mousedown',\n      selectend: 'mouseup',\n    }\n\n    function onXRControllerEvent(event) {\n      const controller = event.target\n\n      tempMatrix.identity().extractRotation(controller.matrixWorld)\n\n      raycaster.ray.origin.setFromMatrixPosition(controller.matrixWorld)\n      raycaster.ray.direction.set(0, 0, -1).applyMatrix4(tempMatrix)\n\n      const intersections = raycaster.intersectObjects(scope.children, false)\n\n      if (intersections.length > 0) {\n        const intersection = intersections[0]\n\n        const object = intersection.object\n        const uv = intersection.uv\n\n        _event.type = events[event.type]\n        _event.data.set(uv.x, 1 - uv.y)\n\n        object.dispatchEvent(_event)\n      }\n    }\n\n    const controller1 = renderer.xr.getController(0)\n    controller1.addEventListener('move', onXRControllerEvent)\n    controller1.addEventListener('select', onXRControllerEvent)\n    controller1.addEventListener('selectstart', onXRControllerEvent)\n    controller1.addEventListener('selectend', onXRControllerEvent)\n\n    const controller2 = renderer.xr.getController(1)\n    controller2.addEventListener('move', onXRControllerEvent)\n    controller2.addEventListener('select', onXRControllerEvent)\n    controller2.addEventListener('selectstart', onXRControllerEvent)\n    controller2.addEventListener('selectend', onXRControllerEvent)\n  }\n}\n\nexport { InteractiveGroup }\n"], "names": [], "mappings": ";AAEA,MAAM,WAA2B,oBAAI,QAAS;AAC9C,MAAM,SAAS,EAAE,MAAM,IAAI,MAAM,SAAU;AAE3C,MAAM,yBAAyB,MAAM;AAAA,EACnC,YAAY,UAAU,QAAQ;AAC5B,UAAO;AAEP,UAAM,QAAQ;AAEd,UAAM,YAAY,IAAI,UAAW;AACjC,UAAM,aAAa,IAAI,QAAS;AAIhC,UAAM,UAAU,SAAS;AAEzB,aAAS,eAAe,OAAO;AAC7B,YAAM,gBAAiB;AAEvB,eAAS,IAAK,MAAM,UAAU,QAAQ,cAAe,IAAI;AACzD,eAAS,IAAI,EAAE,MAAM,UAAU,QAAQ,gBAAgB,IAAI;AAE3D,gBAAU,cAAc,UAAU,MAAM;AAExC,YAAM,aAAa,UAAU,iBAAiB,MAAM,UAAU,KAAK;AAEnE,UAAI,WAAW,SAAS,GAAG;AACzB,cAAM,eAAe,WAAW,CAAC;AAEjC,cAAM,SAAS,aAAa;AAC5B,cAAM,KAAK,aAAa;AAExB,eAAO,OAAO,MAAM;AACpB,eAAO,KAAK,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC;AAE9B,eAAO,cAAc,MAAM;AAAA,MAC5B;AAAA,IACF;AAED,YAAQ,iBAAiB,eAAe,cAAc;AACtD,YAAQ,iBAAiB,aAAa,cAAc;AACpD,YAAQ,iBAAiB,eAAe,cAAc;AACtD,YAAQ,iBAAiB,aAAa,cAAc;AACpD,YAAQ,iBAAiB,WAAW,cAAc;AAClD,YAAQ,iBAAiB,aAAa,cAAc;AACpD,YAAQ,iBAAiB,SAAS,cAAc;AAKhD,UAAM,SAAS;AAAA,MACb,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,WAAW;AAAA,IACZ;AAED,aAAS,oBAAoB,OAAO;AAClC,YAAM,aAAa,MAAM;AAEzB,iBAAW,SAAU,EAAC,gBAAgB,WAAW,WAAW;AAE5D,gBAAU,IAAI,OAAO,sBAAsB,WAAW,WAAW;AACjE,gBAAU,IAAI,UAAU,IAAI,GAAG,GAAG,EAAE,EAAE,aAAa,UAAU;AAE7D,YAAM,gBAAgB,UAAU,iBAAiB,MAAM,UAAU,KAAK;AAEtE,UAAI,cAAc,SAAS,GAAG;AAC5B,cAAM,eAAe,cAAc,CAAC;AAEpC,cAAM,SAAS,aAAa;AAC5B,cAAM,KAAK,aAAa;AAExB,eAAO,OAAO,OAAO,MAAM,IAAI;AAC/B,eAAO,KAAK,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC;AAE9B,eAAO,cAAc,MAAM;AAAA,MAC5B;AAAA,IACF;AAED,UAAM,cAAc,SAAS,GAAG,cAAc,CAAC;AAC/C,gBAAY,iBAAiB,QAAQ,mBAAmB;AACxD,gBAAY,iBAAiB,UAAU,mBAAmB;AAC1D,gBAAY,iBAAiB,eAAe,mBAAmB;AAC/D,gBAAY,iBAAiB,aAAa,mBAAmB;AAE7D,UAAM,cAAc,SAAS,GAAG,cAAc,CAAC;AAC/C,gBAAY,iBAAiB,QAAQ,mBAAmB;AACxD,gBAAY,iBAAiB,UAAU,mBAAmB;AAC1D,gBAAY,iBAAiB,eAAe,mBAAmB;AAC/D,gBAAY,iBAAiB,aAAa,mBAAmB;AAAA,EAC9D;AACH;"}