/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { Gamepad } from '../gamepad/Gamepad.js';
import { P_INPUT_SOURCE } from '../private.js';
import { XRHand } from './XRHand.js';
import { XRSpace } from '../spaces/XRSpace.js';
export declare enum XRHandedness {
    None = "none",
    Left = "left",
    Right = "right"
}
export declare enum XRTargetRayMode {
    Gaze = "gaze",
    TrackedPointer = "tracked-pointer",
    Screen = "screen",
    TransientPointer = "transient-pointer"
}
export declare class XRInputSourceArray extends Array<XRInputSource> {
}
export declare class XRInputSource {
    [P_INPUT_SOURCE]: {
        handedness: XRHandedness;
        targetRayMode: XRTargetRayMode;
        targetRaySpace: XRSpace;
        gripSpace?: XRSpace;
        profiles: Array<string>;
        gamepad?: Gamepad;
        hand?: XRHand;
    };
    constructor(handedness: XRHandedness, targetRayMode: XRTargetRayMode, profiles: string[], targetRaySpace: XRSpace, gamepad?: Gamepad, gripSpace?: XRSpace, hand?: XRHand);
    get handedness(): XRHandedness;
    get targetRayMode(): XRTargetRayMode;
    get targetRaySpace(): XRSpace;
    get gripSpace(): XRSpace | undefined;
    get profiles(): string[];
    get gamepad(): Gamepad | undefined;
    get hand(): XRHand | undefined;
}
//# sourceMappingURL=XRInputSource.d.ts.map