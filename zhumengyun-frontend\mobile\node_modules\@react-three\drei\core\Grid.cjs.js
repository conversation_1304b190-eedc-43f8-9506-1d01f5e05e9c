"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),n=require("react"),o=require("three"),i=require("@react-three/fiber"),r=require("./shaderMaterial.cjs.js"),t=require("../helpers/constants.cjs.js");function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function a(e){if(e&&e.__esModule)return e;var n=Object.create(null);return e&&Object.keys(e).forEach((function(o){if("default"!==o){var i=Object.getOwnPropertyDescriptor(e,o);Object.defineProperty(n,o,i.get?i:{enumerable:!0,get:function(){return e[o]}})}})),n.default=e,Object.freeze(n)}var s=l(e),c=a(n),f=a(o);const d=r.shaderMaterial({cellSize:.5,sectionSize:1,fadeDistance:100,fadeStrength:1,fadeFrom:1,cellThickness:.5,sectionThickness:1,cellColor:new f.Color,sectionColor:new f.Color,infiniteGrid:!1,followCamera:!1,worldCamProjPosition:new f.Vector3,worldPlanePosition:new f.Vector3},"\n    varying vec3 localPosition;\n    varying vec4 worldPosition;\n\n    uniform vec3 worldCamProjPosition;\n    uniform vec3 worldPlanePosition;\n    uniform float fadeDistance;\n    uniform bool infiniteGrid;\n    uniform bool followCamera;\n\n    void main() {\n      localPosition = position.xzy;\n      if (infiniteGrid) localPosition *= 1.0 + fadeDistance;\n      \n      worldPosition = modelMatrix * vec4(localPosition, 1.0);\n      if (followCamera) {\n        worldPosition.xyz += (worldCamProjPosition - worldPlanePosition);\n        localPosition = (inverse(modelMatrix) * worldPosition).xyz;\n      }\n\n      gl_Position = projectionMatrix * viewMatrix * worldPosition;\n    }\n  ",`\n    varying vec3 localPosition;\n    varying vec4 worldPosition;\n\n    uniform vec3 worldCamProjPosition;\n    uniform float cellSize;\n    uniform float sectionSize;\n    uniform vec3 cellColor;\n    uniform vec3 sectionColor;\n    uniform float fadeDistance;\n    uniform float fadeStrength;\n    uniform float fadeFrom;\n    uniform float cellThickness;\n    uniform float sectionThickness;\n\n    float getGrid(float size, float thickness) {\n      vec2 r = localPosition.xz / size;\n      vec2 grid = abs(fract(r - 0.5) - 0.5) / fwidth(r);\n      float line = min(grid.x, grid.y) + 1.0 - thickness;\n      return 1.0 - min(line, 1.0);\n    }\n\n    void main() {\n      float g1 = getGrid(cellSize, cellThickness);\n      float g2 = getGrid(sectionSize, sectionThickness);\n\n      vec3 from = worldCamProjPosition*vec3(fadeFrom);\n      float dist = distance(from, worldPosition.xyz);\n      float d = 1.0 - min(dist / fadeDistance, 1.0);\n      vec3 color = mix(cellColor, sectionColor, min(1.0, sectionThickness * g2));\n\n      gl_FragColor = vec4(color, (g1 + g2) * pow(d, fadeStrength));\n      gl_FragColor.a = mix(0.75 * gl_FragColor.a, gl_FragColor.a, g2);\n      if (gl_FragColor.a <= 0.0) discard;\n\n      #include <tonemapping_fragment>\n      #include <${t.version>=154?"colorspace_fragment":"encodings_fragment"}>\n    }\n  `),m=c.forwardRef((({args:e,cellColor:n="#000000",sectionColor:o="#2080ff",cellSize:r=.5,sectionSize:t=1,followCamera:l=!1,infiniteGrid:a=!1,fadeDistance:m=100,fadeStrength:u=1,fadeFrom:g=1,cellThickness:P=.5,sectionThickness:v=1,side:w=f.BackSide,...C},h)=>{i.extend({GridMaterial:d});const p=c.useRef(null);c.useImperativeHandle(h,(()=>p.current),[]);const x=new f.Plane,j=new f.Vector3(0,1,0),z=new f.Vector3(0,0,0);i.useFrame((e=>{x.setFromNormalAndCoplanarPoint(j,z).applyMatrix4(p.current.matrixWorld);const n=p.current.material,o=n.uniforms.worldCamProjPosition,i=n.uniforms.worldPlanePosition;x.projectPoint(e.camera.position,o.value),i.value.set(0,0,0).applyMatrix4(p.current.matrixWorld)}));const y={cellSize:r,sectionSize:t,cellColor:n,sectionColor:o,cellThickness:P,sectionThickness:v},S={fadeDistance:m,fadeStrength:u,fadeFrom:g,infiniteGrid:a,followCamera:l};return c.createElement("mesh",s.default({ref:p,frustumCulled:!1},C),c.createElement("gridMaterial",s.default({transparent:!0,"extensions-derivatives":!0,side:w},y,S)),c.createElement("planeGeometry",{args:e}))}));exports.Grid=m;
