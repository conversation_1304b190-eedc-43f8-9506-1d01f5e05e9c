/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { NativeMesh, XRMesh } from '../meshes/XRMesh.js';
import { NativePlane, XRPlane } from '../planes/XRPlane.js';
import { P_SESSION } from '../private.js';
import type { WebXRFeature, XRDevice } from '../device/XRDevice.js';
import { XRAnchor, XRAnchorSet } from '../anchors/XRAnchor.js';
import { XRHitTestOptionsInit, XRHitTestSource } from '../hittest/XRHitTest.js';
import { XRInputSource, XRInputSourceArray } from '../input/XRInputSource.js';
import { XRInputSourcesChangeEventHandler } from '../events/XRInputSourcesChangeEvent.js';
import { XRReferenceSpace, XRReferenceSpaceType } from '../spaces/XRReferenceSpace.js';
import { XRRenderState, XRRenderStateInit } from './XRRenderState.js';
import { XRSessionEventHandler } from '../events/XRSessionEvent.js';
import { XREye } from '../views/XRView.js';
import { XRFrame } from '../frameloop/XRFrame.js';
import { XRInputSourceEventHandler } from '../events/XRInputSourceEvent.js';
import { mat4 } from 'gl-matrix';
export type XRVisibilityState = 'visible' | 'visible-blurred' | 'hidden';
export type XRSessionMode = 'inline' | 'immersive-vr' | 'immersive-ar';
export type XRSessionInit = {
    requiredFeatures?: WebXRFeature[];
    optionalFeatures?: WebXRFeature[];
};
export declare enum XREnvironmentBlendMode {
    Opaque = "opaque",
    AlphaBlend = "alpha-blend",
    Additive = "additive"
}
export declare enum XRInteractionMode {
    ScreenSpace = "screen-space",
    WorldSpace = "world-space"
}
type XRFrameRequestCallback = (time: DOMHighResTimeStamp, frame: XRFrame) => void;
type CallbackData = {
    handle: number;
    callback: XRFrameRequestCallback;
    cancelled: boolean;
};
export declare class XRSession extends EventTarget {
    [P_SESSION]: {
        device: XRDevice;
        enabledFeatures: Array<string>;
        isSystemKeyboardSupported: boolean;
        mode: XRSessionMode;
        ended: boolean;
        referenceSpaceIsSupported: (referenceSpaceType: XRReferenceSpaceType) => boolean;
        projectionMatrices: {
            [key in XREye]: mat4;
        };
        getProjectionMatrix: (eye: XREye) => mat4;
        frameHandle: number;
        frameCallbacks: CallbackData[];
        currentFrameCallbacks: CallbackData[] | null;
        onDeviceFrame: () => void;
        deviceFrameHandle?: number;
        renderState: XRRenderState;
        pendingRenderState: XRRenderState | null;
        nominalFrameRate: number;
        referenceSpaces: XRReferenceSpace[];
        inputSourceArray: XRInputSourceArray;
        activeInputSources: XRInputSource[];
        updateActiveInputSources: () => void;
        trackedAnchors: XRAnchorSet;
        persistentAnchors: Map<string, XRAnchor>;
        newAnchors: Map<XRAnchor, {
            resolve: (value: XRAnchor | PromiseLike<XRAnchor>) => void;
            reject: (reason?: any) => void;
        }>;
        frameTrackedAnchors: XRAnchorSet;
        updateTrackedAnchors: () => void;
        trackedPlanes: Map<NativePlane, XRPlane>;
        updateTrackedPlanes: (frame: XRFrame) => void;
        trackedMeshes: Map<NativeMesh, XRMesh>;
        updateTrackedMeshes: (frame: XRFrame) => void;
        hitTestSources: Set<XRHitTestSource>;
        computeHitTestResults: (frame: XRFrame) => void;
        onend: XRSessionEventHandler | null;
        oninputsourceschange: XRInputSourcesChangeEventHandler | null;
        onselect: XRInputSourceEventHandler | null;
        onselectstart: XRInputSourceEventHandler | null;
        onselectend: XRInputSourceEventHandler | null;
        onsqueeze: XRInputSourceEventHandler | null;
        onsqueezestart: XRInputSourceEventHandler | null;
        onsqueezeend: XRInputSourceEventHandler | null;
        onvisibilitychange: XRSessionEventHandler | null;
        onframeratechange: XRSessionEventHandler | null;
    };
    constructor(device: XRDevice, mode: XRSessionMode, enabledFeatures: string[]);
    get visibilityState(): XRVisibilityState;
    get frameRate(): number | undefined;
    get supportedFrameRates(): Float32Array | undefined;
    get renderState(): XRRenderState;
    get inputSources(): XRInputSourceArray;
    get enabledFeatures(): Array<string>;
    get isSystemKeyboardSupported(): boolean;
    get environmentBlendMode(): XREnvironmentBlendMode;
    get interactionMode(): XRInteractionMode;
    updateRenderState(state?: XRRenderStateInit): void;
    updateTargetFrameRate(rate: number): Promise<void>;
    requestReferenceSpace(type: XRReferenceSpaceType): Promise<XRReferenceSpace>;
    requestAnimationFrame(callback: XRFrameRequestCallback): number;
    cancelAnimationFrame(handle: number): void;
    end(): Promise<void>;
    get persistentAnchors(): Readonly<string[]>;
    restorePersistentAnchor(uuid: string): Promise<XRAnchor>;
    deletePersistentAnchor(uuid: string): Promise<undefined>;
    requestHitTestSource(options: XRHitTestOptionsInit): Promise<XRHitTestSource>;
    get onend(): XRSessionEventHandler;
    set onend(callback: XRSessionEventHandler);
    get oninputsourceschange(): XRInputSourcesChangeEventHandler;
    set oninputsourceschange(callback: XRInputSourcesChangeEventHandler);
    get onselect(): XRInputSourceEventHandler;
    set onselect(callback: XRInputSourceEventHandler);
    get onselectstart(): XRInputSourceEventHandler;
    set onselectstart(callback: XRInputSourceEventHandler);
    get onselectend(): XRInputSourceEventHandler;
    set onselectend(callback: XRInputSourceEventHandler);
    get onsqueeze(): XRInputSourceEventHandler;
    set onsqueeze(callback: XRInputSourceEventHandler);
    get onsqueezestart(): XRInputSourceEventHandler;
    set onsqueezestart(callback: XRInputSourceEventHandler);
    get onsqueezeend(): XRInputSourceEventHandler;
    set onsqueezeend(callback: XRInputSourceEventHandler);
    get onvisibilitychange(): XRSessionEventHandler;
    set onvisibilitychange(callback: XRSessionEventHandler);
    get onframeratechange(): XRSessionEventHandler;
    set onframeratechange(callback: XRSessionEventHandler);
}
export {};
//# sourceMappingURL=XRSession.d.ts.map