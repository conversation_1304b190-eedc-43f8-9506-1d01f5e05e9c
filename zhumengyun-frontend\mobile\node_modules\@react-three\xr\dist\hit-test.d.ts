import { GetWorldMatrixFromXRHitTest } from '@pmndrs/xr';
import { RefObject } from 'react';
import { Group, Object3D } from 'three';
export { createXRHitTestSource, requestXRHitTest, type GetWorldMatrixFromXRHitTest } from '@pmndrs/xr';
/**
 * Hook for creating a hit test source originating from the provided object or xrspace
 */
export declare function useXRHitTestSource(relativeTo: RefObject<Object3D | null> | XRSpace | XRReferenceSpaceType, trackableType?: XRHitTestTrackableType | Array<XRHitTestTrackableType>): {
    source: XRHitTestSource;
    getWorldMatrix: (target: import("three").Matrix4, result: XRHitTestResult) => boolean;
} | undefined;
/**
 * Hook for setting up a continous hit test originating from the provided object or xrspace
 */
export declare function useXRHitTest(fn: ((results: Array<XRHitTestResult>, getWorldMatrix: GetWorldMatrixFromXRHitTest) => void) | undefined, relativeTo: RefObject<Object3D | null> | XRSpace | XRReferenceSpaceType, trackableType?: XRHitTestTrackableType | Array<XRHitTestTrackableType>): void;
/**
 * Hook that returns a function to request a single hit test
 */
export declare function useXRRequestHitTest(): (relativeTo: RefObject<Object3D | null> | XRSpace | XRReferenceSpaceType, trackableType?: XRHitTestTrackableType | Array<XRHitTestTrackableType>) => Promise<{
    results: XRHitTestResult[];
    getWorldMatrix: (target: import("three").Matrix4, result: XRHitTestResult) => boolean;
} | undefined> | undefined;
/**
 * Component for getting hit tests originating based on its position in the scene graph
 *
 * @param props ‎
 * #### `space` - [XRSpaceType](https://developer.mozilla.org/en-US/docs/Web/API/XRSpace) | [XRReferenceSpaceType](https://developer.mozilla.org/en-US/docs/Web/API/XRReferenceSpace#reference_space_types)
 * @function
 */
export declare const XRHitTest: import("react").ForwardRefExoticComponent<Omit<Omit<import("@react-three/fiber/dist/declarations/src/core/utils.js").Mutable<import("@react-three/fiber/dist/declarations/src/core/utils.js").Overwrite<Partial<import("@react-three/fiber/dist/declarations/src/core/utils.js").Overwrite<Group<import("three").Object3DEventMap>, import("@react-three/fiber").MathProps<Group<import("three").Object3DEventMap>> & import("@react-three/fiber").ReactProps<Group<import("three").Object3DEventMap>> & Partial<import("@react-three/fiber").EventHandlers>>>, Omit<import("@react-three/fiber").InstanceProps<Group<import("three").Object3DEventMap>, typeof Group>, "object">>>, "children"> & {
    space?: XRSpace | XRReferenceSpaceType;
    trackableType?: XRHitTestTrackableType | Array<XRHitTestTrackableType>;
    onResults?: (results: Array<XRHitTestResult>, getWorldMatrix: GetWorldMatrixFromXRHitTest) => void;
}, "ref"> & import("react").RefAttributes<Group<import("three").Object3DEventMap>>>;
