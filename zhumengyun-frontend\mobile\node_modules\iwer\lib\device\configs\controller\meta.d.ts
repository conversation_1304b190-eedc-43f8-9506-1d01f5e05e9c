/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { XRControllerConfig } from '../../XRController.js';
export declare const oculusTouchV2: XRControllerConfig;
export declare const oculusTouchV3: XRControllerConfig;
export declare const metaQuestTouchPro: XRControllerConfig;
export declare const metaQuestTouchPlus: XRControllerConfig;
//# sourceMappingURL=meta.d.ts.map