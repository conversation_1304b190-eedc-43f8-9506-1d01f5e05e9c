'use client'

import { useState } from 'react'

export default function CreatorTestPage() {
  const [activeTab, setActiveTab] = useState<string>('video')
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [uploadedFiles, setUploadedFiles] = useState<{[key: string]: File[]}>({})
  const [selectedMusic, setSelectedMusic] = useState('')
  const [enableNFT, setEnableNFT] = useState(false)
  const [nftPrice, setNftPrice] = useState(0)
  const [expectedRewards, setExpectedRewards] = useState(50)

  const publishTabs = [
    { key: 'video', title: '首页视频', icon: '🎬', description: '发布到首页视频流' },
    { key: 'discovery', title: '发现图文', icon: '📰', description: '发布到发现页面' },
    { key: 'community', title: '消息社群', icon: '💬', description: '发布到消息页面' },
    { key: 'ecosystem', title: '生态匹配', icon: '🤝', description: '发布生态需求' }
  ]

  const musicLibrary = [
    { id: 'tech-1', name: '科技未来', duration: '2:30', category: 'tech' },
    { id: 'corporate-1', name: '企业力量', duration: '3:15', category: 'corporate' },
    { id: 'inspiring-1', name: '梦想启航', duration: '2:45', category: 'inspiring' }
  ]

  const handleFileUpload = (files: FileList, type: string) => {
    const fileArray = Array.from(files)
    setUploadedFiles(prev => ({
      ...prev,
      [type]: [...(prev[type] || []), ...fileArray]
    }))
  }

  const calculateRewards = () => {
    const base = 50
    const nftBonus = enableNFT ? 20 : 0
    const total = base + nftBonus
    setExpectedRewards(total)
    return total
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
      <div className="relative z-10">
        {/* 顶部导航 */}
        <div className="sticky top-0 bg-black/20 backdrop-blur-lg border-b border-white/10 p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-400 to-blue-500 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">🚀</span>
              </div>
              <div>
                <h1 className="text-xl font-bold">内容发布</h1>
                <p className="text-sm text-purple-300">四大平台 • 智能分发 • Web3奖励</p>
              </div>
            </div>
          </div>

          {/* 发布类型标签页 */}
          <div className="flex space-x-1 bg-white/10 rounded-lg p-1">
            {publishTabs.map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`flex-1 flex flex-col items-center py-3 px-2 rounded-md text-xs font-medium transition-colors ${
                  activeTab === tab.key
                    ? 'bg-purple-500 text-white'
                    : 'text-white/70 hover:text-white'
                }`}
              >
                <span className="text-lg mb-1">{tab.icon}</span>
                <span>{tab.title}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 p-4 pb-20">
          <div className="space-y-6">
            <div className="text-center mb-6">
              <div className="text-4xl mb-3">{publishTabs.find(t => t.key === activeTab)?.icon}</div>
              <h2 className="text-2xl font-bold mb-2">{publishTabs.find(t => t.key === activeTab)?.title}</h2>
              <p className="text-gray-400">{publishTabs.find(t => t.key === activeTab)?.description}</p>
            </div>

            {/* 标题 */}
            <div>
              <label className="block text-sm font-medium mb-2">标题 *</label>
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="请输入标题"
                className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
              />
            </div>

            {/* 描述 */}
            <div>
              <label className="block text-sm font-medium mb-2">描述 *</label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="请输入描述内容..."
                rows={4}
                className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 resize-none"
              />
            </div>

            {/* 文件上传 */}
            {activeTab === 'video' && (
              <div>
                <label className="block text-sm font-medium mb-2">视频文件 *</label>
                <div 
                  className="border-2 border-dashed border-white/20 rounded-lg p-8 text-center hover:border-purple-500 transition-colors cursor-pointer"
                  onClick={() => document.getElementById('video-upload')?.click()}
                >
                  <div className="text-4xl mb-4">📹</div>
                  <p className="text-gray-400 mb-2">点击上传视频文件</p>
                  <p className="text-xs text-gray-500">支持 MP4, MOV, AVI 格式</p>
                  <input
                    id="video-upload"
                    type="file"
                    accept="video/*"
                    multiple
                    className="hidden"
                    onChange={(e) => {
                      if (e.target.files) {
                        handleFileUpload(e.target.files, 'video')
                      }
                    }}
                  />
                </div>
                
                {/* 已上传文件 */}
                {uploadedFiles.video && uploadedFiles.video.length > 0 && (
                  <div className="mt-3 space-y-2">
                    {uploadedFiles.video.map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="text-2xl">🎬</div>
                          <div>
                            <p className="text-white text-sm font-medium">{file.name}</p>
                            <p className="text-gray-400 text-xs">{(file.size / 1024 / 1024).toFixed(1)} MB</p>
                          </div>
                        </div>
                        <button
                          onClick={() => {
                            setUploadedFiles(prev => ({
                              ...prev,
                              video: prev.video?.filter((_, i) => i !== index) || []
                            }))
                          }}
                          className="text-red-400 hover:text-red-300"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* 音乐选择 */}
            {activeTab === 'video' && (
              <div>
                <label className="block text-sm font-medium mb-2">背景音乐</label>
                <div className="space-y-2">
                  {musicLibrary.map((music) => (
                    <button
                      key={music.id}
                      onClick={() => setSelectedMusic(music.id)}
                      className={`w-full p-3 rounded-lg border transition-colors text-left ${
                        selectedMusic === music.id
                          ? 'bg-purple-500/20 border-purple-500 text-white'
                          : 'bg-white/5 border-white/20 text-gray-400 hover:border-white/40'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">{music.name}</div>
                          <div className="text-xs text-gray-500">{music.duration} • {music.category}</div>
                        </div>
                        <div className="text-2xl">🎵</div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Web3功能 */}
            <div className="border-t border-white/20 pt-6">
              <h4 className="text-lg font-semibold mb-4 flex items-center">
                <span className="text-2xl mr-2">🌐</span>
                Web3功能
              </h4>

              {/* NFT选项 */}
              <div className="mb-4">
                <label className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <span className="text-2xl">🎨</span>
                    <span className="text-white font-medium">铸造为NFT</span>
                  </div>
                  <input
                    type="checkbox"
                    checked={enableNFT}
                    onChange={(e) => {
                      setEnableNFT(e.target.checked)
                      calculateRewards()
                    }}
                    className="w-4 h-4 text-purple-500 bg-white/10 border-white/20 rounded focus:ring-purple-500"
                  />
                </label>
                
                {enableNFT && (
                  <div className="pl-8">
                    <input
                      type="number"
                      step="0.01"
                      value={nftPrice}
                      onChange={(e) => setNftPrice(Number(e.target.value))}
                      placeholder="NFT价格 (ETH)"
                      className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                    />
                  </div>
                )}
              </div>

              {/* 奖励预览 */}
              <div className="p-4 bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/20 rounded-lg">
                <div className="text-center">
                  <div className="text-xl font-bold text-white">
                    预期奖励: {calculateRewards()} NGT
                  </div>
                  <div className="text-xs text-gray-400">
                    ≈ ${(calculateRewards() * 0.15).toFixed(2)} USD
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 底部发布按钮 */}
        <div className="fixed bottom-16 left-0 right-0 bg-black/20 backdrop-blur-lg border-t border-white/10 p-4">
          <button
            disabled={!title || !description}
            className={`w-full py-4 rounded-xl font-medium transition-colors ${
              !title || !description
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : 'bg-gradient-to-r from-purple-500 to-blue-500 text-white hover:from-purple-600 hover:to-blue-600'
            }`}
          >
            发布内容
          </button>
        </div>
      </div>
    </div>
  )
}
