import { OrthographicCamera, PerspectiveC<PERSON>ra, Scene, Vector3Tuple } from 'three';
import { StoreApi } from 'zustand/vanilla';
import { defaultScreenCameraApply, ScreenCameraStateAndFunctions } from './camera.js';
import { ScreenHandleStore } from './store.js';
export declare class ZoomScreenHandleStore extends ScreenHandleStore<{
    distance: number;
    origin: Readonly<Vector3Tuple>;
}> {
    private readonly store;
    private readonly getCamera;
    filter?: ((map: ScreenHandleStore["map"]) => boolean) | undefined;
    customApply?: typeof defaultScreenCameraApply | undefined;
    speed?: number | undefined;
    zoomToPointer?: boolean | undefined;
    constructor(store: StoreApi<ScreenCameraStateAndFunctions>, getCamera: () => OrthographicCamera | PerspectiveCamera, filter?: ((map: ScreenHandleStore["map"]) => boolean) | undefined, customApply?: typeof defaultScreenCameraApply | undefined, speed?: number | undefined, zoomToPointer?: boolean | undefined);
    private onWheel;
    bind(scene: Scene): () => void;
}
