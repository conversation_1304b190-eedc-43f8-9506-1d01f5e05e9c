import { <PERSON><PERSON>r, Matrix4, Quaternion, Vector3 } from 'three';
import { HandleTransformState } from '../state.js';
import { HandleOptions, HandleTransformOptions } from '../store.js';
export type TwoPointerHandlePointerData = {
    initialPointerWorldPoint: Vector3;
    initialPointerWorldDirection: Vector3 | undefined;
    initialPointerWorldQuaternion: Quaternion;
    pointerWorldPoint: Vector3;
    pointerWorldOrigin: Vector3;
    pointerWorldDirection: Vector3 | undefined;
    pointerWorldQuaternion: Quaternion;
    prevPointerWorldQuaternion: Quaternion;
};
export type TwoPointerHandleStoreData = {
    initialTargetPosition: Vector3;
    initialTargetQuaternion: Quaternion;
    initialTargetRotation: Euler;
    initialTargetScale: Vector3;
    initialTargetParentWorldMatrix: Matrix4 | undefined;
    prevTwoPointerDeltaRotation: Quaternion | undefined;
    prevAngle: number | undefined;
};
export declare function computeTwoPointerHandleTransformState(time: number, pointer1Data: TwoPointerHandlePointerData, pointer2Data: TwoPointerHandlePointerData, storeData: TwoPointerHandleStoreData, targetParentWorldMatrix: Matrix4 | undefined, options: HandleOptions<any> & {
    translate?: HandleTransformOptions;
}): HandleTransformState;
