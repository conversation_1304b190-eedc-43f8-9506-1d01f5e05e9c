{"version": 3, "file": "bls.d.ts", "sourceRoot": "", "sources": ["../../src/abstract/bls.ts"], "names": [], "mappings": "AAAA,sEAAsE;AAEtE,OAAO,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AACzC,OAAO,EAAE,MAAM,EAAoC,MAAM,cAAc,CAAC;AACxE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAA+B,MAAM,YAAY,CAAC;AAE9E,OAAO,EACL,UAAU,EAAE,IAAI,IAAI,OAAO,EAAuB,YAAY,EAC9D,YAAY,EACb,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EACL,eAAe,EACf,aAAa,IAAI,aAAa,EAC9B,cAAc,EAEf,MAAM,kBAAkB,CAAC;AAE1B;;;;;;;;;;;IAWI;AAEJ,KAAK,EAAE,GAAG,MAAM,CAAC;AAKjB,MAAM,MAAM,mBAAmB,CAAC,EAAE,IAAI;IACpC,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;IACrC,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;IACjD,KAAK,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;CACzC,CAAC;AAEF,MAAM,MAAM,cAAc,CAAC,GAAG,IAAI;IAChC,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;IACtC,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;IAClD,KAAK,CAAC,KAAK,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;CAC1C,CAAC;AAEF,KAAK,MAAM,CAAC,EAAE,EAAE,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG;IACnC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK;QAAE,EAAE,EAAE,EAAE,CAAC;QAAC,EAAE,EAAE,EAAE,CAAA;KAAE,CAAC;IACvC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC;IAC/B,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG,CAAC;CAC5C,CAAC;AAEF,KAAK,OAAO,CAAC,GAAG,EAAE,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG;IACvC,YAAY,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;IAC7C,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,IAAI,CAAC;IAC1D,SAAS,CAAC,GAAG,EAAE,IAAI,GAAG,IAAI,CAAC;IAC3B,iBAAiB,CAAC,GAAG,EAAE,IAAI,GAAG,IAAI,CAAC;CACpC,CAAC;AAEF,MAAM,MAAM,SAAS,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,IAAI;IAC1C,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG;QACnC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;QACnC,UAAU,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC;QAC3B,WAAW,EAAE,OAAO,CAAC;KACtB,CAAC;IACF,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG;QACpC,SAAS,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC;QAC/B,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;QAC5B,WAAW,EAAE,OAAO,CAAC;KACtB,CAAC;IACF,MAAM,EAAE;QACN,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;QACf,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QACnB,GAAG,EAAE,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QACrB,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,IAAI,EAAE,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;KAC1B,CAAC;IACF,MAAM,EAAE;QACN,CAAC,EAAE,MAAM,CAAC;QACV,CAAC,EAAE,MAAM,CAAC;KACX,CAAC;IACF,WAAW,EAAE,OAAO,CAAC;IACrB,IAAI,EAAE,KAAK,CAAC;IACZ,WAAW,EAAE,CAAC,WAAW,CAAC,EAAE,MAAM,KAAK,UAAU,CAAC;CACnD,CAAC;AAEF,MAAM,MAAM,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,IAAI;IACxC,YAAY,EAAE,CAAC,UAAU,EAAE,OAAO,KAAK,UAAU,CAAC;IAClD,8BAA8B,EAAE,CAAC,UAAU,EAAE,OAAO,KAAK,UAAU,CAAC;IACpE,IAAI,EAAE;QACJ,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,UAAU,CAAC;QACxE,CAAC,OAAO,EAAE,aAAa,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;KAChG,CAAC;IACF,kBAAkB,EAAE;QAClB,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,UAAU,CAAC;QACxE,CAAC,OAAO,EAAE,aAAa,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;KAC9F,CAAC;IACF,MAAM,EAAE,CACN,SAAS,EAAE,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,EACnC,OAAO,EAAE,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,EACjC,SAAS,EAAE,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,EAClC,OAAO,CAAC,EAAE,YAAY,KACnB,OAAO,CAAC;IACb,oBAAoB,EAAE,CACpB,SAAS,EAAE,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,EAClC,OAAO,EAAE,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,EAChC,SAAS,EAAE,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,EACnC,OAAO,CAAC,EAAE,YAAY,KACnB,OAAO,CAAC;IACb,WAAW,EAAE,CACX,SAAS,EAAE,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,EACnC,QAAQ,EAAE,CAAC,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE,EACtC,UAAU,EAAE,CAAC,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE,EACvC,OAAO,CAAC,EAAE,YAAY,KACnB,OAAO,CAAC;IACb,mBAAmB,EAAE;QACnB,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC;QAChC,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;KACtD,CAAC;IACF,mBAAmB,EAAE;QACnB,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC;QAChC,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,CAAC,EAAE,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;KACxD,CAAC;IACF,wBAAwB,EAAE;QACxB,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC;QAChC,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;KACtD,CAAC;IACF,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,KAAK,IAAI,CAAC;IAC3D,OAAO,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,GAAG,CAAC,EAAE,iBAAiB,CAAC,EAAE,OAAO,KAAK,IAAI,CAAC;IAC5F,EAAE,EAAE,cAAc,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,OAAO,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7D,EAAE,EAAE,cAAc,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/D,SAAS,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC;IAC/B,cAAc,EAAE,mBAAmB,CAAC,EAAE,CAAC,CAAC;IACxC,MAAM,EAAE;QACN,CAAC,EAAE,MAAM,CAAC;QACV,CAAC,EAAE,MAAM,CAAC;QACV,GAAG,EAAE,MAAM,CAAC;QACZ,GAAG,EAAE,GAAG,CAAC;KACV,CAAC;IACF,MAAM,EAAE;QACN,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;QACf,GAAG,EAAE,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QACrB,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,IAAI,EAAE,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACzB,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;KACpB,CAAC;IACF,KAAK,EAAE;QACL,gBAAgB,EAAE,MAAM,UAAU,CAAC;QACnC,sBAAsB,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;KACpE,CAAC;CACH,CAAC;AAEF,wBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAChC,KAAK,EAAE,SAAS,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GACnC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CA0V7B"}