/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_REF_SPACE } from '../private.js';
import { XRReferenceSpaceEventHandler } from '../events/XRReferenceSpaceEvent.js';
import { XRSpace } from './XRSpace.js';
import { mat4 } from 'gl-matrix';
export declare enum XRReferenceSpaceType {
    Viewer = "viewer",
    Local = "local",
    LocalFloor = "local-floor",
    BoundedFloor = "bounded-floor",
    Unbounded = "unbounded"
}
export declare class XRReferenceSpace extends XRSpace {
    [P_REF_SPACE]: {
        type: XRReferenceSpaceType;
        onreset: XRReferenceSpaceEventHandler | null;
    };
    constructor(type: XRReferenceSpaceType, parentSpace: XRSpace, offsetMatrix?: mat4);
    get onreset(): XRReferenceSpaceEventHandler;
    set onreset(callback: XRReferenceSpaceEventHandler);
    getOffsetReferenceSpace(originOffset: mat4): XRReferenceSpace;
}
//# sourceMappingURL=XRReferenceSpace.d.ts.map