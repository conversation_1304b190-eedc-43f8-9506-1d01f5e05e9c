/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
/// <reference types="webxr" />
import { quat, vec3 } from 'gl-matrix';
import { P_ACTION_RECORDER } from '../private.js';
export interface InputFrame {
    index: number;
    targetRayTransform: {
        position: vec3;
        quaternion: quat;
    };
    gripTransform?: {
        position: vec3;
        quaternion: quat;
    };
    hand?: {
        [joint in XRHandJoint]: {
            position: vec3;
            quaternion: quat;
            radius: number;
        };
    };
    gamepad?: {
        buttons: (number[] | null)[];
        axes: (number | null)[];
    };
}
export interface InputSchema {
    handedness: XRHandedness;
    targetRayMode: XRTargetRayMode;
    profiles: string[];
    hasGrip: boolean;
    hasHand: boolean;
    jointSequence?: XRHandJoint[];
    hasGamepad: boolean;
    mapping?: GamepadMappingType;
    numButtons?: number;
    numAxes?: number;
}
export interface ActionFrame {
    timeStamp: DOMHighResTimeStamp;
    position: vec3;
    quaternion: quat;
    inputFrames: InputFrame[];
}
export declare class ActionRecorder {
    [P_ACTION_RECORDER]: {
        session: XRSession;
        refSpace: XRReferenceSpace;
        inputMap: Map<XRInputSource, number>;
        schemaMap: Map<number, InputSchema>;
        compressedFrames: any[];
        jointRadii: Float32Array;
        jointTransforms: Float32Array;
    };
    constructor(session: XRSession, refSpace: XRReferenceSpace);
    recordFrame(frame: XRFrame): void;
    compressActionFrame(af: ActionFrame): any[];
    log(): void;
}
//# sourceMappingURL=ActionRecorder.d.ts.map