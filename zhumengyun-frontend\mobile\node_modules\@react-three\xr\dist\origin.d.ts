import { ThreeElements } from '@react-three/fiber';
import { Group } from 'three';
export type XROriginProps = ThreeElements['group'] & {
    disabled?: boolean;
};
/**
 * Component for setting the origin of the player (their feet)
 *
 * @param props
 * Accepts the same props as a ThreeJs [Group](https://threejs.org/docs/#api/en/objects/Group)
 * @function
 */
export declare const XROrigin: import("react").ForwardRefExoticComponent<Omit<XROriginProps, "ref"> & import("react").RefAttributes<Group<import("three").Object3DEventMap>>>;
