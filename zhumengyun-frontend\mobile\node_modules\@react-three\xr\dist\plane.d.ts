import { BufferGeometry, Mesh } from 'three';
/**
 * Component for rendering a mesh for the XRPlane based on the detected plane geometry
 *
 * @param props
 * Accepts the same props as a ThreeJs [Mesh](https://threejs.org/docs/#api/en/objects/Mesh)
 * @function
 */
export declare const XRPlaneModel: import("react").ForwardRefExoticComponent<Omit<import("@react-three/fiber/dist/declarations/src/core/utils.js").Mutable<import("@react-three/fiber/dist/declarations/src/core/utils.js").Overwrite<Partial<import("@react-three/fiber/dist/declarations/src/core/utils.js").Overwrite<Mesh<BufferGeometry<import("three").NormalBufferAttributes>, import("three").Material | import("three").Material[], import("three").Object3DEventMap>, import("@react-three/fiber").MathProps<Mesh<BufferGeometry<import("three").NormalBufferAttributes>, import("three").Material | import("three").Material[], import("three").Object3DEventMap>> & import("@react-three/fiber").ReactProps<Mesh<BufferGeometry<import("three").NormalBufferAttributes>, import("three").Material | import("three").Material[], import("three").Object3DEventMap>> & Partial<import("@react-three/fiber").EventHandlers>>>, Omit<import("@react-three/fiber").InstanceProps<Mesh<BufferGeometry<import("three").NormalBufferAttributes>, import("three").Material | import("three").Material[], import("three").Object3DEventMap>, typeof Mesh>, "object">>> & {
    plane: XRPlane;
}, "ref"> & import("react").RefAttributes<Mesh<BufferGeometry<import("three").NormalBufferAttributes>, import("three").Material | import("three").Material[], import("three").Object3DEventMap>>>;
/**
 * Hook for getting all dected planes with the provided semantic label
 */
export declare function useXRPlanes(semanticLabel?: string): readonly XRPlane[];
/**
 * Hook for getting the geometry from the detected plane
 *
 * @param plane the detected plane
 * @param disposeBuffer allows to disable auto disposing the geometry buffer
 */
export declare function useXRPlaneGeometry(plane: XRPlane, disposeBuffer?: boolean): BufferGeometry;
