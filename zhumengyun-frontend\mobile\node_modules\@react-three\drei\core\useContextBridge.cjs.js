"use strict";function e(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}Object.defineProperty(exports,"__esModule",{value:!0});var r=e(require("react"));exports.useContextBridge=function(...e){const t=r.useRef([]);return t.current=e.map((e=>r.useContext(e))),r.useMemo((()=>({children:n})=>e.reduceRight(((e,n,u)=>r.createElement(n.Provider,{value:t.current[u],children:e})),n)),[])};
