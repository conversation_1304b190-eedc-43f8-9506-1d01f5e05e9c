{"version": 3, "file": "scrypt.js", "sourceRoot": "", "sources": ["../../src.ts/crypto/scrypt.ts"], "names": [], "mappings": ";;;AAAA,iDAAwF;AAExF,gDAA2D;AAe3D,IAAI,UAAU,GAAG,KAAK,EAAE,WAAW,GAAG,KAAK,CAAC;AAE5C,MAAM,YAAY,GAAG,KAAK,WAAU,MAAkB,EAAE,IAAgB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,UAA6B;IACnJ,OAAO,MAAM,IAAA,oBAAW,EAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;AAC3E,CAAC,CAAA;AACD,MAAM,WAAW,GAAG,UAAS,MAAkB,EAAE,IAAgB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,KAAa;IAC7G,OAAO,IAAA,eAAU,EAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;AACxD,CAAC,CAAA;AAED,IAAI,aAAa,GAAgJ,YAAY,CAAC;AAC9K,IAAI,YAAY,GAAwG,WAAW,CAAA;AAGnI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoCG;AACI,KAAK,UAAU,MAAM,CAAC,OAAkB,EAAE,KAAgB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,QAA2B;IAC1I,MAAM,MAAM,GAAG,IAAA,mBAAQ,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC3C,MAAM,IAAI,GAAG,IAAA,mBAAQ,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACrC,OAAO,IAAA,kBAAC,EAAC,MAAM,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC1E,CAAC;AAJD,wBAIC;AACD,MAAM,CAAC,CAAC,GAAG,YAAY,CAAC;AACxB,MAAM,CAAC,IAAI,GAAG,cAAmB,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;AACtD,MAAM,CAAC,QAAQ,GAAG,UAAS,IAA+I;IACtK,IAAI,WAAW,EAAE;QAAE,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;KAAE;IACzD,aAAa,GAAG,IAAI,CAAC;AACzB,CAAC,CAAA;AACD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAEtB;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,SAAgB,UAAU,CAAC,OAAkB,EAAE,KAAgB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,KAAa;IAC3G,MAAM,MAAM,GAAG,IAAA,mBAAQ,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC3C,MAAM,IAAI,GAAG,IAAA,mBAAQ,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACrC,OAAO,IAAA,kBAAC,EAAC,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACzD,CAAC;AAJD,gCAIC;AACD,UAAU,CAAC,CAAC,GAAG,WAAW,CAAC;AAC3B,UAAU,CAAC,IAAI,GAAG,cAAmB,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;AACzD,UAAU,CAAC,QAAQ,GAAG,UAAS,IAAyG;IACpI,IAAI,UAAU,EAAE;QAAE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;KAAE;IAC5D,YAAY,GAAG,IAAI,CAAC;AACxB,CAAC,CAAA;AACD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC"}