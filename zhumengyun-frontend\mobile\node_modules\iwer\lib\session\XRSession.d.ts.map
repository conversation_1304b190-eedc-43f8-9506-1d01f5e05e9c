{"version": 3, "file": "XRSession.d.ts", "sourceRoot": "", "sources": ["../../src/session/XRSession.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AACzD,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAON,SAAS,EAGT,MAAM,eAAe,CAAC;AACvB,OAAO,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACpE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAiB,MAAM,wBAAwB,CAAC;AAC9E,OAAO,EACN,oBAAoB,EAEpB,eAAe,EACf,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAE,aAAa,EAAE,kBAAkB,EAAE,MAAM,2BAA2B,CAAC;AAC9E,OAAO,EAEN,gCAAgC,EAChC,MAAM,wCAAwC,CAAC;AAChD,OAAO,EACN,gBAAgB,EAChB,oBAAoB,EACpB,MAAM,+BAA+B,CAAC;AACvC,OAAO,EAAE,aAAa,EAAE,iBAAiB,EAAE,MAAM,oBAAoB,CAAC;AACtE,OAAO,EAEN,qBAAqB,EACrB,MAAM,6BAA6B,CAAC;AAGrC,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAC3C,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,yBAAyB,EAAE,MAAM,iCAAiC,CAAC;AAC5E,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAEjC,MAAM,MAAM,iBAAiB,GAAG,SAAS,GAAG,iBAAiB,GAAG,QAAQ,CAAC;AAEzE,MAAM,MAAM,aAAa,GAAG,QAAQ,GAAG,cAAc,GAAG,cAAc,CAAC;AAEvE,MAAM,MAAM,aAAa,GAAG;IAC3B,gBAAgB,CAAC,EAAE,YAAY,EAAE,CAAC;IAClC,gBAAgB,CAAC,EAAE,YAAY,EAAE,CAAC;CAClC,CAAC;AAEF,oBAAY,sBAAsB;IACjC,MAAM,WAAW;IACjB,UAAU,gBAAgB;IAC1B,QAAQ,aAAa;CACrB;AAED,oBAAY,iBAAiB;IAC5B,WAAW,iBAAiB;IAC5B,UAAU,gBAAgB;CAC1B;AAED,KAAK,sBAAsB,GAAG,CAC7B,IAAI,EAAE,mBAAmB,EACzB,KAAK,EAAE,OAAO,KACV,IAAI,CAAC;AAEV,KAAK,YAAY,GAAG;IACnB,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,sBAAsB,CAAC;IACjC,SAAS,EAAE,OAAO,CAAC;CACnB,CAAC;AAEF,qBAAa,SAAU,SAAQ,WAAW;IACzC,CAAC,SAAS,CAAC,EAAE;QACZ,MAAM,EAAE,QAAQ,CAAC;QACjB,eAAe,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAC/B,yBAAyB,EAAE,OAAO,CAAC;QACnC,IAAI,EAAE,aAAa,CAAC;QACpB,KAAK,EAAE,OAAO,CAAC;QACf,yBAAyB,EAAE,CAC1B,kBAAkB,EAAE,oBAAoB,KACpC,OAAO,CAAC;QAEb,kBAAkB,EAAE;aAAG,GAAG,IAAI,KAAK,GAAG,IAAI;SAAE,CAAC;QAC7C,mBAAmB,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK,IAAI,CAAC;QAE1C,WAAW,EAAE,MAAM,CAAC;QACpB,cAAc,EAAE,YAAY,EAAE,CAAC;QAC/B,qBAAqB,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC;QAE7C,aAAa,EAAE,MAAM,IAAI,CAAC;QAC1B,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAE3B,WAAW,EAAE,aAAa,CAAC;QAC3B,kBAAkB,EAAE,aAAa,GAAG,IAAI,CAAC;QAEzC,gBAAgB,EAAE,MAAM,CAAC;QAEzB,eAAe,EAAE,gBAAgB,EAAE,CAAC;QAEpC,gBAAgB,EAAE,kBAAkB,CAAC;QACrC,kBAAkB,EAAE,aAAa,EAAE,CAAC;QACpC,wBAAwB,EAAE,MAAM,IAAI,CAAC;QAErC,cAAc,EAAE,WAAW,CAAC;QAC5B,iBAAiB,EAAE,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACzC,UAAU,EAAE,GAAG,CACd,QAAQ,EACR;YACC,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC;YAC3D,MAAM,EAAE,CAAC,MAAM,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC;SAC/B,CACD,CAAC;QACF,mBAAmB,EAAE,WAAW,CAAC;QACjC,oBAAoB,EAAE,MAAM,IAAI,CAAC;QAEjC,aAAa,EAAE,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QACzC,mBAAmB,EAAE,CAAC,KAAK,EAAE,OAAO,KAAK,IAAI,CAAC;QAE9C,aAAa,EAAE,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QACvC,mBAAmB,EAAE,CAAC,KAAK,EAAE,OAAO,KAAK,IAAI,CAAC;QAE9C,cAAc,EAAE,GAAG,CAAC,eAAe,CAAC,CAAC;QACrC,qBAAqB,EAAE,CAAC,KAAK,EAAE,OAAO,KAAK,IAAI,CAAC;QAEhD,KAAK,EAAE,qBAAqB,GAAG,IAAI,CAAC;QACpC,oBAAoB,EAAE,gCAAgC,GAAG,IAAI,CAAC;QAC9D,QAAQ,EAAE,yBAAyB,GAAG,IAAI,CAAC;QAC3C,aAAa,EAAE,yBAAyB,GAAG,IAAI,CAAC;QAChD,WAAW,EAAE,yBAAyB,GAAG,IAAI,CAAC;QAC9C,SAAS,EAAE,yBAAyB,GAAG,IAAI,CAAC;QAC5C,cAAc,EAAE,yBAAyB,GAAG,IAAI,CAAC;QACjD,YAAY,EAAE,yBAAyB,GAAG,IAAI,CAAC;QAC/C,kBAAkB,EAAE,qBAAqB,GAAG,IAAI,CAAC;QACjD,iBAAiB,EAAE,qBAAqB,GAAG,IAAI,CAAC;KAChD,CAAC;gBAGD,MAAM,EAAE,QAAQ,EAChB,IAAI,EAAE,aAAa,EACnB,eAAe,EAAE,MAAM,EAAE;IAkX1B,IAAI,eAAe,IAAI,iBAAiB,CAEvC;IAED,IAAI,SAAS,IAAI,MAAM,GAAG,SAAS,CAElC;IAED,IAAI,mBAAmB,IAAI,YAAY,GAAG,SAAS,CAElD;IAED,IAAI,WAAW,IAAI,aAAa,CAE/B;IAED,IAAI,YAAY,IAAI,kBAAkB,CASrC;IAED,IAAI,eAAe,IAAI,KAAK,CAAC,MAAM,CAAC,CAEnC;IAED,IAAI,yBAAyB,IAAI,OAAO,CAEvC;IAED,IAAI,oBAAoB,IAAI,sBAAsB,CAMjD;IAED,IAAI,eAAe,IAAI,iBAAiB,CAEvC;IAED,iBAAiB,CAAC,KAAK,GAAE,iBAAsB,GAAG,IAAI;IAoDhD,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IA8BlD,qBAAqB,CAC1B,IAAI,EAAE,oBAAoB,GACxB,OAAO,CAAC,gBAAgB,CAAC;IA0C5B,qBAAqB,CAAC,QAAQ,EAAE,sBAAsB,GAAG,MAAM;IAa/D,oBAAoB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAoBpC,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC;IAgB1B,IAAI,iBAAiB,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC,CAE1C;IAED,uBAAuB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC;IA8BxD,sBAAsB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC;IAkBxD,oBAAoB,CAAC,OAAO,EAAE,oBAAoB;IA6BlD,IAAI,KAAK,IAIW,qBAAqB,CAFxC;IAED,IAAI,KAAK,CAAC,QAAQ,EAAE,qBAAqB,EAQxC;IAED,IAAI,oBAAoB,IAIW,gCAAgC,CAFlE;IAED,IAAI,oBAAoB,CAAC,QAAQ,EAAE,gCAAgC,EAWlE;IAED,IAAI,QAAQ,IAIW,yBAAyB,CAF/C;IAED,IAAI,QAAQ,CAAC,QAAQ,EAAE,yBAAyB,EAW/C;IAED,IAAI,aAAa,IAIW,yBAAyB,CAFpD;IAED,IAAI,aAAa,CAAC,QAAQ,EAAE,yBAAyB,EAWpD;IAED,IAAI,WAAW,IAIW,yBAAyB,CAFlD;IAED,IAAI,WAAW,CAAC,QAAQ,EAAE,yBAAyB,EAWlD;IAED,IAAI,SAAS,IAIW,yBAAyB,CAFhD;IAED,IAAI,SAAS,CAAC,QAAQ,EAAE,yBAAyB,EAWhD;IAED,IAAI,cAAc,IAIW,yBAAyB,CAFrD;IAED,IAAI,cAAc,CAAC,QAAQ,EAAE,yBAAyB,EAWrD;IAED,IAAI,YAAY,IAIW,yBAAyB,CAFnD;IAED,IAAI,YAAY,CAAC,QAAQ,EAAE,yBAAyB,EAWnD;IAED,IAAI,kBAAkB,IAIW,qBAAqB,CAFrD;IAED,IAAI,kBAAkB,CAAC,QAAQ,EAAE,qBAAqB,EAWrD;IAED,IAAI,iBAAiB,IAIW,qBAAqB,CAFpD;IAED,IAAI,iBAAiB,CAAC,QAAQ,EAAE,qBAAqB,EAWpD;CACD"}