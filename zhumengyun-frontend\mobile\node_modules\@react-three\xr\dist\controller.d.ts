import { XRControllerGamepadComponentId, XRControllerGamepadComponentState, XRControllerLayout, XRControllerLayoutLoaderOptions, XRControllerModelOptions, XRControllerState } from '@pmndrs/xr/internals';
import { ReactNode } from 'react';
import { Object3D } from 'three';
/**
 * Component for placing content in the controller anchored at a specific component such as the Thumbstick
 *
 * @param props
 * #### `id` - `XRControllerGamepadComponentId` Is the id of the component where content should be placed (e.g. `"a-button"`)
 * #### `onPress?` - `Function` Is an optional callback to receive when the component is pressed
 * #### `onRelease?` - `Function` Is an optional callback to receive when the component is released
 * #### `children?` - `ReactNode` Children to be placed inside the componenent (e.g. visualizing a tooltip over the button...)
 *
 * @function
 */
export declare const XRControllerComponent: import("react").ForwardRefExoticComponent<{
    onPress?: () => void;
    onRelease?: () => void;
    id: XRControllerGamepadComponentId;
    children?: ReactNode;
} & import("react").RefAttributes<Object3D<import("three").Object3DEventMap> | undefined>>;
/**
 * Hook for subscribing to a button state change event on the controller
 * @param id of the button
 * @param onChange callback that gets executed when the state of the button changes
 * @param handedness of the controller
 */
export declare function useXRControllerButtonEvent(controller: XRControllerState, id: XRControllerGamepadComponentId, onChange: (state: XRControllerGamepadComponentState['state']) => void): void;
export type { XRControllerModelOptions, XRControllerState };
/**
 * Component for rendering a 3D model for the XRController
 * @param props
 * #### `colorWrite` - Configures the colorWrite property of the model
 * #### `renderOrder` - Configures the render order model
 * @function
 */
export declare const XRControllerModel: import("react").ForwardRefExoticComponent<XRControllerModelOptions & import("react").RefAttributes<Object3D<import("three").Object3DEventMap>>>;
/**
 * For rendering a controller that is not included in WebXR. (e.g controller tutorials/demos)
 *
 * @param profileIds
 * @param handedness
 * @param XRControllerLayoutLoaderOptions
 * @returns Promise<XRControllerLayout>
 */
export declare function useLoadXRControllerLayout(profileIds: string[], handedness: XRHandedness, { baseAssetPath, defaultControllerProfileId }?: XRControllerLayoutLoaderOptions): XRControllerLayout;
/**
 * Loads the controller model for the given layout. This is a suspendable function, so it can be used with React Suspense.
 * @param layout: XRControllerLayout
 * @returns Promise<THREE.Group>
 */
export declare function useLoadXRControllerModel(layout: XRControllerLayout): import("three").Group<import("three").Object3DEventMap>;
