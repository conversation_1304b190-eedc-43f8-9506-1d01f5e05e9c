/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import type { XRDevice } from '../device/XRDevice.js';
import { XRSession, XRSessionInit, XRSessionMode } from '../session/XRSession.js';
import { P_SYSTEM } from '../private.js';
type SessionGrantConfig = {
    resolve: (value: XRSession) => void;
    reject: (reason?: any) => void;
    mode: XRSessionMode;
    options: XRSessionInit;
};
export declare class XRSystem extends EventTarget {
    [P_SYSTEM]: {
        device: XRDevice;
        activeSession?: XRSession;
        grantSession: (SessionGrantConfig: SessionGrantConfig) => void;
        offeredSessionConfig?: SessionGrantConfig;
    };
    constructor(device: XRDevice);
    isSessionSupported(mode: XRSessionMode): Promise<boolean>;
    requestSession(mode: XRSessionMode, options?: XRSessionInit): Promise<XRSession>;
    offerSession(mode: XRSessionMode, options?: XRSessionInit): Promise<XRSession>;
}
export {};
//# sourceMappingURL=XRSystem.d.ts.map