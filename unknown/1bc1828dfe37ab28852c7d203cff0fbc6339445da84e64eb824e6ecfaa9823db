"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/wechat-video-homepage.tsx":
/*!*******************************************!*\
  !*** ./src/app/wechat-video-homepage.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WeChatVideoHomepage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction WeChatVideoHomepage() {\n    _s();\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFollowing, setIsFollowing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showComments, setShowComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showShare, setShowShare] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(60);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('推荐') // 推荐/关注/朋友切换\n    ;\n    // 推荐视频内容数据\n    const recommendedContent = [\n        {\n            id: 1,\n            title: \"震撼！全球最高摩天大楼建设纪录片\",\n            author: \"建筑奇迹\",\n            authorAvatar: \"🏢\",\n            verified: true,\n            description: \"🏗️ 见证828米迪拜塔的建设全过程，工程技术的巅峰之作！\",\n            videoUrl: \"/api/placeholder/400/700\",\n            likes: 1200000,\n            comments: 89000,\n            shares: 156000,\n            tags: [\n                \"#摩天大楼\",\n                \"#建筑奇迹\",\n                \"#工程技术\"\n            ],\n            location: \"阿联酋迪拜\",\n            music: \"史诗音乐 - 建设者之歌\",\n            videoTime: \"12:30\",\n            friendCount: \"热门推荐\"\n        },\n        {\n            id: 2,\n            title: \"AI机器人自动化建房，24小时完工！\",\n            author: \"未来建筑师\",\n            authorAvatar: \"🤖\",\n            verified: true,\n            description: \"🚀 革命性的3D打印建筑技术，机器人24小时自动建房全过程！\",\n            videoUrl: \"/api/placeholder/400/700\",\n            likes: 890000,\n            comments: 67000,\n            shares: 123000,\n            tags: [\n                \"#AI建筑\",\n                \"#3D打印\",\n                \"#未来科技\"\n            ],\n            location: \"美国硅谷\",\n            music: \"科技音乐 - 未来之声\",\n            videoTime: \"08:45\",\n            friendCount: \"热门推荐\"\n        },\n        {\n            id: 3,\n            title: \"中国基建狂魔：高铁穿山越岭全纪录\",\n            author: \"基建达人\",\n            authorAvatar: \"🚄\",\n            verified: true,\n            description: \"🚄 中国高铁建设的震撼场面，穿山越岭的工程奇迹！\",\n            videoUrl: \"/api/placeholder/400/700\",\n            likes: 1560000,\n            comments: 234000,\n            shares: 289000,\n            tags: [\n                \"#中国基建\",\n                \"#高铁建设\",\n                \"#工程奇迹\"\n            ],\n            location: \"中国四川\",\n            music: \"磅礴音乐 - 中华力量\",\n            videoTime: \"15:20\",\n            friendCount: \"热门推荐\"\n        }\n    ];\n    // 关注视频内容数据\n    const followingContent = [\n        {\n            id: 1,\n            title: \"何兰风车+龙猫烤炉！邻居连夜举报......\",\n            author: \"娟小刘J7\",\n            authorAvatar: \"🏗️\",\n            verified: false,\n            description: \"何兰风车+龙猫烤炉！邻居连夜举报......\",\n            videoUrl: \"/api/placeholder/400/700\",\n            likes: 94000,\n            comments: 100000,\n            shares: 34000,\n            tags: [\n                \"#工程建设\",\n                \"#创意设计\",\n                \"#邻里故事\"\n            ],\n            location: \"浙江省杭州市\",\n            music: \"原创音乐 - 工地进行曲\",\n            videoTime: \"10:52\",\n            friendCount: \"1个朋友关注\"\n        },\n        {\n            id: 2,\n            title: \"智慧工地AI监控系统实时演示\",\n            author: \"工程师老王\",\n            authorAvatar: \"👷\",\n            verified: true,\n            description: \"🏗️ 最新AI技术在建筑工地的应用，实时监控施工安全，提升工程效率。\",\n            videoUrl: \"/api/placeholder/400/700\",\n            likes: 156000,\n            comments: 89000,\n            shares: 45000,\n            tags: [\n                \"#智慧工地\",\n                \"#AI监控\",\n                \"#安全施工\"\n            ],\n            location: \"上海市浦东新区\",\n            music: \"电子音乐 - 科技未来\",\n            videoTime: \"08:30\",\n            friendCount: \"3个朋友关注\"\n        },\n        {\n            id: 3,\n            title: \"超级工程：跨海大桥建设全过程\",\n            author: \"桥梁专家\",\n            authorAvatar: \"🌉\",\n            verified: true,\n            description: \"🌊 见证跨海大桥从设计到建成的震撼过程，工程技术的巅峰之作。\",\n            videoUrl: \"/api/placeholder/400/700\",\n            likes: 234000,\n            comments: 156000,\n            shares: 78000,\n            tags: [\n                \"#超级工程\",\n                \"#跨海大桥\",\n                \"#建筑奇迹\"\n            ],\n            location: \"广东省珠海市\",\n            music: \"交响乐 - 建设者之歌\",\n            videoTime: \"15:20\",\n            friendCount: \"8个朋友关注\"\n        }\n    ];\n    // 朋友视频内容数据\n    const friendsContent = [\n        {\n            id: 1,\n            title: \"我家装修日记第30天，终于看到希望了！\",\n            author: \"装修小白\",\n            authorAvatar: \"🏠\",\n            verified: false,\n            description: \"💪 历时一个月的装修终于有起色了，分享一下心得体会！\",\n            videoUrl: \"/api/placeholder/400/700\",\n            likes: 12000,\n            comments: 890,\n            shares: 456,\n            tags: [\n                \"#装修日记\",\n                \"#家居设计\",\n                \"#生活分享\"\n            ],\n            location: \"北京市朝阳区\",\n            music: \"轻音乐 - 温馨家园\",\n            videoTime: \"06:30\",\n            friendCount: \"5个朋友关注\"\n        },\n        {\n            id: 2,\n            title: \"周末和朋友一起DIY小花园\",\n            author: \"园艺爱好者\",\n            authorAvatar: \"🌱\",\n            verified: false,\n            description: \"🌸 和好朋友一起打造小花园，享受美好的周末时光！\",\n            videoUrl: \"/api/placeholder/400/700\",\n            likes: 8900,\n            comments: 567,\n            shares: 234,\n            tags: [\n                \"#DIY花园\",\n                \"#周末时光\",\n                \"#朋友聚会\"\n            ],\n            location: \"上海市徐汇区\",\n            music: \"田园音乐 - 春天的故事\",\n            videoTime: \"09:15\",\n            friendCount: \"12个朋友关注\"\n        },\n        {\n            id: 3,\n            title: \"爸爸亲手给我做的小木屋完工啦！\",\n            author: \"小小建筑师\",\n            authorAvatar: \"🏘️\",\n            verified: false,\n            description: \"👨‍👧 爸爸用了两个月时间给我做的小木屋终于完工了，太感动了！\",\n            videoUrl: \"/api/placeholder/400/700\",\n            likes: 45000,\n            comments: 2300,\n            shares: 1200,\n            tags: [\n                \"#父女情深\",\n                \"#手工制作\",\n                \"#小木屋\"\n            ],\n            location: \"成都市武侯区\",\n            music: \"温情音乐 - 父爱如山\",\n            videoTime: \"11:40\",\n            friendCount: \"18个朋友关注\"\n        }\n    ];\n    // 根据当前标签获取对应的内容\n    const getCurrentContent = ()=>{\n        switch(activeTab){\n            case '推荐':\n                return recommendedContent;\n            case '关注':\n                return followingContent;\n            case '朋友':\n                return friendsContent;\n            default:\n                return recommendedContent;\n        }\n    };\n    const videoContent = getCurrentContent();\n    const currentContent = videoContent[currentIndex];\n    // 格式化数字\n    const formatNumber = (num)=>{\n        if (num >= 10000) {\n            return (num / 10000).toFixed(1) + 'w';\n        } else if (num >= 1000) {\n            return (num / 1000).toFixed(1) + 'k';\n        }\n        return num.toString();\n    };\n    // 格式化时间\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    // 交互处理函数\n    const handleLike = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleLike]\": ()=>setIsLiked(!isLiked)\n    }[\"WeChatVideoHomepage.useCallback[handleLike]\"], [\n        isLiked\n    ]);\n    const handleFollow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleFollow]\": ()=>setIsFollowing(!isFollowing)\n    }[\"WeChatVideoHomepage.useCallback[handleFollow]\"], [\n        isFollowing\n    ]);\n    const handleComment = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleComment]\": ()=>setShowComments(true)\n    }[\"WeChatVideoHomepage.useCallback[handleComment]\"], []);\n    const handleShare = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleShare]\": ()=>setShowShare(true)\n    }[\"WeChatVideoHomepage.useCallback[handleShare]\"], []);\n    const togglePlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[togglePlay]\": ()=>setIsPlaying(!isPlaying)\n    }[\"WeChatVideoHomepage.useCallback[togglePlay]\"], [\n        isPlaying\n    ]);\n    // 标签切换处理函数\n    const handleTabChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleTabChange]\": (tab)=>{\n            setActiveTab(tab);\n            setCurrentIndex(0); // 切换标签时重置到第一个视频\n            setIsLiked(false);\n            setIsFollowing(false);\n            setIsPlaying(true);\n            setCurrentTime(0);\n        }\n    }[\"WeChatVideoHomepage.useCallback[handleTabChange]\"], []);\n    // 视频切换函数\n    const nextVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[nextVideo]\": ()=>{\n            setCurrentIndex({\n                \"WeChatVideoHomepage.useCallback[nextVideo]\": (prev)=>(prev + 1) % videoContent.length\n            }[\"WeChatVideoHomepage.useCallback[nextVideo]\"]);\n            setIsLiked(false);\n            setIsFollowing(false);\n            setIsPlaying(true);\n            setCurrentTime(0);\n        }\n    }[\"WeChatVideoHomepage.useCallback[nextVideo]\"], [\n        videoContent.length\n    ]);\n    const prevVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[prevVideo]\": ()=>{\n            setCurrentIndex({\n                \"WeChatVideoHomepage.useCallback[prevVideo]\": (prev)=>(prev - 1 + videoContent.length) % videoContent.length\n            }[\"WeChatVideoHomepage.useCallback[prevVideo]\"]);\n            setIsLiked(false);\n            setIsFollowing(false);\n            setIsPlaying(true);\n            setCurrentTime(0);\n        }\n    }[\"WeChatVideoHomepage.useCallback[prevVideo]\"], [\n        videoContent.length\n    ]);\n    // 触摸手势处理\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientY);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientY);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isUpSwipe = distance > 50;\n        const isDownSwipe = distance < -50;\n        if (isUpSwipe) {\n            nextVideo();\n        }\n        if (isDownSwipe) {\n            prevVideo();\n        }\n    };\n    // 自动播放进度\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeChatVideoHomepage.useEffect\": ()=>{\n            if (isPlaying) {\n                const timer = setInterval({\n                    \"WeChatVideoHomepage.useEffect.timer\": ()=>{\n                        setCurrentTime({\n                            \"WeChatVideoHomepage.useEffect.timer\": (prev)=>{\n                                if (prev >= duration) {\n                                    nextVideo();\n                                    return 0;\n                                }\n                                return prev + 1;\n                            }\n                        }[\"WeChatVideoHomepage.useEffect.timer\"]);\n                    }\n                }[\"WeChatVideoHomepage.useEffect.timer\"], 1000);\n                return ({\n                    \"WeChatVideoHomepage.useEffect\": ()=>clearInterval(timer)\n                })[\"WeChatVideoHomepage.useEffect\"];\n            }\n        }\n    }[\"WeChatVideoHomepage.useEffect\"], [\n        isPlaying,\n        duration,\n        nextVideo\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-gray-800 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pt-20 pb-32\",\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                onClick: togglePlay,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-full relative px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full bg-gray-700 rounded-2xl overflow-hidden relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-gray-600 via-gray-700 to-gray-800\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-b from-gray-500/20 to-gray-900/60\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center text-white\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-32 h-32 bg-orange-500/30 rounded-full flex items-center justify-center mb-4 mx-auto backdrop-blur-sm border-2 border-orange-400/50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-5xl\",\n                                                                children: currentContent.authorAvatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-black/40 backdrop-blur-sm rounded-lg p-4 mx-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-bold mb-2\",\n                                                                    children: currentContent.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm opacity-90\",\n                                                                    children: currentContent.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-20 left-1/4 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-32 right-1/3 w-24 h-24 bg-white/5 rounded-full blur-lg animate-pulse delay-1000\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, this),\n                            !isPlaying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center justify-center z-20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-white/30 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-0 h-0 border-l-[20px] border-l-white border-y-[12px] border-y-transparent ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center px-4 pt-12 pb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange('推荐'),\n                                    className: \"text-lg font-medium transition-all duration-200 relative \".concat(activeTab === '推荐' ? 'text-white' : 'text-white/70'),\n                                    children: [\n                                        \"推荐\",\n                                        activeTab === '推荐' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange('关注'),\n                                    className: \"text-lg font-medium transition-all duration-200 relative \".concat(activeTab === '关注' ? 'text-white' : 'text-white/70'),\n                                    children: [\n                                        \"关注\",\n                                        activeTab === '关注' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange('朋友'),\n                                    className: \"text-lg font-medium transition-all duration-200 flex items-center space-x-1 relative \".concat(activeTab === '朋友' ? 'text-white' : 'text-white/70'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"朋友\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500 text-sm\",\n                                            children: \"♥\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeTab === '朋友' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute right-4 top-12 flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-8 h-8 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-white\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-8 h-8 flex items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-white rounded-full mx-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-16 right-4 bg-black/60 backdrop-blur-sm rounded-lg px-2 py-1 z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-white text-sm font-medium\",\n                    children: currentContent.videoTime\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 412,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-20 left-0 right-0 z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 border-t border-gray-700 px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 rounded-full overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-lg\",\n                                                children: currentContent.authorAvatar\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-medium text-base\",\n                                                        children: currentContent.author\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    currentContent.verified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-xs\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300 text-sm\",\n                                                children: currentContent.friendCount\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShare,\n                                        className: \"flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-white\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-xs\",\n                                                children: formatNumber(currentContent.shares)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                        onClick: handleLike,\n                                        whileTap: {\n                                            scale: 0.8\n                                        },\n                                        className: \"flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                    animate: isLiked ? {\n                                                        scale: [\n                                                            1,\n                                                            1.2,\n                                                            1\n                                                        ]\n                                                    } : {},\n                                                    transition: {\n                                                        duration: 0.3\n                                                    },\n                                                    children: isLiked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-red-500\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-white\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-xs\",\n                                                children: formatNumber(currentContent.likes + (isLiked ? 1 : 0))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleComment,\n                                        className: \"flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-xs\",\n                                                children: formatNumber(currentContent.comments)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 418,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 417,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: showComments && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black/50 z-50 flex items-end\",\n                    onClick: ()=>setShowComments(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            y: '100%'\n                        },\n                        animate: {\n                            y: 0\n                        },\n                        exit: {\n                            y: '100%'\n                        },\n                        className: \"w-full bg-white rounded-t-3xl max-h-[70vh] overflow-hidden\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 border-b border-gray-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: [\n                                            formatNumber(currentContent.comments),\n                                            \"条评论\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowComments(false),\n                                        className: \"w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500\",\n                                            children: \"✕\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                                children: (()=>{\n                                    // 根据当前标签和视频内容显示不同的评论\n                                    const getComments = ()=>{\n                                        if (activeTab === '推荐') {\n                                            return [\n                                                {\n                                                    user: '建筑学教授',\n                                                    avatar: '👨‍🏫',\n                                                    content: '这种超高层建筑的施工技术确实令人震撼！',\n                                                    time: '1分钟前',\n                                                    likes: 2580\n                                                },\n                                                {\n                                                    user: '结构工程师',\n                                                    avatar: '🏗️',\n                                                    content: '钢结构的连接工艺太精湛了，学到了很多',\n                                                    time: '3分钟前',\n                                                    likes: 1890\n                                                },\n                                                {\n                                                    user: '建筑爱好者',\n                                                    avatar: '🏢',\n                                                    content: '人类的建筑智慧真是无穷无尽',\n                                                    time: '5分钟前',\n                                                    likes: 1567\n                                                },\n                                                {\n                                                    user: '工程管理师',\n                                                    avatar: '📋',\n                                                    content: '项目管理的复杂度可想而知',\n                                                    time: '8分钟前',\n                                                    likes: 1245\n                                                },\n                                                {\n                                                    user: '安全专家',\n                                                    avatar: '🦺',\n                                                    content: '高空作业的安全措施做得很到位',\n                                                    time: '12分钟前',\n                                                    likes: 934\n                                                }\n                                            ];\n                                        } else if (activeTab === '关注') {\n                                            return [\n                                                {\n                                                    user: '工程师老李',\n                                                    avatar: '👷',\n                                                    content: '这个创意太有趣了，邻居举报是因为嫉妒吧哈哈',\n                                                    time: '2分钟前',\n                                                    likes: 1280\n                                                },\n                                                {\n                                                    user: '建筑设计师',\n                                                    avatar: '🏗️',\n                                                    content: '龙猫烤炉的设计很有想象力，但要注意安全规范',\n                                                    time: '5分钟前',\n                                                    likes: 890\n                                                },\n                                                {\n                                                    user: '邻居大妈',\n                                                    avatar: '👵',\n                                                    content: '确实有点吵，但是很有创意，支持！',\n                                                    time: '8分钟前',\n                                                    likes: 567\n                                                },\n                                                {\n                                                    user: '工地小王',\n                                                    avatar: '⛑️',\n                                                    content: '哈哈哈，我们工地也想搞一个',\n                                                    time: '10分钟前',\n                                                    likes: 345\n                                                },\n                                                {\n                                                    user: '安全监督员',\n                                                    avatar: '🦺',\n                                                    content: '创意不错，但要符合建筑安全标准哦',\n                                                    time: '15分钟前',\n                                                    likes: 234\n                                                }\n                                            ];\n                                        } else {\n                                            return [\n                                                {\n                                                    user: '装修达人',\n                                                    avatar: '🔨',\n                                                    content: '装修真的是个体力活，加油！',\n                                                    time: '1分钟前',\n                                                    likes: 89\n                                                },\n                                                {\n                                                    user: '邻居小张',\n                                                    avatar: '👨',\n                                                    content: '我家也在装修，互相学习一下',\n                                                    time: '3分钟前',\n                                                    likes: 67\n                                                },\n                                                {\n                                                    user: '设计师朋友',\n                                                    avatar: '🎨',\n                                                    content: '颜色搭配很不错，有品味！',\n                                                    time: '5分钟前',\n                                                    likes: 45\n                                                },\n                                                {\n                                                    user: '妈妈',\n                                                    avatar: '👩',\n                                                    content: '儿子加油，妈妈支持你！',\n                                                    time: '8分钟前',\n                                                    likes: 123\n                                                },\n                                                {\n                                                    user: '室友',\n                                                    avatar: '👫',\n                                                    content: '终于要完工了，期待新家！',\n                                                    time: '10分钟前',\n                                                    likes: 34\n                                                }\n                                            ];\n                                        }\n                                    };\n                                    return getComments().map((comment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: comment.avatar\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 566,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: comment.user\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                    lineNumber: 570,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-500 text-sm\",\n                                                                    children: comment.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700 mb-2\",\n                                                            children: comment.content\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"flex items-center space-x-1 text-gray-500 hover:text-red-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: \"❤️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                            lineNumber: 576,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: comment.likes\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                            lineNumber: 577,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                    lineNumber: 575,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-gray-500 hover:text-blue-500 text-sm\",\n                                                                    children: \"回复\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                    lineNumber: 579,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 21\n                                        }, this));\n                                })()\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t border-gray-100 bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full bg-gradient-to-r from-orange-500 to-red-500 flex items-center justify-center flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm\",\n                                                children: \"我\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 591,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"说点什么...\",\n                                                    className: \"flex-1 bg-white border border-gray-200 rounded-full px-4 py-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-blue-500 hover:bg-blue-600 rounded-full px-6 py-2 text-white font-medium\",\n                                                    children: \"发送\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 505,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 503,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: showShare && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black/50 z-50 flex items-end\",\n                    onClick: ()=>setShowShare(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            y: '100%'\n                        },\n                        animate: {\n                            y: 0\n                        },\n                        exit: {\n                            y: '100%'\n                        },\n                        className: \"w-full bg-white rounded-t-3xl p-6\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"分享到\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 628,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowShare(false),\n                                        className: \"w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500\",\n                                            children: \"✕\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 627,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-4 gap-6 mb-8\",\n                                children: [\n                                    {\n                                        name: '微信好友',\n                                        icon: '💬',\n                                        color: 'bg-green-500'\n                                    },\n                                    {\n                                        name: '朋友圈',\n                                        icon: '🌟',\n                                        color: 'bg-green-600'\n                                    },\n                                    {\n                                        name: '工程群',\n                                        icon: '👷',\n                                        color: 'bg-orange-500'\n                                    },\n                                    {\n                                        name: '项目组',\n                                        icon: '🏗️',\n                                        color: 'bg-blue-600'\n                                    },\n                                    {\n                                        name: '微博',\n                                        icon: '📱',\n                                        color: 'bg-red-500'\n                                    },\n                                    {\n                                        name: '抖音',\n                                        icon: '🎵',\n                                        color: 'bg-black'\n                                    },\n                                    {\n                                        name: '复制链接',\n                                        icon: '🔗',\n                                        color: 'bg-gray-600'\n                                    },\n                                    {\n                                        name: '保存视频',\n                                        icon: '📥',\n                                        color: 'bg-purple-500'\n                                    }\n                                ].map((platform, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"flex flex-col items-center space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-14 h-14 \".concat(platform.color, \" rounded-2xl flex items-center justify-center\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xl\",\n                                                    children: platform.icon\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700 text-sm text-center\",\n                                                children: platform.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 637,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowShare(false),\n                                className: \"w-full py-4 bg-gray-100 hover:bg-gray-200 rounded-2xl text-gray-700 font-medium\",\n                                children: \"取消\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 620,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 613,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 611,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n        lineNumber: 296,\n        columnNumber: 5\n    }, this);\n}\n_s(WeChatVideoHomepage, \"SnbUFPB3GMwLqUrSBAyCZZAw6kI=\");\n_c = WeChatVideoHomepage;\nvar _c;\n$RefreshReg$(_c, \"WeChatVideoHomepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/wechat-video-homepage.tsx\n"));

/***/ })

});