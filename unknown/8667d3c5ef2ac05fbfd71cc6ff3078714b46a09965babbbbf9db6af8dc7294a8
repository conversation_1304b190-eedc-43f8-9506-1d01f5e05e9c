"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/wechat-video-homepage.tsx":
/*!*******************************************!*\
  !*** ./src/app/wechat-video-homepage.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WeChatVideoHomepage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction WeChatVideoHomepage() {\n    _s();\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFollowing, setIsFollowing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showComments, setShowComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showShare, setShowShare] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('推荐');\n    const [showControls, setShowControls] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 视频内容数据\n    const videoContent = [\n        {\n            id: 1,\n            title: \"震撼！全球最高摩天大楼建设纪录片\",\n            author: \"建筑奇迹\",\n            authorAvatar: \"🏢\",\n            verified: true,\n            description: \"🏗️ 见证828米迪拜塔的建设全过程，工程技术的巅峰之作！\",\n            videoUrl: \"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4\",\n            likes: 1200000,\n            comments: 89000,\n            shares: 156000,\n            videoTime: \"12:30\",\n            friendCount: \"1个朋友关注\"\n        },\n        {\n            id: 2,\n            title: \"AI机器人自动化建房，24小时完工！\",\n            author: \"未来建筑师\",\n            authorAvatar: \"🤖\",\n            verified: true,\n            description: \"🚀 革命性的3D打印建筑技术，机器人24小时自动建房全过程！\",\n            videoUrl: \"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4\",\n            likes: 890000,\n            comments: 67000,\n            shares: 123000,\n            videoTime: \"08:45\",\n            friendCount: \"5个朋友关注\"\n        },\n        {\n            id: 3,\n            title: \"我家装修日记30天，终于看到希望了！\",\n            author: \"装修小白\",\n            authorAvatar: \"🏠\",\n            verified: false,\n            description: \"💪 历时一个月的装修终于有起色了，分享一下踩过的坑！\",\n            videoUrl: \"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4\",\n            likes: 456000,\n            comments: 23000,\n            shares: 78000,\n            videoTime: \"06:30\",\n            friendCount: \"5个朋友关注\"\n        }\n    ];\n    const currentContent = videoContent[currentIndex];\n    // 格式化数字\n    const formatNumber = (num)=>{\n        if (num >= 10000) {\n            return (num / 10000).toFixed(1) + 'w';\n        }\n        return num.toString();\n    };\n    // 交互处理函数\n    const handleLike = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleLike]\": ()=>setIsLiked(!isLiked)\n    }[\"WeChatVideoHomepage.useCallback[handleLike]\"], [\n        isLiked\n    ]);\n    const handleComment = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleComment]\": ()=>setShowComments(true)\n    }[\"WeChatVideoHomepage.useCallback[handleComment]\"], []);\n    const handleShare = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleShare]\": ()=>setShowShare(true)\n    }[\"WeChatVideoHomepage.useCallback[handleShare]\"], []);\n    const togglePlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[togglePlay]\": ()=>{\n            if (videoRef.current) {\n                if (isPlaying) {\n                    videoRef.current.pause();\n                } else {\n                    videoRef.current.play();\n                }\n                setIsPlaying(!isPlaying);\n            }\n        }\n    }[\"WeChatVideoHomepage.useCallback[togglePlay]\"], [\n        isPlaying\n    ]);\n    // 点击视频显示控制界面\n    const handleVideoClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleVideoClick]\": ()=>{\n            setShowControls(!showControls);\n            // 3秒后自动隐藏控制界面\n            if (!showControls) {\n                setTimeout({\n                    \"WeChatVideoHomepage.useCallback[handleVideoClick]\": ()=>{\n                        setShowControls(false);\n                    }\n                }[\"WeChatVideoHomepage.useCallback[handleVideoClick]\"], 3000);\n            }\n        }\n    }[\"WeChatVideoHomepage.useCallback[handleVideoClick]\"], [\n        showControls\n    ]);\n    // 格式化时间\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = Math.floor(seconds % 60);\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    // 视频播放控制\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeChatVideoHomepage.useEffect\": ()=>{\n            if (videoRef.current) {\n                if (isPlaying) {\n                    videoRef.current.play();\n                } else {\n                    videoRef.current.pause();\n                }\n            }\n        }\n    }[\"WeChatVideoHomepage.useEffect\"], [\n        currentIndex,\n        isPlaying\n    ]);\n    // 视频时间更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeChatVideoHomepage.useEffect\": ()=>{\n            const video = videoRef.current;\n            if (!video) return;\n            const updateTime = {\n                \"WeChatVideoHomepage.useEffect.updateTime\": ()=>{\n                    setCurrentTime(video.currentTime);\n                    setProgress(video.currentTime / video.duration * 100);\n                }\n            }[\"WeChatVideoHomepage.useEffect.updateTime\"];\n            const updateDuration = {\n                \"WeChatVideoHomepage.useEffect.updateDuration\": ()=>{\n                    setDuration(video.duration);\n                }\n            }[\"WeChatVideoHomepage.useEffect.updateDuration\"];\n            video.addEventListener('timeupdate', updateTime);\n            video.addEventListener('loadedmetadata', updateDuration);\n            return ({\n                \"WeChatVideoHomepage.useEffect\": ()=>{\n                    video.removeEventListener('timeupdate', updateTime);\n                    video.removeEventListener('loadedmetadata', updateDuration);\n                }\n            })[\"WeChatVideoHomepage.useEffect\"];\n        }\n    }[\"WeChatVideoHomepage.useEffect\"], [\n        currentIndex\n    ]);\n    // 标签切换处理函数\n    const handleTabChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleTabChange]\": (tab)=>{\n            setActiveTab(tab);\n            setCurrentIndex(0);\n            setIsLiked(false);\n            setIsFollowing(false);\n            setIsPlaying(true);\n        }\n    }[\"WeChatVideoHomepage.useCallback[handleTabChange]\"], []);\n    // 视频切换函数\n    const nextVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[nextVideo]\": ()=>{\n            setCurrentIndex({\n                \"WeChatVideoHomepage.useCallback[nextVideo]\": (prev)=>(prev + 1) % videoContent.length\n            }[\"WeChatVideoHomepage.useCallback[nextVideo]\"]);\n            setIsLiked(false);\n            setIsFollowing(false);\n            setIsPlaying(true);\n        }\n    }[\"WeChatVideoHomepage.useCallback[nextVideo]\"], [\n        videoContent.length\n    ]);\n    const prevVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[prevVideo]\": ()=>{\n            setCurrentIndex({\n                \"WeChatVideoHomepage.useCallback[prevVideo]\": (prev)=>(prev - 1 + videoContent.length) % videoContent.length\n            }[\"WeChatVideoHomepage.useCallback[prevVideo]\"]);\n            setIsLiked(false);\n            setIsFollowing(false);\n            setIsPlaying(true);\n        }\n    }[\"WeChatVideoHomepage.useCallback[prevVideo]\"], [\n        videoContent.length\n    ]);\n    // 触摸手势处理\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientY);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientY);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isUpSwipe = distance > 50;\n        const isDownSwipe = distance < -50;\n        if (isUpSwipe) {\n            nextVideo();\n        }\n        if (isDownSwipe) {\n            prevVideo();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                mode: \"wait\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        y: 100,\n                        opacity: 0\n                    },\n                    animate: {\n                        y: 0,\n                        opacity: 1\n                    },\n                    exit: {\n                        y: -100,\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.3,\n                        ease: \"easeInOut\"\n                    },\n                    className: \"absolute inset-0\",\n                    onTouchStart: handleTouchStart,\n                    onTouchMove: handleTouchMove,\n                    onTouchEnd: handleTouchEnd,\n                    onClick: handleVideoClick,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                ref: videoRef,\n                                src: currentContent.videoUrl,\n                                className: \"w-full h-full object-cover\",\n                                loop: true,\n                                muted: true,\n                                playsInline: true,\n                                autoPlay: true,\n                                onLoadedData: ()=>{\n                                    if (videoRef.current && isPlaying) {\n                                        videoRef.current.play();\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this),\n                        !isPlaying && !showControls && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center z-20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 bg-white/30 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-0 h-0 border-l-[20px] border-l-white border-y-[12px] border-y-transparent ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                            children: showControls && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                exit: {\n                                    opacity: 0\n                                },\n                                transition: {\n                                    duration: 0.2\n                                },\n                                className: \"absolute inset-0 bg-black/50 flex flex-col justify-between z-30\",\n                                onClick: (e)=>e.stopPropagation(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: togglePlay,\n                                            className: \"w-16 h-16 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center backdrop-blur-sm transition-all duration-200 hover:scale-110\",\n                                            children: isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1.5 h-6 bg-white/50 hover:bg-white transition-colors duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1.5 h-6 bg-white/50 hover:bg-white transition-colors duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-0 h-0 border-l-[16px] border-l-white/50 hover:border-l-white border-y-[10px] border-y-transparent ml-1 transition-colors duration-200\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 pb-8 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-1 bg-white/20 rounded-full overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-full bg-white/50 transition-all duration-200\",\n                                                            style: {\n                                                                width: \"\".concat(progress, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-white/50 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: formatTime(currentTime)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: formatTime(duration)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"flex flex-col items-center space-y-2 transition-all duration-200 hover:scale-110\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center backdrop-blur-sm transition-all duration-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-6 h-6 text-white/50 hover:text-white transition-colors duration-200\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                        lineNumber: 289,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/50 text-xs\",\n                                                                children: \"私密投票\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"flex flex-col items-center space-y-2 transition-all duration-200 hover:scale-110\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-r from-purple-500/50 to-pink-500/50 hover:from-purple-500 hover:to-pink-500 rounded-full flex items-center justify-center backdrop-blur-sm transition-all duration-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-6 h-6 text-white/50 hover:text-white transition-colors duration-200\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/50 text-xs\",\n                                                                children: \"购买NFT\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, currentIndex, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 left-0 right-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center px-4 pt-12 pb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange('推荐'),\n                                    className: \"text-lg font-medium transition-all duration-200 relative \".concat(activeTab === '推荐' ? 'text-white' : 'text-white/50'),\n                                    children: [\n                                        \"推荐\",\n                                        activeTab === '推荐' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange('关注'),\n                                    className: \"text-lg font-medium transition-all duration-200 relative \".concat(activeTab === '关注' ? 'text-white' : 'text-white/50'),\n                                    children: [\n                                        \"关注\",\n                                        activeTab === '关注' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange('朋友'),\n                                    className: \"text-lg font-medium transition-all duration-200 flex items-center space-x-1 relative \".concat(activeTab === '朋友' ? 'text-white' : 'text-white/50'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"朋友\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500/50 text-sm\",\n                                            children: \"♥\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeTab === '朋友' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute right-4 top-12 flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-8 h-8 flex items-center justify-center transition-colors duration-200 hover:bg-white/20 rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-white/50 hover:text-white transition-colors duration-200\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-8 h-8 flex items-center justify-center transition-colors duration-200 hover:bg-white/20 rounded-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-white/50 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-white/50 rounded-full mx-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-white/50 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-16 right-4 bg-black/60 backdrop-blur-sm rounded-lg px-2 py-1 z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-white text-sm font-medium\",\n                    children: currentContent.videoTime\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-20 left-0 right-0 z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 rounded-full overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-lg\",\n                                                children: currentContent.authorAvatar\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white/50 font-medium text-base\",\n                                                        children: currentContent.author\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    currentContent.verified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 bg-blue-500/50 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-xs\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/30 text-sm\",\n                                                children: currentContent.friendCount\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShare,\n                                        className: \"flex flex-col items-center transition-all duration-200 hover:scale-110\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-white/50 hover:text-white transition-colors duration-200\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/50 text-xs\",\n                                                children: formatNumber(currentContent.shares)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                        onClick: handleLike,\n                                        whileTap: {\n                                            scale: 0.8\n                                        },\n                                        className: \"flex flex-col items-center transition-all duration-200 hover:scale-110\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                    animate: isLiked ? {\n                                                        scale: [\n                                                            1,\n                                                            1.2,\n                                                            1\n                                                        ]\n                                                    } : {},\n                                                    transition: {\n                                                        duration: 0.3\n                                                    },\n                                                    children: isLiked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-red-500\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-white/50 hover:text-white transition-colors duration-200\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/50 text-xs\",\n                                                children: formatNumber(currentContent.likes + (isLiked ? 1 : 0))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleComment,\n                                        className: \"flex flex-col items-center transition-all duration-200 hover:scale-110\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-white/50 hover:text-white transition-colors duration-200\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/50 text-xs\",\n                                                children: formatNumber(currentContent.comments)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 375,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 374,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, this);\n}\n_s(WeChatVideoHomepage, \"1laIHd8pwMh+KMUxsZVA0G/K+Jc=\");\n_c = WeChatVideoHomepage;\nvar _c;\n$RefreshReg$(_c, \"WeChatVideoHomepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/wechat-video-homepage.tsx\n"));

/***/ })

});