"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/wechat-video-homepage.tsx":
/*!*******************************************!*\
  !*** ./src/app/wechat-video-homepage.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WeChatVideoHomepage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _contexts_UIContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/UIContext */ \"(app-pages-browser)/./src/contexts/UIContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction WeChatVideoHomepage() {\n    _s();\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFollowing, setIsFollowing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showComments, setShowComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showShare, setShowShare] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('推荐');\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showButtons, setShowButtons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { showControls, setShowControls } = (0,_contexts_UIContext__WEBPACK_IMPORTED_MODULE_2__.useUI)();\n    const autoHideTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 视频内容数据\n    const videoContent = [\n        {\n            id: 1,\n            title: \"震撼！全球最高摩天大楼建设纪录片\",\n            author: \"建筑奇迹\",\n            authorAvatar: \"🏢\",\n            verified: true,\n            description: \"🏗️ 见证828米迪拜塔的建设全过程，工程技术的巅峰之作！\",\n            videoUrl: \"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4\",\n            likes: 1200000,\n            comments: 89000,\n            shares: 156000,\n            videoTime: \"12:30\",\n            friendCount: \"1个朋友关注\"\n        },\n        {\n            id: 2,\n            title: \"AI机器人自动化建房，24小时完工！\",\n            author: \"未来建筑师\",\n            authorAvatar: \"🤖\",\n            verified: true,\n            description: \"🚀 革命性的3D打印建筑技术，机器人24小时自动建房全过程！\",\n            videoUrl: \"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4\",\n            likes: 890000,\n            comments: 67000,\n            shares: 123000,\n            videoTime: \"08:45\",\n            friendCount: \"5个朋友关注\"\n        },\n        {\n            id: 3,\n            title: \"我家装修日记30天，终于看到希望了！\",\n            author: \"装修小白\",\n            authorAvatar: \"🏠\",\n            verified: false,\n            description: \"💪 历时一个月的装修终于有起色了，分享一下踩过的坑！\",\n            videoUrl: \"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4\",\n            likes: 456000,\n            comments: 23000,\n            shares: 78000,\n            videoTime: \"06:30\",\n            friendCount: \"5个朋友关注\"\n        }\n    ];\n    const currentContent = videoContent[currentIndex];\n    // 格式化数字\n    const formatNumber = (num)=>{\n        if (num >= 10000) {\n            return (num / 10000).toFixed(1) + 'w';\n        }\n        return num.toString();\n    };\n    // 交互处理函数\n    const handleLike = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleLike]\": ()=>setIsLiked(!isLiked)\n    }[\"WeChatVideoHomepage.useCallback[handleLike]\"], [\n        isLiked\n    ]);\n    const handleComment = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleComment]\": ()=>setShowComments(true)\n    }[\"WeChatVideoHomepage.useCallback[handleComment]\"], []);\n    const handleShare = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleShare]\": ()=>setShowShare(true)\n    }[\"WeChatVideoHomepage.useCallback[handleShare]\"], []);\n    const togglePlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[togglePlay]\": ()=>{\n            if (videoRef.current) {\n                if (isPlaying) {\n                    videoRef.current.pause();\n                } else {\n                    videoRef.current.play();\n                }\n                setIsPlaying(!isPlaying);\n            }\n        }\n    }[\"WeChatVideoHomepage.useCallback[togglePlay]\"], [\n        isPlaying\n    ]);\n    // 点击视频显示/隐藏控制界面\n    const handleVideoClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleVideoClick]\": ()=>{\n            setShowControls(!showControls);\n            // 如果显示控制界面，重置按钮显示状态并启动3秒自动隐藏按钮的计时器\n            if (!showControls) {\n                setShowButtons(true);\n                // 清除之前的计时器\n                if (autoHideTimeoutRef.current) {\n                    clearTimeout(autoHideTimeoutRef.current);\n                }\n                // 3秒后自动隐藏按钮\n                autoHideTimeoutRef.current = setTimeout({\n                    \"WeChatVideoHomepage.useCallback[handleVideoClick]\": ()=>{\n                        setShowButtons(false);\n                    }\n                }[\"WeChatVideoHomepage.useCallback[handleVideoClick]\"], 3000);\n            } else {\n                // 隐藏控制界面时清除计时器\n                if (autoHideTimeoutRef.current) {\n                    clearTimeout(autoHideTimeoutRef.current);\n                    autoHideTimeoutRef.current = null;\n                }\n            }\n        }\n    }[\"WeChatVideoHomepage.useCallback[handleVideoClick]\"], [\n        showControls,\n        setShowControls\n    ]);\n    // 清理计时器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeChatVideoHomepage.useEffect\": ()=>{\n            return ({\n                \"WeChatVideoHomepage.useEffect\": ()=>{\n                    if (autoHideTimeoutRef.current) {\n                        clearTimeout(autoHideTimeoutRef.current);\n                    }\n                }\n            })[\"WeChatVideoHomepage.useEffect\"];\n        }\n    }[\"WeChatVideoHomepage.useEffect\"], []);\n    // 格式化时间\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = Math.floor(seconds % 60);\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    // 视频播放控制\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeChatVideoHomepage.useEffect\": ()=>{\n            if (videoRef.current) {\n                if (isPlaying) {\n                    videoRef.current.play();\n                } else {\n                    videoRef.current.pause();\n                }\n            }\n        }\n    }[\"WeChatVideoHomepage.useEffect\"], [\n        currentIndex,\n        isPlaying\n    ]);\n    // 视频时间更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeChatVideoHomepage.useEffect\": ()=>{\n            const video = videoRef.current;\n            if (!video) return;\n            const updateTime = {\n                \"WeChatVideoHomepage.useEffect.updateTime\": ()=>{\n                    setCurrentTime(video.currentTime);\n                    setProgress(video.currentTime / video.duration * 100);\n                }\n            }[\"WeChatVideoHomepage.useEffect.updateTime\"];\n            const updateDuration = {\n                \"WeChatVideoHomepage.useEffect.updateDuration\": ()=>{\n                    setDuration(video.duration);\n                }\n            }[\"WeChatVideoHomepage.useEffect.updateDuration\"];\n            video.addEventListener('timeupdate', updateTime);\n            video.addEventListener('loadedmetadata', updateDuration);\n            return ({\n                \"WeChatVideoHomepage.useEffect\": ()=>{\n                    video.removeEventListener('timeupdate', updateTime);\n                    video.removeEventListener('loadedmetadata', updateDuration);\n                }\n            })[\"WeChatVideoHomepage.useEffect\"];\n        }\n    }[\"WeChatVideoHomepage.useEffect\"], [\n        currentIndex\n    ]);\n    // 标签切换处理函数\n    const handleTabChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleTabChange]\": (tab)=>{\n            setActiveTab(tab);\n            setCurrentIndex(0);\n            setIsLiked(false);\n            setIsFollowing(false);\n            setIsPlaying(true);\n        }\n    }[\"WeChatVideoHomepage.useCallback[handleTabChange]\"], []);\n    // 视频切换函数\n    const nextVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[nextVideo]\": ()=>{\n            setCurrentIndex({\n                \"WeChatVideoHomepage.useCallback[nextVideo]\": (prev)=>(prev + 1) % videoContent.length\n            }[\"WeChatVideoHomepage.useCallback[nextVideo]\"]);\n            setIsLiked(false);\n            setIsFollowing(false);\n            setIsPlaying(true);\n        }\n    }[\"WeChatVideoHomepage.useCallback[nextVideo]\"], [\n        videoContent.length\n    ]);\n    const prevVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[prevVideo]\": ()=>{\n            setCurrentIndex({\n                \"WeChatVideoHomepage.useCallback[prevVideo]\": (prev)=>(prev - 1 + videoContent.length) % videoContent.length\n            }[\"WeChatVideoHomepage.useCallback[prevVideo]\"]);\n            setIsLiked(false);\n            setIsFollowing(false);\n            setIsPlaying(true);\n        }\n    }[\"WeChatVideoHomepage.useCallback[prevVideo]\"], [\n        videoContent.length\n    ]);\n    // 触摸手势处理\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientY);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientY);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isUpSwipe = distance > 50;\n        const isDownSwipe = distance < -50;\n        if (isUpSwipe) {\n            nextVideo();\n        }\n        if (isDownSwipe) {\n            prevVideo();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                mode: \"wait\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        y: 100,\n                        opacity: 0\n                    },\n                    animate: {\n                        y: 0,\n                        opacity: 1\n                    },\n                    exit: {\n                        y: -100,\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.3,\n                        ease: \"easeInOut\"\n                    },\n                    className: \"absolute inset-0\",\n                    onTouchStart: handleTouchStart,\n                    onTouchMove: handleTouchMove,\n                    onTouchEnd: handleTouchEnd,\n                    onClick: handleVideoClick,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                ref: videoRef,\n                                src: currentContent.videoUrl,\n                                className: \"w-full h-full object-cover\",\n                                loop: true,\n                                muted: true,\n                                playsInline: true,\n                                autoPlay: true,\n                                onLoadedData: ()=>{\n                                    if (videoRef.current && isPlaying) {\n                                        videoRef.current.play();\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this),\n                        !isPlaying && !showControls && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center z-20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 bg-white/30 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-0 h-0 border-l-[20px] border-l-white border-y-[12px] border-y-transparent ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                            children: showControls && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                exit: {\n                                    opacity: 0\n                                },\n                                transition: {\n                                    duration: 0.2\n                                },\n                                className: \"absolute inset-0 bg-black/50 flex flex-col justify-between z-30\",\n                                onClick: (e)=>e.stopPropagation(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-8 transition-opacity duration-200 \".concat(showButtons ? 'opacity-100' : 'opacity-0'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: togglePlay,\n                                            className: \"w-16 h-16 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center backdrop-blur-sm transition-all duration-200 hover:scale-110\",\n                                            children: isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1.5 h-6 bg-white/50 hover:bg-white transition-colors duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1.5 h-6 bg-white/50 hover:bg-white transition-colors duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-0 h-0 border-l-[16px] border-l-white/50 hover:border-l-white border-y-[10px] border-y-transparent ml-1 transition-colors duration-200\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 pb-8 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-1 bg-white/20 rounded-full overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-full bg-white/50 transition-all duration-200\",\n                                                            style: {\n                                                                width: \"\".concat(progress, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-white/50 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: formatTime(currentTime)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: formatTime(duration)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"flex flex-col items-center space-y-2 transition-all duration-200 hover:scale-110\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center backdrop-blur-sm transition-all duration-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-6 h-6 text-white/50 hover:text-white transition-colors duration-200\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/50 text-xs\",\n                                                                children: \"私密投票\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"flex flex-col items-center space-y-2 transition-all duration-200 hover:scale-110\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-r from-purple-500/50 to-pink-500/50 hover:from-purple-500 hover:to-pink-500 rounded-full flex items-center justify-center backdrop-blur-sm transition-all duration-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-6 h-6 text-white/50 hover:text-white transition-colors duration-200\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/50 text-xs\",\n                                                                children: \"购买NFT\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, currentIndex, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 left-0 right-0 z-50 transition-opacity duration-200 \".concat(showControls ? 'opacity-0 pointer-events-none' : 'opacity-100'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center px-4 pt-12 pb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange('推荐'),\n                                    className: \"text-lg font-medium transition-all duration-200 relative \".concat(activeTab === '推荐' ? 'text-white' : 'text-white/50'),\n                                    children: [\n                                        \"推荐\",\n                                        activeTab === '推荐' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange('关注'),\n                                    className: \"text-lg font-medium transition-all duration-200 relative \".concat(activeTab === '关注' ? 'text-white' : 'text-white/50'),\n                                    children: [\n                                        \"关注\",\n                                        activeTab === '关注' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange('朋友'),\n                                    className: \"text-lg font-medium transition-all duration-200 flex items-center space-x-1 relative \".concat(activeTab === '朋友' ? 'text-white' : 'text-white/50'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"朋友\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500/50 text-sm\",\n                                            children: \"♥\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeTab === '朋友' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute right-4 top-12 flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-8 h-8 flex items-center justify-center transition-colors duration-200 hover:bg-white/20 rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-white/50 hover:text-white transition-colors duration-200\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-8 h-8 flex items-center justify-center transition-colors duration-200 hover:bg-white/20 rounded-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-white/50 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-white/50 rounded-full mx-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-white/50 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-16 right-4 bg-black/60 backdrop-blur-sm rounded-lg px-2 py-1 z-40 transition-opacity duration-200 \".concat(showControls ? 'opacity-0 pointer-events-none' : 'opacity-100'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-white text-sm font-medium\",\n                    children: currentContent.videoTime\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-20 left-0 right-0 z-40 transition-opacity duration-200 \".concat(showControls ? 'opacity-0 pointer-events-none' : 'opacity-100'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 rounded-full overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-lg\",\n                                                children: currentContent.authorAvatar\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white/50 font-medium text-base\",\n                                                        children: currentContent.author\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    currentContent.verified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 bg-blue-500/50 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-xs\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/30 text-sm\",\n                                                children: currentContent.friendCount\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShare,\n                                        className: \"flex flex-col items-center transition-all duration-200 hover:scale-110\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-white/50 hover:text-white transition-colors duration-200\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/50 text-xs\",\n                                                children: formatNumber(currentContent.shares)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                        onClick: handleLike,\n                                        whileTap: {\n                                            scale: 0.8\n                                        },\n                                        className: \"flex flex-col items-center transition-all duration-200 hover:scale-110\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    animate: isLiked ? {\n                                                        scale: [\n                                                            1,\n                                                            1.2,\n                                                            1\n                                                        ]\n                                                    } : {},\n                                                    transition: {\n                                                        duration: 0.3\n                                                    },\n                                                    children: isLiked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-red-500\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-white/50 hover:text-white transition-colors duration-200\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/50 text-xs\",\n                                                children: formatNumber(currentContent.likes + (isLiked ? 1 : 0))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleComment,\n                                        className: \"flex flex-col items-center transition-all duration-200 hover:scale-110\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-white/50 hover:text-white transition-colors duration-200\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/50 text-xs\",\n                                                children: formatNumber(currentContent.comments)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 401,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\n_s(WeChatVideoHomepage, \"rGby+wIibI4XBIzPAVJ1WzoBwTs=\", false, function() {\n    return [\n        _contexts_UIContext__WEBPACK_IMPORTED_MODULE_2__.useUI\n    ];\n});\n_c = WeChatVideoHomepage;\nvar _c;\n$RefreshReg$(_c, \"WeChatVideoHomepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/wechat-video-homepage.tsx\n"));

/***/ })

});