"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/wechat-video-homepage.tsx":
/*!*******************************************!*\
  !*** ./src/app/wechat-video-homepage.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WeChatVideoHomepage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction WeChatVideoHomepage() {\n    _s();\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFollowing, setIsFollowing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showComments, setShowComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showShare, setShowShare] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('推荐');\n    // 视频内容数据\n    const videoContent = [\n        {\n            id: 1,\n            title: \"震撼！全球最高摩天大楼建设纪录片\",\n            author: \"建筑奇迹\",\n            authorAvatar: \"🏢\",\n            verified: true,\n            description: \"🏗️ 见证828米迪拜塔的建设全过程，工程技术的巅峰之作！\",\n            likes: 1200000,\n            comments: 89000,\n            shares: 156000,\n            videoTime: \"12:30\",\n            friendCount: \"1个朋友关注\"\n        },\n        {\n            id: 2,\n            title: \"AI机器人自动化建房，24小时完工！\",\n            author: \"未来建筑师\",\n            authorAvatar: \"🤖\",\n            verified: true,\n            description: \"🚀 革命性的3D打印建筑技术，机器人24小时自动建房全过程！\",\n            likes: 890000,\n            comments: 67000,\n            shares: 123000,\n            videoTime: \"08:45\",\n            friendCount: \"5个朋友关注\"\n        },\n        {\n            id: 3,\n            title: \"我家装修日记30天，终于看到希望了！\",\n            author: \"装修小白\",\n            authorAvatar: \"🏠\",\n            verified: false,\n            description: \"💪 历时一个月的装修终于有起色了，分享一下踩过的坑！\",\n            likes: 456000,\n            comments: 23000,\n            shares: 78000,\n            videoTime: \"06:30\",\n            friendCount: \"5个朋友关注\"\n        }\n    ];\n    const currentContent = videoContent[currentIndex];\n    // 格式化数字\n    const formatNumber = (num)=>{\n        if (num >= 10000) {\n            return (num / 10000).toFixed(1) + 'w';\n        }\n        return num.toString();\n    };\n    // 交互处理函数\n    const handleLike = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleLike]\": ()=>setIsLiked(!isLiked)\n    }[\"WeChatVideoHomepage.useCallback[handleLike]\"], [\n        isLiked\n    ]);\n    const handleComment = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleComment]\": ()=>setShowComments(true)\n    }[\"WeChatVideoHomepage.useCallback[handleComment]\"], []);\n    const handleShare = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleShare]\": ()=>setShowShare(true)\n    }[\"WeChatVideoHomepage.useCallback[handleShare]\"], []);\n    const togglePlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[togglePlay]\": ()=>setIsPlaying(!isPlaying)\n    }[\"WeChatVideoHomepage.useCallback[togglePlay]\"], [\n        isPlaying\n    ]);\n    // 标签切换处理函数\n    const handleTabChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleTabChange]\": (tab)=>{\n            setActiveTab(tab);\n            setCurrentIndex(0);\n            setIsLiked(false);\n            setIsFollowing(false);\n            setIsPlaying(true);\n        }\n    }[\"WeChatVideoHomepage.useCallback[handleTabChange]\"], []);\n    // 视频切换函数\n    const nextVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[nextVideo]\": ()=>{\n            setCurrentIndex({\n                \"WeChatVideoHomepage.useCallback[nextVideo]\": (prev)=>(prev + 1) % videoContent.length\n            }[\"WeChatVideoHomepage.useCallback[nextVideo]\"]);\n            setIsLiked(false);\n            setIsFollowing(false);\n            setIsPlaying(true);\n        }\n    }[\"WeChatVideoHomepage.useCallback[nextVideo]\"], [\n        videoContent.length\n    ]);\n    const prevVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[prevVideo]\": ()=>{\n            setCurrentIndex({\n                \"WeChatVideoHomepage.useCallback[prevVideo]\": (prev)=>(prev - 1 + videoContent.length) % videoContent.length\n            }[\"WeChatVideoHomepage.useCallback[prevVideo]\"]);\n            setIsLiked(false);\n            setIsFollowing(false);\n            setIsPlaying(true);\n        }\n    }[\"WeChatVideoHomepage.useCallback[prevVideo]\"], [\n        videoContent.length\n    ]);\n    // 触摸手势处理\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientY);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientY);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isUpSwipe = distance > 50;\n        const isDownSwipe = distance < -50;\n        if (isUpSwipe) {\n            nextVideo();\n        }\n        if (isDownSwipe) {\n            prevVideo();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-gray-800 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                mode: \"wait\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        y: 100,\n                        opacity: 0\n                    },\n                    animate: {\n                        y: 0,\n                        opacity: 1\n                    },\n                    exit: {\n                        y: -100,\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.3,\n                        ease: \"easeInOut\"\n                    },\n                    className: \"absolute inset-0 bg-gray-800\",\n                    onTouchStart: handleTouchStart,\n                    onTouchMove: handleTouchMove,\n                    onTouchEnd: handleTouchEnd,\n                    onClick: togglePlay,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-40 h-40 bg-orange-500/30 rounded-full flex items-center justify-center mb-6 mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-6xl\",\n                                            children: currentContent.authorAvatar\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-black/40 rounded-lg p-6 mx-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-3\",\n                                                children: currentContent.title\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-base opacity-90\",\n                                                children: currentContent.description\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        !isPlaying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center z-20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 bg-white/30 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-0 h-0 border-l-[20px] border-l-white border-y-[12px] border-y-transparent ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, currentIndex, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center px-4 pt-12 pb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange('推荐'),\n                                    className: \"text-lg font-medium transition-all duration-200 relative \".concat(activeTab === '推荐' ? 'text-white' : 'text-white/70'),\n                                    children: [\n                                        \"推荐\",\n                                        activeTab === '推荐' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange('关注'),\n                                    className: \"text-lg font-medium transition-all duration-200 relative \".concat(activeTab === '关注' ? 'text-white' : 'text-white/70'),\n                                    children: [\n                                        \"关注\",\n                                        activeTab === '关注' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange('朋友'),\n                                    className: \"text-lg font-medium transition-all duration-200 flex items-center space-x-1 relative \".concat(activeTab === '朋友' ? 'text-white' : 'text-white/70'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"朋友\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500 text-sm\",\n                                            children: \"♥\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeTab === '朋友' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute right-4 top-12 flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-8 h-8 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-white\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-8 h-8 flex items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-white rounded-full mx-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-16 right-4 bg-black/60 backdrop-blur-sm rounded-lg px-2 py-1 z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-white text-sm font-medium\",\n                    children: currentContent.videoTime\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 221,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-20 left-0 right-0 z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 rounded-full overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-lg\",\n                                                children: currentContent.authorAvatar\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-medium text-base\",\n                                                        children: currentContent.author\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    currentContent.verified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-xs\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300 text-sm\",\n                                                children: currentContent.friendCount\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShare,\n                                        className: \"flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-white\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-xs\",\n                                                children: formatNumber(currentContent.shares)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                        onClick: handleLike,\n                                        whileTap: {\n                                            scale: 0.8\n                                        },\n                                        className: \"flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                    animate: isLiked ? {\n                                                        scale: [\n                                                            1,\n                                                            1.2,\n                                                            1\n                                                        ]\n                                                    } : {},\n                                                    transition: {\n                                                        duration: 0.3\n                                                    },\n                                                    children: isLiked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-red-500\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-white\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-xs\",\n                                                children: formatNumber(currentContent.likes + (isLiked ? 1 : 0))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleComment,\n                                        className: \"flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-xs\",\n                                                children: formatNumber(currentContent.comments)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(WeChatVideoHomepage, \"w7JRl0lPrbavqLhGV+WE6+yxNis=\");\n_c = WeChatVideoHomepage;\nvar _c;\n$RefreshReg$(_c, \"WeChatVideoHomepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/wechat-video-homepage.tsx\n"));

/***/ })

});