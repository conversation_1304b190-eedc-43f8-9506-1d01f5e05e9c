import { ColorRepresentation, Vector3 } from 'three';
import { Axis } from '../../state.js';
import { HandlesContext } from '../context.js';
import { HandlesProperties } from '../index.js';
import { RegisteredHandle } from '../registered.js';
export declare class PivotAxisRotationHandle extends RegisteredHandle {
    constructor(context: HandlesContext, axis: Axis, tagPrefix: string, axisVector?: Vector3);
    bind(defaultColor: ColorRepresentation, config?: HandlesProperties): (() => void) | undefined;
}
