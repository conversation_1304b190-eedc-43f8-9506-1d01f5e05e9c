"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),n=require("./Line.cjs.js");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function s(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}require("@react-three/fiber"),require("three-stdlib");var c=u(e),i=s(t),o=s(r);const a=i.forwardRef((({threshold:e=15,geometry:t,...r},u)=>{const s=i.useRef(null);i.useImperativeHandle(u,(()=>s.current),[]);const a=i.useMemo((()=>[0,0,0,1,0,0]),[]),f=i.useRef(),l=i.useRef();return i.useLayoutEffect((()=>{const r=s.current.parent,n=null!=t?t:null==r?void 0:r.geometry;if(!n)return;if(f.current===n&&l.current===e)return;f.current=n,l.current=e;const u=new o.EdgesGeometry(n,e).attributes.position.array;s.current.geometry.setPositions(u),s.current.geometry.attributes.instanceStart.needsUpdate=!0,s.current.geometry.attributes.instanceEnd.needsUpdate=!0,s.current.computeLineDistances()})),i.createElement(n.Line,c.default({segments:!0,points:a,ref:s,raycast:()=>null},r))}));exports.Edges=a;
