"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("three"),r=require("@react-three/fiber");function n(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var u=n(e),i=n(t);function o(e,t,n){const o=r.useThree((e=>e.size)),s=r.useThree((e=>e.viewport)),a="number"==typeof e?e:o.width*s.dpr,l="number"==typeof t?t:o.height*s.dpr,c=("number"==typeof e?n:e)||{},{samples:p=0,depth:f,...d}=c,h=u.useMemo((()=>{const e=new i.WebGLRenderTarget(a,l,{minFilter:i.LinearFilter,magFilter:i.LinearFilter,type:i.HalfFloatType,...d});return f&&(e.depthTexture=new i.DepthTexture(a,l,i.FloatType)),e.samples=p,e}),[]);return u.useLayoutEffect((()=>{h.setSize(a,l),p&&(h.samples=p)}),[p,h,a,l]),u.useEffect((()=>()=>h.dispose()),[]),h}exports.Fbo=({children:e,width:t,height:r,...n})=>{const i=o(t,r,n);return u.createElement(u.Fragment,null,null==e?void 0:e(i))},exports.useFBO=o;
