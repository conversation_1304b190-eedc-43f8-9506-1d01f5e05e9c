{"version": 3, "file": "chainstart.js", "sourceRoot": "", "sources": ["../../../../src/common/hardforks/chainstart.ts"], "names": [], "mappings": ";;AAAA,kBAAe;IACd,IAAI,EAAE,YAAY;IAClB,OAAO,EAAE,kCAAkC;IAC3C,GAAG,EAAE,EAAE;IACP,MAAM,EAAE,EAAE;IACV,SAAS,EAAE;QACV,WAAW,EAAE;YACZ,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,mCAAmC;SACtC;QACD,oBAAoB,EAAE;YACrB,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,iEAAiE;SACpE;QACD,iBAAiB,EAAE;YAClB,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,2FAA2F;SAC9F;KACD;IACD,SAAS,EAAE;QACV,IAAI,EAAE;YACL,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,wDAAwD;SAC3D;QACD,QAAQ,EAAE;YACT,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;YAC1B,CAAC,EAAE,6CAA6C;SAChD;QACD,GAAG,EAAE;YACJ,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,4BAA4B;SAC/B;QACD,OAAO,EAAE;YACR,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,sDAAsD;SACzD;QACD,IAAI,EAAE;YACL,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,6BAA6B;SAChC;QACD,QAAQ,EAAE;YACT,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,4CAA4C;SAC/C;QACD,KAAK,EAAE;YACN,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,8BAA8B;SACjC;QACD,SAAS,EAAE;YACV,CAAC,EAAE,KAAK;YACR,CAAC,EAAE,6DAA6D;SAChE;QACD,WAAW,EAAE;YACZ,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,qEAAqE;SACxE;QACD,YAAY,EAAE;YACb,CAAC,EAAE,KAAK;YACR,CAAC,EAAE,2DAA2D;SAC9D;QACD,QAAQ,EAAE;YACT,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,iCAAiC;SACpC;QACD,GAAG,EAAE;YACJ,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,4BAA4B;SAC/B;QACD,OAAO,EAAE;YACR,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,qCAAqC;SACxC;QACD,QAAQ,EAAE;YACT,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,6HAA6H;SAChI;QACD,MAAM,EAAE;YACP,CAAC,EAAE,KAAK;YACR,CAAC,EAAE,+BAA+B;SAClC;QACD,IAAI,EAAE;YACL,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,6BAA6B;SAChC;QACD,WAAW,EAAE;YACZ,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,qCAAqC;SACxC;QACD,iBAAiB,EAAE;YAClB,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,mDAAmD;SACtD;QACD,cAAc,EAAE;YACf,CAAC,EAAE,KAAK;YACR,CAAC,EAAE,+DAA+D;SAClE;QACD,kBAAkB,EAAE;YACnB,CAAC,EAAE,KAAK;YACR,CAAC,EAAE,6CAA6C;SAChD;QACD,MAAM,EAAE;YACP,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,wJAAwJ;SAC3J;QACD,YAAY,EAAE;YACb,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,gEAAgE;SACnE;QACD,UAAU,EAAE;YACX,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,EAAE;SACL;QACD,EAAE,EAAE;YACH,CAAC,EAAE,KAAK;YACR,CAAC,EAAE,0EAA0E;SAC7E;QACD,UAAU,EAAE;YACX,CAAC,EAAE,KAAK;YACR,CAAC,EAAE,wCAAwC;SAC3C;QACD,UAAU,EAAE;YACX,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,sHAAsH;SACzH;QACD,aAAa,EAAE;YACd,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,+HAA+H;SAClI;QACD,IAAI,EAAE;YACL,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,wGAAwG;SAC3G;QACD,SAAS,EAAE;YACV,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,EAAE;SACL;QACD,MAAM,EAAE;YACP,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,EAAE;SACL;QACD,UAAU,EAAE;YACX,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,EAAE;SACL;QACD,SAAS,EAAE;YACV,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,EAAE;SACL;QACD,aAAa,EAAE;YACd,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,EAAE;SACL;QACD,QAAQ,EAAE;YACT,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,EAAE;SACL;QACD,YAAY,EAAE;YACb,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,EAAE;SACL;QACD,IAAI,EAAE;YACL,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,6BAA6B;SAChC;QACD,GAAG,EAAE;YACJ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,4BAA4B;SAC/B;QACD,GAAG,EAAE;YACJ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,4BAA4B;SAC/B;QACD,GAAG,EAAE;YACJ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,4BAA4B;SAC/B;QACD,GAAG,EAAE;YACJ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,4BAA4B;SAC/B;QACD,IAAI,EAAE;YACL,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,6BAA6B;SAChC;QACD,GAAG,EAAE;YACJ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,4BAA4B;SAC/B;QACD,IAAI,EAAE;YACL,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,6BAA6B;SAChC;QACD,MAAM,EAAE;YACP,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,+BAA+B;SAClC;QACD,MAAM,EAAE;YACP,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,+BAA+B;SAClC;QACD,UAAU,EAAE;YACX,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,mCAAmC;SACtC;QACD,EAAE,EAAE;YACH,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,2BAA2B;SAC9B;QACD,EAAE,EAAE;YACH,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,2BAA2B;SAC9B;QACD,GAAG,EAAE;YACJ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,4BAA4B;SAC/B;QACD,GAAG,EAAE;YACJ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,4BAA4B;SAC/B;QACD,EAAE,EAAE;YACH,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,2BAA2B;SAC9B;QACD,MAAM,EAAE;YACP,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,+BAA+B;SAClC;QACD,GAAG,EAAE;YACJ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,4BAA4B;SAC/B;QACD,EAAE,EAAE;YACH,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,2BAA2B;SAC9B;QACD,GAAG,EAAE;YACJ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,4BAA4B;SAC/B;QACD,GAAG,EAAE;YACJ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,4BAA4B;SAC/B;QACD,IAAI,EAAE;YACL,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,6BAA6B;SAChC;QACD,OAAO,EAAE;YACR,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,gCAAgC;SACnC;QACD,OAAO,EAAE;YACR,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,gCAAgC;SACnC;QACD,MAAM,EAAE;YACP,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,+BAA+B;SAClC;QACD,MAAM,EAAE;YACP,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,+BAA+B;SAClC;QACD,SAAS,EAAE;YACV,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,kCAAkC;SACrC;QACD,YAAY,EAAE;YACb,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,qCAAqC;SACxC;QACD,YAAY,EAAE;YACb,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,qCAAqC;SACxC;QACD,YAAY,EAAE;YACb,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,qCAAqC;SACxC;QACD,QAAQ,EAAE;YACT,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,iCAAiC;SACpC;QACD,QAAQ,EAAE;YACT,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,iCAAiC;SACpC;QACD,QAAQ,EAAE;YACT,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,iCAAiC;SACpC;QACD,WAAW,EAAE;YACZ,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,oCAAoC;SACvC;QACD,WAAW,EAAE;YACZ,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,oCAAoC;SACvC;QACD,SAAS,EAAE;YACV,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,kCAAkC;SACrC;QACD,QAAQ,EAAE;YACT,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,iCAAiC;SACpC;QACD,SAAS,EAAE;YACV,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,kCAAkC;SACrC;QACD,MAAM,EAAE;YACP,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,+BAA+B;SAClC;QACD,UAAU,EAAE;YACX,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,mCAAmC;SACtC;QACD,QAAQ,EAAE;YACT,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,iCAAiC;SACpC;QACD,GAAG,EAAE;YACJ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,4BAA4B;SAC/B;QACD,KAAK,EAAE;YACN,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,8BAA8B;SACjC;QACD,MAAM,EAAE;YACP,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,+BAA+B;SAClC;QACD,OAAO,EAAE;YACR,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,gCAAgC;SACnC;QACD,MAAM,EAAE;YACP,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,+BAA+B;SAClC;QACD,IAAI,EAAE;YACL,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,6BAA6B;SAChC;QACD,KAAK,EAAE;YACN,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,8BAA8B;SACjC;QACD,EAAE,EAAE;YACH,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,2BAA2B;SAC9B;QACD,KAAK,EAAE;YACN,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,8BAA8B;SACjC;QACD,GAAG,EAAE;YACJ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,4BAA4B;SAC/B;QACD,IAAI,EAAE;YACL,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,6BAA6B;SAChC;QACD,GAAG,EAAE;YACJ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,4BAA4B;SAC/B;QACD,IAAI,EAAE;YACL,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,6BAA6B;SAChC;QACD,QAAQ,EAAE;YACT,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,iCAAiC;SACpC;QACD,MAAM,EAAE;YACP,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,+BAA+B;SAClC;QACD,OAAO,EAAE;YACR,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,gCAAgC;SACnC;QACD,YAAY,EAAE;YACb,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,qCAAqC;SACxC;KACD;IACD,EAAE,EAAE;QACH,UAAU,EAAE;YACX,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,kCAAkC;SACrC;QACD,eAAe,EAAE;YAChB,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,oCAAoC;SACvC;QACD,gBAAgB,EAAE;YACjB,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,8CAA8C;SACjD;KACD;IACD,GAAG,EAAE;QACJ,iBAAiB,EAAE;YAClB,CAAC,EAAE,MAAM;YACT,CAAC,EAAE,6CAA6C;SAChD;QACD,sBAAsB,EAAE;YACvB,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,sEAAsE;SACzE;QACD,aAAa,EAAE;YACd,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,0GAA0G;SAC7G;QACD,aAAa,EAAE;YACd,CAAC,EAAE,KAAK;YACR,CAAC,EAAE,uCAAuC;SAC1C;QACD,cAAc,EAAE;YACf,CAAC,EAAE,MAAM;YACT,CAAC,EAAE,wCAAwC;SAC3C;QACD,WAAW,EAAE;YACZ,CAAC,EAAE,qBAAqB;YACxB,CAAC,EAAE,oDAAoD;SACvD;QACD,mBAAmB,EAAE;YACpB,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,wDAAwD;SAC3D;KACD;CACD,CAAC"}