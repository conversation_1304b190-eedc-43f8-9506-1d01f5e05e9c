/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { XRInputSource } from '../input/XRInputSource.js';
import { XRSession } from '../session/XRSession.js';
interface XRInputSourcesChangeEventInit extends EventInit {
    session: XRSession;
    added: XRInputSource[];
    removed: XRInputSource[];
}
export declare class XRInputSourcesChangeEvent extends Event {
    readonly session: XRSession;
    readonly added: XRInputSource[];
    readonly removed: XRInputSource[];
    constructor(type: string, eventInitDict: XRInputSourcesChangeEventInit);
}
export interface XRInputSourcesChangeEventHandler {
    (evt: XRInputSourcesChangeEvent): any;
}
export {};
//# sourceMappingURL=XRInputSourcesChangeEvent.d.ts.map