{"version": 3, "file": "AMFLoader.js", "sources": ["../../src/loaders/AMFLoader.js"], "sourcesContent": ["import {\n  BufferGeometry,\n  Color,\n  FileLoader,\n  Float32BufferAttribute,\n  Group,\n  Loader,\n  LoaderUtils,\n  Mesh,\n  MeshPhongMaterial,\n} from 'three'\nimport { unzipSync } from 'fflate'\nimport { decodeText } from '../_polyfill/LoaderUtils'\n\n/**\n * Description: Early release of an AMF Loader following the pattern of the\n * example loaders in the three.js project.\n *\n * Usage:\n *\tconst loader = new AMFLoader();\n *\tloader.load('/path/to/project.amf', function(objecttree) {\n *\t\tscene.add(objecttree);\n *\t});\n *\n * Materials now supported, material colors supported\n * Zip support, requires fflate\n * No constellation support (yet)!\n *\n */\n\nclass AMFLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(data) {\n    function loadDocument(data) {\n      let view = new DataView(data)\n      const magic = String.fromCharCode(view.getUint8(0), view.getUint8(1))\n\n      if (magic === 'PK') {\n        let zip = null\n        let file = null\n\n        console.log('THREE.AMFLoader: Loading Zip')\n\n        try {\n          zip = unzipSync(new Uint8Array(data))\n        } catch (e) {\n          if (e instanceof ReferenceError) {\n            console.log('THREE.AMFLoader: fflate missing and file is compressed.')\n            return null\n          }\n        }\n\n        for (file in zip) {\n          if (file.toLowerCase().substr(-4) === '.amf') {\n            break\n          }\n        }\n\n        console.log('THREE.AMFLoader: Trying to load file asset: ' + file)\n        view = new DataView(zip[file].buffer)\n      }\n\n      const fileText = decodeText(view)\n      const xmlData = new DOMParser().parseFromString(fileText, 'application/xml')\n\n      if (xmlData.documentElement.nodeName.toLowerCase() !== 'amf') {\n        console.log('THREE.AMFLoader: Error loading AMF - no AMF document found.')\n        return null\n      }\n\n      return xmlData\n    }\n\n    function loadDocumentScale(node) {\n      let scale = 1.0\n      let unit = 'millimeter'\n\n      if (node.documentElement.attributes.unit !== undefined) {\n        unit = node.documentElement.attributes.unit.value.toLowerCase()\n      }\n\n      const scaleUnits = {\n        millimeter: 1.0,\n        inch: 25.4,\n        feet: 304.8,\n        meter: 1000.0,\n        micron: 0.001,\n      }\n\n      if (scaleUnits[unit] !== undefined) {\n        scale = scaleUnits[unit]\n      }\n\n      console.log('THREE.AMFLoader: Unit scale: ' + scale)\n      return scale\n    }\n\n    function loadMaterials(node) {\n      let matName = 'AMF Material'\n      const matId = node.attributes.id.textContent\n      let color = { r: 1.0, g: 1.0, b: 1.0, a: 1.0 }\n\n      let loadedMaterial = null\n\n      for (let i = 0; i < node.childNodes.length; i++) {\n        const matChildEl = node.childNodes[i]\n\n        if (matChildEl.nodeName === 'metadata' && matChildEl.attributes.type !== undefined) {\n          if (matChildEl.attributes.type.value === 'name') {\n            matName = matChildEl.textContent\n          }\n        } else if (matChildEl.nodeName === 'color') {\n          color = loadColor(matChildEl)\n        }\n      }\n\n      loadedMaterial = new MeshPhongMaterial({\n        flatShading: true,\n        color: new Color(color.r, color.g, color.b),\n        name: matName,\n      })\n\n      if (color.a !== 1.0) {\n        loadedMaterial.transparent = true\n        loadedMaterial.opacity = color.a\n      }\n\n      return { id: matId, material: loadedMaterial }\n    }\n\n    function loadColor(node) {\n      const color = { r: 1.0, g: 1.0, b: 1.0, a: 1.0 }\n\n      for (let i = 0; i < node.childNodes.length; i++) {\n        const matColor = node.childNodes[i]\n\n        if (matColor.nodeName === 'r') {\n          color.r = matColor.textContent\n        } else if (matColor.nodeName === 'g') {\n          color.g = matColor.textContent\n        } else if (matColor.nodeName === 'b') {\n          color.b = matColor.textContent\n        } else if (matColor.nodeName === 'a') {\n          color.a = matColor.textContent\n        }\n      }\n\n      return color\n    }\n\n    function loadMeshVolume(node) {\n      const volume = { name: '', triangles: [], materialid: null }\n\n      let currVolumeNode = node.firstElementChild\n\n      if (node.attributes.materialid !== undefined) {\n        volume.materialId = node.attributes.materialid.nodeValue\n      }\n\n      while (currVolumeNode) {\n        if (currVolumeNode.nodeName === 'metadata') {\n          if (currVolumeNode.attributes.type !== undefined) {\n            if (currVolumeNode.attributes.type.value === 'name') {\n              volume.name = currVolumeNode.textContent\n            }\n          }\n        } else if (currVolumeNode.nodeName === 'triangle') {\n          const v1 = currVolumeNode.getElementsByTagName('v1')[0].textContent\n          const v2 = currVolumeNode.getElementsByTagName('v2')[0].textContent\n          const v3 = currVolumeNode.getElementsByTagName('v3')[0].textContent\n\n          volume.triangles.push(v1, v2, v3)\n        }\n\n        currVolumeNode = currVolumeNode.nextElementSibling\n      }\n\n      return volume\n    }\n\n    function loadMeshVertices(node) {\n      const vertArray = []\n      const normalArray = []\n      let currVerticesNode = node.firstElementChild\n\n      while (currVerticesNode) {\n        if (currVerticesNode.nodeName === 'vertex') {\n          let vNode = currVerticesNode.firstElementChild\n\n          while (vNode) {\n            if (vNode.nodeName === 'coordinates') {\n              const x = vNode.getElementsByTagName('x')[0].textContent\n              const y = vNode.getElementsByTagName('y')[0].textContent\n              const z = vNode.getElementsByTagName('z')[0].textContent\n\n              vertArray.push(x, y, z)\n            } else if (vNode.nodeName === 'normal') {\n              const nx = vNode.getElementsByTagName('nx')[0].textContent\n              const ny = vNode.getElementsByTagName('ny')[0].textContent\n              const nz = vNode.getElementsByTagName('nz')[0].textContent\n\n              normalArray.push(nx, ny, nz)\n            }\n\n            vNode = vNode.nextElementSibling\n          }\n        }\n\n        currVerticesNode = currVerticesNode.nextElementSibling\n      }\n\n      return { vertices: vertArray, normals: normalArray }\n    }\n\n    function loadObject(node) {\n      const objId = node.attributes.id.textContent\n      const loadedObject = { name: 'amfobject', meshes: [] }\n      let currColor = null\n      let currObjNode = node.firstElementChild\n\n      while (currObjNode) {\n        if (currObjNode.nodeName === 'metadata') {\n          if (currObjNode.attributes.type !== undefined) {\n            if (currObjNode.attributes.type.value === 'name') {\n              loadedObject.name = currObjNode.textContent\n            }\n          }\n        } else if (currObjNode.nodeName === 'color') {\n          currColor = loadColor(currObjNode)\n        } else if (currObjNode.nodeName === 'mesh') {\n          let currMeshNode = currObjNode.firstElementChild\n          const mesh = { vertices: [], normals: [], volumes: [], color: currColor }\n\n          while (currMeshNode) {\n            if (currMeshNode.nodeName === 'vertices') {\n              const loadedVertices = loadMeshVertices(currMeshNode)\n\n              mesh.normals = mesh.normals.concat(loadedVertices.normals)\n              mesh.vertices = mesh.vertices.concat(loadedVertices.vertices)\n            } else if (currMeshNode.nodeName === 'volume') {\n              mesh.volumes.push(loadMeshVolume(currMeshNode))\n            }\n\n            currMeshNode = currMeshNode.nextElementSibling\n          }\n\n          loadedObject.meshes.push(mesh)\n        }\n\n        currObjNode = currObjNode.nextElementSibling\n      }\n\n      return { id: objId, obj: loadedObject }\n    }\n\n    const xmlData = loadDocument(data)\n    let amfName = ''\n    let amfAuthor = ''\n    const amfScale = loadDocumentScale(xmlData)\n    const amfMaterials = {}\n    const amfObjects = {}\n    const childNodes = xmlData.documentElement.childNodes\n\n    let i, j\n\n    for (i = 0; i < childNodes.length; i++) {\n      const child = childNodes[i]\n\n      if (child.nodeName === 'metadata') {\n        if (child.attributes.type !== undefined) {\n          if (child.attributes.type.value === 'name') {\n            amfName = child.textContent\n          } else if (child.attributes.type.value === 'author') {\n            amfAuthor = child.textContent\n          }\n        }\n      } else if (child.nodeName === 'material') {\n        const loadedMaterial = loadMaterials(child)\n\n        amfMaterials[loadedMaterial.id] = loadedMaterial.material\n      } else if (child.nodeName === 'object') {\n        const loadedObject = loadObject(child)\n\n        amfObjects[loadedObject.id] = loadedObject.obj\n      }\n    }\n\n    const sceneObject = new Group()\n    const defaultMaterial = new MeshPhongMaterial({ color: 0xaaaaff, flatShading: true })\n\n    sceneObject.name = amfName\n    sceneObject.userData.author = amfAuthor\n    sceneObject.userData.loader = 'AMF'\n\n    for (const id in amfObjects) {\n      const part = amfObjects[id]\n      const meshes = part.meshes\n      const newObject = new Group()\n      newObject.name = part.name || ''\n\n      for (i = 0; i < meshes.length; i++) {\n        let objDefaultMaterial = defaultMaterial\n        const mesh = meshes[i]\n        const vertices = new Float32BufferAttribute(mesh.vertices, 3)\n        let normals = null\n\n        if (mesh.normals.length) {\n          normals = new Float32BufferAttribute(mesh.normals, 3)\n        }\n\n        if (mesh.color) {\n          const color = mesh.color\n\n          objDefaultMaterial = defaultMaterial.clone()\n          objDefaultMaterial.color = new Color(color.r, color.g, color.b)\n\n          if (color.a !== 1.0) {\n            objDefaultMaterial.transparent = true\n            objDefaultMaterial.opacity = color.a\n          }\n        }\n\n        const volumes = mesh.volumes\n\n        for (j = 0; j < volumes.length; j++) {\n          const volume = volumes[j]\n          const newGeometry = new BufferGeometry()\n          let material = objDefaultMaterial\n\n          newGeometry.setIndex(volume.triangles)\n          newGeometry.setAttribute('position', vertices.clone())\n\n          if (normals) {\n            newGeometry.setAttribute('normal', normals.clone())\n          }\n\n          if (amfMaterials[volume.materialId] !== undefined) {\n            material = amfMaterials[volume.materialId]\n          }\n\n          newGeometry.scale(amfScale, amfScale, amfScale)\n          newObject.add(new Mesh(newGeometry, material.clone()))\n        }\n      }\n\n      sceneObject.add(newObject)\n    }\n\n    return sceneObject\n  }\n}\n\nexport { AMFLoader }\n"], "names": ["data", "xmlData", "i"], "mappings": ";;;AA8BA,MAAM,kBAAkB,OAAO;AAAA,EAC7B,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACd;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AAEd,UAAM,SAAS,IAAI,WAAW,MAAM,OAAO;AAC3C,WAAO,QAAQ,MAAM,IAAI;AACzB,WAAO,gBAAgB,aAAa;AACpC,WAAO,iBAAiB,MAAM,aAAa;AAC3C,WAAO,mBAAmB,MAAM,eAAe;AAC/C,WAAO;AAAA,MACL;AAAA,MACA,SAAU,MAAM;AACd,YAAI;AACF,iBAAO,MAAM,MAAM,IAAI,CAAC;AAAA,QACzB,SAAQ,GAAP;AACA,cAAI,SAAS;AACX,oBAAQ,CAAC;AAAA,UACrB,OAAiB;AACL,oBAAQ,MAAM,CAAC;AAAA,UAChB;AAED,gBAAM,QAAQ,UAAU,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,MAAM,MAAM;AACV,aAAS,aAAaA,OAAM;AAC1B,UAAI,OAAO,IAAI,SAASA,KAAI;AAC5B,YAAM,QAAQ,OAAO,aAAa,KAAK,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC;AAEpE,UAAI,UAAU,MAAM;AAClB,YAAI,MAAM;AACV,YAAI,OAAO;AAEX,gBAAQ,IAAI,8BAA8B;AAE1C,YAAI;AACF,gBAAM,UAAU,IAAI,WAAWA,KAAI,CAAC;AAAA,QACrC,SAAQ,GAAP;AACA,cAAI,aAAa,gBAAgB;AAC/B,oBAAQ,IAAI,yDAAyD;AACrE,mBAAO;AAAA,UACR;AAAA,QACF;AAED,aAAK,QAAQ,KAAK;AAChB,cAAI,KAAK,YAAa,EAAC,OAAO,EAAE,MAAM,QAAQ;AAC5C;AAAA,UACD;AAAA,QACF;AAED,gBAAQ,IAAI,iDAAiD,IAAI;AACjE,eAAO,IAAI,SAAS,IAAI,IAAI,EAAE,MAAM;AAAA,MACrC;AAED,YAAM,WAAW,WAAW,IAAI;AAChC,YAAMC,WAAU,IAAI,UAAS,EAAG,gBAAgB,UAAU,iBAAiB;AAE3E,UAAIA,SAAQ,gBAAgB,SAAS,YAAW,MAAO,OAAO;AAC5D,gBAAQ,IAAI,6DAA6D;AACzE,eAAO;AAAA,MACR;AAED,aAAOA;AAAA,IACR;AAED,aAAS,kBAAkB,MAAM;AAC/B,UAAI,QAAQ;AACZ,UAAI,OAAO;AAEX,UAAI,KAAK,gBAAgB,WAAW,SAAS,QAAW;AACtD,eAAO,KAAK,gBAAgB,WAAW,KAAK,MAAM,YAAa;AAAA,MAChE;AAED,YAAM,aAAa;AAAA,QACjB,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAED,UAAI,WAAW,IAAI,MAAM,QAAW;AAClC,gBAAQ,WAAW,IAAI;AAAA,MACxB;AAED,cAAQ,IAAI,kCAAkC,KAAK;AACnD,aAAO;AAAA,IACR;AAED,aAAS,cAAc,MAAM;AAC3B,UAAI,UAAU;AACd,YAAM,QAAQ,KAAK,WAAW,GAAG;AACjC,UAAI,QAAQ,EAAE,GAAG,GAAK,GAAG,GAAK,GAAG,GAAK,GAAG,EAAK;AAE9C,UAAI,iBAAiB;AAErB,eAASC,KAAI,GAAGA,KAAI,KAAK,WAAW,QAAQA,MAAK;AAC/C,cAAM,aAAa,KAAK,WAAWA,EAAC;AAEpC,YAAI,WAAW,aAAa,cAAc,WAAW,WAAW,SAAS,QAAW;AAClF,cAAI,WAAW,WAAW,KAAK,UAAU,QAAQ;AAC/C,sBAAU,WAAW;AAAA,UACtB;AAAA,QACX,WAAmB,WAAW,aAAa,SAAS;AAC1C,kBAAQ,UAAU,UAAU;AAAA,QAC7B;AAAA,MACF;AAED,uBAAiB,IAAI,kBAAkB;AAAA,QACrC,aAAa;AAAA,QACb,OAAO,IAAI,MAAM,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AAAA,QAC1C,MAAM;AAAA,MACd,CAAO;AAED,UAAI,MAAM,MAAM,GAAK;AACnB,uBAAe,cAAc;AAC7B,uBAAe,UAAU,MAAM;AAAA,MAChC;AAED,aAAO,EAAE,IAAI,OAAO,UAAU,eAAgB;AAAA,IAC/C;AAED,aAAS,UAAU,MAAM;AACvB,YAAM,QAAQ,EAAE,GAAG,GAAK,GAAG,GAAK,GAAG,GAAK,GAAG,EAAK;AAEhD,eAASA,KAAI,GAAGA,KAAI,KAAK,WAAW,QAAQA,MAAK;AAC/C,cAAM,WAAW,KAAK,WAAWA,EAAC;AAElC,YAAI,SAAS,aAAa,KAAK;AAC7B,gBAAM,IAAI,SAAS;AAAA,QAC7B,WAAmB,SAAS,aAAa,KAAK;AACpC,gBAAM,IAAI,SAAS;AAAA,QAC7B,WAAmB,SAAS,aAAa,KAAK;AACpC,gBAAM,IAAI,SAAS;AAAA,QAC7B,WAAmB,SAAS,aAAa,KAAK;AACpC,gBAAM,IAAI,SAAS;AAAA,QACpB;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,eAAe,MAAM;AAC5B,YAAM,SAAS,EAAE,MAAM,IAAI,WAAW,CAAE,GAAE,YAAY,KAAM;AAE5D,UAAI,iBAAiB,KAAK;AAE1B,UAAI,KAAK,WAAW,eAAe,QAAW;AAC5C,eAAO,aAAa,KAAK,WAAW,WAAW;AAAA,MAChD;AAED,aAAO,gBAAgB;AACrB,YAAI,eAAe,aAAa,YAAY;AAC1C,cAAI,eAAe,WAAW,SAAS,QAAW;AAChD,gBAAI,eAAe,WAAW,KAAK,UAAU,QAAQ;AACnD,qBAAO,OAAO,eAAe;AAAA,YAC9B;AAAA,UACF;AAAA,QACX,WAAmB,eAAe,aAAa,YAAY;AACjD,gBAAM,KAAK,eAAe,qBAAqB,IAAI,EAAE,CAAC,EAAE;AACxD,gBAAM,KAAK,eAAe,qBAAqB,IAAI,EAAE,CAAC,EAAE;AACxD,gBAAM,KAAK,eAAe,qBAAqB,IAAI,EAAE,CAAC,EAAE;AAExD,iBAAO,UAAU,KAAK,IAAI,IAAI,EAAE;AAAA,QACjC;AAED,yBAAiB,eAAe;AAAA,MACjC;AAED,aAAO;AAAA,IACR;AAED,aAAS,iBAAiB,MAAM;AAC9B,YAAM,YAAY,CAAE;AACpB,YAAM,cAAc,CAAE;AACtB,UAAI,mBAAmB,KAAK;AAE5B,aAAO,kBAAkB;AACvB,YAAI,iBAAiB,aAAa,UAAU;AAC1C,cAAI,QAAQ,iBAAiB;AAE7B,iBAAO,OAAO;AACZ,gBAAI,MAAM,aAAa,eAAe;AACpC,oBAAM,IAAI,MAAM,qBAAqB,GAAG,EAAE,CAAC,EAAE;AAC7C,oBAAM,IAAI,MAAM,qBAAqB,GAAG,EAAE,CAAC,EAAE;AAC7C,oBAAM,IAAI,MAAM,qBAAqB,GAAG,EAAE,CAAC,EAAE;AAE7C,wBAAU,KAAK,GAAG,GAAG,CAAC;AAAA,YACpC,WAAuB,MAAM,aAAa,UAAU;AACtC,oBAAM,KAAK,MAAM,qBAAqB,IAAI,EAAE,CAAC,EAAE;AAC/C,oBAAM,KAAK,MAAM,qBAAqB,IAAI,EAAE,CAAC,EAAE;AAC/C,oBAAM,KAAK,MAAM,qBAAqB,IAAI,EAAE,CAAC,EAAE;AAE/C,0BAAY,KAAK,IAAI,IAAI,EAAE;AAAA,YAC5B;AAED,oBAAQ,MAAM;AAAA,UACf;AAAA,QACF;AAED,2BAAmB,iBAAiB;AAAA,MACrC;AAED,aAAO,EAAE,UAAU,WAAW,SAAS,YAAa;AAAA,IACrD;AAED,aAAS,WAAW,MAAM;AACxB,YAAM,QAAQ,KAAK,WAAW,GAAG;AACjC,YAAM,eAAe,EAAE,MAAM,aAAa,QAAQ,CAAA,EAAI;AACtD,UAAI,YAAY;AAChB,UAAI,cAAc,KAAK;AAEvB,aAAO,aAAa;AAClB,YAAI,YAAY,aAAa,YAAY;AACvC,cAAI,YAAY,WAAW,SAAS,QAAW;AAC7C,gBAAI,YAAY,WAAW,KAAK,UAAU,QAAQ;AAChD,2BAAa,OAAO,YAAY;AAAA,YACjC;AAAA,UACF;AAAA,QACX,WAAmB,YAAY,aAAa,SAAS;AAC3C,sBAAY,UAAU,WAAW;AAAA,QAC3C,WAAmB,YAAY,aAAa,QAAQ;AAC1C,cAAI,eAAe,YAAY;AAC/B,gBAAM,OAAO,EAAE,UAAU,IAAI,SAAS,CAAE,GAAE,SAAS,CAAA,GAAI,OAAO,UAAW;AAEzE,iBAAO,cAAc;AACnB,gBAAI,aAAa,aAAa,YAAY;AACxC,oBAAM,iBAAiB,iBAAiB,YAAY;AAEpD,mBAAK,UAAU,KAAK,QAAQ,OAAO,eAAe,OAAO;AACzD,mBAAK,WAAW,KAAK,SAAS,OAAO,eAAe,QAAQ;AAAA,YAC1E,WAAuB,aAAa,aAAa,UAAU;AAC7C,mBAAK,QAAQ,KAAK,eAAe,YAAY,CAAC;AAAA,YAC/C;AAED,2BAAe,aAAa;AAAA,UAC7B;AAED,uBAAa,OAAO,KAAK,IAAI;AAAA,QAC9B;AAED,sBAAc,YAAY;AAAA,MAC3B;AAED,aAAO,EAAE,IAAI,OAAO,KAAK,aAAc;AAAA,IACxC;AAED,UAAM,UAAU,aAAa,IAAI;AACjC,QAAI,UAAU;AACd,QAAI,YAAY;AAChB,UAAM,WAAW,kBAAkB,OAAO;AAC1C,UAAM,eAAe,CAAE;AACvB,UAAM,aAAa,CAAE;AACrB,UAAM,aAAa,QAAQ,gBAAgB;AAE3C,QAAI,GAAG;AAEP,SAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,YAAM,QAAQ,WAAW,CAAC;AAE1B,UAAI,MAAM,aAAa,YAAY;AACjC,YAAI,MAAM,WAAW,SAAS,QAAW;AACvC,cAAI,MAAM,WAAW,KAAK,UAAU,QAAQ;AAC1C,sBAAU,MAAM;AAAA,UACjB,WAAU,MAAM,WAAW,KAAK,UAAU,UAAU;AACnD,wBAAY,MAAM;AAAA,UACnB;AAAA,QACF;AAAA,MACT,WAAiB,MAAM,aAAa,YAAY;AACxC,cAAM,iBAAiB,cAAc,KAAK;AAE1C,qBAAa,eAAe,EAAE,IAAI,eAAe;AAAA,MACzD,WAAiB,MAAM,aAAa,UAAU;AACtC,cAAM,eAAe,WAAW,KAAK;AAErC,mBAAW,aAAa,EAAE,IAAI,aAAa;AAAA,MAC5C;AAAA,IACF;AAED,UAAM,cAAc,IAAI,MAAO;AAC/B,UAAM,kBAAkB,IAAI,kBAAkB,EAAE,OAAO,UAAU,aAAa,MAAM;AAEpF,gBAAY,OAAO;AACnB,gBAAY,SAAS,SAAS;AAC9B,gBAAY,SAAS,SAAS;AAE9B,eAAW,MAAM,YAAY;AAC3B,YAAM,OAAO,WAAW,EAAE;AAC1B,YAAM,SAAS,KAAK;AACpB,YAAM,YAAY,IAAI,MAAO;AAC7B,gBAAU,OAAO,KAAK,QAAQ;AAE9B,WAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAClC,YAAI,qBAAqB;AACzB,cAAM,OAAO,OAAO,CAAC;AACrB,cAAM,WAAW,IAAI,uBAAuB,KAAK,UAAU,CAAC;AAC5D,YAAI,UAAU;AAEd,YAAI,KAAK,QAAQ,QAAQ;AACvB,oBAAU,IAAI,uBAAuB,KAAK,SAAS,CAAC;AAAA,QACrD;AAED,YAAI,KAAK,OAAO;AACd,gBAAM,QAAQ,KAAK;AAEnB,+BAAqB,gBAAgB,MAAO;AAC5C,6BAAmB,QAAQ,IAAI,MAAM,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AAE9D,cAAI,MAAM,MAAM,GAAK;AACnB,+BAAmB,cAAc;AACjC,+BAAmB,UAAU,MAAM;AAAA,UACpC;AAAA,QACF;AAED,cAAM,UAAU,KAAK;AAErB,aAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACnC,gBAAM,SAAS,QAAQ,CAAC;AACxB,gBAAM,cAAc,IAAI,eAAgB;AACxC,cAAI,WAAW;AAEf,sBAAY,SAAS,OAAO,SAAS;AACrC,sBAAY,aAAa,YAAY,SAAS,MAAK,CAAE;AAErD,cAAI,SAAS;AACX,wBAAY,aAAa,UAAU,QAAQ,MAAK,CAAE;AAAA,UACnD;AAED,cAAI,aAAa,OAAO,UAAU,MAAM,QAAW;AACjD,uBAAW,aAAa,OAAO,UAAU;AAAA,UAC1C;AAED,sBAAY,MAAM,UAAU,UAAU,QAAQ;AAC9C,oBAAU,IAAI,IAAI,KAAK,aAAa,SAAS,MAAK,CAAE,CAAC;AAAA,QACtD;AAAA,MACF;AAED,kBAAY,IAAI,SAAS;AAAA,IAC1B;AAED,WAAO;AAAA,EACR;AACH;"}