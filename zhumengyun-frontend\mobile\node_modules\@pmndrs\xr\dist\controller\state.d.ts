import { XRControllerLayoutLoader } from './layout.js';
import { XRControllerState } from '../input.js';
export declare function createXRControllerState(id: string, inputSource: XRInputSource, layoutLoader: XRControllerLayoutLoader, events: ReadonlyArray<XRInputSourceEvent>, isPrimary: boolean): Promise<XRControllerState> | XRControllerState;
export declare function updateXRControllerState({ gamepad, inputSource, layout }: XRControllerState): void;
