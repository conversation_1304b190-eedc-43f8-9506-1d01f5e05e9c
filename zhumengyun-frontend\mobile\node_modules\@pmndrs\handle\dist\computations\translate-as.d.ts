import { Matrix4, Quaternion, Vector3 } from 'three';
import { HandleTransformState } from '../state.js';
import { HandleOptions } from '../store.js';
import { BaseHandleStoreData } from './utils.js';
export type TranslateAsHandlePointerData = {
    pointerWorldPoint: Vector3;
    pointerWorldDirection: Vector3 | undefined;
    pointerWorldQuaternion: Quaternion;
    pointerWorldOrigin: Vector3;
    prevPointerWorldQuaternion: Quaternion;
    initialPointerWorldPoint: Vector3;
    initialPointerWorldDirection: Vector3 | undefined;
};
export type TranslateAsHandleStoreData = {
    initialTargetPosition: Vector3;
    initialTargetQuaternion: Quaternion;
    initialTargetScale: Vector3;
    initialTargetParentWorldMatrix: Matrix4 | undefined;
    prevTranslateAsDeltaRotation: Quaternion | undefined;
} & BaseHandleStoreData;
export declare function computeTranslateAsHandleTransformState(time: number, pointerData: TranslateAsHandlePointerData, storeData: TranslateAsHandleStoreData, targetWorldMatrix: Matrix4, targetParentWorldMatrix: Matrix4 | undefined, options: HandleOptions<any> & {
    translate?: 'as-rotate' | 'as-scale' | 'as-rotate-and-scale';
}): HandleTransformState;
