{"version": 3, "file": "web3_provider.js", "sourceRoot": "", "sources": ["../../src/web3_provider.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;;;;;;;;;;;;;;AAEF,8EAAwE;AACxE,0EAAkD;AAkBlD,2CAA6C;AAC7C,yCAA+D;AAC/D,2CAAyD;AAEzD;;;;;;EAME;AAEF,MAAsB,oBAEpB,SAAQ,4BAAe;IAWxB,YACC,OAAgB,EAChB,SAAoB,EACpB,KAAa,EACb,IAAY,EACZ,qBAA2D;QAE3D,KAAK,EAAE,CAAC;QAER,IACC,qBAAqB,KAAK,SAAS;YACnC,SAAS,KAAK,oBAAS,CAAC,KAAK;YAC7B,CAAC,CAAC,iBAAiB,IAAI,qBAAqB,CAAC,EAC5C,CAAC;YACF,MAAM,IAAI,sCAA0B,CAAC,eAAe,CAAC,CAAC;QACvD,CAAC;aAAM,IACN,qBAAqB,KAAK,SAAS;YACnC,SAAS,KAAK,oBAAS,CAAC,SAAS;YACjC,CAAC,CACA,eAAe,IAAI,qBAAqB;gBACxC,kBAAkB,IAAI,qBAAqB,CAC3C,EACA,CAAC;YACF,MAAM,IAAI,sCAA0B,CAAC,oBAAoB,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,SAAS,KAAK,oBAAS,CAAC,KAAK,EAAE,CAAC;YACnC,IAAI,CAAC,QAAQ,GAAG,IAAI,6BAAY,CAC/B,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,EAC/C,qBAA4C,CAC5C,CAAC;QACH,CAAC;aAAM,IAAI,SAAS,KAAK,oBAAS,CAAC,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC,QAAQ,GAAG,IAAI,2BAAiB,CACpC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,EAC9C,qBAAuC,aAAvC,qBAAqB,uBAArB,qBAAqB,CAAoB,aAAa,EACtD,qBAAuC,aAAvC,qBAAqB,uBAArB,qBAAqB,CAAoB,gBAAgB,CAC1D,CAAC;QACH,CAAC;IACF,CAAC;IAEY,OAAO,CAInB,OAAgD,EAChD,cAA4B;;YAE5B,IAAI,IAAI,CAAC,SAAS,KAAK,oBAAS,CAAC,KAAK,EAAE,CAAC;gBACxC,OAAO,CAAC,MAAO,IAAI,CAAC,QAAyB,CAAC,OAAO,CACpD,OAAO,EACP,cAAc,CACd,CAAqD,CAAC;YACxD,CAAC;YAED,OAAQ,IAAI,CAAC,QAA8B,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC9D,CAAC;KAAA;IAEM,SAAS;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;IAClC,CAAC;IACM,qBAAqB;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAC;IAC9C,CAAC;IAkBM,IAAI,CAAC,KAAa,EAAE,SAAkB;;QAC5C,IAAI,MAAA,IAAI,CAAC,QAAQ,0CAAE,IAAI,EAAE,CAAC;YACzB,iEAAiE;YACjE,IAAI,CAAC,QAAQ,CAAC,IAAI,CACjB,KAAK,EACL,SAE4B,CAC5B,CAAC;QACH,CAAC;IACF,CAAC;IACM,kBAAkB,CAAE,KAAa;;QACvC,IAAI,MAAA,IAAI,CAAC,QAAQ,0CAAE,kBAAkB;YAAE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAChF,CAAC;IACM,OAAO;;QACb,IAAI,MAAA,IAAI,CAAC,QAAQ,0CAAE,OAAO;YAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;IACrD,CAAC;IACM,UAAU,CAAC,KAA0B,EAAE,KAA0B;;QACvE,IAAI,MAAA,IAAI,CAAC,QAAQ,0CAAE,UAAU;YAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACvE,CAAC;IACM,KAAK;;QACX,IAAI,MAAA,IAAI,CAAC,QAAQ,0CAAE,KAAK;YAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IACjD,CAAC;IAwBM,EAAE,CAAC,KAAc,EAAE,SAAkB;QAC3C,IAAI,IAAI,CAAC,QAAQ;YAChB,iEAAiE;YACjE,IAAI,CAAC,QAAQ,CAAC,EAAE,CACf,KAAe,EACf,SAEmC,CACnC,CAAC;IACJ,CAAC;IAqBM,cAAc,CAAC,KAAc,EAAE,SAAkB;QACvD,IAAI,IAAI,CAAC,QAAQ;YAChB,IAAI,CAAC,QAAQ,CAAC,cAAc,CAC3B,KAAe,EACf,SAE4B,CAC5B,CAAC;IACJ,CAAC;CACD;AAnLD,oDAmLC"}