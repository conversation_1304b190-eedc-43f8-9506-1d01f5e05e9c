import { AllowedPointerEvents, AllowedPointerEventsType, PointerOptions, Pointer, PointerEventsMap } from '@pmndrs/pointer-events';
import { MeshLineMaterial } from 'meshline';
import { BufferGeometry, Camera, ColorRepresentation, Mesh, Object3D, Object3DEventMap, Vector3, Vector3Tuple } from 'three';
/**
 * marks its children as teleportable
 */
export declare function makeTeleportTarget(root: Object3D<Object3DEventMap & PointerEventsMap>, camera: Camera | (() => Camera), onTeleport: (pointer: Vector3, event: PointerEvent) => void): () => void;
/**
 * @param space
 * @param rayGroup must be placed directly into the scene
 */
export declare function syncTeleportPointerRayGroup(space: Object3D, rayGroup: Object3D, deltaTimeMs: number): void;
/**
 * check if the object is marked as teleportable
 */
export declare function isTeleportTarget(object: Object3D): boolean;
export declare function buildTeleportTargetFilter(options?: PointerOptions): (object: Object3D, pointerEvents: AllowedPointerEvents, pointerEventsType: AllowedPointerEventsType, pointerEventsOrder: number) => boolean;
export declare function createTeleportRayLine(): Vector3[];
export type TeleportPointerRayModelOptions = {
    /**
     * @default white
     */
    color?: ColorRepresentation | Vector3Tuple | ((pointer: Pointer) => ColorRepresentation | Vector3Tuple);
    /**
     * @default 0.4
     */
    opacity?: number | ((pointer: Pointer) => number);
    /**
     * @default 0.01
     */
    size?: number;
};
export declare class TeleportPointerRayModel extends Mesh<BufferGeometry, MeshLineMaterial> {
    private readonly multiplier;
    private lineLengths;
    options: TeleportPointerRayModelOptions;
    constructor(points: Array<Vector3>);
    update(pointer: Pointer): void;
}
