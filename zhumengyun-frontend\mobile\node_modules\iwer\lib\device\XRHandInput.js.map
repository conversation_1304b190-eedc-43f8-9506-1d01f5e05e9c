{"version": 3, "file": "XRHandInput.js", "sourceRoot": "", "sources": ["../../src/device/XRHandInput.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EACN,OAAO,EAEP,kBAAkB,GAClB,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAe,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EACN,SAAS,EACT,YAAY,EACZ,aAAa,EACb,OAAO,EACP,eAAe,GACf,MAAM,eAAe,CAAC;AACvB,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AACzD,OAAO,EACN,YAAY,EACZ,aAAa,EACb,eAAe,GACf,MAAM,2BAA2B,CAAC;AACnC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAG7C,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AACzD,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,eAAe,EAAE,MAAM,2BAA2B,CAAC;AAwB5D,MAAM,CAAC,MAAM,gBAAgB,GAAsB;IAClD,SAAS,EAAE,aAAa;IACxB,kBAAkB,EAAE;QACnB,cAAc;QACd,qBAAqB;QACrB,iBAAiB;KACjB;IACD,KAAK,EAAE;QACN,OAAO,EAAE,eAAe;QACxB,KAAK,EAAE,aAAa;QACpB,KAAK,EAAE,aAAa;KACpB;CACD,CAAC;AAEF,MAAM,mBAAmB,GAAkB;IAC1C,OAAO,EAAE,kBAAkB,CAAC,IAAI;IAChC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC;IAClE,IAAI,EAAE,EAAE;CACR,CAAC;AAEF,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACnC,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACrC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAChC,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjC,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACnC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAC9B,MAAM,oBAAoB,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAC3C,MAAM,sBAAsB,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAC7C,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACxC,MAAM,iBAAiB,GAAG,CACzB,GAAS,EACT,UAAgB,EAChB,QAAc,EACd,KAAa,EACZ,EAAE;IACH,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IAC9C,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;IAC7C,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;IACvC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAC1C,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IACzC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACnC,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,YAAY,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;IACjE,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,cAAc,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;IACxE,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACxD,IAAI,CAAC,4BAA4B,CAChC,GAAG,EACH,sBAAsB,EACtB,oBAAoB,EACpB,iBAAiB,CACjB,CAAC;IACF,OAAO,GAAG,CAAC;AACZ,CAAC,CAAC;AAEF,MAAM,sBAAsB,GAAG;IAC9B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;CACnD,CAAC;AACF,MAAM,mBAAmB,GAAG,CAAC,UAAgB,EAAE,EAAE;IAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;QAC5B,UAAU,CAAC,CAAC,CAAC,IAAI,sBAAsB,CAAC,CAAC,CAAC,CAAC;KAC3C;AACF,CAAC,CAAC;AAEF,MAAM,OAAO,WAAY,SAAQ,cAAc;IAW9C,YACC,eAAkC,EAClC,UAAwB,EACxB,WAAwB;QAExB,IAAI,UAAU,KAAK,YAAY,CAAC,IAAI,IAAI,UAAU,KAAK,YAAY,CAAC,KAAK,EAAE;YAC1E,MAAM,IAAI,YAAY,CACrB,6DAA6D,EAC7D,mBAAmB,CACnB,CAAC;SACF;QACD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,KAAK,EAAE;YACnE,MAAM,IAAI,YAAY,CACrB,sDAAsD,EACtD,mBAAmB,CACnB,CAAC;SACF;QAED,MAAM,cAAc,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC;QAChD,MAAM,SAAS,GAAG,IAAI,OAAO,CAAC,cAAc,CAAC,CAAC;QAC9C,MAAM,QAAQ,GAAG;YAChB,eAAe,CAAC,SAAS;YACzB,GAAG,eAAe,CAAC,kBAAkB;SACrC,CAAC;QACF,MAAM,IAAI,GAAG,IAAI,MAAM,EAAE,CAAC;QAC1B,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;YAChD,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,YAAY,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QACH,MAAM,WAAW,GAAG,IAAI,aAAa,CACpC,UAAU,EACV,eAAe,CAAC,cAAc,EAC9B,QAAQ,EACR,cAAc,EACd,IAAI,OAAO,CAAC,mBAAmB,CAAC,EAChC,SAAS,EACT,IAAI,CACJ,CAAC;QAEF,KAAK,CAAC,WAAW,CAAC,CAAC;QACnB,IAAI,CAAC,YAAY,CAAC,GAAG;YACpB,MAAM,EAAE,SAAS;YACjB,KAAK,EAAE,eAAe,CAAC,KAAK;SAC5B,CAAC;QAEF,IAAI,CAAC,cAAc,EAAE,CAAC;IACvB,CAAC;IAED,IAAI,MAAM;QACT,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC;IAClC,CAAC;IAED,IAAI,MAAM,CAAC,MAAc;QACxB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YACtC,OAAO,CAAC,IAAI,CAAC,eAAe,MAAM,YAAY,CAAC,CAAC;YAChD,OAAO;SACP;QACD,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC;IACpC,CAAC;IAED,cAAc;QACb,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC;QACvE,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC;QACjD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;YAChD,MAAM,iBAAiB,GACtB,UAAU,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC;YACpD,MAAM,gBAAgB,GACrB,SAAS,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC;YACnD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;YAC1D,iBAAiB,CAChB,UAAU,CAAC,OAAO,CAAC,CAAC,YAAY,EAChC,iBAAiB,EACjB,gBAAgB,EAChB,IAAI,CAAC,UAAU,CACf,CAAC;YACF,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,KAAK,YAAY,CAAC,KAAK,EAAE;gBACvD,mBAAmB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC;aACtD;YACD,UAAU,CAAC,aAAa,CAAC,CAAC,MAAM;gBAC/B,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,MAAM;oBACpE,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;QAChE,CAAC,CAAC,CAAC;QACH,IAAI,UAAU,CAAC,gBAAgB,IAAI,SAAS,CAAC,gBAAgB,EAAE;YAC9D,iBAAiB,CAChB,IAAI,CAAC,WAAW,CAAC,SAAU,CAAC,OAAO,CAAC,CAAC,YAAY,EACjD,UAAU,CAAC,gBAAiB,EAC5B,SAAS,CAAC,gBAAiB,EAC3B,IAAI,CAAC,UAAU,CACf,CAAC;SACF;IACF,CAAC;IAED,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,OAAQ,CAAC,SAAS,CAAC,CAAC,UAAU,CACtE,OAAO,CACN,CAAC,KAAK,CAAC;IACV,CAAC;IAED,gBAAgB,CAAC,KAAa;QAC7B,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;YAC3B,OAAO,CAAC,IAAI,CAAC,sBAAsB,KAAK,qBAAqB,CAAC,CAAC;YAC/D,OAAO;SACP;QACD,MAAM,aAAa,GAClB,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,OAAQ,CAAC,SAAS,CAAC,CAAC,UAAU,CAC/D,OAAO,CACN,CAAC;QACJ,aAAa,CAAC,SAAS,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC;IAC/C,CAAC;IAED,YAAY,CAAC,KAAc;QAC1B,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,CAAC,cAAc,EAAE,CAAC;IACvB,CAAC;CACD"}