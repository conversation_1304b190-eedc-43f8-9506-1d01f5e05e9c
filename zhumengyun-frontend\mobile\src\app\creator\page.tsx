'use client'

import React, { useState } from 'react'
import { 中文页面布局, 中文容器, 中文卡片, 中文按钮 } from '../../components/CleanLayout'
import {
  Plus,
  Camera,
  FileText,
  Image,
  Upload,
  MapPin,
  Users,
  Star,
  Hash,
  Calendar,
  Music,
  Video
} from 'lucide-react'

export default function CreatorPage() {
  const [contentType, setContentType] = useState('text')
  const [content, setContent] = useState('')
  const [title, setTitle] = useState('')
  const [tags, setTags] = useState('')
  const [location, setLocation] = useState('')
  const [isPublishing, setIsPublishing] = useState(false)

  const contentTypes = [
    { id: 'text', name: '文字', icon: <FileText className="w-5 h-5" /> },
    { id: 'image', name: '图片', icon: <Image className="w-5 h-5" /> },
    { id: 'video', name: '视频', icon: <Video className="w-5 h-5" /> },
    { id: 'music', name: '音频', icon: <Music className="w-5 h-5" /> }
  ]

  const handlePublish = async () => {
    setIsPublishing(true)
    // 模拟发布过程
    setTimeout(() => {
      setIsPublishing(false)
      // 重置表单
      setContent('')
      setTitle('')
      setTags('')
      setLocation('')
      alert('发布成功！')
    }, 2000)
  }

  return (
    <中文页面布局>
      <中文容器 className="py-6">
        {/* 内容类型选择 */}
        <中文卡片 className="mb-4">
          <h2 className="text-lg font-semibold text-[#24292f] mb-3">选择内容类型</h2>
          <div className="grid grid-cols-4 gap-3">
            {contentTypes.map((type) => (
              <button
                key={type.id}
                onClick={() => setContentType(type.id)}
                className={`flex flex-col items-center p-3 rounded-md border transition-colors ${
                  contentType === type.id
                    ? 'border-[#0969da] bg-[#ddf4ff] text-[#0969da]'
                    : 'border-[#d0d7de] bg-white text-[#656d76] hover:border-[#0969da]'
                }`}
              >
                {type.icon}
                <span className="text-xs mt-1">{type.name}</span>
              </button>
            ))}
          </div>
        </中文卡片>

        {/* 发布表单 */}
        <中文卡片 className="mb-4">
          <div className="space-y-4">
            {/* 标题输入 */}
            <div>
              <label className="block text-sm font-medium text-[#24292f] mb-2">
                标题
              </label>
              <input
                type="text"
                placeholder="输入内容标题..."
                className="中文-搜索-输入框"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
              />
            </div>

            {/* 内容输入 */}
            <div>
              <label className="block text-sm font-medium text-[#24292f] mb-2">
                内容
              </label>
              {contentType === 'text' && (
                <textarea
                  placeholder="分享你的想法..."
                  className="w-full px-3 py-2 text-sm border border-[#d0d7de] rounded-md resize-none"
                  rows={6}
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                />
              )}
              {contentType === 'image' && (
                <div className="border-2 border-dashed border-[#d0d7de] rounded-md p-8 text-center">
                  <Camera className="w-12 h-12 text-[#656d76] mx-auto mb-2" />
                  <p className="text-sm text-[#656d76] mb-2">点击上传图片</p>
                  <中文按钮 variant="secondary">
                    <Upload className="w-4 h-4 mr-2" />
                    选择图片
                  </中文按钮>
                </div>
              )}
              {contentType === 'video' && (
                <div className="border-2 border-dashed border-[#d0d7de] rounded-md p-8 text-center">
                  <Video className="w-12 h-12 text-[#656d76] mx-auto mb-2" />
                  <p className="text-sm text-[#656d76] mb-2">点击上传视频</p>
                  <中文按钮 variant="secondary">
                    <Upload className="w-4 h-4 mr-2" />
                    选择视频
                  </中文按钮>
                </div>
              )}
              {contentType === 'music' && (
                <div className="border-2 border-dashed border-[#d0d7de] rounded-md p-8 text-center">
                  <Music className="w-12 h-12 text-[#656d76] mx-auto mb-2" />
                  <p className="text-sm text-[#656d76] mb-2">点击上传音频</p>
                  <中文按钮 variant="secondary">
                    <Upload className="w-4 h-4 mr-2" />
                    选择音频
                  </中文按钮>
                </div>
              )}
            </div>

            {/* 标签输入 */}
            <div>
              <label className="block text-sm font-medium text-[#24292f] mb-2">
                标签
              </label>
              <div className="relative">
                <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#656d76] w-4 h-4" />
                <input
                  type="text"
                  placeholder="添加标签，用空格分隔"
                  className="pl-10 pr-4 py-2 text-sm border border-[#d0d7de] rounded-md w-full"
                  value={tags}
                  onChange={(e) => setTags(e.target.value)}
                />
              </div>
            </div>

            {/* 位置输入 */}
            <div>
              <label className="block text-sm font-medium text-[#24292f] mb-2">
                位置 (可选)
              </label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#656d76] w-4 h-4" />
                <input
                  type="text"
                  placeholder="添加位置信息"
                  className="pl-10 pr-4 py-2 text-sm border border-[#d0d7de] rounded-md w-full"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                />
              </div>
            </div>
          </div>
        </中文卡片>

        {/* 发布设置 */}
        <中文卡片 className="mb-4">
          <h3 className="text-sm font-medium text-[#24292f] mb-3">发布设置</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-[#656d76]">公开发布</span>
              <input type="checkbox" className="rounded" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-[#656d76]">允许评论</span>
              <input type="checkbox" className="rounded" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-[#656d76]">推送通知</span>
              <input type="checkbox" className="rounded" />
            </div>
          </div>
        </中文卡片>

        {/* 发布按钮 */}
        <div className="flex space-x-3">
          <中文按钮
            variant="secondary"
            className="flex-1"
          >
            保存草稿
          </中文按钮>
          <中文按钮
            variant="primary"
            className="flex-1"
            onClick={handlePublish}
            disabled={isPublishing || !title.trim()}
          >
            {isPublishing ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                发布中...
              </>
            ) : (
              <>
                <Plus className="w-4 h-4 mr-2" />
                发布
              </>
            )}
          </中文按钮>
        </div>
      </中文容器>
    </中文页面布局>
  )
}
