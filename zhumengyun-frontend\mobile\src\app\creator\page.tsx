'use client'

import { useState, useMemo } from 'react'
import { projectAPI } from '@/lib/project-api'

// 发布类型
type PublishType = 'video' | 'discovery' | 'community' | 'ecosystem'

// 发布目标平台
type PublishTarget = 'homepage-video' | 'discovery-content' | 'message-community' | 'ecosystem-matching'

// 生态需求类型
type EcosystemType =
  | 'tech-cooperation'      // 技术合作
  | 'content-cooperation'   // 内容合作
  | 'industry-cooperation'  // 行业合作
  | 'project-matching'      // 工程项目匹配
  | 'job-matching'          // 职位匹配
  | 'funding-matching'      // 资金匹配
  | 'supply-chain'          // 供应链匹配
  | 'enterprise-matching'   // 企业匹配
  | 'quality-content'       // 高质量内容

// 统一发布数据接口
interface PublishData {
  // 基础信息
  title: string
  description: string
  publishType: PublishType
  publishTargets: PublishTarget[]
  category: string
  type?: string

  // 内容信息
  content: {
    text: string
    media: {
      type: 'image' | 'video' | 'document' | 'audio'
      files: File[]
      thumbnails: string[]
    }
    tags: string[]
  }

  // 视频发布特定
  video: {
    duration: number
    quality: '720p' | '1080p' | '4K'
    effects: string[]
    music: string
    subtitles: boolean
  }

  // 发现页图文特定
  discovery: {
    ecosystemType: EcosystemType
    targetAudience: string[]
    collaboration: {
      type: string
      requirements: string[]
      benefits: string[]
    }
  }

  // 社群发布特定
  community: {
    groupType: 'public' | 'private' | 'professional'
    discussionTopic: string
    expertLevel: 'beginner' | 'intermediate' | 'expert'
    interactionType: 'discussion' | 'qa' | 'sharing' | 'networking'
  }

  // 生态匹配特定
  ecosystem: {
    matchingType: EcosystemType
    budget: { min: number, max: number, currency: string }
    timeline: { start: string, end: string }
    location: { city: string, province: string, remote: boolean }
    requirements: string[]
    qualifications: string[]
  }

  // 发布设置
  settings: {
    visibility: 'public' | 'private' | 'followers'
    allowComments: boolean
    allowSharing: boolean
    aiOptimization: boolean
    autoTranslation: boolean
  }
}

export default function CreatorPage() {
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<PublishType>('video')
  const [currentStep, setCurrentStep] = useState<string>('basic')
  const [publishData, setPublishData] = useState<PublishData>({
    title: '',
    description: '',
    publishType: 'video',
    publishTargets: ['homepage-video'],
    category: '',
    content: {
      text: '',
      media: { type: 'video', files: [], thumbnails: [] },
      tags: []
    },
    video: {
      duration: 0,
      quality: '1080p',
      effects: [],
      music: '',
      subtitles: false
    },
    discovery: {
      ecosystemType: 'tech-cooperation',
      targetAudience: [],
      collaboration: { type: '', requirements: [], benefits: [] }
    },
    community: {
      groupType: 'public',
      discussionTopic: '',
      expertLevel: 'intermediate',
      interactionType: 'discussion'
    },
    ecosystem: {
      matchingType: 'project-matching',
      budget: { min: 0, max: 0, currency: 'CNY' },
      timeline: { start: '', end: '' },
      location: { city: '', province: '', remote: false },
      requirements: [],
      qualifications: []
    },
    settings: {
      visibility: 'public',
      allowComments: true,
      allowSharing: true,
      aiOptimization: true,
      autoTranslation: false
    }
  })
  const [showSuccessModal, setShowSuccessModal] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [showTemplates, setShowTemplates] = useState(false)
  const [showMusicLibrary, setShowMusicLibrary] = useState(false)
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([])
  const [uploadProgress, setUploadProgress] = useState<{[key: string]: number}>({})
  const [uploadedFiles, setUploadedFiles] = useState<{[key: string]: File[]}>({})
  const [selectedMusic, setSelectedMusic] = useState<string>('')
  const [isPlaying, setIsPlaying] = useState(false)

  // Web3功能状态
  const [enableNFT, setEnableNFT] = useState(false)
  const [nftPrice, setNftPrice] = useState<number>(0)
  const [nftRoyalty, setNftRoyalty] = useState<number>(10)
  const [expectedRewards, setExpectedRewards] = useState<number>(0)
  const [creatorLevel, setCreatorLevel] = useState<number>(3)
  const [didVerified, setDidVerified] = useState(true)

  // 10步发布流程配置
  const publishSteps = [
    { key: 'basic', title: '基础信息', icon: '📝' },
    { key: 'requirements', title: '需求详情', icon: '📋' },
    { key: 'conditions', title: '条件设置', icon: '⚙️' },
    { key: 'files', title: '文件上传', icon: '📁' },
    { key: 'skills', title: '技能标签', icon: '🏷️' },
    { key: 'preview', title: '预览确认', icon: '👀' },
    { key: 'confirm', title: '确认发布', icon: '✅' },
    { key: 'submit', title: '提交处理', icon: '🚀' },
    { key: 'review', title: '自动审核', icon: '🔍' },
    { key: 'success', title: '发布成功', icon: '🎉' }
  ]

  // 项目分类配置
  const projectCategories = [
    { value: 'construction', label: '建筑工程', icon: '🏗️' },
    { value: 'design', label: '设计服务', icon: '🎨' },
    { value: 'consulting', label: '咨询服务', icon: '💼' },
    { value: 'technology', label: '技术开发', icon: '💻' },
    { value: 'planning', label: '规划设计', icon: '📐' },
    { value: 'management', label: '项目管理', icon: '📊' }
  ]

  // 四大发布功能配置
  const publishTabs = [
    {
      key: 'video' as PublishType,
      title: '首页视频',
      icon: '🎬',
      description: '发布到首页视频流，展示项目动态',
      target: 'homepage-video' as PublishTarget
    },
    {
      key: 'discovery' as PublishType,
      title: '发现图文',
      icon: '📰',
      description: '发布到发现页面，展示生态需求',
      target: 'discovery-content' as PublishTarget
    },
    {
      key: 'community' as PublishType,
      title: '消息社群',
      icon: '💬',
      description: '发布到消息页面社群讨论',
      target: 'message-community' as PublishTarget
    },
    {
      key: 'ecosystem' as PublishType,
      title: '生态匹配',
      icon: '🤝',
      description: '发布生态需求，智能匹配合作',
      target: 'ecosystem-matching' as PublishTarget
    }
  ]

  // 生态需求类型配置
  const ecosystemTypes = [
    { value: 'tech-cooperation', label: '技术合作', icon: '⚡', description: '寻求技术合作伙伴' },
    { value: 'content-cooperation', label: '内容合作', icon: '📚', description: '内容创作与分发合作' },
    { value: 'industry-cooperation', label: '行业合作', icon: '🏭', description: '行业应用深度合作' },
    { value: 'project-matching', label: '工程项目匹配', icon: '🏗️', description: '工程项目需求匹配' },
    { value: 'job-matching', label: '职位匹配', icon: '💼', description: '人才招聘与求职匹配' },
    { value: 'funding-matching', label: '资金匹配', icon: '💰', description: '投融资需求匹配' },
    { value: 'supply-chain', label: '供应链匹配', icon: '🔗', description: '供应链合作伙伴' },
    { value: 'enterprise-matching', label: '企业匹配', icon: '🏢', description: '企业间战略合作' },
    { value: 'quality-content', label: '高质量内容', icon: '⭐', description: '优质内容创作与分享' }
  ]

  // 视频效果选项
  const videoEffects = [
    '转场特效', '滤镜美化', '字幕动画', '背景音乐', '画面稳定', '色彩调节', '慢动作', '快进效果'
  ]

  // 社群类型选项
  const communityTypes = [
    { value: 'discussion', label: '技术讨论', icon: '💭' },
    { value: 'qa', label: '问答互助', icon: '❓' },
    { value: 'sharing', label: '经验分享', icon: '📢' },
    { value: 'networking', label: '人脉拓展', icon: '🌐' }
  ]

  // 音乐库数据
  const musicLibrary = [
    {
      id: 'tech-1',
      name: '科技未来',
      category: 'tech',
      duration: '2:30',
      url: '/music/tech-future.mp3',
      description: '现代科技感背景音乐，适合技术展示'
    },
    {
      id: 'corporate-1',
      name: '企业力量',
      category: 'corporate',
      duration: '3:15',
      url: '/music/corporate-power.mp3',
      description: '专业企业宣传音乐，彰显实力'
    },
    {
      id: 'inspiring-1',
      name: '梦想启航',
      category: 'inspiring',
      duration: '2:45',
      url: '/music/dream-launch.mp3',
      description: '励志向上的音乐，激发正能量'
    },
    {
      id: 'ambient-1',
      name: '宁静致远',
      category: 'ambient',
      duration: '4:00',
      url: '/music/peaceful.mp3',
      description: '轻柔环境音乐，营造舒适氛围'
    }
  ]

  // 积分奖励配置
  const rewardConfig = {
    video: { base: 50, quality: 20, engagement: 30 },
    discovery: { base: 30, quality: 15, engagement: 25 },
    community: { base: 20, quality: 10, engagement: 40 },
    ecosystem: { base: 40, quality: 25, engagement: 35 }
  }

  // 发布模板数据
  const publishTemplates = [
    {
      id: 'video-project-update',
      type: 'video',
      title: '项目进度更新',
      description: '展示工程项目最新进展',
      template: {
        title: '【项目进展】{项目名称} - 第{阶段}阶段完成',
        description: '项目概况：\n✅ 已完成工作：\n🔄 正在进行：\n📅 下阶段计划：\n\n#工程进展 #项目管理',
        tags: ['工程进展', '项目管理', '建筑施工']
      }
    },
    {
      id: 'discovery-tech-cooperation',
      type: 'discovery',
      title: '技术合作需求',
      description: '寻求技术合作伙伴',
      template: {
        title: '寻求{技术领域}技术合作伙伴',
        description: '项目背景：\n\n技术需求：\n• \n• \n• \n\n合作方式：\n期望收益：\n\n联系方式：\n\n#技术合作 #工程技术',
        tags: ['技术合作', '工程技术', '合作伙伴']
      }
    },
    {
      id: 'community-tech-discussion',
      type: 'community',
      title: '技术讨论话题',
      description: '发起专业技术讨论',
      template: {
        title: '关于{技术话题}的讨论',
        description: '问题描述：\n\n遇到的挑战：\n\n期望讨论的方向：\n1. \n2. \n3. \n\n欢迎各位专家分享经验和见解！\n\n#技术讨论 #经验分享',
        tags: ['技术讨论', '经验分享', '专业交流']
      }
    },
    {
      id: 'ecosystem-project-matching',
      type: 'ecosystem',
      title: '工程项目匹配',
      description: '发布工程项目需求',
      template: {
        title: '{项目类型}项目寻求合作团队',
        description: '项目概述：\n\n项目规模：\n技术要求：\n质量标准：\n\n团队要求：\n• 资质要求：\n• 经验要求：\n• 团队规模：\n\n#工程项目 #团队合作',
        tags: ['工程项目', '团队合作', '项目匹配']
      }
    }
  ]

  // 更新发布数据
  const updatePublishData = (field: string, value: any) => {
    setPublishData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // 切换发布类型
  const switchPublishType = (type: PublishType) => {
    setActiveTab(type)
    setPublishData(prev => ({
      ...prev,
      publishType: type,
      publishTargets: [publishTabs.find(tab => tab.key === type)?.target || 'homepage-video']
    }))
  }

  // 步骤导航函数
  const nextStep = () => {
    const currentIndex = publishSteps.findIndex(step => step.key === currentStep)
    if (currentIndex < publishSteps.length - 1) {
      setCurrentStep(publishSteps[currentIndex + 1].key)
    }
  }

  const prevStep = () => {
    const currentIndex = publishSteps.findIndex(step => step.key === currentStep)
    if (currentIndex > 0) {
      setCurrentStep(publishSteps[currentIndex - 1].key)
    }
  }

  // 提交项目
  const submitProject = async () => {
    setLoading(true)
    setCurrentStep('submit')
    
    try {
      // 模拟提交过程
      setTimeout(() => {
        setCurrentStep('review')
        setTimeout(() => {
          setCurrentStep('success')
          setLoading(false)
        }, 2000)
      }, 2000)
    } catch (error) {
      console.error('项目提交失败:', error)
      setLoading(false)
    }
  }

  // 提交发布
  const submitPublish = async () => {
    setLoading(true)

    // 模拟发布过程
    setTimeout(() => {
      setShowSuccessModal(true)
      setLoading(false)

      // 重置表单
      setTimeout(() => {
        setPublishData(prev => ({
          ...prev,
          title: '',
          description: '',
          content: { ...prev.content, text: '', files: [] }
        }))
      }, 2000)
    }, 2000)
  }

  // 获取当前发布类型信息
  const getCurrentTabInfo = () => {
    return publishTabs.find(tab => tab.key === activeTab) || publishTabs[0]
  }

  // 获取当前步骤信息
  const getCurrentStepInfo = () => {
    return publishSteps.find(step => step.key === currentStep) || publishSteps[0]
  }

  // 获取步骤进度
  const getStepProgress = () => {
    const currentIndex = publishSteps.findIndex(step => step.key === currentStep)
    return ((currentIndex + 1) / publishSteps.length) * 100
  }

  // 应用模板
  const applyTemplate = (template: any) => {
    setPublishData(prev => ({
      ...prev,
      title: template.template.title,
      description: template.template.description,
      content: {
        ...prev.content,
        tags: template.template.tags
      }
    }))
    setShowTemplates(false)
  }

  // 生成AI建议
  const generateAISuggestions = (content: string) => {
    if (content.length < 10) return

    // 模拟AI建议
    const suggestions = [
      '建议添加项目时间节点',
      '可以增加技术细节描述',
      '推荐添加相关标签提高曝光',
      '建议上传项目图片或视频',
      '可以提及团队规模和经验要求'
    ]

    setAiSuggestions(suggestions.slice(0, 3))
  }

  // 文件上传处理
  const handleFileUpload = async (files: FileList, type: string) => {
    const fileArray = Array.from(files)

    // 验证文件类型和大小
    const validFiles = fileArray.filter(file => {
      if (type === 'video') {
        return file.type.startsWith('video/') && file.size <= 500 * 1024 * 1024 // 500MB
      } else if (type === 'image') {
        return file.type.startsWith('image/') && file.size <= 10 * 1024 * 1024 // 10MB
      } else if (type === 'audio') {
        return file.type.startsWith('audio/') && file.size <= 50 * 1024 * 1024 // 50MB
      }
      return false
    })

    if (validFiles.length === 0) {
      alert('请选择有效的文件格式和大小')
      return
    }

    // 模拟上传进度
    for (const file of validFiles) {
      const fileId = `${type}-${Date.now()}-${Math.random()}`

      // 开始上传
      setUploadProgress(prev => ({ ...prev, [fileId]: 0 }))

      // 模拟上传进度
      const uploadInterval = setInterval(() => {
        setUploadProgress(prev => {
          const currentProgress = prev[fileId] || 0
          if (currentProgress >= 100) {
            clearInterval(uploadInterval)
            return prev
          }
          return { ...prev, [fileId]: currentProgress + 10 }
        })
      }, 200)

      // 上传完成后添加到已上传文件
      setTimeout(() => {
        setUploadedFiles(prev => ({
          ...prev,
          [type]: [...(prev[type] || []), file]
        }))
        setUploadProgress(prev => {
          const newProgress = { ...prev }
          delete newProgress[fileId]
          return newProgress
        })
      }, 2000)
    }
  }

  // 音乐选择处理
  const handleMusicSelect = (musicId: string) => {
    setSelectedMusic(musicId)
    updatePublishData('video', { ...publishData.video, music: musicId })
    setShowMusicLibrary(false)
  }

  // 音乐播放控制
  const toggleMusicPlay = () => {
    setIsPlaying(!isPlaying)
    // 这里可以添加实际的音频播放逻辑
  }

  // 使用useMemo缓存奖励计算结果
  const currentRewards = useMemo(() => {
    // 映射activeTab到rewardConfig的键
    const configKey = activeTab === 'video' ? 'video' :
                     activeTab === 'discovery' ? 'discovery' :
                     activeTab === 'community' ? 'community' : 'ecosystem'

    const config = rewardConfig[configKey] || rewardConfig.video
    const baseReward = config.base
    const qualityBonus = enableNFT ? config.quality : 0
    const engagementBonus = didVerified ? config.engagement : 0
    const total = baseReward + qualityBonus + engagementBonus
    return total
  }, [activeTab, enableNFT, didVerified])

  // 计算预期奖励 - 返回缓存的结果
  const calculateRewards = () => {
    return currentRewards
  }

  // 更新奖励状态的函数
  const updateRewards = () => {
    setExpectedRewards(currentRewards)
  }

  // 加载状态页面
  if (loading && (currentStep === 'submit' || currentStep === 'review')) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-20 h-20 bg-gradient-to-r from-purple-400 to-blue-500 rounded-3xl flex items-center justify-center mb-6 mx-auto animate-pulse">
            <span className="text-white font-bold text-3xl">🚀</span>
          </div>
          <p className="text-purple-300 text-xl mb-3">项目发布中</p>
          <p className="text-white text-sm mb-4">
            {currentStep === 'submit' ? 'AI智能处理项目信息...' : 'AI自动审核项目内容...'}
          </p>
          <div className="w-64 bg-white/20 rounded-full h-2 mx-auto">
            <div className="bg-gradient-to-r from-purple-400 to-blue-500 h-2 rounded-full animate-pulse" style={{ width: '75%' }}></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
      {/* 发布页面 */}
      <div className="relative z-10">
        {/* 顶部导航 */}
        <div className="sticky top-0 bg-black/20 backdrop-blur-lg border-b border-white/10 p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-400 to-blue-500 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">🚀</span>
              </div>
              <div>
                <h1 className="text-xl font-bold">内容发布</h1>
                <p className="text-sm text-purple-300">四大平台 • 智能分发 • 生态匹配</p>
              </div>
            </div>
            <div className="text-right">
              <div className="flex space-x-2 mb-2">
                <button
                  onClick={() => setShowTemplates(true)}
                  className="px-3 py-1 bg-white/10 border border-white/20 rounded-lg text-xs text-white hover:bg-white/20 transition-colors"
                >
                  📋 模板
                </button>
                <button
                  onClick={() => {
                    // 快速发布：清空表单，设置默认值
                    setPublishData(prev => ({
                      ...prev,
                      title: '',
                      description: '',
                      content: { ...prev.content, text: '', tags: [] }
                    }))
                  }}
                  className="px-3 py-1 bg-white/10 border border-white/20 rounded-lg text-xs text-white hover:bg-white/20 transition-colors"
                >
                  🚀 快速
                </button>
                <button
                  onClick={() => {
                    // 这里可以添加查看发布历史的功能
                    alert('发布历史功能开发中...')
                  }}
                  className="px-3 py-1 bg-white/10 border border-white/20 rounded-lg text-xs text-white hover:bg-white/20 transition-colors"
                >
                  📊 历史
                </button>
              </div>
              <p className="text-sm text-gray-400">{getCurrentTabInfo().title}</p>
              <p className="text-xs text-purple-300">{getCurrentTabInfo().description}</p>
            </div>
          </div>

          {/* 发布类型标签页 */}
          <div className="flex space-x-1 bg-white/10 rounded-lg p-1">
            {publishTabs.map((tab) => (
              <button
                key={tab.key}
                onClick={() => switchPublishType(tab.key)}
                className={`flex-1 flex flex-col items-center py-3 px-2 rounded-md text-xs font-medium transition-colors ${
                  activeTab === tab.key
                    ? 'bg-purple-500 text-white'
                    : 'text-white/70 hover:text-white'
                }`}
              >
                <span className="text-lg mb-1">{tab.icon}</span>
                <span>{tab.title}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 p-4 pb-20">
          {/* 首页视频发布 */}
          {activeTab === 'video' && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <div className="text-4xl mb-3">🎬</div>
                <h2 className="text-2xl font-bold mb-2">首页视频发布</h2>
                <p className="text-gray-400">发布到首页视频流，展示项目动态和工程进展</p>
              </div>

              {/* 视频标题 */}
              <div>
                <label className="block text-sm font-medium mb-2">视频标题 *</label>
                <input
                  type="text"
                  value={publishData.title}
                  onChange={(e) => updatePublishData('title', e.target.value)}
                  placeholder="请输入视频标题"
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                />
              </div>

              {/* 视频描述 */}
              <div>
                <label className="block text-sm font-medium mb-2">视频描述 *</label>
                <textarea
                  value={publishData.description}
                  onChange={(e) => {
                    updatePublishData('description', e.target.value)
                    generateAISuggestions(e.target.value)
                  }}
                  placeholder="请描述视频内容、项目亮点和技术特色..."
                  rows={4}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 resize-none"
                />

                {/* AI建议 */}
                {aiSuggestions.length > 0 && (
                  <div className="mt-3 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                    <div className="flex items-center mb-2">
                      <span className="text-blue-400 text-sm font-medium">🤖 AI建议</span>
                    </div>
                    <div className="space-y-1">
                      {aiSuggestions.map((suggestion, index) => (
                        <div key={index} className="text-xs text-blue-300">
                          • {suggestion}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* 视频上传 */}
              <div>
                <label className="block text-sm font-medium mb-2">视频文件 *</label>
                <div
                  className="border-2 border-dashed border-white/20 rounded-lg p-8 text-center hover:border-purple-500 transition-colors cursor-pointer"
                  onClick={() => document.getElementById('video-upload')?.click()}
                  onDragOver={(e) => e.preventDefault()}
                  onDrop={(e) => {
                    e.preventDefault()
                    if (e.dataTransfer.files) {
                      handleFileUpload(e.dataTransfer.files, 'video')
                    }
                  }}
                >
                  <div className="text-4xl mb-4">📹</div>
                  <p className="text-gray-400 mb-2">点击或拖拽上传视频文件</p>
                  <p className="text-xs text-gray-500">支持 MP4, MOV, AVI 格式，最大 500MB</p>
                  <input
                    id="video-upload"
                    type="file"
                    accept="video/*"
                    multiple
                    className="hidden"
                    onChange={(e) => {
                      if (e.target.files) {
                        handleFileUpload(e.target.files, 'video')
                      }
                    }}
                  />
                </div>

                {/* 上传进度 */}
                {Object.entries(uploadProgress).map(([fileId, progress]) => (
                  <div key={fileId} className="mt-3">
                    <div className="flex justify-between text-sm text-gray-400 mb-1">
                      <span>上传中...</span>
                      <span>{progress}%</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-purple-400 to-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      ></div>
                    </div>
                  </div>
                ))}

                {/* 已上传文件 */}
                {uploadedFiles.video && uploadedFiles.video.length > 0 && (
                  <div className="mt-3 space-y-2">
                    {uploadedFiles.video.map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="text-2xl">🎬</div>
                          <div>
                            <p className="text-white text-sm font-medium">{file.name}</p>
                            <p className="text-gray-400 text-xs">{(file.size / 1024 / 1024).toFixed(1)} MB</p>
                          </div>
                        </div>
                        <button
                          onClick={() => {
                            setUploadedFiles(prev => ({
                              ...prev,
                              video: prev.video?.filter((_, i) => i !== index) || []
                            }))
                          }}
                          className="text-red-400 hover:text-red-300"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* 视频设置 */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">视频质量</label>
                  <select
                    value={publishData.video.quality}
                    onChange={(e) => updatePublishData('video', { ...publishData.video, quality: e.target.value })}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-purple-500"
                  >
                    <option value="720p" className="bg-gray-800">720p HD</option>
                    <option value="1080p" className="bg-gray-800">1080p Full HD</option>
                    <option value="4K" className="bg-gray-800">4K Ultra HD</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">背景音乐</label>
                  <div className="space-y-3">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setShowMusicLibrary(true)}
                        className="flex-1 bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white hover:bg-white/20 transition-colors"
                      >
                        🎵 选择音乐
                      </button>
                      <button
                        onClick={() => document.getElementById('music-upload')?.click()}
                        className="flex-1 bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white hover:bg-white/20 transition-colors"
                      >
                        📁 上传音乐
                      </button>
                    </div>

                    <input
                      id="music-upload"
                      type="file"
                      accept="audio/*"
                      className="hidden"
                      onChange={(e) => {
                        if (e.target.files) {
                          handleFileUpload(e.target.files, 'audio')
                        }
                      }}
                    />

                    {/* 已选择的音乐 */}
                    {selectedMusic && (
                      <div className="p-3 bg-white/5 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="text-2xl">🎵</div>
                            <div>
                              <p className="text-white text-sm font-medium">
                                {musicLibrary.find(m => m.id === selectedMusic)?.name || '自定义音乐'}
                              </p>
                              <p className="text-gray-400 text-xs">
                                {musicLibrary.find(m => m.id === selectedMusic)?.duration || '未知时长'}
                              </p>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              onClick={toggleMusicPlay}
                              className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white hover:bg-purple-600 transition-colors"
                            >
                              {isPlaying ? '⏸️' : '▶️'}
                            </button>
                            <button
                              onClick={() => {
                                setSelectedMusic('')
                                updatePublishData('video', { ...publishData.video, music: '' })
                              }}
                              className="text-red-400 hover:text-red-300"
                            >
                              ×
                            </button>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* 已上传的音频文件 */}
                    {uploadedFiles.audio && uploadedFiles.audio.length > 0 && (
                      <div className="space-y-2">
                        {uploadedFiles.audio.map((file, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                            <div className="flex items-center space-x-3">
                              <div className="text-2xl">🎵</div>
                              <div>
                                <p className="text-white text-sm font-medium">{file.name}</p>
                                <p className="text-gray-400 text-xs">{(file.size / 1024 / 1024).toFixed(1)} MB</p>
                              </div>
                            </div>
                            <div className="flex space-x-2">
                              <button
                                onClick={() => {
                                  setSelectedMusic(`custom-${index}`)
                                  updatePublishData('video', { ...publishData.video, music: file.name })
                                }}
                                className="px-3 py-1 bg-purple-500 rounded text-white text-xs hover:bg-purple-600 transition-colors"
                              >
                                选择
                              </button>
                              <button
                                onClick={() => {
                                  setUploadedFiles(prev => ({
                                    ...prev,
                                    audio: prev.audio?.filter((_, i) => i !== index) || []
                                  }))
                                }}
                                className="text-red-400 hover:text-red-300"
                              >
                                ×
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 视频效果 */}
              <div>
                <label className="block text-sm font-medium mb-2">视频效果</label>
                <div className="flex flex-wrap gap-2">
                  {videoEffects.map((effect) => (
                    <button
                      key={effect}
                      onClick={() => {
                        const effects = publishData.video.effects.includes(effect)
                          ? publishData.video.effects.filter(e => e !== effect)
                          : [...publishData.video.effects, effect]
                        updatePublishData('video', { ...publishData.video, effects })
                      }}
                      className={`px-3 py-1 rounded-full text-sm transition-colors ${
                        publishData.video.effects.includes(effect)
                          ? 'bg-purple-500 text-white'
                          : 'bg-white/10 text-gray-400 hover:text-white'
                      }`}
                    >
                      {effect}
                    </button>
                  ))}
                </div>
              </div>

              {/* 字幕设置 */}
              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={publishData.video.subtitles}
                    onChange={(e) => updatePublishData('video', { ...publishData.video, subtitles: e.target.checked })}
                    className="w-4 h-4 text-purple-500 bg-white/10 border-white/20 rounded focus:ring-purple-500"
                  />
                  <span className="text-sm text-gray-300">自动生成字幕</span>
                </label>
              </div>
            </div>
          )}

          {/* 发现图文发布 */}
          {activeTab === 'discovery' && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <div className="text-4xl mb-3">📰</div>
                <h2 className="text-2xl font-bold mb-2">发现图文发布</h2>
                <p className="text-gray-400">发布到发现页面，展示各种生态需求和合作机会</p>
              </div>

              {/* 生态需求类型 */}
              <div>
                <label className="block text-sm font-medium mb-2">生态需求类型 *</label>
                <div className="grid grid-cols-2 gap-3">
                  {ecosystemTypes.map((type) => (
                    <button
                      key={type.value}
                      onClick={() => updatePublishData('discovery', {
                        ...publishData.discovery,
                        ecosystemType: type.value
                      })}
                      className={`p-4 rounded-lg border transition-colors text-left ${
                        publishData.discovery.ecosystemType === type.value
                          ? 'bg-purple-500/20 border-purple-500 text-white'
                          : 'bg-white/5 border-white/20 text-gray-400 hover:border-white/40'
                      }`}
                    >
                      <div className="text-2xl mb-2">{type.icon}</div>
                      <div className="text-sm font-medium mb-1">{type.label}</div>
                      <div className="text-xs text-gray-500">{type.description}</div>
                    </button>
                  ))}
                </div>
              </div>

              {/* 内容标题 */}
              <div>
                <label className="block text-sm font-medium mb-2">内容标题 *</label>
                <input
                  type="text"
                  value={publishData.title}
                  onChange={(e) => updatePublishData('title', e.target.value)}
                  placeholder="请输入内容标题"
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                />
              </div>

              {/* 内容描述 */}
              <div>
                <label className="block text-sm font-medium mb-2">详细描述 *</label>
                <textarea
                  value={publishData.content.text}
                  onChange={(e) => updatePublishData('content', {
                    ...publishData.content,
                    text: e.target.value
                  })}
                  placeholder="请详细描述您的需求、合作方式、期望成果等..."
                  rows={6}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 resize-none"
                />
              </div>

              {/* 图片上传 */}
              <div>
                <label className="block text-sm font-medium mb-2">配图上传</label>
                <div
                  className="border-2 border-dashed border-white/20 rounded-lg p-6 text-center hover:border-purple-500 transition-colors cursor-pointer"
                  onClick={() => document.getElementById('image-upload')?.click()}
                  onDragOver={(e) => e.preventDefault()}
                  onDrop={(e) => {
                    e.preventDefault()
                    if (e.dataTransfer.files) {
                      handleFileUpload(e.dataTransfer.files, 'image')
                    }
                  }}
                >
                  <div className="text-3xl mb-3">🖼️</div>
                  <p className="text-gray-400 mb-2">点击或拖拽上传图片</p>
                  <p className="text-xs text-gray-500">支持 JPG, PNG, GIF 格式，最大 10MB</p>
                  <input
                    id="image-upload"
                    type="file"
                    accept="image/*"
                    multiple
                    className="hidden"
                    onChange={(e) => {
                      if (e.target.files) {
                        handleFileUpload(e.target.files, 'image')
                      }
                    }}
                  />
                </div>

                {/* 已上传图片 */}
                {uploadedFiles.image && uploadedFiles.image.length > 0 && (
                  <div className="mt-3 grid grid-cols-2 gap-3">
                    {uploadedFiles.image.map((file, index) => (
                      <div key={index} className="relative group">
                        <div className="aspect-square bg-white/5 rounded-lg overflow-hidden">
                          <img
                            src={URL.createObjectURL(file)}
                            alt={file.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <button
                          onClick={() => {
                            setUploadedFiles(prev => ({
                              ...prev,
                              image: prev.image?.filter((_, i) => i !== index) || []
                            }))
                          }}
                          className="absolute top-2 right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-sm opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          ×
                        </button>
                        <div className="mt-1 text-xs text-gray-400 truncate">{file.name}</div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* 目标受众 */}
              <div>
                <label className="block text-sm font-medium mb-2">目标受众</label>
                <div className="flex flex-wrap gap-2">
                  {['工程师', '建筑师', '项目经理', '投资人', '企业家', '技术专家', '设计师', '开发者'].map((audience) => (
                    <button
                      key={audience}
                      onClick={() => {
                        const audiences = publishData.discovery.targetAudience.includes(audience)
                          ? publishData.discovery.targetAudience.filter(a => a !== audience)
                          : [...publishData.discovery.targetAudience, audience]
                        updatePublishData('discovery', {
                          ...publishData.discovery,
                          targetAudience: audiences
                        })
                      }}
                      className={`px-3 py-1 rounded-full text-sm transition-colors ${
                        publishData.discovery.targetAudience.includes(audience)
                          ? 'bg-blue-500 text-white'
                          : 'bg-white/10 text-gray-400 hover:text-white'
                      }`}
                    >
                      {audience}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 消息社群发布 */}
          {activeTab === 'community' && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <div className="text-4xl mb-3">💬</div>
                <h2 className="text-2xl font-bold mb-2">消息社群发布</h2>
                <p className="text-gray-400">发布到消息页面社群，开启专业讨论和交流</p>
              </div>

              {/* 社群类型 */}
              <div>
                <label className="block text-sm font-medium mb-2">社群类型 *</label>
                <div className="grid grid-cols-2 gap-3">
                  {communityTypes.map((type) => (
                    <button
                      key={type.value}
                      onClick={() => updatePublishData('community', {
                        ...publishData.community,
                        interactionType: type.value
                      })}
                      className={`p-4 rounded-lg border transition-colors ${
                        publishData.community.interactionType === type.value
                          ? 'bg-green-500/20 border-green-500 text-white'
                          : 'bg-white/5 border-white/20 text-gray-400 hover:border-white/40'
                      }`}
                    >
                      <div className="text-2xl mb-2">{type.icon}</div>
                      <div className="text-sm font-medium">{type.label}</div>
                    </button>
                  ))}
                </div>
              </div>

              {/* 讨论主题 */}
              <div>
                <label className="block text-sm font-medium mb-2">讨论主题 *</label>
                <input
                  type="text"
                  value={publishData.community.discussionTopic}
                  onChange={(e) => updatePublishData('community', {
                    ...publishData.community,
                    discussionTopic: e.target.value
                  })}
                  placeholder="请输入讨论主题"
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                />
              </div>

              {/* 内容详情 */}
              <div>
                <label className="block text-sm font-medium mb-2">内容详情 *</label>
                <textarea
                  value={publishData.content.text}
                  onChange={(e) => updatePublishData('content', {
                    ...publishData.content,
                    text: e.target.value
                  })}
                  placeholder="请详细描述您想讨论的内容、遇到的问题或想分享的经验..."
                  rows={6}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 resize-none"
                />
              </div>

              {/* 专业等级 */}
              <div>
                <label className="block text-sm font-medium mb-2">专业等级</label>
                <div className="flex space-x-3">
                  {[
                    { value: 'beginner', label: '初学者', icon: '🌱' },
                    { value: 'intermediate', label: '中级', icon: '🌿' },
                    { value: 'expert', label: '专家', icon: '🌳' }
                  ].map((level) => (
                    <button
                      key={level.value}
                      onClick={() => updatePublishData('community', {
                        ...publishData.community,
                        expertLevel: level.value
                      })}
                      className={`flex-1 p-3 rounded-lg border transition-colors ${
                        publishData.community.expertLevel === level.value
                          ? 'bg-blue-500/20 border-blue-500 text-white'
                          : 'bg-white/5 border-white/20 text-gray-400 hover:border-white/40'
                      }`}
                    >
                      <div className="text-xl mb-1">{level.icon}</div>
                      <div className="text-sm font-medium">{level.label}</div>
                    </button>
                  ))}
                </div>
              </div>

              {/* 社群设置 */}
              <div>
                <label className="block text-sm font-medium mb-2">社群设置</label>
                <div className="space-y-3">
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="groupType"
                      value="public"
                      checked={publishData.community.groupType === 'public'}
                      onChange={(e) => updatePublishData('community', {
                        ...publishData.community,
                        groupType: e.target.value
                      })}
                      className="w-4 h-4 text-purple-500 bg-white/10 border-white/20 focus:ring-purple-500"
                    />
                    <span className="text-sm text-gray-300">公开讨论 - 所有人可见</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="groupType"
                      value="professional"
                      checked={publishData.community.groupType === 'professional'}
                      onChange={(e) => updatePublishData('community', {
                        ...publishData.community,
                        groupType: e.target.value
                      })}
                      className="w-4 h-4 text-purple-500 bg-white/10 border-white/20 focus:ring-purple-500"
                    />
                    <span className="text-sm text-gray-300">专业群组 - 仅认证用户</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="groupType"
                      value="private"
                      checked={publishData.community.groupType === 'private'}
                      onChange={(e) => updatePublishData('community', {
                        ...publishData.community,
                        groupType: e.target.value
                      })}
                      className="w-4 h-4 text-purple-500 bg-white/10 border-white/20 focus:ring-purple-500"
                    />
                    <span className="text-sm text-gray-300">私密群组 - 邀请制</span>
                  </label>
                </div>
              </div>
            </div>
          )}

          {/* 生态匹配发布 */}
          {activeTab === 'ecosystem' && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <div className="text-4xl mb-3">🤝</div>
                <h2 className="text-2xl font-bold mb-2">生态匹配发布</h2>
                <p className="text-gray-400">发布生态需求，AI智能匹配合作伙伴</p>
              </div>

              {/* 匹配类型 */}
              <div>
                <label className="block text-sm font-medium mb-2">匹配类型 *</label>
                <select
                  value={publishData.ecosystem.matchingType}
                  onChange={(e) => updatePublishData('ecosystem', {
                    ...publishData.ecosystem,
                    matchingType: e.target.value
                  })}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-purple-500"
                >
                  {ecosystemTypes.map((type) => (
                    <option key={type.value} value={type.value} className="bg-gray-800">
                      {type.icon} {type.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* 需求标题 */}
              <div>
                <label className="block text-sm font-medium mb-2">需求标题 *</label>
                <input
                  type="text"
                  value={publishData.title}
                  onChange={(e) => updatePublishData('title', e.target.value)}
                  placeholder="请输入需求标题"
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                />
              </div>

              {/* 详细需求 */}
              <div>
                <label className="block text-sm font-medium mb-2">详细需求 *</label>
                <textarea
                  value={publishData.description}
                  onChange={(e) => updatePublishData('description', e.target.value)}
                  placeholder="请详细描述您的需求、期望的合作方式、项目背景等..."
                  rows={5}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 resize-none"
                />
              </div>

              {/* 预算范围 */}
              <div>
                <label className="block text-sm font-medium mb-2">预算范围</label>
                <div className="grid grid-cols-2 gap-4">
                  <input
                    type="number"
                    value={publishData.ecosystem.budget.min}
                    onChange={(e) => updatePublishData('ecosystem', {
                      ...publishData.ecosystem,
                      budget: { ...publishData.ecosystem.budget, min: Number(e.target.value) }
                    })}
                    placeholder="最低预算"
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                  />
                  <input
                    type="number"
                    value={publishData.ecosystem.budget.max}
                    onChange={(e) => updatePublishData('ecosystem', {
                      ...publishData.ecosystem,
                      budget: { ...publishData.ecosystem.budget, max: Number(e.target.value) }
                    })}
                    placeholder="最高预算"
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                  />
                </div>
              </div>

              {/* 时间要求 */}
              <div>
                <label className="block text-sm font-medium mb-2">时间要求</label>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">开始时间</label>
                    <input
                      type="date"
                      value={publishData.ecosystem.timeline.start}
                      onChange={(e) => updatePublishData('ecosystem', {
                        ...publishData.ecosystem,
                        timeline: { ...publishData.ecosystem.timeline, start: e.target.value }
                      })}
                      className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-purple-500"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">结束时间</label>
                    <input
                      type="date"
                      value={publishData.ecosystem.timeline.end}
                      onChange={(e) => updatePublishData('ecosystem', {
                        ...publishData.ecosystem,
                        timeline: { ...publishData.ecosystem.timeline, end: e.target.value }
                      })}
                      className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-purple-500"
                    />
                  </div>
                </div>
              </div>

              {/* 地理位置 */}
              <div>
                <label className="block text-sm font-medium mb-2">地理位置</label>
                <div className="grid grid-cols-2 gap-4 mb-3">
                  <input
                    type="text"
                    value={publishData.ecosystem.location.province}
                    onChange={(e) => updatePublishData('ecosystem', {
                      ...publishData.ecosystem,
                      location: { ...publishData.ecosystem.location, province: e.target.value }
                    })}
                    placeholder="省份"
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                  />
                  <input
                    type="text"
                    value={publishData.ecosystem.location.city}
                    onChange={(e) => updatePublishData('ecosystem', {
                      ...publishData.ecosystem,
                      location: { ...publishData.ecosystem.location, city: e.target.value }
                    })}
                    placeholder="城市"
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                  />
                </div>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={publishData.ecosystem.location.remote}
                    onChange={(e) => updatePublishData('ecosystem', {
                      ...publishData.ecosystem,
                      location: { ...publishData.ecosystem.location, remote: e.target.checked }
                    })}
                    className="w-4 h-4 text-purple-500 bg-white/10 border-white/20 rounded focus:ring-purple-500"
                  />
                  <span className="text-sm text-gray-300">支持远程合作</span>
                </label>
              </div>
            </div>
          )}

          {/* 高级设置 - 所有发布类型通用 */}
          <div className="mt-8 space-y-6">
            <div className="border-t border-white/20 pt-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <span className="text-2xl mr-2">⚙️</span>
                高级设置
              </h3>

              {/* 可见性设置 */}
              <div className="mb-6">
                <label className="block text-sm font-medium mb-2">可见性</label>
                <div className="flex space-x-3">
                  {[
                    { value: 'public', label: '公开', icon: '🌍', desc: '所有人可见' },
                    { value: 'followers', label: '粉丝', icon: '👥', desc: '仅粉丝可见' },
                    { value: 'private', label: '私密', icon: '🔒', desc: '仅自己可见' }
                  ].map((visibility) => (
                    <button
                      key={visibility.value}
                      onClick={() => updatePublishData('settings', {
                        ...publishData.settings,
                        visibility: visibility.value
                      })}
                      className={`flex-1 p-3 rounded-lg border transition-colors text-center ${
                        publishData.settings.visibility === visibility.value
                          ? 'bg-purple-500/20 border-purple-500 text-white'
                          : 'bg-white/5 border-white/20 text-gray-400 hover:border-white/40'
                      }`}
                    >
                      <div className="text-xl mb-1">{visibility.icon}</div>
                      <div className="text-sm font-medium">{visibility.label}</div>
                      <div className="text-xs text-gray-500">{visibility.desc}</div>
                    </button>
                  ))}
                </div>
              </div>

              {/* 互动设置 */}
              <div className="mb-6">
                <label className="block text-sm font-medium mb-3">互动设置</label>
                <div className="space-y-3">
                  <label className="flex items-center justify-between">
                    <span className="text-sm text-gray-300">允许评论</span>
                    <input
                      type="checkbox"
                      checked={publishData.settings.allowComments}
                      onChange={(e) => updatePublishData('settings', {
                        ...publishData.settings,
                        allowComments: e.target.checked
                      })}
                      className="w-4 h-4 text-purple-500 bg-white/10 border-white/20 rounded focus:ring-purple-500"
                    />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-sm text-gray-300">允许分享</span>
                    <input
                      type="checkbox"
                      checked={publishData.settings.allowSharing}
                      onChange={(e) => updatePublishData('settings', {
                        ...publishData.settings,
                        allowSharing: e.target.checked
                      })}
                      className="w-4 h-4 text-purple-500 bg-white/10 border-white/20 rounded focus:ring-purple-500"
                    />
                  </label>
                </div>
              </div>

              {/* AI优化设置 */}
              <div className="mb-6">
                <label className="block text-sm font-medium mb-3">AI智能优化</label>
                <div className="space-y-3">
                  <label className="flex items-center justify-between">
                    <div>
                      <span className="text-sm text-gray-300">AI内容优化</span>
                      <p className="text-xs text-gray-500">自动优化标题和描述</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={publishData.settings.aiOptimization}
                      onChange={(e) => updatePublishData('settings', {
                        ...publishData.settings,
                        aiOptimization: e.target.checked
                      })}
                      className="w-4 h-4 text-purple-500 bg-white/10 border-white/20 rounded focus:ring-purple-500"
                    />
                  </label>
                  <label className="flex items-center justify-between">
                    <div>
                      <span className="text-sm text-gray-300">自动翻译</span>
                      <p className="text-xs text-gray-500">自动生成多语言版本</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={publishData.settings.autoTranslation}
                      onChange={(e) => updatePublishData('settings', {
                        ...publishData.settings,
                        autoTranslation: e.target.checked
                      })}
                      className="w-4 h-4 text-purple-500 bg-white/10 border-white/20 rounded focus:ring-purple-500"
                    />
                  </label>
                </div>
              </div>

              {/* 标签设置 */}
              <div className="mb-6">
                <label className="block text-sm font-medium mb-2">内容标签</label>
                <div className="flex flex-wrap gap-2 mb-3">
                  {publishData.content.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-purple-500/20 border border-purple-500 rounded-full text-sm text-white flex items-center"
                    >
                      #{tag}
                      <button
                        onClick={() => {
                          const newTags = publishData.content.tags.filter((_, i) => i !== index)
                          updatePublishData('content', {
                            ...publishData.content,
                            tags: newTags
                          })
                        }}
                        className="ml-2 text-purple-300 hover:text-white"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    placeholder="添加标签"
                    className="flex-1 bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        const target = e.target as HTMLInputElement
                        const value = target.value.trim()
                        if (value && !publishData.content.tags.includes(value)) {
                          updatePublishData('content', {
                            ...publishData.content,
                            tags: [...publishData.content.tags, value]
                          })
                          target.value = ''
                        }
                      }
                    }}
                  />
                  <button
                    onClick={() => {
                      const input = document.querySelector('input[placeholder="添加标签"]') as HTMLInputElement
                      const value = input?.value.trim()
                      if (value && !publishData.content.tags.includes(value)) {
                        updatePublishData('content', {
                          ...publishData.content,
                          tags: [...publishData.content.tags, value]
                        })
                        input.value = ''
                      }
                    }}
                    className="px-4 py-2 bg-purple-500 rounded-lg text-white hover:bg-purple-600 transition-colors"
                  >
                    添加
                  </button>
                </div>
              </div>

              {/* Web3功能设置 */}
              <div className="border-t border-white/20 pt-6">
                <h4 className="text-lg font-semibold mb-4 flex items-center">
                  <span className="text-2xl mr-2">🌐</span>
                  Web3功能
                </h4>

                {/* DID身份展示 */}
                <div className="mb-6 p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <span className="text-blue-400">🆔</span>
                      <span className="text-white font-medium">去中心化身份 (DID)</span>
                      {didVerified && <span className="text-green-400">✓</span>}
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-blue-300">创作者等级 {creatorLevel}</div>
                      <div className="text-xs text-gray-400">信誉分: 95/100</div>
                    </div>
                  </div>
                  <div className="text-xs text-gray-400">
                    DID: did:ngt:0x1234...5678 • 已验证身份可获得额外奖励
                  </div>
                </div>

                {/* NFT铸造选项 */}
                <div className="mb-6">
                  <label className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl">🎨</span>
                      <span className="text-white font-medium">铸造为NFT</span>
                    </div>
                    <input
                      type="checkbox"
                      checked={enableNFT}
                      onChange={(e) => {
                        setEnableNFT(e.target.checked)
                        updateRewards()
                      }}
                      className="w-4 h-4 text-purple-500 bg-white/10 border-white/20 rounded focus:ring-purple-500"
                    />
                  </label>

                  {enableNFT && (
                    <div className="space-y-3 pl-8">
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-xs text-gray-400 mb-1">NFT价格 (ETH)</label>
                          <input
                            type="number"
                            step="0.01"
                            value={nftPrice}
                            onChange={(e) => setNftPrice(Number(e.target.value))}
                            placeholder="0.00"
                            className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-400 mb-1">版税 (%)</label>
                          <input
                            type="number"
                            min="0"
                            max="20"
                            value={nftRoyalty}
                            onChange={(e) => setNftRoyalty(Number(e.target.value))}
                            className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                          />
                        </div>
                      </div>
                      <div className="text-xs text-gray-400">
                        💡 NFT将在以太坊主网铸造，支持OpenSea等市场交易
                      </div>
                    </div>
                  )}
                </div>

                {/* 积分奖励预览 */}
                <div className="mb-6 p-4 bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/20 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <span className="text-green-400">🪙</span>
                      <span className="text-white font-medium">预期奖励</span>
                    </div>
                    <button
                      onClick={updateRewards}
                      className="text-green-400 hover:text-green-300 text-sm"
                    >
                      重新计算
                    </button>
                  </div>
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-lg font-bold text-green-400">{rewardConfig[activeTab].base}</div>
                      <div className="text-xs text-gray-400">基础奖励</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-blue-400">
                        {enableNFT ? rewardConfig[activeTab].quality : 0}
                      </div>
                      <div className="text-xs text-gray-400">质量加成</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-purple-400">
                        {didVerified ? rewardConfig[activeTab].engagement : 0}
                      </div>
                      <div className="text-xs text-gray-400">身份加成</div>
                    </div>
                  </div>
                  <div className="mt-3 pt-3 border-t border-white/20 text-center">
                    <div className="text-xl font-bold text-white">
                      总计: {calculateRewards()} NGT
                    </div>
                    <div className="text-xs text-gray-400">
                      ≈ ${(calculateRewards() * 0.15).toFixed(2)} USD
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 底部发布按钮 */}
        <div className="fixed bottom-16 left-0 right-0 bg-black/20 backdrop-blur-lg border-t border-white/10 p-4">
          <div className="flex space-x-3">
            <button
              onClick={() => setShowPreview(true)}
              disabled={!publishData.title || !publishData.description}
              className={`flex-1 py-4 rounded-xl font-medium transition-colors ${
                !publishData.title || !publishData.description
                  ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  : 'bg-white/10 border border-white/20 text-white hover:bg-white/20'
              }`}
            >
              预览
            </button>
            <button
              onClick={submitPublish}
              disabled={!publishData.title || !publishData.description || loading}
              className={`flex-[2] py-4 rounded-xl font-medium transition-colors ${
                !publishData.title || !publishData.description || loading
                  ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  : 'bg-gradient-to-r from-purple-500 to-blue-500 text-white hover:from-purple-600 hover:to-blue-600'
              }`}
            >
              {loading ? '发布中...' : `发布到${getCurrentTabInfo().title}`}
            </button>
          </div>
        </div>

        {/* 音乐库选择弹窗 */}
        {showMusicLibrary && (
          <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4">
            <div className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20 max-h-[80vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white">选择背景音乐</h3>
                <button
                  onClick={() => setShowMusicLibrary(false)}
                  className="text-gray-400 hover:text-white text-2xl"
                >
                  ×
                </button>
              </div>

              <div className="space-y-4">
                {musicLibrary.map((music) => (
                  <div
                    key={music.id}
                    onClick={() => handleMusicSelect(music.id)}
                    className="p-4 bg-white/5 border border-white/20 rounded-lg hover:bg-white/10 cursor-pointer transition-colors"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-white font-medium">{music.name}</h4>
                      <span className="text-gray-400 text-sm">{music.duration}</span>
                    </div>
                    <p className="text-gray-400 text-sm mb-3">{music.description}</p>
                    <div className="flex items-center justify-between">
                      <span className="px-2 py-1 bg-purple-500/20 border border-purple-500 rounded-full text-xs text-purple-300">
                        {music.category}
                      </span>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          // 这里可以添加音乐预览播放功能
                          alert('音乐预览功能开发中...')
                        }}
                        className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white hover:bg-purple-600 transition-colors"
                      >
                        ▶️
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-6">
                <button
                  onClick={() => setShowMusicLibrary(false)}
                  className="w-full py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors"
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 模板选择弹窗 */}
        {showTemplates && (
          <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4">
            <div className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20 max-h-[80vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white">选择发布模板</h3>
                <button
                  onClick={() => setShowTemplates(false)}
                  className="text-gray-400 hover:text-white text-2xl"
                >
                  ×
                </button>
              </div>

              <div className="space-y-4">
                {publishTemplates
                  .filter(template => template.type === activeTab)
                  .map((template) => (
                    <div
                      key={template.id}
                      onClick={() => applyTemplate(template)}
                      className="p-4 bg-white/5 border border-white/20 rounded-lg hover:bg-white/10 cursor-pointer transition-colors"
                    >
                      <h4 className="text-white font-medium mb-2">{template.title}</h4>
                      <p className="text-gray-400 text-sm mb-3">{template.description}</p>
                      <div className="flex flex-wrap gap-1">
                        {template.template.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-purple-500/20 border border-purple-500 rounded-full text-xs text-purple-300"
                          >
                            #{tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  ))}
              </div>

              {publishTemplates.filter(template => template.type === activeTab).length === 0 && (
                <div className="text-center py-8">
                  <div className="text-4xl mb-3">📝</div>
                  <p className="text-gray-400">暂无{getCurrentTabInfo().title}模板</p>
                </div>
              )}

              <div className="mt-6">
                <button
                  onClick={() => setShowTemplates(false)}
                  className="w-full py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors"
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 预览弹窗 */}
        {showPreview && (
          <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4">
            <div className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20 max-h-[80vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white">内容预览</h3>
                <button
                  onClick={() => setShowPreview(false)}
                  className="text-gray-400 hover:text-white text-2xl"
                >
                  ×
                </button>
              </div>

              {/* 预览内容 */}
              <div className="space-y-4">
                {/* 发布类型标识 */}
                <div className="flex items-center space-x-2 mb-4">
                  <span className="text-2xl">{getCurrentTabInfo().icon}</span>
                  <span className="text-purple-300 font-medium">{getCurrentTabInfo().title}</span>
                </div>

                {/* 标题 */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">{publishData.title}</h4>
                </div>

                {/* 描述/内容 */}
                <div>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    {publishData.description || publishData.content.text}
                  </p>
                </div>

                {/* 标签 */}
                {publishData.content.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {publishData.content.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-purple-500/20 border border-purple-500 rounded-full text-xs text-purple-300"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                )}

                {/* 特定类型的预览信息 */}
                {activeTab === 'video' && (
                  <div className="bg-white/5 rounded-lg p-3">
                    <p className="text-xs text-gray-400 mb-2">视频设置</p>
                    <div className="text-sm text-gray-300">
                      <p>质量: {publishData.video.quality}</p>
                      {publishData.video.music && <p>背景音乐: {publishData.video.music}</p>}
                      {publishData.video.subtitles && <p>✓ 自动字幕</p>}
                    </div>
                  </div>
                )}

                {activeTab === 'discovery' && (
                  <div className="bg-white/5 rounded-lg p-3">
                    <p className="text-xs text-gray-400 mb-2">生态需求</p>
                    <div className="text-sm text-gray-300">
                      <p>类型: {ecosystemTypes.find(t => t.value === publishData.discovery.ecosystemType)?.label}</p>
                      {publishData.discovery.targetAudience.length > 0 && (
                        <p>目标受众: {publishData.discovery.targetAudience.join(', ')}</p>
                      )}
                    </div>
                  </div>
                )}

                {activeTab === 'community' && (
                  <div className="bg-white/5 rounded-lg p-3">
                    <p className="text-xs text-gray-400 mb-2">社群设置</p>
                    <div className="text-sm text-gray-300">
                      <p>讨论主题: {publishData.community.discussionTopic}</p>
                      <p>专业等级: {publishData.community.expertLevel}</p>
                      <p>群组类型: {publishData.community.groupType}</p>
                    </div>
                  </div>
                )}

                {activeTab === 'ecosystem' && (
                  <div className="bg-white/5 rounded-lg p-3">
                    <p className="text-xs text-gray-400 mb-2">匹配信息</p>
                    <div className="text-sm text-gray-300">
                      <p>类型: {ecosystemTypes.find(t => t.value === publishData.ecosystem.matchingType)?.label}</p>
                      {(publishData.ecosystem.budget.min > 0 || publishData.ecosystem.budget.max > 0) && (
                        <p>预算: {publishData.ecosystem.budget.min}-{publishData.ecosystem.budget.max} {publishData.ecosystem.budget.currency}</p>
                      )}
                      {publishData.ecosystem.location.city && (
                        <p>地点: {publishData.ecosystem.location.province} {publishData.ecosystem.location.city}</p>
                      )}
                    </div>
                  </div>
                )}

                {/* 发布设置 */}
                <div className="bg-white/5 rounded-lg p-3">
                  <p className="text-xs text-gray-400 mb-2">发布设置</p>
                  <div className="text-sm text-gray-300">
                    <p>可见性: {publishData.settings.visibility}</p>
                    <div className="flex space-x-4 mt-1">
                      {publishData.settings.allowComments && <span>✓ 允许评论</span>}
                      {publishData.settings.allowSharing && <span>✓ 允许分享</span>}
                      {publishData.settings.aiOptimization && <span>✓ AI优化</span>}
                    </div>
                  </div>
                </div>
              </div>

              {/* 预览底部按钮 */}
              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => setShowPreview(false)}
                  className="flex-1 py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors"
                >
                  继续编辑
                </button>
                <button
                  onClick={() => {
                    setShowPreview(false)
                    submitPublish()
                  }}
                  className="flex-1 py-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl text-white font-medium hover:from-purple-600 hover:to-blue-600 transition-colors"
                >
                  确认发布
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 发布成功弹窗 */}
        {showSuccessModal && (
          <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4">
            <div className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-8 max-w-sm w-full border border-white/20 text-center">
              <div className="text-6xl mb-4">🎉</div>
              <h3 className="text-2xl font-bold text-white mb-2">发布成功！</h3>
              <p className="text-gray-400 mb-6">您的内容已成功发布到{getCurrentTabInfo().title}</p>

              <div className="space-y-3 mb-6">
                <div className="bg-white/10 rounded-lg p-3">
                  <div className="text-sm text-gray-400">发布时间</div>
                  <div className="text-white font-medium">{new Date().toLocaleString()}</div>
                </div>
                <div className="bg-white/10 rounded-lg p-3">
                  <div className="text-sm text-gray-400">预计曝光</div>
                  <div className="text-green-400 font-medium">24小时内</div>
                </div>

                {/* Web3奖励展示 */}
                <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/20 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-sm text-gray-400">获得奖励</div>
                    <div className="flex items-center space-x-1">
                      <span className="text-green-400">🪙</span>
                      <span className="text-green-400 font-bold">{calculateRewards()} NGT</span>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500">
                    ≈ ${(calculateRewards() * 0.15).toFixed(2)} USD • 已发放到钱包
                  </div>
                </div>

                {/* NFT状态 */}
                {enableNFT && (
                  <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="text-sm text-gray-400">NFT铸造</div>
                      <div className="flex items-center space-x-1">
                        <span className="text-purple-400">🎨</span>
                        <span className="text-purple-400 font-medium">进行中</span>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">
                      预计5分钟完成 • 价格: {nftPrice} ETH
                    </div>
                  </div>
                )}

                {/* DID身份展示 */}
                {didVerified && (
                  <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-blue-400">🆔</span>
                        <span className="text-sm text-gray-400">DID身份认证</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <span className="text-green-400">✓</span>
                        <span className="text-blue-400 font-medium">等级 {creatorLevel}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* 发布统计 */}
              <div className="grid grid-cols-3 gap-4 mb-6">
                <div className="text-center">
                  <div className="text-xl font-bold text-white">12</div>
                  <div className="text-xs text-gray-400">今日发布</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold text-purple-400">156</div>
                  <div className="text-xs text-gray-400">总发布量</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold text-green-400">89%</div>
                  <div className="text-xs text-gray-400">成功率</div>
                </div>
              </div>

              <button
                onClick={() => setShowSuccessModal(false)}
                className="w-full py-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl text-white font-medium hover:from-purple-600 hover:to-blue-600 transition-colors"
              >
                继续发布
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
