{"version": 3, "file": "format.js", "sourceRoot": "", "sources": ["../../src.ts/providers/format.ts"], "names": [], "mappings": ";;;AAAA;;GAEG;AACH,kDAAmE;AACnE,iDAA8C;AAC9C,sDAAwD;AACxD,gDAG2B;AAS3B,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAIvB,SAAgB,SAAS,CAAC,MAAkB,EAAE,SAAe;IACzD,OAAO,CAAC,UAAS,KAAU;QACvB,IAAI,KAAK,IAAI,IAAI,EAAE;YAAE,OAAO,SAAS,CAAC;SAAE;QACxC,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;AACP,CAAC;AALD,8BAKC;AAED,SAAgB,OAAO,CAAC,MAAkB,EAAE,SAAmB;IAC3D,OAAO,CAAC,CAAC,KAAU,EAAE,EAAE;QACnB,IAAI,SAAS,IAAI,KAAK,IAAI,IAAI,EAAE;YAAE,OAAO,IAAI,CAAC;SAAE;QAChD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAAE,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;SAAE;QAC/D,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC;AACP,CAAC;AAND,0BAMC;AAED,+DAA+D;AAC/D,kEAAkE;AAClE,iDAAiD;AACjD,SAAgB,MAAM,CAAC,MAAkC,EAAE,QAAwC;IAC/F,OAAO,CAAC,CAAC,KAAU,EAAE,EAAE;QACnB,MAAM,MAAM,GAAQ,EAAG,CAAC;QACxB,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;YACtB,IAAI,MAAM,GAAG,GAAG,CAAC;YACjB,IAAI,QAAQ,IAAI,GAAG,IAAI,QAAQ,IAAI,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,EAAE;gBACnD,KAAK,MAAM,MAAM,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;oBAChC,IAAI,MAAM,IAAI,KAAK,EAAE;wBACjB,MAAM,GAAG,MAAM,CAAC;wBAChB,MAAM;qBACT;iBACJ;aACJ;YAED,IAAI;gBACA,MAAM,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;gBACtC,IAAI,EAAE,KAAK,SAAS,EAAE;oBAAE,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;iBAAE;aAC9C;YAAC,OAAO,KAAK,EAAE;gBACZ,MAAM,OAAO,GAAG,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAA,CAAC,CAAC,cAAc,CAAC;gBACzE,IAAA,iBAAM,EAAC,KAAK,EAAE,2BAA4B,GAAI,KAAM,OAAQ,GAAG,EAAE,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,CAAA;aAC1F;SACJ;QACD,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC;AAxBD,wBAwBC;AAED,SAAgB,aAAa,CAAC,KAAU;IACpC,QAAQ,KAAK,EAAE;QACX,KAAK,IAAI,CAAC;QAAC,KAAK,MAAM;YAClB,OAAO,IAAI,CAAC;QAChB,KAAK,KAAK,CAAC;QAAC,KAAK,OAAO;YACpB,OAAO,KAAK,CAAC;KACpB;IACD,IAAA,yBAAc,EAAC,KAAK,EAAE,oBAAqB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAE,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AACzF,CAAC;AARD,sCAQC;AAED,SAAgB,UAAU,CAAC,KAAa;IACpC,IAAA,yBAAc,EAAC,IAAA,sBAAW,EAAC,KAAK,EAAE,IAAI,CAAC,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACzE,OAAO,KAAK,CAAC;AACjB,CAAC;AAHD,gCAGC;AAED,SAAgB,UAAU,CAAC,KAAU;IACjC,IAAA,yBAAc,EAAC,IAAA,sBAAW,EAAC,KAAK,EAAE,EAAE,CAAC,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACvE,OAAO,KAAK,CAAC;AACjB,CAAC;AAHD,gCAGC;AAED,SAAgB,aAAa,CAAC,KAAU;IACpC,IAAI,CAAC,IAAA,sBAAW,EAAC,KAAK,CAAC,EAAE;QACrB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;KACtC;IACD,OAAO,IAAA,uBAAY,EAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACnC,CAAC;AALD,sCAKC;AAED,MAAM,UAAU,GAAG,MAAM,CAAC;IACtB,OAAO,EAAE,qBAAU;IACnB,SAAS,EAAE,UAAU;IACrB,WAAW,EAAE,oBAAS;IACtB,IAAI,EAAE,UAAU;IAChB,KAAK,EAAE,oBAAS;IAChB,OAAO,EAAE,SAAS,CAAC,aAAa,EAAE,KAAK,CAAC;IACxC,MAAM,EAAE,OAAO,CAAC,UAAU,CAAC;IAC3B,eAAe,EAAE,UAAU;IAC3B,gBAAgB,EAAE,oBAAS;CAC9B,EAAE;IACC,KAAK,EAAE,CAAE,UAAU,CAAE;CACxB,CAAC,CAAC;AAEH,SAAgB,SAAS,CAAC,KAAU;IAChC,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;AAC7B,CAAC;AAFD,8BAEC;AAED,MAAM,YAAY,GAAG,MAAM,CAAC;IACxB,IAAI,EAAE,SAAS,CAAC,UAAU,CAAC;IAC3B,UAAU,EAAE,UAAU;IACtB,qBAAqB,EAAE,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC;IAElD,MAAM,EAAE,oBAAS;IAEjB,SAAS,EAAE,oBAAS;IACpB,KAAK,EAAE,SAAS,CAAC,UAAU,CAAC;IAC5B,UAAU,EAAE,oBAAS;IAErB,QAAQ,EAAE,oBAAS;IACnB,OAAO,EAAE,oBAAS;IAElB,SAAS,EAAE,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC;IACtC,YAAY,EAAE,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC;IAEzC,WAAW,EAAE,SAAS,CAAC,oBAAS,EAAE,IAAI,CAAC;IACvC,aAAa,EAAE,SAAS,CAAC,oBAAS,EAAE,IAAI,CAAC;IAEzC,KAAK,EAAE,SAAS,CAAC,qBAAU,CAAC;IAC5B,UAAU,EAAE,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC;IACvC,SAAS,EAAE,UAAU;IAErB,aAAa,EAAE,SAAS,CAAC,oBAAS,CAAC;CACtC,EAAE;IACC,UAAU,EAAE,CAAE,SAAS,CAAE;CAC5B,CAAC,CAAC;AAEH,SAAgB,WAAW,CAAC,KAAU;IAClC,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;IACnC,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAsC,EAAE,EAAE;QACpF,IAAI,OAAM,CAAC,EAAE,CAAC,KAAK,QAAQ,EAAE;YAAE,OAAO,EAAE,CAAC;SAAE;QAC3C,OAAO,yBAAyB,CAAC,EAAE,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAClB,CAAC;AAPD,kCAOC;AAED,MAAM,iBAAiB,GAAG,MAAM,CAAC;IAC7B,gBAAgB,EAAE,oBAAS;IAC3B,WAAW,EAAE,oBAAS;IACtB,eAAe,EAAE,UAAU;IAC3B,OAAO,EAAE,qBAAU;IACnB,MAAM,EAAE,OAAO,CAAC,UAAU,CAAC;IAC3B,IAAI,EAAE,UAAU;IAChB,KAAK,EAAE,oBAAS;IAChB,SAAS,EAAE,UAAU;CACxB,EAAE;IACC,KAAK,EAAE,CAAE,UAAU,CAAE;CACxB,CAAC,CAAC;AAEH,SAAgB,gBAAgB,CAAC,KAAU;IACvC,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC;AACpC,CAAC;AAFD,4CAEC;AAED,MAAM,yBAAyB,GAAG,MAAM,CAAC;IACrC,EAAE,EAAE,SAAS,CAAC,qBAAU,EAAE,IAAI,CAAC;IAC/B,IAAI,EAAE,SAAS,CAAC,qBAAU,EAAE,IAAI,CAAC;IACjC,eAAe,EAAE,SAAS,CAAC,qBAAU,EAAE,IAAI,CAAC;IAC5C,8EAA8E;IAC9E,KAAK,EAAE,oBAAS;IAChB,IAAI,EAAE,SAAS,CAAC,kBAAO,CAAC;IACxB,OAAO,EAAE,oBAAS;IAClB,WAAW,EAAE,SAAS,CAAC,oBAAS,EAAE,IAAI,CAAC;IACvC,SAAS,EAAE,SAAS,CAAC,UAAU,CAAC;IAChC,SAAS,EAAE,UAAU;IACrB,IAAI,EAAE,UAAU;IAChB,IAAI,EAAE,OAAO,CAAC,gBAAgB,CAAC;IAC/B,WAAW,EAAE,oBAAS;IACtB,4CAA4C;IAC5C,iBAAiB,EAAE,oBAAS;IAC5B,iBAAiB,EAAE,SAAS,CAAC,oBAAS,CAAC;IACvC,YAAY,EAAE,SAAS,CAAC,oBAAS,EAAE,IAAI,CAAC;IACxC,MAAM,EAAE,SAAS,CAAC,oBAAS,CAAC;IAC5B,IAAI,EAAE,SAAS,CAAC,oBAAS,EAAE,CAAC,CAAC;CAChC,EAAE;IACC,iBAAiB,EAAE,CAAE,UAAU,CAAE;IACjC,IAAI,EAAE,CAAE,iBAAiB,CAAE;IAC3B,KAAK,EAAE,CAAE,kBAAkB,CAAE;CAChC,CAAC,CAAC;AAEH,SAAgB,wBAAwB,CAAC,KAAU;IAC/C,OAAO,yBAAyB,CAAC,KAAK,CAAC,CAAC;AAC5C,CAAC;AAFD,4DAEC;AAED,SAAgB,yBAAyB,CAAC,KAAU;IAEhD,mEAAmE;IACnE,+CAA+C;IAC/C,IAAI,KAAK,CAAC,EAAE,IAAI,IAAA,oBAAS,EAAC,KAAK,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE;QAC1C,KAAK,CAAC,EAAE,GAAG,4CAA4C,CAAC;KAC3D;IAED,MAAM,MAAM,GAAG,MAAM,CAAC;QAClB,IAAI,EAAE,UAAU;QAEhB,mEAAmE;QACnE,KAAK,EAAE,SAAS,CAAC,oBAAS,EAAE,SAAS,CAAC;QAEtC,IAAI,EAAE,CAAC,KAAU,EAAE,EAAE;YACjB,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBAAE,OAAO,CAAC,CAAC;aAAE;YAClD,OAAO,IAAA,oBAAS,EAAC,KAAK,CAAC,CAAC;QAC5B,CAAC;QACD,UAAU,EAAE,SAAS,CAAC,wBAAa,EAAE,IAAI,CAAC;QAC1C,mBAAmB,EAAE,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC;QAE/D,iBAAiB,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC,CAAM,EAAE,EAAE;YAC5C,IAAI,GAAkB,CAAC;YACvB,IAAI,CAAC,CAAC,SAAS,EAAE;gBACb,GAAG,GAAG,CAAC,CAAC,SAAS,CAAC;aAErB;iBAAM;gBACH,IAAI,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;gBACxB,IAAI,OAAO,KAAK,MAAM,EAAE;oBACpB,OAAO,GAAG,CAAC,CAAC;iBACf;qBAAM,IAAI,OAAO,KAAK,MAAM,EAAE;oBAC3B,OAAO,GAAG,CAAC,CAAC;iBACf;gBACD,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,EAAG,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;aAC5C;YAED,OAAO;gBACH,OAAO,EAAE,IAAA,qBAAU,EAAC,CAAC,CAAC,OAAO,CAAC;gBAC9B,OAAO,EAAE,IAAA,oBAAS,EAAC,CAAC,CAAC,OAAO,CAAC;gBAC7B,KAAK,EAAE,IAAA,oBAAS,EAAC,CAAC,CAAC,KAAK,CAAC;gBACzB,SAAS,EAAE,oBAAS,CAAC,IAAI,CAAC,GAAG,CAAC;aACjC,CAAC;QACN,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC;QAEhB,SAAS,EAAE,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC;QACtC,WAAW,EAAE,SAAS,CAAC,oBAAS,EAAE,IAAI,CAAC;QACvC,gBAAgB,EAAE,SAAS,CAAC,oBAAS,EAAE,IAAI,CAAC;QAE5C,IAAI,EAAE,qBAAU;QAEhB,yEAAyE;QACzE,QAAQ,EAAE,SAAS,CAAC,oBAAS,CAAC;QAC9B,oBAAoB,EAAE,SAAS,CAAC,oBAAS,CAAC;QAC1C,YAAY,EAAE,SAAS,CAAC,oBAAS,CAAC;QAClC,gBAAgB,EAAE,SAAS,CAAC,oBAAS,EAAE,IAAI,CAAC;QAE5C,QAAQ,EAAE,oBAAS;QACnB,EAAE,EAAE,SAAS,CAAC,qBAAU,EAAE,IAAI,CAAC;QAC/B,KAAK,EAAE,oBAAS;QAChB,KAAK,EAAE,oBAAS;QAChB,IAAI,EAAE,UAAU;QAEhB,OAAO,EAAE,SAAS,CAAC,qBAAU,EAAE,IAAI,CAAC;QAEpC,OAAO,EAAE,SAAS,CAAC,oBAAS,EAAE,IAAI,CAAC;KACtC,EAAE;QACC,IAAI,EAAE,CAAE,OAAO,CAAE;QACjB,QAAQ,EAAE,CAAE,KAAK,CAAE;QACnB,KAAK,EAAE,CAAE,kBAAkB,CAAE;KAChC,CAAC,CAAC,KAAK,CAAC,CAAC;IAEV,mEAAmE;IACnE,IAAI,MAAM,CAAC,EAAE,IAAI,IAAI,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE;QAC7C,MAAM,CAAC,OAAO,GAAG,IAAA,2BAAgB,EAAC,MAAM,CAAC,CAAC;KAC7C;IAED,wBAAwB;IAExB,oDAAoD;IACpD,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,UAAU,IAAI,IAAI,EAAE;QACpE,MAAM,CAAC,UAAU,GAAG,EAAG,CAAC;KAC3B;IAED,wBAAwB;IACxB,IAAI,KAAK,CAAC,SAAS,EAAE;QACjB,MAAM,CAAC,SAAS,GAAG,oBAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;KACtD;SAAM;QACH,MAAM,CAAC,SAAS,GAAG,oBAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5C;IAED,2EAA2E;IAC3E,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE;QACxB,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC;QAC/C,IAAI,OAAO,IAAI,IAAI,EAAE;YAAE,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;SAAE;KACrD;IAGD,uBAAuB;IACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAgCE;IAEF,oCAAoC;IACpC,IAAI,MAAM,CAAC,SAAS,IAAI,IAAA,oBAAS,EAAC,MAAM,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;QAC1D,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;KAC3B;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AA1ID,8DA0IC"}