{"version": 3, "file": "XLoader.js", "sources": ["../../src/loaders/XLoader.js"], "sourcesContent": ["import {\n  AnimationClip,\n  AnimationMixer,\n  Bone,\n  BufferGeometry,\n  FileLoader,\n  Float32BufferAttribute,\n  FrontSide,\n  Loader,\n  LoaderUtils,\n  Matrix4,\n  Mesh,\n  MeshPhongMaterial,\n  Quaternion,\n  Skeleton,\n  SkinnedMesh,\n  TextureLoader,\n  Uint16BufferAttribute,\n  Vector2,\n  Vector3,\n} from 'three'\nimport { decodeText } from '../_polyfill/LoaderUtils'\n\nvar XLoader = /* @__PURE__ */ (function () {\n  var classCallCheck = function (instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n      throw new TypeError('Cannot call a class as a function')\n    }\n  }\n\n  var createClass = (function () {\n    function defineProperties(target, props) {\n      for (let i = 0; i < props.length; i++) {\n        var descriptor = props[i]\n        descriptor.enumerable = descriptor.enumerable || false\n        descriptor.configurable = true\n        if ('value' in descriptor) descriptor.writable = true\n        Object.defineProperty(target, descriptor.key, descriptor)\n      }\n    }\n\n    return function (Constructor, protoProps, staticProps) {\n      if (protoProps) defineProperties(Constructor.prototype, protoProps)\n      if (staticProps) defineProperties(Constructor, staticProps)\n      return Constructor\n    }\n  })()\n\n  var XboneInf = function XboneInf() {\n    classCallCheck(this, XboneInf)\n\n    this.boneName = ''\n    this.BoneIndex = 0\n    this.Indeces = []\n    this.Weights = []\n    this.initMatrix = null\n    this.OffsetMatrix = null\n  }\n\n  var XAnimationInfo = function XAnimationInfo() {\n    classCallCheck(this, XAnimationInfo)\n\n    this.animeName = ''\n    this.boneName = ''\n    this.targetBone = null\n    this.keyType = 4\n    this.frameStartLv = 0\n    this.keyFrames = []\n    this.InverseMx = null\n  }\n\n  var XAnimationObj = (function () {\n    function XAnimationObj(_flags) {\n      classCallCheck(this, XAnimationObj)\n\n      this.fps = 30\n      this.name = 'xanimation'\n      this.length = 0\n      this.hierarchy = []\n      this.putFlags = _flags\n      if (this.putFlags.putPos === undefined) {\n        this.putFlags.putPos = true\n      }\n\n      if (this.putFlags.putRot === undefined) {\n        this.putFlags.putRot = true\n      }\n\n      if (this.putFlags.putScl === undefined) {\n        this.putFlags.putScl = true\n      }\n    }\n\n    createClass(XAnimationObj, [\n      {\n        key: 'make',\n        value: function make(XAnimationInfoArray) {\n          for (let i = 0; i < XAnimationInfoArray.length; i++) {\n            this.hierarchy.push(this.makeBonekeys(XAnimationInfoArray[i]))\n          }\n\n          this.length = this.hierarchy[0].keys[this.hierarchy[0].keys.length - 1].time\n        },\n      },\n      {\n        key: 'clone',\n        value: function clone() {\n          return Object.assign({}, this)\n        },\n      },\n      {\n        key: 'makeBonekeys',\n        value: function makeBonekeys(XAnimationInfo) {\n          var refObj = {}\n          refObj.name = XAnimationInfo.boneName\n          refObj.parent = ''\n          refObj.keys = this.keyFrameRefactor(XAnimationInfo)\n          refObj.copy = function () {\n            return Object.assign({}, this)\n          }\n\n          return refObj\n        },\n      },\n      {\n        key: 'keyFrameRefactor',\n        value: function keyFrameRefactor(XAnimationInfo) {\n          var keys = []\n          for (let i = 0; i < XAnimationInfo.keyFrames.length; i++) {\n            var keyframe = {}\n            keyframe.time = XAnimationInfo.keyFrames[i].time * this.fps\n            if (XAnimationInfo.keyFrames[i].pos && this.putFlags.putPos) {\n              keyframe.pos = XAnimationInfo.keyFrames[i].pos\n            }\n\n            if (XAnimationInfo.keyFrames[i].rot && this.putFlags.putRot) {\n              keyframe.rot = XAnimationInfo.keyFrames[i].rot\n            }\n\n            if (XAnimationInfo.keyFrames[i].scl && this.putFlags.putScl) {\n              keyframe.scl = XAnimationInfo.keyFrames[i].scl\n            }\n\n            if (XAnimationInfo.keyFrames[i].matrix) {\n              keyframe.matrix = XAnimationInfo.keyFrames[i].matrix\n              if (this.putFlags.putPos) {\n                keyframe.pos = new Vector3().setFromMatrixPosition(keyframe.matrix)\n              }\n\n              if (this.putFlags.putRot) {\n                keyframe.rot = new Quaternion().setFromRotationMatrix(keyframe.matrix)\n              }\n\n              if (this.putFlags.putScl) {\n                keyframe.scl = new Vector3().setFromMatrixScale(keyframe.matrix)\n              }\n            }\n\n            keys.push(keyframe)\n          }\n\n          return keys\n        },\n      },\n    ])\n    return XAnimationObj\n  })()\n\n  var XKeyFrameInfo = function XKeyFrameInfo() {\n    classCallCheck(this, XKeyFrameInfo)\n\n    this.index = 0\n    this.Frame = 0\n    this.time = 0.0\n    this.matrix = null\n  }\n\n  var XLoader = (function () {\n    function XLoader(manager) {\n      Loader.call(this, manager)\n\n      classCallCheck(this, XLoader)\n\n      this.debug = false\n      this.texloader = new TextureLoader(this.manager)\n      this.url = ''\n      this._putMatLength = 0\n      this._nowMat = null\n      this._nowFrameName = ''\n      this.frameHierarchie = []\n      this.Hierarchies = {}\n      this.HieStack = []\n      this._currentObject = {}\n      this._currentFrame = {}\n      this._data = null\n      this.onLoad = null\n      this.IsUvYReverse = true\n      this.Meshes = []\n      this.animations = []\n      this.animTicksPerSecond = 30\n      this._currentGeo = null\n      this._currentAnime = null\n      this._currentAnimeFrames = null\n    }\n\n    createClass(XLoader, [\n      {\n        key: '_setArgOption',\n        value: function _setArgOption(_arg) {\n          var _start = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0\n\n          if (!_arg) {\n            return\n          }\n\n          for (let i = _start; i < _arg.length; i++) {\n            switch (i) {\n              case 0:\n                this.url = _arg[i]\n                break\n              case 1:\n                this.options = _arg[i]\n                break\n            }\n          }\n\n          if (this.options === undefined) {\n            this.options = {}\n          }\n        },\n      },\n      {\n        key: 'load',\n        value: function load(_arg, onLoad, onProgress, onError) {\n          var _this = this\n\n          this._setArgOption(_arg)\n          var loader = new FileLoader(this.manager)\n          loader.setPath(this.path)\n          loader.setResponseType('arraybuffer')\n          loader.setRequestHeader(this.requestHeader)\n          loader.setWithCredentials(this.withCredentials)\n          loader.load(\n            this.url,\n            function (response) {\n              try {\n                _this.parse(response, onLoad)\n              } catch (e) {\n                if (onError) {\n                  onError(e)\n                } else {\n                  console.error(e)\n                }\n\n                _this.manager.itemError(_this.url)\n              }\n            },\n            onProgress,\n            onError,\n          )\n        },\n      },\n      {\n        key: '_readLine',\n        value: function _readLine(line) {\n          var readed = 0\n          while (true) {\n            var find = -1\n            find = line.indexOf('//', readed)\n            if (find === -1) {\n              find = line.indexOf('#', readed)\n            }\n\n            if (find > -1 && find < 2) {\n              var foundNewLine = -1\n              foundNewLine = line.indexOf('\\r\\n', readed)\n              if (foundNewLine > 0) {\n                readed = foundNewLine + 2\n              } else {\n                foundNewLine = line.indexOf('\\r', readed)\n                if (foundNewLine > 0) {\n                  readed = foundNewLine + 1\n                } else {\n                  readed = line.indexOf('\\n', readed) + 1\n                }\n              }\n            } else {\n              break\n            }\n          }\n\n          return line.substr(readed)\n        },\n      },\n      {\n        key: '_readLine',\n        value: function _readLine(line) {\n          var readed = 0\n          while (true) {\n            var find = -1\n            find = line.indexOf('//', readed)\n            if (find === -1) {\n              find = line.indexOf('#', readed)\n            }\n\n            if (find > -1 && find < 2) {\n              var foundNewLine = -1\n              foundNewLine = line.indexOf('\\r\\n', readed)\n              if (foundNewLine > 0) {\n                readed = foundNewLine + 2\n              } else {\n                foundNewLine = line.indexOf('\\r', readed)\n                if (foundNewLine > 0) {\n                  readed = foundNewLine + 1\n                } else {\n                  readed = line.indexOf('\\n', readed) + 1\n                }\n              }\n            } else {\n              break\n            }\n          }\n\n          return line.substr(readed)\n        },\n      },\n      {\n        key: '_isBinary',\n        value: function _isBinary(binData) {\n          var reader = new DataView(binData)\n          var face_size = (32 / 8) * 3 + (32 / 8) * 3 * 3 + 16 / 8\n          var n_faces = reader.getUint32(80, true)\n          var expect = 80 + 32 / 8 + n_faces * face_size\n          if (expect === reader.byteLength) {\n            return true\n          }\n\n          var fileLength = reader.byteLength\n          for (let index = 0; index < fileLength; index++) {\n            if (reader.getUint8(index, false) > 127) {\n              return true\n            }\n          }\n\n          return false\n        },\n      },\n      {\n        key: '_ensureBinary',\n        value: function _ensureBinary(buf) {\n          if (typeof buf === 'string') {\n            var array_buffer = new Uint8Array(buf.length)\n            for (let i = 0; i < buf.length; i++) {\n              array_buffer[i] = buf.charCodeAt(i) & 0xff\n            }\n\n            return array_buffer.buffer || array_buffer\n          } else {\n            return buf\n          }\n        },\n      },\n      {\n        key: '_ensureString',\n        value: function _ensureString(buf) {\n          if (typeof buf !== 'string') {\n            return decodeText(new Uint8Array(buf))\n          } else {\n            return buf\n          }\n        },\n      },\n      {\n        key: 'parse',\n        value: function _parse(data, onLoad) {\n          var binData = this._ensureBinary(data)\n          this._data = this._ensureString(data)\n          this.onLoad = onLoad\n          return this._isBinary(binData) ? this._parseBinary(binData) : this._parseASCII()\n        },\n      },\n      {\n        key: '_parseBinary',\n        value: function _parseBinary(data) {\n          return this._parseASCII(decodeText(new Uint8Array(data)))\n        },\n      },\n      {\n        key: '_parseASCII',\n        value: function _parseASCII() {\n          var path\n\n          if (this.resourcePath !== '') {\n            path = this.resourcePath\n          } else if (this.path !== '') {\n            path = this.path\n          } else {\n            path = LoaderUtils.extractUrlBase(this.url)\n          }\n\n          this.texloader.setPath(path).setCrossOrigin(this.crossOrigin)\n\n          var endRead = 16\n          this.Hierarchies.children = []\n          this._hierarchieParse(this.Hierarchies, endRead)\n          this._changeRoot()\n          this._currentObject = this.Hierarchies.children.shift()\n          this._mainloop()\n        },\n      },\n      {\n        key: '_hierarchieParse',\n        value: function _hierarchieParse(_parent, _end) {\n          var endRead = _end\n          while (true) {\n            var find1 = this._data.indexOf('{', endRead) + 1\n            var findEnd = this._data.indexOf('}', endRead)\n            var findNext = this._data.indexOf('{', find1) + 1\n            if (find1 > 0 && findEnd > find1) {\n              var _currentObject = {}\n              _currentObject.children = []\n              var nameData = this._readLine(this._data.substr(endRead, find1 - endRead - 1)).trim()\n              var word = nameData.split(/ /g)\n              if (word.length > 0) {\n                _currentObject.type = word[0]\n                if (word.length >= 2) {\n                  _currentObject.name = word[1]\n                } else {\n                  _currentObject.name = word[0] + this.Hierarchies.children.length\n                }\n              } else {\n                _currentObject.name = nameData\n                _currentObject.type = ''\n              }\n\n              if (_currentObject.type === 'Animation') {\n                _currentObject.data = this._data.substr(findNext, findEnd - findNext).trim()\n                var refs = this._hierarchieParse(_currentObject, findEnd + 1)\n                endRead = refs.end\n                _currentObject.children = refs.parent.children\n              } else {\n                var DataEnder = this._data.lastIndexOf(';', findNext > 0 ? Math.min(findNext, findEnd) : findEnd)\n                _currentObject.data = this._data.substr(find1, DataEnder - find1).trim()\n                if (findNext <= 0 || findEnd < findNext) {\n                  endRead = findEnd + 1\n                } else {\n                  var nextStart = Math.max(DataEnder + 1, find1)\n                  var _refs = this._hierarchieParse(_currentObject, nextStart)\n                  endRead = _refs.end\n                  _currentObject.children = _refs.parent.children\n                }\n              }\n\n              _currentObject.parent = _parent\n              if (_currentObject.type != 'template') {\n                _parent.children.push(_currentObject)\n              }\n            } else {\n              endRead = find1 === -1 ? this._data.length : findEnd + 1\n              break\n            }\n          }\n\n          return {\n            parent: _parent,\n            end: endRead,\n          }\n        },\n      },\n      {\n        key: '_mainloop',\n        value: function _mainloop() {\n          var _this2 = this\n\n          this._mainProc()\n          if (this._currentObject.parent || this._currentObject.children.length > 0 || !this._currentObject.worked) {\n            setTimeout(function () {\n              _this2._mainloop()\n            }, 1)\n          } else {\n            setTimeout(function () {\n              _this2.onLoad({\n                models: _this2.Meshes,\n                animations: _this2.animations,\n              })\n            }, 1)\n          }\n        },\n      },\n      {\n        key: '_mainProc',\n        value: function _mainProc() {\n          var breakFlag = false\n          while (true) {\n            if (!this._currentObject.worked) {\n              switch (this._currentObject.type) {\n                case 'template':\n                  break\n                case 'AnimTicksPerSecond':\n                  this.animTicksPerSecond = parseInt(this._currentObject.data)\n                  break\n                case 'Frame':\n                  this._setFrame()\n                  break\n                case 'FrameTransformMatrix':\n                  this._setFrameTransformMatrix()\n                  break\n                case 'Mesh':\n                  this._changeRoot()\n                  this._currentGeo = {}\n                  this._currentGeo.name = this._currentObject.name.trim()\n                  this._currentGeo.parentName = this._getParentName(this._currentObject).trim()\n                  this._currentGeo.VertexSetedBoneCount = []\n                  this._currentGeo.GeometryData = {\n                    vertices: [],\n                    normals: [],\n                    uvs: [],\n                    skinIndices: [],\n                    skinWeights: [],\n                    indices: [],\n                    materialIndices: [],\n                  }\n                  this._currentGeo.Materials = []\n                  this._currentGeo.normalVectors = []\n                  this._currentGeo.BoneInfs = []\n                  this._currentGeo.baseFrame = this._currentFrame\n                  this._makeBoneFrom_CurrentFrame()\n                  this._readVertexDatas()\n                  breakFlag = true\n                  break\n                case 'MeshNormals':\n                  this._readVertexDatas()\n                  break\n                case 'MeshTextureCoords':\n                  this._setMeshTextureCoords()\n                  break\n                case 'VertexDuplicationIndices':\n                  break\n                case 'MeshMaterialList':\n                  this._setMeshMaterialList()\n                  break\n                case 'Material':\n                  this._setMaterial()\n                  break\n                case 'SkinWeights':\n                  this._setSkinWeights()\n                  break\n                case 'AnimationSet':\n                  this._changeRoot()\n                  this._currentAnime = {}\n                  this._currentAnime.name = this._currentObject.name.trim()\n                  this._currentAnime.AnimeFrames = []\n                  break\n                case 'Animation':\n                  if (this._currentAnimeFrames) {\n                    this._currentAnime.AnimeFrames.push(this._currentAnimeFrames)\n                  }\n\n                  this._currentAnimeFrames = new XAnimationInfo()\n                  this._currentAnimeFrames.boneName = this._currentObject.data.trim()\n                  break\n                case 'AnimationKey':\n                  this._readAnimationKey()\n                  breakFlag = true\n                  break\n              }\n\n              this._currentObject.worked = true\n            }\n\n            if (this._currentObject.children.length > 0) {\n              this._currentObject = this._currentObject.children.shift()\n              if (this.debug) {\n                console.log('processing ' + this._currentObject.name)\n              }\n\n              if (breakFlag) break\n            } else {\n              if (this._currentObject.worked) {\n                if (this._currentObject.parent && !this._currentObject.parent.parent) {\n                  this._changeRoot()\n                }\n              }\n\n              if (this._currentObject.parent) {\n                this._currentObject = this._currentObject.parent\n              } else {\n                breakFlag = true\n              }\n\n              if (breakFlag) break\n            }\n          }\n\n          return\n        },\n      },\n      {\n        key: '_changeRoot',\n        value: function _changeRoot() {\n          if (this._currentGeo != null && this._currentGeo.name) {\n            this._makeOutputGeometry()\n          }\n\n          this._currentGeo = {}\n          if (this._currentAnime != null && this._currentAnime.name) {\n            if (this._currentAnimeFrames) {\n              this._currentAnime.AnimeFrames.push(this._currentAnimeFrames)\n              this._currentAnimeFrames = null\n            }\n\n            this._makeOutputAnimation()\n          }\n\n          this._currentAnime = {}\n        },\n      },\n      {\n        key: '_getParentName',\n        value: function _getParentName(_obj) {\n          if (_obj.parent) {\n            if (_obj.parent.name) {\n              return _obj.parent.name\n            } else {\n              return this._getParentName(_obj.parent)\n            }\n          } else {\n            return ''\n          }\n        },\n      },\n      {\n        key: '_setFrame',\n        value: function _setFrame() {\n          this._nowFrameName = this._currentObject.name.trim()\n          this._currentFrame = {}\n          this._currentFrame.name = this._nowFrameName\n          this._currentFrame.children = []\n          if (this._currentObject.parent && this._currentObject.parent.name) {\n            this._currentFrame.parentName = this._currentObject.parent.name\n          }\n\n          this.frameHierarchie.push(this._nowFrameName)\n          this.HieStack[this._nowFrameName] = this._currentFrame\n        },\n      },\n      {\n        key: '_setFrameTransformMatrix',\n        value: function _setFrameTransformMatrix() {\n          this._currentFrame.FrameTransformMatrix = new Matrix4()\n          var data = this._currentObject.data.split(',')\n          this._ParseMatrixData(this._currentFrame.FrameTransformMatrix, data)\n          this._makeBoneFrom_CurrentFrame()\n        },\n      },\n      {\n        key: '_makeBoneFrom_CurrentFrame',\n        value: function _makeBoneFrom_CurrentFrame() {\n          if (!this._currentFrame.FrameTransformMatrix) {\n            return\n          }\n\n          var b = new Bone()\n          b.name = this._currentFrame.name\n          b.applyMatrix4(this._currentFrame.FrameTransformMatrix)\n          b.matrixWorld = b.matrix\n          b.FrameTransformMatrix = this._currentFrame.FrameTransformMatrix\n          this._currentFrame.putBone = b\n          if (this._currentFrame.parentName) {\n            for (let frame in this.HieStack) {\n              if (this.HieStack[frame].name === this._currentFrame.parentName) {\n                this.HieStack[frame].putBone.add(this._currentFrame.putBone)\n              }\n            }\n          }\n        },\n      },\n      {\n        key: '_readVertexDatas',\n        value: function _readVertexDatas() {\n          var endRead = 0\n          var mode = 0\n          var mode_local = 0\n          var maxLength = 0\n          while (true) {\n            var changeMode = false\n            if (mode_local === 0) {\n              var refO = this._readInt1(endRead)\n              endRead = refO.endRead\n              mode_local = 1\n              maxLength = this._currentObject.data.indexOf(';;', endRead) + 1\n              if (maxLength <= 0) {\n                maxLength = this._currentObject.data.length\n              }\n            } else {\n              var find = 0\n              switch (mode) {\n                case 0:\n                  find = this._currentObject.data.indexOf(',', endRead) + 1\n                  break\n                case 1:\n                  find = this._currentObject.data.indexOf(';,', endRead) + 1\n                  break\n              }\n\n              if (find === 0 || find > maxLength) {\n                find = maxLength\n                mode_local = 0\n                changeMode = true\n              }\n\n              switch (this._currentObject.type) {\n                case 'Mesh':\n                  switch (mode) {\n                    case 0:\n                      this._readVertex1(this._currentObject.data.substr(endRead, find - endRead))\n                      break\n                    case 1:\n                      this._readFace1(this._currentObject.data.substr(endRead, find - endRead))\n                      break\n                  }\n\n                  break\n                case 'MeshNormals':\n                  switch (mode) {\n                    case 0:\n                      this._readNormalVector1(this._currentObject.data.substr(endRead, find - endRead))\n                      break\n                  }\n\n                  break\n              }\n\n              endRead = find + 1\n              if (changeMode) {\n                mode++\n              }\n            }\n\n            if (endRead >= this._currentObject.data.length) {\n              break\n            }\n          }\n        },\n      },\n      {\n        key: '_readInt1',\n        value: function _readInt1(start) {\n          var find = this._currentObject.data.indexOf(';', start)\n          return {\n            refI: parseInt(this._currentObject.data.substr(start, find - start)),\n            endRead: find + 1,\n          }\n        },\n      },\n      {\n        key: '_readVertex1',\n        value: function _readVertex1(line) {\n          var data = this._readLine(line.trim())\n            .substr(0, line.length - 2)\n            .split(';')\n          this._currentGeo.GeometryData.vertices.push(parseFloat(data[0]), parseFloat(data[1]), parseFloat(data[2]))\n          this._currentGeo.GeometryData.skinIndices.push(0, 0, 0, 0)\n          this._currentGeo.GeometryData.skinWeights.push(1, 0, 0, 0)\n          this._currentGeo.VertexSetedBoneCount.push(0)\n        },\n      },\n      {\n        key: '_readFace1',\n        value: function _readFace1(line) {\n          var data = this._readLine(line.trim())\n            .substr(2, line.length - 4)\n            .split(',')\n          this._currentGeo.GeometryData.indices.push(\n            parseInt(data[0], 10),\n            parseInt(data[1], 10),\n            parseInt(data[2], 10),\n          )\n        },\n      },\n      {\n        key: '_readNormalVector1',\n        value: function _readNormalVector1(line) {\n          var data = this._readLine(line.trim())\n            .substr(0, line.length - 2)\n            .split(';')\n          this._currentGeo.GeometryData.normals.push(parseFloat(data[0]), parseFloat(data[1]), parseFloat(data[2]))\n        },\n      },\n      {\n        key: '_buildGeometry',\n        value: function _buildGeometry() {\n          var bufferGeometry = new BufferGeometry()\n          var position = []\n          var normals = []\n          var uvs = []\n          var skinIndices = []\n          var skinWeights = []\n\n          //\n\n          var data = this._currentGeo.GeometryData\n\n          for (let i = 0, l = data.indices.length; i < l; i++) {\n            var stride2 = data.indices[i] * 2\n            var stride3 = data.indices[i] * 3\n            var stride4 = data.indices[i] * 4\n\n            position.push(data.vertices[stride3], data.vertices[stride3 + 1], data.vertices[stride3 + 2])\n            normals.push(data.normals[stride3], data.normals[stride3 + 1], data.normals[stride3 + 2])\n            skinIndices.push(\n              data.skinIndices[stride4],\n              data.skinIndices[stride4 + 1],\n              data.skinIndices[stride4 + 2],\n              data.skinIndices[stride4 + 3],\n            )\n            skinWeights.push(\n              data.skinWeights[stride4],\n              data.skinWeights[stride4 + 1],\n              data.skinWeights[stride4 + 2],\n              data.skinWeights[stride4 + 3],\n            )\n            uvs.push(data.uvs[stride2], data.uvs[stride2 + 1])\n          }\n\n          //\n\n          bufferGeometry.setAttribute('position', new Float32BufferAttribute(position, 3))\n          bufferGeometry.setAttribute('normal', new Float32BufferAttribute(normals, 3))\n          bufferGeometry.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n          bufferGeometry.setAttribute('skinIndex', new Uint16BufferAttribute(skinIndices, 4))\n          bufferGeometry.setAttribute('skinWeight', new Float32BufferAttribute(skinWeights, 4))\n\n          this._computeGroups(bufferGeometry, data.materialIndices)\n\n          return bufferGeometry\n        },\n      },\n      {\n        key: '_computeGroups',\n        value: function _computeGroups(bufferGeometry, materialIndices) {\n          var group\n          var groups = []\n          var materialIndex = undefined\n\n          for (let i = 0; i < materialIndices.length; i++) {\n            var currentMaterialIndex = materialIndices[i]\n\n            if (currentMaterialIndex !== materialIndex) {\n              materialIndex = currentMaterialIndex\n\n              if (group !== undefined) {\n                group.count = i * 3 - group.start\n                groups.push(group)\n              }\n\n              group = {\n                start: i * 3,\n                materialIndex: materialIndex,\n              }\n            }\n          }\n\n          if (group !== undefined) {\n            group.count = i * 3 - group.start\n            groups.push(group)\n          }\n\n          bufferGeometry.groups = groups\n        },\n      },\n      {\n        key: '_setMeshTextureCoords',\n        value: function _setMeshTextureCoords() {\n          var endRead = 0\n          var mode = 0\n          var mode_local = 0\n          while (true) {\n            switch (mode) {\n              case 0:\n                if (mode_local === 0) {\n                  var refO = this._readInt1(0)\n                  endRead = refO.endRead\n                  mode_local = 1\n                } else {\n                  var find = this._currentObject.data.indexOf(',', endRead) + 1\n                  if (find === 0) {\n                    find = this._currentObject.data.length\n                    mode = 2\n                    mode_local = 0\n                  }\n\n                  var line = this._currentObject.data.substr(endRead, find - endRead)\n                  var data = this._readLine(line.trim()).split(';')\n                  if (this.IsUvYReverse) {\n                    this._currentGeo.GeometryData.uvs.push(parseFloat(data[0]), 1 - parseFloat(data[1]))\n                  } else {\n                    this._currentGeo.GeometryData.uvs.push(parseFloat(data[0]), parseFloat(data[1]))\n                  }\n\n                  endRead = find + 1\n                }\n\n                break\n            }\n\n            if (endRead >= this._currentObject.data.length) {\n              break\n            }\n          }\n        },\n      },\n      {\n        key: '_setMeshMaterialList',\n        value: function _setMeshMaterialList() {\n          var endRead = 0\n          var mode = 0\n          var mode_local = 0\n          while (true) {\n            if (mode_local < 2) {\n              var refO = this._readInt1(endRead)\n              endRead = refO.endRead\n              mode_local++\n            } else {\n              var find = this._currentObject.data.indexOf(';', endRead)\n              if (find === -1) {\n                find = this._currentObject.data.length\n                mode = 3\n                mode_local = 0\n              }\n\n              var line = this._currentObject.data.substr(endRead, find - endRead)\n              var data = this._readLine(line.trim()).split(',')\n              for (let i = 0; i < data.length; i++) {\n                this._currentGeo.GeometryData.materialIndices[i] = parseInt(data[i])\n              }\n\n              endRead = this._currentObject.data.length\n            }\n\n            if (endRead >= this._currentObject.data.length || mode >= 3) {\n              break\n            }\n          }\n        },\n      },\n      {\n        key: '_setMaterial',\n        value: function _setMaterial() {\n          var _nowMat = new MeshPhongMaterial({\n            color: Math.random() * 0xffffff,\n          })\n          _nowMat.side = FrontSide\n          _nowMat.name = this._currentObject.name\n          var endRead = 0\n          var find = this._currentObject.data.indexOf(';;', endRead)\n          var line = this._currentObject.data.substr(endRead, find - endRead)\n          var data = this._readLine(line.trim()).split(';')\n          _nowMat.color.r = parseFloat(data[0])\n          _nowMat.color.g = parseFloat(data[1])\n          _nowMat.color.b = parseFloat(data[2])\n          endRead = find + 2\n          find = this._currentObject.data.indexOf(';', endRead)\n          line = this._currentObject.data.substr(endRead, find - endRead)\n          _nowMat.shininess = parseFloat(this._readLine(line))\n          endRead = find + 1\n          find = this._currentObject.data.indexOf(';;', endRead)\n          line = this._currentObject.data.substr(endRead, find - endRead)\n          var data2 = this._readLine(line.trim()).split(';')\n          _nowMat.specular.r = parseFloat(data2[0])\n          _nowMat.specular.g = parseFloat(data2[1])\n          _nowMat.specular.b = parseFloat(data2[2])\n          endRead = find + 2\n          find = this._currentObject.data.indexOf(';;', endRead)\n          if (find === -1) {\n            find = this._currentObject.data.length\n          }\n\n          line = this._currentObject.data.substr(endRead, find - endRead)\n          var data3 = this._readLine(line.trim()).split(';')\n          _nowMat.emissive.r = parseFloat(data3[0])\n          _nowMat.emissive.g = parseFloat(data3[1])\n          _nowMat.emissive.b = parseFloat(data3[2])\n          var localObject = null\n          while (true) {\n            if (this._currentObject.children.length > 0) {\n              localObject = this._currentObject.children.shift()\n              if (this.debug) {\n                console.log('processing ' + localObject.name)\n              }\n\n              var fileName = localObject.data.substr(1, localObject.data.length - 2)\n              switch (localObject.type) {\n                case 'TextureFilename':\n                  _nowMat.map = this.texloader.load(fileName)\n                  break\n                case 'BumpMapFilename':\n                  _nowMat.bumpMap = this.texloader.load(fileName)\n                  _nowMat.bumpScale = 0.05\n                  break\n                case 'NormalMapFilename':\n                  _nowMat.normalMap = this.texloader.load(fileName)\n                  _nowMat.normalScale = new Vector2(2, 2)\n                  break\n                case 'EmissiveMapFilename':\n                  _nowMat.emissiveMap = this.texloader.load(fileName)\n                  break\n                case 'LightMapFilename':\n                  _nowMat.lightMap = this.texloader.load(fileName)\n                  break\n              }\n            } else {\n              break\n            }\n          }\n\n          this._currentGeo.Materials.push(_nowMat)\n        },\n      },\n      {\n        key: '_setSkinWeights',\n        value: function _setSkinWeights() {\n          var boneInf = new XboneInf()\n          var endRead = 0\n          var find = this._currentObject.data.indexOf(';', endRead)\n          var line = this._currentObject.data.substr(endRead, find - endRead)\n          endRead = find + 1\n          boneInf.boneName = line.substr(1, line.length - 2)\n          boneInf.BoneIndex = this._currentGeo.BoneInfs.length\n          find = this._currentObject.data.indexOf(';', endRead)\n          endRead = find + 1\n          find = this._currentObject.data.indexOf(';', endRead)\n          line = this._currentObject.data.substr(endRead, find - endRead)\n          var data = this._readLine(line.trim()).split(',')\n          for (let i = 0; i < data.length; i++) {\n            boneInf.Indeces.push(parseInt(data[i]))\n          }\n\n          endRead = find + 1\n          find = this._currentObject.data.indexOf(';', endRead)\n          line = this._currentObject.data.substr(endRead, find - endRead)\n          var data2 = this._readLine(line.trim()).split(',')\n          for (let _i = 0; _i < data2.length; _i++) {\n            boneInf.Weights.push(parseFloat(data2[_i]))\n          }\n\n          endRead = find + 1\n          find = this._currentObject.data.indexOf(';', endRead)\n          if (find <= 0) {\n            find = this._currentObject.data.length\n          }\n\n          line = this._currentObject.data.substr(endRead, find - endRead)\n          var data3 = this._readLine(line.trim()).split(',')\n          boneInf.OffsetMatrix = new Matrix4()\n          this._ParseMatrixData(boneInf.OffsetMatrix, data3)\n          this._currentGeo.BoneInfs.push(boneInf)\n        },\n      },\n      {\n        key: '_makePutBoneList',\n        value: function _makePutBoneList(_RootName, _bones) {\n          var putting = false\n          for (let frame in this.HieStack) {\n            if (this.HieStack[frame].name === _RootName || putting) {\n              putting = true\n              var b = new Bone()\n              b.name = this.HieStack[frame].name\n              b.applyMatrix4(this.HieStack[frame].FrameTransformMatrix)\n              b.matrixWorld = b.matrix\n              b.FrameTransformMatrix = this.HieStack[frame].FrameTransformMatrix\n              b.pos = new Vector3().setFromMatrixPosition(b.FrameTransformMatrix).toArray()\n              b.rotq = new Quaternion().setFromRotationMatrix(b.FrameTransformMatrix).toArray()\n              b.scl = new Vector3().setFromMatrixScale(b.FrameTransformMatrix).toArray()\n              if (this.HieStack[frame].parentName && this.HieStack[frame].parentName.length > 0) {\n                for (let i = 0; i < _bones.length; i++) {\n                  if (this.HieStack[frame].parentName === _bones[i].name) {\n                    _bones[i].add(b)\n                    b.parent = i\n                    break\n                  }\n                }\n              }\n\n              _bones.push(b)\n            }\n          }\n        },\n      },\n      {\n        key: '_makeOutputGeometry',\n        value: function _makeOutputGeometry() {\n          var mesh = null\n          if (this._currentGeo.BoneInfs.length > 0) {\n            var putBones = []\n            this._makePutBoneList(this._currentGeo.baseFrame.parentName, putBones)\n            for (let bi = 0; bi < this._currentGeo.BoneInfs.length; bi++) {\n              var boneIndex = 0\n              for (let bb = 0; bb < putBones.length; bb++) {\n                if (putBones[bb].name === this._currentGeo.BoneInfs[bi].boneName) {\n                  boneIndex = bb\n                  putBones[bb].OffsetMatrix = new Matrix4()\n                  putBones[bb].OffsetMatrix.copy(this._currentGeo.BoneInfs[bi].OffsetMatrix)\n                  break\n                }\n              }\n\n              for (let vi = 0; vi < this._currentGeo.BoneInfs[bi].Indeces.length; vi++) {\n                var nowVertexID = this._currentGeo.BoneInfs[bi].Indeces[vi]\n                var nowVal = this._currentGeo.BoneInfs[bi].Weights[vi]\n\n                var stride = nowVertexID * 4\n\n                switch (this._currentGeo.VertexSetedBoneCount[nowVertexID]) {\n                  case 0:\n                    this._currentGeo.GeometryData.skinIndices[stride] = boneIndex\n                    this._currentGeo.GeometryData.skinWeights[stride] = nowVal\n                    break\n                  case 1:\n                    this._currentGeo.GeometryData.skinIndices[stride + 1] = boneIndex\n                    this._currentGeo.GeometryData.skinWeights[stride + 1] = nowVal\n                    break\n                  case 2:\n                    this._currentGeo.GeometryData.skinIndices[stride + 2] = boneIndex\n                    this._currentGeo.GeometryData.skinWeights[stride + 2] = nowVal\n                    break\n                  case 3:\n                    this._currentGeo.GeometryData.skinIndices[stride + 3] = boneIndex\n                    this._currentGeo.GeometryData.skinWeights[stride + 3] = nowVal\n                    break\n                }\n\n                this._currentGeo.VertexSetedBoneCount[nowVertexID]++\n                if (this._currentGeo.VertexSetedBoneCount[nowVertexID] > 4) {\n                  console.log('warn! over 4 bone weight! :' + nowVertexID)\n                }\n              }\n            }\n\n            for (let sk = 0; sk < this._currentGeo.Materials.length; sk++) {\n              this._currentGeo.Materials[sk].skinning = true\n            }\n\n            var offsetList = []\n            for (let _bi = 0; _bi < putBones.length; _bi++) {\n              if (putBones[_bi].OffsetMatrix) {\n                offsetList.push(putBones[_bi].OffsetMatrix)\n              } else {\n                offsetList.push(new Matrix4())\n              }\n            }\n\n            var bufferGeometry = this._buildGeometry()\n            mesh = new SkinnedMesh(\n              bufferGeometry,\n              this._currentGeo.Materials.length === 1 ? this._currentGeo.Materials[0] : this._currentGeo.Materials,\n            )\n\n            this._initSkeleton(mesh, putBones, offsetList)\n          } else {\n            var _bufferGeometry = this._buildGeometry()\n            mesh = new Mesh(\n              _bufferGeometry,\n              this._currentGeo.Materials.length === 1 ? this._currentGeo.Materials[0] : this._currentGeo.Materials,\n            )\n          }\n\n          mesh.name = this._currentGeo.name\n          var worldBaseMx = new Matrix4()\n          var currentMxFrame = this._currentGeo.baseFrame.putBone\n          if (currentMxFrame && currentMxFrame.parent) {\n            while (true) {\n              currentMxFrame = currentMxFrame.parent\n              if (currentMxFrame) {\n                worldBaseMx.multiply(currentMxFrame.FrameTransformMatrix)\n              } else {\n                break\n              }\n            }\n\n            mesh.applyMatrix4(worldBaseMx)\n          }\n\n          this.Meshes.push(mesh)\n        },\n      },\n      {\n        key: '_initSkeleton',\n        value: function _initSkeleton(mesh, boneList, boneInverses) {\n          var bones = [],\n            bone,\n            gbone\n          var i, il\n\n          for (i = 0, il = boneList.length; i < il; i++) {\n            gbone = boneList[i]\n\n            bone = new Bone()\n            bones.push(bone)\n\n            bone.name = gbone.name\n            bone.position.fromArray(gbone.pos)\n            bone.quaternion.fromArray(gbone.rotq)\n            if (gbone.scl !== undefined) bone.scale.fromArray(gbone.scl)\n          }\n\n          for (i = 0, il = boneList.length; i < il; i++) {\n            gbone = boneList[i]\n\n            if (gbone.parent !== -1 && gbone.parent !== null && bones[gbone.parent] !== undefined) {\n              bones[gbone.parent].add(bones[i])\n            } else {\n              mesh.add(bones[i])\n            }\n          }\n\n          mesh.updateMatrixWorld(true)\n\n          var skeleton = new Skeleton(bones, boneInverses)\n          mesh.bind(skeleton, mesh.matrixWorld)\n        },\n      },\n      {\n        key: '_readAnimationKey',\n        value: function _readAnimationKey() {\n          var endRead = 0\n          var find = this._currentObject.data.indexOf(';', endRead)\n          var line = this._currentObject.data.substr(endRead, find - endRead)\n          endRead = find + 1\n          var nowKeyType = parseInt(this._readLine(line))\n          find = this._currentObject.data.indexOf(';', endRead)\n          endRead = find + 1\n          line = this._currentObject.data.substr(endRead)\n          var data = this._readLine(line.trim()).split(';;,')\n          for (let i = 0; i < data.length; i++) {\n            var data2 = data[i].split(';')\n            var keyInfo = new XKeyFrameInfo()\n            keyInfo.type = nowKeyType\n            keyInfo.Frame = parseInt(data2[0])\n            keyInfo.index = this._currentAnimeFrames.keyFrames.length\n            keyInfo.time = keyInfo.Frame\n            if (nowKeyType != 4) {\n              var frameFound = false\n              for (let mm = 0; mm < this._currentAnimeFrames.keyFrames.length; mm++) {\n                if (this._currentAnimeFrames.keyFrames[mm].Frame === keyInfo.Frame) {\n                  keyInfo = this._currentAnimeFrames.keyFrames[mm]\n                  frameFound = true\n                  break\n                }\n              }\n\n              var frameValue = data2[2].split(',')\n              switch (nowKeyType) {\n                case 0:\n                  keyInfo.rot = new Quaternion(\n                    parseFloat(frameValue[1]),\n                    parseFloat(frameValue[2]),\n                    parseFloat(frameValue[3]),\n                    parseFloat(frameValue[0]) * -1,\n                  )\n                  break\n                case 1:\n                  keyInfo.scl = new Vector3(\n                    parseFloat(frameValue[0]),\n                    parseFloat(frameValue[1]),\n                    parseFloat(frameValue[2]),\n                  )\n                  break\n                case 2:\n                  keyInfo.pos = new Vector3(\n                    parseFloat(frameValue[0]),\n                    parseFloat(frameValue[1]),\n                    parseFloat(frameValue[2]),\n                  )\n                  break\n              }\n\n              if (!frameFound) {\n                this._currentAnimeFrames.keyFrames.push(keyInfo)\n              }\n            } else {\n              keyInfo.matrix = new Matrix4()\n              this._ParseMatrixData(keyInfo.matrix, data2[2].split(','))\n              this._currentAnimeFrames.keyFrames.push(keyInfo)\n            }\n          }\n        },\n      },\n      {\n        key: '_makeOutputAnimation',\n        value: function _makeOutputAnimation() {\n          var animationObj = new XAnimationObj(this.options)\n          animationObj.fps = this.animTicksPerSecond\n          animationObj.name = this._currentAnime.name\n          animationObj.make(this._currentAnime.AnimeFrames)\n          this.animations.push(animationObj)\n        },\n      },\n      {\n        key: 'assignAnimation',\n        value: function assignAnimation(_model, _animation) {\n          var model = _model\n          var animation = _animation\n          if (!model) {\n            model = this.Meshes[0]\n          }\n\n          if (!animation) {\n            animation = this.animations[0]\n          }\n\n          if (!model || !animation) {\n            return null\n          }\n\n          var put = {}\n          put.fps = animation.fps\n          put.name = animation.name\n          put.length = animation.length\n          put.hierarchy = []\n          for (let b = 0; b < model.skeleton.bones.length; b++) {\n            var findAnimation = false\n            for (let i = 0; i < animation.hierarchy.length; i++) {\n              if (model.skeleton.bones[b].name === animation.hierarchy[i].name) {\n                findAnimation = true\n                var c_key = animation.hierarchy[i].copy()\n                c_key.parent = -1\n                if (model.skeleton.bones[b].parent && model.skeleton.bones[b].parent.type === 'Bone') {\n                  for (let bb = 0; bb < put.hierarchy.length; bb++) {\n                    if (put.hierarchy[bb].name === model.skeleton.bones[b].parent.name) {\n                      c_key.parent = bb\n                      c_key.parentName = model.skeleton.bones[b].parent.name\n                    }\n                  }\n                }\n\n                put.hierarchy.push(c_key)\n                break\n              }\n            }\n\n            if (!findAnimation) {\n              var _c_key = animation.hierarchy[0].copy()\n              _c_key.name = model.skeleton.bones[b].name\n              _c_key.parent = -1\n              for (let k = 0; k < _c_key.keys.length; k++) {\n                if (_c_key.keys[k].pos) {\n                  _c_key.keys[k].pos.set(0, 0, 0)\n                }\n\n                if (_c_key.keys[k].scl) {\n                  _c_key.keys[k].scl.set(1, 1, 1)\n                }\n\n                if (_c_key.keys[k].rot) {\n                  _c_key.keys[k].rot.set(0, 0, 0, 1)\n                }\n              }\n\n              put.hierarchy.push(_c_key)\n            }\n          }\n\n          if (!model.geometry.animations) {\n            model.geometry.animations = []\n          }\n\n          model.geometry.animations.push(AnimationClip.parseAnimation(put, model.skeleton.bones))\n          if (!model.animationMixer) {\n            model.animationMixer = new AnimationMixer(model)\n          }\n\n          return put\n        },\n      },\n      {\n        key: '_ParseMatrixData',\n        value: function _ParseMatrixData(targetMatrix, data) {\n          targetMatrix.set(\n            parseFloat(data[0]),\n            parseFloat(data[4]),\n            parseFloat(data[8]),\n            parseFloat(data[12]),\n            parseFloat(data[1]),\n            parseFloat(data[5]),\n            parseFloat(data[9]),\n            parseFloat(data[13]),\n            parseFloat(data[2]),\n            parseFloat(data[6]),\n            parseFloat(data[10]),\n            parseFloat(data[14]),\n            parseFloat(data[3]),\n            parseFloat(data[7]),\n            parseFloat(data[11]),\n            parseFloat(data[15]),\n          )\n        },\n      },\n    ])\n    return XLoader\n  })()\n\n  return XLoader\n})()\n\nexport { XLoader }\n"], "names": ["i", "XboneInf", "XAnimationInfo", "XAnimationObj", "XKeyFrameInfo", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;AAuBG,IAAC,UAA2B,2BAAY;AACzC,MAAI,iBAAiB,SAAU,UAAU,aAAa;AACpD,QAAI,EAAE,oBAAoB,cAAc;AACtC,YAAM,IAAI,UAAU,mCAAmC;AAAA,IACxD;AAAA,EACF;AAED,MAAI,cAAe,WAAY;AAC7B,aAAS,iBAAiB,QAAQ,OAAO;AACvC,eAASA,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACrC,YAAI,aAAa,MAAMA,EAAC;AACxB,mBAAW,aAAa,WAAW,cAAc;AACjD,mBAAW,eAAe;AAC1B,YAAI,WAAW;AAAY,qBAAW,WAAW;AACjD,eAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,MACzD;AAAA,IACF;AAED,WAAO,SAAU,aAAa,YAAY,aAAa;AACrD,UAAI;AAAY,yBAAiB,YAAY,WAAW,UAAU;AAClE,UAAI;AAAa,yBAAiB,aAAa,WAAW;AAC1D,aAAO;AAAA,IACR;AAAA,EACL,EAAM;AAEJ,MAAI,WAAW,SAASC,YAAW;AACjC,mBAAe,MAAMA,SAAQ;AAE7B,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,UAAU,CAAE;AACjB,SAAK,UAAU,CAAE;AACjB,SAAK,aAAa;AAClB,SAAK,eAAe;AAAA,EACrB;AAED,MAAI,iBAAiB,SAASC,kBAAiB;AAC7C,mBAAe,MAAMA,eAAc;AAEnC,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,YAAY,CAAE;AACnB,SAAK,YAAY;AAAA,EAClB;AAED,MAAI,gBAAiB,WAAY;AAC/B,aAASC,eAAc,QAAQ;AAC7B,qBAAe,MAAMA,cAAa;AAElC,WAAK,MAAM;AACX,WAAK,OAAO;AACZ,WAAK,SAAS;AACd,WAAK,YAAY,CAAE;AACnB,WAAK,WAAW;AAChB,UAAI,KAAK,SAAS,WAAW,QAAW;AACtC,aAAK,SAAS,SAAS;AAAA,MACxB;AAED,UAAI,KAAK,SAAS,WAAW,QAAW;AACtC,aAAK,SAAS,SAAS;AAAA,MACxB;AAED,UAAI,KAAK,SAAS,WAAW,QAAW;AACtC,aAAK,SAAS,SAAS;AAAA,MACxB;AAAA,IACF;AAED,gBAAYA,gBAAe;AAAA,MACzB;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,KAAK,qBAAqB;AACxC,mBAASH,KAAI,GAAGA,KAAI,oBAAoB,QAAQA,MAAK;AACnD,iBAAK,UAAU,KAAK,KAAK,aAAa,oBAAoBA,EAAC,CAAC,CAAC;AAAA,UAC9D;AAED,eAAK,SAAS,KAAK,UAAU,CAAC,EAAE,KAAK,KAAK,UAAU,CAAC,EAAE,KAAK,SAAS,CAAC,EAAE;AAAA,QACzE;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACtB,iBAAO,OAAO,OAAO,CAAE,GAAE,IAAI;AAAA,QAC9B;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,aAAaE,iBAAgB;AAC3C,cAAI,SAAS,CAAE;AACf,iBAAO,OAAOA,gBAAe;AAC7B,iBAAO,SAAS;AAChB,iBAAO,OAAO,KAAK,iBAAiBA,eAAc;AAClD,iBAAO,OAAO,WAAY;AACxB,mBAAO,OAAO,OAAO,CAAE,GAAE,IAAI;AAAA,UAC9B;AAED,iBAAO;AAAA,QACR;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,iBAAiBA,iBAAgB;AAC/C,cAAI,OAAO,CAAE;AACb,mBAASF,KAAI,GAAGA,KAAIE,gBAAe,UAAU,QAAQF,MAAK;AACxD,gBAAI,WAAW,CAAE;AACjB,qBAAS,OAAOE,gBAAe,UAAUF,EAAC,EAAE,OAAO,KAAK;AACxD,gBAAIE,gBAAe,UAAUF,EAAC,EAAE,OAAO,KAAK,SAAS,QAAQ;AAC3D,uBAAS,MAAME,gBAAe,UAAUF,EAAC,EAAE;AAAA,YAC5C;AAED,gBAAIE,gBAAe,UAAUF,EAAC,EAAE,OAAO,KAAK,SAAS,QAAQ;AAC3D,uBAAS,MAAME,gBAAe,UAAUF,EAAC,EAAE;AAAA,YAC5C;AAED,gBAAIE,gBAAe,UAAUF,EAAC,EAAE,OAAO,KAAK,SAAS,QAAQ;AAC3D,uBAAS,MAAME,gBAAe,UAAUF,EAAC,EAAE;AAAA,YAC5C;AAED,gBAAIE,gBAAe,UAAUF,EAAC,EAAE,QAAQ;AACtC,uBAAS,SAASE,gBAAe,UAAUF,EAAC,EAAE;AAC9C,kBAAI,KAAK,SAAS,QAAQ;AACxB,yBAAS,MAAM,IAAI,QAAS,EAAC,sBAAsB,SAAS,MAAM;AAAA,cACnE;AAED,kBAAI,KAAK,SAAS,QAAQ;AACxB,yBAAS,MAAM,IAAI,WAAY,EAAC,sBAAsB,SAAS,MAAM;AAAA,cACtE;AAED,kBAAI,KAAK,SAAS,QAAQ;AACxB,yBAAS,MAAM,IAAI,QAAS,EAAC,mBAAmB,SAAS,MAAM;AAAA,cAChE;AAAA,YACF;AAED,iBAAK,KAAK,QAAQ;AAAA,UACnB;AAED,iBAAO;AAAA,QACR;AAAA,MACF;AAAA,IACP,CAAK;AACD,WAAOG;AAAA,EACX,EAAM;AAEJ,MAAI,gBAAgB,SAASC,iBAAgB;AAC3C,mBAAe,MAAMA,cAAa;AAElC,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,SAAS;AAAA,EACf;AAED,MAAIC,WAAW,WAAY;AACzB,aAASA,SAAQ,SAAS;AACxB,aAAO,KAAK,MAAM,OAAO;AAEzB,qBAAe,MAAMA,QAAO;AAE5B,WAAK,QAAQ;AACb,WAAK,YAAY,IAAI,cAAc,KAAK,OAAO;AAC/C,WAAK,MAAM;AACX,WAAK,gBAAgB;AACrB,WAAK,UAAU;AACf,WAAK,gBAAgB;AACrB,WAAK,kBAAkB,CAAE;AACzB,WAAK,cAAc,CAAE;AACrB,WAAK,WAAW,CAAE;AAClB,WAAK,iBAAiB,CAAE;AACxB,WAAK,gBAAgB,CAAE;AACvB,WAAK,QAAQ;AACb,WAAK,SAAS;AACd,WAAK,eAAe;AACpB,WAAK,SAAS,CAAE;AAChB,WAAK,aAAa,CAAE;AACpB,WAAK,qBAAqB;AAC1B,WAAK,cAAc;AACnB,WAAK,gBAAgB;AACrB,WAAK,sBAAsB;AAAA,IAC5B;AAED,gBAAYA,UAAS;AAAA,MACnB;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,cAAc,MAAM;AAClC,cAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAEjF,cAAI,CAAC,MAAM;AACT;AAAA,UACD;AAED,mBAASL,KAAI,QAAQA,KAAI,KAAK,QAAQA,MAAK;AACzC,oBAAQA,IAAC;AAAA,cACP,KAAK;AACH,qBAAK,MAAM,KAAKA,EAAC;AACjB;AAAA,cACF,KAAK;AACH,qBAAK,UAAU,KAAKA,EAAC;AACrB;AAAA,YACH;AAAA,UACF;AAED,cAAI,KAAK,YAAY,QAAW;AAC9B,iBAAK,UAAU,CAAE;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,KAAK,MAAM,QAAQ,YAAY,SAAS;AACtD,cAAI,QAAQ;AAEZ,eAAK,cAAc,IAAI;AACvB,cAAI,SAAS,IAAI,WAAW,KAAK,OAAO;AACxC,iBAAO,QAAQ,KAAK,IAAI;AACxB,iBAAO,gBAAgB,aAAa;AACpC,iBAAO,iBAAiB,KAAK,aAAa;AAC1C,iBAAO,mBAAmB,KAAK,eAAe;AAC9C,iBAAO;AAAA,YACL,KAAK;AAAA,YACL,SAAU,UAAU;AAClB,kBAAI;AACF,sBAAM,MAAM,UAAU,MAAM;AAAA,cAC7B,SAAQ,GAAP;AACA,oBAAI,SAAS;AACX,0BAAQ,CAAC;AAAA,gBAC3B,OAAuB;AACL,0BAAQ,MAAM,CAAC;AAAA,gBAChB;AAED,sBAAM,QAAQ,UAAU,MAAM,GAAG;AAAA,cAClC;AAAA,YACF;AAAA,YACD;AAAA,YACA;AAAA,UACD;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,UAAU,MAAM;AAC9B,cAAI,SAAS;AACb,iBAAO,MAAM;AACX,gBAAI,OAAO;AACX,mBAAO,KAAK,QAAQ,MAAM,MAAM;AAChC,gBAAI,SAAS,IAAI;AACf,qBAAO,KAAK,QAAQ,KAAK,MAAM;AAAA,YAChC;AAED,gBAAI,OAAO,MAAM,OAAO,GAAG;AACzB,kBAAI,eAAe;AACnB,6BAAe,KAAK,QAAQ,QAAQ,MAAM;AAC1C,kBAAI,eAAe,GAAG;AACpB,yBAAS,eAAe;AAAA,cACxC,OAAqB;AACL,+BAAe,KAAK,QAAQ,MAAM,MAAM;AACxC,oBAAI,eAAe,GAAG;AACpB,2BAAS,eAAe;AAAA,gBAC1C,OAAuB;AACL,2BAAS,KAAK,QAAQ,MAAM,MAAM,IAAI;AAAA,gBACvC;AAAA,cACF;AAAA,YACf,OAAmB;AACL;AAAA,YACD;AAAA,UACF;AAED,iBAAO,KAAK,OAAO,MAAM;AAAA,QAC1B;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,UAAU,MAAM;AAC9B,cAAI,SAAS;AACb,iBAAO,MAAM;AACX,gBAAI,OAAO;AACX,mBAAO,KAAK,QAAQ,MAAM,MAAM;AAChC,gBAAI,SAAS,IAAI;AACf,qBAAO,KAAK,QAAQ,KAAK,MAAM;AAAA,YAChC;AAED,gBAAI,OAAO,MAAM,OAAO,GAAG;AACzB,kBAAI,eAAe;AACnB,6BAAe,KAAK,QAAQ,QAAQ,MAAM;AAC1C,kBAAI,eAAe,GAAG;AACpB,yBAAS,eAAe;AAAA,cACxC,OAAqB;AACL,+BAAe,KAAK,QAAQ,MAAM,MAAM;AACxC,oBAAI,eAAe,GAAG;AACpB,2BAAS,eAAe;AAAA,gBAC1C,OAAuB;AACL,2BAAS,KAAK,QAAQ,MAAM,MAAM,IAAI;AAAA,gBACvC;AAAA,cACF;AAAA,YACf,OAAmB;AACL;AAAA,YACD;AAAA,UACF;AAED,iBAAO,KAAK,OAAO,MAAM;AAAA,QAC1B;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,UAAU,SAAS;AACjC,cAAI,SAAS,IAAI,SAAS,OAAO;AACjC,cAAI,YAAa,KAAK,IAAK,IAAK,KAAK,IAAK,IAAI,IAAI,KAAK;AACvD,cAAI,UAAU,OAAO,UAAU,IAAI,IAAI;AACvC,cAAI,SAAS,KAAK,KAAK,IAAI,UAAU;AACrC,cAAI,WAAW,OAAO,YAAY;AAChC,mBAAO;AAAA,UACR;AAED,cAAI,aAAa,OAAO;AACxB,mBAAS,QAAQ,GAAG,QAAQ,YAAY,SAAS;AAC/C,gBAAI,OAAO,SAAS,OAAO,KAAK,IAAI,KAAK;AACvC,qBAAO;AAAA,YACR;AAAA,UACF;AAED,iBAAO;AAAA,QACR;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,cAAc,KAAK;AACjC,cAAI,OAAO,QAAQ,UAAU;AAC3B,gBAAI,eAAe,IAAI,WAAW,IAAI,MAAM;AAC5C,qBAASA,KAAI,GAAGA,KAAI,IAAI,QAAQA,MAAK;AACnC,2BAAaA,EAAC,IAAI,IAAI,WAAWA,EAAC,IAAI;AAAA,YACvC;AAED,mBAAO,aAAa,UAAU;AAAA,UAC1C,OAAiB;AACL,mBAAO;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,cAAc,KAAK;AACjC,cAAI,OAAO,QAAQ,UAAU;AAC3B,mBAAO,WAAW,IAAI,WAAW,GAAG,CAAC;AAAA,UACjD,OAAiB;AACL,mBAAO;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,OAAO,MAAM,QAAQ;AACnC,cAAI,UAAU,KAAK,cAAc,IAAI;AACrC,eAAK,QAAQ,KAAK,cAAc,IAAI;AACpC,eAAK,SAAS;AACd,iBAAO,KAAK,UAAU,OAAO,IAAI,KAAK,aAAa,OAAO,IAAI,KAAK,YAAa;AAAA,QACjF;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,aAAa,MAAM;AACjC,iBAAO,KAAK,YAAY,WAAW,IAAI,WAAW,IAAI,CAAC,CAAC;AAAA,QACzD;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,cAAc;AAC5B,cAAI;AAEJ,cAAI,KAAK,iBAAiB,IAAI;AAC5B,mBAAO,KAAK;AAAA,UACxB,WAAqB,KAAK,SAAS,IAAI;AAC3B,mBAAO,KAAK;AAAA,UACxB,OAAiB;AACL,mBAAO,YAAY,eAAe,KAAK,GAAG;AAAA,UAC3C;AAED,eAAK,UAAU,QAAQ,IAAI,EAAE,eAAe,KAAK,WAAW;AAE5D,cAAI,UAAU;AACd,eAAK,YAAY,WAAW,CAAE;AAC9B,eAAK,iBAAiB,KAAK,aAAa,OAAO;AAC/C,eAAK,YAAa;AAClB,eAAK,iBAAiB,KAAK,YAAY,SAAS,MAAO;AACvD,eAAK,UAAW;AAAA,QACjB;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,iBAAiB,SAAS,MAAM;AAC9C,cAAI,UAAU;AACd,iBAAO,MAAM;AACX,gBAAI,QAAQ,KAAK,MAAM,QAAQ,KAAK,OAAO,IAAI;AAC/C,gBAAI,UAAU,KAAK,MAAM,QAAQ,KAAK,OAAO;AAC7C,gBAAI,WAAW,KAAK,MAAM,QAAQ,KAAK,KAAK,IAAI;AAChD,gBAAI,QAAQ,KAAK,UAAU,OAAO;AAChC,kBAAI,iBAAiB,CAAE;AACvB,6BAAe,WAAW,CAAE;AAC5B,kBAAI,WAAW,KAAK,UAAU,KAAK,MAAM,OAAO,SAAS,QAAQ,UAAU,CAAC,CAAC,EAAE,KAAM;AACrF,kBAAI,OAAO,SAAS,MAAM,IAAI;AAC9B,kBAAI,KAAK,SAAS,GAAG;AACnB,+BAAe,OAAO,KAAK,CAAC;AAC5B,oBAAI,KAAK,UAAU,GAAG;AACpB,iCAAe,OAAO,KAAK,CAAC;AAAA,gBAC9C,OAAuB;AACL,iCAAe,OAAO,KAAK,CAAC,IAAI,KAAK,YAAY,SAAS;AAAA,gBAC3D;AAAA,cACjB,OAAqB;AACL,+BAAe,OAAO;AACtB,+BAAe,OAAO;AAAA,cACvB;AAED,kBAAI,eAAe,SAAS,aAAa;AACvC,+BAAe,OAAO,KAAK,MAAM,OAAO,UAAU,UAAU,QAAQ,EAAE,KAAM;AAC5E,oBAAI,OAAO,KAAK,iBAAiB,gBAAgB,UAAU,CAAC;AAC5D,0BAAU,KAAK;AACf,+BAAe,WAAW,KAAK,OAAO;AAAA,cACtD,OAAqB;AACL,oBAAI,YAAY,KAAK,MAAM,YAAY,KAAK,WAAW,IAAI,KAAK,IAAI,UAAU,OAAO,IAAI,OAAO;AAChG,+BAAe,OAAO,KAAK,MAAM,OAAO,OAAO,YAAY,KAAK,EAAE,KAAM;AACxE,oBAAI,YAAY,KAAK,UAAU,UAAU;AACvC,4BAAU,UAAU;AAAA,gBACtC,OAAuB;AACL,sBAAI,YAAY,KAAK,IAAI,YAAY,GAAG,KAAK;AAC7C,sBAAI,QAAQ,KAAK,iBAAiB,gBAAgB,SAAS;AAC3D,4BAAU,MAAM;AAChB,iCAAe,WAAW,MAAM,OAAO;AAAA,gBACxC;AAAA,cACF;AAED,6BAAe,SAAS;AACxB,kBAAI,eAAe,QAAQ,YAAY;AACrC,wBAAQ,SAAS,KAAK,cAAc;AAAA,cACrC;AAAA,YACf,OAAmB;AACL,wBAAU,UAAU,KAAK,KAAK,MAAM,SAAS,UAAU;AACvD;AAAA,YACD;AAAA,UACF;AAED,iBAAO;AAAA,YACL,QAAQ;AAAA,YACR,KAAK;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,YAAY;AAC1B,cAAI,SAAS;AAEb,eAAK,UAAW;AAChB,cAAI,KAAK,eAAe,UAAU,KAAK,eAAe,SAAS,SAAS,KAAK,CAAC,KAAK,eAAe,QAAQ;AACxG,uBAAW,WAAY;AACrB,qBAAO,UAAW;AAAA,YACnB,GAAE,CAAC;AAAA,UAChB,OAAiB;AACL,uBAAW,WAAY;AACrB,qBAAO,OAAO;AAAA,gBACZ,QAAQ,OAAO;AAAA,gBACf,YAAY,OAAO;AAAA,cACnC,CAAe;AAAA,YACF,GAAE,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,YAAY;AAC1B,cAAI,YAAY;AAChB,iBAAO,MAAM;AACX,gBAAI,CAAC,KAAK,eAAe,QAAQ;AAC/B,sBAAQ,KAAK,eAAe,MAAI;AAAA,gBAC9B,KAAK;AACH;AAAA,gBACF,KAAK;AACH,uBAAK,qBAAqB,SAAS,KAAK,eAAe,IAAI;AAC3D;AAAA,gBACF,KAAK;AACH,uBAAK,UAAW;AAChB;AAAA,gBACF,KAAK;AACH,uBAAK,yBAA0B;AAC/B;AAAA,gBACF,KAAK;AACH,uBAAK,YAAa;AAClB,uBAAK,cAAc,CAAE;AACrB,uBAAK,YAAY,OAAO,KAAK,eAAe,KAAK,KAAM;AACvD,uBAAK,YAAY,aAAa,KAAK,eAAe,KAAK,cAAc,EAAE,KAAM;AAC7E,uBAAK,YAAY,uBAAuB,CAAE;AAC1C,uBAAK,YAAY,eAAe;AAAA,oBAC9B,UAAU,CAAE;AAAA,oBACZ,SAAS,CAAE;AAAA,oBACX,KAAK,CAAE;AAAA,oBACP,aAAa,CAAE;AAAA,oBACf,aAAa,CAAE;AAAA,oBACf,SAAS,CAAE;AAAA,oBACX,iBAAiB,CAAE;AAAA,kBACpB;AACD,uBAAK,YAAY,YAAY,CAAE;AAC/B,uBAAK,YAAY,gBAAgB,CAAE;AACnC,uBAAK,YAAY,WAAW,CAAE;AAC9B,uBAAK,YAAY,YAAY,KAAK;AAClC,uBAAK,2BAA4B;AACjC,uBAAK,iBAAkB;AACvB,8BAAY;AACZ;AAAA,gBACF,KAAK;AACH,uBAAK,iBAAkB;AACvB;AAAA,gBACF,KAAK;AACH,uBAAK,sBAAuB;AAC5B;AAAA,gBACF,KAAK;AACH;AAAA,gBACF,KAAK;AACH,uBAAK,qBAAsB;AAC3B;AAAA,gBACF,KAAK;AACH,uBAAK,aAAc;AACnB;AAAA,gBACF,KAAK;AACH,uBAAK,gBAAiB;AACtB;AAAA,gBACF,KAAK;AACH,uBAAK,YAAa;AAClB,uBAAK,gBAAgB,CAAE;AACvB,uBAAK,cAAc,OAAO,KAAK,eAAe,KAAK,KAAM;AACzD,uBAAK,cAAc,cAAc,CAAE;AACnC;AAAA,gBACF,KAAK;AACH,sBAAI,KAAK,qBAAqB;AAC5B,yBAAK,cAAc,YAAY,KAAK,KAAK,mBAAmB;AAAA,kBAC7D;AAED,uBAAK,sBAAsB,IAAI,eAAgB;AAC/C,uBAAK,oBAAoB,WAAW,KAAK,eAAe,KAAK,KAAM;AACnE;AAAA,gBACF,KAAK;AACH,uBAAK,kBAAmB;AACxB,8BAAY;AACZ;AAAA,cACH;AAED,mBAAK,eAAe,SAAS;AAAA,YAC9B;AAED,gBAAI,KAAK,eAAe,SAAS,SAAS,GAAG;AAC3C,mBAAK,iBAAiB,KAAK,eAAe,SAAS,MAAO;AAC1D,kBAAI,KAAK,OAAO;AACd,wBAAQ,IAAI,gBAAgB,KAAK,eAAe,IAAI;AAAA,cACrD;AAED,kBAAI;AAAW;AAAA,YAC7B,OAAmB;AACL,kBAAI,KAAK,eAAe,QAAQ;AAC9B,oBAAI,KAAK,eAAe,UAAU,CAAC,KAAK,eAAe,OAAO,QAAQ;AACpE,uBAAK,YAAa;AAAA,gBACnB;AAAA,cACF;AAED,kBAAI,KAAK,eAAe,QAAQ;AAC9B,qBAAK,iBAAiB,KAAK,eAAe;AAAA,cAC1D,OAAqB;AACL,4BAAY;AAAA,cACb;AAED,kBAAI;AAAW;AAAA,YAChB;AAAA,UACF;AAED;AAAA,QACD;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,cAAc;AAC5B,cAAI,KAAK,eAAe,QAAQ,KAAK,YAAY,MAAM;AACrD,iBAAK,oBAAqB;AAAA,UAC3B;AAED,eAAK,cAAc,CAAE;AACrB,cAAI,KAAK,iBAAiB,QAAQ,KAAK,cAAc,MAAM;AACzD,gBAAI,KAAK,qBAAqB;AAC5B,mBAAK,cAAc,YAAY,KAAK,KAAK,mBAAmB;AAC5D,mBAAK,sBAAsB;AAAA,YAC5B;AAED,iBAAK,qBAAsB;AAAA,UAC5B;AAED,eAAK,gBAAgB,CAAE;AAAA,QACxB;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,eAAe,MAAM;AACnC,cAAI,KAAK,QAAQ;AACf,gBAAI,KAAK,OAAO,MAAM;AACpB,qBAAO,KAAK,OAAO;AAAA,YACjC,OAAmB;AACL,qBAAO,KAAK,eAAe,KAAK,MAAM;AAAA,YACvC;AAAA,UACb,OAAiB;AACL,mBAAO;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,YAAY;AAC1B,eAAK,gBAAgB,KAAK,eAAe,KAAK,KAAM;AACpD,eAAK,gBAAgB,CAAE;AACvB,eAAK,cAAc,OAAO,KAAK;AAC/B,eAAK,cAAc,WAAW,CAAE;AAChC,cAAI,KAAK,eAAe,UAAU,KAAK,eAAe,OAAO,MAAM;AACjE,iBAAK,cAAc,aAAa,KAAK,eAAe,OAAO;AAAA,UAC5D;AAED,eAAK,gBAAgB,KAAK,KAAK,aAAa;AAC5C,eAAK,SAAS,KAAK,aAAa,IAAI,KAAK;AAAA,QAC1C;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,2BAA2B;AACzC,eAAK,cAAc,uBAAuB,IAAI,QAAS;AACvD,cAAI,OAAO,KAAK,eAAe,KAAK,MAAM,GAAG;AAC7C,eAAK,iBAAiB,KAAK,cAAc,sBAAsB,IAAI;AACnE,eAAK,2BAA4B;AAAA,QAClC;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,6BAA6B;AAC3C,cAAI,CAAC,KAAK,cAAc,sBAAsB;AAC5C;AAAA,UACD;AAED,cAAI,IAAI,IAAI,KAAM;AAClB,YAAE,OAAO,KAAK,cAAc;AAC5B,YAAE,aAAa,KAAK,cAAc,oBAAoB;AACtD,YAAE,cAAc,EAAE;AAClB,YAAE,uBAAuB,KAAK,cAAc;AAC5C,eAAK,cAAc,UAAU;AAC7B,cAAI,KAAK,cAAc,YAAY;AACjC,qBAAS,SAAS,KAAK,UAAU;AAC/B,kBAAI,KAAK,SAAS,KAAK,EAAE,SAAS,KAAK,cAAc,YAAY;AAC/D,qBAAK,SAAS,KAAK,EAAE,QAAQ,IAAI,KAAK,cAAc,OAAO;AAAA,cAC5D;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,mBAAmB;AACjC,cAAI,UAAU;AACd,cAAI,OAAO;AACX,cAAI,aAAa;AACjB,cAAI,YAAY;AAChB,iBAAO,MAAM;AACX,gBAAI,aAAa;AACjB,gBAAI,eAAe,GAAG;AACpB,kBAAI,OAAO,KAAK,UAAU,OAAO;AACjC,wBAAU,KAAK;AACf,2BAAa;AACb,0BAAY,KAAK,eAAe,KAAK,QAAQ,MAAM,OAAO,IAAI;AAC9D,kBAAI,aAAa,GAAG;AAClB,4BAAY,KAAK,eAAe,KAAK;AAAA,cACtC;AAAA,YACf,OAAmB;AACL,kBAAI,OAAO;AACX,sBAAQ,MAAI;AAAA,gBACV,KAAK;AACH,yBAAO,KAAK,eAAe,KAAK,QAAQ,KAAK,OAAO,IAAI;AACxD;AAAA,gBACF,KAAK;AACH,yBAAO,KAAK,eAAe,KAAK,QAAQ,MAAM,OAAO,IAAI;AACzD;AAAA,cACH;AAED,kBAAI,SAAS,KAAK,OAAO,WAAW;AAClC,uBAAO;AACP,6BAAa;AACb,6BAAa;AAAA,cACd;AAED,sBAAQ,KAAK,eAAe,MAAI;AAAA,gBAC9B,KAAK;AACH,0BAAQ,MAAI;AAAA,oBACV,KAAK;AACH,2BAAK,aAAa,KAAK,eAAe,KAAK,OAAO,SAAS,OAAO,OAAO,CAAC;AAC1E;AAAA,oBACF,KAAK;AACH,2BAAK,WAAW,KAAK,eAAe,KAAK,OAAO,SAAS,OAAO,OAAO,CAAC;AACxE;AAAA,kBACH;AAED;AAAA,gBACF,KAAK;AACH,0BAAQ,MAAI;AAAA,oBACV,KAAK;AACH,2BAAK,mBAAmB,KAAK,eAAe,KAAK,OAAO,SAAS,OAAO,OAAO,CAAC;AAChF;AAAA,kBACH;AAED;AAAA,cACH;AAED,wBAAU,OAAO;AACjB,kBAAI,YAAY;AACd;AAAA,cACD;AAAA,YACF;AAED,gBAAI,WAAW,KAAK,eAAe,KAAK,QAAQ;AAC9C;AAAA,YACD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,UAAU,OAAO;AAC/B,cAAI,OAAO,KAAK,eAAe,KAAK,QAAQ,KAAK,KAAK;AACtD,iBAAO;AAAA,YACL,MAAM,SAAS,KAAK,eAAe,KAAK,OAAO,OAAO,OAAO,KAAK,CAAC;AAAA,YACnE,SAAS,OAAO;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,aAAa,MAAM;AACjC,cAAI,OAAO,KAAK,UAAU,KAAK,KAAI,CAAE,EAClC,OAAO,GAAG,KAAK,SAAS,CAAC,EACzB,MAAM,GAAG;AACZ,eAAK,YAAY,aAAa,SAAS,KAAK,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,CAAC;AACzG,eAAK,YAAY,aAAa,YAAY,KAAK,GAAG,GAAG,GAAG,CAAC;AACzD,eAAK,YAAY,aAAa,YAAY,KAAK,GAAG,GAAG,GAAG,CAAC;AACzD,eAAK,YAAY,qBAAqB,KAAK,CAAC;AAAA,QAC7C;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,WAAW,MAAM;AAC/B,cAAI,OAAO,KAAK,UAAU,KAAK,KAAI,CAAE,EAClC,OAAO,GAAG,KAAK,SAAS,CAAC,EACzB,MAAM,GAAG;AACZ,eAAK,YAAY,aAAa,QAAQ;AAAA,YACpC,SAAS,KAAK,CAAC,GAAG,EAAE;AAAA,YACpB,SAAS,KAAK,CAAC,GAAG,EAAE;AAAA,YACpB,SAAS,KAAK,CAAC,GAAG,EAAE;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,mBAAmB,MAAM;AACvC,cAAI,OAAO,KAAK,UAAU,KAAK,KAAI,CAAE,EAClC,OAAO,GAAG,KAAK,SAAS,CAAC,EACzB,MAAM,GAAG;AACZ,eAAK,YAAY,aAAa,QAAQ,KAAK,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,CAAC;AAAA,QACzG;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,iBAAiB;AAC/B,cAAI,iBAAiB,IAAI,eAAgB;AACzC,cAAI,WAAW,CAAE;AACjB,cAAI,UAAU,CAAE;AAChB,cAAI,MAAM,CAAE;AACZ,cAAI,cAAc,CAAE;AACpB,cAAI,cAAc,CAAE;AAIpB,cAAI,OAAO,KAAK,YAAY;AAE5B,mBAASA,KAAI,GAAG,IAAI,KAAK,QAAQ,QAAQA,KAAI,GAAGA,MAAK;AACnD,gBAAI,UAAU,KAAK,QAAQA,EAAC,IAAI;AAChC,gBAAI,UAAU,KAAK,QAAQA,EAAC,IAAI;AAChC,gBAAI,UAAU,KAAK,QAAQA,EAAC,IAAI;AAEhC,qBAAS,KAAK,KAAK,SAAS,OAAO,GAAG,KAAK,SAAS,UAAU,CAAC,GAAG,KAAK,SAAS,UAAU,CAAC,CAAC;AAC5F,oBAAQ,KAAK,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,UAAU,CAAC,GAAG,KAAK,QAAQ,UAAU,CAAC,CAAC;AACxF,wBAAY;AAAA,cACV,KAAK,YAAY,OAAO;AAAA,cACxB,KAAK,YAAY,UAAU,CAAC;AAAA,cAC5B,KAAK,YAAY,UAAU,CAAC;AAAA,cAC5B,KAAK,YAAY,UAAU,CAAC;AAAA,YAC7B;AACD,wBAAY;AAAA,cACV,KAAK,YAAY,OAAO;AAAA,cACxB,KAAK,YAAY,UAAU,CAAC;AAAA,cAC5B,KAAK,YAAY,UAAU,CAAC;AAAA,cAC5B,KAAK,YAAY,UAAU,CAAC;AAAA,YAC7B;AACD,gBAAI,KAAK,KAAK,IAAI,OAAO,GAAG,KAAK,IAAI,UAAU,CAAC,CAAC;AAAA,UAClD;AAID,yBAAe,aAAa,YAAY,IAAI,uBAAuB,UAAU,CAAC,CAAC;AAC/E,yBAAe,aAAa,UAAU,IAAI,uBAAuB,SAAS,CAAC,CAAC;AAC5E,yBAAe,aAAa,MAAM,IAAI,uBAAuB,KAAK,CAAC,CAAC;AACpE,yBAAe,aAAa,aAAa,IAAI,sBAAsB,aAAa,CAAC,CAAC;AAClF,yBAAe,aAAa,cAAc,IAAI,uBAAuB,aAAa,CAAC,CAAC;AAEpF,eAAK,eAAe,gBAAgB,KAAK,eAAe;AAExD,iBAAO;AAAA,QACR;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,eAAe,gBAAgB,iBAAiB;AAC9D,cAAI;AACJ,cAAI,SAAS,CAAE;AACf,cAAI,gBAAgB;AAEpB,mBAASA,KAAI,GAAGA,KAAI,gBAAgB,QAAQA,MAAK;AAC/C,gBAAI,uBAAuB,gBAAgBA,EAAC;AAE5C,gBAAI,yBAAyB,eAAe;AAC1C,8BAAgB;AAEhB,kBAAI,UAAU,QAAW;AACvB,sBAAM,QAAQA,KAAI,IAAI,MAAM;AAC5B,uBAAO,KAAK,KAAK;AAAA,cAClB;AAED,sBAAQ;AAAA,gBACN,OAAOA,KAAI;AAAA,gBACX;AAAA,cACD;AAAA,YACF;AAAA,UACF;AAED,cAAI,UAAU,QAAW;AACvB,kBAAM,QAAQ,IAAI,IAAI,MAAM;AAC5B,mBAAO,KAAK,KAAK;AAAA,UAClB;AAED,yBAAe,SAAS;AAAA,QACzB;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,wBAAwB;AACtC,cAAI,UAAU;AACd,cAAI,OAAO;AACX,cAAI,aAAa;AACjB,iBAAO,MAAM;AACX,oBAAQ,MAAI;AAAA,cACV,KAAK;AACH,oBAAI,eAAe,GAAG;AACpB,sBAAI,OAAO,KAAK,UAAU,CAAC;AAC3B,4BAAU,KAAK;AACf,+BAAa;AAAA,gBAC/B,OAAuB;AACL,sBAAI,OAAO,KAAK,eAAe,KAAK,QAAQ,KAAK,OAAO,IAAI;AAC5D,sBAAI,SAAS,GAAG;AACd,2BAAO,KAAK,eAAe,KAAK;AAChC,2BAAO;AACP,iCAAa;AAAA,kBACd;AAED,sBAAI,OAAO,KAAK,eAAe,KAAK,OAAO,SAAS,OAAO,OAAO;AAClE,sBAAI,OAAO,KAAK,UAAU,KAAK,MAAM,EAAE,MAAM,GAAG;AAChD,sBAAI,KAAK,cAAc;AACrB,yBAAK,YAAY,aAAa,IAAI,KAAK,WAAW,KAAK,CAAC,CAAC,GAAG,IAAI,WAAW,KAAK,CAAC,CAAC,CAAC;AAAA,kBACvG,OAAyB;AACL,yBAAK,YAAY,aAAa,IAAI,KAAK,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,CAAC;AAAA,kBAChF;AAED,4BAAU,OAAO;AAAA,gBAClB;AAED;AAAA,YACH;AAED,gBAAI,WAAW,KAAK,eAAe,KAAK,QAAQ;AAC9C;AAAA,YACD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,uBAAuB;AACrC,cAAI,UAAU;AACd,cAAI,OAAO;AACX,cAAI,aAAa;AACjB,iBAAO,MAAM;AACX,gBAAI,aAAa,GAAG;AAClB,kBAAI,OAAO,KAAK,UAAU,OAAO;AACjC,wBAAU,KAAK;AACf;AAAA,YACd,OAAmB;AACL,kBAAI,OAAO,KAAK,eAAe,KAAK,QAAQ,KAAK,OAAO;AACxD,kBAAI,SAAS,IAAI;AACf,uBAAO,KAAK,eAAe,KAAK;AAChC,uBAAO;AACP,6BAAa;AAAA,cACd;AAED,kBAAI,OAAO,KAAK,eAAe,KAAK,OAAO,SAAS,OAAO,OAAO;AAClE,kBAAI,OAAO,KAAK,UAAU,KAAK,MAAM,EAAE,MAAM,GAAG;AAChD,uBAASA,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK;AACpC,qBAAK,YAAY,aAAa,gBAAgBA,EAAC,IAAI,SAAS,KAAKA,EAAC,CAAC;AAAA,cACpE;AAED,wBAAU,KAAK,eAAe,KAAK;AAAA,YACpC;AAED,gBAAI,WAAW,KAAK,eAAe,KAAK,UAAU,QAAQ,GAAG;AAC3D;AAAA,YACD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,eAAe;AAC7B,cAAI,UAAU,IAAI,kBAAkB;AAAA,YAClC,OAAO,KAAK,OAAM,IAAK;AAAA,UACnC,CAAW;AACD,kBAAQ,OAAO;AACf,kBAAQ,OAAO,KAAK,eAAe;AACnC,cAAI,UAAU;AACd,cAAI,OAAO,KAAK,eAAe,KAAK,QAAQ,MAAM,OAAO;AACzD,cAAI,OAAO,KAAK,eAAe,KAAK,OAAO,SAAS,OAAO,OAAO;AAClE,cAAI,OAAO,KAAK,UAAU,KAAK,MAAM,EAAE,MAAM,GAAG;AAChD,kBAAQ,MAAM,IAAI,WAAW,KAAK,CAAC,CAAC;AACpC,kBAAQ,MAAM,IAAI,WAAW,KAAK,CAAC,CAAC;AACpC,kBAAQ,MAAM,IAAI,WAAW,KAAK,CAAC,CAAC;AACpC,oBAAU,OAAO;AACjB,iBAAO,KAAK,eAAe,KAAK,QAAQ,KAAK,OAAO;AACpD,iBAAO,KAAK,eAAe,KAAK,OAAO,SAAS,OAAO,OAAO;AAC9D,kBAAQ,YAAY,WAAW,KAAK,UAAU,IAAI,CAAC;AACnD,oBAAU,OAAO;AACjB,iBAAO,KAAK,eAAe,KAAK,QAAQ,MAAM,OAAO;AACrD,iBAAO,KAAK,eAAe,KAAK,OAAO,SAAS,OAAO,OAAO;AAC9D,cAAI,QAAQ,KAAK,UAAU,KAAK,MAAM,EAAE,MAAM,GAAG;AACjD,kBAAQ,SAAS,IAAI,WAAW,MAAM,CAAC,CAAC;AACxC,kBAAQ,SAAS,IAAI,WAAW,MAAM,CAAC,CAAC;AACxC,kBAAQ,SAAS,IAAI,WAAW,MAAM,CAAC,CAAC;AACxC,oBAAU,OAAO;AACjB,iBAAO,KAAK,eAAe,KAAK,QAAQ,MAAM,OAAO;AACrD,cAAI,SAAS,IAAI;AACf,mBAAO,KAAK,eAAe,KAAK;AAAA,UACjC;AAED,iBAAO,KAAK,eAAe,KAAK,OAAO,SAAS,OAAO,OAAO;AAC9D,cAAI,QAAQ,KAAK,UAAU,KAAK,MAAM,EAAE,MAAM,GAAG;AACjD,kBAAQ,SAAS,IAAI,WAAW,MAAM,CAAC,CAAC;AACxC,kBAAQ,SAAS,IAAI,WAAW,MAAM,CAAC,CAAC;AACxC,kBAAQ,SAAS,IAAI,WAAW,MAAM,CAAC,CAAC;AACxC,cAAI,cAAc;AAClB,iBAAO,MAAM;AACX,gBAAI,KAAK,eAAe,SAAS,SAAS,GAAG;AAC3C,4BAAc,KAAK,eAAe,SAAS,MAAO;AAClD,kBAAI,KAAK,OAAO;AACd,wBAAQ,IAAI,gBAAgB,YAAY,IAAI;AAAA,cAC7C;AAED,kBAAI,WAAW,YAAY,KAAK,OAAO,GAAG,YAAY,KAAK,SAAS,CAAC;AACrE,sBAAQ,YAAY,MAAI;AAAA,gBACtB,KAAK;AACH,0BAAQ,MAAM,KAAK,UAAU,KAAK,QAAQ;AAC1C;AAAA,gBACF,KAAK;AACH,0BAAQ,UAAU,KAAK,UAAU,KAAK,QAAQ;AAC9C,0BAAQ,YAAY;AACpB;AAAA,gBACF,KAAK;AACH,0BAAQ,YAAY,KAAK,UAAU,KAAK,QAAQ;AAChD,0BAAQ,cAAc,IAAI,QAAQ,GAAG,CAAC;AACtC;AAAA,gBACF,KAAK;AACH,0BAAQ,cAAc,KAAK,UAAU,KAAK,QAAQ;AAClD;AAAA,gBACF,KAAK;AACH,0BAAQ,WAAW,KAAK,UAAU,KAAK,QAAQ;AAC/C;AAAA,cACH;AAAA,YACf,OAAmB;AACL;AAAA,YACD;AAAA,UACF;AAED,eAAK,YAAY,UAAU,KAAK,OAAO;AAAA,QACxC;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,kBAAkB;AAChC,cAAI,UAAU,IAAI,SAAU;AAC5B,cAAI,UAAU;AACd,cAAI,OAAO,KAAK,eAAe,KAAK,QAAQ,KAAK,OAAO;AACxD,cAAI,OAAO,KAAK,eAAe,KAAK,OAAO,SAAS,OAAO,OAAO;AAClE,oBAAU,OAAO;AACjB,kBAAQ,WAAW,KAAK,OAAO,GAAG,KAAK,SAAS,CAAC;AACjD,kBAAQ,YAAY,KAAK,YAAY,SAAS;AAC9C,iBAAO,KAAK,eAAe,KAAK,QAAQ,KAAK,OAAO;AACpD,oBAAU,OAAO;AACjB,iBAAO,KAAK,eAAe,KAAK,QAAQ,KAAK,OAAO;AACpD,iBAAO,KAAK,eAAe,KAAK,OAAO,SAAS,OAAO,OAAO;AAC9D,cAAI,OAAO,KAAK,UAAU,KAAK,MAAM,EAAE,MAAM,GAAG;AAChD,mBAASA,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK;AACpC,oBAAQ,QAAQ,KAAK,SAAS,KAAKA,EAAC,CAAC,CAAC;AAAA,UACvC;AAED,oBAAU,OAAO;AACjB,iBAAO,KAAK,eAAe,KAAK,QAAQ,KAAK,OAAO;AACpD,iBAAO,KAAK,eAAe,KAAK,OAAO,SAAS,OAAO,OAAO;AAC9D,cAAI,QAAQ,KAAK,UAAU,KAAK,MAAM,EAAE,MAAM,GAAG;AACjD,mBAAS,KAAK,GAAG,KAAK,MAAM,QAAQ,MAAM;AACxC,oBAAQ,QAAQ,KAAK,WAAW,MAAM,EAAE,CAAC,CAAC;AAAA,UAC3C;AAED,oBAAU,OAAO;AACjB,iBAAO,KAAK,eAAe,KAAK,QAAQ,KAAK,OAAO;AACpD,cAAI,QAAQ,GAAG;AACb,mBAAO,KAAK,eAAe,KAAK;AAAA,UACjC;AAED,iBAAO,KAAK,eAAe,KAAK,OAAO,SAAS,OAAO,OAAO;AAC9D,cAAI,QAAQ,KAAK,UAAU,KAAK,MAAM,EAAE,MAAM,GAAG;AACjD,kBAAQ,eAAe,IAAI,QAAS;AACpC,eAAK,iBAAiB,QAAQ,cAAc,KAAK;AACjD,eAAK,YAAY,SAAS,KAAK,OAAO;AAAA,QACvC;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,iBAAiB,WAAW,QAAQ;AAClD,cAAI,UAAU;AACd,mBAAS,SAAS,KAAK,UAAU;AAC/B,gBAAI,KAAK,SAAS,KAAK,EAAE,SAAS,aAAa,SAAS;AACtD,wBAAU;AACV,kBAAI,IAAI,IAAI,KAAM;AAClB,gBAAE,OAAO,KAAK,SAAS,KAAK,EAAE;AAC9B,gBAAE,aAAa,KAAK,SAAS,KAAK,EAAE,oBAAoB;AACxD,gBAAE,cAAc,EAAE;AAClB,gBAAE,uBAAuB,KAAK,SAAS,KAAK,EAAE;AAC9C,gBAAE,MAAM,IAAI,QAAS,EAAC,sBAAsB,EAAE,oBAAoB,EAAE,QAAS;AAC7E,gBAAE,OAAO,IAAI,WAAY,EAAC,sBAAsB,EAAE,oBAAoB,EAAE,QAAS;AACjF,gBAAE,MAAM,IAAI,QAAS,EAAC,mBAAmB,EAAE,oBAAoB,EAAE,QAAS;AAC1E,kBAAI,KAAK,SAAS,KAAK,EAAE,cAAc,KAAK,SAAS,KAAK,EAAE,WAAW,SAAS,GAAG;AACjF,yBAASA,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AACtC,sBAAI,KAAK,SAAS,KAAK,EAAE,eAAe,OAAOA,EAAC,EAAE,MAAM;AACtD,2BAAOA,EAAC,EAAE,IAAI,CAAC;AACf,sBAAE,SAASA;AACX;AAAA,kBACD;AAAA,gBACF;AAAA,cACF;AAED,qBAAO,KAAK,CAAC;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,sBAAsB;AACpC,cAAI,OAAO;AACX,cAAI,KAAK,YAAY,SAAS,SAAS,GAAG;AACxC,gBAAI,WAAW,CAAE;AACjB,iBAAK,iBAAiB,KAAK,YAAY,UAAU,YAAY,QAAQ;AACrE,qBAAS,KAAK,GAAG,KAAK,KAAK,YAAY,SAAS,QAAQ,MAAM;AAC5D,kBAAI,YAAY;AAChB,uBAAS,KAAK,GAAG,KAAK,SAAS,QAAQ,MAAM;AAC3C,oBAAI,SAAS,EAAE,EAAE,SAAS,KAAK,YAAY,SAAS,EAAE,EAAE,UAAU;AAChE,8BAAY;AACZ,2BAAS,EAAE,EAAE,eAAe,IAAI,QAAS;AACzC,2BAAS,EAAE,EAAE,aAAa,KAAK,KAAK,YAAY,SAAS,EAAE,EAAE,YAAY;AACzE;AAAA,gBACD;AAAA,cACF;AAED,uBAAS,KAAK,GAAG,KAAK,KAAK,YAAY,SAAS,EAAE,EAAE,QAAQ,QAAQ,MAAM;AACxE,oBAAI,cAAc,KAAK,YAAY,SAAS,EAAE,EAAE,QAAQ,EAAE;AAC1D,oBAAI,SAAS,KAAK,YAAY,SAAS,EAAE,EAAE,QAAQ,EAAE;AAErD,oBAAI,SAAS,cAAc;AAE3B,wBAAQ,KAAK,YAAY,qBAAqB,WAAW,GAAC;AAAA,kBACxD,KAAK;AACH,yBAAK,YAAY,aAAa,YAAY,MAAM,IAAI;AACpD,yBAAK,YAAY,aAAa,YAAY,MAAM,IAAI;AACpD;AAAA,kBACF,KAAK;AACH,yBAAK,YAAY,aAAa,YAAY,SAAS,CAAC,IAAI;AACxD,yBAAK,YAAY,aAAa,YAAY,SAAS,CAAC,IAAI;AACxD;AAAA,kBACF,KAAK;AACH,yBAAK,YAAY,aAAa,YAAY,SAAS,CAAC,IAAI;AACxD,yBAAK,YAAY,aAAa,YAAY,SAAS,CAAC,IAAI;AACxD;AAAA,kBACF,KAAK;AACH,yBAAK,YAAY,aAAa,YAAY,SAAS,CAAC,IAAI;AACxD,yBAAK,YAAY,aAAa,YAAY,SAAS,CAAC,IAAI;AACxD;AAAA,gBACH;AAED,qBAAK,YAAY,qBAAqB,WAAW;AACjD,oBAAI,KAAK,YAAY,qBAAqB,WAAW,IAAI,GAAG;AAC1D,0BAAQ,IAAI,gCAAgC,WAAW;AAAA,gBACxD;AAAA,cACF;AAAA,YACF;AAED,qBAAS,KAAK,GAAG,KAAK,KAAK,YAAY,UAAU,QAAQ,MAAM;AAC7D,mBAAK,YAAY,UAAU,EAAE,EAAE,WAAW;AAAA,YAC3C;AAED,gBAAI,aAAa,CAAE;AACnB,qBAAS,MAAM,GAAG,MAAM,SAAS,QAAQ,OAAO;AAC9C,kBAAI,SAAS,GAAG,EAAE,cAAc;AAC9B,2BAAW,KAAK,SAAS,GAAG,EAAE,YAAY;AAAA,cAC1D,OAAqB;AACL,2BAAW,KAAK,IAAI,SAAS;AAAA,cAC9B;AAAA,YACF;AAED,gBAAI,iBAAiB,KAAK,eAAgB;AAC1C,mBAAO,IAAI;AAAA,cACT;AAAA,cACA,KAAK,YAAY,UAAU,WAAW,IAAI,KAAK,YAAY,UAAU,CAAC,IAAI,KAAK,YAAY;AAAA,YAC5F;AAED,iBAAK,cAAc,MAAM,UAAU,UAAU;AAAA,UACzD,OAAiB;AACL,gBAAI,kBAAkB,KAAK,eAAgB;AAC3C,mBAAO,IAAI;AAAA,cACT;AAAA,cACA,KAAK,YAAY,UAAU,WAAW,IAAI,KAAK,YAAY,UAAU,CAAC,IAAI,KAAK,YAAY;AAAA,YAC5F;AAAA,UACF;AAED,eAAK,OAAO,KAAK,YAAY;AAC7B,cAAI,cAAc,IAAI,QAAS;AAC/B,cAAI,iBAAiB,KAAK,YAAY,UAAU;AAChD,cAAI,kBAAkB,eAAe,QAAQ;AAC3C,mBAAO,MAAM;AACX,+BAAiB,eAAe;AAChC,kBAAI,gBAAgB;AAClB,4BAAY,SAAS,eAAe,oBAAoB;AAAA,cACxE,OAAqB;AACL;AAAA,cACD;AAAA,YACF;AAED,iBAAK,aAAa,WAAW;AAAA,UAC9B;AAED,eAAK,OAAO,KAAK,IAAI;AAAA,QACtB;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,cAAc,MAAM,UAAU,cAAc;AAC1D,cAAI,QAAQ,CAAE,GACZ,MACA;AACF,cAAIA,IAAG;AAEP,eAAKA,KAAI,GAAG,KAAK,SAAS,QAAQA,KAAI,IAAIA,MAAK;AAC7C,oBAAQ,SAASA,EAAC;AAElB,mBAAO,IAAI,KAAM;AACjB,kBAAM,KAAK,IAAI;AAEf,iBAAK,OAAO,MAAM;AAClB,iBAAK,SAAS,UAAU,MAAM,GAAG;AACjC,iBAAK,WAAW,UAAU,MAAM,IAAI;AACpC,gBAAI,MAAM,QAAQ;AAAW,mBAAK,MAAM,UAAU,MAAM,GAAG;AAAA,UAC5D;AAED,eAAKA,KAAI,GAAG,KAAK,SAAS,QAAQA,KAAI,IAAIA,MAAK;AAC7C,oBAAQ,SAASA,EAAC;AAElB,gBAAI,MAAM,WAAW,MAAM,MAAM,WAAW,QAAQ,MAAM,MAAM,MAAM,MAAM,QAAW;AACrF,oBAAM,MAAM,MAAM,EAAE,IAAI,MAAMA,EAAC,CAAC;AAAA,YAC9C,OAAmB;AACL,mBAAK,IAAI,MAAMA,EAAC,CAAC;AAAA,YAClB;AAAA,UACF;AAED,eAAK,kBAAkB,IAAI;AAE3B,cAAI,WAAW,IAAI,SAAS,OAAO,YAAY;AAC/C,eAAK,KAAK,UAAU,KAAK,WAAW;AAAA,QACrC;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,oBAAoB;AAClC,cAAI,UAAU;AACd,cAAI,OAAO,KAAK,eAAe,KAAK,QAAQ,KAAK,OAAO;AACxD,cAAI,OAAO,KAAK,eAAe,KAAK,OAAO,SAAS,OAAO,OAAO;AAClE,oBAAU,OAAO;AACjB,cAAI,aAAa,SAAS,KAAK,UAAU,IAAI,CAAC;AAC9C,iBAAO,KAAK,eAAe,KAAK,QAAQ,KAAK,OAAO;AACpD,oBAAU,OAAO;AACjB,iBAAO,KAAK,eAAe,KAAK,OAAO,OAAO;AAC9C,cAAI,OAAO,KAAK,UAAU,KAAK,MAAM,EAAE,MAAM,KAAK;AAClD,mBAASA,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK;AACpC,gBAAI,QAAQ,KAAKA,EAAC,EAAE,MAAM,GAAG;AAC7B,gBAAI,UAAU,IAAI,cAAe;AACjC,oBAAQ,OAAO;AACf,oBAAQ,QAAQ,SAAS,MAAM,CAAC,CAAC;AACjC,oBAAQ,QAAQ,KAAK,oBAAoB,UAAU;AACnD,oBAAQ,OAAO,QAAQ;AACvB,gBAAI,cAAc,GAAG;AACnB,kBAAI,aAAa;AACjB,uBAAS,KAAK,GAAG,KAAK,KAAK,oBAAoB,UAAU,QAAQ,MAAM;AACrE,oBAAI,KAAK,oBAAoB,UAAU,EAAE,EAAE,UAAU,QAAQ,OAAO;AAClE,4BAAU,KAAK,oBAAoB,UAAU,EAAE;AAC/C,+BAAa;AACb;AAAA,gBACD;AAAA,cACF;AAED,kBAAI,aAAa,MAAM,CAAC,EAAE,MAAM,GAAG;AACnC,sBAAQ,YAAU;AAAA,gBAChB,KAAK;AACH,0BAAQ,MAAM,IAAI;AAAA,oBAChB,WAAW,WAAW,CAAC,CAAC;AAAA,oBACxB,WAAW,WAAW,CAAC,CAAC;AAAA,oBACxB,WAAW,WAAW,CAAC,CAAC;AAAA,oBACxB,WAAW,WAAW,CAAC,CAAC,IAAI;AAAA,kBAC7B;AACD;AAAA,gBACF,KAAK;AACH,0BAAQ,MAAM,IAAI;AAAA,oBAChB,WAAW,WAAW,CAAC,CAAC;AAAA,oBACxB,WAAW,WAAW,CAAC,CAAC;AAAA,oBACxB,WAAW,WAAW,CAAC,CAAC;AAAA,kBACzB;AACD;AAAA,gBACF,KAAK;AACH,0BAAQ,MAAM,IAAI;AAAA,oBAChB,WAAW,WAAW,CAAC,CAAC;AAAA,oBACxB,WAAW,WAAW,CAAC,CAAC;AAAA,oBACxB,WAAW,WAAW,CAAC,CAAC;AAAA,kBACzB;AACD;AAAA,cACH;AAED,kBAAI,CAAC,YAAY;AACf,qBAAK,oBAAoB,UAAU,KAAK,OAAO;AAAA,cAChD;AAAA,YACf,OAAmB;AACL,sBAAQ,SAAS,IAAI,QAAS;AAC9B,mBAAK,iBAAiB,QAAQ,QAAQ,MAAM,CAAC,EAAE,MAAM,GAAG,CAAC;AACzD,mBAAK,oBAAoB,UAAU,KAAK,OAAO;AAAA,YAChD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,uBAAuB;AACrC,cAAI,eAAe,IAAI,cAAc,KAAK,OAAO;AACjD,uBAAa,MAAM,KAAK;AACxB,uBAAa,OAAO,KAAK,cAAc;AACvC,uBAAa,KAAK,KAAK,cAAc,WAAW;AAChD,eAAK,WAAW,KAAK,YAAY;AAAA,QAClC;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,gBAAgB,QAAQ,YAAY;AAClD,cAAI,QAAQ;AACZ,cAAI,YAAY;AAChB,cAAI,CAAC,OAAO;AACV,oBAAQ,KAAK,OAAO,CAAC;AAAA,UACtB;AAED,cAAI,CAAC,WAAW;AACd,wBAAY,KAAK,WAAW,CAAC;AAAA,UAC9B;AAED,cAAI,CAAC,SAAS,CAAC,WAAW;AACxB,mBAAO;AAAA,UACR;AAED,cAAI,MAAM,CAAE;AACZ,cAAI,MAAM,UAAU;AACpB,cAAI,OAAO,UAAU;AACrB,cAAI,SAAS,UAAU;AACvB,cAAI,YAAY,CAAE;AAClB,mBAAS,IAAI,GAAG,IAAI,MAAM,SAAS,MAAM,QAAQ,KAAK;AACpD,gBAAI,gBAAgB;AACpB,qBAASA,KAAI,GAAGA,KAAI,UAAU,UAAU,QAAQA,MAAK;AACnD,kBAAI,MAAM,SAAS,MAAM,CAAC,EAAE,SAAS,UAAU,UAAUA,EAAC,EAAE,MAAM;AAChE,gCAAgB;AAChB,oBAAI,QAAQ,UAAU,UAAUA,EAAC,EAAE,KAAM;AACzC,sBAAM,SAAS;AACf,oBAAI,MAAM,SAAS,MAAM,CAAC,EAAE,UAAU,MAAM,SAAS,MAAM,CAAC,EAAE,OAAO,SAAS,QAAQ;AACpF,2BAAS,KAAK,GAAG,KAAK,IAAI,UAAU,QAAQ,MAAM;AAChD,wBAAI,IAAI,UAAU,EAAE,EAAE,SAAS,MAAM,SAAS,MAAM,CAAC,EAAE,OAAO,MAAM;AAClE,4BAAM,SAAS;AACf,4BAAM,aAAa,MAAM,SAAS,MAAM,CAAC,EAAE,OAAO;AAAA,oBACnD;AAAA,kBACF;AAAA,gBACF;AAED,oBAAI,UAAU,KAAK,KAAK;AACxB;AAAA,cACD;AAAA,YACF;AAED,gBAAI,CAAC,eAAe;AAClB,kBAAI,SAAS,UAAU,UAAU,CAAC,EAAE,KAAM;AAC1C,qBAAO,OAAO,MAAM,SAAS,MAAM,CAAC,EAAE;AACtC,qBAAO,SAAS;AAChB,uBAAS,IAAI,GAAG,IAAI,OAAO,KAAK,QAAQ,KAAK;AAC3C,oBAAI,OAAO,KAAK,CAAC,EAAE,KAAK;AACtB,yBAAO,KAAK,CAAC,EAAE,IAAI,IAAI,GAAG,GAAG,CAAC;AAAA,gBAC/B;AAED,oBAAI,OAAO,KAAK,CAAC,EAAE,KAAK;AACtB,yBAAO,KAAK,CAAC,EAAE,IAAI,IAAI,GAAG,GAAG,CAAC;AAAA,gBAC/B;AAED,oBAAI,OAAO,KAAK,CAAC,EAAE,KAAK;AACtB,yBAAO,KAAK,CAAC,EAAE,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;AAAA,gBAClC;AAAA,cACF;AAED,kBAAI,UAAU,KAAK,MAAM;AAAA,YAC1B;AAAA,UACF;AAED,cAAI,CAAC,MAAM,SAAS,YAAY;AAC9B,kBAAM,SAAS,aAAa,CAAE;AAAA,UAC/B;AAED,gBAAM,SAAS,WAAW,KAAK,cAAc,eAAe,KAAK,MAAM,SAAS,KAAK,CAAC;AACtF,cAAI,CAAC,MAAM,gBAAgB;AACzB,kBAAM,iBAAiB,IAAI,eAAe,KAAK;AAAA,UAChD;AAED,iBAAO;AAAA,QACR;AAAA,MACF;AAAA,MACD;AAAA,QACE,KAAK;AAAA,QACL,OAAO,SAAS,iBAAiB,cAAc,MAAM;AACnD,uBAAa;AAAA,YACX,WAAW,KAAK,CAAC,CAAC;AAAA,YAClB,WAAW,KAAK,CAAC,CAAC;AAAA,YAClB,WAAW,KAAK,CAAC,CAAC;AAAA,YAClB,WAAW,KAAK,EAAE,CAAC;AAAA,YACnB,WAAW,KAAK,CAAC,CAAC;AAAA,YAClB,WAAW,KAAK,CAAC,CAAC;AAAA,YAClB,WAAW,KAAK,CAAC,CAAC;AAAA,YAClB,WAAW,KAAK,EAAE,CAAC;AAAA,YACnB,WAAW,KAAK,CAAC,CAAC;AAAA,YAClB,WAAW,KAAK,CAAC,CAAC;AAAA,YAClB,WAAW,KAAK,EAAE,CAAC;AAAA,YACnB,WAAW,KAAK,EAAE,CAAC;AAAA,YACnB,WAAW,KAAK,CAAC,CAAC;AAAA,YAClB,WAAW,KAAK,CAAC,CAAC;AAAA,YAClB,WAAW,KAAK,EAAE,CAAC;AAAA,YACnB,WAAW,KAAK,EAAE,CAAC;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AAAA,IACP,CAAK;AACD,WAAOK;AAAA,EACX,EAAM;AAEJ,SAAOA;AACT,EAAC;"}