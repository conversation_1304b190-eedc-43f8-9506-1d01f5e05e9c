import { ColorRepresentation, Vector3 } from 'three';
import { Axis } from '../../state.js';
import { HandlesContext } from '../context.js';
import { HandlesProperties } from '../index.js';
import { RegisteredHandle } from '../registered.js';
export declare class AxisTranslateHandle extends RegisteredHandle {
    private readonly invert;
    private readonly showArrowBody;
    constructor(context: HandlesContext, axis: Axis, tagPrefix?: string, axisVector?: Vector3, invert?: boolean, showArrowBody?: boolean);
    bind(defaultColor: ColorRepresentation, defaultHoverColor: ColorRepresentation, config?: HandlesProperties): (() => void) | undefined;
}
