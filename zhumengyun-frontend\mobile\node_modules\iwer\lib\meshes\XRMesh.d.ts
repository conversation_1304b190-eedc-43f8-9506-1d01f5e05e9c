/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_MESH } from '../private.js';
import type { XRFrame } from '../frameloop/XRFrame.js';
import { XRRigidTransform } from '../primitives/XRRigidTransform.js';
import { XRSemanticLabels } from '../labels/labels.js';
import { XRSpace } from '../spaces/XRSpace.js';
export declare class XRMesh {
    [P_MESH]: {
        nativeMesh: NativeMesh;
        frame: XRFrame;
        meshSpace: XRSpace;
        vertices: Float32Array;
        indices: Uint32Array;
        lastChangedTime: DOMHighResTimeStamp;
        semanticLabel?: XRSemanticLabels;
    };
    constructor(nativeMesh: NativeMesh, meshSpace: XRSpace, vertices: Float32Array, indices: Uint32Array, semanticLabel?: XRSemanticLabels);
    get meshSpace(): XRSpace;
    get vertices(): Readonly<Float32Array>;
    get indices(): Readonly<Uint32Array>;
    get lastChangedTime(): number;
    get semanticLabel(): XRSemanticLabels | undefined;
}
export declare class XRMeshSet extends Set<XRMesh> {
}
export declare class NativeMesh {
    transform: XRRigidTransform;
    vertices: Float32Array;
    indices: Uint32Array;
    semanticLabel: XRSemanticLabels;
    constructor(transform: XRRigidTransform, vertices: Float32Array, indices: Uint32Array, semanticLabel: XRSemanticLabels);
}
//# sourceMappingURL=XRMesh.d.ts.map