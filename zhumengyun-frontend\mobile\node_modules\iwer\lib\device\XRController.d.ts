/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { GamepadConfig } from '../gamepad/Gamepad.js';
import { GlobalSpace } from '../spaces/XRSpace.js';
import { P_CONTROLLER } from '../private.js';
import { XRHandedness } from '../input/XRInputSource.js';
import { XRTrackedInput } from './XRTrackedInput.js';
import { mat4 } from 'gl-matrix';
export interface XRControllerConfig {
    profileId: string;
    fallbackProfileIds: string[];
    layout: {
        [handedness in XRHandedness]?: {
            gamepad: GamepadConfig;
            gripOffsetMatrix?: mat4;
            numHapticActuators: number;
        };
    };
}
export declare class XRController extends XRTrackedInput {
    [P_CONTROLLER]: {
        profileId: string;
        gamepadConfig: GamepadConfig;
    };
    constructor(controllerConfig: XRControllerConfig, handedness: XRHandedness, globalSpace: GlobalSpace);
    get gamepadConfig(): GamepadConfig;
    get profileId(): string;
    updateButtonValue(id: string, value: number): void;
    updateButtonTouch(id: string, touched: boolean): void;
    updateAxis(id: string, type: 'x-axis' | 'y-axis', value: number): void;
    updateAxes(id: string, x: number, y: number): void;
}
//# sourceMappingURL=XRController.d.ts.map