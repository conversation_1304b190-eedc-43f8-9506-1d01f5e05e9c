{"mappings": ";;AAAA;IACC,QAAQ,CAAC,UAAU,SAAS;IAM5B,KAAK,GACJ,MAAM,WAAW,KACf;QAAE,UAAU,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,UAAU,CAAA;KAAE,EAAE,CA4BrE;CACF;AIhCD;IACC;;;;;;;;;;;;OAYG;IACH,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,EAAE,OAAO,CAAC;IAChB;;OAEG;IACH,UAAU,EAAE,OAAO,CAAC;IACpB;;OAEG;IACH,IAAI,EAAE,OAAO,CAAC;IACd,UAAU,EAAE,OAAO,CAAC;IACpB;;OAEG;IACH,QAAQ,EAAE,OAAO,CAAC;CAClB;AAiBD,iBAAkB,SAAQ,iBAAiB;IAC1C,IAAI,IAAI,IAAI;IAEZ,QAAQ,CAAC,UAAU,kBAAkB;IACrC,QAAQ,CAAC,UAAU,OAAO;IAG1B,QAAQ,CAAC,eAAe;;;MAA4B;IAGpD,QAAQ,CAAC,aAAa;;;;;;;;;;;MAAkB;IAExC,QAAQ,CAAC,OAAO,SAAyB;IACzC,QAAQ,CAAC,cAAc,SAAyB;IAEhD,IAAI,yBAAmB;IACvB,MAAM,2BAAqB;IAE3B;;;;;;OAMG;IACH,QAAQ,CAAC,QAAQ,kBA6CZ;IAGL,UAAU,0BAAc;IACxB,WAAW,eAAe;IAE1B,iBAAiB,CAChB,IAAI,EAAE,IAAI,EACV,EAAE,EAAE,CAAC,GAAG,EAAE,WAAW,GAAG,IAAI,KAAK,IAAI,GACnC,UAAU;IAcb,yBAAyB,CAAC,MAAM,EAAE,MAAM,GAAG,WAAW,GAAG,iBAAiB;IAS1E,QAAQ,IAAI,OAAO;CAGnB;AAED;;;;;;;;GAQG;AACH,OAAO,MAAM,UAAiB,CAAC;AC/J/B;IACC;;OAEG;IACH,QAAQ,IAAA;IACR;;OAEG;IACH,MAAM,IAAA;IACN;;OAEG;IACH,QAAQ,IAAA;IACR;;OAEG;IACH,GAAG,IAAA;CACH;AC1BD;IACC,IAAI,SAAS;IACb,KAAK,UAAU;CACf;AAED;IACC;;OAEG;IACH,mBAAmB,yBAAyB;IAC5C;;OAEG;IACH,YAAY,iBAAiB;IAC7B;;OAEG;IACH,SAAS,eAAe;IACxB;;OAEG;IACH,UAAU,gBAAgB;IAC1B;;OAEG;IACH,OAAO,YAAY;IACnB;;OAEG;IACH,eAAe,qBAAqB;IACpC;;OAEG;IACH,cAAc,oBAAoB;IAClC;;OAEG;IACH,WAAW,iBAAiB;IAC5B;;OAEG;IACH,WAAW,iBAAiB;IAC5B;;OAEG;IACH,YAAY,kBAAkB;IAC9B;;;;;;;;OAQG;IACH,aAAa,mBAAmB;IAChC;;OAEG;IACH,MAAM,WAAW;CACjB;AAED;IACC,iBAAiB,uBAAuB;IACxC,gBAAgB,sBAAsB;CACtC;AAED;IACC,UAAU,iBAAiB;IAC3B,YAAY,oBAAoB;CAChC;AAED;IACC,MAAM,WAAW;IACjB,UAAU,gBAAgB;IAC1B,IAAI,SAAS;IACb,IAAI,QAAQ;CACZ;AAED;IACC,OAAO,YAAY;IACnB,YAAY,iBAAiB;IAC7B,KAAK,UAAU;IACf,KAAK,UAAU;CACf;AAED;IACC,SAAS,cAAc;IACvB,SAAS,cAAc;IACvB,KAAK,UAAU;IACf,MAAM,WAAW;IACjB,IAAI,SAAS,CAAE,wCAAwC;IACvD,KAAK,UAAU,CAAE,gBAAgB;IACjC,OAAO,aAAa,CAAE,4BAA4B;IAClD,UAAU,gBAAgB,CAAE,qCAAqC;IACjE,KAAK,UAAU,CAAE,uDAAuD;IACxE,MAAM,WAAW;CACjB;AE5FD;;;GAGG;AACH,oBAAoB,SAAQ,YAAY;gBAStC,MAAM,EAAE,GAAG,EACX,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,MAAM,EACZ,GAAG,EAAE,MAAM,EACM,YAAY,GAAE,MAAa;IAS7C,KAAK,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI;IA0FtC,kCAAkC;IAClC,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI;IA0BrB,KAAK,IAAI,IAAI;CAsBb;ACzKD;IACC,IAAI,EAAE,iBAAiB,CAAC;IACxB,OAAO,EAAE,GAAG,CAAC;IACb,GAAG,EAAE,MAAM,CAAC;CACZ;ACHD,0BAAiC,SAAS,SAAS,MAAM;IACxD,KAAK,EAAE,CAAC,KAAK,EAAE,UAAU,GAAG,SAAS,EAAE,CAAC,KAAK,IAAI,CAAC;CAClD;AAED,oCACC,SAAS,SAAS,MAAM,EACxB,MAAM,SAAS,gBAAgB,SAAS,CAAC,CACxC,SAAQ,aAAa,MAAM,EAAE,KAAK,CAAC;IACpC;;;;OAIG;IACH,SAAS,CAAC,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,GAAG,KAAK,GAAG,IAAI;CAMrD;AACD;;;GAGG;AACH,uBAAuB,CAAC,SAAS,MAAM,CAAE,SAAQ,KAAK;IACrD;;OAEG;gBACS,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,KAAK,GAAG,MAAM;IAWjC,IAAI,EAAE,CAAC,CAAC;CACf;AChCD,+BACC,SAAS,SAAS,MAAM,GAAG,uBAAuB,CACjD,SAAQ,gBAAgB,SAAS,CAAC;IACnC;;;;;;OAMG;IACH,KAAK,EAAE,MAAM,IAAI,CAAC;IAClB;;;;OAIG;IACH,KAAK,EAAE,CAAC,KAAK,EAAE,UAAU,GAAG,SAAS,EAAE,CAAC,KAAK,IAAI,CAAC;IAClD,eAAe,EAAE,CAAC,KAAK,EAAE,qBAAqB,KAAK,IAAI,CAAC;CACxD;AAED,QAAO,QAAQ,sBACd,cAAc,SAAS,eAAe,EACtC,SAAS,SAAS,MAAM,GAAG,KAAK,CAC/B,SAAQ,sBACT,SAAS,GAAG,uBAAuB,EACnC,cAAc,GAAG,qBAAqB,uBAAuB,GAAG,SAAS,CAAC,CAC1E;IA6BC;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM;IACd,QAAQ,EAAE,IAAI;IACrB,QAAQ,CAAC,OAAO,EAAE,GAAG;IAjCtB,SAAS,CAAC,KAAK,UAAS;IAExB;;;OAGG;IACH,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC;IACvB,YAAY,EAAE,MAAM,CAAC;IAErB,cAAc,EAAE,iBAAiB,CAAC;IAClC,WAAW,EAAE,cAAc,CAAC;IAE5B,QAAQ,KAAK,IAAI,IAAI,cAAc,CAAC;IAEpC;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;;OAGG;IACH,IAAI,IAAI,YAEP;IAED,SAAS;IACR;;OAEG;IACM,IAAI,EAAE,MAAM,EACd,QAAQ,EAAE,IAAI,EACZ,OAAO,EAAE,GAAG;IAOtB,QAAQ,CAAC,KAAK,IAAI,IAAI;IAEtB;;OAEG;IACH,QAAQ,CAAC,aAAa,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI;IAEpD;;;SAGK;IACL,QAAQ,CAAC,sBAAsB,CAAC,EAAE,EAAE,cAAc,GAAG,IAAI;CACzD;AC5ED,8BACC,SAAQ,gBAAgB,uBAAuB,GAAG,uBAAuB,CAAC,EACzE,qBAAqB,uBAAuB,GAAG,uBAAuB,CAAC;IACxE;;OAEG;IACH,IAAI,EAAE,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,CAAC;IAC9B;;OAEG;IACH,IAAI,EAAE,MAAM,IAAI,CAAC;CACjB;AAED;;GAEG;AACH,OAAO,QAAQ,qBAAsB,SAAQ,eAC5C,oBAAoB,EACpB,uBAAuB,CACvB;IACA,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,SAAS;IAC5C,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,mBAAmB,SAAmB;IAGhE,QAAQ,CAAC,QAAQ,CAAC,aAAa,EAAE,MAAM,CAAC;IACxC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC;IAE3B,IAAW,IAAI,mBAEd;gBAEW,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAmBxD,8DAA8D;IACrD,sBAAsB,CAAC,EAAE,EAAE,cAAc,GAAG,IAAI;IAoBzD;;OAEG;IAEH,uCAAuC;IACvC,KAAK,CAAC,OAAO,CAAC,EAAE;QAAE,KAAK,CAAC,EAAE,OAAO,CAAA;KAAE,GAAG,IAAI;IAoC1C,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAE3E,gCAAgC;IACzB,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,UAAQ;IAWhC,aAAa,CAAC,OAAO,EAAE,aAAa;CAoB1C;AEhKD;IACC;;OAEG;IACH,YAAY,CAAC,EAAE,QAAQ,CAAC;CACxB;AAED;IACC,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,MAAM,CAAC,EAAE,gBAAgB,CAAC;IAC1B,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,cAAc,CAAC,EAAE,cAAc,CAAC;CAChC;AAED;IACC;;;;;OAKG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;;;;OAKG;IACH,QAAQ,CAAC,EAAE,GAAG,CAAC;IACf,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,QAAQ,CAAC,EAAE,OAAO,CAAC;CACnB;AAED;IACC;;;;;OAKG;IACH,QAAQ,CAAC,EAAE,GAAG,CAAC;IACf;;OAEG;IACH,YAAY,CAAC,EAAE,QAAQ,CAAC;CACxB;ACzCD,+BAAuC,SAAQ,qBAAqB,KAAK,CAAC;IACzE;;;;;;OAMG;IACH,MAAM,EAAE,CAAC,MAAM,EAAE,WAAW,KAAK,IAAI,CAAC;IACtC;;;;OAIG;IACH,iBAAiB,EAAE,MAAM,IAAI,CAAC;CAC9B;AAED;;;GAGG;AACH,4BAA6B,SAAQ,eAAe,qBAAqB,CAAC;IAEzE,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;IAMvB;;OAEG;IACH,IAAI,IAAI,mBAEP;IAED,IAAI,WAAW,IAAI,WAAW,CAE7B;IAED,IAAI,YAAY,IAAI,WAAW,CAE9B;gBAEW,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAkBxD,8DAA8D;IACrD,sBAAsB,CAAC,EAAE,EAAE,cAAc,GAAG,IAAI;IAazD,SAAS,CAAC,YAAY,KAAA;IAOtB;;OAEG;IACH,aAAa,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI;IAmB3C;;;;;;;;;OASM;IACN,MAAM,CAAC,MAAM,CAAC,EAAE,WAAW,EAAE,OAAO,GAAE,YAAiB,GAAG,IAAI;IA4B9D;;OAEG;IAEH;;OAEG;IACH,KAAK,IAAI,IAAI;CA2Bb;AEvLD,OAAO,QAAQ,yBAA0B,SAAQ,cAAc;IAK9D,IAAW,UAAU,IAAI,MAAM,CAE9B;IAEe,sBAAsB,CAAC,EAAE,EAAE,cAAc;IAQzD,SAAS,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,EAAE,YAAY,GAAG,IAAI;IAE5D,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,WAAW,GAAG,IAAI;IAwD/B,KAAK,CAAC,OAAO,CAAC,EAAE;QAAE,KAAK,CAAC,EAAE,OAAO,CAAA;KAAE;CAanD;AInED,wBAAkB,YAAW,YAAY;IACxC;;OAEG;IACH,KAAK,CAAC,EAAE,QAAQ,CAAC;IACjB;;;OAGG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;;;OAIG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;;;;OAKG;IACH,MAAM,CAAC,EAAE,GAAG,CAAC;IACb;;;;;OAKG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,cAAc,CAAC,EAAE,cAAc,CAAC;IAChC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;IAC3D,WAAW,CAAC,EAAE,iBAAiB,CAAC;CAChC;AAID;IACC,CAAC,GAAG,EAAE,MAAM,GAAG,KACd,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,IAAI,EACd,OAAO,EAAE,GAAG,KACR,cAAc,CAAC;CACpB;AAED;IACC;;;;OAIG;IACH,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,KAAK,IAAI,CAAC;IAC3B;;OAEG;IACH,UAAU,EAAE,CAAC,cAAc,EAAE,cAAc,KAAK,IAAI,CAAC;IACrD;;OAEG;IACH,IAAI,EAAE,CAAC,eAAe,EAAE,eAAe,KAAK,IAAI,CAAC;IACjD;;OAEG;IACH,KAAK,EAAE,MAAM,IAAI,CAAC;IAClB;;OAEG;IACH,YAAY,EAAE,CAAC,SAAS,EAAE,MAAM,KAAK,IAAI,CAAC;IAC1C;;;;OAIG;IACH,KAAK,EAAE,CAAC,KAAK,EAAE,UAAU,GAAG,aAAa,EAAE,CAAC,KAAK,IAAI,CAAC;CACtD;AACD;;GAEG;AACH,iBAAkB,SAAQ,sBAAsB,aAAa,EAAE,UAAU,CAAC;IAGzE,SAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,iBAAiB,CAOhD;IAiBF;;;;;OAKG;IACH,IAAI,EAAE,WAEL;IAED,IAAI,OAAO,gBAEV;IAED,IAAI,IAAI,YAEP;IAED;;OAEG;IACH,IAAI,MAAM,WAET;IAED;;;;OAIG;IACH,IAAI,WAAW,IAAI,MAAM,CAQxB;IAED;;OAEG;IACH,IAAI,SAAS,YAEZ;IACD;;OAEG;IACH,IAAI,YAAY,YAEf;IAED;;OAEG;;IAGH;;;OAGG;gBACS,OAAO,EAAE,WAAW;IAEhC;;;;;;OAMG;gBACS,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,WAAW;IAkQ7C;;;OAGG;IAEI,YAAY,CAAC,YAAY,EAAE,MAAM,GAAG,aAAa,EAAE;IAW1D;;;;OAIG;IACH,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,GAAE,iBAAsB,GAAG,cAAc;IA4BtE;;;;;OAKG;IACH,IAAI,CACH,IAAI,EAAE,MAAM,EACZ,MAAM,EAAE,WAAW,EACnB,OAAO,GAAE,UAAe,GACtB,eAAe;IA6ClB,iBAAiB,CAAC,UAAU,EAAE,cAAc,GAAG,eAAe,GAAG,IAAI;IAerE,sDAAsD;IACtD,aAAa,CACZ,MAAM,EAAE,MAAM,EACd,YAAY,EAAE,MAAM,GAClB,IAAI,GAAG,cAAc,GAAG,eAAe;IAsC1C;;;;;;;;;OASG;IACH,OAAO,IAAI,IAAI;IAoCf;;;;;OAKG;IACH,UAAU,IAAI,IAAI;IAoBlB;;;;;;OAMG;IACH,SAAS,IAAI,IAAI;IAuBjB;;;;;OAKG;IACH,YAAY,CAAC,EAAE,IAAI,GAAG,GAAG,EAAE,SAAO,GAAG,IAAI;CAMzC;ACpuBD;;GAEG;AACH,wBAAyB,SAAQ,IAAI;IAC3B,YAAY,EAAE,iBAAiB,CAGtC;CACF;ACPD,OAAO,QAAQ,uBAAwB,SAAQ,cAAc;IAgC5D,SAAS,CAAC,MAAM,2DAA0C;IAE1D,SAAS,CAAC,cAAc,8BAQrB;IAEH,SAAS,aAAa,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAMlD,sBAAsB,CAAC,EAAE,KAAA;CAMzC;ACxDD,oBAAqB,SAAQ,gBAAgB;IAC5C,QAAQ,CAAC,aAAa,aAAa;gBAGvB,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;cAerC,KAAK,CAAC,IAAI,KAAA;CAG7B;ACAD,eAAe,IAAI,CAAC", "sources": ["lib/lib/dataconnection/BufferedConnection/binaryPackChunker.ts", "lib/lib/supports.ts", "lib/lib/utils/validateId.ts", "lib/lib/utils/randomToken.ts", "lib/lib/util.ts", "lib/lib/logger.ts", "lib/lib/enums.ts", "lib/lib/version.ts", "lib/lib/socket.ts", "lib/lib/servermessage.ts", "lib/lib/peerError.ts", "lib/lib/baseconnection.ts", "lib/lib/dataconnection/DataConnection.ts", "lib/lib/negotiator.ts", "lib/lib/optionInterfaces.ts", "lib/lib/mediaconnection.ts", "lib/lib/api.ts", "lib/lib/dataconnection/BufferedConnection/BufferedConnection.ts", "lib/lib/dataconnection/BufferedConnection/BinaryPack.ts", "lib/lib/dataconnection/BufferedConnection/Raw.ts", "lib/lib/dataconnection/BufferedConnection/Json.ts", "lib/lib/peer.ts", "lib/lib/msgPackPeer.ts", "lib/lib/dataconnection/StreamConnection/StreamConnection.ts", "lib/lib/dataconnection/StreamConnection/MsgPack.ts", "lib/lib/exports.ts", "lib/exports.ts"], "sourcesContent": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "export { util, type Util } from \"./util\";\nimport { Peer } from \"./peer\";\nimport { MsgPackPeer } from \"./msgPackPeer\";\n\nexport type { PeerEvents, PeerOptions } from \"./peer\";\n\nexport type {\n\tPeerJSOption,\n\tPeerConnectOption,\n\tAnswerOption,\n\tCallOption,\n} from \"./optionInterfaces\";\nexport type { UtilSupportsObj } from \"./util\";\nexport type { DataConnection } from \"./dataconnection/DataConnection\";\nexport type { MediaConnection } from \"./mediaconnection\";\nexport type { LogLevel } from \"./logger\";\nexport * from \"./enums\";\n\nexport { BufferedConnection } from \"./dataconnection/BufferedConnection/BufferedConnection\";\nexport { StreamConnection } from \"./dataconnection/StreamConnection/StreamConnection\";\nexport { MsgPack } from \"./dataconnection/StreamConnection/MsgPack\";\nexport type { SerializerMapping } from \"./peer\";\n\nexport { Peer, MsgPackPeer };\n\nexport { PeerError } from \"./peerError\";\nexport default Peer;\n"], "names": [], "version": 3, "file": "types.d.ts.map", "sourceRoot": "../"}