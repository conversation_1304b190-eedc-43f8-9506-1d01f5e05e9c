import*as e from"../../core/platform/platform.js";import*as t from"./helpers/helpers.js";export{t as Helpers};import*as r from"./types/types.js";export{r as Types};import*as s from"./extras/extras.js";export{s as Extras};import*as n from"./handlers/handlers.js";export{n as Handlers};import*as i from"./insights/insights.js";export{i as Insights};import*as a from"./lantern/lantern.js";export{a as Lantern};import*as o from"./root-causes/root-causes.js";export{o as RootCauses};import*as c from"../../core/sdk/sdk.js";var d=Object.freeze({__proto__:null,EntriesFilter:class{#e;#t=[];#r=[];#s=new Map;constructor(e){this.#e=e}findPossibleActions(e){const t=this.#e.get(e);if(!t)return{MERGE_FUNCTION:!1,COLLAPSE_FUNCTION:!1,COLLAPSE_REPEATING_DESCENDANTS:!1,RESET_CHILDREN:!1,UNDO_ALL_ACTIONS:!1};const r=t.parent,s=this.#n(t).filter((e=>!this.#t.includes(e))),n=this.#i(t).filter((e=>!this.#t.includes(e))),i=this.#n(t).filter((e=>this.#t.includes(e)));return{MERGE_FUNCTION:null!==r,COLLAPSE_FUNCTION:s.length>0,COLLAPSE_REPEATING_DESCENDANTS:n.length>0,RESET_CHILDREN:i.length>0,UNDO_ALL_ACTIONS:this.#t.length>0}}findHiddenDescendantsAmount(e){const t=this.#e.get(e);if(!t)return 0;return this.#n(t).filter((e=>this.invisibleEntries().includes(e))).length}invisibleEntries(){return this.#t}setHiddenAndExpandableEntries(e,t){this.#t.push(...e),this.#r.push(...t)}inEntryInvisible(e){return this.#t.includes(e)}expandableEntries(){return this.#r}applyFilterAction(t){const r=new Set;switch(t.type){case"MERGE_FUNCTION":{r.add(t.entry);const e=this.#e.get(t.entry)||null,s=e&&this.#a(e);s&&this.#o(s.entry);break}case"COLLAPSE_FUNCTION":{const e=this.#e.get(t.entry);if(!e)break;this.#n(e).forEach((e=>r.add(e))),this.#o(t.entry);break}case"COLLAPSE_REPEATING_DESCENDANTS":{const e=this.#e.get(t.entry);if(!e)break;this.#i(e).forEach((e=>r.add(e))),r.size>0&&this.#o(t.entry);break}case"UNDO_ALL_ACTIONS":this.#t=[],this.#r=[];break;case"RESET_CHILDREN":this.#c(t.entry);break;default:e.assertNever(t.type,`Unknown EntriesFilter action: ${t.type}`)}return this.#t.push(...r),this.#t}#o(e){this.#r.push(e);const t=this.#e.get(e);if(!t)return;const r=this.#n(t);r.length>0&&(this.#r=this.#r.filter((e=>!r.includes(e))))}#a(e){let t=e.parent;for(;t&&this.#t.includes(t.entry);)t=t.parent;return t}#n(e){const t=this.#s.get(e);if(t)return t;const r=[],s=[...e.children];for(;s.length>0;){const e=s.shift();if(e){r.push(e.entry);const t=this.#s.get(e);t?r.push(...t):s.push(...e.children)}}return this.#s.set(e,r),r}#i(e){const s=[...e.children],n=[],i=r.TraceEvents.isProfileCall(e.entry);for(;s.length>0;){const a=s.shift();if(a){const o=r.TraceEvents.isProfileCall(a.entry);if(i&&o){const r=e.entry,s=a.entry;t.SamplesIntegrator.SamplesIntegrator.framesAreEqual(r.callFrame,s.callFrame)&&n.push(a.entry)}else i||o||e.entry.name===a.entry.name&&n.push(a.entry);s.push(...a.children)}}return n}revealEntry(e){const t=this.#e.get(e);if(!t)return;let r=t;for(;r.parent&&!this.#r.includes(r.entry);)r=r.parent;this.#c(r.entry)}#c(e){const t=this.#e.get(e);if(!t)return;const r=this.#n(t);this.#t=this.#t.filter((e=>!r.includes(e))),this.#r=this.#r.filter((t=>!r.includes(t)&&t!==e))}isEntryExpandable(e){return this.#r.includes(e)}}});function l(e){const t=e.Meta,r=t.mainFrameId,s=e.PageLoadMetrics.metricScoresByFrameId.get(r);if(!s)throw new a.Core.LanternError("missing metric scores for main frame");const n=t.mainFrameNavigations.at(-1)?.args.data?.navigationId,i=n&&s.get(n);if(!i)throw new a.Core.LanternError("missing metric scores for specified navigation");return{timestamps:{firstContentfulPaint:(e=>{const t=i.get(e);if(!t?.event)throw new a.Core.LanternError(`missing metric: ${e}`);return t.event.ts})("FCP"),largestContentfulPaint:(e=>{const t=i.get(e);if(t?.event)return t.event.ts})("LCP")}}}function h(e){return"string"==typeof e&&(e=new URL(e)),{scheme:e.protocol.split(":")[0],host:e.hostname,securityOrigin:e.origin}}function u(e,t,r){if(void 0===r.args.data.connectionId||void 0===r.args.data.connectionReused)throw new a.Core.LanternError("Trace is too old");let s;try{s=new URL(r.args.data.url)}catch(e){return}const n=r.args.data.timing?{workerFetchStart:-1,workerRespondWithSettled:-1,...r.args.data.timing}:void 0,i=n?1e3*n.requestTime:r.args.data.syntheticData.downloadStart/1e3;let o=!1;const c=t.get(r.pid);c?.includes(r.tid)&&(o=!0),e.Workers.workerIdByThread.has(r.tid)&&(o=!0);const d=r.args.data.initiator??{type:"other"};if(r.args.data.stackTrace){const e=r.args.data.stackTrace.map((e=>({scriptId:String(e.scriptId),url:e.url,lineNumber:e.lineNumber-1,columnNumber:e.columnNumber-1,functionName:e.functionName})));d.stack={callFrames:e}}let l=r.args.data.resourceType;"xmlhttprequest"===r.args.data.initiator?.fetchType?l="XHR":"fetch"===r.args.data.initiator?.fetchType&&(l="Fetch");let u=r.args.data.decodedBodyLength??0;if("data:"===s.protocol&&0===u){const e="base64,",t=s.pathname.indexOf(e);-1!==t&&(u=atob(s.pathname.substring(t+e.length)).length)}return{rawRequest:r,requestId:r.args.data.requestId,connectionId:r.args.data.connectionId,connectionReused:r.args.data.connectionReused,url:r.args.data.url,protocol:r.args.data.protocol,parsedURL:h(s),documentURL:r.args.data.requestingFrameUrl,rendererStartTime:r.ts/1e3,networkRequestTime:i,responseHeadersEndTime:r.args.data.syntheticData.downloadStart/1e3,networkEndTime:r.args.data.syntheticData.finishTime/1e3,transferSize:r.args.data.encodedDataLength,resourceSize:u,fromDiskCache:r.args.data.syntheticData.isDiskCached,fromMemoryCache:r.args.data.syntheticData.isMemoryCached,isLinkPreload:r.args.data.isLinkPreload,finished:r.args.data.finished,failed:r.args.data.failed,statusCode:r.args.data.statusCode,initiator:d,timing:n,resourceType:l,mimeType:r.args.data.mimeType,priority:r.args.data.priority,frameId:r.args.data.frame,fromWorker:o,redirects:void 0,redirectSource:void 0,redirectDestination:void 0,initiatorRequest:void 0}}function f(e,t){if(e.redirectSource)return e.redirectSource;const r=a.Graph.PageDependencyGraph.getNetworkInitiators(e)[0];let s=t.get(r)||[];if(s=s.filter((t=>t.responseHeadersEndTime<=e.rendererStartTime&&t.finished&&!t.failed)),s.length>1){const e=s.filter((e=>e.resourceType!==a.Types.NetworkRequestTypes.Other));e.length&&(s=e)}if(s.length>1){const t=s.filter((t=>t.frameId===e.frameId));t.length&&(s=t)}if(s.length>1&&"parser"===e.initiator.type){const e=s.filter((e=>e.resourceType===a.Types.NetworkRequestTypes.Document));e.length&&(s=e)}if(s.length>1){const e=s.filter((e=>e.isLinkPreload));if(e.length){const t=s.filter((e=>!e.isLinkPreload)),r=t.every((e=>e.fromDiskCache||e.fromMemoryCache));t.length&&r&&(s=e)}}return 1===s.length?s[0]:null}function g(e,t){const r=function(e){const t=new Map,r=["ServiceWorker thread","DedicatedWorker thread"];for(const s of e.traceEvents){if("thread_name"!==s.name||!s.args.name)continue;if(!r.includes(s.args.name))continue;const e=t.get(s.pid);e?e.push(s.tid):t.set(s.pid,[s.tid])}return t}(e),s=[];for(const e of t.NetworkRequests.byTime){const n=u(t,r,e);n&&s.push(n)}for(const e of[...s]){if(!e.rawRequest)continue;const t=e.rawRequest.args.data.redirects;if(!t.length)continue;const r=[];for(const n of t){const t=structuredClone(e);t.networkRequestTime=n.ts/1e3,t.rendererStartTime=t.networkRequestTime,t.networkEndTime=(n.ts+n.dur)/1e3,t.responseHeadersEndTime=t.networkEndTime,t.timing={requestTime:t.networkRequestTime/1e3,receiveHeadersStart:t.responseHeadersEndTime,receiveHeadersEnd:t.responseHeadersEndTime,proxyStart:-1,proxyEnd:-1,dnsStart:-1,dnsEnd:-1,connectStart:-1,connectEnd:-1,sslStart:-1,sslEnd:-1,sendStart:-1,sendEnd:-1,workerStart:-1,workerReady:-1,workerFetchStart:-1,workerRespondWithSettled:-1,pushStart:-1,pushEnd:-1},t.url=n.url,t.parsedURL=h(n.url),t.statusCode=302,t.resourceType=void 0,t.transferSize=400,r.push(t),s.push(t)}r.push(e);for(let e=0;e<r.length;e++){const t=r[e];e>0&&(t.redirectSource=r[e-1],t.redirects=r.slice(0,e)),e!==r.length-1&&(t.redirectDestination=r[e+1])}for(let e=1;e<r.length;e++)r[e].requestId=`${r[e-1].requestId}:redirect`}return function(e){const t=new Map;for(const r of e){const e=t.get(r.url)||[];e.push(r),t.set(r.url,e)}for(const r of e){const e=f(r,t);e&&(r.initiatorRequest=e)}}(s),s.sort(((e,t)=>e.rendererStartTime-t.rendererStartTime))}function p(e,t,r,s){const n=function(e,t){const r=t.Meta,s=r.mainFrameNavigations.length?new Set(r.mainFrameNavigations.map((e=>e.pid))):r.topLevelRendererIds,n=new Map;for(const e of s){const t=r.threadsInProcess.get(e)??[];let s=!1;for(const[r,i]of t)if("CrRendererMain"===i.args.name){n.set(e,r),s=!0;break}if(!s)for(const[r,i]of t)if("CrBrowserMain"===i.args.name){n.set(e,r),s=!0;break}}return e.traceEvents.filter((e=>n.get(e.pid)===e.tid))}(t,r);if(!s){s={requestedUrl:e[0].url,mainDocumentUrl:""};let t=e[0];for(;t.redirectDestination;)t=t.redirectDestination;s.mainDocumentUrl=t.url}return a.Graph.PageDependencyGraph.createGraph(n,e,s)}var m=Object.freeze({__proto__:null,createProcessedNavigation:l,createNetworkRequests:g,createGraph:p});class E extends Event{data;static eventName="traceparseprogress";constructor(e,t={bubbles:!0}){super(E.eventName,t),this.data=e}}class v extends EventTarget{#d;#l="IDLE";#h=r.Configuration.defaults();#u=null;#f=null;static createWithAllHandlers(){return new v(n.ModelHandlers,r.Configuration.defaults())}static getEnabledInsightRunners(e){const t={};for(const[r,s]of Object.entries(i.InsightRunners)){s.deps().some((t=>!e[t]))||Object.assign(t,{[r]:s})}return t}constructor(e,t){super(),this.#g(e),this.#d={Meta:n.ModelHandlers.Meta,...e},t&&(this.#h=t),this.#p()}#p(){for(const e of Object.values(this.#d))"handleUserConfig"in e&&e.handleUserConfig&&e.handleUserConfig(this.#h)}#g(e){if(Object.keys(e).length===Object.keys(n.ModelHandlers).length)return;const t=new Set;for(const[r,s]of Object.entries(e)){t.add(r);const e="deps"in s?s.deps():[];for(const r of e)t.add(r)}const r=new Set(Object.keys(e));t.delete("Meta");for(const e of t)if(!r.has(e))throw new Error(`Required handler ${e} not provided.`)}reset(){if("PARSING"===this.#l)throw new Error("Trace processor can't reset while parsing.");const e=Object.values(this.#d);for(const t of e)t.reset();this.#u=null,this.#f=null,this.#l="IDLE"}async parse(e,t=!1){if("IDLE"!==this.#l)throw new Error(`Trace processor can't start parsing when not idle. Current state: ${this.#l}`);try{this.#l="PARSING",await this.#m(e,t),this.#u&&this.#E(this.#u,e),this.#l="FINISHED_PARSING"}catch(e){throw this.#l="ERRORED_WHILE_PARSING",e}}async#m(e,t){const r=[...y(this.#d).values()];for(const e of r)e.reset();for(const e of r)e.initialize?.(t);for(let t=0;t<e.length;++t){t%5e4==0&&t&&(this.dispatchEvent(new E({index:t,total:e.length})),await new Promise((e=>setTimeout(e,0))));const s=e[t];for(let e=0;e<r.length;++e)r[e].handleEvent(s)}for(const e of r)e.finalize&&(await new Promise((e=>setTimeout(e,0))),await e.finalize());const s=(e,t=!0)=>{if(e instanceof Map)return new Map(e);if(e instanceof Set)return new Set(e);if(Array.isArray(e))return[...e];if("object"==typeof e&&e&&t){const t={};for(const[r,n]of Object.entries(e))t[r]=s(n,!1);return t}return e},n={};for(const[e,t]of Object.entries(this.#d)){const r=s(t.data());Object.assign(n,{[e]:r})}this.#u=n}get traceParsedData(){return"FINISHED_PARSING"!==this.#l?null:this.#u}get insights(){return"FINISHED_PARSING"!==this.#l?null:this.#f}#v(e,t){if(!e.NetworkRequests||!e.Workers||!e.PageLoadMetrics)return;if(!e.NetworkRequests.byTime.length)throw new a.Core.LanternError("No network requests found in trace");const r={traceEvents:t},s=g(r,e),n=p(s,r,e),i=l(e),o=a.Core.NetworkAnalyzer.analyze(s),c=a.Simulation.Simulator.createSimulator({networkAnalysis:o,throttlingMethod:"simulate"}),d={graph:n,simulator:c,processedNavigation:i},h=a.Metrics.FirstContentfulPaint.compute(d),u=a.Metrics.LargestContentfulPaint.compute(d,{fcpResult:h}),f=a.Metrics.Interactive.compute(d,{lcpResult:u});return{graph:n,simulator:c,metrics:{firstContentfulPaint:h,interactive:f,largestContentfulPaint:u,totalBlockingTime:a.Metrics.TotalBlockingTime.compute(d,{fcpResult:h,interactiveResult:f})}}}#E(e,t){this.#f=new Map;const r=v.getEnabledInsightRunners(e);let s;try{s=this.#v(e,t)}catch(e){const t=["mainDocumentRequest not found","missing metric scores for main frame","missing metric: FCP","missing metric: LCP","No network requests found in trace","Trace is too old"];e instanceof a.Core.LanternError?t.some((t=>e.message===t))||console.error(e.message):console.error(e)}for(const t of e.Meta.mainFrameNavigations){if(!t.args.frame||!t.args.data?.navigationId)continue;const n={frameId:t.args.frame,navigationId:t.args.data.navigationId,lantern:s},i={};for(const[t,s]of Object.entries(r)){let r;try{r=s.generateInsight(e,n)}catch(e){r=e}Object.assign(i,{[t]:r})}this.#f.set(n.navigationId,i)}}}function y(e){const t=new Map,r=new Set,s=n=>{if(t.has(n))return;if(r.has(n)){let e="";for(const t of r)(e||t===n)&&(e+=`${t}->`);throw e+=n,new Error(`Found dependency cycle in trace event handlers: ${e}`)}r.add(n);const i=e[n];if(!i)return;const a=i.deps?.();a&&a.forEach(s),t.set(n,i)};for(const t of Object.keys(e))s(t);return t}var T=Object.freeze({__proto__:null,TraceParseProgressEvent:E,TraceProcessor:v,sortHandlers:y});class b extends EventTarget{#y=[];#T=[];#b=new Map;#C=[];#w=0;#N;#S=r.Configuration.defaults();static createWithAllHandlers(e){return new b(n.ModelHandlers,e)}static createWithSubsetOfHandlers(e,t){return new b(e,t)}constructor(e,t){super(),t&&(this.#S=t),this.#N=new v(e,this.#S)}async parse(e,r){const s=r?.metadata||{},n=r?.isFreshRecording||!1,i=e=>{const{data:t}=e;this.dispatchEvent(new C({type:"PROGRESS_UPDATE",data:t}))};this.#N.addEventListener(E.eventName,i);const a={traceEvents:e,metadata:s,traceParsedData:null,traceInsights:null};try{const r=t.SyntheticEvents.SyntheticEventsManager.initAndActivate(e);await this.#N.parse(e,n),this.#R(a,this.#N.traceParsedData,this.#N.insights),this.#y.push(a),this.#T.push(r)}catch(e){throw e}finally{this.#N.removeEventListener(E.eventName,i),this.dispatchEvent(new C({type:"COMPLETE",data:"done"}))}}#R(r,s,n){r.traceParsedData=s,r.traceInsights=n,this.#w++;let i=`Trace ${this.#w}`,a=null;if(r.traceParsedData&&(a=t.Trace.extractOriginFromTrace(r.traceParsedData.Meta.mainFrameURL),a)){const t=e.MapUtilities.getWithDefault(this.#b,a,(()=>1));i=`${a} (${t})`,this.#b.set(a,t+1)}this.#C.push(i)}lastTraceIndex(){return this.size()-1}traceParsedData(e=this.#y.length-1){return this.#y[e]?this.#y[e].traceParsedData:null}traceInsights(e=this.#y.length-1){return this.#y[e]?this.#y[e].traceInsights:null}metadata(e=this.#y.length-1){return this.#y[e]?this.#y[e].metadata:null}overrideModifications(e,t){this.#y[e]&&(this.#y[e].metadata.modifications=t)}rawTraceEvents(e=this.#y.length-1){return this.#y[e]?this.#y[e].traceEvents:null}syntheticTraceEventsManager(e=this.#y.length-1){return this.#T[e]?this.#T[e]:null}size(){return this.#y.length}deleteTraceByIndex(e){this.#y.splice(e,1),this.#C.splice(e,1)}getRecordingsAvailable(){return this.#C}resetProcessor(){this.#N.reset()}}class C extends Event{data;static eventName="modelupdate";constructor(e){super(C.eventName),this.data=e}}var w=Object.freeze({__proto__:null,Model:b,ModelUpdateEvent:C,isModelUpdateDataComplete:function(e){return"COMPLETE"===e.type},isModelUpdateDataProgress:function(e){return"PROGRESS_UPDATE"===e.type}});class N extends c.SDKModel.SDKModel{#D;#I;#P;#M;#k;constructor(e){super(e),this.#D=e.tracingAgent(),e.registerTracingDispatcher(new S(this)),this.#I=null,this.#P=0,this.#M=0}bufferUsage(e,t,r){this.#P=void 0===t?null:t,this.#I&&this.#I.tracingBufferUsage(e||r||0)}eventsCollected(e){this.#I&&(this.#I.traceEventsCollected(e),this.#M+=e.length,this.#P?(this.#M>this.#P&&(this.#M=this.#P),this.#I.eventsRetrievalProgress(this.#M/this.#P)):this.#I.eventsRetrievalProgress(0))}tracingComplete(){this.#P=0,this.#M=0,this.#I&&(this.#I.tracingComplete(),this.#I=null),this.#k=!1}async reset(){this.#I&&await this.#D.invoke_end(),this.#P=0,this.#M=0,this.#I=null,this.#k=!1}async start(e,t,r){if(this.#I)throw new Error("Tracing is already started");this.#I=e;const s={bufferUsageReportingInterval:500,categories:t,options:r,transferMode:"ReportEvents"},n=await this.#D.invoke_start(s);return n.getError()&&(this.#I=null),n}stop(){if(!this.#I)throw new Error("Tracing is not started");if(this.#k)throw new Error("Tracing is already being stopped");this.#k=!0,this.#D.invoke_end()}}class S{#A;constructor(e){this.#A=e}bufferUsage({value:e,eventCount:t,percentFull:r}){this.#A.bufferUsage(e,t,r)}dataCollected({value:e}){this.#A.eventsCollected(e)}tracingComplete(){this.#A.tracingComplete()}}c.SDKModel.SDKModel.register(N,{capabilities:128,autostart:!1});var R=Object.freeze({__proto__:null,TracingManager:N});export{d as EntriesFilter,m as LanternComputationData,T as Processor,w as TraceModel,R as TracingManager};
