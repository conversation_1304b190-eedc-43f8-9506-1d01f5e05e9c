import { ColorRepresentation } from 'three';
import { Axis } from '../../state.js';
import { HandlesContext } from '../context.js';
import { HandlesProperties } from '../index.js';
import { RegisteredHandle } from '../registered.js';
export declare class PivotAxisScaleHandle extends RegisteredHandle {
    constructor(context: HandlesContext, axis: Axis, tagPrefix: string);
    bind(defaultColor: ColorRepresentation, config?: HandlesProperties): (() => void) | undefined;
}
