'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useRouter } from 'next/navigation'

export default function VerificationPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    // 基础信息
    realName: '',
    idNumber: '',
    phone: '',
    email: '',
    // 高级认证
    bankCard: '',
    address: '',
    // 企业认证
    companyName: '',
    businessLicense: '',
    legalPerson: '',
    taxId: ''
  })
  const [verificationType, setVerificationType] = useState<'personal' | 'enterprise'>('personal')
  const [uploadedFiles, setUploadedFiles] = useState<{[key: string]: File | null}>({
    idCardFront: null,
    idCardBack: null,
    businessLicense: null,
    bankCard: null
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const steps = [
    { id: 1, title: '选择认证类型', icon: '📋' },
    { id: 2, title: '填写基本信息', icon: '✏️' },
    { id: 3, title: '上传证件照片', icon: '📷' },
    { id: 4, title: '人脸识别验证', icon: '👤' },
    { id: 5, title: '提交审核', icon: '✅' }
  ]

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleFileUpload = (field: string, file: File) => {
    setUploadedFiles(prev => ({ ...prev, [field]: file }))
  }

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    try {
      // 模拟提交认证信息
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 保存认证状态到本地存储
      localStorage.setItem(`realname-user-001`, 'verified')
      localStorage.setItem('verification-completed', 'true')
      
      alert('实名认证提交成功！我们将在1-3个工作日内完成审核。')
      router.push('/profile')
    } catch (error) {
      console.error('认证提交失败:', error)
      alert('提交失败，请稍后重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 text-white">
      {/* 头部 */}
      <div className="sticky top-0 z-20 bg-black/20 backdrop-blur-lg border-b border-white/10">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <button onClick={() => router.back()} className="text-white">
              ← 返回
            </button>
            <h1 className="text-xl font-bold">实名认证</h1>
            <div className="w-8"></div>
          </div>
        </div>
      </div>

      {/* 进度条 */}
      <div className="px-4 py-6">
        <div className="flex items-center justify-between mb-4">
          {steps.map((step, index) => (
            <div key={step.id} className="flex flex-col items-center">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold ${
                currentStep >= step.id 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-600 text-gray-400'
              }`}>
                {currentStep > step.id ? '✓' : step.icon}
              </div>
              <span className="text-xs mt-2 text-center">{step.title}</span>
              {index < steps.length - 1 && (
                <div className={`w-full h-1 mt-2 ${
                  currentStep > step.id ? 'bg-blue-500' : 'bg-gray-600'
                }`}></div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 内容区域 */}
      <div className="px-4 pb-8">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20"
          >
            {/* 步骤1：选择认证类型 */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-center mb-6">选择认证类型</h2>
                
                <div className="space-y-4">
                  <button
                    onClick={() => setVerificationType('personal')}
                    className={`w-full p-6 rounded-xl border-2 transition-all ${
                      verificationType === 'personal'
                        ? 'border-blue-500 bg-blue-500/20'
                        : 'border-white/20 bg-white/5 hover:bg-white/10'
                    }`}
                  >
                    <div className="flex items-center space-x-4">
                      <div className="text-4xl">👤</div>
                      <div className="text-left">
                        <h3 className="text-lg font-bold">个人认证</h3>
                        <p className="text-gray-400 text-sm">适用于个人创作者和自由职业者</p>
                        <div className="text-green-400 text-xs mt-1">
                          ✓ 基础功能 ✓ 内容发布 ✓ 技能服务
                        </div>
                      </div>
                    </div>
                  </button>

                  <button
                    onClick={() => setVerificationType('enterprise')}
                    className={`w-full p-6 rounded-xl border-2 transition-all ${
                      verificationType === 'enterprise'
                        ? 'border-blue-500 bg-blue-500/20'
                        : 'border-white/20 bg-white/5 hover:bg-white/10'
                    }`}
                  >
                    <div className="flex items-center space-x-4">
                      <div className="text-4xl">🏢</div>
                      <div className="text-left">
                        <h3 className="text-lg font-bold">企业认证</h3>
                        <p className="text-gray-400 text-sm">适用于企业和机构用户</p>
                        <div className="text-green-400 text-xs mt-1">
                          ✓ 全部功能 ✓ 企业服务 ✓ 批量管理
                        </div>
                      </div>
                    </div>
                  </button>
                </div>

                <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                  <div className="flex items-start space-x-2">
                    <span className="text-yellow-400 text-sm">⚠️</span>
                    <div className="text-yellow-200 text-xs">
                      <p className="font-medium mb-1">认证须知：</p>
                      <p>• 实名认证是使用平台服务的必要条件</p>
                      <p>• 认证信息将严格保密，仅用于身份验证</p>
                      <p>• 提供虚假信息将导致账户被永久封禁</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 步骤2：填写基本信息 */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-center mb-6">填写基本信息</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-white text-sm font-medium mb-2">真实姓名 *</label>
                    <input
                      type="text"
                      value={formData.realName}
                      onChange={(e) => handleInputChange('realName', e.target.value)}
                      placeholder="请输入真实姓名"
                      className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400"
                    />
                  </div>

                  <div>
                    <label className="block text-white text-sm font-medium mb-2">身份证号 *</label>
                    <input
                      type="text"
                      value={formData.idNumber}
                      onChange={(e) => handleInputChange('idNumber', e.target.value)}
                      placeholder="请输入18位身份证号"
                      className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400"
                    />
                  </div>

                  <div>
                    <label className="block text-white text-sm font-medium mb-2">手机号码 *</label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder="请输入手机号码"
                      className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400"
                    />
                  </div>

                  <div>
                    <label className="block text-white text-sm font-medium mb-2">邮箱地址</label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="请输入邮箱地址"
                      className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400"
                    />
                  </div>

                  {verificationType === 'enterprise' && (
                    <>
                      <div>
                        <label className="block text-white text-sm font-medium mb-2">企业名称 *</label>
                        <input
                          type="text"
                          value={formData.companyName}
                          onChange={(e) => handleInputChange('companyName', e.target.value)}
                          placeholder="请输入企业全称"
                          className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400"
                        />
                      </div>

                      <div>
                        <label className="block text-white text-sm font-medium mb-2">统一社会信用代码 *</label>
                        <input
                          type="text"
                          value={formData.taxId}
                          onChange={(e) => handleInputChange('taxId', e.target.value)}
                          placeholder="请输入统一社会信用代码"
                          className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400"
                        />
                      </div>

                      <div>
                        <label className="block text-white text-sm font-medium mb-2">法定代表人 *</label>
                        <input
                          type="text"
                          value={formData.legalPerson}
                          onChange={(e) => handleInputChange('legalPerson', e.target.value)}
                          placeholder="请输入法定代表人姓名"
                          className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400"
                        />
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}

            {/* 步骤3：上传证件照片 */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-center mb-6">上传证件照片</h2>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="border-2 border-dashed border-white/30 rounded-lg p-6 text-center">
                      <div className="text-4xl mb-2">📄</div>
                      <p className="text-sm text-gray-400 mb-2">身份证正面</p>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => e.target.files && handleFileUpload('idCardFront', e.target.files[0])}
                        className="hidden"
                        id="idCardFront"
                      />
                      <label htmlFor="idCardFront" className="text-blue-400 text-xs cursor-pointer">
                        点击上传
                      </label>
                    </div>

                    <div className="border-2 border-dashed border-white/30 rounded-lg p-6 text-center">
                      <div className="text-4xl mb-2">📄</div>
                      <p className="text-sm text-gray-400 mb-2">身份证背面</p>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => e.target.files && handleFileUpload('idCardBack', e.target.files[0])}
                        className="hidden"
                        id="idCardBack"
                      />
                      <label htmlFor="idCardBack" className="text-blue-400 text-xs cursor-pointer">
                        点击上传
                      </label>
                    </div>
                  </div>

                  {verificationType === 'enterprise' && (
                    <div className="border-2 border-dashed border-white/30 rounded-lg p-6 text-center">
                      <div className="text-4xl mb-2">🏢</div>
                      <p className="text-sm text-gray-400 mb-2">营业执照</p>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => e.target.files && handleFileUpload('businessLicense', e.target.files[0])}
                        className="hidden"
                        id="businessLicense"
                      />
                      <label htmlFor="businessLicense" className="text-blue-400 text-xs cursor-pointer">
                        点击上传
                      </label>
                    </div>
                  )}

                  <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                    <div className="text-blue-200 text-xs">
                      <p className="font-medium mb-1">上传要求：</p>
                      <p>• 图片格式：JPG、PNG</p>
                      <p>• 图片大小：不超过5MB</p>
                      <p>• 图片清晰，四角完整，无遮挡</p>
                      <p>• 证件信息清晰可见</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 步骤4：人脸识别验证 */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-center mb-6">人脸识别验证</h2>
                
                <div className="text-center">
                  <div className="w-48 h-48 mx-auto bg-white/10 rounded-full flex items-center justify-center mb-6">
                    <div className="text-6xl">👤</div>
                  </div>
                  
                  <p className="text-gray-400 mb-6">
                    请确保光线充足，面部清晰可见，按照提示完成人脸识别验证
                  </p>
                  
                  <button className="w-full py-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl text-white font-medium mb-4">
                    开始人脸识别
                  </button>
                  
                  <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                    <div className="text-green-200 text-xs">
                      <p className="font-medium mb-1">验证说明：</p>
                      <p>• 请正对摄像头，保持面部居中</p>
                      <p>• 按照语音提示完成指定动作</p>
                      <p>• 验证过程约需30秒</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 步骤5：提交审核 */}
            {currentStep === 5 && (
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-center mb-6">确认提交</h2>
                
                <div className="space-y-4">
                  <div className="bg-white/5 rounded-lg p-4">
                    <h3 className="text-lg font-semibold mb-3">认证信息确认</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">认证类型：</span>
                        <span>{verificationType === 'personal' ? '个人认证' : '企业认证'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">真实姓名：</span>
                        <span>{formData.realName}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">手机号码：</span>
                        <span>{formData.phone}</span>
                      </div>
                      {verificationType === 'enterprise' && (
                        <div className="flex justify-between">
                          <span className="text-gray-400">企业名称：</span>
                          <span>{formData.companyName}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                    <div className="text-yellow-200 text-xs">
                      <p className="font-medium mb-1">审核说明：</p>
                      <p>• 审核时间：1-3个工作日</p>
                      <p>• 审核结果将通过短信和站内信通知</p>
                      <p>• 如有问题，客服将主动联系您</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input type="checkbox" id="agree" className="rounded" />
                    <label htmlFor="agree" className="text-sm text-gray-300">
                      我已阅读并同意 <span className="text-blue-400">《用户协议》</span> 和 <span className="text-blue-400">《隐私政策》</span>
                    </label>
                  </div>
                </div>
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex space-x-4 mt-8">
              {currentStep > 1 && (
                <button
                  onClick={handlePrevious}
                  className="flex-1 py-3 bg-gray-700 rounded-xl text-white font-medium"
                >
                  上一步
                </button>
              )}
              
              {currentStep < steps.length ? (
                <button
                  onClick={handleNext}
                  className="flex-1 py-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl text-white font-medium"
                >
                  下一步
                </button>
              ) : (
                <button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="flex-1 py-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl text-white font-medium disabled:opacity-50"
                >
                  {isSubmitting ? '提交中...' : '提交审核'}
                </button>
              )}
            </div>
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  )
}
