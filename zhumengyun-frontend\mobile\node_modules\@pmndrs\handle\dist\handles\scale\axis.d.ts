import { ColorRepresentation } from 'three';
import { Axis } from '../../state.js';
import { HandlesContext } from '../context.js';
import { HandlesProperties } from '../index.js';
import { RegisteredHandle } from '../registered.js';
export declare class AxisScaleHandle extends RegisteredHandle {
    private readonly invert;
    private readonly showHandleLine;
    constructor(context: HandlesContext, axis: Axis, tagPrefix?: string, invert?: boolean, showHandleLine?: boolean);
    bind(defaultColor: ColorRepresentation, defaultHoverColor: ColorRepresentation, config?: HandlesProperties): (() => void) | undefined;
}
