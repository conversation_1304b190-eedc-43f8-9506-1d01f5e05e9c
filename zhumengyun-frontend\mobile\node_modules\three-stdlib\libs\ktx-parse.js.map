{"version": 3, "file": "ktx-parse.js", "sources": ["../../src/libs/ktx-parse.js"], "sourcesContent": ["///////////////////////////////////////////////////\n// KTX2 Header.\n///////////////////////////////////////////////////\nconst KHR_SUPERCOMPRESSION_NONE = 0\nconst KHR_SUPERCOMPRESSION_BASISLZ = 1\nconst KHR_SUPERCOMPRESSION_ZSTD = 2\nconst KHR_SUPERCOMPRESSION_ZLIB = 3 ///////////////////////////////////////////////////\n// Data Format Descriptor (DFD).\n///////////////////////////////////////////////////\n\nconst KHR_DF_KHR_DESCRIPTORTYPE_BASICFORMAT = 0\nconst KHR_DF_VENDORID_KHRONOS = 0\nconst KHR_DF_VERSION = 2\nconst KHR_DF_MODEL_UNSPECIFIED = 0\nconst KHR_DF_MODEL_RGBSDA = 1 // ...\n\nconst KHR_DF_MODEL_ETC1 = 160\nconst KHR_DF_MODEL_ETC2 = 161\nconst KHR_DF_MODEL_ASTC = 162\nconst KHR_DF_MODEL_ETC1S = 163\nconst KHR_DF_MODEL_UASTC = 166\nconst KHR_DF_FLAG_ALPHA_STRAIGHT = 0\nconst KHR_DF_FLAG_ALPHA_PREMULTIPLIED = 1\nconst KHR_DF_TRANSFER_UNSPECIFIED = 0\nconst KHR_DF_TRANSFER_LINEAR = 1\nconst KHR_DF_TRANSFER_SRGB = 2\nconst KHR_DF_TRANSFER_ITU = 3\nconst KHR_DF_TRANSFER_NTSC = 4\nconst KHR_DF_TRANSFER_SLOG = 5\nconst KHR_DF_TRANSFER_SLOG2 = 6\nconst KHR_DF_TRANSFER_BT1886 = 7\nconst KHR_DF_TRANSFER_HLG_OETF = 8\nconst KHR_DF_TRANSFER_HLG_EOTF = 9\nconst KHR_DF_TRANSFER_PQ_EOTF = 10\nconst KHR_DF_TRANSFER_PQ_OETF = 11\nconst KHR_DF_TRANSFER_DCIP3 = 12\nconst KHR_DF_TRANSFER_PAL_OETF = 13\nconst KHR_DF_TRANSFER_PAL625_EOTF = 14\nconst KHR_DF_TRANSFER_ST240 = 15\nconst KHR_DF_TRANSFER_ACESCC = 16\nconst KHR_DF_TRANSFER_ACESCCT = 17\nconst KHR_DF_TRANSFER_ADOBERGB = 18\nconst KHR_DF_PRIMARIES_UNSPECIFIED = 0\nconst KHR_DF_PRIMARIES_BT709 = 1\nconst KHR_DF_PRIMARIES_BT601_EBU = 2\nconst KHR_DF_PRIMARIES_BT601_SMPTE = 3\nconst KHR_DF_PRIMARIES_BT2020 = 4\nconst KHR_DF_PRIMARIES_CIEXYZ = 5\nconst KHR_DF_PRIMARIES_ACES = 6\nconst KHR_DF_PRIMARIES_ACESCC = 7\nconst KHR_DF_PRIMARIES_NTSC1953 = 8\nconst KHR_DF_PRIMARIES_PAL525 = 9\nconst KHR_DF_PRIMARIES_DISPLAYP3 = 10\nconst KHR_DF_PRIMARIES_ADOBERGB = 11\nconst KHR_DF_CHANNEL_RGBSDA_RED = 0\nconst KHR_DF_CHANNEL_RGBSDA_GREEN = 1\nconst KHR_DF_CHANNEL_RGBSDA_BLUE = 2\nconst KHR_DF_CHANNEL_RGBSDA_STENCIL = 13\nconst KHR_DF_CHANNEL_RGBSDA_DEPTH = 14\nconst KHR_DF_CHANNEL_RGBSDA_ALPHA = 15\nconst KHR_DF_SAMPLE_DATATYPE_FLOAT = 0x80\nconst KHR_DF_SAMPLE_DATATYPE_SIGNED = 0x40\nconst KHR_DF_SAMPLE_DATATYPE_EXPONENT = 0x20\nconst KHR_DF_SAMPLE_DATATYPE_LINEAR = 0x10 ///////////////////////////////////////////////////\n// VK FORMAT.\n///////////////////////////////////////////////////\n\nconst VK_FORMAT_UNDEFINED = 0\nconst VK_FORMAT_R4G4_UNORM_PACK8 = 1\nconst VK_FORMAT_R4G4B4A4_UNORM_PACK16 = 2\nconst VK_FORMAT_B4G4R4A4_UNORM_PACK16 = 3\nconst VK_FORMAT_R5G6B5_UNORM_PACK16 = 4\nconst VK_FORMAT_B5G6R5_UNORM_PACK16 = 5\nconst VK_FORMAT_R5G5B5A1_UNORM_PACK16 = 6\nconst VK_FORMAT_B5G5R5A1_UNORM_PACK16 = 7\nconst VK_FORMAT_A1R5G5B5_UNORM_PACK16 = 8\nconst VK_FORMAT_R8_UNORM = 9\nconst VK_FORMAT_R8_SNORM = 10\nconst VK_FORMAT_R8_UINT = 13\nconst VK_FORMAT_R8_SINT = 14\nconst VK_FORMAT_R8_SRGB = 15\nconst VK_FORMAT_R8G8_UNORM = 16\nconst VK_FORMAT_R8G8_SNORM = 17\nconst VK_FORMAT_R8G8_UINT = 20\nconst VK_FORMAT_R8G8_SINT = 21\nconst VK_FORMAT_R8G8_SRGB = 22\nconst VK_FORMAT_R8G8B8_UNORM = 23\nconst VK_FORMAT_R8G8B8_SNORM = 24\nconst VK_FORMAT_R8G8B8_UINT = 27\nconst VK_FORMAT_R8G8B8_SINT = 28\nconst VK_FORMAT_R8G8B8_SRGB = 29\nconst VK_FORMAT_B8G8R8_UNORM = 30\nconst VK_FORMAT_B8G8R8_SNORM = 31\nconst VK_FORMAT_B8G8R8_UINT = 34\nconst VK_FORMAT_B8G8R8_SINT = 35\nconst VK_FORMAT_B8G8R8_SRGB = 36\nconst VK_FORMAT_R8G8B8A8_UNORM = 37\nconst VK_FORMAT_R8G8B8A8_SNORM = 38\nconst VK_FORMAT_R8G8B8A8_UINT = 41\nconst VK_FORMAT_R8G8B8A8_SINT = 42\nconst VK_FORMAT_R8G8B8A8_SRGB = 43\nconst VK_FORMAT_B8G8R8A8_UNORM = 44\nconst VK_FORMAT_B8G8R8A8_SNORM = 45\nconst VK_FORMAT_B8G8R8A8_UINT = 48\nconst VK_FORMAT_B8G8R8A8_SINT = 49\nconst VK_FORMAT_B8G8R8A8_SRGB = 50\nconst VK_FORMAT_A2R10G10B10_UNORM_PACK32 = 58\nconst VK_FORMAT_A2R10G10B10_SNORM_PACK32 = 59\nconst VK_FORMAT_A2R10G10B10_UINT_PACK32 = 62\nconst VK_FORMAT_A2R10G10B10_SINT_PACK32 = 63\nconst VK_FORMAT_A2B10G10R10_UNORM_PACK32 = 64\nconst VK_FORMAT_A2B10G10R10_SNORM_PACK32 = 65\nconst VK_FORMAT_A2B10G10R10_UINT_PACK32 = 68\nconst VK_FORMAT_A2B10G10R10_SINT_PACK32 = 69\nconst VK_FORMAT_R16_UNORM = 70\nconst VK_FORMAT_R16_SNORM = 71\nconst VK_FORMAT_R16_UINT = 74\nconst VK_FORMAT_R16_SINT = 75\nconst VK_FORMAT_R16_SFLOAT = 76\nconst VK_FORMAT_R16G16_UNORM = 77\nconst VK_FORMAT_R16G16_SNORM = 78\nconst VK_FORMAT_R16G16_UINT = 81\nconst VK_FORMAT_R16G16_SINT = 82\nconst VK_FORMAT_R16G16_SFLOAT = 83\nconst VK_FORMAT_R16G16B16_UNORM = 84\nconst VK_FORMAT_R16G16B16_SNORM = 85\nconst VK_FORMAT_R16G16B16_UINT = 88\nconst VK_FORMAT_R16G16B16_SINT = 89\nconst VK_FORMAT_R16G16B16_SFLOAT = 90\nconst VK_FORMAT_R16G16B16A16_UNORM = 91\nconst VK_FORMAT_R16G16B16A16_SNORM = 92\nconst VK_FORMAT_R16G16B16A16_UINT = 95\nconst VK_FORMAT_R16G16B16A16_SINT = 96\nconst VK_FORMAT_R16G16B16A16_SFLOAT = 97\nconst VK_FORMAT_R32_UINT = 98\nconst VK_FORMAT_R32_SINT = 99\nconst VK_FORMAT_R32_SFLOAT = 100\nconst VK_FORMAT_R32G32_UINT = 101\nconst VK_FORMAT_R32G32_SINT = 102\nconst VK_FORMAT_R32G32_SFLOAT = 103\nconst VK_FORMAT_R32G32B32_UINT = 104\nconst VK_FORMAT_R32G32B32_SINT = 105\nconst VK_FORMAT_R32G32B32_SFLOAT = 106\nconst VK_FORMAT_R32G32B32A32_UINT = 107\nconst VK_FORMAT_R32G32B32A32_SINT = 108\nconst VK_FORMAT_R32G32B32A32_SFLOAT = 109\nconst VK_FORMAT_R64_UINT = 110\nconst VK_FORMAT_R64_SINT = 111\nconst VK_FORMAT_R64_SFLOAT = 112\nconst VK_FORMAT_R64G64_UINT = 113\nconst VK_FORMAT_R64G64_SINT = 114\nconst VK_FORMAT_R64G64_SFLOAT = 115\nconst VK_FORMAT_R64G64B64_UINT = 116\nconst VK_FORMAT_R64G64B64_SINT = 117\nconst VK_FORMAT_R64G64B64_SFLOAT = 118\nconst VK_FORMAT_R64G64B64A64_UINT = 119\nconst VK_FORMAT_R64G64B64A64_SINT = 120\nconst VK_FORMAT_R64G64B64A64_SFLOAT = 121\nconst VK_FORMAT_B10G11R11_UFLOAT_PACK32 = 122\nconst VK_FORMAT_E5B9G9R9_UFLOAT_PACK32 = 123\nconst VK_FORMAT_D16_UNORM = 124\nconst VK_FORMAT_X8_D24_UNORM_PACK32 = 125\nconst VK_FORMAT_D32_SFLOAT = 126\nconst VK_FORMAT_S8_UINT = 127\nconst VK_FORMAT_D16_UNORM_S8_UINT = 128\nconst VK_FORMAT_D24_UNORM_S8_UINT = 129\nconst VK_FORMAT_D32_SFLOAT_S8_UINT = 130\nconst VK_FORMAT_BC1_RGB_UNORM_BLOCK = 131\nconst VK_FORMAT_BC1_RGB_SRGB_BLOCK = 132\nconst VK_FORMAT_BC1_RGBA_UNORM_BLOCK = 133\nconst VK_FORMAT_BC1_RGBA_SRGB_BLOCK = 134\nconst VK_FORMAT_BC2_UNORM_BLOCK = 135\nconst VK_FORMAT_BC2_SRGB_BLOCK = 136\nconst VK_FORMAT_BC3_UNORM_BLOCK = 137\nconst VK_FORMAT_BC3_SRGB_BLOCK = 138\nconst VK_FORMAT_BC4_UNORM_BLOCK = 139\nconst VK_FORMAT_BC4_SNORM_BLOCK = 140\nconst VK_FORMAT_BC5_UNORM_BLOCK = 141\nconst VK_FORMAT_BC5_SNORM_BLOCK = 142\nconst VK_FORMAT_BC6H_UFLOAT_BLOCK = 143\nconst VK_FORMAT_BC6H_SFLOAT_BLOCK = 144\nconst VK_FORMAT_BC7_UNORM_BLOCK = 145\nconst VK_FORMAT_BC7_SRGB_BLOCK = 146\nconst VK_FORMAT_ETC2_R8G8B8_UNORM_BLOCK = 147\nconst VK_FORMAT_ETC2_R8G8B8_SRGB_BLOCK = 148\nconst VK_FORMAT_ETC2_R8G8B8A1_UNORM_BLOCK = 149\nconst VK_FORMAT_ETC2_R8G8B8A1_SRGB_BLOCK = 150\nconst VK_FORMAT_ETC2_R8G8B8A8_UNORM_BLOCK = 151\nconst VK_FORMAT_ETC2_R8G8B8A8_SRGB_BLOCK = 152\nconst VK_FORMAT_EAC_R11_UNORM_BLOCK = 153\nconst VK_FORMAT_EAC_R11_SNORM_BLOCK = 154\nconst VK_FORMAT_EAC_R11G11_UNORM_BLOCK = 155\nconst VK_FORMAT_EAC_R11G11_SNORM_BLOCK = 156\nconst VK_FORMAT_ASTC_4x4_UNORM_BLOCK = 157\nconst VK_FORMAT_ASTC_4x4_SRGB_BLOCK = 158\nconst VK_FORMAT_ASTC_5x4_UNORM_BLOCK = 159\nconst VK_FORMAT_ASTC_5x4_SRGB_BLOCK = 160\nconst VK_FORMAT_ASTC_5x5_UNORM_BLOCK = 161\nconst VK_FORMAT_ASTC_5x5_SRGB_BLOCK = 162\nconst VK_FORMAT_ASTC_6x5_UNORM_BLOCK = 163\nconst VK_FORMAT_ASTC_6x5_SRGB_BLOCK = 164\nconst VK_FORMAT_ASTC_6x6_UNORM_BLOCK = 165\nconst VK_FORMAT_ASTC_6x6_SRGB_BLOCK = 166\nconst VK_FORMAT_ASTC_8x5_UNORM_BLOCK = 167\nconst VK_FORMAT_ASTC_8x5_SRGB_BLOCK = 168\nconst VK_FORMAT_ASTC_8x6_UNORM_BLOCK = 169\nconst VK_FORMAT_ASTC_8x6_SRGB_BLOCK = 170\nconst VK_FORMAT_ASTC_8x8_UNORM_BLOCK = 171\nconst VK_FORMAT_ASTC_8x8_SRGB_BLOCK = 172\nconst VK_FORMAT_ASTC_10x5_UNORM_BLOCK = 173\nconst VK_FORMAT_ASTC_10x5_SRGB_BLOCK = 174\nconst VK_FORMAT_ASTC_10x6_UNORM_BLOCK = 175\nconst VK_FORMAT_ASTC_10x6_SRGB_BLOCK = 176\nconst VK_FORMAT_ASTC_10x8_UNORM_BLOCK = 177\nconst VK_FORMAT_ASTC_10x8_SRGB_BLOCK = 178\nconst VK_FORMAT_ASTC_10x10_UNORM_BLOCK = 179\nconst VK_FORMAT_ASTC_10x10_SRGB_BLOCK = 180\nconst VK_FORMAT_ASTC_12x10_UNORM_BLOCK = 181\nconst VK_FORMAT_ASTC_12x10_SRGB_BLOCK = 182\nconst VK_FORMAT_ASTC_12x12_UNORM_BLOCK = 183\nconst VK_FORMAT_ASTC_12x12_SRGB_BLOCK = 184\nconst VK_FORMAT_R10X6_UNORM_PACK16 = 1000156007\nconst VK_FORMAT_R10X6G10X6_UNORM_2PACK16 = 1000156008\nconst VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16 = 1000156009\nconst VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16 = 1000156010\nconst VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16 = 1000156011\nconst VK_FORMAT_R12X4_UNORM_PACK16 = 1000156017\nconst VK_FORMAT_R12X4G12X4_UNORM_2PACK16 = 1000156018\nconst VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16 = 1000156019\nconst VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16 = 1000156020\nconst VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16 = 1000156021\nconst VK_FORMAT_PVRTC1_2BPP_UNORM_BLOCK_IMG = 1000054000\nconst VK_FORMAT_PVRTC1_4BPP_UNORM_BLOCK_IMG = 1000054001\nconst VK_FORMAT_PVRTC2_2BPP_UNORM_BLOCK_IMG = 1000054002\nconst VK_FORMAT_PVRTC2_4BPP_UNORM_BLOCK_IMG = 1000054003\nconst VK_FORMAT_PVRTC1_2BPP_SRGB_BLOCK_IMG = 1000054004\nconst VK_FORMAT_PVRTC1_4BPP_SRGB_BLOCK_IMG = 1000054005\nconst VK_FORMAT_PVRTC2_2BPP_SRGB_BLOCK_IMG = 1000054006\nconst VK_FORMAT_PVRTC2_4BPP_SRGB_BLOCK_IMG = 1000054007\nconst VK_FORMAT_ASTC_4x4_SFLOAT_BLOCK_EXT = 1000066000\nconst VK_FORMAT_ASTC_5x4_SFLOAT_BLOCK_EXT = 1000066001\nconst VK_FORMAT_ASTC_5x5_SFLOAT_BLOCK_EXT = 1000066002\nconst VK_FORMAT_ASTC_6x5_SFLOAT_BLOCK_EXT = 1000066003\nconst VK_FORMAT_ASTC_6x6_SFLOAT_BLOCK_EXT = 1000066004\nconst VK_FORMAT_ASTC_8x5_SFLOAT_BLOCK_EXT = 1000066005\nconst VK_FORMAT_ASTC_8x6_SFLOAT_BLOCK_EXT = 1000066006\nconst VK_FORMAT_ASTC_8x8_SFLOAT_BLOCK_EXT = 1000066007\nconst VK_FORMAT_ASTC_10x5_SFLOAT_BLOCK_EXT = 1000066008\nconst VK_FORMAT_ASTC_10x6_SFLOAT_BLOCK_EXT = 1000066009\nconst VK_FORMAT_ASTC_10x8_SFLOAT_BLOCK_EXT = 1000066010\nconst VK_FORMAT_ASTC_10x10_SFLOAT_BLOCK_EXT = 1000066011\nconst VK_FORMAT_ASTC_12x10_SFLOAT_BLOCK_EXT = 1000066012\nconst VK_FORMAT_ASTC_12x12_SFLOAT_BLOCK_EXT = 1000066013\nconst VK_FORMAT_A4R4G4B4_UNORM_PACK16_EXT = 1000340000\nconst VK_FORMAT_A4B4G4R4_UNORM_PACK16_EXT = 1000340001\n\n/**\n * Represents an unpacked KTX 2.0 texture container. Data for individual mip levels are stored in\n * the `.levels` array, typically compressed in Basis Universal formats. Additional properties\n * provide metadata required to process, transcode, and upload these textures.\n */\n\nclass KTX2Container {\n  constructor() {\n    this.vkFormat = VK_FORMAT_UNDEFINED\n    this.typeSize = 1\n    this.pixelWidth = 0\n    this.pixelHeight = 0\n    this.pixelDepth = 0\n    this.layerCount = 0\n    this.faceCount = 1\n    this.supercompressionScheme = KHR_SUPERCOMPRESSION_NONE\n    this.levels = []\n    this.dataFormatDescriptor = [\n      {\n        vendorId: KHR_DF_VENDORID_KHRONOS,\n        descriptorType: KHR_DF_KHR_DESCRIPTORTYPE_BASICFORMAT,\n        descriptorBlockSize: 0,\n        versionNumber: KHR_DF_VERSION,\n        colorModel: KHR_DF_MODEL_UNSPECIFIED,\n        colorPrimaries: KHR_DF_PRIMARIES_BT709,\n        transferFunction: KHR_DF_TRANSFER_SRGB,\n        flags: KHR_DF_FLAG_ALPHA_STRAIGHT,\n        texelBlockDimension: [0, 0, 0, 0],\n        bytesPlane: [0, 0, 0, 0, 0, 0, 0, 0],\n        samples: [],\n      },\n    ]\n    this.keyValue = {}\n    this.globalData = null\n  }\n}\n\nclass BufferReader {\n  constructor(data, byteOffset, byteLength, littleEndian) {\n    this._dataView = void 0\n    this._littleEndian = void 0\n    this._offset = void 0\n    this._dataView = new DataView(data.buffer, data.byteOffset + byteOffset, byteLength)\n    this._littleEndian = littleEndian\n    this._offset = 0\n  }\n\n  _nextUint8() {\n    const value = this._dataView.getUint8(this._offset)\n\n    this._offset += 1\n    return value\n  }\n\n  _nextUint16() {\n    const value = this._dataView.getUint16(this._offset, this._littleEndian)\n\n    this._offset += 2\n    return value\n  }\n\n  _nextUint32() {\n    const value = this._dataView.getUint32(this._offset, this._littleEndian)\n\n    this._offset += 4\n    return value\n  }\n\n  _nextUint64() {\n    const left = this._dataView.getUint32(this._offset, this._littleEndian)\n\n    const right = this._dataView.getUint32(this._offset + 4, this._littleEndian) // TODO(cleanup): Just test this...\n    // const value = this._littleEndian ? left + (2 ** 32 * right) : (2 ** 32 * left) + right;\n\n    const value = left + 2 ** 32 * right\n    this._offset += 8\n    return value\n  }\n\n  _nextInt32() {\n    const value = this._dataView.getInt32(this._offset, this._littleEndian)\n\n    this._offset += 4\n    return value\n  }\n\n  _nextUint8Array(len) {\n    const value = new Uint8Array(this._dataView.buffer, this._dataView.byteOffset + this._offset, len)\n    this._offset += len\n    return value\n  }\n\n  _skip(bytes) {\n    this._offset += bytes\n    return this\n  }\n\n  _scan(maxByteLength, term) {\n    if (term === void 0) {\n      term = 0x00\n    }\n\n    const byteOffset = this._offset\n    let byteLength = 0\n\n    while (this._dataView.getUint8(this._offset) !== term && byteLength < maxByteLength) {\n      byteLength++\n      this._offset++\n    }\n\n    if (byteLength < maxByteLength) this._offset++\n    return new Uint8Array(this._dataView.buffer, this._dataView.byteOffset + byteOffset, byteLength)\n  }\n}\n\n///////////////////////////////////////////////////\n// Common.\n///////////////////////////////////////////////////\nconst KTX_WRITER = 'KTX-Parse v' + '0.6.0'\nconst NUL = new Uint8Array([0x00]) ///////////////////////////////////////////////////\n// KTX2 Header.\n///////////////////////////////////////////////////\n\nconst KTX2_ID = [\n  // '´', 'K', 'T', 'X', '2', '0', 'ª', '\\r', '\\n', '\\x1A', '\\n'\n  0xab,\n  0x4b,\n  0x54,\n  0x58,\n  0x20,\n  0x32,\n  0x30,\n  0xbb,\n  0x0d,\n  0x0a,\n  0x1a,\n  0x0a,\n]\nconst HEADER_BYTE_LENGTH = 68 // 13 * 4 + 2 * 8\n\n/** Encodes text to an ArrayBuffer. */\nfunction encodeText(text) {\n  if (typeof TextEncoder !== 'undefined') {\n    return new TextEncoder().encode(text)\n  }\n\n  return Buffer.from(text)\n}\n/** Decodes an ArrayBuffer to text. */\n\nfunction decodeText(buffer) {\n  if (typeof TextDecoder !== 'undefined') {\n    return new TextDecoder().decode(buffer)\n  }\n\n  return Buffer.from(buffer).toString('utf8')\n}\n/** Concatenates N ArrayBuffers. */\n\nfunction concat(buffers) {\n  let totalByteLength = 0\n\n  for (const buffer of buffers) {\n    totalByteLength += buffer.byteLength\n  }\n\n  const result = new Uint8Array(totalByteLength)\n  let byteOffset = 0\n\n  for (const buffer of buffers) {\n    result.set(new Uint8Array(buffer), byteOffset)\n    byteOffset += buffer.byteLength\n  }\n\n  return result\n}\n\n/**\n * Parses a KTX 2.0 file, returning an unpacked {@link KTX2Container} instance with all associated\n * data. The container's mip levels and other binary data are pointers into the original file, not\n * copies, so the original file should not be overwritten after reading.\n *\n * @param data Bytes of KTX 2.0 file, as Uint8Array or Buffer.\n */\n\nfunction read(data) {\n  ///////////////////////////////////////////////////\n  // KTX 2.0 Identifier.\n  ///////////////////////////////////////////////////\n  const id = new Uint8Array(data.buffer, data.byteOffset, KTX2_ID.length)\n\n  if (\n    id[0] !== KTX2_ID[0] || // '´'\n    id[1] !== KTX2_ID[1] || // 'K'\n    id[2] !== KTX2_ID[2] || // 'T'\n    id[3] !== KTX2_ID[3] || // 'X'\n    id[4] !== KTX2_ID[4] || // ' '\n    id[5] !== KTX2_ID[5] || // '2'\n    id[6] !== KTX2_ID[6] || // '0'\n    id[7] !== KTX2_ID[7] || // 'ª'\n    id[8] !== KTX2_ID[8] || // '\\r'\n    id[9] !== KTX2_ID[9] || // '\\n'\n    id[10] !== KTX2_ID[10] || // '\\x1A'\n    id[11] !== KTX2_ID[11] // '\\n'\n  ) {\n    throw new Error('Missing KTX 2.0 identifier.')\n  }\n\n  const container = new KTX2Container() ///////////////////////////////////////////////////\n  // Header.\n  ///////////////////////////////////////////////////\n\n  const headerByteLength = 17 * Uint32Array.BYTES_PER_ELEMENT\n  const headerReader = new BufferReader(data, KTX2_ID.length, headerByteLength, true)\n  container.vkFormat = headerReader._nextUint32()\n  container.typeSize = headerReader._nextUint32()\n  container.pixelWidth = headerReader._nextUint32()\n  container.pixelHeight = headerReader._nextUint32()\n  container.pixelDepth = headerReader._nextUint32()\n  container.layerCount = headerReader._nextUint32()\n  container.faceCount = headerReader._nextUint32()\n\n  const levelCount = headerReader._nextUint32()\n\n  container.supercompressionScheme = headerReader._nextUint32()\n\n  const dfdByteOffset = headerReader._nextUint32()\n\n  const dfdByteLength = headerReader._nextUint32()\n\n  const kvdByteOffset = headerReader._nextUint32()\n\n  const kvdByteLength = headerReader._nextUint32()\n\n  const sgdByteOffset = headerReader._nextUint64()\n\n  const sgdByteLength = headerReader._nextUint64() ///////////////////////////////////////////////////\n  // Level Index.\n  ///////////////////////////////////////////////////\n\n  const levelByteLength = levelCount * 3 * 8\n  const levelReader = new BufferReader(data, KTX2_ID.length + headerByteLength, levelByteLength, true)\n\n  for (let i = 0; i < levelCount; i++) {\n    container.levels.push({\n      levelData: new Uint8Array(data.buffer, data.byteOffset + levelReader._nextUint64(), levelReader._nextUint64()),\n      uncompressedByteLength: levelReader._nextUint64(),\n    })\n  } ///////////////////////////////////////////////////\n  // Data Format Descriptor (DFD).\n  ///////////////////////////////////////////////////\n\n  const dfdReader = new BufferReader(data, dfdByteOffset, dfdByteLength, true)\n  const dfd = {\n    vendorId: dfdReader\n      ._skip(\n        4,\n        /* totalSize */\n      )\n      ._nextUint16(),\n    descriptorType: dfdReader._nextUint16(),\n    versionNumber: dfdReader._nextUint16(),\n    descriptorBlockSize: dfdReader._nextUint16(),\n    colorModel: dfdReader._nextUint8(),\n    colorPrimaries: dfdReader._nextUint8(),\n    transferFunction: dfdReader._nextUint8(),\n    flags: dfdReader._nextUint8(),\n    texelBlockDimension: [\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n    ],\n    bytesPlane: [\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n    ],\n    samples: [],\n  }\n  const sampleStart = 6\n  const sampleWords = 4\n  const numSamples = (dfd.descriptorBlockSize / 4 - sampleStart) / sampleWords\n\n  for (let i = 0; i < numSamples; i++) {\n    const sample = {\n      bitOffset: dfdReader._nextUint16(),\n      bitLength: dfdReader._nextUint8(),\n      channelType: dfdReader._nextUint8(),\n      samplePosition: [dfdReader._nextUint8(), dfdReader._nextUint8(), dfdReader._nextUint8(), dfdReader._nextUint8()],\n      sampleLower: -Infinity,\n      sampleUpper: Infinity,\n    }\n\n    if (sample.channelType & KHR_DF_SAMPLE_DATATYPE_SIGNED) {\n      sample.sampleLower = dfdReader._nextInt32()\n      sample.sampleUpper = dfdReader._nextInt32()\n    } else {\n      sample.sampleLower = dfdReader._nextUint32()\n      sample.sampleUpper = dfdReader._nextUint32()\n    }\n\n    dfd.samples[i] = sample\n  }\n\n  container.dataFormatDescriptor.length = 0\n  container.dataFormatDescriptor.push(dfd) ///////////////////////////////////////////////////\n  // Key/Value Data (KVD).\n  ///////////////////////////////////////////////////\n\n  const kvdReader = new BufferReader(data, kvdByteOffset, kvdByteLength, true)\n\n  while (kvdReader._offset < kvdByteLength) {\n    const keyValueByteLength = kvdReader._nextUint32()\n\n    const keyData = kvdReader._scan(keyValueByteLength)\n\n    const key = decodeText(keyData)\n    container.keyValue[key] = kvdReader._nextUint8Array(keyValueByteLength - keyData.byteLength - 1)\n\n    if (key.match(/^ktx/i)) {\n      const text = decodeText(container.keyValue[key])\n      container.keyValue[key] = text.substring(0, text.lastIndexOf('\\x00'))\n    }\n\n    const kvPadding = keyValueByteLength % 4 ? 4 - (keyValueByteLength % 4) : 0 // align(4)\n    // 4-byte alignment.\n\n    kvdReader._skip(kvPadding)\n  } ///////////////////////////////////////////////////\n  // Supercompression Global Data (SGD).\n  ///////////////////////////////////////////////////\n\n  if (sgdByteLength <= 0) return container\n  const sgdReader = new BufferReader(data, sgdByteOffset, sgdByteLength, true)\n\n  const endpointCount = sgdReader._nextUint16()\n\n  const selectorCount = sgdReader._nextUint16()\n\n  const endpointsByteLength = sgdReader._nextUint32()\n\n  const selectorsByteLength = sgdReader._nextUint32()\n\n  const tablesByteLength = sgdReader._nextUint32()\n\n  const extendedByteLength = sgdReader._nextUint32()\n\n  const imageDescs = []\n\n  for (let i = 0; i < levelCount; i++) {\n    imageDescs.push({\n      imageFlags: sgdReader._nextUint32(),\n      rgbSliceByteOffset: sgdReader._nextUint32(),\n      rgbSliceByteLength: sgdReader._nextUint32(),\n      alphaSliceByteOffset: sgdReader._nextUint32(),\n      alphaSliceByteLength: sgdReader._nextUint32(),\n    })\n  }\n\n  const endpointsByteOffset = sgdByteOffset + sgdReader._offset\n  const selectorsByteOffset = endpointsByteOffset + endpointsByteLength\n  const tablesByteOffset = selectorsByteOffset + selectorsByteLength\n  const extendedByteOffset = tablesByteOffset + tablesByteLength\n  const endpointsData = new Uint8Array(data.buffer, data.byteOffset + endpointsByteOffset, endpointsByteLength)\n  const selectorsData = new Uint8Array(data.buffer, data.byteOffset + selectorsByteOffset, selectorsByteLength)\n  const tablesData = new Uint8Array(data.buffer, data.byteOffset + tablesByteOffset, tablesByteLength)\n  const extendedData = new Uint8Array(data.buffer, data.byteOffset + extendedByteOffset, extendedByteLength)\n  container.globalData = {\n    endpointCount,\n    selectorCount,\n    imageDescs,\n    endpointsData,\n    selectorsData,\n    tablesData,\n    extendedData,\n  }\n  return container\n}\n\nconst DEFAULT_OPTIONS = {\n  keepWriter: false,\n}\n/**\n * Serializes a {@link KTX2Container} instance to a KTX 2.0 file. Mip levels and other binary data\n * are copied into the resulting Uint8Array, so the original container can safely be edited or\n * destroyed after it is serialized.\n *\n * Options:\n * - keepWriter: If true, 'KTXWriter' key/value field is written as provided by the container.\n * \t\tOtherwise, a string for the current ktx-parse version is generated. Default: false.\n *\n * @param container\n * @param options\n */\n\nfunction write(container, options) {\n  if (options === void 0) {\n    options = {}\n  }\n\n  options = { ...DEFAULT_OPTIONS, ...options } ///////////////////////////////////////////////////\n  // Supercompression Global Data (SGD).\n  ///////////////////////////////////////////////////\n\n  let sgdBuffer = new ArrayBuffer(0)\n\n  if (container.globalData) {\n    const sgdHeaderBuffer = new ArrayBuffer(20 + container.globalData.imageDescs.length * 5 * 4)\n    const sgdHeaderView = new DataView(sgdHeaderBuffer)\n    sgdHeaderView.setUint16(0, container.globalData.endpointCount, true)\n    sgdHeaderView.setUint16(2, container.globalData.selectorCount, true)\n    sgdHeaderView.setUint32(4, container.globalData.endpointsData.byteLength, true)\n    sgdHeaderView.setUint32(8, container.globalData.selectorsData.byteLength, true)\n    sgdHeaderView.setUint32(12, container.globalData.tablesData.byteLength, true)\n    sgdHeaderView.setUint32(16, container.globalData.extendedData.byteLength, true)\n\n    for (let i = 0; i < container.globalData.imageDescs.length; i++) {\n      const imageDesc = container.globalData.imageDescs[i]\n      sgdHeaderView.setUint32(20 + i * 5 * 4 + 0, imageDesc.imageFlags, true)\n      sgdHeaderView.setUint32(20 + i * 5 * 4 + 4, imageDesc.rgbSliceByteOffset, true)\n      sgdHeaderView.setUint32(20 + i * 5 * 4 + 8, imageDesc.rgbSliceByteLength, true)\n      sgdHeaderView.setUint32(20 + i * 5 * 4 + 12, imageDesc.alphaSliceByteOffset, true)\n      sgdHeaderView.setUint32(20 + i * 5 * 4 + 16, imageDesc.alphaSliceByteLength, true)\n    }\n\n    sgdBuffer = concat([\n      sgdHeaderBuffer,\n      container.globalData.endpointsData,\n      container.globalData.selectorsData,\n      container.globalData.tablesData,\n      container.globalData.extendedData,\n    ])\n  } ///////////////////////////////////////////////////\n  // Key/Value Data (KVD).\n  ///////////////////////////////////////////////////\n\n  const keyValueData = []\n  let keyValue = container.keyValue\n\n  if (!options.keepWriter) {\n    keyValue = { ...container.keyValue, KTXwriter: KTX_WRITER }\n  }\n\n  for (const key in keyValue) {\n    const value = keyValue[key]\n    const keyData = encodeText(key)\n    const valueData = typeof value === 'string' ? concat([encodeText(value), NUL]) : value\n    const kvByteLength = keyData.byteLength + 1 + valueData.byteLength\n    const kvPadding = kvByteLength % 4 ? 4 - (kvByteLength % 4) : 0 // align(4)\n\n    keyValueData.push(\n      concat([\n        new Uint32Array([kvByteLength]),\n        keyData,\n        NUL,\n        valueData,\n        new Uint8Array(kvPadding).fill(0x00), // align(4)\n      ]),\n    )\n  }\n\n  const kvdBuffer = concat(keyValueData) ///////////////////////////////////////////////////\n  // Data Format Descriptor (DFD).\n  ///////////////////////////////////////////////////\n\n  if (\n    container.dataFormatDescriptor.length !== 1 ||\n    container.dataFormatDescriptor[0].descriptorType !== KHR_DF_KHR_DESCRIPTORTYPE_BASICFORMAT\n  ) {\n    throw new Error('Only BASICFORMAT Data Format Descriptor output supported.')\n  }\n\n  const dfd = container.dataFormatDescriptor[0]\n  const dfdBuffer = new ArrayBuffer(28 + dfd.samples.length * 16)\n  const dfdView = new DataView(dfdBuffer)\n  const descriptorBlockSize = 24 + dfd.samples.length * 16\n  dfdView.setUint32(0, dfdBuffer.byteLength, true)\n  dfdView.setUint16(4, dfd.vendorId, true)\n  dfdView.setUint16(6, dfd.descriptorType, true)\n  dfdView.setUint16(8, dfd.versionNumber, true)\n  dfdView.setUint16(10, descriptorBlockSize, true)\n  dfdView.setUint8(12, dfd.colorModel)\n  dfdView.setUint8(13, dfd.colorPrimaries)\n  dfdView.setUint8(14, dfd.transferFunction)\n  dfdView.setUint8(15, dfd.flags)\n\n  if (!Array.isArray(dfd.texelBlockDimension)) {\n    throw new Error('texelBlockDimension is now an array. For dimensionality `d`, set `d - 1`.')\n  }\n\n  dfdView.setUint8(16, dfd.texelBlockDimension[0])\n  dfdView.setUint8(17, dfd.texelBlockDimension[1])\n  dfdView.setUint8(18, dfd.texelBlockDimension[2])\n  dfdView.setUint8(19, dfd.texelBlockDimension[3])\n\n  for (let i = 0; i < 8; i++) dfdView.setUint8(20 + i, dfd.bytesPlane[i])\n\n  for (let i = 0; i < dfd.samples.length; i++) {\n    const sample = dfd.samples[i]\n    const sampleByteOffset = 28 + i * 16\n\n    if (sample.channelID) {\n      throw new Error('channelID has been renamed to channelType.')\n    }\n\n    dfdView.setUint16(sampleByteOffset + 0, sample.bitOffset, true)\n    dfdView.setUint8(sampleByteOffset + 2, sample.bitLength)\n    dfdView.setUint8(sampleByteOffset + 3, sample.channelType)\n    dfdView.setUint8(sampleByteOffset + 4, sample.samplePosition[0])\n    dfdView.setUint8(sampleByteOffset + 5, sample.samplePosition[1])\n    dfdView.setUint8(sampleByteOffset + 6, sample.samplePosition[2])\n    dfdView.setUint8(sampleByteOffset + 7, sample.samplePosition[3])\n\n    if (sample.channelType & KHR_DF_SAMPLE_DATATYPE_SIGNED) {\n      dfdView.setInt32(sampleByteOffset + 8, sample.sampleLower, true)\n      dfdView.setInt32(sampleByteOffset + 12, sample.sampleUpper, true)\n    } else {\n      dfdView.setUint32(sampleByteOffset + 8, sample.sampleLower, true)\n      dfdView.setUint32(sampleByteOffset + 12, sample.sampleUpper, true)\n    }\n  } ///////////////////////////////////////////////////\n  // Data alignment.\n  ///////////////////////////////////////////////////\n\n  const dfdByteOffset = KTX2_ID.length + HEADER_BYTE_LENGTH + container.levels.length * 3 * 8\n  const kvdByteOffset = dfdByteOffset + dfdBuffer.byteLength\n  let sgdByteOffset = sgdBuffer.byteLength > 0 ? kvdByteOffset + kvdBuffer.byteLength : 0\n  if (sgdByteOffset % 8) sgdByteOffset += 8 - (sgdByteOffset % 8) // align(8)\n  ///////////////////////////////////////////////////\n  // Level Index.\n  ///////////////////////////////////////////////////\n\n  const levelData = []\n  const levelIndex = new DataView(new ArrayBuffer(container.levels.length * 3 * 8))\n  let levelDataByteOffset = (sgdByteOffset || kvdByteOffset + kvdBuffer.byteLength) + sgdBuffer.byteLength\n\n  for (let i = 0; i < container.levels.length; i++) {\n    const level = container.levels[i]\n    levelData.push(level.levelData)\n    levelIndex.setBigUint64(i * 24 + 0, BigInt(levelDataByteOffset), true)\n    levelIndex.setBigUint64(i * 24 + 8, BigInt(level.levelData.byteLength), true)\n    levelIndex.setBigUint64(i * 24 + 16, BigInt(level.uncompressedByteLength), true)\n    levelDataByteOffset += level.levelData.byteLength\n  } ///////////////////////////////////////////////////\n  // Header.\n  ///////////////////////////////////////////////////\n\n  const headerBuffer = new ArrayBuffer(HEADER_BYTE_LENGTH)\n  const headerView = new DataView(headerBuffer)\n  headerView.setUint32(0, container.vkFormat, true)\n  headerView.setUint32(4, container.typeSize, true)\n  headerView.setUint32(8, container.pixelWidth, true)\n  headerView.setUint32(12, container.pixelHeight, true)\n  headerView.setUint32(16, container.pixelDepth, true)\n  headerView.setUint32(20, container.layerCount, true)\n  headerView.setUint32(24, container.faceCount, true)\n  headerView.setUint32(28, container.levels.length, true)\n  headerView.setUint32(32, container.supercompressionScheme, true)\n  headerView.setUint32(36, dfdByteOffset, true)\n  headerView.setUint32(40, dfdBuffer.byteLength, true)\n  headerView.setUint32(44, kvdByteOffset, true)\n  headerView.setUint32(48, kvdBuffer.byteLength, true)\n  headerView.setBigUint64(52, BigInt(sgdBuffer.byteLength > 0 ? sgdByteOffset : 0), true)\n  headerView.setBigUint64(60, BigInt(sgdBuffer.byteLength), true) ///////////////////////////////////////////////////\n  // Compose.\n  ///////////////////////////////////////////////////\n\n  return new Uint8Array(\n    concat([\n      new Uint8Array(KTX2_ID).buffer,\n      headerBuffer,\n      levelIndex.buffer,\n      dfdBuffer,\n      kvdBuffer,\n      sgdByteOffset > 0\n        ? new ArrayBuffer(sgdByteOffset - (kvdByteOffset + kvdBuffer.byteLength)) // align(8)\n        : new ArrayBuffer(0),\n      sgdBuffer,\n      ...levelData,\n    ]),\n  )\n}\n\nexport {\n  KHR_DF_CHANNEL_RGBSDA_ALPHA,\n  KHR_DF_CHANNEL_RGBSDA_BLUE,\n  KHR_DF_CHANNEL_RGBSDA_DEPTH,\n  KHR_DF_CHANNEL_RGBSDA_GREEN,\n  KHR_DF_CHANNEL_RGBSDA_RED,\n  KHR_DF_CHANNEL_RGBSDA_STENCIL,\n  KHR_DF_FLAG_ALPHA_PREMULTIPLIED,\n  KHR_DF_FLAG_ALPHA_STRAIGHT,\n  KHR_DF_KHR_DESCRIPTORTYPE_BASICFORMAT,\n  KHR_DF_MODEL_ASTC,\n  KHR_DF_MODEL_ETC1,\n  KHR_DF_MODEL_ETC1S,\n  KHR_DF_MODEL_ETC2,\n  KHR_DF_MODEL_RGBSDA,\n  KHR_DF_MODEL_UASTC,\n  KHR_DF_MODEL_UNSPECIFIED,\n  KHR_DF_PRIMARIES_ACES,\n  KHR_DF_PRIMARIES_ACESCC,\n  KHR_DF_PRIMARIES_ADOBERGB,\n  KHR_DF_PRIMARIES_BT2020,\n  KHR_DF_PRIMARIES_BT601_EBU,\n  KHR_DF_PRIMARIES_BT601_SMPTE,\n  KHR_DF_PRIMARIES_BT709,\n  KHR_DF_PRIMARIES_CIEXYZ,\n  KHR_DF_PRIMARIES_DISPLAYP3,\n  KHR_DF_PRIMARIES_NTSC1953,\n  KHR_DF_PRIMARIES_PAL525,\n  KHR_DF_PRIMARIES_UNSPECIFIED,\n  KHR_DF_SAMPLE_DATATYPE_EXPONENT,\n  KHR_DF_SAMPLE_DATATYPE_FLOAT,\n  KHR_DF_SAMPLE_DATATYPE_LINEAR,\n  KHR_DF_SAMPLE_DATATYPE_SIGNED,\n  KHR_DF_TRANSFER_ACESCC,\n  KHR_DF_TRANSFER_ACESCCT,\n  KHR_DF_TRANSFER_ADOBERGB,\n  KHR_DF_TRANSFER_BT1886,\n  KHR_DF_TRANSFER_DCIP3,\n  KHR_DF_TRANSFER_HLG_EOTF,\n  KHR_DF_TRANSFER_HLG_OETF,\n  KHR_DF_TRANSFER_ITU,\n  KHR_DF_TRANSFER_LINEAR,\n  KHR_DF_TRANSFER_NTSC,\n  KHR_DF_TRANSFER_PAL625_EOTF,\n  KHR_DF_TRANSFER_PAL_OETF,\n  KHR_DF_TRANSFER_PQ_EOTF,\n  KHR_DF_TRANSFER_PQ_OETF,\n  KHR_DF_TRANSFER_SLOG,\n  KHR_DF_TRANSFER_SLOG2,\n  KHR_DF_TRANSFER_SRGB,\n  KHR_DF_TRANSFER_ST240,\n  KHR_DF_TRANSFER_UNSPECIFIED,\n  KHR_DF_VENDORID_KHRONOS,\n  KHR_DF_VERSION,\n  KHR_SUPERCOMPRESSION_BASISLZ,\n  KHR_SUPERCOMPRESSION_NONE,\n  KHR_SUPERCOMPRESSION_ZLIB,\n  KHR_SUPERCOMPRESSION_ZSTD,\n  KTX2Container,\n  VK_FORMAT_A1R5G5B5_UNORM_PACK16,\n  VK_FORMAT_A2B10G10R10_SINT_PACK32,\n  VK_FORMAT_A2B10G10R10_SNORM_PACK32,\n  VK_FORMAT_A2B10G10R10_UINT_PACK32,\n  VK_FORMAT_A2B10G10R10_UNORM_PACK32,\n  VK_FORMAT_A2R10G10B10_SINT_PACK32,\n  VK_FORMAT_A2R10G10B10_SNORM_PACK32,\n  VK_FORMAT_A2R10G10B10_UINT_PACK32,\n  VK_FORMAT_A2R10G10B10_UNORM_PACK32,\n  VK_FORMAT_A4B4G4R4_UNORM_PACK16_EXT,\n  VK_FORMAT_A4R4G4B4_UNORM_PACK16_EXT,\n  VK_FORMAT_ASTC_10x10_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_10x10_SRGB_BLOCK,\n  VK_FORMAT_ASTC_10x10_UNORM_BLOCK,\n  VK_FORMAT_ASTC_10x5_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_10x5_SRGB_BLOCK,\n  VK_FORMAT_ASTC_10x5_UNORM_BLOCK,\n  VK_FORMAT_ASTC_10x6_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_10x6_SRGB_BLOCK,\n  VK_FORMAT_ASTC_10x6_UNORM_BLOCK,\n  VK_FORMAT_ASTC_10x8_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_10x8_SRGB_BLOCK,\n  VK_FORMAT_ASTC_10x8_UNORM_BLOCK,\n  VK_FORMAT_ASTC_12x10_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_12x10_SRGB_BLOCK,\n  VK_FORMAT_ASTC_12x10_UNORM_BLOCK,\n  VK_FORMAT_ASTC_12x12_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_12x12_SRGB_BLOCK,\n  VK_FORMAT_ASTC_12x12_UNORM_BLOCK,\n  VK_FORMAT_ASTC_4x4_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_4x4_SRGB_BLOCK,\n  VK_FORMAT_ASTC_4x4_UNORM_BLOCK,\n  VK_FORMAT_ASTC_5x4_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_5x4_SRGB_BLOCK,\n  VK_FORMAT_ASTC_5x4_UNORM_BLOCK,\n  VK_FORMAT_ASTC_5x5_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_5x5_SRGB_BLOCK,\n  VK_FORMAT_ASTC_5x5_UNORM_BLOCK,\n  VK_FORMAT_ASTC_6x5_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_6x5_SRGB_BLOCK,\n  VK_FORMAT_ASTC_6x5_UNORM_BLOCK,\n  VK_FORMAT_ASTC_6x6_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_6x6_SRGB_BLOCK,\n  VK_FORMAT_ASTC_6x6_UNORM_BLOCK,\n  VK_FORMAT_ASTC_8x5_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_8x5_SRGB_BLOCK,\n  VK_FORMAT_ASTC_8x5_UNORM_BLOCK,\n  VK_FORMAT_ASTC_8x6_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_8x6_SRGB_BLOCK,\n  VK_FORMAT_ASTC_8x6_UNORM_BLOCK,\n  VK_FORMAT_ASTC_8x8_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_8x8_SRGB_BLOCK,\n  VK_FORMAT_ASTC_8x8_UNORM_BLOCK,\n  VK_FORMAT_B10G11R11_UFLOAT_PACK32,\n  VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16,\n  VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16,\n  VK_FORMAT_B4G4R4A4_UNORM_PACK16,\n  VK_FORMAT_B5G5R5A1_UNORM_PACK16,\n  VK_FORMAT_B5G6R5_UNORM_PACK16,\n  VK_FORMAT_B8G8R8A8_SINT,\n  VK_FORMAT_B8G8R8A8_SNORM,\n  VK_FORMAT_B8G8R8A8_SRGB,\n  VK_FORMAT_B8G8R8A8_UINT,\n  VK_FORMAT_B8G8R8A8_UNORM,\n  VK_FORMAT_B8G8R8_SINT,\n  VK_FORMAT_B8G8R8_SNORM,\n  VK_FORMAT_B8G8R8_SRGB,\n  VK_FORMAT_B8G8R8_UINT,\n  VK_FORMAT_B8G8R8_UNORM,\n  VK_FORMAT_BC1_RGBA_SRGB_BLOCK,\n  VK_FORMAT_BC1_RGBA_UNORM_BLOCK,\n  VK_FORMAT_BC1_RGB_SRGB_BLOCK,\n  VK_FORMAT_BC1_RGB_UNORM_BLOCK,\n  VK_FORMAT_BC2_SRGB_BLOCK,\n  VK_FORMAT_BC2_UNORM_BLOCK,\n  VK_FORMAT_BC3_SRGB_BLOCK,\n  VK_FORMAT_BC3_UNORM_BLOCK,\n  VK_FORMAT_BC4_SNORM_BLOCK,\n  VK_FORMAT_BC4_UNORM_BLOCK,\n  VK_FORMAT_BC5_SNORM_BLOCK,\n  VK_FORMAT_BC5_UNORM_BLOCK,\n  VK_FORMAT_BC6H_SFLOAT_BLOCK,\n  VK_FORMAT_BC6H_UFLOAT_BLOCK,\n  VK_FORMAT_BC7_SRGB_BLOCK,\n  VK_FORMAT_BC7_UNORM_BLOCK,\n  VK_FORMAT_D16_UNORM,\n  VK_FORMAT_D16_UNORM_S8_UINT,\n  VK_FORMAT_D24_UNORM_S8_UINT,\n  VK_FORMAT_D32_SFLOAT,\n  VK_FORMAT_D32_SFLOAT_S8_UINT,\n  VK_FORMAT_E5B9G9R9_UFLOAT_PACK32,\n  VK_FORMAT_EAC_R11G11_SNORM_BLOCK,\n  VK_FORMAT_EAC_R11G11_UNORM_BLOCK,\n  VK_FORMAT_EAC_R11_SNORM_BLOCK,\n  VK_FORMAT_EAC_R11_UNORM_BLOCK,\n  VK_FORMAT_ETC2_R8G8B8A1_SRGB_BLOCK,\n  VK_FORMAT_ETC2_R8G8B8A1_UNORM_BLOCK,\n  VK_FORMAT_ETC2_R8G8B8A8_SRGB_BLOCK,\n  VK_FORMAT_ETC2_R8G8B8A8_UNORM_BLOCK,\n  VK_FORMAT_ETC2_R8G8B8_SRGB_BLOCK,\n  VK_FORMAT_ETC2_R8G8B8_UNORM_BLOCK,\n  VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16,\n  VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16,\n  VK_FORMAT_PVRTC1_2BPP_SRGB_BLOCK_IMG,\n  VK_FORMAT_PVRTC1_2BPP_UNORM_BLOCK_IMG,\n  VK_FORMAT_PVRTC1_4BPP_SRGB_BLOCK_IMG,\n  VK_FORMAT_PVRTC1_4BPP_UNORM_BLOCK_IMG,\n  VK_FORMAT_PVRTC2_2BPP_SRGB_BLOCK_IMG,\n  VK_FORMAT_PVRTC2_2BPP_UNORM_BLOCK_IMG,\n  VK_FORMAT_PVRTC2_4BPP_SRGB_BLOCK_IMG,\n  VK_FORMAT_PVRTC2_4BPP_UNORM_BLOCK_IMG,\n  VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16,\n  VK_FORMAT_R10X6G10X6_UNORM_2PACK16,\n  VK_FORMAT_R10X6_UNORM_PACK16,\n  VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16,\n  VK_FORMAT_R12X4G12X4_UNORM_2PACK16,\n  VK_FORMAT_R12X4_UNORM_PACK16,\n  VK_FORMAT_R16G16B16A16_SFLOAT,\n  VK_FORMAT_R16G16B16A16_SINT,\n  VK_FORMAT_R16G16B16A16_SNORM,\n  VK_FORMAT_R16G16B16A16_UINT,\n  VK_FORMAT_R16G16B16A16_UNORM,\n  VK_FORMAT_R16G16B16_SFLOAT,\n  VK_FORMAT_R16G16B16_SINT,\n  VK_FORMAT_R16G16B16_SNORM,\n  VK_FORMAT_R16G16B16_UINT,\n  VK_FORMAT_R16G16B16_UNORM,\n  VK_FORMAT_R16G16_SFLOAT,\n  VK_FORMAT_R16G16_SINT,\n  VK_FORMAT_R16G16_SNORM,\n  VK_FORMAT_R16G16_UINT,\n  VK_FORMAT_R16G16_UNORM,\n  VK_FORMAT_R16_SFLOAT,\n  VK_FORMAT_R16_SINT,\n  VK_FORMAT_R16_SNORM,\n  VK_FORMAT_R16_UINT,\n  VK_FORMAT_R16_UNORM,\n  VK_FORMAT_R32G32B32A32_SFLOAT,\n  VK_FORMAT_R32G32B32A32_SINT,\n  VK_FORMAT_R32G32B32A32_UINT,\n  VK_FORMAT_R32G32B32_SFLOAT,\n  VK_FORMAT_R32G32B32_SINT,\n  VK_FORMAT_R32G32B32_UINT,\n  VK_FORMAT_R32G32_SFLOAT,\n  VK_FORMAT_R32G32_SINT,\n  VK_FORMAT_R32G32_UINT,\n  VK_FORMAT_R32_SFLOAT,\n  VK_FORMAT_R32_SINT,\n  VK_FORMAT_R32_UINT,\n  VK_FORMAT_R4G4B4A4_UNORM_PACK16,\n  VK_FORMAT_R4G4_UNORM_PACK8,\n  VK_FORMAT_R5G5B5A1_UNORM_PACK16,\n  VK_FORMAT_R5G6B5_UNORM_PACK16,\n  VK_FORMAT_R64G64B64A64_SFLOAT,\n  VK_FORMAT_R64G64B64A64_SINT,\n  VK_FORMAT_R64G64B64A64_UINT,\n  VK_FORMAT_R64G64B64_SFLOAT,\n  VK_FORMAT_R64G64B64_SINT,\n  VK_FORMAT_R64G64B64_UINT,\n  VK_FORMAT_R64G64_SFLOAT,\n  VK_FORMAT_R64G64_SINT,\n  VK_FORMAT_R64G64_UINT,\n  VK_FORMAT_R64_SFLOAT,\n  VK_FORMAT_R64_SINT,\n  VK_FORMAT_R64_UINT,\n  VK_FORMAT_R8G8B8A8_SINT,\n  VK_FORMAT_R8G8B8A8_SNORM,\n  VK_FORMAT_R8G8B8A8_SRGB,\n  VK_FORMAT_R8G8B8A8_UINT,\n  VK_FORMAT_R8G8B8A8_UNORM,\n  VK_FORMAT_R8G8B8_SINT,\n  VK_FORMAT_R8G8B8_SNORM,\n  VK_FORMAT_R8G8B8_SRGB,\n  VK_FORMAT_R8G8B8_UINT,\n  VK_FORMAT_R8G8B8_UNORM,\n  VK_FORMAT_R8G8_SINT,\n  VK_FORMAT_R8G8_SNORM,\n  VK_FORMAT_R8G8_SRGB,\n  VK_FORMAT_R8G8_UINT,\n  VK_FORMAT_R8G8_UNORM,\n  VK_FORMAT_R8_SINT,\n  VK_FORMAT_R8_SNORM,\n  VK_FORMAT_R8_SRGB,\n  VK_FORMAT_R8_UINT,\n  VK_FORMAT_R8_UNORM,\n  VK_FORMAT_S8_UINT,\n  VK_FORMAT_UNDEFINED,\n  VK_FORMAT_X8_D24_UNORM_PACK32,\n  read,\n  write,\n}\n//# sourceMappingURL=ktx-parse.esm.js.map\n"], "names": [], "mappings": "AAGK,MAAC,4BAA4B;AAE7B,MAAC,4BAA4B;AAK7B,MAAC,wCAAwC;AACzC,MAAC,0BAA0B;AAC3B,MAAC,iBAAiB;AAClB,MAAC,2BAA2B;AAQ5B,MAAC,6BAA6B;AAC9B,MAAC,kCAAkC;AAGnC,MAAC,uBAAuB;AAiBxB,MAAC,+BAA+B;AAChC,MAAC,yBAAyB;AAS1B,MAAC,6BAA6B;AAS9B,MAAC,gCAAgC;AAMjC,MAAC,sBAAsB;AASvB,MAAC,qBAAqB;AAItB,MAAC,oBAAoB;AACrB,MAAC,uBAAuB;AAIxB,MAAC,sBAAsB;AAWvB,MAAC,2BAA2B;AAI5B,MAAC,0BAA0B;AAkB3B,MAAC,uBAAuB;AAKxB,MAAC,0BAA0B;AAU3B,MAAC,gCAAgC;AAGjC,MAAC,uBAAuB;AAGxB,MAAC,0BAA0B;AAM3B,MAAC,gCAAgC;AAwDjC,MAAC,iCAAiC;AAClC,MAAC,gCAAgC;AA4DtC,MAAM,cAAc;AAAA,EAClB,cAAc;AACZ,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,yBAAyB;AAC9B,SAAK,SAAS,CAAE;AAChB,SAAK,uBAAuB;AAAA,MAC1B;AAAA,QACE,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QACrB,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,QAClB,OAAO;AAAA,QACP,qBAAqB,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,QAChC,YAAY,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,QACnC,SAAS,CAAE;AAAA,MACZ;AAAA,IACF;AACD,SAAK,WAAW,CAAE;AAClB,SAAK,aAAa;AAAA,EACnB;AACH;AAEA,MAAM,aAAa;AAAA,EACjB,YAAY,MAAM,YAAY,YAAY,cAAc;AACtD,SAAK,YAAY;AACjB,SAAK,gBAAgB;AACrB,SAAK,UAAU;AACf,SAAK,YAAY,IAAI,SAAS,KAAK,QAAQ,KAAK,aAAa,YAAY,UAAU;AACnF,SAAK,gBAAgB;AACrB,SAAK,UAAU;AAAA,EAChB;AAAA,EAED,aAAa;AACX,UAAM,QAAQ,KAAK,UAAU,SAAS,KAAK,OAAO;AAElD,SAAK,WAAW;AAChB,WAAO;AAAA,EACR;AAAA,EAED,cAAc;AACZ,UAAM,QAAQ,KAAK,UAAU,UAAU,KAAK,SAAS,KAAK,aAAa;AAEvE,SAAK,WAAW;AAChB,WAAO;AAAA,EACR;AAAA,EAED,cAAc;AACZ,UAAM,QAAQ,KAAK,UAAU,UAAU,KAAK,SAAS,KAAK,aAAa;AAEvE,SAAK,WAAW;AAChB,WAAO;AAAA,EACR;AAAA,EAED,cAAc;AACZ,UAAM,OAAO,KAAK,UAAU,UAAU,KAAK,SAAS,KAAK,aAAa;AAEtE,UAAM,QAAQ,KAAK,UAAU,UAAU,KAAK,UAAU,GAAG,KAAK,aAAa;AAG3E,UAAM,QAAQ,OAAO,KAAK,KAAK;AAC/B,SAAK,WAAW;AAChB,WAAO;AAAA,EACR;AAAA,EAED,aAAa;AACX,UAAM,QAAQ,KAAK,UAAU,SAAS,KAAK,SAAS,KAAK,aAAa;AAEtE,SAAK,WAAW;AAChB,WAAO;AAAA,EACR;AAAA,EAED,gBAAgB,KAAK;AACnB,UAAM,QAAQ,IAAI,WAAW,KAAK,UAAU,QAAQ,KAAK,UAAU,aAAa,KAAK,SAAS,GAAG;AACjG,SAAK,WAAW;AAChB,WAAO;AAAA,EACR;AAAA,EAED,MAAM,OAAO;AACX,SAAK,WAAW;AAChB,WAAO;AAAA,EACR;AAAA,EAED,MAAM,eAAe,MAAM;AACzB,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACR;AAED,UAAM,aAAa,KAAK;AACxB,QAAI,aAAa;AAEjB,WAAO,KAAK,UAAU,SAAS,KAAK,OAAO,MAAM,QAAQ,aAAa,eAAe;AACnF;AACA,WAAK;AAAA,IACN;AAED,QAAI,aAAa;AAAe,WAAK;AACrC,WAAO,IAAI,WAAW,KAAK,UAAU,QAAQ,KAAK,UAAU,aAAa,YAAY,UAAU;AAAA,EAChG;AACH;AAUA,MAAM,UAAU;AAAA;AAAA,EAEd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAaA,SAAS,WAAW,QAAQ;AAC1B,MAAI,OAAO,gBAAgB,aAAa;AACtC,WAAO,IAAI,YAAW,EAAG,OAAO,MAAM;AAAA,EACvC;AAED,SAAO,OAAO,KAAK,MAAM,EAAE,SAAS,MAAM;AAC5C;AA6BA,SAAS,KAAK,MAAM;AAIlB,QAAM,KAAK,IAAI,WAAW,KAAK,QAAQ,KAAK,YAAY,QAAQ,MAAM;AAEtE,MACE,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,EACnB,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,EACnB,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,EACnB,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,EACnB,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,EACnB,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,EACnB,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,EACnB,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,EACnB,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,EACnB,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,EACnB,GAAG,EAAE,MAAM,QAAQ,EAAE;AAAA,EACrB,GAAG,EAAE,MAAM,QAAQ,EAAE,GACrB;AACA,UAAM,IAAI,MAAM,6BAA6B;AAAA,EAC9C;AAED,QAAM,YAAY,IAAI,cAAe;AAIrC,QAAM,mBAAmB,KAAK,YAAY;AAC1C,QAAM,eAAe,IAAI,aAAa,MAAM,QAAQ,QAAQ,kBAAkB,IAAI;AAClF,YAAU,WAAW,aAAa,YAAa;AAC/C,YAAU,WAAW,aAAa,YAAa;AAC/C,YAAU,aAAa,aAAa,YAAa;AACjD,YAAU,cAAc,aAAa,YAAa;AAClD,YAAU,aAAa,aAAa,YAAa;AACjD,YAAU,aAAa,aAAa,YAAa;AACjD,YAAU,YAAY,aAAa,YAAa;AAEhD,QAAM,aAAa,aAAa,YAAa;AAE7C,YAAU,yBAAyB,aAAa,YAAa;AAE7D,QAAM,gBAAgB,aAAa,YAAa;AAEhD,QAAM,gBAAgB,aAAa,YAAa;AAEhD,QAAM,gBAAgB,aAAa,YAAa;AAEhD,QAAM,gBAAgB,aAAa,YAAa;AAEhD,QAAM,gBAAgB,aAAa,YAAa;AAEhD,QAAM,gBAAgB,aAAa,YAAa;AAIhD,QAAM,kBAAkB,aAAa,IAAI;AACzC,QAAM,cAAc,IAAI,aAAa,MAAM,QAAQ,SAAS,kBAAkB,iBAAiB,IAAI;AAEnG,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,cAAU,OAAO,KAAK;AAAA,MACpB,WAAW,IAAI,WAAW,KAAK,QAAQ,KAAK,aAAa,YAAY,YAAW,GAAI,YAAY,YAAW,CAAE;AAAA,MAC7G,wBAAwB,YAAY,YAAa;AAAA,IACvD,CAAK;AAAA,EACF;AAID,QAAM,YAAY,IAAI,aAAa,MAAM,eAAe,eAAe,IAAI;AAC3E,QAAM,MAAM;AAAA,IACV,UAAU,UACP;AAAA,MACC;AAAA;AAAA,IAED,EACA,YAAa;AAAA,IAChB,gBAAgB,UAAU,YAAa;AAAA,IACvC,eAAe,UAAU,YAAa;AAAA,IACtC,qBAAqB,UAAU,YAAa;AAAA,IAC5C,YAAY,UAAU,WAAY;AAAA,IAClC,gBAAgB,UAAU,WAAY;AAAA,IACtC,kBAAkB,UAAU,WAAY;AAAA,IACxC,OAAO,UAAU,WAAY;AAAA,IAC7B,qBAAqB;AAAA,MACnB,UAAU,WAAY;AAAA,MACtB,UAAU,WAAY;AAAA,MACtB,UAAU,WAAY;AAAA,MACtB,UAAU,WAAY;AAAA,IACvB;AAAA,IACD,YAAY;AAAA,MACV,UAAU,WAAY;AAAA,MACtB,UAAU,WAAY;AAAA,MACtB,UAAU,WAAY;AAAA,MACtB,UAAU,WAAY;AAAA,MACtB,UAAU,WAAY;AAAA,MACtB,UAAU,WAAY;AAAA,MACtB,UAAU,WAAY;AAAA,MACtB,UAAU,WAAY;AAAA,IACvB;AAAA,IACD,SAAS,CAAE;AAAA,EACZ;AACD,QAAM,cAAc;AACpB,QAAM,cAAc;AACpB,QAAM,cAAc,IAAI,sBAAsB,IAAI,eAAe;AAEjE,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,UAAM,SAAS;AAAA,MACb,WAAW,UAAU,YAAa;AAAA,MAClC,WAAW,UAAU,WAAY;AAAA,MACjC,aAAa,UAAU,WAAY;AAAA,MACnC,gBAAgB,CAAC,UAAU,WAAU,GAAI,UAAU,cAAc,UAAU,WAAU,GAAI,UAAU,WAAU,CAAE;AAAA,MAC/G,aAAa;AAAA,MACb,aAAa;AAAA,IACd;AAED,QAAI,OAAO,cAAc,+BAA+B;AACtD,aAAO,cAAc,UAAU,WAAY;AAC3C,aAAO,cAAc,UAAU,WAAY;AAAA,IACjD,OAAW;AACL,aAAO,cAAc,UAAU,YAAa;AAC5C,aAAO,cAAc,UAAU,YAAa;AAAA,IAC7C;AAED,QAAI,QAAQ,CAAC,IAAI;AAAA,EAClB;AAED,YAAU,qBAAqB,SAAS;AACxC,YAAU,qBAAqB,KAAK,GAAG;AAIvC,QAAM,YAAY,IAAI,aAAa,MAAM,eAAe,eAAe,IAAI;AAE3E,SAAO,UAAU,UAAU,eAAe;AACxC,UAAM,qBAAqB,UAAU,YAAa;AAElD,UAAM,UAAU,UAAU,MAAM,kBAAkB;AAElD,UAAM,MAAM,WAAW,OAAO;AAC9B,cAAU,SAAS,GAAG,IAAI,UAAU,gBAAgB,qBAAqB,QAAQ,aAAa,CAAC;AAE/F,QAAI,IAAI,MAAM,OAAO,GAAG;AACtB,YAAM,OAAO,WAAW,UAAU,SAAS,GAAG,CAAC;AAC/C,gBAAU,SAAS,GAAG,IAAI,KAAK,UAAU,GAAG,KAAK,YAAY,IAAM,CAAC;AAAA,IACrE;AAED,UAAM,YAAY,qBAAqB,IAAI,IAAK,qBAAqB,IAAK;AAG1E,cAAU,MAAM,SAAS;AAAA,EAC1B;AAID,MAAI,iBAAiB;AAAG,WAAO;AAC/B,QAAM,YAAY,IAAI,aAAa,MAAM,eAAe,eAAe,IAAI;AAE3E,QAAM,gBAAgB,UAAU,YAAa;AAE7C,QAAM,gBAAgB,UAAU,YAAa;AAE7C,QAAM,sBAAsB,UAAU,YAAa;AAEnD,QAAM,sBAAsB,UAAU,YAAa;AAEnD,QAAM,mBAAmB,UAAU,YAAa;AAEhD,QAAM,qBAAqB,UAAU,YAAa;AAElD,QAAM,aAAa,CAAE;AAErB,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,eAAW,KAAK;AAAA,MACd,YAAY,UAAU,YAAa;AAAA,MACnC,oBAAoB,UAAU,YAAa;AAAA,MAC3C,oBAAoB,UAAU,YAAa;AAAA,MAC3C,sBAAsB,UAAU,YAAa;AAAA,MAC7C,sBAAsB,UAAU,YAAa;AAAA,IACnD,CAAK;AAAA,EACF;AAED,QAAM,sBAAsB,gBAAgB,UAAU;AACtD,QAAM,sBAAsB,sBAAsB;AAClD,QAAM,mBAAmB,sBAAsB;AAC/C,QAAM,qBAAqB,mBAAmB;AAC9C,QAAM,gBAAgB,IAAI,WAAW,KAAK,QAAQ,KAAK,aAAa,qBAAqB,mBAAmB;AAC5G,QAAM,gBAAgB,IAAI,WAAW,KAAK,QAAQ,KAAK,aAAa,qBAAqB,mBAAmB;AAC5G,QAAM,aAAa,IAAI,WAAW,KAAK,QAAQ,KAAK,aAAa,kBAAkB,gBAAgB;AACnG,QAAM,eAAe,IAAI,WAAW,KAAK,QAAQ,KAAK,aAAa,oBAAoB,kBAAkB;AACzG,YAAU,aAAa;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD,SAAO;AACT;"}