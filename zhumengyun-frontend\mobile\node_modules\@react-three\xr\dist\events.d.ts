import { ForwardEventsOptions } from '@pmndrs/pointer-events';
import { EventManager } from '@react-three/fiber';
export declare function PointerEvents({ batchEvents, clickThesholdMs, clickThresholdMs, contextMenuButton, customSort, dblClickThresholdMs, filter, forwardPointerCapture, intersectEveryFrame, pointerTypePrefix, }: ForwardEventsOptions): null;
export declare const noEvents: () => EventManager<HTMLElement>;
