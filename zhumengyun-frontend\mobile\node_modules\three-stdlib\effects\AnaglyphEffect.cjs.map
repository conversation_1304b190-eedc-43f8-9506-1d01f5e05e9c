{"version": 3, "file": "AnaglyphEffect.cjs", "sources": ["../../src/effects/AnaglyphEffect.js"], "sourcesContent": ["import {\n  <PERSON>ar<PERSON><PERSON><PERSON>,\n  <PERSON>3,\n  <PERSON><PERSON>,\n  NearestFilter,\n  OrthographicCamera,\n  PlaneGeometry,\n  RGBAFormat,\n  Scene,\n  ShaderMaterial,\n  StereoCamera,\n  WebGLRenderTarget,\n} from 'three'\nimport { version } from '../_polyfill/constants'\n\nclass AnaglyphEffect {\n  constructor(renderer, width = 512, height = 512) {\n    // Dubois matrices from https://citeseerx.ist.psu.edu/viewdoc/download?doi=10.1.1.7.6968&rep=rep1&type=pdf#page=4\n\n    this.colorMatrixLeft = new Matrix3().fromArray([\n      0.4561,\n      -0.0400822,\n      -0.0152161,\n      0.500484,\n      -0.0378246,\n      -0.0205971,\n      0.176381,\n      -0.0157589,\n      -0.00546856,\n    ])\n\n    this.colorMatrixRight = new Matrix3().fromArray([\n      -0.0434706,\n      0.378476,\n      -0.0721527,\n      -0.0879388,\n      0.73364,\n      -0.112961,\n      -0.00155529,\n      -0.0184503,\n      1.2264,\n    ])\n\n    const _camera = new OrthographicCamera(-1, 1, 1, -1, 0, 1)\n\n    const _scene = new Scene()\n\n    const _stereo = new StereoCamera()\n\n    const _params = { minFilter: LinearFilter, magFilter: NearestFilter, format: RGBAFormat }\n\n    const _renderTargetL = new WebGLRenderTarget(width, height, _params)\n    const _renderTargetR = new WebGLRenderTarget(width, height, _params)\n\n    const _material = new ShaderMaterial({\n      uniforms: {\n        mapLeft: { value: _renderTargetL.texture },\n        mapRight: { value: _renderTargetR.texture },\n\n        colorMatrixLeft: { value: this.colorMatrixLeft },\n        colorMatrixRight: { value: this.colorMatrixRight },\n      },\n\n      vertexShader: [\n        'varying vec2 vUv;',\n\n        'void main() {',\n\n        '\tvUv = vec2( uv.x, uv.y );',\n        '\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );',\n\n        '}',\n      ].join('\\n'),\n\n      fragmentShader: [\n        'uniform sampler2D mapLeft;',\n        'uniform sampler2D mapRight;',\n        'varying vec2 vUv;',\n\n        'uniform mat3 colorMatrixLeft;',\n        'uniform mat3 colorMatrixRight;',\n\n        'void main() {',\n\n        '\tvec2 uv = vUv;',\n\n        '\tvec4 colorL = texture2D( mapLeft, uv );',\n        '\tvec4 colorR = texture2D( mapRight, uv );',\n\n        '\tvec3 color = clamp(',\n        '\t\t\tcolorMatrixLeft * colorL.rgb +',\n        '\t\t\tcolorMatrixRight * colorR.rgb, 0., 1. );',\n\n        '\tgl_FragColor = vec4(',\n        '\t\t\tcolor.r, color.g, color.b,',\n        '\t\t\tmax( colorL.a, colorR.a ) );',\n\n        '\t#include <tonemapping_fragment>',\n        `\t#include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>`,\n\n        '}',\n      ].join('\\n'),\n    })\n\n    const _mesh = new Mesh(new PlaneGeometry(2, 2), _material)\n    _scene.add(_mesh)\n\n    this.setSize = function (width, height) {\n      renderer.setSize(width, height)\n\n      const pixelRatio = renderer.getPixelRatio()\n\n      _renderTargetL.setSize(width * pixelRatio, height * pixelRatio)\n      _renderTargetR.setSize(width * pixelRatio, height * pixelRatio)\n    }\n\n    this.render = function (scene, camera) {\n      const currentRenderTarget = renderer.getRenderTarget()\n\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld()\n\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld()\n\n      _stereo.update(camera)\n\n      renderer.setRenderTarget(_renderTargetL)\n      renderer.clear()\n      renderer.render(scene, _stereo.cameraL)\n\n      renderer.setRenderTarget(_renderTargetR)\n      renderer.clear()\n      renderer.render(scene, _stereo.cameraR)\n\n      renderer.setRenderTarget(null)\n      renderer.render(_scene, _camera)\n\n      renderer.setRenderTarget(currentRenderTarget)\n    }\n\n    this.dispose = function () {\n      _renderTargetL.dispose()\n      _renderTargetR.dispose()\n      _mesh.geometry.dispose()\n      _mesh.material.dispose()\n    }\n  }\n}\n\nexport { AnaglyphEffect }\n"], "names": ["Matrix3", "OrthographicCamera", "Scene", "StereoCamera", "LinearFilter", "NearestFilter", "RGBAFormat", "WebGLRenderTarget", "ShaderMaterial", "version", "<PERSON><PERSON>", "PlaneGeometry", "width", "height"], "mappings": ";;;;AAeA,MAAM,eAAe;AAAA,EACnB,YAAY,UAAU,QAAQ,KAAK,SAAS,KAAK;AAG/C,SAAK,kBAAkB,IAAIA,MAAO,QAAA,EAAG,UAAU;AAAA,MAC7C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACN,CAAK;AAED,SAAK,mBAAmB,IAAIA,MAAO,QAAA,EAAG,UAAU;AAAA,MAC9C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACN,CAAK;AAED,UAAM,UAAU,IAAIC,MAAkB,mBAAC,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC;AAEzD,UAAM,SAAS,IAAIC,YAAO;AAE1B,UAAM,UAAU,IAAIC,mBAAc;AAElC,UAAM,UAAU,EAAE,WAAWC,MAAAA,cAAc,WAAWC,MAAa,eAAE,QAAQC,iBAAY;AAEzF,UAAM,iBAAiB,IAAIC,MAAAA,kBAAkB,OAAO,QAAQ,OAAO;AACnE,UAAM,iBAAiB,IAAIA,MAAAA,kBAAkB,OAAO,QAAQ,OAAO;AAEnE,UAAM,YAAY,IAAIC,qBAAe;AAAA,MACnC,UAAU;AAAA,QACR,SAAS,EAAE,OAAO,eAAe,QAAS;AAAA,QAC1C,UAAU,EAAE,OAAO,eAAe,QAAS;AAAA,QAE3C,iBAAiB,EAAE,OAAO,KAAK,gBAAiB;AAAA,QAChD,kBAAkB,EAAE,OAAO,KAAK,iBAAkB;AAAA,MACnD;AAAA,MAED,cAAc;AAAA,QACZ;AAAA,QAEA;AAAA,QAEA;AAAA,QACA;AAAA,QAEA;AAAA,MACR,EAAQ,KAAK,IAAI;AAAA,MAEX,gBAAgB;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QAEA;AAAA,QACA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QACA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QAEA;AAAA,QACA,cAAcC,UAAAA,WAAW,MAAM,wBAAwB;AAAA,QAEvD;AAAA,MACR,EAAQ,KAAK,IAAI;AAAA,IACjB,CAAK;AAED,UAAM,QAAQ,IAAIC,WAAK,IAAIC,MAAa,cAAC,GAAG,CAAC,GAAG,SAAS;AACzD,WAAO,IAAI,KAAK;AAEhB,SAAK,UAAU,SAAUC,QAAOC,SAAQ;AACtC,eAAS,QAAQD,QAAOC,OAAM;AAE9B,YAAM,aAAa,SAAS,cAAe;AAE3C,qBAAe,QAAQD,SAAQ,YAAYC,UAAS,UAAU;AAC9D,qBAAe,QAAQD,SAAQ,YAAYC,UAAS,UAAU;AAAA,IAC/D;AAED,SAAK,SAAS,SAAU,OAAO,QAAQ;AACrC,YAAM,sBAAsB,SAAS,gBAAiB;AAEtD,UAAI,MAAM,0BAA0B;AAAM,cAAM,kBAAmB;AAEnE,UAAI,OAAO,WAAW,QAAQ,OAAO,0BAA0B;AAAM,eAAO,kBAAmB;AAE/F,cAAQ,OAAO,MAAM;AAErB,eAAS,gBAAgB,cAAc;AACvC,eAAS,MAAO;AAChB,eAAS,OAAO,OAAO,QAAQ,OAAO;AAEtC,eAAS,gBAAgB,cAAc;AACvC,eAAS,MAAO;AAChB,eAAS,OAAO,OAAO,QAAQ,OAAO;AAEtC,eAAS,gBAAgB,IAAI;AAC7B,eAAS,OAAO,QAAQ,OAAO;AAE/B,eAAS,gBAAgB,mBAAmB;AAAA,IAC7C;AAED,SAAK,UAAU,WAAY;AACzB,qBAAe,QAAS;AACxB,qBAAe,QAAS;AACxB,YAAM,SAAS,QAAS;AACxB,YAAM,SAAS,QAAS;AAAA,IACzB;AAAA,EACF;AACH;;"}