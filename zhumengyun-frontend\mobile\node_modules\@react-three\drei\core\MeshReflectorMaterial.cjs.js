"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),a=require("@react-three/fiber"),n=require("../materials/BlurPass.cjs.js"),s=require("../materials/MeshReflectorMaterial.cjs.js");function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var a=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,a.get?a:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}require("../materials/ConvolutionMaterial.cjs.js"),require("../helpers/constants.cjs.js");var l=o(e),u=i(t);const d=u.forwardRef((({mixBlur:e=0,mixStrength:t=1,resolution:o=256,blur:i=[0,0],minDepthThreshold:d=.9,maxDepthThreshold:c=1,depthScale:p=0,depthToBlurRatioBias:m=.25,mirror:h=0,distortion:f=1,mixContrast:x=1,distortionMap:M,reflectorOffset:T=0,...S},y)=>{a.extend({MeshReflectorMaterialImpl:s.MeshReflectorMaterial});const w=a.useThree((({gl:e})=>e)),b=a.useThree((({camera:e})=>e)),R=a.useThree((({scene:e})=>e)),j=(i=Array.isArray(i)?i:[i,i])[0]+i[1]>0,v=u.useRef(null);u.useImperativeHandle(y,(()=>v.current),[]);const[g]=u.useState((()=>new r.Plane)),[D]=u.useState((()=>new r.Vector3)),[B]=u.useState((()=>new r.Vector3)),[_]=u.useState((()=>new r.Vector3)),[O]=u.useState((()=>new r.Matrix4)),[P]=u.useState((()=>new r.Vector3(0,0,-1))),[U]=u.useState((()=>new r.Vector4)),[V]=u.useState((()=>new r.Vector3)),[E]=u.useState((()=>new r.Vector3)),[F]=u.useState((()=>new r.Vector4)),[W]=u.useState((()=>new r.Matrix4)),[I]=u.useState((()=>new r.PerspectiveCamera)),q=u.useCallback((()=>{var e;const t=v.current.parent||(null==(e=v.current)?void 0:e.__r3f.parent);if(!t)return;if(B.setFromMatrixPosition(t.matrixWorld),_.setFromMatrixPosition(b.matrixWorld),O.extractRotation(t.matrixWorld),D.set(0,0,1),D.applyMatrix4(O),B.addScaledVector(D,T),V.subVectors(B,_),V.dot(D)>0)return;V.reflect(D).negate(),V.add(B),O.extractRotation(b.matrixWorld),P.set(0,0,-1),P.applyMatrix4(O),P.add(_),E.subVectors(B,P),E.reflect(D).negate(),E.add(B),I.position.copy(V),I.up.set(0,1,0),I.up.applyMatrix4(O),I.up.reflect(D),I.lookAt(E),I.far=b.far,I.updateMatrixWorld(),I.projectionMatrix.copy(b.projectionMatrix),W.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),W.multiply(I.projectionMatrix),W.multiply(I.matrixWorldInverse),W.multiply(t.matrixWorld),g.setFromNormalAndCoplanarPoint(D,B),g.applyMatrix4(I.matrixWorldInverse),U.set(g.normal.x,g.normal.y,g.normal.z,g.constant);const r=I.projectionMatrix;F.x=(Math.sign(U.x)+r.elements[8])/r.elements[0],F.y=(Math.sign(U.y)+r.elements[9])/r.elements[5],F.z=-1,F.w=(1+r.elements[10])/r.elements[14],U.multiplyScalar(2/U.dot(F)),r.elements[2]=U.x,r.elements[6]=U.y,r.elements[10]=U.z+1,r.elements[14]=U.w}),[b,T]),[C,k,L,z]=u.useMemo((()=>{const a={minFilter:r.LinearFilter,magFilter:r.LinearFilter,type:r.HalfFloatType},s=new r.WebGLRenderTarget(o,o,a);s.depthBuffer=!0,s.depthTexture=new r.DepthTexture(o,o),s.depthTexture.format=r.DepthFormat,s.depthTexture.type=r.UnsignedShortType;const l=new r.WebGLRenderTarget(o,o,a);return[s,l,new n.BlurPass({gl:w,resolution:o,width:i[0],height:i[1],minDepthThreshold:d,maxDepthThreshold:c,depthScale:p,depthToBlurRatioBias:m}),{mirror:h,textureMatrix:W,mixBlur:e,tDiffuse:s.texture,tDepth:s.depthTexture,tDiffuseBlur:l.texture,hasBlur:j,mixStrength:t,minDepthThreshold:d,maxDepthThreshold:c,depthScale:p,depthToBlurRatioBias:m,distortion:f,distortionMap:M,mixContrast:x,"defines-USE_BLUR":j?"":void 0,"defines-USE_DEPTH":p>0?"":void 0,"defines-USE_DISTORTION":M?"":void 0}]}),[w,i,W,o,h,j,e,t,d,c,p,m,f,M,x]);return a.useFrame((()=>{var e;const t=v.current.parent||(null==(e=v.current)?void 0:e.__r3f.parent);if(!t)return;t.visible=!1;const r=w.xr.enabled,a=w.shadowMap.autoUpdate;q(),w.xr.enabled=!1,w.shadowMap.autoUpdate=!1,w.setRenderTarget(C),w.state.buffers.depth.setMask(!0),w.autoClear||w.clear(),w.render(R,I),j&&L.render(w,C,k),w.xr.enabled=r,w.shadowMap.autoUpdate=a,t.visible=!0,w.setRenderTarget(null)})),u.createElement("meshReflectorMaterialImpl",l.default({attach:"material",key:"key"+z["defines-USE_BLUR"]+z["defines-USE_DEPTH"]+z["defines-USE_DISTORTION"],ref:v},z,S))}));exports.MeshReflectorMaterial=d;
