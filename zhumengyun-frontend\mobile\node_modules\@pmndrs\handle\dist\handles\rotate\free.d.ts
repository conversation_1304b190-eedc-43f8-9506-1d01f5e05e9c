import { Camera } from 'three';
import { HandlesContext } from '../context.js';
import { HandlesProperties } from '../index.js';
import { RegisteredHandle } from '../registered.js';
export declare class FreeRotateHandle extends RegisteredHandle {
    constructor(context: HandlesContext, tagPrefix?: string);
    update(camera: Camera): void;
    bind(config?: HandlesProperties): (() => void) | undefined;
}
