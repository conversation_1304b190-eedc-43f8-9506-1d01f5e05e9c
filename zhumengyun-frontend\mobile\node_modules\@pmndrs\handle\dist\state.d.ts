import { PointerEvent } from '@pmndrs/pointer-events';
import { Vector3, Quaternion, Euler } from 'three';
export type HandleTransformState = Readonly<{
    /**
     * time (or delta time) in ms
     */
    time: number;
    position: Vector3;
    /**
     * can not be used with axis locked rotation
     */
    quaternion: Quaternion;
    rotation: Euler;
    scale: Vector3;
    pointerAmount: number;
}>;
export type Axis = 'x' | 'y' | 'z';
export type HandleState<T> = Readonly<{
    /**
     * the current event that caused the current transformation
     * is undefined for imperative non-event driven changes like cancelling the interaction
     */
    readonly event: PointerEvent | undefined;
    readonly initial: HandleTransformState;
    /**
     * undefined for the first time
     */
    readonly previous?: HandleTransformState;
    readonly current: HandleTransformState;
    /**
     * undefined for the first time
     */
    readonly delta?: Omit<HandleTransformState, 'pointerAmount'>;
    readonly offset: Omit<HandleTransformState, 'pointerAmount'>;
    readonly first: boolean;
    readonly last: boolean;
    readonly memo: T | undefined;
    readonly cancel: () => void;
}>;
export declare class HandleStateImpl<T> implements HandleState<T> {
    readonly cancel: () => void;
    previous: HandleTransformState | undefined;
    memo: T | undefined;
    event: PointerEvent | undefined;
    initial: HandleTransformState;
    current: HandleTransformState;
    first: boolean;
    last: boolean;
    private _delta?;
    private _offset?;
    constructor(cancel: () => void);
    start(event: PointerEvent, current: HandleTransformState): void;
    update(event: PointerEvent | undefined, current: HandleTransformState): void;
    end(event: PointerEvent | undefined): void;
    get delta(): Omit<HandleTransformState, 'pointerAmount'> | undefined;
    get offset(): Omit<HandleTransformState, 'pointerAmount'>;
}
