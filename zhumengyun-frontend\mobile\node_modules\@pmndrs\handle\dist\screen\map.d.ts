import { PerspectiveCamera, OrthographicCamera, Scene } from 'three';
import { StoreApi } from 'zustand';
import { ScreenCameraState, ScreenCameraStateAndFunctions } from './camera.js';
import { PanScreenHandleStore } from './pan.js';
import { RotateScreenHandleStore } from './rotate.js';
import { ZoomScreenHandleStore } from './zoom.js';
export declare function defaultMapHandlesScreenCameraApply(update: Partial<ScreenCameraState>, store: StoreApi<ScreenCameraState>): void;
export declare class MapHandles {
    readonly rotate: RotateScreenHandleStore;
    readonly pan: PanScreenHandleStore;
    readonly zoom: ZoomScreenHandleStore;
    private readonly store;
    private readonly getCamera;
    private updateDamping;
    private damping;
    constructor(canvas: HTMLCanvasElement, camera: (() => PerspectiveCamera | OrthographicCamera) | PerspectiveCamera | OrthographicCamera, store?: StoreApi<ScreenCameraStateAndFunctions>);
    getStore(): StoreApi<ScreenCameraStateAndFunctions>;
    update(deltaTime: number): void;
    bind(scene: Scene, damping?: boolean | number): () => void;
}
/**
 * @deprecated use MapHandles instead
 */
export declare const MapControls: typeof MapHandles;
