import { P_RAY } from '../private.js';
import { XRRigidTransform } from '../primitives/XRRigidTransform.js';
declare class DOMPointReadOnly {
    x: number;
    y: number;
    z: number;
    w: number;
    constructor(x?: number, y?: number, z?: number, w?: number);
}
export declare class XRRay {
    [P_RAY]: {
        origin: DOMPointReadOnly;
        direction: DOMPointReadOnly;
        matrix: Float32Array | null;
    };
    constructor(origin?: DOMPointInit | XRRigidTransform, direction?: DOMPointInit);
    get origin(): DOMPointReadOnly;
    get direction(): DOMPointReadOnly;
    get matrix(): Float32Array;
}
export {};
//# sourceMappingURL=XRRay.d.ts.map