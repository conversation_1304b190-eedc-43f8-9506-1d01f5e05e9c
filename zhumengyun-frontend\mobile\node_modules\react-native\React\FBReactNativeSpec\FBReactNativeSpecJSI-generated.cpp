/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "FBReactNativeSpecJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeDevMenuCxxSpecJSI_show(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevMenuCxxSpecJSI *>(&turboModule)->show(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevMenuCxxSpecJSI_reload(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevMenuCxxSpecJSI *>(&turboModule)->reload(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevMenuCxxSpecJSI_setProfilingEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevMenuCxxSpecJSI *>(&turboModule)->setProfilingEnabled(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asBool()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevMenuCxxSpecJSI_setHotLoadingEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevMenuCxxSpecJSI *>(&turboModule)->setHotLoadingEnabled(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asBool()
  );
  return jsi::Value::undefined();
}

NativeDevMenuCxxSpecJSI::NativeDevMenuCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("DevMenu", jsInvoker) {
  methodMap_["show"] = MethodMetadata {0, __hostFunction_NativeDevMenuCxxSpecJSI_show};
  methodMap_["reload"] = MethodMetadata {0, __hostFunction_NativeDevMenuCxxSpecJSI_reload};
  methodMap_["setProfilingEnabled"] = MethodMetadata {1, __hostFunction_NativeDevMenuCxxSpecJSI_setProfilingEnabled};
  methodMap_["setHotLoadingEnabled"] = MethodMetadata {1, __hostFunction_NativeDevMenuCxxSpecJSI_setHotLoadingEnabled};
}
static jsi::Value __hostFunction_NativeReactDevToolsRuntimeSettingsModuleCxxSpecJSI_setReloadAndProfileConfig(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeReactDevToolsRuntimeSettingsModuleCxxSpecJSI *>(&turboModule)->setReloadAndProfileConfig(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeReactDevToolsRuntimeSettingsModuleCxxSpecJSI_getReloadAndProfileConfig(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactDevToolsRuntimeSettingsModuleCxxSpecJSI *>(&turboModule)->getReloadAndProfileConfig(
    rt
  );
}

NativeReactDevToolsRuntimeSettingsModuleCxxSpecJSI::NativeReactDevToolsRuntimeSettingsModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ReactDevToolsRuntimeSettingsModule", jsInvoker) {
  methodMap_["setReloadAndProfileConfig"] = MethodMetadata {1, __hostFunction_NativeReactDevToolsRuntimeSettingsModuleCxxSpecJSI_setReloadAndProfileConfig};
  methodMap_["getReloadAndProfileConfig"] = MethodMetadata {0, __hostFunction_NativeReactDevToolsRuntimeSettingsModuleCxxSpecJSI_getReloadAndProfileConfig};
}
static jsi::Value __hostFunction_NativeReactDevToolsSettingsManagerCxxSpecJSI_setGlobalHookSettings(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeReactDevToolsSettingsManagerCxxSpecJSI *>(&turboModule)->setGlobalHookSettings(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeReactDevToolsSettingsManagerCxxSpecJSI_getGlobalHookSettings(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  auto result = static_cast<NativeReactDevToolsSettingsManagerCxxSpecJSI *>(&turboModule)->getGlobalHookSettings(
    rt
  );
  return result ? jsi::Value(std::move(*result)) : jsi::Value::null();
}

NativeReactDevToolsSettingsManagerCxxSpecJSI::NativeReactDevToolsSettingsManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ReactDevToolsSettingsManager", jsInvoker) {
  methodMap_["setGlobalHookSettings"] = MethodMetadata {1, __hostFunction_NativeReactDevToolsSettingsManagerCxxSpecJSI_setGlobalHookSettings};
  methodMap_["getGlobalHookSettings"] = MethodMetadata {0, __hostFunction_NativeReactDevToolsSettingsManagerCxxSpecJSI_getGlobalHookSettings};
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_commonTestFlag(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->commonTestFlag(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_commonTestFlagWithoutNativeImplementation(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->commonTestFlagWithoutNativeImplementation(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_animatedShouldSignalBatch(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->animatedShouldSignalBatch(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_cxxNativeAnimatedEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->cxxNativeAnimatedEnabled(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_disableMainQueueSyncDispatchIOS(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->disableMainQueueSyncDispatchIOS(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_disableMountItemReorderingAndroid(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->disableMountItemReorderingAndroid(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableAccessibilityOrder(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableAccessibilityOrder(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableAccumulatedUpdatesInRawPropsAndroid(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableAccumulatedUpdatesInRawPropsAndroid(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableBridgelessArchitecture(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableBridgelessArchitecture(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableCppPropsIteratorSetter(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableCppPropsIteratorSetter(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableCustomFocusSearchOnClippedElementsAndroid(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableCustomFocusSearchOnClippedElementsAndroid(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableDestroyShadowTreeRevisionAsync(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableDestroyShadowTreeRevisionAsync(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableDoubleMeasurementFixAndroid(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableDoubleMeasurementFixAndroid(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableEagerRootViewAttachment(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableEagerRootViewAttachment(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableFabricLogs(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableFabricLogs(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableFabricRenderer(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableFabricRenderer(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableFixForParentTagDuringReparenting(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableFixForParentTagDuringReparenting(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableFontScaleChangesUpdatingLayout(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableFontScaleChangesUpdatingLayout(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableIOSViewClipToPaddingBox(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableIOSViewClipToPaddingBox(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableJSRuntimeGCOnMemoryPressureOnIOS(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableJSRuntimeGCOnMemoryPressureOnIOS(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableLayoutAnimationsOnAndroid(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableLayoutAnimationsOnAndroid(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableLayoutAnimationsOnIOS(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableLayoutAnimationsOnIOS(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableMainQueueModulesOnIOS(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableMainQueueModulesOnIOS(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableNativeCSSParsing(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableNativeCSSParsing(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableNetworkEventReporting(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableNetworkEventReporting(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableNewBackgroundAndBorderDrawables(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableNewBackgroundAndBorderDrawables(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enablePreparedTextLayout(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enablePreparedTextLayout(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enablePropsUpdateReconciliationAndroid(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enablePropsUpdateReconciliationAndroid(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableResourceTimingAPI(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableResourceTimingAPI(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableSynchronousStateUpdates(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableSynchronousStateUpdates(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableViewCulling(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableViewCulling(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableViewRecycling(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableViewRecycling(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableViewRecyclingForText(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableViewRecyclingForText(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableViewRecyclingForView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->enableViewRecyclingForView(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_fixMappingOfEventPrioritiesBetweenFabricAndReact(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->fixMappingOfEventPrioritiesBetweenFabricAndReact(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_fuseboxEnabledRelease(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->fuseboxEnabledRelease(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_fuseboxNetworkInspectionEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->fuseboxNetworkInspectionEnabled(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_incorporateMaxLinesDuringAndroidLayout(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->incorporateMaxLinesDuringAndroidLayout(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_traceTurboModulePromiseRejectionsOnAndroid(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->traceTurboModulePromiseRejectionsOnAndroid(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_updateRuntimeShadowNodeReferencesOnCommit(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->updateRuntimeShadowNodeReferencesOnCommit(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_useAlwaysAvailableJSErrorHandling(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->useAlwaysAvailableJSErrorHandling(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_useFabricInterop(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->useFabricInterop(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_useNativeViewConfigsInBridgelessMode(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->useNativeViewConfigsInBridgelessMode(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_useOptimizedEventBatchingOnAndroid(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->useOptimizedEventBatchingOnAndroid(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_useRawPropsJsiValue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->useRawPropsJsiValue(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_useShadowNodeStateOnClone(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->useShadowNodeStateOnClone(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_useTurboModuleInterop(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->useTurboModuleInterop(
    rt
  );
}
static jsi::Value __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_useTurboModules(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeReactNativeFeatureFlagsCxxSpecJSI *>(&turboModule)->useTurboModules(
    rt
  );
}

NativeReactNativeFeatureFlagsCxxSpecJSI::NativeReactNativeFeatureFlagsCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("NativeReactNativeFeatureFlagsCxx", jsInvoker) {
  methodMap_["commonTestFlag"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_commonTestFlag};
  methodMap_["commonTestFlagWithoutNativeImplementation"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_commonTestFlagWithoutNativeImplementation};
  methodMap_["animatedShouldSignalBatch"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_animatedShouldSignalBatch};
  methodMap_["cxxNativeAnimatedEnabled"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_cxxNativeAnimatedEnabled};
  methodMap_["disableMainQueueSyncDispatchIOS"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_disableMainQueueSyncDispatchIOS};
  methodMap_["disableMountItemReorderingAndroid"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_disableMountItemReorderingAndroid};
  methodMap_["enableAccessibilityOrder"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableAccessibilityOrder};
  methodMap_["enableAccumulatedUpdatesInRawPropsAndroid"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableAccumulatedUpdatesInRawPropsAndroid};
  methodMap_["enableBridgelessArchitecture"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableBridgelessArchitecture};
  methodMap_["enableCppPropsIteratorSetter"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableCppPropsIteratorSetter};
  methodMap_["enableCustomFocusSearchOnClippedElementsAndroid"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableCustomFocusSearchOnClippedElementsAndroid};
  methodMap_["enableDestroyShadowTreeRevisionAsync"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableDestroyShadowTreeRevisionAsync};
  methodMap_["enableDoubleMeasurementFixAndroid"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableDoubleMeasurementFixAndroid};
  methodMap_["enableEagerRootViewAttachment"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableEagerRootViewAttachment};
  methodMap_["enableFabricLogs"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableFabricLogs};
  methodMap_["enableFabricRenderer"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableFabricRenderer};
  methodMap_["enableFixForParentTagDuringReparenting"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableFixForParentTagDuringReparenting};
  methodMap_["enableFontScaleChangesUpdatingLayout"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableFontScaleChangesUpdatingLayout};
  methodMap_["enableIOSViewClipToPaddingBox"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableIOSViewClipToPaddingBox};
  methodMap_["enableJSRuntimeGCOnMemoryPressureOnIOS"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableJSRuntimeGCOnMemoryPressureOnIOS};
  methodMap_["enableLayoutAnimationsOnAndroid"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableLayoutAnimationsOnAndroid};
  methodMap_["enableLayoutAnimationsOnIOS"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableLayoutAnimationsOnIOS};
  methodMap_["enableMainQueueModulesOnIOS"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableMainQueueModulesOnIOS};
  methodMap_["enableNativeCSSParsing"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableNativeCSSParsing};
  methodMap_["enableNetworkEventReporting"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableNetworkEventReporting};
  methodMap_["enableNewBackgroundAndBorderDrawables"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableNewBackgroundAndBorderDrawables};
  methodMap_["enablePreparedTextLayout"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enablePreparedTextLayout};
  methodMap_["enablePropsUpdateReconciliationAndroid"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enablePropsUpdateReconciliationAndroid};
  methodMap_["enableResourceTimingAPI"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableResourceTimingAPI};
  methodMap_["enableSynchronousStateUpdates"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableSynchronousStateUpdates};
  methodMap_["enableViewCulling"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableViewCulling};
  methodMap_["enableViewRecycling"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableViewRecycling};
  methodMap_["enableViewRecyclingForText"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableViewRecyclingForText};
  methodMap_["enableViewRecyclingForView"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_enableViewRecyclingForView};
  methodMap_["fixMappingOfEventPrioritiesBetweenFabricAndReact"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_fixMappingOfEventPrioritiesBetweenFabricAndReact};
  methodMap_["fuseboxEnabledRelease"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_fuseboxEnabledRelease};
  methodMap_["fuseboxNetworkInspectionEnabled"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_fuseboxNetworkInspectionEnabled};
  methodMap_["incorporateMaxLinesDuringAndroidLayout"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_incorporateMaxLinesDuringAndroidLayout};
  methodMap_["traceTurboModulePromiseRejectionsOnAndroid"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_traceTurboModulePromiseRejectionsOnAndroid};
  methodMap_["updateRuntimeShadowNodeReferencesOnCommit"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_updateRuntimeShadowNodeReferencesOnCommit};
  methodMap_["useAlwaysAvailableJSErrorHandling"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_useAlwaysAvailableJSErrorHandling};
  methodMap_["useFabricInterop"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_useFabricInterop};
  methodMap_["useNativeViewConfigsInBridgelessMode"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_useNativeViewConfigsInBridgelessMode};
  methodMap_["useOptimizedEventBatchingOnAndroid"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_useOptimizedEventBatchingOnAndroid};
  methodMap_["useRawPropsJsiValue"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_useRawPropsJsiValue};
  methodMap_["useShadowNodeStateOnClone"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_useShadowNodeStateOnClone};
  methodMap_["useTurboModuleInterop"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_useTurboModuleInterop};
  methodMap_["useTurboModules"] = MethodMetadata {0, __hostFunction_NativeReactNativeFeatureFlagsCxxSpecJSI_useTurboModules};
}
static jsi::Value __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isReduceMotionEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityInfoCxxSpecJSI *>(&turboModule)->isReduceMotionEnabled(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isInvertColorsEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityInfoCxxSpecJSI *>(&turboModule)->isInvertColorsEnabled(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isHighTextContrastEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityInfoCxxSpecJSI *>(&turboModule)->isHighTextContrastEnabled(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isTouchExplorationEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityInfoCxxSpecJSI *>(&turboModule)->isTouchExplorationEnabled(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isAccessibilityServiceEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityInfoCxxSpecJSI *>(&turboModule)->isAccessibilityServiceEnabled(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityInfoCxxSpecJSI_setAccessibilityFocus(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityInfoCxxSpecJSI *>(&turboModule)->setAccessibilityFocus(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityInfoCxxSpecJSI_announceForAccessibility(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityInfoCxxSpecJSI *>(&turboModule)->announceForAccessibility(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityInfoCxxSpecJSI_getRecommendedTimeoutMillis(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityInfoCxxSpecJSI *>(&turboModule)->getRecommendedTimeoutMillis(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isGrayscaleEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityInfoCxxSpecJSI *>(&turboModule)->isGrayscaleEnabled(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}

NativeAccessibilityInfoCxxSpecJSI::NativeAccessibilityInfoCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("AccessibilityInfo", jsInvoker) {
  methodMap_["isReduceMotionEnabled"] = MethodMetadata {1, __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isReduceMotionEnabled};
  methodMap_["isInvertColorsEnabled"] = MethodMetadata {1, __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isInvertColorsEnabled};
  methodMap_["isHighTextContrastEnabled"] = MethodMetadata {1, __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isHighTextContrastEnabled};
  methodMap_["isTouchExplorationEnabled"] = MethodMetadata {1, __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isTouchExplorationEnabled};
  methodMap_["isAccessibilityServiceEnabled"] = MethodMetadata {1, __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isAccessibilityServiceEnabled};
  methodMap_["setAccessibilityFocus"] = MethodMetadata {1, __hostFunction_NativeAccessibilityInfoCxxSpecJSI_setAccessibilityFocus};
  methodMap_["announceForAccessibility"] = MethodMetadata {1, __hostFunction_NativeAccessibilityInfoCxxSpecJSI_announceForAccessibility};
  methodMap_["getRecommendedTimeoutMillis"] = MethodMetadata {2, __hostFunction_NativeAccessibilityInfoCxxSpecJSI_getRecommendedTimeoutMillis};
  methodMap_["isGrayscaleEnabled"] = MethodMetadata {1, __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isGrayscaleEnabled};
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentBoldTextState(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->getCurrentBoldTextState(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentGrayscaleState(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->getCurrentGrayscaleState(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentInvertColorsState(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->getCurrentInvertColorsState(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentReduceMotionState(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->getCurrentReduceMotionState(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentDarkerSystemColorsState(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->getCurrentDarkerSystemColorsState(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentPrefersCrossFadeTransitionsState(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->getCurrentPrefersCrossFadeTransitionsState(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentReduceTransparencyState(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->getCurrentReduceTransparencyState(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentVoiceOverState(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->getCurrentVoiceOverState(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_setAccessibilityContentSizeMultipliers(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->setAccessibilityContentSizeMultipliers(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_setAccessibilityFocus(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->setAccessibilityFocus(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_announceForAccessibility(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->announceForAccessibility(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_announceForAccessibilityWithOptions(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->announceForAccessibilityWithOptions(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
  return jsi::Value::undefined();
}

NativeAccessibilityManagerCxxSpecJSI::NativeAccessibilityManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("AccessibilityManager", jsInvoker) {
  methodMap_["getCurrentBoldTextState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentBoldTextState};
  methodMap_["getCurrentGrayscaleState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentGrayscaleState};
  methodMap_["getCurrentInvertColorsState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentInvertColorsState};
  methodMap_["getCurrentReduceMotionState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentReduceMotionState};
  methodMap_["getCurrentDarkerSystemColorsState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentDarkerSystemColorsState};
  methodMap_["getCurrentPrefersCrossFadeTransitionsState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentPrefersCrossFadeTransitionsState};
  methodMap_["getCurrentReduceTransparencyState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentReduceTransparencyState};
  methodMap_["getCurrentVoiceOverState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentVoiceOverState};
  methodMap_["setAccessibilityContentSizeMultipliers"] = MethodMetadata {1, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_setAccessibilityContentSizeMultipliers};
  methodMap_["setAccessibilityFocus"] = MethodMetadata {1, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_setAccessibilityFocus};
  methodMap_["announceForAccessibility"] = MethodMetadata {1, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_announceForAccessibility};
  methodMap_["announceForAccessibilityWithOptions"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_announceForAccessibilityWithOptions};
}
static jsi::Value __hostFunction_NativeActionSheetManagerCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeActionSheetManagerCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeActionSheetManagerCxxSpecJSI_showActionSheetWithOptions(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeActionSheetManagerCxxSpecJSI *>(&turboModule)->showActionSheetWithOptions(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeActionSheetManagerCxxSpecJSI_showShareActionSheetWithOptions(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeActionSheetManagerCxxSpecJSI *>(&turboModule)->showShareActionSheetWithOptions(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeActionSheetManagerCxxSpecJSI_dismissActionSheet(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeActionSheetManagerCxxSpecJSI *>(&turboModule)->dismissActionSheet(
    rt
  );
  return jsi::Value::undefined();
}

NativeActionSheetManagerCxxSpecJSI::NativeActionSheetManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ActionSheetManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeActionSheetManagerCxxSpecJSI_getConstants};
  methodMap_["showActionSheetWithOptions"] = MethodMetadata {2, __hostFunction_NativeActionSheetManagerCxxSpecJSI_showActionSheetWithOptions};
  methodMap_["showShareActionSheetWithOptions"] = MethodMetadata {3, __hostFunction_NativeActionSheetManagerCxxSpecJSI_showShareActionSheetWithOptions};
  methodMap_["dismissActionSheet"] = MethodMetadata {0, __hostFunction_NativeActionSheetManagerCxxSpecJSI_dismissActionSheet};
}
static jsi::Value __hostFunction_NativeAlertManagerCxxSpecJSI_alertWithArgs(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAlertManagerCxxSpecJSI *>(&turboModule)->alertWithArgs(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}

NativeAlertManagerCxxSpecJSI::NativeAlertManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("AlertManager", jsInvoker) {
  methodMap_["alertWithArgs"] = MethodMetadata {2, __hostFunction_NativeAlertManagerCxxSpecJSI_alertWithArgs};
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_startOperationBatch(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->startOperationBatch(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_finishOperationBatch(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->finishOperationBatch(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_createAnimatedNode(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->createAnimatedNode(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_updateAnimatedNodeConfig(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->updateAnimatedNodeConfig(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_getValue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->getValue(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_startListeningToAnimatedNodeValue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->startListeningToAnimatedNodeValue(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_stopListeningToAnimatedNodeValue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->stopListeningToAnimatedNodeValue(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_connectAnimatedNodes(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->connectAnimatedNodes(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_disconnectAnimatedNodes(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->disconnectAnimatedNodes(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_startAnimatingNode(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->startAnimatingNode(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber(),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt),
    count <= 3 ? throw jsi::JSError(rt, "Expected argument in position 3 to be passed") : args[3].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_stopAnimation(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->stopAnimation(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_setAnimatedNodeValue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->setAnimatedNodeValue(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_setAnimatedNodeOffset(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->setAnimatedNodeOffset(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_flattenAnimatedNodeOffset(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->flattenAnimatedNodeOffset(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_extractAnimatedNodeOffset(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->extractAnimatedNodeOffset(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_connectAnimatedNodeToView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->connectAnimatedNodeToView(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_disconnectAnimatedNodeFromView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->disconnectAnimatedNodeFromView(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_restoreDefaultValues(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->restoreDefaultValues(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_dropAnimatedNode(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->dropAnimatedNode(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_addAnimatedEventToView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->addAnimatedEventToView(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_removeAnimatedEventFromView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->removeAnimatedEventFromView(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_queueAndExecuteBatchedOperations(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->queueAndExecuteBatchedOperations(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asArray(rt)
  );
  return jsi::Value::undefined();
}

NativeAnimatedModuleCxxSpecJSI::NativeAnimatedModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("NativeAnimatedModule", jsInvoker) {
  methodMap_["startOperationBatch"] = MethodMetadata {0, __hostFunction_NativeAnimatedModuleCxxSpecJSI_startOperationBatch};
  methodMap_["finishOperationBatch"] = MethodMetadata {0, __hostFunction_NativeAnimatedModuleCxxSpecJSI_finishOperationBatch};
  methodMap_["createAnimatedNode"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_createAnimatedNode};
  methodMap_["updateAnimatedNodeConfig"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_updateAnimatedNodeConfig};
  methodMap_["getValue"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_getValue};
  methodMap_["startListeningToAnimatedNodeValue"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_startListeningToAnimatedNodeValue};
  methodMap_["stopListeningToAnimatedNodeValue"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_stopListeningToAnimatedNodeValue};
  methodMap_["connectAnimatedNodes"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_connectAnimatedNodes};
  methodMap_["disconnectAnimatedNodes"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_disconnectAnimatedNodes};
  methodMap_["startAnimatingNode"] = MethodMetadata {4, __hostFunction_NativeAnimatedModuleCxxSpecJSI_startAnimatingNode};
  methodMap_["stopAnimation"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_stopAnimation};
  methodMap_["setAnimatedNodeValue"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_setAnimatedNodeValue};
  methodMap_["setAnimatedNodeOffset"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_setAnimatedNodeOffset};
  methodMap_["flattenAnimatedNodeOffset"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_flattenAnimatedNodeOffset};
  methodMap_["extractAnimatedNodeOffset"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_extractAnimatedNodeOffset};
  methodMap_["connectAnimatedNodeToView"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_connectAnimatedNodeToView};
  methodMap_["disconnectAnimatedNodeFromView"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_disconnectAnimatedNodeFromView};
  methodMap_["restoreDefaultValues"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_restoreDefaultValues};
  methodMap_["dropAnimatedNode"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_dropAnimatedNode};
  methodMap_["addAnimatedEventToView"] = MethodMetadata {3, __hostFunction_NativeAnimatedModuleCxxSpecJSI_addAnimatedEventToView};
  methodMap_["removeAnimatedEventFromView"] = MethodMetadata {3, __hostFunction_NativeAnimatedModuleCxxSpecJSI_removeAnimatedEventFromView};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_removeListeners};
  methodMap_["queueAndExecuteBatchedOperations"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_queueAndExecuteBatchedOperations};
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_startOperationBatch(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->startOperationBatch(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_finishOperationBatch(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->finishOperationBatch(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_createAnimatedNode(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->createAnimatedNode(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_updateAnimatedNodeConfig(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->updateAnimatedNodeConfig(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_getValue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->getValue(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_startListeningToAnimatedNodeValue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->startListeningToAnimatedNodeValue(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_stopListeningToAnimatedNodeValue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->stopListeningToAnimatedNodeValue(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_connectAnimatedNodes(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->connectAnimatedNodes(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_disconnectAnimatedNodes(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->disconnectAnimatedNodes(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_startAnimatingNode(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->startAnimatingNode(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber(),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt),
    count <= 3 ? throw jsi::JSError(rt, "Expected argument in position 3 to be passed") : args[3].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_stopAnimation(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->stopAnimation(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_setAnimatedNodeValue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->setAnimatedNodeValue(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_setAnimatedNodeOffset(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->setAnimatedNodeOffset(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_flattenAnimatedNodeOffset(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->flattenAnimatedNodeOffset(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_extractAnimatedNodeOffset(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->extractAnimatedNodeOffset(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_connectAnimatedNodeToView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->connectAnimatedNodeToView(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_disconnectAnimatedNodeFromView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->disconnectAnimatedNodeFromView(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_restoreDefaultValues(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->restoreDefaultValues(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_dropAnimatedNode(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->dropAnimatedNode(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_addAnimatedEventToView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->addAnimatedEventToView(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_removeAnimatedEventFromView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->removeAnimatedEventFromView(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_queueAndExecuteBatchedOperations(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->queueAndExecuteBatchedOperations(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asArray(rt)
  );
  return jsi::Value::undefined();
}

NativeAnimatedTurboModuleCxxSpecJSI::NativeAnimatedTurboModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("NativeAnimatedTurboModule", jsInvoker) {
  methodMap_["startOperationBatch"] = MethodMetadata {0, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_startOperationBatch};
  methodMap_["finishOperationBatch"] = MethodMetadata {0, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_finishOperationBatch};
  methodMap_["createAnimatedNode"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_createAnimatedNode};
  methodMap_["updateAnimatedNodeConfig"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_updateAnimatedNodeConfig};
  methodMap_["getValue"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_getValue};
  methodMap_["startListeningToAnimatedNodeValue"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_startListeningToAnimatedNodeValue};
  methodMap_["stopListeningToAnimatedNodeValue"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_stopListeningToAnimatedNodeValue};
  methodMap_["connectAnimatedNodes"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_connectAnimatedNodes};
  methodMap_["disconnectAnimatedNodes"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_disconnectAnimatedNodes};
  methodMap_["startAnimatingNode"] = MethodMetadata {4, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_startAnimatingNode};
  methodMap_["stopAnimation"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_stopAnimation};
  methodMap_["setAnimatedNodeValue"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_setAnimatedNodeValue};
  methodMap_["setAnimatedNodeOffset"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_setAnimatedNodeOffset};
  methodMap_["flattenAnimatedNodeOffset"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_flattenAnimatedNodeOffset};
  methodMap_["extractAnimatedNodeOffset"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_extractAnimatedNodeOffset};
  methodMap_["connectAnimatedNodeToView"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_connectAnimatedNodeToView};
  methodMap_["disconnectAnimatedNodeFromView"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_disconnectAnimatedNodeFromView};
  methodMap_["restoreDefaultValues"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_restoreDefaultValues};
  methodMap_["dropAnimatedNode"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_dropAnimatedNode};
  methodMap_["addAnimatedEventToView"] = MethodMetadata {3, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_addAnimatedEventToView};
  methodMap_["removeAnimatedEventFromView"] = MethodMetadata {3, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_removeAnimatedEventFromView};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_removeListeners};
  methodMap_["queueAndExecuteBatchedOperations"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_queueAndExecuteBatchedOperations};
}
static jsi::Value __hostFunction_NativeAppearanceCxxSpecJSI_getColorScheme(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  auto result = static_cast<NativeAppearanceCxxSpecJSI *>(&turboModule)->getColorScheme(
    rt
  );
  return result ? jsi::Value(std::move(*result)) : jsi::Value::null();
}
static jsi::Value __hostFunction_NativeAppearanceCxxSpecJSI_setColorScheme(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAppearanceCxxSpecJSI *>(&turboModule)->setColorScheme(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAppearanceCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAppearanceCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAppearanceCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAppearanceCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}

NativeAppearanceCxxSpecJSI::NativeAppearanceCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("Appearance", jsInvoker) {
  methodMap_["getColorScheme"] = MethodMetadata {0, __hostFunction_NativeAppearanceCxxSpecJSI_getColorScheme};
  methodMap_["setColorScheme"] = MethodMetadata {1, __hostFunction_NativeAppearanceCxxSpecJSI_setColorScheme};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeAppearanceCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeAppearanceCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativeAppStateCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeAppStateCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeAppStateCxxSpecJSI_getCurrentAppState(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAppStateCxxSpecJSI *>(&turboModule)->getCurrentAppState(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAppStateCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAppStateCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAppStateCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAppStateCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}

NativeAppStateCxxSpecJSI::NativeAppStateCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("AppState", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeAppStateCxxSpecJSI_getConstants};
  methodMap_["getCurrentAppState"] = MethodMetadata {2, __hostFunction_NativeAppStateCxxSpecJSI_getCurrentAppState};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeAppStateCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeAppStateCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativeBlobModuleCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobModuleCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeBlobModuleCxxSpecJSI_addNetworkingHandler(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobModuleCxxSpecJSI *>(&turboModule)->addNetworkingHandler(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobModuleCxxSpecJSI_addWebSocketHandler(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobModuleCxxSpecJSI *>(&turboModule)->addWebSocketHandler(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobModuleCxxSpecJSI_removeWebSocketHandler(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobModuleCxxSpecJSI *>(&turboModule)->removeWebSocketHandler(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobModuleCxxSpecJSI_sendOverSocket(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobModuleCxxSpecJSI *>(&turboModule)->sendOverSocket(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobModuleCxxSpecJSI_createFromParts(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobModuleCxxSpecJSI *>(&turboModule)->createFromParts(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asArray(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobModuleCxxSpecJSI_release(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobModuleCxxSpecJSI *>(&turboModule)->release(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}

NativeBlobModuleCxxSpecJSI::NativeBlobModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("BlobModule", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeBlobModuleCxxSpecJSI_getConstants};
  methodMap_["addNetworkingHandler"] = MethodMetadata {0, __hostFunction_NativeBlobModuleCxxSpecJSI_addNetworkingHandler};
  methodMap_["addWebSocketHandler"] = MethodMetadata {1, __hostFunction_NativeBlobModuleCxxSpecJSI_addWebSocketHandler};
  methodMap_["removeWebSocketHandler"] = MethodMetadata {1, __hostFunction_NativeBlobModuleCxxSpecJSI_removeWebSocketHandler};
  methodMap_["sendOverSocket"] = MethodMetadata {2, __hostFunction_NativeBlobModuleCxxSpecJSI_sendOverSocket};
  methodMap_["createFromParts"] = MethodMetadata {2, __hostFunction_NativeBlobModuleCxxSpecJSI_createFromParts};
  methodMap_["release"] = MethodMetadata {1, __hostFunction_NativeBlobModuleCxxSpecJSI_release};
}
static jsi::Value __hostFunction_NativeBugReportingCxxSpecJSI_startReportAProblemFlow(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBugReportingCxxSpecJSI *>(&turboModule)->startReportAProblemFlow(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBugReportingCxxSpecJSI_setExtraData(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBugReportingCxxSpecJSI *>(&turboModule)->setExtraData(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
  return jsi::Value::undefined();
}

NativeBugReportingCxxSpecJSI::NativeBugReportingCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("BugReporting", jsInvoker) {
  methodMap_["startReportAProblemFlow"] = MethodMetadata {0, __hostFunction_NativeBugReportingCxxSpecJSI_startReportAProblemFlow};
  methodMap_["setExtraData"] = MethodMetadata {2, __hostFunction_NativeBugReportingCxxSpecJSI_setExtraData};
}
static jsi::Value __hostFunction_NativeClipboardCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeClipboardCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeClipboardCxxSpecJSI_getString(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeClipboardCxxSpecJSI *>(&turboModule)->getString(
    rt
  );
}
static jsi::Value __hostFunction_NativeClipboardCxxSpecJSI_setString(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeClipboardCxxSpecJSI *>(&turboModule)->setString(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}

NativeClipboardCxxSpecJSI::NativeClipboardCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("Clipboard", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeClipboardCxxSpecJSI_getConstants};
  methodMap_["getString"] = MethodMetadata {0, __hostFunction_NativeClipboardCxxSpecJSI_getString};
  methodMap_["setString"] = MethodMetadata {1, __hostFunction_NativeClipboardCxxSpecJSI_setString};
}
static jsi::Value __hostFunction_NativeDeviceEventManagerCxxSpecJSI_invokeDefaultBackPressHandler(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDeviceEventManagerCxxSpecJSI *>(&turboModule)->invokeDefaultBackPressHandler(
    rt
  );
  return jsi::Value::undefined();
}

NativeDeviceEventManagerCxxSpecJSI::NativeDeviceEventManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("DeviceEventManager", jsInvoker) {
  methodMap_["invokeDefaultBackPressHandler"] = MethodMetadata {0, __hostFunction_NativeDeviceEventManagerCxxSpecJSI_invokeDefaultBackPressHandler};
}
static jsi::Value __hostFunction_NativeDeviceInfoCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDeviceInfoCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}

NativeDeviceInfoCxxSpecJSI::NativeDeviceInfoCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("DeviceInfo", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeDeviceInfoCxxSpecJSI_getConstants};
}
static jsi::Value __hostFunction_NativeDevLoadingViewCxxSpecJSI_showMessage(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevLoadingViewCxxSpecJSI *>(&turboModule)->showMessage(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 || args[1].isNull() || args[1].isUndefined() ? std::nullopt : std::make_optional(args[1].asNumber()),
    count <= 2 || args[2].isNull() || args[2].isUndefined() ? std::nullopt : std::make_optional(args[2].asNumber())
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevLoadingViewCxxSpecJSI_hide(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevLoadingViewCxxSpecJSI *>(&turboModule)->hide(
    rt
  );
  return jsi::Value::undefined();
}

NativeDevLoadingViewCxxSpecJSI::NativeDevLoadingViewCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("DevLoadingView", jsInvoker) {
  methodMap_["showMessage"] = MethodMetadata {3, __hostFunction_NativeDevLoadingViewCxxSpecJSI_showMessage};
  methodMap_["hide"] = MethodMetadata {0, __hostFunction_NativeDevLoadingViewCxxSpecJSI_hide};
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_reload(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->reload(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_reloadWithReason(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->reloadWithReason(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_onFastRefresh(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->onFastRefresh(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_setHotLoadingEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->setHotLoadingEnabled(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asBool()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_setProfilingEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->setProfilingEnabled(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asBool()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_toggleElementInspector(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->toggleElementInspector(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_addMenuItem(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->addMenuItem(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_openDebugger(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->openDebugger(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_setIsShakeToShowDevMenuEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->setIsShakeToShowDevMenuEnabled(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asBool()
  );
  return jsi::Value::undefined();
}

NativeDevSettingsCxxSpecJSI::NativeDevSettingsCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("DevSettings", jsInvoker) {
  methodMap_["reload"] = MethodMetadata {0, __hostFunction_NativeDevSettingsCxxSpecJSI_reload};
  methodMap_["reloadWithReason"] = MethodMetadata {1, __hostFunction_NativeDevSettingsCxxSpecJSI_reloadWithReason};
  methodMap_["onFastRefresh"] = MethodMetadata {0, __hostFunction_NativeDevSettingsCxxSpecJSI_onFastRefresh};
  methodMap_["setHotLoadingEnabled"] = MethodMetadata {1, __hostFunction_NativeDevSettingsCxxSpecJSI_setHotLoadingEnabled};
  methodMap_["setProfilingEnabled"] = MethodMetadata {1, __hostFunction_NativeDevSettingsCxxSpecJSI_setProfilingEnabled};
  methodMap_["toggleElementInspector"] = MethodMetadata {0, __hostFunction_NativeDevSettingsCxxSpecJSI_toggleElementInspector};
  methodMap_["addMenuItem"] = MethodMetadata {1, __hostFunction_NativeDevSettingsCxxSpecJSI_addMenuItem};
  methodMap_["openDebugger"] = MethodMetadata {0, __hostFunction_NativeDevSettingsCxxSpecJSI_openDebugger};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeDevSettingsCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeDevSettingsCxxSpecJSI_removeListeners};
  methodMap_["setIsShakeToShowDevMenuEnabled"] = MethodMetadata {1, __hostFunction_NativeDevSettingsCxxSpecJSI_setIsShakeToShowDevMenuEnabled};
}
static jsi::Value __hostFunction_NativeDialogManagerAndroidCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDialogManagerAndroidCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeDialogManagerAndroidCxxSpecJSI_showAlert(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDialogManagerAndroidCxxSpecJSI *>(&turboModule)->showAlert(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}

NativeDialogManagerAndroidCxxSpecJSI::NativeDialogManagerAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("DialogManagerAndroid", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeDialogManagerAndroidCxxSpecJSI_getConstants};
  methodMap_["showAlert"] = MethodMetadata {3, __hostFunction_NativeDialogManagerAndroidCxxSpecJSI_showAlert};
}
static jsi::Value __hostFunction_NativeExceptionsManagerCxxSpecJSI_reportFatalException(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeExceptionsManagerCxxSpecJSI *>(&turboModule)->reportFatalException(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asArray(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeExceptionsManagerCxxSpecJSI_reportSoftException(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeExceptionsManagerCxxSpecJSI *>(&turboModule)->reportSoftException(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asArray(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeExceptionsManagerCxxSpecJSI_reportException(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeExceptionsManagerCxxSpecJSI *>(&turboModule)->reportException(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeExceptionsManagerCxxSpecJSI_dismissRedbox(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeExceptionsManagerCxxSpecJSI *>(&turboModule)->dismissRedbox(
    rt
  );
  return jsi::Value::undefined();
}

NativeExceptionsManagerCxxSpecJSI::NativeExceptionsManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ExceptionsManager", jsInvoker) {
  methodMap_["reportFatalException"] = MethodMetadata {3, __hostFunction_NativeExceptionsManagerCxxSpecJSI_reportFatalException};
  methodMap_["reportSoftException"] = MethodMetadata {3, __hostFunction_NativeExceptionsManagerCxxSpecJSI_reportSoftException};
  methodMap_["reportException"] = MethodMetadata {1, __hostFunction_NativeExceptionsManagerCxxSpecJSI_reportException};
  methodMap_["dismissRedbox"] = MethodMetadata {0, __hostFunction_NativeExceptionsManagerCxxSpecJSI_dismissRedbox};
}
static jsi::Value __hostFunction_NativeFileReaderModuleCxxSpecJSI_readAsDataURL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeFileReaderModuleCxxSpecJSI *>(&turboModule)->readAsDataURL(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeFileReaderModuleCxxSpecJSI_readAsText(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeFileReaderModuleCxxSpecJSI *>(&turboModule)->readAsText(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt)
  );
}

NativeFileReaderModuleCxxSpecJSI::NativeFileReaderModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("FileReaderModule", jsInvoker) {
  methodMap_["readAsDataURL"] = MethodMetadata {1, __hostFunction_NativeFileReaderModuleCxxSpecJSI_readAsDataURL};
  methodMap_["readAsText"] = MethodMetadata {2, __hostFunction_NativeFileReaderModuleCxxSpecJSI_readAsText};
}
static jsi::Value __hostFunction_NativeFrameRateLoggerCxxSpecJSI_setGlobalOptions(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFrameRateLoggerCxxSpecJSI *>(&turboModule)->setGlobalOptions(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeFrameRateLoggerCxxSpecJSI_setContext(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFrameRateLoggerCxxSpecJSI *>(&turboModule)->setContext(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeFrameRateLoggerCxxSpecJSI_beginScroll(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFrameRateLoggerCxxSpecJSI *>(&turboModule)->beginScroll(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeFrameRateLoggerCxxSpecJSI_endScroll(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFrameRateLoggerCxxSpecJSI *>(&turboModule)->endScroll(
    rt
  );
  return jsi::Value::undefined();
}

NativeFrameRateLoggerCxxSpecJSI::NativeFrameRateLoggerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("FrameRateLogger", jsInvoker) {
  methodMap_["setGlobalOptions"] = MethodMetadata {1, __hostFunction_NativeFrameRateLoggerCxxSpecJSI_setGlobalOptions};
  methodMap_["setContext"] = MethodMetadata {1, __hostFunction_NativeFrameRateLoggerCxxSpecJSI_setContext};
  methodMap_["beginScroll"] = MethodMetadata {0, __hostFunction_NativeFrameRateLoggerCxxSpecJSI_beginScroll};
  methodMap_["endScroll"] = MethodMetadata {0, __hostFunction_NativeFrameRateLoggerCxxSpecJSI_endScroll};
}
static jsi::Value __hostFunction_NativeHeadlessJsTaskSupportCxxSpecJSI_notifyTaskFinished(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeHeadlessJsTaskSupportCxxSpecJSI *>(&turboModule)->notifyTaskFinished(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeHeadlessJsTaskSupportCxxSpecJSI_notifyTaskRetry(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeHeadlessJsTaskSupportCxxSpecJSI *>(&turboModule)->notifyTaskRetry(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
}

NativeHeadlessJsTaskSupportCxxSpecJSI::NativeHeadlessJsTaskSupportCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("HeadlessJsTaskSupport", jsInvoker) {
  methodMap_["notifyTaskFinished"] = MethodMetadata {1, __hostFunction_NativeHeadlessJsTaskSupportCxxSpecJSI_notifyTaskFinished};
  methodMap_["notifyTaskRetry"] = MethodMetadata {1, __hostFunction_NativeHeadlessJsTaskSupportCxxSpecJSI_notifyTaskRetry};
}
static jsi::Value __hostFunction_NativeI18nManagerCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeI18nManagerCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeI18nManagerCxxSpecJSI_allowRTL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeI18nManagerCxxSpecJSI *>(&turboModule)->allowRTL(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asBool()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeI18nManagerCxxSpecJSI_forceRTL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeI18nManagerCxxSpecJSI *>(&turboModule)->forceRTL(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asBool()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeI18nManagerCxxSpecJSI_swapLeftAndRightInRTL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeI18nManagerCxxSpecJSI *>(&turboModule)->swapLeftAndRightInRTL(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asBool()
  );
  return jsi::Value::undefined();
}

NativeI18nManagerCxxSpecJSI::NativeI18nManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("I18nManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeI18nManagerCxxSpecJSI_getConstants};
  methodMap_["allowRTL"] = MethodMetadata {1, __hostFunction_NativeI18nManagerCxxSpecJSI_allowRTL};
  methodMap_["forceRTL"] = MethodMetadata {1, __hostFunction_NativeI18nManagerCxxSpecJSI_forceRTL};
  methodMap_["swapLeftAndRightInRTL"] = MethodMetadata {1, __hostFunction_NativeI18nManagerCxxSpecJSI_swapLeftAndRightInRTL};
}
static jsi::Value __hostFunction_NativeImageEditorCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageEditorCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeImageEditorCxxSpecJSI_cropImage(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeImageEditorCxxSpecJSI *>(&turboModule)->cropImage(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt).asFunction(rt),
    count <= 3 ? throw jsi::JSError(rt, "Expected argument in position 3 to be passed") : args[3].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}

NativeImageEditorCxxSpecJSI::NativeImageEditorCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ImageEditingManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeImageEditorCxxSpecJSI_getConstants};
  methodMap_["cropImage"] = MethodMetadata {4, __hostFunction_NativeImageEditorCxxSpecJSI_cropImage};
}
static jsi::Value __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_abortRequest(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeImageLoaderAndroidCxxSpecJSI *>(&turboModule)->abortRequest(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderAndroidCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_getSize(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderAndroidCxxSpecJSI *>(&turboModule)->getSize(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_getSizeWithHeaders(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderAndroidCxxSpecJSI *>(&turboModule)->getSizeWithHeaders(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_prefetchImage(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderAndroidCxxSpecJSI *>(&turboModule)->prefetchImage(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
}
static jsi::Value __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_queryCache(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderAndroidCxxSpecJSI *>(&turboModule)->queryCache(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asArray(rt)
  );
}

NativeImageLoaderAndroidCxxSpecJSI::NativeImageLoaderAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ImageLoader", jsInvoker) {
  methodMap_["abortRequest"] = MethodMetadata {1, __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_abortRequest};
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_getConstants};
  methodMap_["getSize"] = MethodMetadata {1, __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_getSize};
  methodMap_["getSizeWithHeaders"] = MethodMetadata {2, __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_getSizeWithHeaders};
  methodMap_["prefetchImage"] = MethodMetadata {2, __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_prefetchImage};
  methodMap_["queryCache"] = MethodMetadata {1, __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_queryCache};
}
static jsi::Value __hostFunction_NativeImageLoaderIOSCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderIOSCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeImageLoaderIOSCxxSpecJSI_getSize(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderIOSCxxSpecJSI *>(&turboModule)->getSize(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeImageLoaderIOSCxxSpecJSI_getSizeWithHeaders(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderIOSCxxSpecJSI *>(&turboModule)->getSizeWithHeaders(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeImageLoaderIOSCxxSpecJSI_prefetchImage(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderIOSCxxSpecJSI *>(&turboModule)->prefetchImage(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeImageLoaderIOSCxxSpecJSI_prefetchImageWithMetadata(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderIOSCxxSpecJSI *>(&turboModule)->prefetchImageWithMetadata(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber()
  );
}
static jsi::Value __hostFunction_NativeImageLoaderIOSCxxSpecJSI_queryCache(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderIOSCxxSpecJSI *>(&turboModule)->queryCache(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asArray(rt)
  );
}

NativeImageLoaderIOSCxxSpecJSI::NativeImageLoaderIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ImageLoader", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeImageLoaderIOSCxxSpecJSI_getConstants};
  methodMap_["getSize"] = MethodMetadata {1, __hostFunction_NativeImageLoaderIOSCxxSpecJSI_getSize};
  methodMap_["getSizeWithHeaders"] = MethodMetadata {2, __hostFunction_NativeImageLoaderIOSCxxSpecJSI_getSizeWithHeaders};
  methodMap_["prefetchImage"] = MethodMetadata {1, __hostFunction_NativeImageLoaderIOSCxxSpecJSI_prefetchImage};
  methodMap_["prefetchImageWithMetadata"] = MethodMetadata {3, __hostFunction_NativeImageLoaderIOSCxxSpecJSI_prefetchImageWithMetadata};
  methodMap_["queryCache"] = MethodMetadata {1, __hostFunction_NativeImageLoaderIOSCxxSpecJSI_queryCache};
}
static jsi::Value __hostFunction_NativeImageStoreAndroidCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageStoreAndroidCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeImageStoreAndroidCxxSpecJSI_getBase64ForTag(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeImageStoreAndroidCxxSpecJSI *>(&turboModule)->getBase64ForTag(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}

NativeImageStoreAndroidCxxSpecJSI::NativeImageStoreAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ImageStoreManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeImageStoreAndroidCxxSpecJSI_getConstants};
  methodMap_["getBase64ForTag"] = MethodMetadata {3, __hostFunction_NativeImageStoreAndroidCxxSpecJSI_getBase64ForTag};
}
static jsi::Value __hostFunction_NativeImageStoreIOSCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageStoreIOSCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeImageStoreIOSCxxSpecJSI_getBase64ForTag(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeImageStoreIOSCxxSpecJSI *>(&turboModule)->getBase64ForTag(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeImageStoreIOSCxxSpecJSI_hasImageForTag(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeImageStoreIOSCxxSpecJSI *>(&turboModule)->hasImageForTag(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeImageStoreIOSCxxSpecJSI_removeImageForTag(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeImageStoreIOSCxxSpecJSI *>(&turboModule)->removeImageForTag(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeImageStoreIOSCxxSpecJSI_addImageFromBase64(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeImageStoreIOSCxxSpecJSI *>(&turboModule)->addImageFromBase64(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}

NativeImageStoreIOSCxxSpecJSI::NativeImageStoreIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ImageStoreManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeImageStoreIOSCxxSpecJSI_getConstants};
  methodMap_["getBase64ForTag"] = MethodMetadata {3, __hostFunction_NativeImageStoreIOSCxxSpecJSI_getBase64ForTag};
  methodMap_["hasImageForTag"] = MethodMetadata {2, __hostFunction_NativeImageStoreIOSCxxSpecJSI_hasImageForTag};
  methodMap_["removeImageForTag"] = MethodMetadata {1, __hostFunction_NativeImageStoreIOSCxxSpecJSI_removeImageForTag};
  methodMap_["addImageFromBase64"] = MethodMetadata {3, __hostFunction_NativeImageStoreIOSCxxSpecJSI_addImageFromBase64};
}
static jsi::Value __hostFunction_NativeIntentAndroidCxxSpecJSI_getInitialURL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeIntentAndroidCxxSpecJSI *>(&turboModule)->getInitialURL(
    rt
  );
}
static jsi::Value __hostFunction_NativeIntentAndroidCxxSpecJSI_canOpenURL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeIntentAndroidCxxSpecJSI *>(&turboModule)->canOpenURL(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeIntentAndroidCxxSpecJSI_openURL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeIntentAndroidCxxSpecJSI *>(&turboModule)->openURL(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeIntentAndroidCxxSpecJSI_openSettings(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeIntentAndroidCxxSpecJSI *>(&turboModule)->openSettings(
    rt
  );
}
static jsi::Value __hostFunction_NativeIntentAndroidCxxSpecJSI_sendIntent(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeIntentAndroidCxxSpecJSI *>(&turboModule)->sendIntent(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 || args[1].isNull() || args[1].isUndefined() ? std::nullopt : std::make_optional(args[1].asObject(rt).asArray(rt))
  );
}

NativeIntentAndroidCxxSpecJSI::NativeIntentAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("IntentAndroid", jsInvoker) {
  methodMap_["getInitialURL"] = MethodMetadata {0, __hostFunction_NativeIntentAndroidCxxSpecJSI_getInitialURL};
  methodMap_["canOpenURL"] = MethodMetadata {1, __hostFunction_NativeIntentAndroidCxxSpecJSI_canOpenURL};
  methodMap_["openURL"] = MethodMetadata {1, __hostFunction_NativeIntentAndroidCxxSpecJSI_openURL};
  methodMap_["openSettings"] = MethodMetadata {0, __hostFunction_NativeIntentAndroidCxxSpecJSI_openSettings};
  methodMap_["sendIntent"] = MethodMetadata {2, __hostFunction_NativeIntentAndroidCxxSpecJSI_sendIntent};
}
static jsi::Value __hostFunction_NativeJSCHeapCaptureCxxSpecJSI_captureComplete(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeJSCHeapCaptureCxxSpecJSI *>(&turboModule)->captureComplete(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 || args[1].isNull() || args[1].isUndefined() ? std::nullopt : std::make_optional(args[1].asString(rt))
  );
  return jsi::Value::undefined();
}

NativeJSCHeapCaptureCxxSpecJSI::NativeJSCHeapCaptureCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("JSCHeapCapture", jsInvoker) {
  methodMap_["captureComplete"] = MethodMetadata {2, __hostFunction_NativeJSCHeapCaptureCxxSpecJSI_captureComplete};
}
static jsi::Value __hostFunction_NativeKeyboardObserverCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeKeyboardObserverCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeKeyboardObserverCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeKeyboardObserverCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}

NativeKeyboardObserverCxxSpecJSI::NativeKeyboardObserverCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("KeyboardObserver", jsInvoker) {
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeKeyboardObserverCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeKeyboardObserverCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativeLinkingManagerCxxSpecJSI_getInitialURL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeLinkingManagerCxxSpecJSI *>(&turboModule)->getInitialURL(
    rt
  );
}
static jsi::Value __hostFunction_NativeLinkingManagerCxxSpecJSI_canOpenURL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeLinkingManagerCxxSpecJSI *>(&turboModule)->canOpenURL(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeLinkingManagerCxxSpecJSI_openURL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeLinkingManagerCxxSpecJSI *>(&turboModule)->openURL(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeLinkingManagerCxxSpecJSI_openSettings(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeLinkingManagerCxxSpecJSI *>(&turboModule)->openSettings(
    rt
  );
}
static jsi::Value __hostFunction_NativeLinkingManagerCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeLinkingManagerCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeLinkingManagerCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeLinkingManagerCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}

NativeLinkingManagerCxxSpecJSI::NativeLinkingManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("LinkingManager", jsInvoker) {
  methodMap_["getInitialURL"] = MethodMetadata {0, __hostFunction_NativeLinkingManagerCxxSpecJSI_getInitialURL};
  methodMap_["canOpenURL"] = MethodMetadata {1, __hostFunction_NativeLinkingManagerCxxSpecJSI_canOpenURL};
  methodMap_["openURL"] = MethodMetadata {1, __hostFunction_NativeLinkingManagerCxxSpecJSI_openURL};
  methodMap_["openSettings"] = MethodMetadata {0, __hostFunction_NativeLinkingManagerCxxSpecJSI_openSettings};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeLinkingManagerCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeLinkingManagerCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativeLogBoxCxxSpecJSI_show(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeLogBoxCxxSpecJSI *>(&turboModule)->show(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeLogBoxCxxSpecJSI_hide(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeLogBoxCxxSpecJSI *>(&turboModule)->hide(
    rt
  );
  return jsi::Value::undefined();
}

NativeLogBoxCxxSpecJSI::NativeLogBoxCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("LogBox", jsInvoker) {
  methodMap_["show"] = MethodMetadata {0, __hostFunction_NativeLogBoxCxxSpecJSI_show};
  methodMap_["hide"] = MethodMetadata {0, __hostFunction_NativeLogBoxCxxSpecJSI_hide};
}
static jsi::Value __hostFunction_NativeModalManagerCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeModalManagerCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeModalManagerCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeModalManagerCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}

NativeModalManagerCxxSpecJSI::NativeModalManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ModalManager", jsInvoker) {
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeModalManagerCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeModalManagerCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativeNetworkingAndroidCxxSpecJSI_sendRequest(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingAndroidCxxSpecJSI *>(&turboModule)->sendRequest(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber(),
    count <= 3 ? throw jsi::JSError(rt, "Expected argument in position 3 to be passed") : args[3].asObject(rt).asArray(rt),
    count <= 4 ? throw jsi::JSError(rt, "Expected argument in position 4 to be passed") : args[4].asObject(rt),
    count <= 5 ? throw jsi::JSError(rt, "Expected argument in position 5 to be passed") : args[5].asString(rt),
    count <= 6 ? throw jsi::JSError(rt, "Expected argument in position 6 to be passed") : args[6].asBool(),
    count <= 7 ? throw jsi::JSError(rt, "Expected argument in position 7 to be passed") : args[7].asNumber(),
    count <= 8 ? throw jsi::JSError(rt, "Expected argument in position 8 to be passed") : args[8].asBool()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeNetworkingAndroidCxxSpecJSI_abortRequest(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingAndroidCxxSpecJSI *>(&turboModule)->abortRequest(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeNetworkingAndroidCxxSpecJSI_clearCookies(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingAndroidCxxSpecJSI *>(&turboModule)->clearCookies(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeNetworkingAndroidCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingAndroidCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeNetworkingAndroidCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingAndroidCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}

NativeNetworkingAndroidCxxSpecJSI::NativeNetworkingAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("Networking", jsInvoker) {
  methodMap_["sendRequest"] = MethodMetadata {9, __hostFunction_NativeNetworkingAndroidCxxSpecJSI_sendRequest};
  methodMap_["abortRequest"] = MethodMetadata {1, __hostFunction_NativeNetworkingAndroidCxxSpecJSI_abortRequest};
  methodMap_["clearCookies"] = MethodMetadata {1, __hostFunction_NativeNetworkingAndroidCxxSpecJSI_clearCookies};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeNetworkingAndroidCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeNetworkingAndroidCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativeNetworkingIOSCxxSpecJSI_sendRequest(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingIOSCxxSpecJSI *>(&turboModule)->sendRequest(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeNetworkingIOSCxxSpecJSI_abortRequest(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingIOSCxxSpecJSI *>(&turboModule)->abortRequest(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeNetworkingIOSCxxSpecJSI_clearCookies(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingIOSCxxSpecJSI *>(&turboModule)->clearCookies(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeNetworkingIOSCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingIOSCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeNetworkingIOSCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingIOSCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}

NativeNetworkingIOSCxxSpecJSI::NativeNetworkingIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("Networking", jsInvoker) {
  methodMap_["sendRequest"] = MethodMetadata {2, __hostFunction_NativeNetworkingIOSCxxSpecJSI_sendRequest};
  methodMap_["abortRequest"] = MethodMetadata {1, __hostFunction_NativeNetworkingIOSCxxSpecJSI_abortRequest};
  methodMap_["clearCookies"] = MethodMetadata {1, __hostFunction_NativeNetworkingIOSCxxSpecJSI_clearCookies};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeNetworkingIOSCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeNetworkingIOSCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativePermissionsAndroidCxxSpecJSI_checkPermission(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePermissionsAndroidCxxSpecJSI *>(&turboModule)->checkPermission(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativePermissionsAndroidCxxSpecJSI_requestPermission(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePermissionsAndroidCxxSpecJSI *>(&turboModule)->requestPermission(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativePermissionsAndroidCxxSpecJSI_shouldShowRequestPermissionRationale(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePermissionsAndroidCxxSpecJSI *>(&turboModule)->shouldShowRequestPermissionRationale(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
}
static jsi::Value __hostFunction_NativePermissionsAndroidCxxSpecJSI_requestMultiplePermissions(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePermissionsAndroidCxxSpecJSI *>(&turboModule)->requestMultiplePermissions(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asArray(rt)
  );
}

NativePermissionsAndroidCxxSpecJSI::NativePermissionsAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("PermissionsAndroid", jsInvoker) {
  methodMap_["checkPermission"] = MethodMetadata {1, __hostFunction_NativePermissionsAndroidCxxSpecJSI_checkPermission};
  methodMap_["requestPermission"] = MethodMetadata {1, __hostFunction_NativePermissionsAndroidCxxSpecJSI_requestPermission};
  methodMap_["shouldShowRequestPermissionRationale"] = MethodMetadata {1, __hostFunction_NativePermissionsAndroidCxxSpecJSI_shouldShowRequestPermissionRationale};
  methodMap_["requestMultiplePermissions"] = MethodMetadata {1, __hostFunction_NativePermissionsAndroidCxxSpecJSI_requestMultiplePermissions};
}
static jsi::Value __hostFunction_NativePlatformConstantsAndroidCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePlatformConstantsAndroidCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativePlatformConstantsAndroidCxxSpecJSI_getAndroidID(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePlatformConstantsAndroidCxxSpecJSI *>(&turboModule)->getAndroidID(
    rt
  );
}

NativePlatformConstantsAndroidCxxSpecJSI::NativePlatformConstantsAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("PlatformConstants", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativePlatformConstantsAndroidCxxSpecJSI_getConstants};
  methodMap_["getAndroidID"] = MethodMetadata {0, __hostFunction_NativePlatformConstantsAndroidCxxSpecJSI_getAndroidID};
}
static jsi::Value __hostFunction_NativePlatformConstantsIOSCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePlatformConstantsIOSCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}

NativePlatformConstantsIOSCxxSpecJSI::NativePlatformConstantsIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("PlatformConstants", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativePlatformConstantsIOSCxxSpecJSI_getConstants};
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_onFinishRemoteNotification(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->onFinishRemoteNotification(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_setApplicationIconBadgeNumber(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->setApplicationIconBadgeNumber(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getApplicationIconBadgeNumber(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->getApplicationIconBadgeNumber(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_requestPermissions(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->requestPermissions(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_abandonPermissions(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->abandonPermissions(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_checkPermissions(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->checkPermissions(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_presentLocalNotification(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->presentLocalNotification(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_scheduleLocalNotification(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->scheduleLocalNotification(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_cancelAllLocalNotifications(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->cancelAllLocalNotifications(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_cancelLocalNotifications(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->cancelLocalNotifications(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getInitialNotification(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->getInitialNotification(
    rt
  );
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getScheduledLocalNotifications(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->getScheduledLocalNotifications(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_removeAllDeliveredNotifications(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->removeAllDeliveredNotifications(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_removeDeliveredNotifications(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->removeDeliveredNotifications(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asArray(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getDeliveredNotifications(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->getDeliveredNotifications(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getAuthorizationStatus(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->getAuthorizationStatus(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}

NativePushNotificationManagerIOSCxxSpecJSI::NativePushNotificationManagerIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("PushNotificationManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getConstants};
  methodMap_["onFinishRemoteNotification"] = MethodMetadata {2, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_onFinishRemoteNotification};
  methodMap_["setApplicationIconBadgeNumber"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_setApplicationIconBadgeNumber};
  methodMap_["getApplicationIconBadgeNumber"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getApplicationIconBadgeNumber};
  methodMap_["requestPermissions"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_requestPermissions};
  methodMap_["abandonPermissions"] = MethodMetadata {0, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_abandonPermissions};
  methodMap_["checkPermissions"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_checkPermissions};
  methodMap_["presentLocalNotification"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_presentLocalNotification};
  methodMap_["scheduleLocalNotification"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_scheduleLocalNotification};
  methodMap_["cancelAllLocalNotifications"] = MethodMetadata {0, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_cancelAllLocalNotifications};
  methodMap_["cancelLocalNotifications"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_cancelLocalNotifications};
  methodMap_["getInitialNotification"] = MethodMetadata {0, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getInitialNotification};
  methodMap_["getScheduledLocalNotifications"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getScheduledLocalNotifications};
  methodMap_["removeAllDeliveredNotifications"] = MethodMetadata {0, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_removeAllDeliveredNotifications};
  methodMap_["removeDeliveredNotifications"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_removeDeliveredNotifications};
  methodMap_["getDeliveredNotifications"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getDeliveredNotifications};
  methodMap_["getAuthorizationStatus"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getAuthorizationStatus};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativeRedBoxCxxSpecJSI_setExtraData(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeRedBoxCxxSpecJSI *>(&turboModule)->setExtraData(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeRedBoxCxxSpecJSI_dismiss(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeRedBoxCxxSpecJSI *>(&turboModule)->dismiss(
    rt
  );
  return jsi::Value::undefined();
}

NativeRedBoxCxxSpecJSI::NativeRedBoxCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("RedBox", jsInvoker) {
  methodMap_["setExtraData"] = MethodMetadata {2, __hostFunction_NativeRedBoxCxxSpecJSI_setExtraData};
  methodMap_["dismiss"] = MethodMetadata {0, __hostFunction_NativeRedBoxCxxSpecJSI_dismiss};
}
static jsi::Value __hostFunction_NativeSegmentFetcherCxxSpecJSI_fetchSegment(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeSegmentFetcherCxxSpecJSI *>(&turboModule)->fetchSegment(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeSegmentFetcherCxxSpecJSI_getSegment(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeSegmentFetcherCxxSpecJSI *>(&turboModule)->getSegment(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}

NativeSegmentFetcherCxxSpecJSI::NativeSegmentFetcherCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("SegmentFetcher", jsInvoker) {
  methodMap_["fetchSegment"] = MethodMetadata {3, __hostFunction_NativeSegmentFetcherCxxSpecJSI_fetchSegment};
  methodMap_["getSegment"] = MethodMetadata {3, __hostFunction_NativeSegmentFetcherCxxSpecJSI_getSegment};
}
static jsi::Value __hostFunction_NativeSettingsManagerCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeSettingsManagerCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeSettingsManagerCxxSpecJSI_setValues(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeSettingsManagerCxxSpecJSI *>(&turboModule)->setValues(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeSettingsManagerCxxSpecJSI_deleteValues(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeSettingsManagerCxxSpecJSI *>(&turboModule)->deleteValues(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asArray(rt)
  );
  return jsi::Value::undefined();
}

NativeSettingsManagerCxxSpecJSI::NativeSettingsManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("SettingsManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeSettingsManagerCxxSpecJSI_getConstants};
  methodMap_["setValues"] = MethodMetadata {1, __hostFunction_NativeSettingsManagerCxxSpecJSI_setValues};
  methodMap_["deleteValues"] = MethodMetadata {1, __hostFunction_NativeSettingsManagerCxxSpecJSI_deleteValues};
}
static jsi::Value __hostFunction_NativeShareModuleCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeShareModuleCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeShareModuleCxxSpecJSI_share(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeShareModuleCxxSpecJSI *>(&turboModule)->share(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt),
    count <= 1 || args[1].isUndefined() ? std::nullopt : std::make_optional(args[1].asString(rt))
  );
}

NativeShareModuleCxxSpecJSI::NativeShareModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ShareModule", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeShareModuleCxxSpecJSI_getConstants};
  methodMap_["share"] = MethodMetadata {2, __hostFunction_NativeShareModuleCxxSpecJSI_share};
}
static jsi::Value __hostFunction_NativeSoundManagerCxxSpecJSI_playTouchSound(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeSoundManagerCxxSpecJSI *>(&turboModule)->playTouchSound(
    rt
  );
  return jsi::Value::undefined();
}

NativeSoundManagerCxxSpecJSI::NativeSoundManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("SoundManager", jsInvoker) {
  methodMap_["playTouchSound"] = MethodMetadata {0, __hostFunction_NativeSoundManagerCxxSpecJSI_playTouchSound};
}
static jsi::Value __hostFunction_NativeSourceCodeCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeSourceCodeCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}

NativeSourceCodeCxxSpecJSI::NativeSourceCodeCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("SourceCode", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeSourceCodeCxxSpecJSI_getConstants};
}
static jsi::Value __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeStatusBarManagerAndroidCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_setColor(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerAndroidCxxSpecJSI *>(&turboModule)->setColor(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asBool()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_setTranslucent(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerAndroidCxxSpecJSI *>(&turboModule)->setTranslucent(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asBool()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_setStyle(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerAndroidCxxSpecJSI *>(&turboModule)->setStyle(
    rt,
    count <= 0 || args[0].isNull() || args[0].isUndefined() ? std::nullopt : std::make_optional(args[0].asString(rt))
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_setHidden(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerAndroidCxxSpecJSI *>(&turboModule)->setHidden(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asBool()
  );
  return jsi::Value::undefined();
}

NativeStatusBarManagerAndroidCxxSpecJSI::NativeStatusBarManagerAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("StatusBarManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_getConstants};
  methodMap_["setColor"] = MethodMetadata {2, __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_setColor};
  methodMap_["setTranslucent"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_setTranslucent};
  methodMap_["setStyle"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_setStyle};
  methodMap_["setHidden"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_setHidden};
}
static jsi::Value __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeStatusBarManagerIOSCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_getHeight(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerIOSCxxSpecJSI *>(&turboModule)->getHeight(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_setNetworkActivityIndicatorVisible(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerIOSCxxSpecJSI *>(&turboModule)->setNetworkActivityIndicatorVisible(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asBool()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerIOSCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerIOSCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_setStyle(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerIOSCxxSpecJSI *>(&turboModule)->setStyle(
    rt,
    count <= 0 || args[0].isNull() || args[0].isUndefined() ? std::nullopt : std::make_optional(args[0].asString(rt)),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asBool()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_setHidden(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerIOSCxxSpecJSI *>(&turboModule)->setHidden(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asBool(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt)
  );
  return jsi::Value::undefined();
}

NativeStatusBarManagerIOSCxxSpecJSI::NativeStatusBarManagerIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("StatusBarManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_getConstants};
  methodMap_["getHeight"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_getHeight};
  methodMap_["setNetworkActivityIndicatorVisible"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_setNetworkActivityIndicatorVisible};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_removeListeners};
  methodMap_["setStyle"] = MethodMetadata {2, __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_setStyle};
  methodMap_["setHidden"] = MethodMetadata {2, __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_setHidden};
}
static jsi::Value __hostFunction_NativeTimingCxxSpecJSI_createTimer(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeTimingCxxSpecJSI *>(&turboModule)->createTimer(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber(),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber(),
    count <= 3 ? throw jsi::JSError(rt, "Expected argument in position 3 to be passed") : args[3].asBool()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeTimingCxxSpecJSI_deleteTimer(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeTimingCxxSpecJSI *>(&turboModule)->deleteTimer(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeTimingCxxSpecJSI_setSendIdleEvents(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeTimingCxxSpecJSI *>(&turboModule)->setSendIdleEvents(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asBool()
  );
  return jsi::Value::undefined();
}

NativeTimingCxxSpecJSI::NativeTimingCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("Timing", jsInvoker) {
  methodMap_["createTimer"] = MethodMetadata {4, __hostFunction_NativeTimingCxxSpecJSI_createTimer};
  methodMap_["deleteTimer"] = MethodMetadata {1, __hostFunction_NativeTimingCxxSpecJSI_deleteTimer};
  methodMap_["setSendIdleEvents"] = MethodMetadata {1, __hostFunction_NativeTimingCxxSpecJSI_setSendIdleEvents};
}
static jsi::Value __hostFunction_NativeToastAndroidCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeToastAndroidCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeToastAndroidCxxSpecJSI_show(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeToastAndroidCxxSpecJSI *>(&turboModule)->show(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeToastAndroidCxxSpecJSI_showWithGravity(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeToastAndroidCxxSpecJSI *>(&turboModule)->showWithGravity(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber(),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeToastAndroidCxxSpecJSI_showWithGravityAndOffset(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeToastAndroidCxxSpecJSI *>(&turboModule)->showWithGravityAndOffset(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber(),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber(),
    count <= 3 ? throw jsi::JSError(rt, "Expected argument in position 3 to be passed") : args[3].asNumber(),
    count <= 4 ? throw jsi::JSError(rt, "Expected argument in position 4 to be passed") : args[4].asNumber()
  );
  return jsi::Value::undefined();
}

NativeToastAndroidCxxSpecJSI::NativeToastAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ToastAndroid", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeToastAndroidCxxSpecJSI_getConstants};
  methodMap_["show"] = MethodMetadata {2, __hostFunction_NativeToastAndroidCxxSpecJSI_show};
  methodMap_["showWithGravity"] = MethodMetadata {3, __hostFunction_NativeToastAndroidCxxSpecJSI_showWithGravity};
  methodMap_["showWithGravityAndOffset"] = MethodMetadata {5, __hostFunction_NativeToastAndroidCxxSpecJSI_showWithGravityAndOffset};
}
static jsi::Value __hostFunction_NativeVibrationCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeVibrationCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}
static jsi::Value __hostFunction_NativeVibrationCxxSpecJSI_vibrate(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeVibrationCxxSpecJSI *>(&turboModule)->vibrate(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeVibrationCxxSpecJSI_vibrateByPattern(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeVibrationCxxSpecJSI *>(&turboModule)->vibrateByPattern(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asArray(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeVibrationCxxSpecJSI_cancel(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeVibrationCxxSpecJSI *>(&turboModule)->cancel(
    rt
  );
  return jsi::Value::undefined();
}

NativeVibrationCxxSpecJSI::NativeVibrationCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("Vibration", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeVibrationCxxSpecJSI_getConstants};
  methodMap_["vibrate"] = MethodMetadata {1, __hostFunction_NativeVibrationCxxSpecJSI_vibrate};
  methodMap_["vibrateByPattern"] = MethodMetadata {2, __hostFunction_NativeVibrationCxxSpecJSI_vibrateByPattern};
  methodMap_["cancel"] = MethodMetadata {0, __hostFunction_NativeVibrationCxxSpecJSI_cancel};
}
static jsi::Value __hostFunction_NativeWebSocketModuleCxxSpecJSI_connect(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeWebSocketModuleCxxSpecJSI *>(&turboModule)->connect(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 || args[1].isNull() || args[1].isUndefined() ? std::nullopt : std::make_optional(args[1].asObject(rt).asArray(rt)),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt),
    count <= 3 ? throw jsi::JSError(rt, "Expected argument in position 3 to be passed") : args[3].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeWebSocketModuleCxxSpecJSI_send(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeWebSocketModuleCxxSpecJSI *>(&turboModule)->send(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeWebSocketModuleCxxSpecJSI_sendBinary(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeWebSocketModuleCxxSpecJSI *>(&turboModule)->sendBinary(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeWebSocketModuleCxxSpecJSI_ping(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeWebSocketModuleCxxSpecJSI *>(&turboModule)->ping(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeWebSocketModuleCxxSpecJSI_close(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeWebSocketModuleCxxSpecJSI *>(&turboModule)->close(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeWebSocketModuleCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeWebSocketModuleCxxSpecJSI *>(&turboModule)->addListener(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeWebSocketModuleCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeWebSocketModuleCxxSpecJSI *>(&turboModule)->removeListeners(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}

NativeWebSocketModuleCxxSpecJSI::NativeWebSocketModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("WebSocketModule", jsInvoker) {
  methodMap_["connect"] = MethodMetadata {4, __hostFunction_NativeWebSocketModuleCxxSpecJSI_connect};
  methodMap_["send"] = MethodMetadata {2, __hostFunction_NativeWebSocketModuleCxxSpecJSI_send};
  methodMap_["sendBinary"] = MethodMetadata {2, __hostFunction_NativeWebSocketModuleCxxSpecJSI_sendBinary};
  methodMap_["ping"] = MethodMetadata {1, __hostFunction_NativeWebSocketModuleCxxSpecJSI_ping};
  methodMap_["close"] = MethodMetadata {3, __hostFunction_NativeWebSocketModuleCxxSpecJSI_close};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeWebSocketModuleCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeWebSocketModuleCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativeCPUTimeCxxSpecJSI_getCPUTimeNanos(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeCPUTimeCxxSpecJSI *>(&turboModule)->getCPUTimeNanos(
    rt
  );
}
static jsi::Value __hostFunction_NativeCPUTimeCxxSpecJSI_hasAccurateCPUTimeNanosForBenchmarks(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeCPUTimeCxxSpecJSI *>(&turboModule)->hasAccurateCPUTimeNanosForBenchmarks(
    rt
  );
}

NativeCPUTimeCxxSpecJSI::NativeCPUTimeCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("CPUTimeCxx", jsInvoker) {
  methodMap_["getCPUTimeNanos"] = MethodMetadata {0, __hostFunction_NativeCPUTimeCxxSpecJSI_getCPUTimeNanos};
  methodMap_["hasAccurateCPUTimeNanosForBenchmarks"] = MethodMetadata {0, __hostFunction_NativeCPUTimeCxxSpecJSI_hasAccurateCPUTimeNanosForBenchmarks};
}
static jsi::Value __hostFunction_NativeFantomCxxSpecJSI_startSurface(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFantomCxxSpecJSI *>(&turboModule)->startSurface(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber(),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber(),
    count <= 3 ? throw jsi::JSError(rt, "Expected argument in position 3 to be passed") : args[3].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeFantomCxxSpecJSI_stopSurface(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFantomCxxSpecJSI *>(&turboModule)->stopSurface(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeFantomCxxSpecJSI_enqueueNativeEvent(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFantomCxxSpecJSI *>(&turboModule)->enqueueNativeEvent(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0]),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt),
    count <= 2 || args[2].isUndefined() ? std::nullopt : std::make_optional(jsi::Value(rt, args[2])),
    count <= 3 || args[3].isUndefined() ? std::nullopt : std::make_optional(args[3].asNumber()),
    count <= 4 || args[4].isUndefined() ? std::nullopt : std::make_optional(args[4].asBool())
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeFantomCxxSpecJSI_enqueueScrollEvent(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFantomCxxSpecJSI *>(&turboModule)->enqueueScrollEvent(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0]),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeFantomCxxSpecJSI_enqueueModalSizeUpdate(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFantomCxxSpecJSI *>(&turboModule)->enqueueModalSizeUpdate(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0]),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber(),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeFantomCxxSpecJSI_takeMountingManagerLogs(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeFantomCxxSpecJSI *>(&turboModule)->takeMountingManagerLogs(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
}
static jsi::Value __hostFunction_NativeFantomCxxSpecJSI_flushMessageQueue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFantomCxxSpecJSI *>(&turboModule)->flushMessageQueue(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeFantomCxxSpecJSI_flushEventQueue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFantomCxxSpecJSI *>(&turboModule)->flushEventQueue(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeFantomCxxSpecJSI_validateEmptyMessageQueue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFantomCxxSpecJSI *>(&turboModule)->validateEmptyMessageQueue(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeFantomCxxSpecJSI_getRenderedOutput(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeFantomCxxSpecJSI *>(&turboModule)->getRenderedOutput(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeFantomCxxSpecJSI_reportTestSuiteResultsJSON(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFantomCxxSpecJSI *>(&turboModule)->reportTestSuiteResultsJSON(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeFantomCxxSpecJSI_createShadowNodeReferenceCounter(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeFantomCxxSpecJSI *>(&turboModule)->createShadowNodeReferenceCounter(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0])
  );
}
static jsi::Value __hostFunction_NativeFantomCxxSpecJSI_saveJSMemoryHeapSnapshot(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFantomCxxSpecJSI *>(&turboModule)->saveJSMemoryHeapSnapshot(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt)
  );
  return jsi::Value::undefined();
}

NativeFantomCxxSpecJSI::NativeFantomCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("NativeFantomCxx", jsInvoker) {
  methodMap_["startSurface"] = MethodMetadata {4, __hostFunction_NativeFantomCxxSpecJSI_startSurface};
  methodMap_["stopSurface"] = MethodMetadata {1, __hostFunction_NativeFantomCxxSpecJSI_stopSurface};
  methodMap_["enqueueNativeEvent"] = MethodMetadata {5, __hostFunction_NativeFantomCxxSpecJSI_enqueueNativeEvent};
  methodMap_["enqueueScrollEvent"] = MethodMetadata {2, __hostFunction_NativeFantomCxxSpecJSI_enqueueScrollEvent};
  methodMap_["enqueueModalSizeUpdate"] = MethodMetadata {3, __hostFunction_NativeFantomCxxSpecJSI_enqueueModalSizeUpdate};
  methodMap_["takeMountingManagerLogs"] = MethodMetadata {1, __hostFunction_NativeFantomCxxSpecJSI_takeMountingManagerLogs};
  methodMap_["flushMessageQueue"] = MethodMetadata {0, __hostFunction_NativeFantomCxxSpecJSI_flushMessageQueue};
  methodMap_["flushEventQueue"] = MethodMetadata {0, __hostFunction_NativeFantomCxxSpecJSI_flushEventQueue};
  methodMap_["validateEmptyMessageQueue"] = MethodMetadata {0, __hostFunction_NativeFantomCxxSpecJSI_validateEmptyMessageQueue};
  methodMap_["getRenderedOutput"] = MethodMetadata {2, __hostFunction_NativeFantomCxxSpecJSI_getRenderedOutput};
  methodMap_["reportTestSuiteResultsJSON"] = MethodMetadata {1, __hostFunction_NativeFantomCxxSpecJSI_reportTestSuiteResultsJSON};
  methodMap_["createShadowNodeReferenceCounter"] = MethodMetadata {1, __hostFunction_NativeFantomCxxSpecJSI_createShadowNodeReferenceCounter};
  methodMap_["saveJSMemoryHeapSnapshot"] = MethodMetadata {1, __hostFunction_NativeFantomCxxSpecJSI_saveJSMemoryHeapSnapshot};
}
static jsi::Value __hostFunction_NativeFantomForcedCloneCommitHookCxxSpecJSI_setup(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFantomForcedCloneCommitHookCxxSpecJSI *>(&turboModule)->setup(
    rt
  );
  return jsi::Value::undefined();
}

NativeFantomForcedCloneCommitHookCxxSpecJSI::NativeFantomForcedCloneCommitHookCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("NativeFantomForcedCloneCommitHookCxx", jsInvoker) {
  methodMap_["setup"] = MethodMetadata {0, __hostFunction_NativeFantomForcedCloneCommitHookCxxSpecJSI_setup};
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_compareDocumentPosition(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->compareDocumentPosition(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0]),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : jsi::Value(rt, args[1])
  );
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_getChildNodes(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->getChildNodes(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0])
  );
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_getParentNode(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->getParentNode(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0])
  );
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_isConnected(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->isConnected(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0])
  );
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_getBorderWidth(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->getBorderWidth(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0])
  );
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_getBoundingClientRect(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->getBoundingClientRect(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0]),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asBool()
  );
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_getInnerSize(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->getInnerSize(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0])
  );
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_getScrollPosition(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->getScrollPosition(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0])
  );
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_getScrollSize(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->getScrollSize(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0])
  );
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_getTagName(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->getTagName(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0])
  );
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_getTextContent(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->getTextContent(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0])
  );
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_hasPointerCapture(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->hasPointerCapture(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0]),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_releasePointerCapture(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->releasePointerCapture(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0]),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_setPointerCapture(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->setPointerCapture(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0]),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_getOffset(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->getOffset(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0])
  );
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_linkRootNode(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->linkRootNode(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : jsi::Value(rt, args[1])
  );
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_measure(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->measure(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0]),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_measureInWindow(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->measureInWindow(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0]),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDOMCxxSpecJSI_measureLayout(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDOMCxxSpecJSI *>(&turboModule)->measureLayout(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0]),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : jsi::Value(rt, args[1]),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asObject(rt).asFunction(rt),
    count <= 3 ? throw jsi::JSError(rt, "Expected argument in position 3 to be passed") : args[3].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}

NativeDOMCxxSpecJSI::NativeDOMCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("NativeDOMCxx", jsInvoker) {
  methodMap_["compareDocumentPosition"] = MethodMetadata {2, __hostFunction_NativeDOMCxxSpecJSI_compareDocumentPosition};
  methodMap_["getChildNodes"] = MethodMetadata {1, __hostFunction_NativeDOMCxxSpecJSI_getChildNodes};
  methodMap_["getParentNode"] = MethodMetadata {1, __hostFunction_NativeDOMCxxSpecJSI_getParentNode};
  methodMap_["isConnected"] = MethodMetadata {1, __hostFunction_NativeDOMCxxSpecJSI_isConnected};
  methodMap_["getBorderWidth"] = MethodMetadata {1, __hostFunction_NativeDOMCxxSpecJSI_getBorderWidth};
  methodMap_["getBoundingClientRect"] = MethodMetadata {2, __hostFunction_NativeDOMCxxSpecJSI_getBoundingClientRect};
  methodMap_["getInnerSize"] = MethodMetadata {1, __hostFunction_NativeDOMCxxSpecJSI_getInnerSize};
  methodMap_["getScrollPosition"] = MethodMetadata {1, __hostFunction_NativeDOMCxxSpecJSI_getScrollPosition};
  methodMap_["getScrollSize"] = MethodMetadata {1, __hostFunction_NativeDOMCxxSpecJSI_getScrollSize};
  methodMap_["getTagName"] = MethodMetadata {1, __hostFunction_NativeDOMCxxSpecJSI_getTagName};
  methodMap_["getTextContent"] = MethodMetadata {1, __hostFunction_NativeDOMCxxSpecJSI_getTextContent};
  methodMap_["hasPointerCapture"] = MethodMetadata {2, __hostFunction_NativeDOMCxxSpecJSI_hasPointerCapture};
  methodMap_["releasePointerCapture"] = MethodMetadata {2, __hostFunction_NativeDOMCxxSpecJSI_releasePointerCapture};
  methodMap_["setPointerCapture"] = MethodMetadata {2, __hostFunction_NativeDOMCxxSpecJSI_setPointerCapture};
  methodMap_["getOffset"] = MethodMetadata {1, __hostFunction_NativeDOMCxxSpecJSI_getOffset};
  methodMap_["linkRootNode"] = MethodMetadata {2, __hostFunction_NativeDOMCxxSpecJSI_linkRootNode};
  methodMap_["measure"] = MethodMetadata {2, __hostFunction_NativeDOMCxxSpecJSI_measure};
  methodMap_["measureInWindow"] = MethodMetadata {2, __hostFunction_NativeDOMCxxSpecJSI_measureInWindow};
  methodMap_["measureLayout"] = MethodMetadata {4, __hostFunction_NativeDOMCxxSpecJSI_measureLayout};
}
static jsi::Value __hostFunction_NativeIdleCallbacksCxxSpecJSI_requestIdleCallback(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeIdleCallbacksCxxSpecJSI *>(&turboModule)->requestIdleCallback(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt),
    count <= 1 || args[1].isUndefined() ? std::nullopt : std::make_optional(args[1].asObject(rt))
  );
}
static jsi::Value __hostFunction_NativeIdleCallbacksCxxSpecJSI_cancelIdleCallback(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeIdleCallbacksCxxSpecJSI *>(&turboModule)->cancelIdleCallback(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0])
  );
  return jsi::Value::undefined();
}

NativeIdleCallbacksCxxSpecJSI::NativeIdleCallbacksCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("NativeIdleCallbacksCxx", jsInvoker) {
  methodMap_["requestIdleCallback"] = MethodMetadata {2, __hostFunction_NativeIdleCallbacksCxxSpecJSI_requestIdleCallback};
  methodMap_["cancelIdleCallback"] = MethodMetadata {1, __hostFunction_NativeIdleCallbacksCxxSpecJSI_cancelIdleCallback};
}
static jsi::Value __hostFunction_NativeIntersectionObserverCxxSpecJSI_observe(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeIntersectionObserverCxxSpecJSI *>(&turboModule)->observe(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeIntersectionObserverCxxSpecJSI_unobserve(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeIntersectionObserverCxxSpecJSI *>(&turboModule)->unobserve(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : jsi::Value(rt, args[1])
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeIntersectionObserverCxxSpecJSI_connect(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeIntersectionObserverCxxSpecJSI *>(&turboModule)->connect(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeIntersectionObserverCxxSpecJSI_disconnect(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeIntersectionObserverCxxSpecJSI *>(&turboModule)->disconnect(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeIntersectionObserverCxxSpecJSI_takeRecords(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeIntersectionObserverCxxSpecJSI *>(&turboModule)->takeRecords(
    rt
  );
}

NativeIntersectionObserverCxxSpecJSI::NativeIntersectionObserverCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("NativeIntersectionObserverCxx", jsInvoker) {
  methodMap_["observe"] = MethodMetadata {1, __hostFunction_NativeIntersectionObserverCxxSpecJSI_observe};
  methodMap_["unobserve"] = MethodMetadata {2, __hostFunction_NativeIntersectionObserverCxxSpecJSI_unobserve};
  methodMap_["connect"] = MethodMetadata {1, __hostFunction_NativeIntersectionObserverCxxSpecJSI_connect};
  methodMap_["disconnect"] = MethodMetadata {0, __hostFunction_NativeIntersectionObserverCxxSpecJSI_disconnect};
  methodMap_["takeRecords"] = MethodMetadata {0, __hostFunction_NativeIntersectionObserverCxxSpecJSI_takeRecords};
}
static jsi::Value __hostFunction_NativeMicrotasksCxxSpecJSI_queueMicrotask(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeMicrotasksCxxSpecJSI *>(&turboModule)->queueMicrotask(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}

NativeMicrotasksCxxSpecJSI::NativeMicrotasksCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("NativeMicrotasksCxx", jsInvoker) {
  methodMap_["queueMicrotask"] = MethodMetadata {1, __hostFunction_NativeMicrotasksCxxSpecJSI_queueMicrotask};
}
static jsi::Value __hostFunction_NativeMutationObserverCxxSpecJSI_observe(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeMutationObserverCxxSpecJSI *>(&turboModule)->observe(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeMutationObserverCxxSpecJSI_unobserve(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeMutationObserverCxxSpecJSI *>(&turboModule)->unobserve(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : jsi::Value(rt, args[1])
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeMutationObserverCxxSpecJSI_connect(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeMutationObserverCxxSpecJSI *>(&turboModule)->connect(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeMutationObserverCxxSpecJSI_disconnect(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeMutationObserverCxxSpecJSI *>(&turboModule)->disconnect(
    rt
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeMutationObserverCxxSpecJSI_takeRecords(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeMutationObserverCxxSpecJSI *>(&turboModule)->takeRecords(
    rt
  );
}

NativeMutationObserverCxxSpecJSI::NativeMutationObserverCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("NativeMutationObserverCxx", jsInvoker) {
  methodMap_["observe"] = MethodMetadata {1, __hostFunction_NativeMutationObserverCxxSpecJSI_observe};
  methodMap_["unobserve"] = MethodMetadata {2, __hostFunction_NativeMutationObserverCxxSpecJSI_unobserve};
  methodMap_["connect"] = MethodMetadata {2, __hostFunction_NativeMutationObserverCxxSpecJSI_connect};
  methodMap_["disconnect"] = MethodMetadata {0, __hostFunction_NativeMutationObserverCxxSpecJSI_disconnect};
  methodMap_["takeRecords"] = MethodMetadata {0, __hostFunction_NativeMutationObserverCxxSpecJSI_takeRecords};
}
static jsi::Value __hostFunction_NativePerformanceCxxSpecJSI_now(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePerformanceCxxSpecJSI *>(&turboModule)->now(
    rt
  );
}
static jsi::Value __hostFunction_NativePerformanceCxxSpecJSI_markWithResult(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePerformanceCxxSpecJSI *>(&turboModule)->markWithResult(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 || args[1].isUndefined() ? std::nullopt : std::make_optional(args[1].asNumber())
  );
}
static jsi::Value __hostFunction_NativePerformanceCxxSpecJSI_measureWithResult(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePerformanceCxxSpecJSI *>(&turboModule)->measureWithResult(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber(),
    count <= 2 ? throw jsi::JSError(rt, "Expected argument in position 2 to be passed") : args[2].asNumber(),
    count <= 3 || args[3].isUndefined() ? std::nullopt : std::make_optional(args[3].asNumber()),
    count <= 4 || args[4].isUndefined() ? std::nullopt : std::make_optional(args[4].asString(rt)),
    count <= 5 || args[5].isUndefined() ? std::nullopt : std::make_optional(args[5].asString(rt))
  );
}
static jsi::Value __hostFunction_NativePerformanceCxxSpecJSI_clearMarks(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePerformanceCxxSpecJSI *>(&turboModule)->clearMarks(
    rt,
    count <= 0 || args[0].isUndefined() ? std::nullopt : std::make_optional(args[0].asString(rt))
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePerformanceCxxSpecJSI_clearMeasures(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePerformanceCxxSpecJSI *>(&turboModule)->clearMeasures(
    rt,
    count <= 0 || args[0].isUndefined() ? std::nullopt : std::make_optional(args[0].asString(rt))
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePerformanceCxxSpecJSI_getEntries(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePerformanceCxxSpecJSI *>(&turboModule)->getEntries(
    rt
  );
}
static jsi::Value __hostFunction_NativePerformanceCxxSpecJSI_getEntriesByName(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePerformanceCxxSpecJSI *>(&turboModule)->getEntriesByName(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 || args[1].isNull() || args[1].isUndefined() ? std::nullopt : std::make_optional(args[1].asNumber())
  );
}
static jsi::Value __hostFunction_NativePerformanceCxxSpecJSI_getEntriesByType(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePerformanceCxxSpecJSI *>(&turboModule)->getEntriesByType(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
}
static jsi::Value __hostFunction_NativePerformanceCxxSpecJSI_getEventCounts(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePerformanceCxxSpecJSI *>(&turboModule)->getEventCounts(
    rt
  );
}
static jsi::Value __hostFunction_NativePerformanceCxxSpecJSI_getSimpleMemoryInfo(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePerformanceCxxSpecJSI *>(&turboModule)->getSimpleMemoryInfo(
    rt
  );
}
static jsi::Value __hostFunction_NativePerformanceCxxSpecJSI_getReactNativeStartupTiming(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePerformanceCxxSpecJSI *>(&turboModule)->getReactNativeStartupTiming(
    rt
  );
}
static jsi::Value __hostFunction_NativePerformanceCxxSpecJSI_createObserver(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePerformanceCxxSpecJSI *>(&turboModule)->createObserver(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt).asFunction(rt)
  );
}
static jsi::Value __hostFunction_NativePerformanceCxxSpecJSI_getDroppedEntriesCount(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePerformanceCxxSpecJSI *>(&turboModule)->getDroppedEntriesCount(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0])
  );
}
static jsi::Value __hostFunction_NativePerformanceCxxSpecJSI_observe(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePerformanceCxxSpecJSI *>(&turboModule)->observe(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0]),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePerformanceCxxSpecJSI_disconnect(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePerformanceCxxSpecJSI *>(&turboModule)->disconnect(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0])
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePerformanceCxxSpecJSI_takeRecords(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePerformanceCxxSpecJSI *>(&turboModule)->takeRecords(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : jsi::Value(rt, args[0]),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asBool()
  );
}
static jsi::Value __hostFunction_NativePerformanceCxxSpecJSI_getSupportedPerformanceEntryTypes(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePerformanceCxxSpecJSI *>(&turboModule)->getSupportedPerformanceEntryTypes(
    rt
  );
}

NativePerformanceCxxSpecJSI::NativePerformanceCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("NativePerformanceCxx", jsInvoker) {
  methodMap_["now"] = MethodMetadata {0, __hostFunction_NativePerformanceCxxSpecJSI_now};
  methodMap_["markWithResult"] = MethodMetadata {2, __hostFunction_NativePerformanceCxxSpecJSI_markWithResult};
  methodMap_["measureWithResult"] = MethodMetadata {6, __hostFunction_NativePerformanceCxxSpecJSI_measureWithResult};
  methodMap_["clearMarks"] = MethodMetadata {1, __hostFunction_NativePerformanceCxxSpecJSI_clearMarks};
  methodMap_["clearMeasures"] = MethodMetadata {1, __hostFunction_NativePerformanceCxxSpecJSI_clearMeasures};
  methodMap_["getEntries"] = MethodMetadata {0, __hostFunction_NativePerformanceCxxSpecJSI_getEntries};
  methodMap_["getEntriesByName"] = MethodMetadata {2, __hostFunction_NativePerformanceCxxSpecJSI_getEntriesByName};
  methodMap_["getEntriesByType"] = MethodMetadata {1, __hostFunction_NativePerformanceCxxSpecJSI_getEntriesByType};
  methodMap_["getEventCounts"] = MethodMetadata {0, __hostFunction_NativePerformanceCxxSpecJSI_getEventCounts};
  methodMap_["getSimpleMemoryInfo"] = MethodMetadata {0, __hostFunction_NativePerformanceCxxSpecJSI_getSimpleMemoryInfo};
  methodMap_["getReactNativeStartupTiming"] = MethodMetadata {0, __hostFunction_NativePerformanceCxxSpecJSI_getReactNativeStartupTiming};
  methodMap_["createObserver"] = MethodMetadata {1, __hostFunction_NativePerformanceCxxSpecJSI_createObserver};
  methodMap_["getDroppedEntriesCount"] = MethodMetadata {1, __hostFunction_NativePerformanceCxxSpecJSI_getDroppedEntriesCount};
  methodMap_["observe"] = MethodMetadata {2, __hostFunction_NativePerformanceCxxSpecJSI_observe};
  methodMap_["disconnect"] = MethodMetadata {1, __hostFunction_NativePerformanceCxxSpecJSI_disconnect};
  methodMap_["takeRecords"] = MethodMetadata {2, __hostFunction_NativePerformanceCxxSpecJSI_takeRecords};
  methodMap_["getSupportedPerformanceEntryTypes"] = MethodMetadata {0, __hostFunction_NativePerformanceCxxSpecJSI_getSupportedPerformanceEntryTypes};
}


} // namespace facebook::react
