{"version": 3, "file": "subscriber-polling.js", "sourceRoot": "", "sources": ["../../src.ts/providers/subscriber-polling.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAKxD,SAAS,IAAI,CAAC,GAAQ;IAClB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3C,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,oBAAoB,CAAC,QAA0B,EAAE,KAAoB;IACjF,IAAI,KAAK,KAAK,OAAO,EAAE;QAAE,OAAO,IAAI,sBAAsB,CAAC,QAAQ,CAAC,CAAC;KAAE;IACvE,IAAI,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE;QAAE,OAAO,IAAI,4BAA4B,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;KAAE;IAEzF,MAAM,CAAC,KAAK,EAAE,2BAA2B,EAAE,uBAAuB,EAAE;QAChE,SAAS,EAAE,sBAAsB,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE;KACrD,CAAC,CAAC;AACP,CAAC;AAED,uBAAuB;AAEvB;;;;;GAKG;AACH,MAAM,OAAO,sBAAsB;IAC/B,SAAS,CAAmB;IAC5B,OAAO,CAAgB;IAEvB,SAAS,CAAS;IAElB,iEAAiE;IACjE,2DAA2D;IAC3D,YAAY,CAAS;IAErB;;OAEG;IACH,YAAY,QAA0B;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAI,eAAe,KAAa,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IACxD,IAAI,eAAe,CAAC,KAAa,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC;IAE9D,KAAK,CAAC,KAAK;QACP,IAAI;YACA,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;YAE1D,mDAAmD;YACnD,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,CAAC,EAAE;gBAC1B,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;gBAChC,OAAO;aACV;YAED,6DAA6D;YAE7D,IAAI,WAAW,KAAK,IAAI,CAAC,YAAY,EAAE;gBACnC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC,IAAI,WAAW,EAAE,CAAC,EAAE,EAAE;oBACvD,uBAAuB;oBACvB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;wBAAE,OAAO;qBAAE;oBAErC,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;iBACzC;gBAED,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;aACnC;SAEJ;QAAC,OAAO,KAAK,EAAE;YACZ,6DAA6D;YAC7D,gCAAgC;YAChC,qBAAqB;SACxB;QAED,uBAAuB;QACvB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;YAAE,OAAO;SAAE;QAErC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IACrF,CAAC;IAED,KAAK;QACD,IAAI,IAAI,CAAC,OAAO,EAAE;YAAE,OAAO;SAAE;QAC7B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACjF,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,IAAI;QACA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YAAE,OAAO;SAAE;QAC9B,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,eAAyB;QAC3B,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,eAAe,EAAE;YAAE,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;SAAE;IACpD,CAAC;IAED,MAAM;QACF,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;CACJ;AAGD;;;;;GAKG;AACH,MAAM,OAAO,iBAAiB;IAC1B,SAAS,CAAmB;IAC5B,KAAK,CAAsB;IAC3B,QAAQ,CAAU;IAElB;;OAEG;IACH,YAAY,QAA0B;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,CAAC,WAAmB,EAAE,EAAE;YACjC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC5C,CAAC,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CAAC,WAAmB,EAAE,QAA0B;QACvD,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtD,CAAC;IAED,KAAK;QACD,IAAI,IAAI,CAAC,QAAQ,EAAE;YAAE,OAAO;SAAE;QAC9B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED,IAAI;QACA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAAE,OAAO;SAAE;QAC/B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,eAAyB,IAAU,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACvD,MAAM,KAAW,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;CACnC;AAED,MAAM,OAAO,yBAA0B,SAAQ,iBAAiB;IACnD,IAAI,CAAS;IACtB,UAAU,CAAS;IAEnB,YAAY,QAA0B,EAAE,GAAW;QAC/C,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,eAAyB;QAC3B,IAAI,eAAe,EAAE;YAAE,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;SAAE;QAC9C,KAAK,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,WAAmB,EAAE,QAA0B;QACvD,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,IAAI,KAAK,IAAI,IAAI,EAAE;YAAE,OAAO;SAAE;QAE9B,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC,EAAE;YACxB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;SAClC;aAAM,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE;YACvC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;YACvC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;SAClC;IACL,CAAC;CACJ;AAGD;;;;GAIG;AACH,MAAM,OAAO,uBAAwB,SAAQ,iBAAiB;IAC1D,OAAO,CAAe;IAEtB,YAAY,QAA0B,EAAE,MAAoB;QACxD,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,WAAmB,EAAE,QAA0B;QACvD,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;QACzB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;CACJ;AAED;;;;;GAKG;AACH,MAAM,OAAO,4BAA6B,SAAQ,iBAAiB;IAC/D,KAAK,CAAS;IAEd;;;OAGG;IACH,YAAY,QAA0B,EAAE,IAAY;QAChD,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,WAAmB,EAAE,QAA0B;QACvD,MAAM,EAAE,GAAG,MAAM,QAAQ,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,IAAI,EAAE,EAAE;YAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;SAAE;IAC9C,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,sBAAsB;IAC/B,SAAS,CAAmB;IAC5B,OAAO,CAAc;IACrB,OAAO,CAAsB;IAE7B,QAAQ,CAAU;IAElB,iEAAiE;IACjE,2DAA2D;IAC3D,YAAY,CAAS;IAErB;;;OAGG;IACH,YAAY,QAA0B,EAAE,MAAmB;QACvD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,WAAmB;QAC3B,+CAA+C;QAC/C,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,CAAC,EAAE;YAAE,OAAO;SAAE;QAEzC,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAClC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACzC,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC;QAE7B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAElD,6DAA6D;QAC7D,4DAA4D;QAC5D,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACnB,IAAI,IAAI,CAAC,YAAY,GAAG,WAAW,GAAG,EAAE,EAAE;gBACtC,IAAI,CAAC,YAAY,GAAG,WAAW,GAAG,EAAE,CAAC;aACxC;YACD,OAAO;SACV;QAED,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;YACpB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YAEvC,wDAAwD;YACxD,wDAAwD;YACxD,oDAAoD;YACpD,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,WAAW,CAAC;SACvC;IACL,CAAC;IAED,KAAK;QACD,IAAI,IAAI,CAAC,QAAQ,EAAE;YAAE,OAAO;SAAE;QAC9B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,CAAC,EAAE;YAC1B,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;gBACjD,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;YACpC,CAAC,CAAC,CAAC;SACN;QACD,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED,IAAI;QACA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAAE,OAAO;SAAE;QAC/B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,eAAyB;QAC3B,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,eAAe,EAAE;YAAE,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;SAAE;IACpD,CAAC;IAED,MAAM;QACF,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;CACJ"}