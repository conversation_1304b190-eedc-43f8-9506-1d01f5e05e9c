{"version": 3, "file": "mode-cfb.js", "sourceRoot": "", "sources": ["../src.ts/mode-cfb.ts"], "names": [], "mappings": ";AAAA,kBAAkB;;;;;;;;;;;;;;;AAElB,uCAA4C;AAE5C,MAAa,GAAI,SAAQ,yBAAe;IAMtC,YAAY,GAAe,EAAE,EAAe,EAAE,cAAsB,CAAC;QACnE,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;;QANzB,0BAAgB;QAChB,qCAA2B;QAOzB,+DAA+D;QAC/D,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;YACvD,MAAM,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;SAC5C;QAED,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC5B,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE;SACtD,CAAC,CAAC;QAEH,IAAI,EAAE,EAAE;YACN,IAAI,EAAE,CAAC,MAAM,GAAG,EAAE,EAAE;gBAClB,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;aAC3D;YACD,uBAAA,IAAI,WAAO,IAAI,UAAU,CAAC,EAAE,CAAC,MAAA,CAAC;SAC/B;aAAM;YACL,uBAAA,IAAI,WAAO,IAAI,UAAU,CAAC,EAAE,CAAC,MAAA,CAAC;SAC/B;QAED,uBAAA,IAAI,sBAAkB,IAAI,CAAC,EAAE,MAAA,CAAC;IAChC,CAAC;IAED,IAAI,EAAE,KAAiB,OAAO,IAAI,UAAU,CAAC,uBAAA,IAAI,eAAI,CAAC,CAAC,CAAC,CAAC;IAUzD,OAAO,CAAC,SAAqB;QAC3B,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE;YAC3C,MAAM,IAAI,SAAS,CAAC,gEAAgE,CAAC,CAAC;SACvF;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QAEzC,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;QAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,WAAW,EAAE;YACvD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,uBAAA,IAAI,0BAAe,CAAC,CAAC;YACzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;gBACpC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;aACpC;YAED,uBAAA,IAAI,kCAAO,MAAX,IAAI,EAAQ,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SACrC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,OAAO,CAAC,UAAsB;QAC5B,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE;YAC1C,MAAM,IAAI,SAAS,CAAC,iEAAiE,CAAC,CAAC;SAC1F;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QAEzC,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;QAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,WAAW,EAAE;YACtD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,uBAAA,IAAI,0BAAe,CAAC,CAAC;YACzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;gBACpC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;aACnC;YAED,uBAAA,IAAI,kCAAO,MAAX,IAAI,EAAQ,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SACrC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AAjFD,kBAiFC;8HAjDQ,IAAgB;IACrB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;IAEzC,qBAAqB;IACrB,uBAAA,IAAI,0BAAe,CAAC,GAAG,CAAC,uBAAA,IAAI,0BAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;IACnE,uBAAA,IAAI,0BAAe,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,EAAE,GAAG,WAAW,CAAC,CAAC;AAC3E,CAAC"}