{"name": "nextgen-2025-mobile", "version": "2.0.0", "description": "NextGen 2025 - AI原生数字生活操作系统 | AI-Native Digital Life Operating System", "private": true, "scripts": {"dev": "next dev -p 3006", "build": "next build", "start": "next start -p 3002", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@mediapipe/tasks-vision": "^0.10.8", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "clsx": "^2.0.0", "critters": "^0.0.23", "date-fns": "^3.0.6", "ethers": "^6.9.0", "framer-motion": "^10.16.16", "lucide-react": "^0.525.0", "next": "^15.4.0-canary.121", "openai": "^4.24.1", "peerjs": "^1.5.2", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.5.3", "react-spring": "^9.7.3", "react-use-gesture": "^9.1.3", "simple-peer": "^9.11.1", "socket.io-client": "^4.7.4", "swiper": "^11.0.5", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "web3": "^4.3.0", "zustand": "^4.4.7"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "eslint": "^8.56.0", "eslint-config-next": "^15.4.0-canary.121"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["ai-native", "digital-life", "operating-system", "engineering", "construction", "ar-vr", "web3", "creator-economy", "metaverse", "iot", "nextjs", "react", "typescript"], "author": "NextGen 2025 Team", "license": "MIT"}