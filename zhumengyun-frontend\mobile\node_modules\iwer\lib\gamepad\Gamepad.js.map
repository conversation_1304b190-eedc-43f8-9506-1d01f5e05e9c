{"version": 3, "file": "Gamepad.js", "sourceRoot": "", "sources": ["../../src/gamepad/Gamepad.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAE1C,MAAM,CAAN,IAAY,kBAIX;AAJD,WAAY,kBAAkB;IAC7B,+BAAS,CAAA;IACT,2CAAqB,CAAA;IACrB,gDAA0B,CAAA;AAC3B,CAAC,EAJW,kBAAkB,KAAlB,kBAAkB,QAI7B;AAmBD,MAAM,OAAO,aAAa;IAWzB,YACC,IAAoC,EACpC,YAAyC;QAEzC,IAAI,CAAC,SAAS,CAAC,GAAG;YACjB,IAAI;YACJ,YAAY;YACZ,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,CAAC;YACR,cAAc,EAAE,CAAC;YACjB,YAAY,EAAE,IAAI;SAClB,CAAC;IACH,CAAC;IAED,IAAI,OAAO;QACV,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;YACtC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC;SAC/B;aAAM;YACN,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;SACjC;IACF,CAAC;IAED,IAAI,OAAO;QACV,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;YACtC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC;SAC/B;aAAM;YACN,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;SAC/C;IACF,CAAC;IAED,IAAI,KAAK;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC;IAC9B,CAAC;CACD;AAED,MAAM,OAAO,kBAAkB;IAA/B;QACC,YAAO,GAAG,KAAK,CAAC;QAChB,YAAO,GAAG,KAAK,CAAC;QAChB,UAAK,GAAG,CAAC,CAAC;IACX,CAAC;CAAA;AAED,MAAM,OAAO,OAAO;IAkBnB,YACC,aAA4B,EAC5B,KAAa,EAAE,EACf,QAAgB,CAAC,CAAC;QAElB,IAAI,CAAC,SAAS,CAAC,GAAG;YACjB,EAAE;YACF,KAAK;YACL,SAAS,EAAE,KAAK;YAChB,SAAS,EAAE,WAAW,CAAC,GAAG,EAAE;YAC5B,OAAO,EAAE,aAAa,CAAC,OAAO;YAC9B,UAAU,EAAE,EAAE;YACd,eAAe,EAAE,EAAE;YACnB,OAAO,EAAE,EAAE;YACX,YAAY,EAAE,EAAE;YAChB,eAAe,EAAE,EAAE;SACnB,CAAC;QACF,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;;YAC9C,IAAI,YAAY,KAAK,IAAI,EAAE;gBAC1B,IAAI,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC3C;iBAAM;gBACN,IAAI,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;gBACtD,IAAI,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,IAAI,aAAa,CAC9D,YAAY,CAAC,IAAI,EACjB,MAAA,YAAY,CAAC,YAAY,mCAAI,IAAI,CACjC,CAAC;aACF;QACF,CAAC,CAAC,CAAC;QACH,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACzC,IAAI,UAAU,KAAK,IAAI,EAAE;gBACxB,IAAI,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACxC;iBAAM;gBACN,IAAI,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;gBACnE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;oBAC5C,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;iBACxD;aACD;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,IAAI,EAAE;QACL,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;IAC3B,CAAC;IAED,IAAI,KAAK;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC;IAClC,CAAC;IAED,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC;IAClC,CAAC;IAED,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC;IAChC,CAAC;IAED,IAAI,IAAI;QACP,MAAM,IAAI,GAAsB,EAAE,CAAC;QACnC,IAAI,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;YAC3C,IAAI,EAAE,KAAK,IAAI,EAAE;gBAChB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAChB;iBAAM;gBACN,MAAM,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC9C,MAAM,QAAQ,GAAG,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC7C,IAAI,CAAC,IAAI;gBACR,kDAAkD;gBAClD,QAAQ,KAAK,QAAQ;oBACpB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;oBACnC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CACpC,CAAC;aACF;QACF,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACb,CAAC;IAED,IAAI,OAAO;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CACjD,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CACvE,CAAC;IACH,CAAC;IAED,IAAI,eAAe;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC;IACxC,CAAC;IAED,IAAI,iBAAiB;QACpB,OAAO,IAAI,CAAC;IACb,CAAC;CACD"}