"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("three"),t=require("react"),n=require("@react-three/fiber"),a=require("./useIntersect.cjs.js"),i=require("./Fbo.cjs.js"),l=require("./RenderTexture.cjs.js"),o=require("./shaderMaterial.cjs.js"),u=require("three-stdlib"),s=require("../helpers/constants.cjs.js");function v(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function c(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var d=v(e),m=c(r),f=c(t);const g=o.shaderMaterial({blur:0,map:null,sdf:null,blend:0,size:0,resolution:new m.Vector2},"varying vec2 vUv;\n   void main() {\n     gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n     vUv = uv;\n   }",`uniform sampler2D sdf;\n   uniform sampler2D map;\n   uniform float blur;\n   uniform float size;\n   uniform float time;\n   uniform vec2 resolution;\n   varying vec2 vUv;\n   #include <packing>\n   void main() {\n     vec2 uv = gl_FragCoord.xy / resolution.xy;\n     vec4 t = texture2D(map, uv);\n     float k = blur;\n     float d = texture2D(sdf, vUv).r/size;\n     float alpha = 1.0 - smoothstep(0.0, 1.0, clamp(d/k + 1.0, 0.0, 1.0));\n     gl_FragColor = vec4(t.rgb, blur == 0.0 ? t.a : t.a * alpha);\n     #include <tonemapping_fragment>\n     #include <${s.version>=154?"colorspace_fragment":"encodings_fragment"}>\n   }`),p=f.forwardRef((({children:e,events:r,blur:t=0,eventPriority:o=0,renderPriority:u=0,worldUnits:s=!1,resolution:v=512,...c},p)=>{n.extend({PortalMaterialImpl:g});const h=f.useRef(null),{scene:y,gl:b,size:w,viewport:M,setEvents:U}=n.useThree(),T=i.useFBO(v,v),[R,S]=f.useState(0);n.useFrame((()=>{const e=h.current.blend>0?Math.max(1,u):0;R!==e&&S(e)})),f.useEffect((()=>{void 0!==r&&U({enabled:!r})}),[r]);const[_,D]=f.useState(!0),j=a.useIntersect(D);f.useLayoutEffect((()=>{var e;j.current=null==(e=h.current)?void 0:e.__r3f.parent}),[]),f.useLayoutEffect((()=>{if(j.current&&t&&null===h.current.sdf){const e=new m.Mesh(j.current.geometry,new m.MeshBasicMaterial),r=(new m.Box3).setFromBufferAttribute(e.geometry.attributes.position),t=new m.OrthographicCamera(r.min.x*(1+2/v),r.max.x*(1+2/v),r.max.y*(1+2/v),r.min.y*(1+2/v),.1,1e3);t.position.set(0,0,1),t.lookAt(0,0,0),b.setRenderTarget(T),b.render(e,t);const n=F(v,v,b)(T.texture),a=new Float32Array(v*v);b.readRenderTargetPixels(n,0,0,v,v,a);let i=1/0;for(let e=0;e<a.length;e++)a[e]<i&&(i=a[e]);i=-i,h.current.size=i,h.current.sdf=n.texture,b.setRenderTarget(null)}}),[v,t]),f.useImperativeHandle(p,(()=>h.current));const P=f.useCallback(((e,r,t)=>{var n;if(!j.current)return!1;if(r.pointer.set(e.offsetX/r.size.width*2-1,-e.offsetY/r.size.height*2+1),r.raycaster.setFromCamera(r.pointer,r.camera),0===(null==(n=h.current)?void 0:n.blend)){const[e]=r.raycaster.intersectObject(j.current);if(!e)return r.raycaster.camera=void 0,!1}}),[]);return f.createElement("portalMaterialImpl",d.default({ref:h,blur:t,blend:0,resolution:[w.width*M.dpr,w.height*M.dpr],attach:"material"},c),f.createElement(l.RenderTexture,{attach:"map",frames:_?1/0:0,eventPriority:o,renderPriority:u,compute:P},e,f.createElement(x,{events:r,rootScene:y,priority:R,material:h,worldUnits:s})))}));function x({events:e,rootScene:r,material:t,priority:a,worldUnits:l}){const o=n.useThree((e=>e.scene)),v=n.useThree((e=>e.setEvents)),c=i.useFBO(),d=i.useFBO();f.useLayoutEffect((()=>{o.matrixAutoUpdate=!1}),[]),f.useEffect((()=>{void 0!==e&&v({enabled:e})}),[e]);const[g,p]=f.useMemo((()=>{const e={value:0};return[new u.FullScreenQuad(new m.ShaderMaterial({uniforms:{a:{value:c.texture},b:{value:d.texture},blend:e},vertexShader:"\n          varying vec2 vUv;\n          void main() {\n            vUv = uv;\n            gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n          }",fragmentShader:`\n          uniform sampler2D a;\n          uniform sampler2D b;\n          uniform float blend;\n          varying vec2 vUv;\n          #include <packing>\n          void main() {\n            vec4 ta = texture2D(a, vUv);\n            vec4 tb = texture2D(b, vUv);\n            gl_FragColor = mix(tb, ta, blend);\n            #include <tonemapping_fragment>\n            #include <${s.version>=154?"colorspace_fragment":"encodings_fragment"}>\n          }`})),e]}),[]);return n.useFrame((e=>{var n;let i=null==t||null==(n=t.current)?void 0:n.__r3f.parent;if(i){var u,s,v,m;if(l)o.matrixWorld.identity();else a&&1===(null==(u=t.current)?void 0:u.blend)&&i.updateWorldMatrix(!0,!1),o.matrixWorld.copy(i.matrixWorld);if(a)(null==(s=t.current)?void 0:s.blend)>0&&(null==(v=t.current)?void 0:v.blend)<1?(p.value=t.current.blend,e.gl.setRenderTarget(c),e.gl.render(o,e.camera),e.gl.setRenderTarget(d),e.gl.render(r,e.camera),e.gl.setRenderTarget(null),g.render(e.gl)):1===(null==(m=t.current)?void 0:m.blend)&&e.gl.render(o,e.camera)}}),a),f.createElement(f.Fragment,null)}const F=(e,r,t)=>{let n=new m.WebGLRenderTarget(e,r,{minFilter:m.LinearMipmapLinearFilter,magFilter:m.LinearFilter,type:m.FloatType,format:m.RedFormat,generateMipmaps:!0}),a=new m.WebGLRenderTarget(e,r,{minFilter:m.NearestFilter,magFilter:m.NearestFilter}),i=new m.WebGLRenderTarget(e,r,{minFilter:m.NearestFilter,magFilter:m.NearestFilter}),l=new m.WebGLRenderTarget(e,r,{minFilter:m.NearestFilter,magFilter:m.NearestFilter}),o=new m.WebGLRenderTarget(e,r,{minFilter:m.NearestFilter,magFilter:m.NearestFilter}),s=new m.WebGLRenderTarget(e,r,{minFilter:m.NearestFilter,magFilter:m.NearestFilter,type:m.FloatType,format:m.RedFormat}),v=new m.WebGLRenderTarget(e,r,{minFilter:m.NearestFilter,magFilter:m.NearestFilter,type:m.FloatType,format:m.RedFormat});const c=new u.FullScreenQuad(new m.ShaderMaterial({uniforms:{tex:{value:null}},vertexShader:"\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }",fragmentShader:"\n        uniform sampler2D tex;\n        varying vec2 vUv;\n        #include <packing>\n        void main() {\n          gl_FragColor = pack2HalfToRGBA(vUv * (round(texture2D(tex, vUv).x)));\n        }"})),d=new u.FullScreenQuad(new m.ShaderMaterial({uniforms:{tex:{value:null}},vertexShader:"\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }",fragmentShader:"\n        uniform sampler2D tex;\n        varying vec2 vUv;\n        #include <packing>\n        void main() {\n          gl_FragColor = pack2HalfToRGBA(vUv * (1.0 - round(texture2D(tex, vUv).x)));\n        }"})),f=new u.FullScreenQuad(new m.ShaderMaterial({uniforms:{tex:{value:null},offset:{value:0},level:{value:0},maxSteps:{value:0}},vertexShader:"\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }",fragmentShader:`\n        varying vec2 vUv;\n        uniform sampler2D tex;\n        uniform float offset;\n        uniform float level;\n        uniform float maxSteps;\n        #include <packing>\n        void main() {\n          float closestDist = 9999999.9;\n          vec2 closestPos = vec2(0.0);\n          for (float x = -1.0; x <= 1.0; x += 1.0) {\n            for (float y = -1.0; y <= 1.0; y += 1.0) {\n              vec2 voffset = vUv;\n              voffset += vec2(x, y) * vec2(${1/e}, ${1/r}) * offset;\n              vec2 pos = unpackRGBATo2Half(texture2D(tex, voffset));\n              float dist = distance(pos.xy, vUv);\n              if(pos.x != 0.0 && pos.y != 0.0 && dist < closestDist) {\n                closestDist = dist;\n                closestPos = pos;\n              }\n            }\n          }\n          gl_FragColor = pack2HalfToRGBA(closestPos);\n        }`})),g=new u.FullScreenQuad(new m.ShaderMaterial({uniforms:{tex:{value:null},size:{value:new m.Vector2(e,r)}},vertexShader:"\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }",fragmentShader:"\n        varying vec2 vUv;\n        uniform sampler2D tex;\n        uniform vec2 size;\n        #include <packing>\n        void main() {\n          gl_FragColor = vec4(distance(size * unpackRGBATo2Half(texture2D(tex, vUv)), size * vUv), 0.0, 0.0, 0.0);\n        }"})),p=new u.FullScreenQuad(new m.ShaderMaterial({uniforms:{inside:{value:v.texture},outside:{value:s.texture},tex:{value:null}},vertexShader:"\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }",fragmentShader:"\n        varying vec2 vUv;\n        uniform sampler2D inside;\n        uniform sampler2D outside;\n        uniform sampler2D tex;\n        #include <packing>\n        void main() {\n          float i = texture2D(inside, vUv).x;\n          float o =texture2D(outside, vUv).x;\n          if (texture2D(tex, vUv).x == 0.0) {\n            gl_FragColor = vec4(o, 0.0, 0.0, 0.0);\n          } else {\n            gl_FragColor = vec4(-i, 0.0, 0.0, 0.0);\n          }\n        }"}));return u=>{let x=n;u.minFilter=m.NearestFilter,u.magFilter=m.NearestFilter,c.material.uniforms.tex.value=u,t.setRenderTarget(a),c.render(t);const F=Math.ceil(Math.log(Math.max(e,r))/Math.log(2));let h=a,y=null;for(let e=0;e<F;e++){const r=Math.pow(2,F-e-1);y=h===a?l:a,f.material.uniforms.level.value=e,f.material.uniforms.maxSteps.value=F,f.material.uniforms.offset.value=r,f.material.uniforms.tex.value=h.texture,t.setRenderTarget(y),f.render(t),h=y}t.setRenderTarget(s),g.material.uniforms.tex.value=y.texture,g.render(t),d.material.uniforms.tex.value=u,t.setRenderTarget(i),d.render(t),h=i;for(let e=0;e<F;e++){const r=Math.pow(2,F-e-1);y=h===i?o:i,f.material.uniforms.level.value=e,f.material.uniforms.maxSteps.value=F,f.material.uniforms.offset.value=r,f.material.uniforms.tex.value=h.texture,t.setRenderTarget(y),f.render(t),h=y}return t.setRenderTarget(v),g.material.uniforms.tex.value=y.texture,g.render(t),t.setRenderTarget(x),p.material.uniforms.tex.value=u,p.render(t),t.setRenderTarget(null),x}};exports.MeshPortalMaterial=p;
