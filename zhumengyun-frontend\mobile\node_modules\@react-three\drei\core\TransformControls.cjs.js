"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("@react-three/fiber"),r=require("react"),n=require("three"),o=require("three-stdlib");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function s(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var a=u(e),c=s(r),i=s(n);const f=c.forwardRef((({children:e,domElement:r,onChange:n,onMouseDown:u,onMouseUp:s,onObjectChange:f,object:l,makeDefault:d,camera:h,enabled:m,axis:v,mode:E,translationSnap:b,rotationSnap:g,scaleSnap:p,space:L,size:j,showX:w,showY:y,showZ:O,...T},D)=>{const C=t.useThree((e=>e.controls)),R=t.useThree((e=>e.gl)),S=t.useThree((e=>e.events)),q=t.useThree((e=>e.camera)),x=t.useThree((e=>e.invalidate)),M=t.useThree((e=>e.get)),_=t.useThree((e=>e.set)),z=h||q,P=r||S.connected||R.domElement,U=c.useMemo((()=>new o.TransformControls(z,P)),[z,P]),k=c.useRef(null);c.useLayoutEffect((()=>(l?U.attach(l instanceof i.Object3D?l:l.current):k.current instanceof i.Object3D&&U.attach(k.current),()=>{U.detach()})),[l,e,U]),c.useEffect((()=>{if(C){const e=e=>C.enabled=!e.value;return U.addEventListener("dragging-changed",e),()=>U.removeEventListener("dragging-changed",e)}}),[U,C]);const X=c.useRef(),Y=c.useRef(),Z=c.useRef(),F=c.useRef();return c.useLayoutEffect((()=>{X.current=n}),[n]),c.useLayoutEffect((()=>{Y.current=u}),[u]),c.useLayoutEffect((()=>{Z.current=s}),[s]),c.useLayoutEffect((()=>{F.current=f}),[f]),c.useEffect((()=>{const e=e=>{x(),null==X.current||X.current(e)},t=e=>null==Y.current?void 0:Y.current(e),r=e=>null==Z.current?void 0:Z.current(e),n=e=>null==F.current?void 0:F.current(e);return U.addEventListener("change",e),U.addEventListener("mouseDown",t),U.addEventListener("mouseUp",r),U.addEventListener("objectChange",n),()=>{U.removeEventListener("change",e),U.removeEventListener("mouseDown",t),U.removeEventListener("mouseUp",r),U.removeEventListener("objectChange",n)}}),[x,U]),c.useEffect((()=>{if(d){const e=M().controls;return _({controls:U}),()=>_({controls:e})}}),[d,U]),c.createElement(c.Fragment,null,c.createElement("primitive",{ref:D,object:U,enabled:m,axis:v,mode:E,translationSnap:b,rotationSnap:g,scaleSnap:p,space:L,size:j,showX:w,showY:y,showZ:O}),c.createElement("group",a.default({ref:k},T),e))}));exports.TransformControls=f;
