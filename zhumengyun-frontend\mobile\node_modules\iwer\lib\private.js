/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
export const P_ACTION_PLAYER = Symbol('@iwer/action-player');
export const P_ACTION_RECORDER = Symbol('@iwer/action-recorder');
export const P_ANCHOR = Symbol('@iwer/xr-anchor');
export const P_CONTROLLER = Symbol('@iwer/xr-controller');
export const P_DEVICE = Symbol('@iwer/xr-device');
export const P_HAND_INPUT = Symbol('@iwer/xr-hand-input');
export const P_TRACKED_INPUT = Symbol('@iwer/xr-tracked-input');
export const P_FRAME = Symbol('@iwer/xr-frame');
export const P_GAMEPAD = Symbol('@iwer/gamepad');
export const P_SYSTEM = Symbol('@iwer/xr-system');
export const P_INPUT_SOURCE = Symbol('@iwer/xr-input-source');
export const P_WEBGL_LAYER = Symbol('@iwer/xr-webgl-layer');
export const P_MESH = Symbol('@iwer/xr-mesh');
export const P_PLANE = Symbol('@iwer/xr-plane');
export const P_JOINT_POSE = Symbol('@iwer/xr-joint-pose');
export const P_POSE = Symbol('@iwer/xr-pose');
export const P_VIEWER_POSE = Symbol('@iwer/xr-viewer-pose');
export const P_RIGID_TRANSFORM = Symbol('@iwer/xr-rigid-transform');
export const P_RENDER_STATE = Symbol('@iwer/xr-render-state');
export const P_SESSION = Symbol('@iwer/xr-session');
export const P_JOINT_SPACE = Symbol('@iwer/xr-joint-space');
export const P_REF_SPACE = Symbol('@iwer/xr-reference-space');
export const P_SPACE = Symbol('@iwer/xr-space');
export const P_VIEW = Symbol('@iwer/xr-view');
export const P_VIEWPORT = Symbol('@iwer/xr-viewport');
export const P_RAY = Symbol('@iwer/xr-ray');
export const P_HIT_TEST = Symbol('@iwer/xr-hit-test');
//# sourceMappingURL=private.js.map