import*as i from"../../core/i18n/i18n.js";import*as n from"../../ui/legacy/legacy.js";let a;const t={animations:"Animations",showAnimations:"Show Animations"},o=i.i18n.registerUIStrings("panels/animation/animation-meta.ts",t),e=i.i18n.getLazilyComputedLocalizedString.bind(void 0,o);n.ViewManager.registerViewExtension({location:"drawer-view",id:"animations",title:e(t.animations),commandPrompt:e(t.showAnimations),persistence:"closeable",order:0,loadView:async()=>(await async function(){return a||(a=await import("./animation.js")),a}()).AnimationTimeline.AnimationTimeline.instance()});
