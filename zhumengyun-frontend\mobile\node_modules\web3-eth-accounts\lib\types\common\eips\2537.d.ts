declare const _default: {
    name: string;
    number: number;
    comment: string;
    url: string;
    status: string;
    minimumHardfork: string;
    gasConfig: {};
    gasPrices: {
        Bls12381G1AddGas: {
            v: number;
            d: string;
        };
        Bls12381G1MulGas: {
            v: number;
            d: string;
        };
        Bls12381G2AddGas: {
            v: number;
            d: string;
        };
        Bls12381G2MulGas: {
            v: number;
            d: string;
        };
        Bls12381PairingBaseGas: {
            v: number;
            d: string;
        };
        Bls12381PairingPerPairGas: {
            v: number;
            d: string;
        };
        Bls12381MapG1Gas: {
            v: number;
            d: string;
        };
        Bls12381MapG2Gas: {
            v: number;
            d: string;
        };
        Bls12381MultiExpGasDiscount: {
            v: number[][];
            d: string;
        };
    };
    vm: {};
    pow: {};
};
export default _default;
//# sourceMappingURL=2537.d.ts.map