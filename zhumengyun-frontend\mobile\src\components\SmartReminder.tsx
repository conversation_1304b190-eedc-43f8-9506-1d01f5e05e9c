'use client'

import { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface Reminder {
  id: string
  type: 'learning' | 'task' | 'social' | 'health' | 'work' | 'creative'
  title: string
  description: string
  icon: string
  priority: 'low' | 'medium' | 'high'
  scheduledTime: Date
  isRecurring: boolean
  recurringPattern?: 'daily' | 'weekly' | 'monthly'
  actionUrl?: string
  metadata?: {
    progress?: number
    relatedContent?: string[]
    estimatedDuration?: number
  }
}

interface SmartReminderProps {
  userProfile?: any
  currentPage?: string
  onReminderAction?: (reminder: Reminder) => void
}

export default function SmartReminder({ 
  userProfile, 
  currentPage, 
  onReminderAction 
}: SmartReminderProps) {
  const [activeReminders, setActiveReminders] = useState<Reminder[]>([])
  const [showReminder, setShowReminder] = useState(false)
  const [currentReminder, setCurrentReminder] = useState<Reminder | null>(null)

  // 生成智能提醒
  const generateSmartReminders = useCallback(() => {
    const now = new Date()
    const reminders: Reminder[] = []

    // 基于用户画像生成个性化提醒
    if (userProfile?.learningNeeds?.length > 0) {
      reminders.push({
        id: 'learning-daily',
        type: 'learning',
        title: '今日学习计划',
        description: '继续您的技能提升之旅',
        icon: '📚',
        priority: 'medium',
        scheduledTime: new Date(now.getTime() + 30 * 60 * 1000), // 30分钟后
        isRecurring: true,
        recurringPattern: 'daily',
        actionUrl: '/engineering',
        metadata: {
          progress: 65,
          estimatedDuration: 20
        }
      })
    }

    // 基于使用习惯生成提醒
    if (userProfile?.preferredScenarios?.includes('work')) {
      reminders.push({
        id: 'work-focus',
        type: 'work',
        title: '专注工作时间',
        description: '开启专注模式，提升工作效率',
        icon: '💼',
        priority: 'high',
        scheduledTime: new Date(now.getTime() + 60 * 60 * 1000), // 1小时后
        isRecurring: true,
        recurringPattern: 'daily',
        actionUrl: '/creator',
        metadata: {
          estimatedDuration: 90
        }
      })
    }

    // 基于当前页面生成上下文提醒
    if (currentPage === '/creator') {
      reminders.push({
        id: 'creative-inspiration',
        type: 'creative',
        title: '创作灵感时间',
        description: '查看今日热门创作模板和AI工具',
        icon: '🎨',
        priority: 'medium',
        scheduledTime: new Date(now.getTime() + 15 * 60 * 1000), // 15分钟后
        isRecurring: false,
        actionUrl: '/creator?tab=templates'
      })
    }

    // 社交互动提醒
    if (userProfile?.preferredScenarios?.includes('social')) {
      reminders.push({
        id: 'social-check',
        type: 'social',
        title: '社区互动',
        description: '查看好友动态和社区讨论',
        icon: '👥',
        priority: 'low',
        scheduledTime: new Date(now.getTime() + 2 * 60 * 60 * 1000), // 2小时后
        isRecurring: true,
        recurringPattern: 'daily',
        actionUrl: '/social'
      })
    }

    // 健康提醒
    reminders.push({
      id: 'health-break',
      type: 'health',
      title: '休息提醒',
      description: '您已经使用了1小时，建议休息一下',
      icon: '🧘',
      priority: 'medium',
      scheduledTime: new Date(now.getTime() + 60 * 60 * 1000), // 1小时后
      isRecurring: true,
      recurringPattern: 'daily'
    })

    return reminders
  }, [userProfile, currentPage])

  // 检查是否有需要显示的提醒
  const checkReminders = useCallback(() => {
    const now = new Date()
    const reminders = generateSmartReminders()
    
    const dueReminders = reminders.filter(reminder => {
      const timeDiff = reminder.scheduledTime.getTime() - now.getTime()
      return timeDiff <= 0 && timeDiff > -5 * 60 * 1000 // 5分钟容错
    })

    if (dueReminders.length > 0 && !showReminder) {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      const sortedReminders = dueReminders.sort((a, b) => 
        priorityOrder[b.priority] - priorityOrder[a.priority]
      )
      
      setCurrentReminder(sortedReminders[0])
      setActiveReminders(sortedReminders)
      setShowReminder(true)
    }
  }, [generateSmartReminders, showReminder])

  // 定期检查提醒
  useEffect(() => {
    const interval = setInterval(checkReminders, 60 * 1000) // 每分钟检查一次
    checkReminders() // 立即检查一次
    
    return () => clearInterval(interval)
  }, [checkReminders])

  const handleReminderAction = (action: 'accept' | 'dismiss' | 'snooze') => {
    if (!currentReminder) return

    switch (action) {
      case 'accept':
        onReminderAction?.(currentReminder)
        if (currentReminder.actionUrl) {
          // 这里可以添加路由跳转逻辑
          console.log('Navigate to:', currentReminder.actionUrl)
        }
        break
      case 'snooze':
        // 延迟10分钟
        const snoozeTime = new Date(Date.now() + 10 * 60 * 1000)
        const snoozedReminder = { ...currentReminder, scheduledTime: snoozeTime }
        setActiveReminders(prev => [...prev.filter(r => r.id !== currentReminder.id), snoozedReminder])
        break
      case 'dismiss':
        setActiveReminders(prev => prev.filter(r => r.id !== currentReminder.id))
        break
    }

    setShowReminder(false)
    setCurrentReminder(null)

    // 如果还有其他提醒，显示下一个
    setTimeout(() => {
      const remaining = activeReminders.filter(r => r.id !== currentReminder.id)
      if (remaining.length > 0) {
        setCurrentReminder(remaining[0])
        setShowReminder(true)
      }
    }, 1000)
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'from-red-500 to-orange-500'
      case 'medium': return 'from-blue-500 to-purple-500'
      case 'low': return 'from-green-500 to-teal-500'
      default: return 'from-gray-500 to-gray-600'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'learning': return 'text-blue-400'
      case 'work': return 'text-purple-400'
      case 'creative': return 'text-pink-400'
      case 'social': return 'text-green-400'
      case 'health': return 'text-yellow-400'
      default: return 'text-gray-400'
    }
  }

  return (
    <AnimatePresence>
      {showReminder && currentReminder && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          className="fixed bottom-20 left-4 right-4 z-40"
        >
          <div className={`bg-gradient-to-r ${getPriorityColor(currentReminder.priority)} p-0.5 rounded-2xl`}>
            <div className="bg-gray-900 rounded-2xl p-4">
              <div className="flex items-start space-x-3">
                <div className="text-3xl">{currentReminder.icon}</div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="text-white font-bold text-sm">{currentReminder.title}</h3>
                    <span className={`text-xs px-2 py-1 rounded-full bg-gray-800 ${getTypeColor(currentReminder.type)}`}>
                      {currentReminder.type}
                    </span>
                  </div>
                  <p className="text-gray-400 text-xs mb-3">{currentReminder.description}</p>
                  
                  {currentReminder.metadata?.progress && (
                    <div className="mb-3">
                      <div className="flex justify-between text-xs text-gray-400 mb-1">
                        <span>进度</span>
                        <span>{currentReminder.metadata.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-1.5">
                        <div 
                          className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                          style={{ width: `${currentReminder.metadata.progress}%` }}
                        />
                      </div>
                    </div>
                  )}

                  {currentReminder.metadata?.estimatedDuration && (
                    <div className="flex items-center space-x-1 text-xs text-gray-400 mb-3">
                      <span>⏱️</span>
                      <span>预计 {currentReminder.metadata.estimatedDuration} 分钟</span>
                    </div>
                  )}

                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleReminderAction('accept')}
                      className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-3 rounded-lg text-xs font-medium transition-colors"
                    >
                      立即开始
                    </button>
                    <button
                      onClick={() => handleReminderAction('snooze')}
                      className="bg-gray-700 hover:bg-gray-600 text-gray-300 py-2 px-3 rounded-lg text-xs font-medium transition-colors"
                    >
                      稍后
                    </button>
                    <button
                      onClick={() => handleReminderAction('dismiss')}
                      className="bg-gray-700 hover:bg-gray-600 text-gray-300 py-2 px-3 rounded-lg text-xs font-medium transition-colors"
                    >
                      忽略
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
