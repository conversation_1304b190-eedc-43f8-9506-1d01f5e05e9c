/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<a79e31b31acad00723ee07ef8462d1d5>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Components/DrawerAndroid/DrawerLayoutAndroid.js.flow
 */

import DrawerLayoutAndroid from "./DrawerLayoutAndroidFallback";
export type { DrawerLayoutAndroidProps, DrawerSlideEvent } from "./DrawerLayoutAndroidTypes";
declare const $$DrawerLayoutAndroid: typeof DrawerLayoutAndroid;
declare type $$DrawerLayoutAndroid = typeof $$DrawerLayoutAndroid;
export default $$DrawerLayoutAndroid;
