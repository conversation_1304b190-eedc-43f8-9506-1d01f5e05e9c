{"version": 3, "file": "checks.js", "sourceRoot": "", "sources": ["../../src.ts/address/checks.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAE3D,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAK1C;;;;;;;;;;;;;GAaG;AACH,MAAM,UAAU,aAAa,CAAC,KAAU;IACpC,OAAO,CAAC,KAAK,IAAI,OAAM,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,UAAU,CAAC,CAAC;AAC9D,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,UAAU,SAAS,CAAC,KAAU;IAChC,IAAI;QACA,UAAU,CAAC,KAAK,CAAC,CAAC;QAClB,OAAO,IAAI,CAAC;KACf;IAAC,OAAO,KAAK,EAAE,GAAG;IACnB,OAAO,KAAK,CAAC;AACjB,CAAC;AAED,KAAK,UAAU,YAAY,CAAC,MAAW,EAAE,OAA+B;IACpE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC;IAC7B,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,KAAK,4CAA4C,EAAE;QAC3E,MAAM,CAAC,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;QACjG,cAAc,CAAC,KAAK,EAAE,+DAA+D,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;KAC5G;IACD,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC;AAC9B,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoCG;AACH,MAAM,UAAU,cAAc,CAAC,MAAmB,EAAE,QAA8B;IAE9E,IAAI,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;QAC7B,IAAI,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAE;YAAE,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC;SAAE;QAErE,MAAM,CAAC,QAAQ,IAAI,IAAI,EAAE,oCAAoC,EACzD,uBAAuB,EAAE,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,CAAC;QAE3D,OAAO,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;KAE7D;SAAM,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE;QAC9B,OAAO,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;KAEpD;SAAM,IAAI,MAAM,IAAI,OAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,UAAU,EAAE;QACrD,OAAO,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACvC;IAED,cAAc,CAAC,KAAK,EAAE,+BAA+B,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC7E,CAAC"}