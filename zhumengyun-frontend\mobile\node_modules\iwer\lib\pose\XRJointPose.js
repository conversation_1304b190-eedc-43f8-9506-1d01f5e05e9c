/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { P_JOINT_POSE } from '../private.js';
import { XRPose } from './XRPose.js';
export class XRJointPose extends XRPose {
    constructor(transform, radius, emulatedPosition = false, linearVelocity = undefined, angularVelocity = undefined) {
        super(transform, emulatedPosition, linearVelocity, angularVelocity);
        this[P_JOINT_POSE] = { radius };
    }
    get radius() {
        return this[P_JOINT_POSE].radius;
    }
}
//# sourceMappingURL=XRJointPose.js.map