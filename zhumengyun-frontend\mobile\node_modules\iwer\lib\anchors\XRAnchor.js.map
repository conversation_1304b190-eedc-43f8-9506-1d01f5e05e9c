{"version": 3, "file": "XRAnchor.js", "sourceRoot": "", "sources": ["../../src/anchors/XRAnchor.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAGvE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAG/C,MAAM,OAAO,QAAQ;IAOpB,YAAY,WAAoB,EAAE,OAAkB;QACnD,IAAI,CAAC,QAAQ,CAAC,GAAG;YAChB,WAAW;YACX,OAAO;YACP,OAAO,EAAE,KAAK;SACd,CAAC;QACF,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED,IAAI,WAAW;QACd,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE;YAC3B,MAAM,IAAI,YAAY,CACrB,oCAAoC,EACpC,mBAAmB,CACnB,CAAC;SACF;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAY,CAAC;IACpC,CAAC;IAED,uBAAuB;QACtB,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC9C,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE;gBAC3B,MAAM,CACL,IAAI,YAAY,CACf,oCAAoC,EACpC,mBAAmB,CACnB,CACD,CAAC;aACF;iBAAM;gBACN,MAAM,iBAAiB,GACtB,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC;gBACrD,KAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,iBAAiB,CAAC,OAAO,EAAE,EAAE;oBACzD,IAAI,MAAM,KAAK,IAAI,EAAE;wBACpB,OAAO,CAAC,IAAI,CAAC,CAAC;wBACd,OAAO;qBACP;iBACD;gBACD,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;gBACjC,aAAa,CAAC,sBAAsB,CACnC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,EACtB,IAAI,EACJ,IAAI,CACJ,CAAC;gBACF,OAAO,CAAC,IAAI,CAAC,CAAC;aACd;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,MAAM;QACL,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE;YAC3B,OAAO;SACP;QACD,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC/D,CAAC;CACD;AAED,MAAM,OAAO,WAAY,SAAQ,GAAa;CAAG;AAEjD,MAAM,2BAA2B,GAChC,qDAAqD,CAAC;AAEvD,MAAM,OAAO,aAAa;IACzB,MAAM,CAAC,mCAAmC,CAAC,OAAkB;QAC5D,MAAM,iBAAiB,GAAG,IAAI,CAAC,KAAK,CACnC,YAAY,CAAC,OAAO,CAAC,2BAA2B,CAAC,IAAI,IAAI,CAC7B,CAAC;QAC9B,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,YAAY,CAAC,EAAE,EAAE;YAClE,MAAM,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC;YACpE,MAAM,WAAW,GAAG,IAAI,OAAO,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAC3D,MAAM,MAAM,GAAG,IAAI,QAAQ,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YAClD,OAAO,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,sBAAsB,CAC5B,OAAkB,EAClB,MAAgB,EAChB,IAAY;QAEZ,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9C,OAAO,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACvD,MAAM,iBAAiB,GAAG,IAAI,CAAC,KAAK,CACnC,YAAY,CAAC,OAAO,CAAC,2BAA2B,CAAC,IAAI,IAAI,CAC7B,CAAC;QAC9B,iBAAiB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CACnC,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAY,CAAC,OAAO,CAAC,CAAC,YAAY,CAC3C,CAAC;QACV,YAAY,CAAC,OAAO,CACnB,2BAA2B,EAC3B,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CACjC,CAAC;IACH,CAAC;CACD"}