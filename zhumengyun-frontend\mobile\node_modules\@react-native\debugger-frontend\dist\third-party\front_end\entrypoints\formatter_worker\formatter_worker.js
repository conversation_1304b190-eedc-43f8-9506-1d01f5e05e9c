import*as e from"../../core/platform/platform.js";import*as t from"../../core/root/root.js";import*as r from"../../third_party/acorn/acorn.js";import*as n from"../../models/text_utils/text_utils.js";var i;!function(){function e(e,t,r){for(var n in t||(t={}),e)!e.hasOwnProperty(n)||!1===r&&t.hasOwnProperty(n)||(t[n]=e[n]);return t}function t(e,t,r,n,i){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=n||0,a=i||0;;){var s=e.indexOf("\t",o);if(s<0||s>=t)return a+(t-o);a+=s-o,a+=r-a%r,o=s+1}}function r(){}var n=function(e,t,r){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=r};n.prototype.eol=function(){return this.pos>=this.string.length},n.prototype.sol=function(){return this.pos==this.lineStart},n.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},n.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},n.prototype.eat=function(e){var t=this.string.charAt(this.pos);if("string"==typeof e?t==e:t&&(e.test?e.test(t):e(t)))return++this.pos,t},n.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},n.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},n.prototype.skipToEnd=function(){this.pos=this.string.length},n.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},n.prototype.backUp=function(e){this.pos-=e},n.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=t(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?t(this.string,this.lineStart,this.tabSize):0)},n.prototype.indentation=function(){return t(this.string,null,this.tabSize)-(this.lineStart?t(this.string,this.lineStart,this.tabSize):0)},n.prototype.match=function(e,t,r){if("string"!=typeof e){var n=this.string.slice(this.pos).match(e);return n&&n.index>0?null:(n&&!1!==t&&(this.pos+=n[0].length),n)}var i=function(e){return r?e.toLowerCase():e};if(i(this.string.substr(this.pos,e.length))==i(e))return!1!==t&&(this.pos+=e.length),!0},n.prototype.current=function(){return this.string.slice(this.start,this.pos)},n.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},n.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},n.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};var i={},o={};function a(t){if("string"==typeof t&&o.hasOwnProperty(t))t=o[t];else if(t&&"string"==typeof t.name&&o.hasOwnProperty(t.name)){var n=o[t.name];"string"==typeof n&&(n={name:n}),i=n,s=t,Object.create?l=Object.create(i):(r.prototype=i,l=new r),s&&e(s,l),(t=l).name=n.name}else{if("string"==typeof t&&/^[\w\-]+\/[\w\-]+\+xml$/.test(t))return a("application/xml");if("string"==typeof t&&/^[\w\-]+\/[\w\-]+\+json$/.test(t))return a("application/json")}var i,s,l;return"string"==typeof t?{name:t}:t||{name:"null"}}var s={};var l,c={__proto__:null,modes:i,mimeModes:o,defineMode:function(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),i[e]=t},defineMIME:function(e,t){o[e]=t},resolveMode:a,getMode:function e(t,r){r=a(r);var n=i[r.name];if(!n)return e(t,"text/plain");var o=n(t,r);if(s.hasOwnProperty(r.name)){var l=s[r.name];for(var c in l)l.hasOwnProperty(c)&&(o.hasOwnProperty(c)&&(o["_"+c]=o[c]),o[c]=l[c])}if(o.name=r.name,r.helperType&&(o.helperType=r.helperType),r.modeProps)for(var d in r.modeProps)o[d]=r.modeProps[d];return o},modeExtensions:s,extendMode:function(t,r){e(r,s.hasOwnProperty(t)?s[t]:s[t]={})},copyState:function(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var r={};for(var n in t){var i=t[n];i instanceof Array&&(i=i.concat([])),r[n]=i}return r},innerMode:function(e,t){for(var r;e.innerMode&&(r=e.innerMode(t))&&r.mode!=e;)t=r.state,e=r.mode;return r||{mode:e,state:t}},startState:function(e,t,r){return!e.startState||e.startState(t,r)}},d="undefined"!=typeof globalThis?globalThis:window;for(var u in d.CodeMirror={},CodeMirror.StringStream=n,c)CodeMirror[u]=c[u];CodeMirror.defineMode("null",(function(){return{token:function(e){return e.skipToEnd()}}})),CodeMirror.defineMIME("text/plain","null"),CodeMirror.registerHelper=CodeMirror.registerGlobalHelper=Math.min,CodeMirror.splitLines=function(e){return e.split(/\r?\n|\r/)},CodeMirror.countColumn=t,CodeMirror.defaults={indentUnit:2},l=function(e){e.runMode=function(t,r,n,i){var o=e.getMode(e.defaults,r),a=i&&i.tabSize||e.defaults.tabSize;if(n.appendChild){var s=/MSIE \d/.test(navigator.userAgent)&&(null==document.documentMode||document.documentMode<9),l=n,c=0;l.innerHTML="",n=function(e,t){if("\n"==e)return l.appendChild(document.createTextNode(s?"\r":e)),void(c=0);for(var r="",n=0;;){var i=e.indexOf("\t",n);if(-1==i){r+=e.slice(n),c+=e.length-n;break}c+=i-n,r+=e.slice(n,i);var o=a-c%a;c+=o;for(var d=0;d<o;++d)r+=" ";n=i+1}if(t){var u=l.appendChild(document.createElement("span"));u.className="cm-"+t.replace(/ +/g," cm-"),u.appendChild(document.createTextNode(r))}else l.appendChild(document.createTextNode(r))}}for(var d=e.splitLines(t),u=i&&i.state||e.startState(o),p=0,f=d.length;p<f;++p){p&&n("\n");var h=new e.StringStream(d[p],null,{lookAhead:function(e){return d[p+e]},baseToken:function(){}});for(!h.string&&o.blankLine&&o.blankLine(u);!h.eol();){var m=o.token(h,u);n(h.current(),m,p,h.start,u,o),h.start=h.pos}}}},"object"==typeof exports&&"object"==typeof module?l(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],l):l(CodeMirror)}(),i=function(e){function t(e){for(var t={},r=0;r<e.length;++r)t[e[r].toLowerCase()]=!0;return t}e.defineMode("css",(function(t,r){var n=r.inline;r.propertyKeywords||(r=e.resolveMode("text/css"));var i,o,a=t.indentUnit,s=r.tokenHooks,l=r.documentTypes||{},c=r.mediaTypes||{},d=r.mediaFeatures||{},u=r.mediaValueKeywords||{},p=r.propertyKeywords||{},f=r.nonStandardPropertyKeywords||{},h=r.fontProperties||{},m=r.counterDescriptors||{},b=r.colorKeywords||{},g=r.valueKeywords||{},k=r.allowNested,y=r.lineComment,w=!0===r.supportsAtComponent,v=!1!==t.highlightNonStandardPropertyKeywords;function x(e,t){return i=t,e}function S(e,t){var r=e.next();if(s[r]){var n=s[r](e,t);if(!1!==n)return n}return"@"==r?(e.eatWhile(/[\w\\\-]/),x("def",e.current())):"="==r||("~"==r||"|"==r)&&e.eat("=")?x(null,"compare"):'"'==r||"'"==r?(t.tokenize=T(r),t.tokenize(e,t)):"#"==r?(e.eatWhile(/[\w\\\-]/),x("atom","hash")):"!"==r?(e.match(/^\s*\w*/),x("keyword","important")):/\d/.test(r)||"."==r&&e.eat(/\d/)?(e.eatWhile(/[\w.%]/),x("number","unit")):"-"!==r?/[,+>*\/]/.test(r)?x(null,"select-op"):"."==r&&e.match(/^-?[_a-z][_a-z0-9-]*/i)?x("qualifier","qualifier"):/[:;{}\[\]\(\)]/.test(r)?x(null,r):e.match(/^[\w-.]+(?=\()/)?(/^(url(-prefix)?|domain|regexp)$/i.test(e.current())&&(t.tokenize=N),x("variable callee","variable")):/[\w\\\-]/.test(r)?(e.eatWhile(/[\w\\\-]/),x("property","word")):x(null,null):/[\d.]/.test(e.peek())?(e.eatWhile(/[\w.%]/),x("number","unit")):e.match(/^-[\w\\\-]*/)?(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?x("variable-2","variable-definition"):x("variable-2","variable")):e.match(/^\w+-/)?x("meta","meta"):void 0}function T(e){return function(t,r){for(var n,i=!1;null!=(n=t.next());){if(n==e&&!i){")"==e&&t.backUp(1);break}i=!i&&"\\"==n}return(n==e||!i&&")"!=e)&&(r.tokenize=null),x("string","string")}}function N(e,t){return e.next(),e.match(/^\s*[\"\')]/,!1)?t.tokenize=null:t.tokenize=T(")"),x(null,"(")}function E(e,t,r){this.type=e,this.indent=t,this.prev=r}function C(e,t,r,n){return e.context=new E(r,t.indentation()+(!1===n?0:a),e.context),r}function O(e){return e.context.prev&&(e.context=e.context.prev),e.context.type}function L(e,t,r){return I[r.context.type](e,t,r)}function z(e,t,r,n){for(var i=n||1;i>0;i--)r.context=r.context.prev;return L(e,t,r)}function P(e){var t=e.current().toLowerCase();o=g.hasOwnProperty(t)?"atom":b.hasOwnProperty(t)?"keyword":"variable"}var I={top:function(e,t,r){if("{"==e)return C(r,t,"block");if("}"==e&&r.context.prev)return O(r);if(w&&/@component/i.test(e))return C(r,t,"atComponentBlock");if(/^@(-moz-)?document$/i.test(e))return C(r,t,"documentTypes");if(/^@(media|supports|(-moz-)?document|import)$/i.test(e))return C(r,t,"atBlock");if(/^@(font-face|counter-style)/i.test(e))return r.stateArg=e,"restricted_atBlock_before";if(/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(e))return"keyframes";if(e&&"@"==e.charAt(0))return C(r,t,"at");if("hash"==e)o="builtin";else if("word"==e)o="tag";else{if("variable-definition"==e)return"maybeprop";if("interpolation"==e)return C(r,t,"interpolation");if(":"==e)return"pseudo";if(k&&"("==e)return C(r,t,"parens")}return r.context.type},block:function(e,t,r){if("word"==e){var n=t.current().toLowerCase();return p.hasOwnProperty(n)?(o="property","maybeprop"):f.hasOwnProperty(n)?(o=v?"string-2":"property","maybeprop"):k?(o=t.match(/^\s*:(?:\s|$)/,!1)?"property":"tag","block"):(o+=" error","maybeprop")}return"meta"==e?"block":k||"hash"!=e&&"qualifier"!=e?I.top(e,t,r):(o="error","block")},maybeprop:function(e,t,r){return":"==e?C(r,t,"prop"):L(e,t,r)},prop:function(e,t,r){if(";"==e)return O(r);if("{"==e&&k)return C(r,t,"propBlock");if("}"==e||"{"==e)return z(e,t,r);if("("==e)return C(r,t,"parens");if("hash"!=e||/^#([0-9a-fA-f]{3,4}|[0-9a-fA-f]{6}|[0-9a-fA-f]{8})$/.test(t.current())){if("word"==e)P(t);else if("interpolation"==e)return C(r,t,"interpolation")}else o+=" error";return"prop"},propBlock:function(e,t,r){return"}"==e?O(r):"word"==e?(o="property","maybeprop"):r.context.type},parens:function(e,t,r){return"{"==e||"}"==e?z(e,t,r):")"==e?O(r):"("==e?C(r,t,"parens"):"interpolation"==e?C(r,t,"interpolation"):("word"==e&&P(t),"parens")},pseudo:function(e,t,r){return"meta"==e?"pseudo":"word"==e?(o="variable-3",r.context.type):L(e,t,r)},documentTypes:function(e,t,r){return"word"==e&&l.hasOwnProperty(t.current())?(o="tag",r.context.type):I.atBlock(e,t,r)},atBlock:function(e,t,r){if("("==e)return C(r,t,"atBlock_parens");if("}"==e||";"==e)return z(e,t,r);if("{"==e)return O(r)&&C(r,t,k?"block":"top");if("interpolation"==e)return C(r,t,"interpolation");if("word"==e){var n=t.current().toLowerCase();o="only"==n||"not"==n||"and"==n||"or"==n?"keyword":c.hasOwnProperty(n)?"attribute":d.hasOwnProperty(n)?"property":u.hasOwnProperty(n)?"keyword":p.hasOwnProperty(n)?"property":f.hasOwnProperty(n)?v?"string-2":"property":g.hasOwnProperty(n)?"atom":b.hasOwnProperty(n)?"keyword":"error"}return r.context.type},atComponentBlock:function(e,t,r){return"}"==e?z(e,t,r):"{"==e?O(r)&&C(r,t,k?"block":"top",!1):("word"==e&&(o="error"),r.context.type)},atBlock_parens:function(e,t,r){return")"==e?O(r):"{"==e||"}"==e?z(e,t,r,2):I.atBlock(e,t,r)},restricted_atBlock_before:function(e,t,r){return"{"==e?C(r,t,"restricted_atBlock"):"word"==e&&"@counter-style"==r.stateArg?(o="variable","restricted_atBlock_before"):L(e,t,r)},restricted_atBlock:function(e,t,r){return"}"==e?(r.stateArg=null,O(r)):"word"==e?(o="@font-face"==r.stateArg&&!h.hasOwnProperty(t.current().toLowerCase())||"@counter-style"==r.stateArg&&!m.hasOwnProperty(t.current().toLowerCase())?"error":"property","maybeprop"):"restricted_atBlock"},keyframes:function(e,t,r){return"word"==e?(o="variable","keyframes"):"{"==e?C(r,t,"top"):L(e,t,r)},at:function(e,t,r){return";"==e?O(r):"{"==e||"}"==e?z(e,t,r):("word"==e?o="tag":"hash"==e&&(o="builtin"),"at")},interpolation:function(e,t,r){return"}"==e?O(r):"{"==e||";"==e?z(e,t,r):("word"==e?o="variable":"variable"!=e&&"("!=e&&")"!=e&&(o="error"),"interpolation")}};return{startState:function(e){return{tokenize:null,state:n?"block":"top",stateArg:null,context:new E(n?"block":"top",e||0,null)}},token:function(e,t){if(!t.tokenize&&e.eatSpace())return null;var r=(t.tokenize||S)(e,t);return r&&"object"==typeof r&&(i=r[1],r=r[0]),o=r,"comment"!=i&&(t.state=I[t.state](i,e,t)),o},indent:function(e,t){var r=e.context,n=t&&t.charAt(0),i=r.indent;return"prop"!=r.type||"}"!=n&&")"!=n||(r=r.prev),r.prev&&("}"!=n||"block"!=r.type&&"top"!=r.type&&"interpolation"!=r.type&&"restricted_atBlock"!=r.type?(")"!=n||"parens"!=r.type&&"atBlock_parens"!=r.type)&&("{"!=n||"at"!=r.type&&"atBlock"!=r.type)||(i=Math.max(0,r.indent-a)):i=(r=r.prev).indent),i},electricChars:"}",blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:y,fold:"brace"}}));var r=["domain","regexp","url","url-prefix"],n=t(r),i=["all","aural","braille","handheld","print","projection","screen","tty","tv","embossed"],o=t(i),a=["width","min-width","max-width","height","min-height","max-height","device-width","min-device-width","max-device-width","device-height","min-device-height","max-device-height","aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio","color","min-color","max-color","color-index","min-color-index","max-color-index","monochrome","min-monochrome","max-monochrome","resolution","min-resolution","max-resolution","scan","grid","orientation","device-pixel-ratio","min-device-pixel-ratio","max-device-pixel-ratio","pointer","any-pointer","hover","any-hover","prefers-color-scheme"],s=t(a),l=["landscape","portrait","none","coarse","fine","on-demand","hover","interlace","progressive","dark","light"],c=t(l),d=["align-content","align-items","align-self","alignment-adjust","alignment-baseline","all","anchor-point","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","appearance","azimuth","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","binding","bleed","block-size","bookmark-label","bookmark-level","bookmark-state","bookmark-target","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","color","color-profile","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain","content","counter-increment","counter-reset","crop","cue","cue-after","cue-before","cursor","direction","display","dominant-baseline","drop-initial-after-adjust","drop-initial-after-align","drop-initial-before-adjust","drop-initial-before-align","drop-initial-size","drop-initial-value","elevation","empty-cells","fit","fit-position","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-offset","flow-from","flow-into","font","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","gap","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-gap","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-gap","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphens","icon","image-orientation","image-rendering","image-resolution","inline-box-align","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","isolation","justify-content","justify-items","justify-self","left","letter-spacing","line-break","line-height","line-height-step","line-stacking","line-stacking-ruby","line-stacking-shift","line-stacking-strategy","list-style","list-style-image","list-style-position","list-style-type","margin","margin-bottom","margin-left","margin-right","margin-top","marks","marquee-direction","marquee-loop","marquee-play-count","marquee-speed","marquee-style","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","move-to","nav-down","nav-index","nav-left","nav-right","nav-up","object-fit","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-style","overflow-wrap","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-policy","pause","pause-after","pause-before","perspective","perspective-origin","pitch","pitch-range","place-content","place-items","place-self","play-during","position","presentation-level","punctuation-trim","quotes","region-break-after","region-break-before","region-break-inside","region-fragment","rendering-intent","resize","rest","rest-after","rest-before","richness","right","rotate","rotation","rotation-point","row-gap","ruby-align","ruby-overhang","ruby-position","ruby-span","scale","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-type","shape-image-threshold","shape-inside","shape-margin","shape-outside","size","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","stress","string-set","tab-size","table-layout","target","target-name","target-new","target-position","text-align","text-align-last","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-skip-ink","text-decoration-style","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-height","text-indent","text-justify","text-orientation","text-outline","text-overflow","text-rendering","text-shadow","text-size-adjust","text-space-collapse","text-transform","text-underline-position","text-wrap","top","touch-action","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","translate","unicode-bidi","user-select","vertical-align","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","z-index","clip-path","clip-rule","mask","enable-background","filter","flood-color","flood-opacity","lighting-color","stop-color","stop-opacity","pointer-events","color-interpolation","color-interpolation-filters","color-rendering","fill","fill-opacity","fill-rule","image-rendering","marker","marker-end","marker-mid","marker-start","paint-order","shape-rendering","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-rendering","baseline-shift","dominant-baseline","glyph-orientation-horizontal","glyph-orientation-vertical","text-anchor","writing-mode"],u=t(d),p=["border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","margin-block","margin-block-end","margin-block-start","margin-inline","margin-inline-end","margin-inline-start","padding-block","padding-block-end","padding-block-start","padding-inline","padding-inline-end","padding-inline-start","scroll-snap-stop","scrollbar-3d-light-color","scrollbar-arrow-color","scrollbar-base-color","scrollbar-dark-shadow-color","scrollbar-face-color","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-track-color","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","shape-inside","zoom"],f=t(p),h=t(["font-display","font-family","src","unicode-range","font-variant","font-feature-settings","font-stretch","font-weight","font-style"]),m=t(["additive-symbols","fallback","negative","pad","prefix","range","speak-as","suffix","symbols","system"]),b=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],g=t(b),k=["above","absolute","activeborder","additive","activecaption","afar","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","amharic","amharic-abegede","antialiased","appworkspace","arabic-indic","armenian","asterisks","attr","auto","auto-flow","avoid","avoid-column","avoid-page","avoid-region","axis-pan","background","backwards","baseline","below","bidi-override","binary","bengali","blink","block","block-axis","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","bullets","button","button-bevel","buttonface","buttonhighlight","buttonshadow","buttontext","calc","cambodian","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","cjk-earthly-branch","cjk-heavenly-stem","cjk-ideographic","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","contain","content","contents","content-box","context-menu","continuous","copy","counter","counters","cover","crop","cross","crosshair","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","devanagari","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic","ethiopic-abegede","ethiopic-abegede-am-et","ethiopic-abegede-gez","ethiopic-abegede-ti-er","ethiopic-abegede-ti-et","ethiopic-halehame-aa-er","ethiopic-halehame-aa-et","ethiopic-halehame-am-et","ethiopic-halehame-gez","ethiopic-halehame-om-et","ethiopic-halehame-sid-et","ethiopic-halehame-so-et","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","ethiopic-halehame-tig","ethiopic-numeric","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fill-box","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","georgian","graytext","grid","groove","gujarati","gurmukhi","hand","hangul","hangul-consonant","hard-light","hebrew","help","hidden","hide","higher","highlight","highlighttext","hiragana","hiragana-iroha","horizontal","hsl","hsla","hue","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","japanese-formal","japanese-informal","justify","kannada","katakana","katakana-iroha","keep-all","khmer","korean-hangul-formal","korean-hanja-formal","korean-hanja-informal","landscape","lao","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-alpha","lower-armenian","lower-greek","lower-hexadecimal","lower-latin","lower-norwegian","lower-roman","lowercase","ltr","luminosity","malayalam","manipulation","match","matrix","matrix3d","media-controls-background","media-current-time-display","media-fullscreen-button","media-mute-button","media-play-button","media-return-to-realtime-button","media-rewind-button","media-seek-back-button","media-seek-forward-button","media-slider","media-sliderthumb","media-time-remaining-display","media-volume-slider","media-volume-slider-container","media-volume-sliderthumb","medium","menu","menulist","menulist-button","menulist-text","menulist-textfield","menutext","message-box","middle","min-intrinsic","mix","mongolian","monospace","move","multiple","multiple_mask_images","multiply","myanmar","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","octal","opacity","open-quote","optimizeLegibility","optimizeSpeed","oriya","oromo","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","persian","perspective","pinch-zoom","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","scroll-position","se-resize","searchfield","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","self-start","self-end","semi-condensed","semi-expanded","separate","serif","show","sidama","simp-chinese-formal","simp-chinese-informal","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","somali","source-atop","source-in","source-out","source-over","space","space-around","space-between","space-evenly","spell-out","square","square-button","start","static","status-bar","stretch","stroke","stroke-box","sub","subpixel-antialiased","svg_masks","super","sw-resize","symbolic","symbols","system-ui","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","tamil","telugu","text","text-bottom","text-top","textarea","textfield","thai","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","tibetan","tigre","tigrinya-er","tigrinya-er-abegede","tigrinya-et","tigrinya-et-abegede","to","top","trad-chinese-formal","trad-chinese-informal","transform","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","unidirectional-pan","unset","up","upper-alpha","upper-armenian","upper-greek","upper-hexadecimal","upper-latin","upper-norwegian","upper-roman","uppercase","urdu","url","var","vertical","vertical-text","view-box","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"],y=t(k),w=r.concat(i).concat(a).concat(l).concat(d).concat(p).concat(b).concat(k);function v(e,t){for(var r,n=!1;null!=(r=e.next());){if(n&&"/"==r){t.tokenize=null;break}n="*"==r}return["comment","comment"]}e.registerHelper("hintWords","css",w),e.defineMIME("text/css",{documentTypes:n,mediaTypes:o,mediaFeatures:s,mediaValueKeywords:c,propertyKeywords:u,nonStandardPropertyKeywords:f,fontProperties:h,counterDescriptors:m,colorKeywords:g,valueKeywords:y,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=v,v(e,t))}},name:"css"}),e.defineMIME("text/x-scss",{mediaTypes:o,mediaFeatures:s,mediaValueKeywords:c,propertyKeywords:u,nonStandardPropertyKeywords:f,colorKeywords:g,valueKeywords:y,fontProperties:h,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=v,v(e,t)):["operator","operator"]},":":function(e){return!!e.match(/^\s*\{/,!1)&&[null,null]},$:function(e){return e.match(/^[\w-]+/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"]},"#":function(e){return!!e.eat("{")&&[null,"interpolation"]}},name:"css",helperType:"scss"}),e.defineMIME("text/x-less",{mediaTypes:o,mediaFeatures:s,mediaValueKeywords:c,propertyKeywords:u,nonStandardPropertyKeywords:f,colorKeywords:g,valueKeywords:y,fontProperties:h,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=v,v(e,t)):["operator","operator"]},"@":function(e){return e.eat("{")?[null,"interpolation"]:!e.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\b/i,!1)&&(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"])},"&":function(){return["atom","atom"]}},name:"css",helperType:"less"}),e.defineMIME("text/x-gss",{documentTypes:n,mediaTypes:o,mediaFeatures:s,propertyKeywords:u,nonStandardPropertyKeywords:f,fontProperties:h,counterDescriptors:m,colorKeywords:g,valueKeywords:y,supportsAtComponent:!0,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=v,v(e,t))}},name:"css",helperType:"gss"})},"object"==typeof exports&&"object"==typeof module?i(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],i):i(CodeMirror),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}((function(e){var t={autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0,caseFold:!0},r={autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1,allowMissingTagName:!1,caseFold:!1};e.defineMode("xml",(function(n,i){var o,a,s=n.indentUnit,l={},c=i.htmlMode?t:r;for(var d in c)l[d]=c[d];for(var d in i)l[d]=i[d];function u(e,t){function r(r){return t.tokenize=r,r(e,t)}var n=e.next();return"<"==n?e.eat("!")?e.eat("[")?e.match("CDATA[")?r(f("atom","]]>")):null:e.match("--")?r(f("comment","--\x3e")):e.match("DOCTYPE",!0,!0)?(e.eatWhile(/[\w\._\-]/),r(h(1))):null:e.eat("?")?(e.eatWhile(/[\w\._\-]/),t.tokenize=f("meta","?>"),"meta"):(o=e.eat("/")?"closeTag":"openTag",t.tokenize=p,"tag bracket"):"&"==n?(e.eat("#")?e.eat("x")?e.eatWhile(/[a-fA-F\d]/)&&e.eat(";"):e.eatWhile(/[\d]/)&&e.eat(";"):e.eatWhile(/[\w\.\-:]/)&&e.eat(";"))?"atom":"error":(e.eatWhile(/[^&<]/),null)}function p(e,t){var r,n,i=e.next();if(">"==i||"/"==i&&e.eat(">"))return t.tokenize=u,o=">"==i?"endTag":"selfcloseTag","tag bracket";if("="==i)return o="equals",null;if("<"==i){t.tokenize=u,t.state=k,t.tagName=t.tagStart=null;var a=t.tokenize(e,t);return a?a+" tag error":"tag error"}return/[\'\"]/.test(i)?(t.tokenize=(r=i,n=function(e,t){for(;!e.eol();)if(e.next()==r){t.tokenize=p;break}return"string"},n.isInAttribute=!0,n),t.stringStartCol=e.column(),t.tokenize(e,t)):(e.match(/^[^\s\u00a0=<>\"\']*[^\s\u00a0=<>\"\'\/]/),"word")}function f(e,t){return function(r,n){for(;!r.eol();){if(r.match(t)){n.tokenize=u;break}r.next()}return e}}function h(e){return function(t,r){for(var n;null!=(n=t.next());){if("<"==n)return r.tokenize=h(e+1),r.tokenize(t,r);if(">"==n){if(1==e){r.tokenize=u;break}return r.tokenize=h(e-1),r.tokenize(t,r)}}return"meta"}}function m(e,t,r){this.prev=e.context,this.tagName=t||"",this.indent=e.indented,this.startOfLine=r,(l.doNotIndent.hasOwnProperty(t)||e.context&&e.context.noIndent)&&(this.noIndent=!0)}function b(e){e.context&&(e.context=e.context.prev)}function g(e,t){for(var r;;){if(!e.context)return;if(r=e.context.tagName,!l.contextGrabbers.hasOwnProperty(r)||!l.contextGrabbers[r].hasOwnProperty(t))return;b(e)}}function k(e,t,r){return"openTag"==e?(r.tagStart=t.column(),y):"closeTag"==e?w:k}function y(e,t,r){return"word"==e?(r.tagName=t.current(),a="tag",S):l.allowMissingTagName&&"endTag"==e?(a="tag bracket",S(e,t,r)):(a="error",y)}function w(e,t,r){if("word"==e){var n=t.current();return r.context&&r.context.tagName!=n&&l.implicitlyClosed.hasOwnProperty(r.context.tagName)&&b(r),r.context&&r.context.tagName==n||!1===l.matchClosing?(a="tag",v):(a="tag error",x)}return l.allowMissingTagName&&"endTag"==e?(a="tag bracket",v(e,t,r)):(a="error",x)}function v(e,t,r){return"endTag"!=e?(a="error",v):(b(r),k)}function x(e,t,r){return a="error",v(e,0,r)}function S(e,t,r){if("word"==e)return a="attribute",T;if("endTag"==e||"selfcloseTag"==e){var n=r.tagName,i=r.tagStart;return r.tagName=r.tagStart=null,"selfcloseTag"==e||l.autoSelfClosers.hasOwnProperty(n)?g(r,n):(g(r,n),r.context=new m(r,n,i==r.indented)),k}return a="error",S}function T(e,t,r){return"equals"==e?N:(l.allowMissing||(a="error"),S(e,0,r))}function N(e,t,r){return"string"==e?E:"word"==e&&l.allowUnquoted?(a="string",S):(a="error",S(e,0,r))}function E(e,t,r){return"string"==e?E:S(e,0,r)}return u.isInText=!0,{startState:function(e){var t={tokenize:u,state:k,indented:e||0,tagName:null,tagStart:null,context:null};return null!=e&&(t.baseIndent=e),t},token:function(e,t){if(!t.tagName&&e.sol()&&(t.indented=e.indentation()),e.eatSpace())return null;o=null;var r=t.tokenize(e,t);return(r||o)&&"comment"!=r&&(a=null,t.state=t.state(o||r,e,t),a&&(r="error"==a?r+" error":a)),r},indent:function(t,r,n){var i=t.context;if(t.tokenize.isInAttribute)return t.tagStart==t.indented?t.stringStartCol+1:t.indented+s;if(i&&i.noIndent)return e.Pass;if(t.tokenize!=p&&t.tokenize!=u)return n?n.match(/^(\s*)/)[0].length:0;if(t.tagName)return!1!==l.multilineTagIndentPastTag?t.tagStart+t.tagName.length+2:t.tagStart+s*(l.multilineTagIndentFactor||1);if(l.alignCDATA&&/<!\[CDATA\[/.test(r))return 0;var o=r&&/^<(\/)?([\w_:\.-]*)/.exec(r);if(o&&o[1])for(;i;){if(i.tagName==o[2]){i=i.prev;break}if(!l.implicitlyClosed.hasOwnProperty(i.tagName))break;i=i.prev}else if(o)for(;i;){var a=l.contextGrabbers[i.tagName];if(!a||!a.hasOwnProperty(o[2]))break;i=i.prev}for(;i&&i.prev&&!i.startOfLine;)i=i.prev;return i?i.indent+s:t.baseIndent||0},electricInput:/<\/[\s\w:]+>$/,blockCommentStart:"\x3c!--",blockCommentEnd:"--\x3e",configuration:l.htmlMode?"html":"xml",helperType:l.htmlMode?"html":"xml",skipAttribute:function(e){e.state==N&&(e.state=S)},xmlCurrentTag:function(e){return e.tagName?{name:e.tagName,close:"closeTag"==e.type}:null},xmlCurrentContext:function(e){for(var t=[],r=e.context;r;r=r.prev)t.push(r.tagName);return t.reverse()}}})),e.defineMIME("text/xml","xml"),e.defineMIME("application/xml","xml"),e.mimeModes.hasOwnProperty("text/html")||e.defineMIME("text/html",{name:"xml",htmlMode:!0})})),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}((function(e){e.defineMode("javascript",(function(t,r){var n,i,o=t.indentUnit,a=r.statementIndent,s=r.jsonld,l=r.json||s,c=!1!==r.trackScope,d=r.typescript,u=r.wordCharacters||/[\w$\xa1-\uffff]/,p=function(){function e(e){return{type:e,style:"keyword"}}var t=e("keyword a"),r=e("keyword b"),n=e("keyword c"),i=e("keyword d"),o=e("operator"),a={type:"atom",style:"atom"};return{if:e("if"),while:t,with:t,else:r,do:r,try:r,finally:r,return:i,break:i,continue:i,new:e("new"),delete:n,void:n,throw:n,debugger:e("debugger"),var:e("var"),const:e("var"),let:e("var"),function:e("function"),catch:e("catch"),for:e("for"),switch:e("switch"),case:e("case"),default:e("default"),in:o,typeof:o,instanceof:o,true:a,false:a,null:a,undefined:a,NaN:a,Infinity:a,this:e("this"),class:e("class"),super:e("atom"),yield:n,export:e("export"),import:e("import"),extends:n,await:n}}(),f=/[+\-*&%=<>!?|~^@]/,h=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function m(e,t,r){return n=e,i=r,t}function b(e,t){var r,n=e.next();if('"'==n||"'"==n)return t.tokenize=(r=n,function(e,t){var n,i=!1;if(s&&"@"==e.peek()&&e.match(h))return t.tokenize=b,m("jsonld-keyword","meta");for(;null!=(n=e.next())&&(n!=r||i);)i=!i&&"\\"==n;return i||(t.tokenize=b),m("string","string")}),t.tokenize(e,t);if("."==n&&e.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return m("number","number");if("."==n&&e.match(".."))return m("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(n))return m(n);if("="==n&&e.eat(">"))return m("=>","operator");if("0"==n&&e.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return m("number","number");if(/\d/.test(n))return e.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),m("number","number");if("/"==n)return e.eat("*")?(t.tokenize=g,g(e,t)):e.eat("/")?(e.skipToEnd(),m("comment","comment")):Xe(e,t,1)?(function(e){for(var t,r=!1,n=!1;null!=(t=e.next());){if(!r){if("/"==t&&!n)return;"["==t?n=!0:n&&"]"==t&&(n=!1)}r=!r&&"\\"==t}}(e),e.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),m("regexp","string-2")):(e.eat("="),m("operator","operator",e.current()));if("`"==n)return t.tokenize=k,k(e,t);if("#"==n&&"!"==e.peek())return e.skipToEnd(),m("meta","meta");if("#"==n&&e.eatWhile(u))return m("variable","property");if("<"==n&&e.match("!--")||"-"==n&&e.match("->")&&!/\S/.test(e.string.slice(0,e.start)))return e.skipToEnd(),m("comment","comment");if(f.test(n))return">"==n&&t.lexical&&">"==t.lexical.type||(e.eat("=")?"!"!=n&&"="!=n||e.eat("="):/[<>*+\-|&?]/.test(n)&&(e.eat(n),">"==n&&e.eat(n))),"?"==n&&e.eat(".")?m("."):m("operator","operator",e.current());if(u.test(n)){e.eatWhile(u);var i=e.current();if("."!=t.lastType){if(p.propertyIsEnumerable(i)){var o=p[i];return m(o.type,o.style,i)}if("async"==i&&e.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return m("async","keyword",i)}return m("variable","variable",i)}}function g(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=b;break}n="*"==r}return m("comment","comment")}function k(e,t){for(var r,n=!1;null!=(r=e.next());){if(!n&&("`"==r||"$"==r&&e.eat("{"))){t.tokenize=b;break}n=!n&&"\\"==r}return m("quasi","string-2",e.current())}var y="([{}])";function w(e,t){t.fatArrowAt&&(t.fatArrowAt=null);var r=e.string.indexOf("=>",e.start);if(!(r<0)){if(d){var n=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(e.string.slice(e.start,r));n&&(r=n.index)}for(var i=0,o=!1,a=r-1;a>=0;--a){var s=e.string.charAt(a),l=y.indexOf(s);if(l>=0&&l<3){if(!i){++a;break}if(0==--i){"("==s&&(o=!0);break}}else if(l>=3&&l<6)++i;else if(u.test(s))o=!0;else if(/["'\/`]/.test(s))for(;;--a){if(0==a)return;if(e.string.charAt(a-1)==s&&"\\"!=e.string.charAt(a-2)){a--;break}}else if(o&&!i){++a;break}}o&&!i&&(t.fatArrowAt=a)}}var v={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,import:!0,"jsonld-keyword":!0};function x(e,t,r,n,i,o){this.indented=e,this.column=t,this.type=r,this.prev=i,this.info=o,null!=n&&(this.align=n)}function S(e,t){if(!c)return!1;for(var r=e.localVars;r;r=r.next)if(r.name==t)return!0;for(var n=e.context;n;n=n.prev)for(r=n.vars;r;r=r.next)if(r.name==t)return!0}var T={state:null,column:null,marked:null,cc:null};function N(){for(var e=arguments.length-1;e>=0;e--)T.cc.push(arguments[e])}function E(){return N.apply(null,arguments),!0}function C(e,t){for(var r=t;r;r=r.next)if(r.name==e)return!0;return!1}function O(e){var t=T.state;if(T.marked="def",c){if(t.context)if("var"==t.lexical.info&&t.context&&t.context.block){var n=L(e,t.context);if(null!=n)return void(t.context=n)}else if(!C(e,t.localVars))return void(t.localVars=new I(e,t.localVars));r.globalVars&&!C(e,t.globalVars)&&(t.globalVars=new I(e,t.globalVars))}}function L(e,t){if(t){if(t.block){var r=L(e,t.prev);return r?r==t.prev?t:new P(r,t.vars,!0):null}return C(e,t.vars)?t:new P(t.prev,new I(e,t.vars),!1)}return null}function z(e){return"public"==e||"private"==e||"protected"==e||"abstract"==e||"readonly"==e}function P(e,t,r){this.prev=e,this.vars=t,this.block=r}function I(e,t){this.name=e,this.next=t}var j=new I("this",new I("arguments",null));function M(){T.state.context=new P(T.state.context,T.state.localVars,!1),T.state.localVars=j}function A(){T.state.context=new P(T.state.context,T.state.localVars,!0),T.state.localVars=null}function V(){T.state.localVars=T.state.context.vars,T.state.context=T.state.context.prev}function _(e,t){var r=function(){var r=T.state,n=r.indented;if("stat"==r.lexical.type)n=r.lexical.indented;else for(var i=r.lexical;i&&")"==i.type&&i.align;i=i.prev)n=i.indented;r.lexical=new x(n,T.stream.column(),e,null,r.lexical,t)};return r.lex=!0,r}function D(){var e=T.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function B(e){return function t(r){return r==e?E():";"==e||"}"==r||")"==r||"]"==r?N():E(t)}}function F(e,t){return"var"==e?E(_("vardef",t),xe,B(";"),D):"keyword a"==e?E(_("form"),$,F,D):"keyword b"==e?E(_("form"),F,D):"keyword d"==e?T.stream.match(/^\s*$/,!1)?E():E(_("stat"),U,B(";"),D):"debugger"==e?E(B(";")):"{"==e?E(_("}"),A,le,D,V):";"==e?E():"if"==e?("else"==T.state.lexical.info&&T.state.cc[T.state.cc.length-1]==D&&T.state.cc.pop()(),E(_("form"),$,F,D,Oe)):"function"==e?E(Ie):"for"==e?E(_("form"),A,Le,F,V,D):"class"==e||d&&"interface"==t?(T.marked="keyword",E(_("form","class"==e?e:t),_e,D)):"variable"==e?d&&"declare"==t?(T.marked="keyword",E(F)):d&&("module"==t||"enum"==t||"type"==t)&&T.stream.match(/^\s*\w/,!1)?(T.marked="keyword","enum"==t?E(Ge):"type"==t?E(Me,B("operator"),fe,B(";")):E(_("form"),Se,B("{"),_("}"),le,D,D)):d&&"namespace"==t?(T.marked="keyword",E(_("form"),W,F,D)):d&&"abstract"==t?(T.marked="keyword",E(F)):E(_("stat"),te):"switch"==e?E(_("form"),$,B("{"),_("}","switch"),A,le,D,D,V):"case"==e?E(W,B(":")):"default"==e?E(B(":")):"catch"==e?E(_("form"),M,q,F,D,V):"export"==e?E(_("stat"),qe,D):"import"==e?E(_("stat"),Ke,D):"async"==e?E(F):"@"==t?E(W,F):N(_("stat"),W,B(";"),D)}function q(e){if("("==e)return E(Ae,B(")"))}function W(e,t){return R(e,t,!1)}function K(e,t){return R(e,t,!0)}function $(e){return"("!=e?N():E(_(")"),U,B(")"),D)}function R(e,t,r){if(T.state.fatArrowAt==T.stream.start){var n=r?Z:X;if("("==e)return E(M,_(")"),ae(Ae,")"),D,B("=>"),n,V);if("variable"==e)return N(M,Se,B("=>"),n,V)}var i=r?Y:H;return v.hasOwnProperty(e)?E(i):"function"==e?E(Ie,i):"class"==e||d&&"interface"==t?(T.marked="keyword",E(_("form"),Ve,D)):"keyword c"==e||"async"==e?E(r?K:W):"("==e?E(_(")"),U,B(")"),D,i):"operator"==e||"spread"==e?E(r?K:W):"["==e?E(_("]"),Ye,D,i):"{"==e?se(ne,"}",null,i):"quasi"==e?N(G,i):"new"==e?E(function(e){return function(t){return"."==t?E(e?ee:Q):"variable"==t&&d?E(ye,e?Y:H):N(e?K:W)}}(r)):E()}function U(e){return e.match(/[;\}\)\],]/)?N():N(W)}function H(e,t){return","==e?E(U):Y(e,t,!1)}function Y(e,t,r){var n=0==r?H:Y,i=0==r?W:K;return"=>"==e?E(M,r?Z:X,V):"operator"==e?/\+\+|--/.test(t)||d&&"!"==t?E(n):d&&"<"==t&&T.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/,!1)?E(_(">"),ae(fe,">"),D,n):"?"==t?E(W,B(":"),i):E(i):"quasi"==e?N(G,n):";"!=e?"("==e?se(K,")","call",n):"."==e?E(re,n):"["==e?E(_("]"),U,B("]"),D,n):d&&"as"==t?(T.marked="keyword",E(fe,n)):"regexp"==e?(T.state.lastType=T.marked="operator",T.stream.backUp(T.stream.pos-T.stream.start-1),E(i)):void 0:void 0}function G(e,t){return"quasi"!=e?N():"${"!=t.slice(t.length-2)?E(G):E(W,J)}function J(e){if("}"==e)return T.marked="string-2",T.state.tokenize=k,E(G)}function X(e){return w(T.stream,T.state),N("{"==e?F:W)}function Z(e){return w(T.stream,T.state),N("{"==e?F:K)}function Q(e,t){if("target"==t)return T.marked="keyword",E(H)}function ee(e,t){if("target"==t)return T.marked="keyword",E(Y)}function te(e){return":"==e?E(D,F):N(H,B(";"),D)}function re(e){if("variable"==e)return T.marked="property",E()}function ne(e,t){return"async"==e?(T.marked="property",E(ne)):"variable"==e||"keyword"==T.style?(T.marked="property","get"==t||"set"==t?E(ie):(d&&T.state.fatArrowAt==T.stream.start&&(r=T.stream.match(/^\s*:\s*/,!1))&&(T.state.fatArrowAt=T.stream.pos+r[0].length),E(oe))):"number"==e||"string"==e?(T.marked=s?"property":T.style+" property",E(oe)):"jsonld-keyword"==e?E(oe):d&&z(t)?(T.marked="keyword",E(ne)):"["==e?E(W,ce,B("]"),oe):"spread"==e?E(K,oe):"*"==t?(T.marked="keyword",E(ne)):":"==e?N(oe):void 0;var r}function ie(e){return"variable"!=e?N(oe):(T.marked="property",E(Ie))}function oe(e){return":"==e?E(K):"("==e?N(Ie):void 0}function ae(e,t,r){function n(i,o){if(r?r.indexOf(i)>-1:","==i){var a=T.state.lexical;return"call"==a.info&&(a.pos=(a.pos||0)+1),E((function(r,n){return r==t||n==t?N():N(e)}),n)}return i==t||o==t?E():r&&r.indexOf(";")>-1?N(e):E(B(t))}return function(r,i){return r==t||i==t?E():N(e,n)}}function se(e,t,r){for(var n=3;n<arguments.length;n++)T.cc.push(arguments[n]);return E(_(t,r),ae(e,t),D)}function le(e){return"}"==e?E():N(F,le)}function ce(e,t){if(d){if(":"==e)return E(fe);if("?"==t)return E(ce)}}function de(e,t){if(d&&(":"==e||"in"==t))return E(fe)}function ue(e){if(d&&":"==e)return T.stream.match(/^\s*\w+\s+is\b/,!1)?E(W,pe,fe):E(fe)}function pe(e,t){if("is"==t)return T.marked="keyword",E()}function fe(e,t){return"keyof"==t||"typeof"==t||"infer"==t||"readonly"==t?(T.marked="keyword",E("typeof"==t?K:fe)):"variable"==e||"void"==t?(T.marked="type",E(ke)):"|"==t||"&"==t?E(fe):"string"==e||"number"==e||"atom"==e?E(ke):"["==e?E(_("]"),ae(fe,"]",","),D,ke):"{"==e?E(_("}"),me,D,ke):"("==e?E(ae(ge,")"),he,ke):"<"==e?E(ae(fe,">"),fe):void 0}function he(e){if("=>"==e)return E(fe)}function me(e){return e.match(/[\}\)\]]/)?E():","==e||";"==e?E(me):N(be,me)}function be(e,t){return"variable"==e||"keyword"==T.style?(T.marked="property",E(be)):"?"==t||"number"==e||"string"==e?E(be):":"==e?E(fe):"["==e?E(B("variable"),de,B("]"),be):"("==e?N(je,be):e.match(/[;\}\)\],]/)?void 0:E()}function ge(e,t){return"variable"==e&&T.stream.match(/^\s*[?:]/,!1)||"?"==t?E(ge):":"==e?E(fe):"spread"==e?E(ge):N(fe)}function ke(e,t){return"<"==t?E(_(">"),ae(fe,">"),D,ke):"|"==t||"."==e||"&"==t?E(fe):"["==e?E(fe,B("]"),ke):"extends"==t||"implements"==t?(T.marked="keyword",E(fe)):"?"==t?E(fe,B(":"),fe):void 0}function ye(e,t){if("<"==t)return E(_(">"),ae(fe,">"),D,ke)}function we(){return N(fe,ve)}function ve(e,t){if("="==t)return E(fe)}function xe(e,t){return"enum"==t?(T.marked="keyword",E(Ge)):N(Se,ce,Ee,Ce)}function Se(e,t){return d&&z(t)?(T.marked="keyword",E(Se)):"variable"==e?(O(t),E()):"spread"==e?E(Se):"["==e?se(Ne,"]"):"{"==e?se(Te,"}"):void 0}function Te(e,t){return"variable"!=e||T.stream.match(/^\s*:/,!1)?("variable"==e&&(T.marked="property"),"spread"==e?E(Se):"}"==e?N():"["==e?E(W,B("]"),B(":"),Te):E(B(":"),Se,Ee)):(O(t),E(Ee))}function Ne(){return N(Se,Ee)}function Ee(e,t){if("="==t)return E(K)}function Ce(e){if(","==e)return E(xe)}function Oe(e,t){if("keyword b"==e&&"else"==t)return E(_("form","else"),F,D)}function Le(e,t){return"await"==t?E(Le):"("==e?E(_(")"),ze,D):void 0}function ze(e){return"var"==e?E(xe,Pe):"variable"==e?E(Pe):N(Pe)}function Pe(e,t){return")"==e?E():";"==e?E(Pe):"in"==t||"of"==t?(T.marked="keyword",E(W,Pe)):N(W,Pe)}function Ie(e,t){return"*"==t?(T.marked="keyword",E(Ie)):"variable"==e?(O(t),E(Ie)):"("==e?E(M,_(")"),ae(Ae,")"),D,ue,F,V):d&&"<"==t?E(_(">"),ae(we,">"),D,Ie):void 0}function je(e,t){return"*"==t?(T.marked="keyword",E(je)):"variable"==e?(O(t),E(je)):"("==e?E(M,_(")"),ae(Ae,")"),D,ue,V):d&&"<"==t?E(_(">"),ae(we,">"),D,je):void 0}function Me(e,t){return"keyword"==e||"variable"==e?(T.marked="type",E(Me)):"<"==t?E(_(">"),ae(we,">"),D):void 0}function Ae(e,t){return"@"==t&&E(W,Ae),"spread"==e?E(Ae):d&&z(t)?(T.marked="keyword",E(Ae)):d&&"this"==e?E(ce,Ee):N(Se,ce,Ee)}function Ve(e,t){return"variable"==e?_e(e,t):De(e,t)}function _e(e,t){if("variable"==e)return O(t),E(De)}function De(e,t){return"<"==t?E(_(">"),ae(we,">"),D,De):"extends"==t||"implements"==t||d&&","==e?("implements"==t&&(T.marked="keyword"),E(d?fe:W,De)):"{"==e?E(_("}"),Be,D):void 0}function Be(e,t){return"async"==e||"variable"==e&&("static"==t||"get"==t||"set"==t||d&&z(t))&&T.stream.match(/^\s+[\w$\xa1-\uffff]/,!1)?(T.marked="keyword",E(Be)):"variable"==e||"keyword"==T.style?(T.marked="property",E(Fe,Be)):"number"==e||"string"==e?E(Fe,Be):"["==e?E(W,ce,B("]"),Fe,Be):"*"==t?(T.marked="keyword",E(Be)):d&&"("==e?N(je,Be):";"==e||","==e?E(Be):"}"==e?E():"@"==t?E(W,Be):void 0}function Fe(e,t){if("?"==t)return E(Fe);if(":"==e)return E(fe,Ee);if("="==t)return E(K);var r=T.state.lexical.prev;return N(r&&"interface"==r.info?je:Ie)}function qe(e,t){return"*"==t?(T.marked="keyword",E(He,B(";"))):"default"==t?(T.marked="keyword",E(W,B(";"))):"{"==e?E(ae(We,"}"),He,B(";")):N(F)}function We(e,t){return"as"==t?(T.marked="keyword",E(B("variable"))):"variable"==e?N(K,We):void 0}function Ke(e){return"string"==e?E():"("==e?N(W):"."==e?N(H):N($e,Re,He)}function $e(e,t){return"{"==e?se($e,"}"):("variable"==e&&O(t),"*"==t&&(T.marked="keyword"),E(Ue))}function Re(e){if(","==e)return E($e,Re)}function Ue(e,t){if("as"==t)return T.marked="keyword",E($e)}function He(e,t){if("from"==t)return T.marked="keyword",E(W)}function Ye(e){return"]"==e?E():N(ae(K,"]"))}function Ge(){return N(_("form"),Se,B("{"),_("}"),ae(Je,"}"),D,D)}function Je(){return N(Se,Ee)}function Xe(e,t,r){return t.tokenize==b&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(t.lastType)||"quasi"==t.lastType&&/\{\s*$/.test(e.string.slice(0,e.pos-(r||0)))}return V.lex=!0,D.lex=!0,{startState:function(e){var t={tokenize:b,lastType:"sof",cc:[],lexical:new x((e||0)-o,0,"block",!1),localVars:r.localVars,context:r.localVars&&new P(null,null,!1),indented:e||0};return r.globalVars&&"object"==typeof r.globalVars&&(t.globalVars=r.globalVars),t},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation(),w(e,t)),t.tokenize!=g&&e.eatSpace())return null;var r=t.tokenize(e,t);return"comment"==n?r:(t.lastType="operator"!=n||"++"!=i&&"--"!=i?n:"incdec",function(e,t,r,n,i){var o=e.cc;for(T.state=e,T.stream=i,T.marked=null,T.cc=o,T.style=t,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);;)if((o.length?o.pop():l?W:F)(r,n)){for(;o.length&&o[o.length-1].lex;)o.pop()();return T.marked?T.marked:"variable"==r&&S(e,n)?"variable-2":t}}(t,r,n,i,e))},indent:function(t,n){if(t.tokenize==g||t.tokenize==k)return e.Pass;if(t.tokenize!=b)return 0;var i,s=n&&n.charAt(0),l=t.lexical;if(!/^\s*else\b/.test(n))for(var c=t.cc.length-1;c>=0;--c){var d=t.cc[c];if(d==D)l=l.prev;else if(d!=Oe&&d!=V)break}for(;("stat"==l.type||"form"==l.type)&&("}"==s||(i=t.cc[t.cc.length-1])&&(i==H||i==Y)&&!/^[,\.=+\-*:?[\(]/.test(n));)l=l.prev;a&&")"==l.type&&"stat"==l.prev.type&&(l=l.prev);var u=l.type,p=s==u;return"vardef"==u?l.indented+("operator"==t.lastType||","==t.lastType?l.info.length+1:0):"form"==u&&"{"==s?l.indented:"form"==u?l.indented+o:"stat"==u?l.indented+(function(e,t){return"operator"==e.lastType||","==e.lastType||f.test(t.charAt(0))||/[,.]/.test(t.charAt(0))}(t,n)?a||o:0):"switch"!=l.info||p||0==r.doubleIndentSwitch?l.align?l.column+(p?0:1):l.indented+(p?0:o):l.indented+(/^(?:case|default)\b/.test(n)?o:2*o)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:l?null:"/*",blockCommentEnd:l?null:"*/",blockCommentContinue:l?null:" * ",lineComment:l?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:l?"json":"javascript",jsonldMode:s,jsonMode:l,expressionAllowed:Xe,skipExpression:function(e){var t=e.cc[e.cc.length-1];t!=W&&t!=K||e.cc.pop()}}})),e.registerHelper("wordChars","javascript",/[\w$]/),e.defineMIME("text/javascript","javascript"),e.defineMIME("text/ecmascript","javascript"),e.defineMIME("application/javascript","javascript"),e.defineMIME("application/x-javascript","javascript"),e.defineMIME("application/ecmascript","javascript"),e.defineMIME("application/json",{name:"javascript",json:!0}),e.defineMIME("application/x-json",{name:"javascript",json:!0}),e.defineMIME("application/manifest+json",{name:"javascript",json:!0}),e.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),e.defineMIME("text/typescript",{name:"javascript",typescript:!0}),e.defineMIME("application/typescript",{name:"javascript",typescript:!0})}));class o{#e;#t;#r;#n;#i=0;constructor(t,r){this.#n=r;const i=e.StringUtilities.findLineEndingIndexes(t);this.#e=new n.TextCursor.TextCursor(i),this.#t=0,this.#r=0}static punctuator(e,t){return e.type!==r.tokTypes.num&&e.type!==r.tokTypes.regexp&&e.type!==r.tokTypes.string&&e.type!==r.tokTypes.name&&!e.type.keyword&&(!t||1===e.type.label.length&&-1!==t.indexOf(e.type.label))}static keyword(e,t){return Boolean(e.type.keyword)&&e.type!==r.tokTypes._true&&e.type!==r.tokTypes._false&&e.type!==r.tokTypes._null&&(!t||e.type.keyword===t)}static identifier(e,t){return e.type===r.tokTypes.name&&(!t||e.value===t)}static arrowIdentifier(e,t){return e.type===r.tokTypes.arrow&&(!t||e.type.label===t)}static lineComment(e){return"Line"===e.type}static blockComment(e){return"Block"===e.type}nextToken(){const e=this.#n[this.#i++];return e&&e.type!==r.tokTypes.eof?(this.#e.advance(e.start),this.#t=this.#e.lineNumber(),this.#e.advance(e.end),this.#r=this.#e.lineNumber(),e):null}peekToken(){const e=this.#n[this.#i];return e&&e.type!==r.tokTypes.eof?e:null}tokenLineStart(){return this.#t}tokenLineEnd(){return this.#r}}const a=2022;class s{indentString;#o=0;#a=[];#s=0;#l=0;#c=0;#d=0;#u=!0;#p=!1;#f=0;#h=new Map;#m=/[$\u200C\u200D\p{ID_Continue}]/u;mapping={original:[0],formatted:[0]};constructor(e){this.indentString=e}setEnforceSpaceBetweenWords(e){const t=this.#u;return this.#u=e,t}addToken(e,t){if(this.#u&&!this.#f&&!this.#p){const t=this.#a.at(-1)?.at(-1)??"";this.#m.test(t)&&this.#m.test(e)&&this.addSoftSpace()}this.#b(),this.#g(t),this.#k(e)}addSoftSpace(){this.#f||(this.#p=!0)}addHardSpace(){this.#p=!1,++this.#f}addNewLine(e){this.#s&&(e?++this.#d:this.#d=this.#d||1)}increaseNestingLevel(){this.#c+=1}decreaseNestingLevel(){this.#c>0&&(this.#c-=1)}content(){return this.#a.join("")+(this.#d?"\n":"")}#b(){if(this.#d){for(let e=0;e<this.#d;++e)this.#k("\n");this.#k(this.#y())}else this.#p&&this.#k(" ");if(this.#f)for(let e=0;e<this.#f;++e)this.#k(" ");this.#d=0,this.#p=!1,this.#f=0}#y(){const e=this.#h.get(this.#c);if(e)return e;let t="";for(let e=0;e<this.#c;++e)t+=this.indentString;return this.#c<=20&&this.#h.set(this.#c,t),t}#k(e){this.#a.push(e),this.#s+=e.length}#g(e){e-this.#o!=this.#s-this.#l&&(this.mapping.original.push(e),this.#o=e,this.mapping.formatted.push(this.#s),this.#l=this.#s)}}var l=Object.freeze({__proto__:null,FormattedContentBuilder:s});class c{#w;#v;constructor(e,t){this.#w=e,this.#v=t}walk(e){this.#x(e,null)}#x(e,t){if(!e)return;e.parent=t,this.#w.call(null,e);const r=d[e.type];if(r){if("TemplateLiteral"===e.type){const t=e,r=t.expressions.length;for(let e=0;e<r;++e)this.#x(t.quasis[e],t),this.#x(t.expressions[e],t);this.#x(t.quasis[r],t)}else for(let t=0;t<r.length;++t){const n=e[r[t]];Array.isArray(n)?this.#S(n,e):this.#x(n,e)}this.#v.call(null,e)}else console.error("Walk order not defined for "+e.type)}#S(e,t){for(let r=0;r<e.length;++r)this.#x(e[r],t)}}const d={AwaitExpression:["argument"],ArrayExpression:["elements"],ArrayPattern:["elements"],ArrowFunctionExpression:["params","body"],AssignmentExpression:["left","right"],AssignmentPattern:["left","right"],BinaryExpression:["left","right"],BlockStatement:["body"],BreakStatement:["label"],CallExpression:["callee","arguments"],CatchClause:["param","body"],ClassBody:["body"],ClassDeclaration:["id","superClass","body"],ClassExpression:["id","superClass","body"],ChainExpression:["expression"],ConditionalExpression:["test","consequent","alternate"],ContinueStatement:["label"],DebuggerStatement:[],DoWhileStatement:["body","test"],EmptyStatement:[],ExpressionStatement:["expression"],ForInStatement:["left","right","body"],ForOfStatement:["left","right","body"],ForStatement:["init","test","update","body"],FunctionDeclaration:["id","params","body"],FunctionExpression:["id","params","body"],Identifier:[],ImportDeclaration:["specifiers","source"],ImportDefaultSpecifier:["local"],ImportNamespaceSpecifier:["local"],ImportSpecifier:["imported","local"],ImportExpression:["source"],ExportAllDeclaration:["source"],ExportDefaultDeclaration:["declaration"],ExportNamedDeclaration:["specifiers","source","declaration"],ExportSpecifier:["exported","local"],IfStatement:["test","consequent","alternate"],LabeledStatement:["label","body"],Literal:[],LogicalExpression:["left","right"],MemberExpression:["object","property"],MetaProperty:["meta","property"],MethodDefinition:["key","value"],NewExpression:["callee","arguments"],ObjectExpression:["properties"],ObjectPattern:["properties"],ParenthesizedExpression:["expression"],PrivateIdentifier:[],PropertyDefinition:["key","value"],Program:["body"],Property:["key","value"],RestElement:["argument"],ReturnStatement:["argument"],SequenceExpression:["expressions"],SpreadElement:["argument"],StaticBlock:["body"],Super:[],SwitchCase:["test","consequent"],SwitchStatement:["discriminant","cases"],TaggedTemplateExpression:["tag","quasi"],TemplateElement:[],TemplateLiteral:["quasis","expressions"],ThisExpression:[],ThrowStatement:["argument"],TryStatement:["block","handler","finalizer"],UnaryExpression:["argument"],UpdateExpression:["argument"],VariableDeclaration:["declarations"],VariableDeclarator:["id","init"],WhileStatement:["test","body"],WithStatement:["object","body"],YieldExpression:["argument"]};class u{#T;#N;#E;#C;#O;#L;constructor(e){this.#T=e}format(e,t,n,i){this.#C=n,this.#L=i,this.#E=e.substring(this.#C,this.#L),this.#O=0;const s=[],l=r.parse(this.#E,{ranges:!1,preserveParens:!0,allowAwaitOutsideFunction:!0,allowImportExportEverywhere:!0,ecmaVersion:a,allowHashBang:!0,onToken:s,onComment:s});this.#N=new o(this.#E,s);new c(this.#w.bind(this),this.#v.bind(this)).walk(l)}#z(e,t){for(let r=0;r<t.length;++r)"s"===t[r]?this.#T.addSoftSpace():"S"===t[r]?this.#T.addHardSpace():"n"===t[r]?this.#T.addNewLine():">"===t[r]?this.#T.increaseNestingLevel():"<"===t[r]?this.#T.decreaseNestingLevel():"t"===t[r]&&(this.#N.tokenLineStart()-this.#O>1&&this.#T.addNewLine(!0),this.#O=this.#N.tokenLineEnd(),e&&this.#T.addToken(this.#E.substring(e.start,e.end),this.#C+e.start))}#w(e){if(!e.parent)return;let t;for("TemplateLiteral"===e.type&&this.#T.setEnforceSpaceBetweenWords(!1);(t=this.#N.peekToken())&&t.start<e.start;){const t=this.#N.nextToken(),r=this.#P(e.parent,t);this.#z(t,r)}}#v(e){let t;for(;(t=this.#N.peekToken())&&t.start<e.end;){const t=this.#N.nextToken(),r=this.#P(e,t);this.#z(t,r)}this.#z(null,this.#I(e)),"TemplateLiteral"===e.type&&this.#T.setEnforceSpaceBetweenWords(!0)}#j(e){const t=e.parent;if(!t)return!1;if("ForStatement"===t.type){const r=t;return e===r.init||e===r.test||e===r.update}if("ForInStatement"===t.type||"ForOfStatement"===t.type){const r=t;return e===r.left||e===r.right}return!1}#P(e,t){const r=o;if(r.lineComment(t))return"tn";if(r.blockComment(t))return"tn";const n=t,i=e.type;if("ContinueStatement"===i||"BreakStatement"===i)return e.label&&r.keyword(n)?"ts":"t";if("Identifier"===i)return"t";if("PrivateIdentifier"===i)return"t";if("ReturnStatement"===i)return r.punctuator(n,";")?"t":e.argument?"ts":"t";if("AwaitExpression"===i)return r.punctuator(n,";")?"t":e.argument?"ts":"t";if("Property"===i)return r.punctuator(n,":")?"ts":"t";if("ArrayExpression"===i)return r.punctuator(n,",")?"ts":"t";if("LabeledStatement"===i){if(r.punctuator(n,":"))return"ts"}else if("LogicalExpression"===i||"AssignmentExpression"===i||"BinaryExpression"===i){if(r.punctuator(n)&&!r.punctuator(n,"()"))return"sts"}else if("ConditionalExpression"===i){if(r.punctuator(n,"?:"))return"sts"}else if("VariableDeclarator"===i){if(r.punctuator(n,"="))return"sts"}else if("ObjectPattern"===i){if(e.parent&&"VariableDeclarator"===e.parent.type&&r.punctuator(n,"{"))return"st";if(r.punctuator(n,","))return"ts"}else if("FunctionDeclaration"===i){if(r.punctuator(n,",)"))return"ts"}else if("FunctionExpression"===i){if(r.punctuator(n,",)"))return"ts";if(r.keyword(n,"function"))return e.id?"ts":"t"}else if("ArrowFunctionExpression"===i){if(r.punctuator(n,",)"))return"ts";if(r.punctuator(n,"("))return"st";if(r.arrowIdentifier(n,"=>"))return"sts"}else if("WithStatement"===i){if(r.punctuator(n,")"))return e.body&&"BlockStatement"===e.body.type?"ts":"tn>"}else if("SwitchStatement"===i){if(r.punctuator(n,"{"))return"tn>";if(r.punctuator(n,"}"))return"n<tn";if(r.punctuator(n,")"))return"ts"}else if("SwitchCase"===i){if(r.keyword(n,"case"))return"n<ts";if(r.keyword(n,"default"))return"n<t";if(r.punctuator(n,":"))return"tn>"}else if("VariableDeclaration"===i){if(r.punctuator(n,",")){let t=!0;const r=e.declarations;for(let e=0;e<r.length;++e)t=t&&Boolean(r[e].init);return!this.#j(e)&&t?"nSSts":"ts"}}else if("PropertyDefinition"===i){if(r.punctuator(n,"="))return"sts";if(r.punctuator(n,";"))return"tn"}else if("BlockStatement"===i){if(r.punctuator(n,"{"))return e.body.length?"tn>":"t";if(r.punctuator(n,"}"))return e.body.length?"n<t":"t"}else if("CatchClause"===i){if(r.punctuator(n,")"))return"ts"}else if("ObjectExpression"===i){if(!e.properties.length)return"t";if(r.punctuator(n,"{"))return"tn>";if(r.punctuator(n,"}"))return"n<t";if(r.punctuator(n,","))return"tn"}else if("IfStatement"===i){if(r.punctuator(n,")"))return e.consequent&&"BlockStatement"===e.consequent.type?"ts":"tn>";if(r.keyword(n,"else")){const t=e.consequent&&"BlockStatement"===e.consequent.type?"st":"n<t";let r="n>";return!e.alternate||"BlockStatement"!==e.alternate.type&&"IfStatement"!==e.alternate.type||(r="s"),t+r}}else if("CallExpression"===i){if(r.punctuator(n,","))return"ts"}else{if("SequenceExpression"===i&&r.punctuator(n,","))return e.parent&&"SwitchCase"===e.parent.type?"ts":"tn";if("ForStatement"===i||"ForOfStatement"===i||"ForInStatement"===i){if(r.punctuator(n,";"))return"ts";if(r.keyword(n,"in")||r.identifier(n,"of"))return"sts";if(r.punctuator(n,")"))return e.body&&"BlockStatement"===e.body.type?"ts":"tn>"}else if("WhileStatement"===i){if(r.punctuator(n,")"))return e.body&&"BlockStatement"===e.body.type?"ts":"tn>"}else if("DoWhileStatement"===i){const t=e.body&&"BlockStatement"===e.body.type;if(r.keyword(n,"do"))return t?"ts":"tn>";if(r.keyword(n,"while"))return t?"sts":"n<ts";if(r.punctuator(n,";"))return"tn"}else{if("ClassBody"===i)return r.punctuator(n,"{")?"stn>":r.punctuator(n,"}")?"<ntn":"t";if("YieldExpression"===i)return"t";if("Super"===i)return"t";if("ImportExpression"===i)return"t";if("ExportAllDeclaration"===i)return r.punctuator(n,"*")?"sts":"t";if("ExportNamedDeclaration"===i||"ImportDeclaration"===i)return r.punctuator(n,"{")?"st":r.punctuator(n,",")?"ts":r.punctuator(n,"}")?e.source?"ts":"t":r.punctuator(n,"*")?"sts":"t"}}return r.keyword(n)&&!r.keyword(n,"this")?"ts":"t"}#I(e){const t=e.type;if("WithStatement"===t){if(e.body&&"BlockStatement"!==e.body.type)return"n<"}else if("VariableDeclaration"===t){if(!this.#j(e))return"n"}else if("ForStatement"===t||"ForOfStatement"===t||"ForInStatement"===t){if(e.body&&"BlockStatement"!==e.body.type)return"n<"}else{if("BlockStatement"===t){if(e.parent&&"IfStatement"===e.parent.type){const t=e.parent;if(t.alternate&&t.consequent===e)return""}if(e.parent&&"FunctionExpression"===e.parent.type&&e.parent.parent&&"Property"===e.parent.parent.type)return"";if(e.parent&&"FunctionExpression"===e.parent.type&&e.parent.parent&&"VariableDeclarator"===e.parent.parent.type)return"";if(e.parent&&"FunctionExpression"===e.parent.type&&e.parent.parent&&"CallExpression"===e.parent.parent.type)return"";if(e.parent&&"DoWhileStatement"===e.parent.type)return"";if(e.parent&&"TryStatement"===e.parent.type){if(e.parent.block===e)return"s"}if(e.parent&&"CatchClause"===e.parent.type){const t=e.parent;if(t.parent&&t.parent.finalizer)return"s"}return"n"}if("WhileStatement"===t){if(e.body&&"BlockStatement"!==e.body.type)return"n<"}else if("IfStatement"===t){if(e.alternate){if("BlockStatement"!==e.alternate.type&&"IfStatement"!==e.alternate.type)return"<"}else if(e.consequent&&"BlockStatement"!==e.consequent.type)return"<"}else{if("BreakStatement"===t||"ContinueStatement"===t||"ThrowStatement"===t||"ReturnStatement"===t||"ExpressionStatement"===t)return"n";if("ImportDeclaration"===t||"ExportAllDeclaration"===t||"ExportDefaultDeclaration"===t||"ExportNamedDeclaration"===t)return"n"}}return""}}var p=Object.freeze({__proto__:null,JavaScriptFormatter:u});class f{builder;toOffset;fromOffset;lineEndings;lastLine;text;constructor(e){this.builder=e,this.lastLine=-1}format(e,t,r,n){this.lineEndings=t,this.fromOffset=r,this.toOffset=n,this.lastLine=-1,this.text=e;z("application/json")(e.substring(this.fromOffset,this.toOffset),this.tokenCallback.bind(this))}tokenCallback(e,t,r){switch(e.charAt(0)){case"{":case"[":"}"===this.text[r+1]||"]"===this.text[r+1]?this.builder.addToken(e,r):(this.builder.addToken(e,r),this.builder.addNewLine(),this.builder.increaseNestingLevel());break;case"}":case"]":"{"===this.text[r-1]||"["===this.text[r-1]||(this.builder.decreaseNestingLevel(),this.builder.addNewLine()),this.builder.addToken(e,r);break;case":":this.builder.addToken(e,r),this.builder.addSoftSpace();break;case",":this.builder.addToken(e,r),this.builder.addNewLine();break;case"":case" ":case"\n":break;default:this.builder.addToken(e,r)}}}var h=Object.freeze({__proto__:null,JSONFormatter:f});class m{#T;#M;#A;#V;#_;#D;#B;constructor(e){this.#T=e,this.#M=new u(e),this.#A=new f(e),this.#V=new j(e)}format(e,t){this.#_=e,this.#D=t,this.#B=new g(e),this.#F(this.#B.document())}#q(e,t){if(!this.#B)return;let r=this.#B.peekToken();for(;r&&r.startOffset<t;){const t=this.#B.nextToken();this.#P(e,t),r=this.#B.peekToken()}}#F(e){if(!e.openTag||!e.closeTag)throw new Error("Element is missing open or close tag");e.parent&&this.#q(e.parent,e.openTag.startOffset),this.#W(e),this.#q(e,e.openTag.endOffset),this.#K(e);for(let t=0;t<e.children.length;++t)this.#F(e.children[t]);this.#q(e,e.closeTag.startOffset),this.#$(e),this.#q(e,e.closeTag.endOffset),this.#R(e)}#W(e){this.#B&&e.children.length&&e!==this.#B.document()&&this.#T.addNewLine()}#K(e){this.#B&&e.children.length&&e!==this.#B.document()&&(this.#T.increaseNestingLevel(),this.#T.addNewLine())}#$(e){this.#B&&e.children.length&&e!==this.#B.document()&&(this.#T.decreaseNestingLevel(),this.#T.addNewLine())}#R(e){this.#T.addNewLine()}#P(t,r){if(e.StringUtilities.isWhitespace(r.value))return;if(b(r.type,"comment")||b(r.type,"meta"))return this.#T.addNewLine(),this.#T.addToken(r.value.trim(),r.startOffset),void this.#T.addNewLine();if(!t.openTag||!t.closeTag)return;const n=t.openTag.endOffset<=r.startOffset&&r.startOffset<t.closeTag.startOffset;return n&&"style"===t.name?(this.#T.addNewLine(),this.#T.increaseNestingLevel(),this.#V.format(this.#_||"",this.#D||[],r.startOffset,r.endOffset),void this.#T.decreaseNestingLevel()):n&&"script"===t.name?(this.#T.addNewLine(),this.#T.increaseNestingLevel(),!function(e){if(!e.openTag)return!0;if(!e.openTag.attributes.has("type"))return!0;let t=e.openTag.attributes.get("type");if(!t)return!0;t=t.toLowerCase();const r=/^(["\'])(.*)\1$/.exec(t.trim());r&&(t=r[2]);return["application/ecmascript","application/javascript","application/x-ecmascript","application/x-javascript","module","text/ecmascript","text/javascript","text/javascript1.0","text/javascript1.1","text/javascript1.2","text/javascript1.3","text/javascript1.4","text/javascript1.5","text/jscript","text/livescript","text/x-ecmascript","text/x-javascript"].includes(t.trim())}(t)?!function(e){if(!e.openTag)return!1;let t=e.openTag.attributes.get("type");if(!t)return!1;t=t.toLowerCase();const r=/^(["\'])(.*)\1$/.exec(t.trim());r&&(t=r[2]);/^application\/\w+\+json$/.exec(t.trim())&&(t="application/json");return["application/json","importmap","speculationrules"].includes(t.trim())}(t)?(this.#T.addToken(r.value,r.startOffset),this.#T.addNewLine()):this.#A.format(this.#_||"",this.#D||[],r.startOffset,r.endOffset):this.#M.format(this.#_||"",this.#D||[],r.startOffset,r.endOffset),void this.#T.decreaseNestingLevel()):(!n&&b(r.type,"attribute")&&this.#T.addSoftSpace(),void this.#T.addToken(r.value,r.startOffset))}}function b(e,t){return e.has(t)||e.has(`xml-${t}`)}class g{#U;#H;#Y;#n;#G;#J;#X;#Z;#Q;#ee;#te;constructor(e){this.#U="Initial",this.#H=new x("document"),this.#H.openTag=new v("document",0,0,new Map,!0,!1),this.#H.closeTag=new v("document",e.length,e.length,new Map,!1,!1),this.#Y=[this.#H],this.#n=[],this.#G=0,this.#re(e),this.#J=new Map,this.#X="",this.#Z="",this.#Q=!1}#re(e){const t=z("text/html");let r=0,n=0,i=null;const o=e=>{this.#n.push(e),this.#ne(e);const t=this.#Y[this.#Y.length-1];if(t&&("script"===t.name||"style"===t.name)&&t.openTag&&t.openTag.endOffset===n)return P},a=(e,t,a,s)=>{a+=r,n=s+=r;const l=t?new Set(t.split(" ")):new Set,c=new w(e,l,a,s);if(i){if("/"===e&&"attribute"===t&&i.type.has("string"))c.startOffset=i.startOffset,c.value=`${i.value}${e}`,c.type=i.type;else{if(e.startsWith("&")&&"error"===t&&0===i.type.size||null===t&&i.type.has("error"))return i.endOffset=c.endOffset,i.value+=e,void(i.type=c.type);if(o(i)===P)return P}i=null}if("string"!==t&&null!==t)return o(c);i=c};for(;r=n,t(e.substring(n),a),i&&(o(i),i=null),!(n>=e.length);){const t=this.#Y[this.#Y.length-1];if(!t)break;for(;;){if(n=e.indexOf("</",n),-1===n){n=e.length;break}if(e.substring(n+2).toLowerCase().startsWith(t.name))break;n+=2}if(!t.openTag)break;const r=t.openTag.endOffset,i=n,o=e.substring(r,i);this.#n.push(new w(o,new Set,r,i))}for(;this.#Y.length>1;){const t=this.#Y[this.#Y.length-1];if(!t)break;this.#ie(new v(t.name,e.length,e.length,new Map,!1,!1))}}#ne(e){const t=e.value,r=e.type;switch(this.#U){case"Initial":return void(!b(r,"bracket")||"<"!==t&&"</"!==t||(this.#oe(e),this.#U="Tag"));case"Tag":return void(b(r,"tag")&&!b(r,"bracket")?this.#Z=t.trim().toLowerCase():b(r,"attribute")?(this.#X=t.trim().toLowerCase(),this.#J.set(this.#X,""),this.#U="AttributeName"):!b(r,"bracket")||">"!==t&&"/>"!==t||(this.#ae(e),this.#U="Initial"));case"AttributeName":return void(r.size||"="!==t?!b(r,"bracket")||">"!==t&&"/>"!==t||(this.#ae(e),this.#U="Initial"):this.#U="AttributeValue");case"AttributeValue":return void(b(r,"string")?(this.#J.set(this.#X,t),this.#U="Tag"):!b(r,"bracket")||">"!==t&&"/>"!==t||(this.#ae(e),this.#U="Initial"))}}#oe(e){this.#Z="",this.#ee=e.startOffset,this.#te=null,this.#J=new Map,this.#X="",this.#Q="<"===e.value}#ae(e){this.#te=e.endOffset;const t="/>"===e.value||k.has(this.#Z),r=new v(this.#Z,this.#ee||0,this.#te,this.#J,this.#Q,t);this.#se(r)}#se(e){if(e.isOpenTag){const t=this.#Y[this.#Y.length-1];if(t){const n=y.get(t.name);t!==this.#H&&t.openTag&&t.openTag.selfClosingTag?this.#ie(r(t,t.openTag.endOffset)):n&&n.has(e.name)&&this.#ie(r(t,e.startOffset)),this.#le(e)}return}let t=this.#Y[this.#Y.length-1];for(;this.#Y.length>1&&t&&t.name!==e.name;)this.#ie(r(t,e.startOffset)),t=this.#Y[this.#Y.length-1];function r(e,t){return new v(e.name,t,t,new Map,!1,!1)}1!==this.#Y.length&&this.#ie(e)}#ie(e){const t=this.#Y.pop();t&&(t.closeTag=e)}#le(e){const t=this.#Y[this.#Y.length-1],r=new x(e.name);t&&(r.parent=t,t.children.push(r)),r.openTag=e,this.#Y.push(r)}peekToken(){return this.#G<this.#n.length?this.#n[this.#G]:null}nextToken(){return this.#n[this.#G++]}document(){return this.#H}}const k=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),y=new Map([["head",new Set(["body"])],["li",new Set(["li"])],["dt",new Set(["dt","dd"])],["dd",new Set(["dt","dd"])],["p",new Set(["address","article","aside","blockquote","div","dl","fieldset","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","main","nav","ol","p","pre","section","table","ul"])],["rb",new Set(["rb","rt","rtc","rp"])],["rt",new Set(["rb","rt","rtc","rp"])],["rtc",new Set(["rb","rtc","rp"])],["rp",new Set(["rb","rt","rtc","rp"])],["optgroup",new Set(["optgroup"])],["option",new Set(["option","optgroup"])],["colgroup",new Set(["colgroup"])],["thead",new Set(["tbody","tfoot"])],["tbody",new Set(["tbody","tfoot"])],["tfoot",new Set(["tbody"])],["tr",new Set(["tr"])],["td",new Set(["td","th"])],["th",new Set(["td","th"])]]);class w{value;type;startOffset;endOffset;constructor(e,t,r,n){this.value=e,this.type=t,this.startOffset=r,this.endOffset=n}}class v{name;startOffset;endOffset;attributes;isOpenTag;selfClosingTag;constructor(e,t,r,n,i,o){this.name=e,this.startOffset=t,this.endOffset=r,this.attributes=n,this.isOpenTag=i,this.selfClosingTag=o}}class x{name;children=[];parent=null;openTag=null;closeTag=null;constructor(e){this.name=e}}var S=Object.freeze({__proto__:null,HTMLFormatter:m,HTMLModel:g});class T{builder;constructor(e){this.builder=e}format(e,t,r,n){const i=e.substring(r,n);this.builder.addToken(i,r)}}class N{variables=new Map;parent;start;end;children=[];constructor(e,t,r){this.start=e,this.end=t,this.parent=r,r&&r.children.push(this)}export(){const e=[];for(const t of this.variables){const r=[];for(const e of t[1].uses)r.push(e.offset);e.push({name:t[0],kind:t[1].definitionKind,offsets:r})}const t=this.children.map((e=>e.export()));return{start:this.start,end:this.end,variables:e,children:t}}addVariable(e,t,r,n){const i=this.variables.get(e),o={offset:t,scope:this,isShorthandAssignmentProperty:n};i?(0===i.definitionKind&&(i.definitionKind=r),i.uses.push(o)):this.variables.set(e,{definitionKind:r,uses:[o]})}findBinders(e){const t=[];let r=this;for(;null!==r;){const n=r.variables.get(e);n&&0!==n.definitionKind&&t.push(n),r=r.parent}return t}#ce(e,t){const r=this.variables.get(e);r?(r.uses.push(...t.uses),2===t.definitionKind?(console.assert(1!==r.definitionKind),0===r.definitionKind&&(r.definitionKind=t.definitionKind)):console.assert(0===t.definitionKind)):this.variables.set(e,t)}finalizeToParent(e){if(!this.parent)throw console.error("Internal error: wrong nesting in scope analysis."),new Error("Internal error");const t=[];for(const[r,n]of this.variables.entries())(0===n.definitionKind||2===n.definitionKind&&!e)&&(this.parent.#ce(r,n),t.push(r));t.forEach((e=>this.variables.delete(e)))}}class E{#de;#ue=new Set;#pe;#fe;constructor(e){this.#fe=e,this.#de=new N(e.start,e.end,null),this.#pe=this.#de}run(){return this.#he(this.#fe),this.#de}#he(e){if(null!==e)switch(e.type){case"AwaitExpression":case"SpreadElement":case"ThrowStatement":case"UnaryExpression":case"UpdateExpression":this.#he(e.argument);break;case"ArrayExpression":case"ArrayPattern":e.elements.forEach((e=>this.#he(e)));break;case"ExpressionStatement":case"ChainExpression":this.#he(e.expression);break;case"Program":console.assert(this.#pe===this.#de),e.body.forEach((e=>this.#he(e))),console.assert(this.#pe===this.#de);break;case"ArrowFunctionExpression":this.#me(e.start,e.end),e.params.forEach(this.#be.bind(this,2,!1)),"BlockStatement"===e.body.type?e.body.body.forEach(this.#he.bind(this)):this.#he(e.body),this.#ge(!0);break;case"AssignmentExpression":case"AssignmentPattern":case"BinaryExpression":case"LogicalExpression":this.#he(e.left),this.#he(e.right);break;case"BlockStatement":this.#me(e.start,e.end),e.body.forEach(this.#he.bind(this)),this.#ge(!1);break;case"CallExpression":case"NewExpression":this.#he(e.callee),e.arguments.forEach(this.#he.bind(this));break;case"VariableDeclaration":{const t="var"===e.kind?2:1;e.declarations.forEach(this.#ke.bind(this,t));break}case"CatchClause":this.#me(e.start,e.end),this.#be(1,!1,e.param),this.#he(e.body),this.#ge(!1);break;case"ClassBody":e.body.forEach(this.#he.bind(this));break;case"ClassDeclaration":this.#be(1,!1,e.id),this.#he(e.superClass??null),this.#he(e.body);break;case"ClassExpression":this.#he(e.superClass??null),this.#he(e.body);break;case"ConditionalExpression":this.#he(e.test),this.#he(e.consequent),this.#he(e.alternate);break;case"DoWhileStatement":this.#he(e.body),this.#he(e.test);break;case"ForInStatement":case"ForOfStatement":this.#me(e.start,e.end),this.#he(e.left),this.#he(e.right),this.#he(e.body),this.#ge(!1);break;case"ForStatement":this.#me(e.start,e.end),this.#he(e.init??null),this.#he(e.test??null),this.#he(e.update??null),this.#he(e.body),this.#ge(!1);break;case"FunctionDeclaration":this.#be(2,!1,e.id),this.#me(e.id?.end??e.start,e.end),this.#ye("this",e.start,3),this.#ye("arguments",e.start,3),e.params.forEach(this.#be.bind(this,1,!1)),e.body.body.forEach(this.#he.bind(this)),this.#ge(!0);break;case"FunctionExpression":this.#me(e.id?.end??e.start,e.end),this.#ye("this",e.start,3),this.#ye("arguments",e.start,3),e.params.forEach(this.#be.bind(this,1,!1)),e.body.body.forEach(this.#he.bind(this)),this.#ge(!0);break;case"Identifier":this.#ye(e.name,e.start);break;case"IfStatement":this.#he(e.test),this.#he(e.consequent),this.#he(e.alternate??null);break;case"LabeledStatement":this.#he(e.body);break;case"MetaProperty":case"PrivateIdentifier":case"BreakStatement":case"ContinueStatement":case"DebuggerStatement":case"EmptyStatement":case"Literal":case"Super":case"TemplateElement":case"ImportDeclaration":case"ImportDefaultSpecifier":case"ImportNamespaceSpecifier":case"ImportSpecifier":case"ImportExpression":case"ExportAllDeclaration":case"ExportDefaultDeclaration":case"ExportNamedDeclaration":case"ExportSpecifier":break;case"MethodDefinition":e.computed&&this.#he(e.key),this.#he(e.value);break;case"MemberExpression":this.#he(e.object),e.computed&&this.#he(e.property);break;case"ObjectExpression":case"ObjectPattern":e.properties.forEach(this.#he.bind(this));break;case"PropertyDefinition":e.computed&&this.#he(e.key),this.#he(e.value??null);break;case"Property":e.shorthand?(console.assert("Identifier"===e.value.type),console.assert("Identifier"===e.key.type),console.assert(e.value.name===e.key.name),this.#ye(e.value.name,e.value.start,0,!0)):(e.computed&&this.#he(e.key),this.#he(e.value));break;case"RestElement":this.#be(1,!1,e.argument);break;case"ReturnStatement":case"YieldExpression":this.#he(e.argument??null);break;case"SequenceExpression":case"TemplateLiteral":e.expressions.forEach(this.#he.bind(this));break;case"SwitchCase":this.#he(e.test??null),e.consequent.forEach(this.#he.bind(this));break;case"SwitchStatement":this.#he(e.discriminant),e.cases.forEach(this.#he.bind(this));break;case"TaggedTemplateExpression":this.#he(e.tag),this.#he(e.quasi);break;case"ThisExpression":this.#ye("this",e.start);break;case"TryStatement":this.#he(e.block),this.#he(e.handler??null),this.#he(e.finalizer??null);break;case"WithStatement":this.#he(e.object),this.#he(e.body);break;case"WhileStatement":this.#he(e.test),this.#he(e.body);break;case"VariableDeclarator":console.error("Should not encounter VariableDeclarator in general traversal.")}}getFreeVariables(){const e=new Map;for(const[t,r]of this.#de.variables)0===r.definitionKind&&e.set(t,r.uses);return e}getAllNames(){return this.#ue}#me(e,t){this.#pe=new N(e,t,this.#pe)}#ge(e){if(null===this.#pe.parent)throw console.error("Internal error: wrong nesting in scope analysis."),new Error("Internal error");this.#pe.finalizeToParent(e),this.#pe=this.#pe.parent}#ye(e,t,r=0,n=!1){this.#ue.add(e),this.#pe.addVariable(e,t,r,n)}#be(e,t,r){if(null!==r)switch(r.type){case"ArrayPattern":r.elements.forEach(this.#be.bind(this,e,!1));break;case"AssignmentPattern":this.#be(e,t,r.left),this.#he(r.right);break;case"Identifier":this.#ye(r.name,r.start,e,t);break;case"MemberExpression":this.#he(r.object),r.computed&&this.#he(r.property);break;case"ObjectPattern":r.properties.forEach(this.#be.bind(this,e,!1));break;case"Property":r.computed&&this.#he(r.key),this.#be(e,r.shorthand,r.value);break;case"RestElement":this.#be(e,!1,r.argument)}}#ke(e,t){this.#be(e,!1,t.id),this.#he(t.init??null)}}var C=Object.freeze({__proto__:null,parseScopes:function(e,t="script"){let n=null;try{n=r.parse(e,{ecmaVersion:a,allowAwaitOutsideFunction:!0,ranges:!1,sourceType:t})}catch{return null}return new E(n).run()},Scope:N,ScopeVariableAnalysis:E});function O(e,t){const n=function(e,t){const n=r.parse(e,{ecmaVersion:a,allowAwaitOutsideFunction:!0,ranges:!1,checkPrivateFields:!1}),i=new E(n);i.run();const o=i.getFreeVariables(),s=[],l=i.getAllNames();for(const e of t.values())e&&l.add(e);function c(e){let t=1;for(;l.has(`${e}_${t}`);)t++;const r=`${e}_${t}`;return l.add(r),r}for(const[e,r]of t.entries()){const t=o.get(e);if(!t)continue;if(null===r)throw new Error(`Cannot substitute '${e}' as the underlying variable '${r}' is unavailable`);const n=[];for(const i of t)s.push({from:e,to:r,offset:i.offset,isShorthandAssignmentProperty:i.isShorthandAssignmentProperty}),n.push(...i.scope.findBinders(r));for(const e of n){if(3===e.definitionKind)throw new Error(`Cannot avoid capture of '${r}'`);const t=c(r);for(const n of e.uses)s.push({from:r,to:t,offset:n.offset,isShorthandAssignmentProperty:n.isShorthandAssignmentProperty})}}return s.sort(((e,t)=>e.offset-t.offset)),s}(e,t);return function(e,t){const r=[];let n=0;for(const i of t){r.push(e.slice(n,i.offset));let t=i.to;i.isShorthandAssignmentProperty&&(t=`${i.from}: ${i.to}`),r.push(t),n=i.offset+i.from.length}return r.push(e.slice(n)),r.join("")}(e,n)}var L=Object.freeze({__proto__:null,substituteExpression:O});function z(e){const t=CodeMirror.getMode({indentUnit:2},e),r=CodeMirror.startState(t);if(!t||"null"===t.name)throw new Error(`Could not find CodeMirror mode for MimeType: ${e}`);if(!t.token)throw new Error(`Could not find CodeMirror mode with token method: ${e}`);return(e,n)=>{const i=new CodeMirror.StringStream(e);for(;!i.eol();){const e=t.token(i,r),o=i.current();if(n(o,e,i.start,i.start+o.length)===P)return;i.start=i.pos}}}const P={};t.Runtime.Runtime.queryParam("test")&&(console.error=()=>{});var I=Object.freeze({__proto__:null,createTokenizer:z,AbortTokenization:P,evaluatableJavaScriptSubstring:function(e){try{const t=r.tokenizer(e,{ecmaVersion:a});let n=t.getToken();for(;o.punctuator(n);)n=t.getToken();const i=n.start;let s=n.end;for(;n.type!==r.tokTypes.eof;){const e=n.type===r.tokTypes.name||n.type===r.tokTypes.privateId,i=o.keyword(n,"this"),a=n.type===r.tokTypes.string;if(!i&&!e&&!a)break;for(s=n.end,n=t.getToken();o.punctuator(n,"[");){let e=0;do{if(o.punctuator(n,"[")&&++e,n=t.getToken(),o.punctuator(n,"]")&&0==--e){s=n.end,n=t.getToken();break}}while(n.type!==r.tokTypes.eof)}if(!o.punctuator(n,"."))break;n=t.getToken()}return e.substring(i,s)}catch(e){return console.error(e),""}},format:function(t,r,n){let i;const o=new s(n=n||"    "),a=e.StringUtilities.findLineEndingIndexes(r);try{switch(t){case"text/html":new m(o).format(r,a);break;case"text/css":new j(o).format(r,a,0,r.length);break;case"application/javascript":case"text/javascript":new u(o).format(r,a,0,r.length);break;case"application/json":case"application/manifest+json":new f(o).format(r,a,0,r.length);break;default:new T(o).format(r,a,0,r.length)}i={mapping:o.mapping,content:o.content()}}catch(e){console.error(e),i={mapping:{original:[0],formatted:[0]},content:r}}return i},substituteExpression:O});class j{#T;#L;#C;#D;#we;#U;constructor(e){this.#T=e,this.#we=-1,this.#U={eatWhitespace:void 0,seenProperty:void 0,inPropertyValue:void 0,afterClosingBrace:void 0}}format(e,t,r,n){this.#D=t,this.#C=r,this.#L=n,this.#U={eatWhitespace:void 0,seenProperty:void 0,inPropertyValue:void 0,afterClosingBrace:void 0},this.#we=-1;const i=z("text/css"),o=this.#T.setEnforceSpaceBetweenWords(!1);i(e.substring(this.#C,this.#L),this.#ve.bind(this)),this.#T.setEnforceSpaceBetweenWords(o)}#ve(t,r,n){n+=this.#C;const i=e.ArrayUtilities.lowerBound(this.#D,n,e.ArrayUtilities.DEFAULT_COMPARATOR);i!==this.#we&&(this.#U.eatWhitespace=!0),r&&(/^property/.test(r)||/^variable-2/.test(r))&&!this.#U.inPropertyValue&&(this.#U.seenProperty=!0),this.#we=i;if(/^(?:\r?\n|[\t\f\r ])+$/.test(t))this.#U.eatWhitespace||this.#T.addSoftSpace();else if(this.#U.eatWhitespace=!1,"\n"!==t){if("}"!==t&&(this.#U.afterClosingBrace&&this.#T.addNewLine(!0),this.#U.afterClosingBrace=!1),"}"===t)this.#U.inPropertyValue&&this.#T.addNewLine(),this.#T.decreaseNestingLevel(),this.#U.afterClosingBrace=!0,this.#U.inPropertyValue=!1;else{if(":"===t&&!this.#U.inPropertyValue&&this.#U.seenProperty)return this.#T.addToken(t,n),this.#T.addSoftSpace(),this.#U.eatWhitespace=!0,this.#U.inPropertyValue=!0,void(this.#U.seenProperty=!1);if("{"===t)return this.#T.addSoftSpace(),this.#T.addToken(t,n),this.#T.addNewLine(),void this.#T.increaseNestingLevel()}this.#T.addToken(t.replace(/(?:\r?\n|[\t\f\r ])+$/g,""),n),"comment"!==r||this.#U.inPropertyValue||this.#U.seenProperty||this.#T.addNewLine(),";"===t&&this.#U.inPropertyValue?(this.#U.inPropertyValue=!1,this.#T.addNewLine()):"}"===t&&this.#T.addNewLine()}}}var M=Object.freeze({__proto__:null,CSSFormatter:j});const A={Initial:"Initial",Selector:"Selector",Style:"Style",PropertyName:"PropertyName",PropertyValue:"PropertyValue",AtRule:"AtRule"};var V=Object.freeze({__proto__:null,CSSParserStates:A,parseCSS:function e(t,r){const n=t.split("\n");let i,o,a=[],s=0,l=A.Initial;const c=new Set;let d=[];function u(e){d=d.concat(e.chunk)}function p(e){return e.replace(/^(?:\r?\n|[\t\f\r ])+|(?:\r?\n|[\t\f\r ])+$/g,"")}function f(t,n,f,h){const g=n?new Set(n.split(" ")):c;switch(l){case A.Initial:g.has("qualifier")||g.has("builtin")||g.has("tag")?(i={selectorText:t,lineNumber:m,columnNumber:f,properties:[]},l=A.Selector):g.has("def")&&(i={atRule:t,lineNumber:m,columnNumber:f},l=A.AtRule);break;case A.Selector:"{"===t&&g===c?(i.selectorText=p(i.selectorText),i.styleRange=b(m,h),l=A.Style):i.selectorText+=t;break;case A.AtRule:";"!==t&&"{"!==t||g!==c?i.atRule+=t:(i.atRule=p(i.atRule),a.push(i),l=A.Initial);break;case A.Style:if(g.has("meta")||g.has("property")||g.has("variable-2"))o={name:t,value:"",range:b(m,f),nameRange:b(m,f)},l=A.PropertyName;else if("}"===t&&g===c)i.styleRange.endLine=m,i.styleRange.endColumn=f,a.push(i),l=A.Initial;else if(g.has("comment")){if("/*"!==t.substring(0,2)||"*/"!==t.substring(t.length-2))break;const r=t.substring(2,t.length-2);if(d=[],e("a{\n"+r+"}",u),1===d.length&&1===d[0].properties.length){const e=d[0].properties[0];e.disabled=!0,e.range=b(m,f),e.range.endColumn=h;const t=m-1,r=f+2;e.nameRange.startLine+=t,e.nameRange.startColumn+=r,e.nameRange.endLine+=t,e.nameRange.endColumn+=r,e.valueRange.startLine+=t,e.valueRange.startColumn+=r,e.valueRange.endLine+=t,e.valueRange.endColumn+=r,i.properties.push(e)}}break;case A.PropertyName:":"===t&&g===c?(o.name=o.name,o.nameRange.endLine=m,o.nameRange.endColumn=f,o.valueRange=b(m,h),l=A.PropertyValue):g.has("property")&&(o.name+=t);break;case A.PropertyValue:";"!==t&&"}"!==t||g!==c?g.has("comment")||(o.value+=t):(o.value=o.value,o.valueRange.endLine=m,o.valueRange.endColumn=f,o.range.endLine=m,o.range.endColumn=";"===t?h:f,i.properties.push(o),"}"===t?(i.styleRange.endLine=m,i.styleRange.endColumn=f,a.push(i),l=A.Initial):l=A.Style);break;default:console.assert(!1,"Unknown CSS parser state.")}s+=h-f,s>1e5&&(r({chunk:a,isLastChunk:!1}),a=[],s=0)}const h=z("text/css");let m;for(m=0;m<n.length;++m){const e=n[m];h(e,f),f("\n",null,e.length,e.length+1)}function b(e,t){return{startLine:e,startColumn:t,endLine:e,endColumn:t}}r({chunk:a,isLastChunk:!0})}});export{M as CSSFormatter,V as CSSRuleParser,l as FormattedContentBuilder,I as FormatterWorker,S as HTMLFormatter,h as JSONFormatter,p as JavaScriptFormatter,C as ScopeParser,L as Substitute};
