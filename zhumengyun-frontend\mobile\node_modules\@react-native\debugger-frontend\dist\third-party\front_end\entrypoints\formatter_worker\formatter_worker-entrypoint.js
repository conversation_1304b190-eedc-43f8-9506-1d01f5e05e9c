import*as e from"../../core/platform/platform.js";import*as t from"./formatter_worker.js";self.onmessage=function(s){const a=s.data.method,r=s.data.params;if(a)switch(a){case"format":self.postMessage(t.FormatterWorker.format(r.mimeType,r.content,r.indentString));break;case"parseCSS":t.CSSRuleParser.parseCSS(r.content,self.postMessage);break;case"javaScriptSubstitute":self.postMessage(t.Substitute.substituteExpression(r.content,r.mapping));break;case"javaScriptScopeTree":self.postMessage(t.ScopeParser.parseScopes(r.content,r.sourceType)?.export());break;case"evaluatableJavaScriptSubstring":self.postMessage(t.FormatterWorker.evaluatableJavaScriptSubstring(r.content));break;default:e.assertNever(a,`Unsupport method name: ${a}`)}},self.postMessage("workerReady");
